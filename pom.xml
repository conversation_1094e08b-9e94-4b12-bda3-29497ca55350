<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.zte</groupId>
    <artifactId>zte-mes-manufactureshare-centerfactory</artifactId>
    <version>0.0.1</version>
    <packaging>jar</packaging>
    <url>http://itp.zte.com.cn</url>
    <description>微服务开发框架</description>
    <properties>
        <!-- ENV props Start -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <currentVersion>${project.parent.version}</currentVersion>
        <java.version>1.8</java.version>
        <!-- ENV props End -->

        <!-- Spring Boot props Star -->
        <spring-data>1.7.1.RELEASE</spring-data>
        <!-- Spring Boot props End -->
        <!-- The sonar Start -->
        <jacoco.it.execution.data.file>${project.build.directory}/coverage-reports/jacoco-it.exec</jacoco.it.execution.data.file>
        <jacoco.ut.execution.data.file>${project.build.directory}/coverage-reports/jacoco-ut.exec</jacoco.ut.execution.data.file>
        <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
        <sonar.language>java</sonar.language>
        <!-- The sonar End -->
        <zte.approval.version>1.1.54</zte.approval.version>
        <common.version>v2.7.77</common.version>
        <!-- The zte props end -->
    </properties>

    <parent>
        <groupId>com.zte.itp</groupId>
        <artifactId>msa-parent</artifactId>
        <version>4.0.3.3-kms.RELEASE</version>
    </parent>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.ehcache</groupId>
                <artifactId>ehcache</artifactId>
                <version>3.9.2</version>
            </dependency>
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>1.4</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-compress</artifactId>
                <version>1.26.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>5.2.2</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>5.2.2</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.woodstox</groupId>
                <artifactId>woodstox-core</artifactId>
                <version>6.4.0</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>2.0</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!--单元测试，开始-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>5.3.5</version>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <!-- Powermock单元测试 -->
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.7</version>
            <scope>test</scope>
        </dependency>
        <!-- PowerMockito单元测试 -->
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.7</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-core</artifactId>
            <version>2.0.7</version>
            <scope>test</scope>
        </dependency>
        <!--单元测试，结束-->

        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>jakarta.mail</artifactId>
            <version>1.6.7</version>
        </dependency>
        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>

        <!--审批中心-->
        <dependency>
            <groupId>com.zte.iss</groupId>
            <artifactId>zte-iss-approval-sdk</artifactId>
            <version>${zte.approval.version}</version>
        </dependency>

        <!--数方平台-->
        <dependency>
            <groupId>com.zte.vmax.idatashare</groupId>
            <artifactId>idatashare-client-java</artifactId>
            <version>1.0.17</version>
        </dependency>

        <dependency>
            <groupId>com.zte.scm</groupId>
            <artifactId>zte-scm-ms-common</artifactId>
            <version>${common.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.zte.uac.authorityclient</groupId>
                    <artifactId>zte-itp-security-authorityclient</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--统一权限升级-->
        <dependency>
            <groupId>com.zte.sec.upp</groupId>
            <artifactId>zte-sec-upp-sdk</artifactId>
            <version>2.0.58.45-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-context</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-aop</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat</groupId>
                    <artifactId>tomcat-annotations-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-expression</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-test-autoconfigure</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-autoconfigure</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-test</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-tomcat</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-json</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.tomcat.embed</groupId>
                    <artifactId>tomcat-embed-el</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>4.2.1</version>
        </dependency>

        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.11.0</version>
        </dependency>

        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
            <version>42.7.2</version>
        </dependency>

        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
             <version>2.15.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.26</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.google.guava</groupId>
                    <artifactId>guava</artifactId>
                </exclusion>
            </exclusions>
            <version>4.4.0</version>
        </dependency>

        <dependency>
            <groupId>com.zte.itp</groupId>
            <artifactId>msa-auth</artifactId>
            <version>3.2.10.RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>zte-itp-security-authorityclient</artifactId>
                    <groupId>com.zte.uac.authorityclient</groupId>
                </exclusion>
                <!-- 框架升级 4.0.0.release 冲突 -->
                <exclusion>
                    <groupId>com.zte.itp</groupId>
                    <artifactId>msa-basicframework</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--离线实际上使用了代理，java agent。-->
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>org.jacoco.agent</artifactId>
            <version>0.8.11</version>
            <classifier>runtime</classifier>
        </dependency>
        <dependency>
            <groupId>com.zte</groupId>
            <artifactId>zte-mes-gei</artifactId>
            <version>1.0.11</version>
        </dependency>
        <dependency>
            <groupId>com.zte</groupId>
            <artifactId>zte-mes-generator</artifactId>
            <version>1.0.2</version>
            <scope>provided</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.kafka</groupId>
                    <artifactId>kafka-clients</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-beans</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-transports-http</artifactId>
            <version>3.5.10</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.10.0</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.jupiter</groupId>
                    <artifactId>junit-jupiter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.mockito</groupId>
                    <artifactId>mockito-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-test</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-test-autoconfigure</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
    <build>
        <!-- 配置静态资源文件路径，构建需要使用到 -->
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.xml</include>
                    <include>**/*.yml</include>
                </includes>
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <!-- 这部分文件的配置内容已经放在配置中心，打包时过滤掉。 -->
                <excludes>
                    <exclude>to-config-server/application-dev.properties</exclude>
                    <exclude>to-config-server/application-prod.properties</exclude>
                    <exclude>to-config-server/application-test.properties</exclude>
                </excludes>
                <filtering>false</filtering>
            </resource>
        </resources>
        <plugins>
            <!-- JDK 指定编译版本 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.7.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
            <!-- 打包+热启动插件,打包命令如下
             mvn clean package  -Dmaven.test.skip=true //执行编译，打包即可，但不会执行test
       mvn clean install  //执行编译，打包，test,且会安装到本地仓库,并且是jar和pom同时安装的
             -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.4.4</version>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>com.zte</groupId>
                            <artifactId>zte-mes-generator</artifactId>
                        </exclude>
                    </excludes>
                    <mainClass>com.zte.Application</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <!-- 以下2种方式启动可以加载热部署,即系统会监视classes文件,修改后不用重启
                    1:mvn clean spring-boot:run。
                    2:Application debug 启动java文件时。-->
                    <dependency>
                        <groupId>org.springframework</groupId>
                        <artifactId>springloaded</artifactId>
                        <version>1.2.6.RELEASE</version>
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jxr-plugin</artifactId>
                <version>2.5</version>
            </plugin>
            <!-- jacoco -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.11</version>
                <executions>
                    <!--离线模式-->
                    <execution>
                        <id>default-instrument</id>
                        <goals>
                            <goal>instrument</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-restore-instrumented-classes</id>
                        <goals>
                            <goal>restore-instrumented-classes</goal>
                        </goals>
                    </execution>
                    <!--clean+verify 生成报告-->
                    <execution>
                        <phase>verify</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                        <configuration>
                            <dataFile>${project.basedir}/jacoco.exec</dataFile>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.20</version>
                <configuration>
                    <!--设置执行器-->
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>${project.basedir}/jacoco.exec</jacoco-agent.destfile>
                    </systemPropertyVariables>
                    <!--单元测试开启并行 -->
                    <skipTests>false</skipTests>
                    <testFailureIgnore>true</testFailureIgnore>
                </configuration>
            </plugin>
            <!-- 流水线配置plugins结束 -->
            <!-- 静态代码分析插件 -->
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>3.0.1</version>
                <configuration>
                    <threshold>High</threshold>
                    <effort>Default</effort>
                    <findbugsXmlOutput>true</findbugsXmlOutput>
                    <!-- findbugs xml输出路径-->
                    <findbugsXmlOutputDirectory>../SCM_BUILD/FindBugsLog/SCM_CJ011_SystemService</findbugsXmlOutputDirectory>
                </configuration>
            </plugin>
            <!-- 静态代码分析插件 -->
        </plugins>
    </build>
</project>
