customize.msg={0}
RetCode.Success=操作成功
RetCode.ServerError=服务器错误
RetCode.AuthFailed=认证失败
RetCode.PermissionDenied=没有权限
RetCode.ValidationError=验证失败
RetCode.BusinessError=业务异常
year.month.is.over.syncYear=当前月份数据未同步
year.or.month.is.null=年月不能为空
the.bit.number.data.is.abnormal=位号数据异常
no.data.query=当前月份没有可查询的数据
time.format.error=时间格式错误
factory.id.is.null=工厂ID不能为空
factory.id.must.greater.than.zero=工厂ID必须大于零(当前值是{0})
lfid.is.null=LFID不能为空
export.limit.1w=导出总数不能超过1w条，请缩小范围
batch.lfid.all.null=批次/LFID必须输入一个
premanu.type.exists={0}前加工类型存在
user.is.not.exists=用户不存在
password.is.error=密码错误
tagNum.cannot.be.null=当前料单代码、物料代码、前加工类型下存在前加工信息，位号不能为空
tag.null.only.one=同一个物料代码，同前加工类型只能有一个位号为空
tagNum.cannot.be.same=当前前加工类型下存在位号{0}的记录
item.no.is.null=请输入物料代码
excel.resolution.failure=excel 解析失败,{0}
excel.import.failure=excel 导入失败
excel.import.success=excel 导入成功
bom.code.is.null=料单代码为空
send.center.psTask.to.sys.failure=推送中心工厂任务到本地工厂失败：{0}

# 客户物料查询相关错误信息
CUSTOMER_NAME_NULL=客户名称不能为空
COOPERATION_MODE_NULL=合作模式不能为空
bom.data.of.bom.no.not.exist=料单代码对应bom数据不存在
item.no.contains.one.position.but.not.all.position=站位物料代码不一致! 物料代码 {0} 对应货位包含货位 {1} 但不包含输入的全部位号 {2}
bom.data.repeat=料单信息数据重复请检查
bom.code.can.not.be.empty=料单代码不能为空
soft.ver.of.boms.info.not.same=料单信息第 {0} 行和上一行软件版本不一致。请检查
soft.version.not.match=提交版本和应提交版本不一致。请重试
is.uploading.please.wait=正在上传。请稍后
download.is.failed=下载文件失败，请确认docId有效！
upload.artifactory.failed=上传制品库失败! 状态码 {0}
delete.artifactory.failed=删除制品库文件失败! 状态码 {0}
download.artifactory.failed=下载制品库文件失败! 状态码 {0}
soft.version.of.delete.data.is.not.the.latest=当前版本 {0} 不是最新版本,不能删除
data.or.params.error=数据或参数异常! {0} 为空 
match.task.no.data=根据任务号和批次未匹配到 , 任务号： {0} , 批次： {1}
emp.no.is.null=工号不能为空
The.page.size.should.not.be.larger.than.1000 = 分页尺寸不能大于1000
Do.not.perform.deletion.operation.without.incoming.data = 未传入数据不执行删除操作
barcode.param.is.null.or.too.many=条码元素不存在或过多
classify.is.too.many=存在多个分类因子
split.task.no.data=拆分的任务为找到，任务号：{0}，来源：{1}
label.generate.rule.is.null=标签生成规则不能为空
match.multiple.label.generate.rule=匹配到多条标签生成规则
generate.barcode.failure=生成条码失败
generate.max.reel.id.count=reelIdCount不能超过2000
task.not.exist.or.not.allow.to.modify=任务不存在或者发放不允许取消，任务号：{0}
approval_log_name=单板报废审批错误信息
bom_prod_params_errro=bomProd参数异常
Hard.Cord.File.Template=模板文件错误！未包含列：
sn.is.null=条码为空
query.flag.error=查询标识错误(1-CA信息,2-校验码)
sn.num.is.more.than.100=查询条码数量超过100
resource.use.info.export.max=一次最多只能勾选100个资源编号的使用记录
ca.api.call.error=CA证书接口调用异常
item.no.need.bind.ca=该物料无需绑定CA
task.data.is.error={0}批次数据有误
no.task.need.to.gene=没有需生成条码的批次
socket.io.exception=IO异常(loc:{0})
prodplan.id.is.null=批次号为空
xml.to.obj.error=XML转换对象异常: {0}
barcode.not.exist=原标签、新标签不存在
init.barcode.not.exist=原标签不存在
new.barcode.existed=新标签已注册
incomplete.conditions=条件不全
param.is.null=参数缺失
params.error=参数异常:{0}
export.itemlistno.empty=导出时料单代码不能为空
available.quantity.is.zero=可用数量为0，无法申请，资源池编号：{0}
max.version.is.z=最大版本号为Z
position.do.not.match.the.uploaded.record=当前位号 {0} 和已上传记录的位号 {1} 不完全匹配。请检查
factory.id.or.emp.no.is.null=工厂id或用户编号为空
data.not.exist.or.has.deleted=数据不存在或已删除
apply.qty.exceed.can.use.qty=申请数量大于可用数量, 可用数量:{0}
board.instruction.cycle.info.is.creating.please.wait=单板指令周期信息数据正在生成中。请稍候重试
network.license.sign.lock=入网资源正在导入中，请稍候重试
network.license.resource.not.fount=资源号不存在！
network.license.print.not.fount=资源号或SN打印记录不存在
network.license.resource.print.not.fount={0}资源号打印记录不存在！
reel.id.not.register=ReelId未注册
work.time.error=子工序{0}作业时长只能是1-2000的正整数
remain.time.error=子工序{0}滞留时长只能是1-2000的正整数
last.process.should.be.stock=工艺路径中最后一个子工序必须是“入库”
userType.not.corrent=用户类型不正确
rfid.sign.info.data.is.null=签名数据没有传
rfid.sign.info.not.setting=没有设置电子签名信息
rfid.sign.info.not.salt=没有设置电子签名加密盐值
rfid.sign.info.not.cert.id=没有设置电子签名证书id
rfid.sign.info.not.cert.id.get.addr=没有设置电子签名证书id获取地址
rfid.sign.info.cert.id.get.fail=证书id获取失败
rfid.sign.info.cert.id.get.error=证书id获取发生异常
rfid.sign.info.cert.id.oper.success=证书id获取和更新成功
resource.application.is.applying=资源申请正在进行中, 资源编号：{0}
resource.application.not.exist=资源申请不存在, 资源申请ID：{0}
resource.application.apply.amount.is.null=资源申请数量为空, 申请单号：{0}
resource.application.not.newest=该资源申请不最新申请的资源, 无法作废
board.first.house.service.error=单板首件入库服务出错
prodplan.service.error=生产指令服务出错
re.work.error=返工扫描查产品大类，入参为空
resource.info.not.exist=资源池信息不存在，请检查，资源编号：{0}
date.error=日期类型错误
at.least.choose.one=查询条件交付集、合同号、时间至少选择一个
datatype.error=数据类型错误
datetype.error=日期类型错误
cycle.type.error=循环类型错误
result.type.error=输出类型错误
deliver.sets.service.error=点对点微服务错误
cycle.productclass.and.plangroup.error=产品大类和计划组不能同时作为查询条件
str.format.time.not.null=时间字符串不能为空,字符串格式必须为(yyyy-MM-dd HH:mm:ss), 例: 1999-01-01 00:00:00
no.data.to.export=没有数据可导出
deliver.entity.service.error=任务生产周期出错
bom.server.error=单板生产周期服务出错
afferent.data.exceeds.maximum=传入数据超出最大值
no.maximum.value.found=没有找到设置的最大值
data.is.empty=数据为空;
received.data.not.meet.requirements=接收到的数据不符合要求，请重新确认后提交
check.the.file.and.the.selected.customer.part.type=请检查文件和所选客户部件类型是否一致
received.data.not.meet.requirements.new=接收到的数据不符合要求，请重新确认后提交 {0}
file_format_error=文件格式错误，请使用excel导入！
workbook_init_fail=Workbook初始化失败
import.failed.please.try.again=导入失败，请重试！
import.successful.rows=导入成功！行数：
max.import.is.once=一次最多只能导入5000条！
subcontractor.id.can.not.empty=外协厂ID不允许为空，第： {0} 行。
access.party.code.is.empty=接入方编码为空！
outsourcing.factory.name.is.empty=外协厂名称为空！
zte.po.is.empty=中兴po为空！
outsourcing.work.order.number.is.empty=外协工单号为空！
device.sn.en.product.empty=整机sn/en/产品/sn为空！
sn.is.empty=单板sn为空！
mac.is.empty=mac为空
product.code.is.empty=产品代码为空！
product.code.not.exist=物料代码不存在！
primary.key.is.empty=主键为空
system.not.support.the.type=系统暂不支持此种上送类型数据
tk.registration.info.not.found=找不到接入方注册信息(tk)。
pk.registration.info.not.found=找不到接入方注册信息(pk)。
submited.data.is.invalid=上送数据无效，验签失败。
submited.data.format.error=上送数据格式错误，没有传递值：
import.failed.please.check.data=导入失败，请检查表中数据后重试！
product.library.cannot.be.empty=产品库包结存不允许为空，第：
plan.quantity.cannot.be.empty=计划数量不允许为空
product.categories.cannot.be.empty=产品大类不允许为空
product.model.cannot.be.empty=产品型号不允许为空
product.name.cannot.be.empty=产品名称不允许为空
shipped.number.cannot.be.empty=已发货数不允许为空
deadline.cannot.be.empty=截止时间不允许为空
board.balance.cannot.be.empty=单板结存不允许为空
test.package.balance.cannot.be.empty=测试包结存不允许为空
vendor.cannot.be.empty=供应商不能为空
item.name.cannot.be.empty=物料名称不能为空
item.model.cannot.be.empty=物料型号不能为空
material.po.number.cannot.be.empty=物料PO号不能为空
this.batch.quantity.cannot.be.empty=本批数量不能为空
sampling.quantity.cannot.be.empty=抽检数量不能为空
incoming.date.cannot.be.empty=来料日期不能为空
detection.date.cannot.be.empty=检测日期不能为空
detection.no.cannot.be.empty=检测单号不能为空
sampling.method.cannot.be.empty=抽样方式不能为空
detection.result.is.empty=检测结果为空
item.code.cannot.be.empty=物料条码不能为空
item.no.cannot.be.empty=物料代码不能为空
outsourcing.reelid.is.empty=外协 REEL ID为空
reelid.quantity.is.empty=REEL ID数量为空
correlation.time.is.empty=关联时间为空
stock.quantity.is.empty=库存数量为空！
stock.cutoff.time.is.empty=库存截止时间为空！
line_can_not_be_empty=线体不允许为空
sn.scan.time.cannot.be.empty=SN扫描时间不允许为空，或者格式错误
feed.quantity.cannot.be.empty=上料数量不允许为空
feed_time_cannot_be_empty=FEED_TIME不允许为空，或者格式错误
file_name_is_empty=文件名为空
data.table.duplicated=数据表中数据重复！
license.plate.number.is.empty=车牌号为空！
shuttle.error=班车ERROR
phone.number.is.empty=手机号码为空！
route.is.empty=路线为空！
tag.sequence.not.exists=该标签不存在序列
no.bom.details.data=无BOM明细数据
material.base.attributes.not.found=未查询到物料基础属性
tag.number.analysis.failed=位号分析失败
not.meet.rule=不满足独立号规则,
exist.multiple.connectors=存在多个连接符,
not.meet.consecutive.number.rule=不满足连续号规则,
two.tag.numbers.characters.not.equal=两个位号数字前字符不相等,
start.num.greater.or.equal.end.num=开始位号大于或等于结束位号,
no.bom.data.need.split=没有需拆分的BOM数据
tag.number.split.failed=位号拆分失败
file.parsing.error=文件解析错误
file.parsing.success=文件解析成功
sn.rule.params.error=条码规则参数异常
product.classification.cannot.be.empty=产品分类不允许为空
exist.in.other.categories=在其他大类中存在
exist.in.other.categories.new={0}在其他大类中存在
existed=已存在
description=描述：
description.new=描述：{0}
name=名称：
name.new=名称：{0}
product.unit.cannot.be.empty=产品单位不能为空
incoming.model.is.empty=传入机型为空
classification.exist.storage.value=当前分类已有入库目标值，无法删除或修改
model.already.exists=机型已存在
click.the.download.address.below=点击下面的下载地址（下载导出文件）
download.address='>下载地址(
delete.automatically.after=后自动删除,请尽快下载</p>
barcode.create.excel.success=条码追溯信息查询，生成查询结果excel成功
address.will.be.at=<p style='color:red'>此地址将在
query.result.is.empty=查询结果为空
create.query.result.excel.failed=生成查询结果excel失败
generate.query.result.excel.failed=生成查询结果excel失败：请重新生成
item.code.prefix.cannot.be.empty=物料代码前缀不能为空
material.code.prefix.length.be=,物料代码前缀长度必须为
product.type.length.cannot.exceed=产品类型长度不能超过
mac.address.not.maintained=MAC1地址：{0未在MAC地址区间维护里,不能导入}
database.table.exist.sn=数据库中的表已经存在：{0}子部件条码
date.format.must.be=生产日期日期不能为空且格式 必须为：yyyy-MM-dd或yyyy/MM/dd!行：
some.parms.cannot.be.empty=内部合同号、子部件代码、子部件条码、生产日期、软件版本、硬件版本、MAC1、外箱箱号、接入地址、用户名、终端配置密码、制造工艺单号不能为空！行：
data.is.error=数据异常,请重新导入:
mac.address.invalid=请输入有效的MAC地址
duplicate.bar.code.inside.template=模板内部子部件条码重复
duplicate.gpon.sn.inside.the.template=模板内部GPON-SN重复
duplicate.device.id.inside.template=模板内部设备标识重复
create.query.result.excel.success=生成查询结果excel成功
records.qty.cannot.exceed=记录数不允许超过
production.unit.does.not.exist=生产单位不存在
mac.start.address.less.end=MAC起始地址应小于结束地址,且地址区间不能大于200万，不能新增
mac.address.cannot.be.duplicated=MAC地址不能重复
failed.to.get.user.email=获取用户email失败
email.format.incorrect=email格式不正确，请确认
email.to.is.empty=email收件人为空，请确认
email.send.err=邮件发送失败:{0}
failed.to.get.redis.lock=获取redis锁资源失败，请重试
failed.to.get.registration.info=待注册信息获取失败
import.data.is.empty=导入数据为空
duplicate.sn=SN重复
duplicate.sn.new=SN重复 {0}
brand.cannot.be.empty=Brand/品牌不能为空
device.type.cannot.be.empty=Type/设备种类不能为空
model.cannot.be.empty=Model/型号不能为空
prod.task.no.cannot.be.empty=生产任务号(外协)不能为空
manufacturing.process.no.cannot.be.empty=Product BOM/制造工艺单号不能为空
mac1.cannot.be.empty=STB MAC1/ MAC1* 必填项不能为空
sn.cannot.be.empty=PCBA SN1/单板SN1* 必填项不能为空
product.sn.cannot.be.empty=STB SN/产品SN* 必填项不能为空
carton.sn.cannot.be.empty=CARTOON SN/纸箱SN* 必填项不能为空
params.cannot.be.empty=栈板SN/生产日期/电源SN/遥控器SN/FT测试结果/FT测试时间/Burn-In Result老化测试结果/Burn-In Time老化测试时长/MAC配置结果/MAC配置时间* 必填项不能为空
part.two.params.cannot.be.empty=开关机测试结果/开关机测试时间/整机测试结果/整机测试时间/整机校验结果/整机校验时间* 必填项不能为空
part.three.params.cannot.be.empty=出厂配置结果/出厂配置时间/软件版本号/Logo版本号/出厂配置文件名* 必填项不能为空
product.sn.already.exists=产品SN已经存在
transmission.check.failed=传输校验失败(SHA256)
no.barcode.required.bind.ca.certificate=没有需要绑定CA证书的条码
file.upload.failed=文件上传失败
generate.key.exception=生成秘钥异常
encryption.exception=加密异常
decryption.exception=解密异常
signature.exception=签名异常
private.key.is.empty=私钥为空
signing.exception=验签异常
file.initialization.failed=文件初始化失败
file_is_null=文件为空
data.error=数据有误
device.is.bound=设备已绑定，请重新选择
insert.data.operation.failed=插入数据操作失败！
card.machine.info.is.existed=该卡机信息已存在，请检查
del.data.operation.failed=删除数据操作失败！
update.data.operation.failed=修改数据操作失败！
departmenet.is.existed=部门名称已存在，请检查
employee.no.is.existed=该工号已存在，请检查
card.no.is.existed=该卡号已存在，请检查
import.successful=导入成功!
scheduled.update.record.execution.failed=定时更新记录执行失败 
interface.call.succeeded=接口调用成功
interface.call.error=接口调用异常:{0}
is.creating.please.wait=合同任务表征数据正在生成中。请稍候重试
file.upload.succeed=文件上传成功
duplicate.route.name=路线名称重复，请重新命名！
miss.scheduled.date=第{0}条缺失调度日期！请检查
duplicate.site.name=站点名称重复，请重新输入站点名称！
stop.already.existed=经停站已存在，请重新选择！
serial.number.is.exists=序号已存在，请重新输入！
sn.generation.failed=条码生成失败
sn.generation.success=条码生成成功
sn.bind.ca.failed=条码绑定CA失败
access.no.is.existed=接入方编号已存在，请重新输入
access.name.is.existed=接入方名称已存在，请重新输入
bind_success=绑定成功
operation.success=操作成功
preProcess.destination.code.existed=前加工去向代码已存在，请确认
generating.excel=正在生成excel，请留意邮件
generating.offline.export.excel=正在生成excel，请查看下载中心
processing.failed=处理失败
operator.cannot.be.empty=操作人不能为空
no.data.to.del=没有可删除数据，只能删除自己导入的数据！
mac.format.incorrect=MAC格式不正确！
failed.export.emp.no.is.empty=导出数据失败，用户empNo为空
reel.id.verify.passed=Reel ID校验通过
reel.id.is.wrong=Reel ID有误
abnormal.operation=操作异常
reelid.is.registered=ReelId已注册
number.cannot.exceed=记录数不允许超过5000行
catalog.code.cannot.be.empty=请输入目录代码
sub.item.code.can.not.be.empty=请输入子项代码
authentication.failed={"message": "Unified authorization authentication failed"}
max.print.count.is.2000=打印数量不能小于0且不能大于2000
barcode.generate.error=调用条码中心生成条码异常! {0}
barcode.call.system=调用系统不能为空
barcode.is.null=条码不能为空
choreographer.url.type.error=编排器接口地址数据字典未配置
choreographer.call.error=编排器接口调用出错
taskNo.is.null=任务号不能为空
get.itemlistno.error=根据批次查询料单代码出错
query.style.and.brand.error=回写查询型号报错
query.export.error=资源池维护导出异常数据字典未配置
avl.query.error=AVL查询失败
reelid.null=reelid为空
item.no.null=物料代码为空
product.task.is.null=生产批次为空
reelid.sn.is.null=220条码为空
pkcode.info.is.empty=查询pkCode信息为空
input.more.than.regiter=输入数量大于待注册数量
blank.generate.error=调用条码中心生成空条码失败 {0}
please.update.printcs=token为空。请更新打印程序
taskStatus.not.allow.to.modify=当前任务状态不允许修改，任务号是：{0}
not.lfid.meter=不是 {0} 的物料
reel.id.registered={0} Reel Id 已注册
printType.or.printScene.is.null=打印类型或打印场景为空
not.find=未发现上传文件
material.file.upload.error=物料维护上传文件解析错误.
material.file.upload.empty=解析excel 表格数据为空，请检查格式。
material.file.upload.max.beyond=导入excle条目的最大数量为： {0} 请修改后分批，重新上传文件。
material.file.upload.message=导入excle成功条数：{0}，失败条数：{1}。
tagNum.null.is.create=该物料代码维护了位号为空的前加工，不能再维护有位号的前加工。
tagNum.null.can.not.create=该物料代码维护了前加工位号不为空的，不能再维护位号为空的前加工。
material.code.has.maintained.the.pre.processing.data=该物料代码维护了前加工数据
material.code.has.maintained.only.distribution.operation=该物料代码维护了只有配送工序{0}的前加工数据
tagnum.distribution.operation.inconsistent=当前选择的配送工序与维护配送工序:{0}不一致,请确认！
distribution.operation.is.empty=配送工序为空
tagNum.exist.pre.processing.type.and.destination.is.null=位号不为空时前加工类型以及去向不能为空
add.pre.manu.info.batch.failed=新增前加工信息失败，错误信息:{0}
reelid.not.exist.in.mes=原REELID在IMES系统不存在。
tagNum.exist.disabe.create=已经维护位号为空的前加工数据。
tagnum.is.exit=维护了位号 {0} 的 {1} 前加工，不能再维护。
tagnum.some.is.exit=位号{0}中包含已经维护位号 {1} 信息，必须保持位号信息一致。
generate.apply.no.failed=生成单据号失败！
get.program.bill.info.failed=获取烧录验证单信息失败！
get.program.sample.info.failed=获取烧录样片信息失败！
get.program.small.info.failed=获取烧录小批量验证信息失败！
currenthandler.is.null=当前处理人为空
exist.only.one.bracket=只存在一个括号
sn.lost.board.center={0}条码在条码中心不存在
update.barcode.fail=�����������ĸ�������ӿ�ʧ�ܣ�{0}
error_msg_set=第{0}行数据错误信息:{1}
confirm_msg_set=第{0}行数据需确认信息:当前选择的配送工序与维护的配送工序不一致
no.location.data=无位号数据，请先进行BOM位号解析
supplier.coding.error=供应商编码错误，请输入正确的编码
item.inventory.is.null=物料接收库存为空
item.not.check={0} 物料未进行盘点，不允许结束盘点
task.is.not.this.factory=该任务号的生产单位不是本工厂
prod.bind.setting.is.saving=料单代码 {0} 正在保存绑定设置。请稍候重试
item.code.has.bind.on.other.process=物料代码 {0} 已绑定到其它工序。请检查
get.last.package.process.fail=获取装配最后子工序失败
sys.look.not.config=数据字典 {0} 未配置
faied.check.outsourcing.production.unit=查询外协生产单位失败
you.have.bill.to.approve=您有待审批物料单,请及时到iMES系统处理。单据号【{0}】
prodPlanId.is.production=该任务已添加缓冲池，不允许取消发放，批次：{0}
you.have.timer.synchronize.fail=您有informatica定时器数据同步失败,请及时处理!
timer.synchronize.fail=定时器 {0} 同步失败
unbinding.setting.exist=绑定异常关系已存在（包含子卡），无需重复设置
productcode.is.dealing=产品代码 {0} 正在处理中，请稍后
chinese.comma.existed=存在中文逗号，请确认
cad.point.lost.error={0} 位号不在CAD文件中。 </br> {1} 物料将会统一在A面产生调拨单。 </br>
cad.point.lost.last.infor=最后更新时间:{0}，最后更新人:{1}。 </br>
cad.point.lost.error.sure=请确认是否继续CAD解析!
cad.point.empty=CAD 文件中，可用位号信息为空，请确认文件是否正确！！！
type_code_name_trace_code_name_can_not_be_empty_alone=前加工类型,前加工去向数据有问题,代码,名称不能单独为空,请确认!
resource.match.failed=资源号段格式与资源类型不匹配！
resource.type.should.not.be.null=输入资源号进行查询时资源类型必填！
resource.type.not.exists=资源类型不存在！
resource.no.str.time.should.not.be.null=查询时资源号、资源编号或创建时间必选一个，请选择！
search.time.limit=仅用创建时间做查询条件，时间范围不能超过两年
resource.type.can.not.be.null=导出时资源类型必选
export.time.interval.more.than.180.days=导出时间跨度不能超过半年
export.time.can.not.null=导出时间必填
export.time.interval.more.than.90.days=导出时间跨度不能超过3个月
export.resourceNo.can.not.empty=导出详细信息的资源号不能为空
export.total.more.than.100000=导出数量超过10w，请缩小选择时间范围
orderOrTask.has.already.applied.for.resources=该任务号已经申请过资源
start.segment.must.less.end.segment=资源开始号段必须小于结束号段
duplicate.resource.number.segment=资源号段存在重复，请确认
export.timeout=导出超时
resource.data.error=资源号不一致
resource.no.does.not.match=以下资源号不匹配:{0}
import.data.error=资源导入数据错误
import.timeout=导入超时
import.success=导入成功
file.format.error=文件格式错误
resource.file.format.error={0}标签包含{1}个参数，导入的文件包含{2}列，数量不匹配系统无法处理，请确认
resource.file.header.tagparam.error=导入文件头{0}与标签的{1}参数不匹配，请确认
itemCode.selected.cannot.more.than.100=一次选择的料单代码不要超过100
unknown.error.handler=系统出现未知错误，错误日志编号为：{0},请联系运维人员处理，谢谢！
log.id=日志ID:{0}
task_source_cannot_be_empty=任务来源不能为空
get.task.info.error=获取任务信息失败:{0}
task_has_been_issued_to_the_local_factory={0}任务已发放至本地工厂{1}，请确认
factory_not_configured_in_1245_data_dictionary=选择工厂在1245数据字典未配置相关数据，请确认！
the_batch_has_generated_a_nesting_list={0}批次已产生套料单，不允许取消发放！
orgid_is_null_or_factoryid_not_find=组织id或工厂id为空
the_current_batch_is_issued=当前批次未发放，请确认！
get.spm.tld.error=调SPM获取套料单信息失败
sequence.beyond.maxvalue=当前序列号超出最大设定值请检查：{0}
sequence.no.value=序列号初识值未设置，请手动新增！
operater_time_can_not_be_empty=操作时间不能为空
operater_time_can_not_greater_one_year=操作时间范围不能大于两年
failed.to.get.assembly.relation.push=获取{0}装配关系推送结果失败
failed.to.update.result={0}根据料单以及版本更新结果表以及写历史失败
no.push.record.for.the.material.code={0}物料代码{1}版本不存在推送记录，请确认
blank.msg={0}
item.version.null=物料代码版本为空
update.successed=更新成功
item.code.length.not.twelve=物料代码不是12位
taskQty.is.error=任务数量错误
selected.factory.not.the.factory.corresponding.of.task=选择工厂不是其任务组织id对应的可发放工厂，请确认
barcode.get.template.error=调用条码中心获取模板列表失败
barcode.get.template.error.msg=调用条码中心获取模板列表失败:{0}
get.lookup.value.error=获取数据字典值异常! {0}
failed_to_get_barcode_center_url=获取数据字典配置条码中心接口URL失败
failed.to.get.barcode.center.download.url=获取条码中心适配程序下载地址失败
failed_to_adjust_barcode_center_print_interface=调条码中心打印接口失败
call.barCode.center.to.print.falied=调条码中心打印条码失败:{0}
dozen.sn.not.exist.print.data=补打条码{0}不存在历史打印数据,未在IMES上打印过的记录请选择打印模版
bar.code.center.is.null=条码中心为找到该记录，请检查该条码是否为未在IMES上打印的有效数据:{0}
ip.is.null=ip不能为空
item.no.not.exist=未查到此物料代码信息,请输入正确的物料代码
print.count.is.null=打印份数不能为空
print.count.can.not.more.ten=打印份数不能大于10
print.num.is.null=打印数量不能为空
print.num.can.not.more.one.thousand=打印数量不能大于1000
lead.flag.is.null=环保属性不能为空
print.template.is.null=打印模板不能为空
get.printer.failed=获取打印机名称失败
failed.to.obtain.hr.info=调人事接口获取信息失败:{0}
taskno.prodplan.is.null=任务号批次号不能同时为空
no_box_code_data_obtained=未获取到箱码数据
item.type.diff=物料类型和界面所选物料类型不一致请确认！
item.not.exists.bom=物料代码/料单代码在BOM中不存在！
item.influence.bom.msg1=本次操作将影响{0}个料单的绑定关系，系统不对历史绑定关系进行处理，请确认是否需要提交！
item.influence.bom.msg2=本次操作将影响{0}个料单的绑定关系，后续新增料单将自动增加绑定关系!具体料单信息：{1}
item.influence.bom.msg3=不含{0}子工序的料单{1}个(只能料单级手动维护或新增工艺路径再维护),具体料单信息：{2}
item.has.exists=物料代码已经存在有效维护记录，不能再维护请确认！
route.detail.empty=工艺路径为空{0}。
uac_token_cannot_be_empty=UAC TOKEN不能为空,请确认！
failed_to_tune_icenter_interface=调iCenter接口失败,URL：{0}
failed_to_call_icenter_interface=调iCenter接口失败,MSG：{0}
failed_to_call_icenter_to_withdraw_documents=调iCenter撤回单据失败,MSG：{0}
the_barcode_length_is_less_than_7_digits=条码{0}长度不足7位
failed_to_get_barcode_factory_id=获取条码{0}对应的工厂id失败
failed_to_write_test_record=调分工厂写测试记录失败:{0}
speciality.resource.not.exist={0}资源不存在
speciality.param.template.exist=模板名称已存在
speciality.param.template.not.exist={0}模板不存在
speciality.param.template.item.null=参数列表数据不能为空
speciality.param.template.item.name.repeat=模板参数名称重复
speciality.param.template.item.brackets=模板括号不成对：{0}
speciality.param.template.item.two.operator=模板规则操作符错误：{0}
speciality.param.template.item.function=模板规则函数错误：{0}
speciality.param.template.item.rule.err={0}规则错误,请确认后重试!
speciality.param.template.item.error=模板{0}规则中参数不存在或排序错误
speciality.param.applyTask.generate={0}订单号已生成过个参，请确认
speciality.param.applyTask.not.resource={0}订单无{1}资源申请单，无法生成个参，请确认
speciality.param.applyTask.resource.not.match={0}资源申请单{1}与{2}资源申请单{3}的申请数量不一致，无法生成个参，请确认
excel.read.failed=文件解析失败
table.column.error=表格列有误
status.of.taskNo.is.released=标模任务号:{0}的任务状态为“已发放”
length.of.data.exceeds.500=导入数据不能超过500条
taskNo.or.prodplanId.fields.cannot.be.empty=标模任务和批次字段不能为空
fields.such.as.assembly.remarks.must.not.be.empty=标模任务{0}批次{1}对应的装配备注、计调备注、要求装配完工时间、要求测试完工时间、要求包装完工时间、预计发放日、工序指令按任务条码校验、是否锁定任务条码入库、是否触发拉料需有任一字段不为空
duplicate.data.in.the.table=标模任务号:{0}在表内存在重复数据
taskNo.and.prodplanId.do.not.match=标模任务号:{0}和批次:{1}不匹配
failed_to_process_the_document_in_the_factory=调分工厂处理单据失败:{0}
failed_to_process_approval_center_kafka_message=处理审批中心kafka消息失败
task.no.not.exists=任务:{0}不存在
prodplan.id.not.exists=批次:{0}不存在
type.name.should.not.be.null=前加工类型不能为空
process.code.id.exists=子工序代码已存在！
process.name.exists=子工序名称已存在！
station.is.existed=工站已存在!
insert.workstaion.is.success=添加工站成功!
single.page.query.cannot.exceed.500.entries=单页查询不得超过500条
params.is.null=参数为空
query.noCtBasic.by.craftId=当前工艺信息已升级，请刷新
existing.uncommit.craft={0} 已存在拟制中工艺
save.sucess=保存成功
not.find.craft.info=未找到对应的工艺基础信息，请检查
must.be.board.assembly=单板装配的第一个工序必须是单板装配投板
must.be.board.test=单板测试的第一个工序必须是单板测试投板
this.version.craft.exists=该版本已存在 {0}-{1} 工艺路径
the_current_version_is_malformed=当前版本格式错误，请确认
the_version_number_is_temporarily_only_supported=版本号暂时只支持V.+三位,请确认!
not.find.route.head.info=未找到对应的工艺信息，请检查
not.find.route.detail.info=未找到对应的工艺详情信息，请检查
craft_version_exist_submited=该料单已存在版本{0}的已提交的工艺数据
craft_version_exist=该料单已存在版本{0}的工艺数据
tags.param.is.null=标签参数列表为空，请检查
task.no.pulling=当前任务{0}正在从aps 拉取数据，请稍后再试。
task.no.not.exist.aps=当前任务号{0}在IAPS 没有有效数据请确认。
task.no.exist.imes=当前任务号{0}在IMES存在,不能再拉取。
prod.address.empty=配送地{0}配置项缺失，请检查。
bom.id.miss=BOM 数据缺失 bomId:{0},任务号：{1}
sn_carton_relation_err=infor发料数量为{0}, 条码中心SN数量为{1} 单据号:{2}, 箱号:{3}, SN:{4}, 与实际发料数量不符，请检查！
sn_center_greater_than_infor_err=条码中心SN数量为{0}大于infor发料数量{1}，单据详情请查看邮件！
sn_center_less_than_infor_err=条码中心SN数量为{0}小于infor发料数量{1}，与实际发料数量不符，请检查！
sku_is_not_ali_control_or_ali_code=物料代码{0}不是阿里管控料或者在客户物料表中不存在，请检查！
inFor_status_is_not_equal_to_9=此单据infor发料状态为{0}，不等于9，请检查！
box_has_not_been_fully_bound={0}箱未完成绑定!
barcodes_in_box_is_greater_than_infor_codes={0}箱条码数量>infor数量，不能上传。
board.assembly.query.params.not.null=主条码，子条码，开始/结束时间必须输入一个
board.assembly.query.time.not.paired=开始时间和结束时间必须同时输入
board.assembly.query.params.not.one.condition=主条码，子条码，开始/结束时间不能组合输入
board.assembly.query.page.and.row.not.null=使用时间区间查询时需要输入分页参数
board.assembly.query.time.interval.exceeds.one.month=使用时间区间查询时时间区间不能超过31天
board.assembly.query.row.too.large=使用时间区间查询时，分页的每一页行数不能超出100
recovery.finished=资源已回收，请检查
prod.plan.sequence.over= 中心工厂批次序列达到预警值，请及时处理，当前序列：{0}，最大序列：{1},阈值:{2}
prod.plan.exist.center.factory= {0} 批次再中心工厂存在，不能使用。
kafka.msg.save.database=kafka 批次消息消费失败，已经存入本地消息表，请及时分析处理,消息id:{0}
no.resource.recovery=没有资源要回收，请检查
excel.content.format.not.match=Excel内容和格式不匹配
the_current_status_of_the_approver_is_finished=未找到该节点该审批人对应的未审批信息，可能已完成审批，请确认
current_document_information_not_found=未找到当前单据信息，请确认!
params.err=输入参数不正确
mainCraftSection.is.null=请选择执行主工序
a.maximum.of.ten.data.can.be.queried=最多查询十条数据
check_that_the_product_code_is_empty=请检查产品代码或者位号是否为空
aps.proplanId.msg.error=APS 批次同步消息数据异常请检查！
factory.id.of.center.pstask.is.null=IMES中心工厂中该任务号所属工厂ID为空，请确认！
factory.id.of.center.pstask.is.illegality=IMES中心工厂中该任务号所属工厂ID不合法，请确认！
center.factory.not.exist.the.task.no=IME中心工厂不存在该任务号,请确认！
prodplanno.not.null=任务号不能为空
prodplanmodifyno.not.null=变更单号不能为空
failed_to_deal_technical_bill=当前技改单{0}处理失败,请确认！
more_than_50_batches=技改单{0}在线批次超过50个,请重新提单！
reelid_already_exists=reelid:{0}已存在
kafka.msg.save.database.task.update=计划任务修改kafka消息 消费失败，已经存入本地消息表，请及时分析处理,消息id:{0}
get.boardonline.info.failed = 获取board_online信息失败
get.barsubmit.info.failed = 获取barsubmit信息失败
failed.to.update.sys.look.up.meaning=更新数据字典值失败
query.params.lost=任务号、批次号、料单代码、条码必传一个

technical_info_lost=技改信息同步失败,请确认！
technical_info_outnumber=技改回写条码数量超过500万,传给MES存在一定延迟，请关注！
aps.task.status.error={0} 在APS任务状态不是已发放，不能手动拉，请确认！
the_process_path_of_the_material_list_is_not_found=未找到料单的工艺路径数据,系统未自动解析绑定关系
product.code.include.pcb={0}料单包含PCB请点击手工执行BOM分阶按钮进行计算!
the.material.list.code.should.contain.15.characters=料单代码长度应为15个字符，请检查！
no.bom.information.is.found=未找到料单信息，请检查！
synchronize.spm.data.warning=同步SPM技改信息部分成功，存在以下数据需要分析处理：{0}
synchronize.spm.data.error=同步SPM技改信息失败，同步时间区间为{0}至{1}，失败原因：
get.last.sync.time.error=获取SPM最后同步时间失败，具体错误信息如下：
spm.data.not.have.chg.reg.no=SPM该行数据无技改单号
spm.data.not.have.prod.id=SPM该行数据无批次号
the_itemnos_is_more_than_three_hundred=料单代码数量超过三百限制！
the_itemnos_is_more_than_one_thousand=料单代码数量超过一千限制！
the_itemnos_is_more_than_ten=输入的料单代码不能超过10个！
params.can.not.be.null=料单代码和料单名称必输其一
query.bom.no.null=未获取到料单数据
param.size.between.1.and.1000=参数个数必须为1-1000
task.no.cannot.be.null=计划跟踪单号不能为空
send.semi.wip.ext.by.spm.lock=正在回写半成品装配关系中
send.material.wip.ext.by.spm.lock=正在回写原材料装配关系中
no.find.wip.ext.prod.plan=未找到批次{0}，以上批次条码装配关系回写imes失败，请关注！
push.wip.ext.to.factory.failed=装配关系回写本地工厂失败{0}
failed_to_obtain_prod_plan_imes_batch_information=获取prod_plan_imes批次信息失败
param.missing=必填参数缺失
query.params.empty.except.create.by=创建人以外的查询时间不能均为空
is.updating.please.wait=当前料单正在执行操作,请稍后
bom.temperature.is.exited={0}工艺段,{1}料单,{2}面,{3},{4},{5}已存在炉温名称,请确认!
failed_to_get_spm_lock_information=获取SPM锁定锁定信息失败
failed_to_write_local_factory_lock_information=写本地工厂锁定信息失败：{0}
production_type_already_exists=已存在生产类型 {0} 的工艺路径，不能再新增生产类型 {1} 的工艺路径
the_process_path_of_the_maintained_process_type=已维护工艺类型 {0} 的工艺路径
the_current_process_path_is_being_processed=当前工艺路径正在处理中，请稍后再试
currently_exporting=当前正在导出
lookup.6001.empty=单板统计日报数据字段配置（6001）为空
data_volume_exceeds_5_w=数据量超过5W，已邮件导出，请关注邮件信息
no.bom.information.or.no.route=未找到料单信息或未维护工艺路径,请确认！
center.task.query.param.is.null=任务信息查询参数为空，请确认！
center.task.query.page.or.row.illegal=任务信息查询页码和行数非法，请确认！
center.task.query.five.params.not.all.null=任务信息查询任务号、批次、释放日、预计发放日、发放日条件不能全部为空，请确认！
center.task.query.time.larger.2.years.when.not.prod.and.task=任务号、批次未输入时，释放日、预计发放日、发放日若为查询条件，时间不能超过2年(365天*2)
customer.items.params.null=参数为空
customer.type.null=类型为空
customer.params.null.four=类型为{0},客户名称、ZTE代码、供应商、规格型号均不能为空
customer.params.null.one.two.zero=类型为{0},项目类型不能为空
customer.params.null.other=类型为{0},客户名称、项目名称、ZTE代码均不能为空
customer.items.add.or.update=当前数据正在提交,请稍后再试
check.error.please.reselect.file.parsing=校验出错，请重新选择文件解析
customer.items.exist=当前物料信息已存在，不允许{0}，请检查{1}
customer.qry.params.null=项目名称、时间不能均为空
qry.time.can.not.greater.one.year=查询时间范围不能超过两年
no.id.cannot.delete=请传入id
sub.customer.null=请传入子公司名称
item.no.list.null=请传入物料代码
max.item.no.is.once=一次最多只能查询500个物料代码信息
sub.customer.not.exist=数据字典7300未配置{0}子公司信息
size.of.data.too.large=推送的数据每次不能超过500条，请确认！
push.time.within.180.days=推送时间范围超过180天
log.data.query.param.null=客户数据推送日志查询参数不能全部为空，请确认！
log.data.query.param.not,with.time=客户名称，数据类别，推送状态需要和推送时间一起查询
log.data.lookup.type.error=获取推送接口信息失败,请确认是否配置数据字典{0}
log.data.not.match.lookup.type=重推数据没有匹配到对应的数据类型，请确认！
sign.illegal=签名非法，请确认！
url.empty=请求ulr不能为空
zj.customer.model.null=类型为整机时客户型号不能为空
workshop_capacity_already_exists={0}, 车间资源能力已存在
spare.part.detail.error=辅料调拨明细存在辅料名称/数量为空的数据
spare.part.approve.error=辅料调拨审批明细存在审批人/岗位为空的数据
spare.part.bill.lost=辅料调拨单据{0}信息不存在
spare.part.status.error=辅料调拨单据状态不是拟制中/校验失败不能修改
spare.part.status.error.msg=辅料调拨单据状态待验证不能修改
spare.part.partName.error=名称为空
spare.part.quantity.error=数量只能是正整数
spare.part.partName.repetition=单据明细名称重复
spare.part.factory.error=接收工厂和调拨工厂不能是同一个
spare.part.approve.lost=缺失审批节点，请添加{0}
spare.part.bill.no.not.null=调拨单号不能为空
get.fixture.model.head.id.null=获取工装型号失败，headId为空
item.detail.can.not.be.empty=调拨物料详情不能为空
not.in.the.document.details={0}不在{1}单据拣料明细中
close.bill.detail.allocation.status.wrong=调拨单据下对应辅料调拨状态存在非已回退或已调拨状态，无法关闭，请确认！
spare.part.allocation.query.param.null=查询参数错误，调拨单、备品辅料编码可直接查询，其余条件需配合接收时间/调出时间/调拨单查询
day.over.one.year=时间超过一年，不能查询
duplicate_material_storage_attributes=条码或物料代码+供应商已存在物料存储属性！
sn.list.of.ca.certificate.null=待导入证书的标模条码为空，请确认！
sn.list.of.ca.certificate.more.200=本次待导入证书的标模条码数量超出200,不允许导入,请确认！
sn.ca.bind.have.sn.info=条码已导入，无需再次导入
sn.ca.check.error=条码{0}失败原因:{1};
sn.ca.check.error.head=部分条码校验未通过，如下：
page.params.of.ca.query.null=查询CA证书，分页参数不能为空
sn.list.of.ca.query.null=查询CA证书，整机条码不能为空
barcode.not.registered=存在未在条码中心注册条码{0}，请确认！
date.range.repeat=公假时间与已有公假:{0} {1}存在重复，请确认！
holiday.params.error=公假查询条件不能全为空，请确认！
holiday.repeat=已维护该年对应公假{0} {1}，无法新增，请修改或者删除原公假
holiday.date.null=新增/修改传入时间参数为空，请确认！
holiday.year.error=公假年份与时间范围中年份不同，请确认！
pws.is.wrong=接收人输入密码错误，请确认！
factory.id.is.same=当前选择工厂和已发放工厂一致，不能取消发放
batch_has_a_lock_order=批次存在锁定单{0}，请解锁之后再取消发放
no.product.has.process.code=涉及未绑定料单工艺路径均不包含{0}子工序，请确认
pls.select.process.code=请选择子工序
process.code.cannot.be.n=子工序不能为入库
date.range.more.than.90.days=时间跨度超过90天

dimension_null=维度不能为空
production_unit_null=生产单位不能为空
work_order_category_null=工单类别不能为空
plan_group_null=计划组不能为空
model_name_null=机型不能为空
utilization_rate_null=产能利用率不能为空
capacity_day_null=日期不能为空
model_or_plan_group_more_then_ten=机型或计划组不能超过10个

resource.warning.waterLevel.title=资源池存在低于水位值的资源，请及时处理!
resource.warning.waterLevel.tip=以下资源已低于水位值:
resource.warning.expiryDate.title=资源池存在即将超期的资源，请及时处理!
resource.warning.expiryDate.tip=以下资源即将超期:
resource.warning.resourceNo=资源编号
resource.warning.deviceType=设备类型
resource.warning.lowWaterLevel=水位值
resource.warning.availableQuantity=可用资源数量
resource.warning.expiryDate=有效期
resource.warning.expiryDateBefore=有效预警期限（天）

resource.warning.import.check.fail=资源预警维护导入校验失败
resource.warning.required.one=水位值/预警天数必选一;
resource.warning.resourceNo.notEmpty=资源编号不能为空
resource.warning.deviceType.notEmpty=设备型号不能为空
resource.warning.resource.exist=资源编号已存在
resource.warning.device.exist=设备型号已存在
resource.warning.resource.device.noExist=资源类型与设备型号关系不存在
resource.warning.import.max=导入最大支持1000条
resource.warning.type.notEmpty=预警维度不能为空;
resource.warning.device.isNUll=设备型号不存在;
resource.warning.resource.isNUll=资源类型不存在;
resource.warning.water.level.isNumeric=水位值必须是正整数;
resource.warning.expiry.date.isNumeric=有效预警天数必须是正整数;
resource.warning.excel.resource.isSame=excel中存在相同的资源编号;
resource.warning.excel.device.isSame=excel中存在相同的设备类型;
resource.warning.database.resourceType.device.isSame=已维护资源编号相同的设备型号,不允许重复录入;
resource.warning.database.device.isMultiple=数据库存在多个相同设备型号;
resource.warning.database.device.type.isSame={0}资源编号对应的设备类型{1}已维护预警规则,不允许重复录入;
resource.warning.database.resource.same.device.different=已维护相同资源编号不同设备型号,不允许重复录入;
resource.warning.database.resource.isMultiple=数据库存在多个相同资源编号;
resource.warning.database.resource.isEmpty=资源编号数据库不存在;
resource.warning.database.resource.device.isEmpty=资源编号对应设备型号数据库不存在;
resource.warning.database.resource.device.mismatch=资源编号和设备型号不匹配;
resource.warning.type.notSupport=系统不支持此预警维度

network.license.cert.name=证书名称仅支持“进网试用”、“进网许可”;
network.license.resource.type=认证类别仅支持“入网许可”;
network.license.file.name.error=文件名错误，必须为14位资源编号开头的txt文件！
network.license.file.max=最大仅支持30万行！
network.license.file.import=导入文件异常！
network.license.file.error=解析错误！
network.license.sign.exits=电子标签文档内资源已在系统存在，请勿重复导入!
network.license.sign.file.exits=重复，请勿重复导入!
network.license.print.shortage={0}可用资源数量不足!
Hard.Cord.Time.Format=日期输入错误，格式示例: 2022-10-01
get.aps.plan.group.and.model.error=获取aps计划组以及机型信息错误
get.aps.plan.group.and.model.errors=获取aps计划组以及机型信息错误,错误信息:{0}
more.than.max.num=超过最大处理数量{0}

network.license.binding.isNUll={0}不存在，请扫描正确的标志号
network.license.binding.allocated={0}状态不为已分配，请扫描正确的标志号
network.license.binding.repeated={0}已绑定{1}批文，不能重复绑定
resource.no.available.quantity.not.enough={0}资源编号可用数量为{1}，请更换资源编号
current_resource_number_is_operation=当前资源编号正在操作中，请稍后再试
operation_time_can_not_greater_three_months=操作时间范围不能超过三个月
no_current_barcode_binding_record_found=未找到当前条码绑定记录
failed_to_get_preview_link=获取文件预览连接失败{0}
failed_to_generate_preview_header=生成文件预览鉴权头信息失败
approval_comments_cannot_be_empty_when_rejecting=拒绝时审批意见不能为空
query.time.and.billNo.is.null=除异常单号外其他条件要配合登记时间查询
query.last.time.and.billNo.is.null=除异常单号外其他条件要配合最后更新时间查询
time_interval_more_than_half_year=登记时间查询范围不得超过半年
last_time_interval_more_than_half_year=最后更新时间查询范围不得超过半年
the_person_to_be_handed_over_to_can_not_be_null=转交人不能为空
the_approval_type_is_incorrect=审批类型不对
fail_to_upload_file=文件上传失败
file.type.illegal=文件类型不正确，请确认！
bill_info_input_param_empty=异常单号、基地、部门和处理人均不能为空
please_maintain_at_least_one=请维护至少一条单据详情
input_sn_is_null_or_duplicate=录入详情中的条码不能为空且不能重复，请确认
type_or_dec_of_sn_is_null=请将条码{0}的异常类型和异常描述补充完整
qty.of.barcode.can.not.be.null=条码{0}数量不能为空
no.corresponding.documents.obtained.in.the.approval.center=未在审批中心获取到相应单据
external.type.is.null=条码{0}产品大类不能为空
style.is.null=条码{0}机型不能为空
operation_time_can_not_greater_ont_months=绑定时间范围不能超过一个月
operation_time_can_not=绑定时间范围不能为空

resource_use_no_is_empty_or_status_error=资源标号不存在或状态异常
resource_use_barcode_type_not_correct=请输入正确的序号类型
resource_use_num_is_empty=资源号不存在
resource_use_no_is_diff=所属资源编号与输入资源编号不一致
resource_use_status_not_allow_import=资源状态为{0}不允许进行补录
resource_use_num_exist_same=存在相同的资源号
resource_use_barcode_no_same=同一产品SN/MAC不允许使用同一资源编号内的资源号
resource_use_barcode_type_is_diff=所属序号类型与输入序号类型不一致
resource_use_data_option_error=数据处理异常
resource_use_import_error=文件导入异常:
max.export.is.once=单次最多只能导出1000000条！
exceed.max.export.count=超过最大导出数量:{0}
resource_use_import_file_is_empty=资源使用记录导入文件不能为空
scrap.num.bigger.than.data.num=输入报废数量超过资源编号下资源号可报废数量{0}
scrap.num.over.limit=资源报废单次最多只能报废30W条数据
query.resource.scrap.params.error=资源报废查询条件不能为空，操作人需要和操作时间结合查询
query.resource.scrap.date.range.over.year=操作时间范围不能超过一年
network.license.print.num.max=打印资源号总量超过1000
network.license.print.sn.max=打印SN数量超过1000
network.license.print.same=资源号与SN不能同时打印
cf.user.menu.max.count=当前工厂下到达菜单最大收藏数:{0},请先删除部分再收藏！
operation_in_progress_please_wait=正在操作中，请稍后
failed_to_call_b2b_interface=调B2B接口失败,错误信息{0}
ai_question_not_found=您咨询的问题没有找到，请联系运维人员
product.code.not.same.error=输入料单与写片信息中的料单不一致，请确认！
product.code.length.error=料单代码长度应为12位或15位，请确认！
mds.in.program.error=MDS触发iMES前加工数据接口异常，具体异常信息为：{0}
product.code.num.over.ten=料单对应15位料单超过10条，无法执行，请确认！
cloud.disk.input.err=文档云保存文件失败
productCode.is.not.import.cdaFile=该产品代码没有导入CAD文件
call.barCode.center.expandQuery.barCode.falied=调条码中心获取条码扩展信息失败:{0}
data.delete.refresh.page=数据已经被删除,请刷新页面!
customer.param.table.error=客户参数{0}配置信息表{1}有误请确认！
dqas.error=中试接口调用异常,接口信息:{0}
xp.auto.sync.failed.title.one=MDS触发写片信息同步失败,料单:{0},请确认后进行人工维护
xp.auto.sync.failed.title.two=写片前加工自动维护失败：料单:{0},单板名称:{1},PCB版本:{2},请确认后进行人工维护
xp.auto.sync.failed.title.three=写片前加工自动维护失败：料单:{0},单板名称:{1},PCB版本:{2},请确认后进行人工维护,并执行BOM分阶。
xp.auto.sync.failed.msg.two= {0}料单前加工写片数据在UTS或PCB升级但研发未启用
xp.auto.sync.failed.msg.three={0}料单在MDS无写片信息
xp.auto.sync.failed.msg.four={0}料单未查到位号信息,无法同步写片信息,请确认执行BOM位号解析后重新操作
xp.auto.sync.failed.msg=写片前加工自动维护失败,异常原因: {0}
pcbversion.not.exist=单板版本不存在
resource.num.max.one.hundred=资源编号批量查询最多支持100条
relationship.verify.not.pass=领用关系新增/修改参数校验不通过，具体原因为：{0}
change.order.no.data.null=未获取到变更单号对应数据
params.lacking=参数缺失
not.found.item.info=未获取到物料信息
the_model_code_of_the_resource_number_is_empty=资源编号{0}型号编码为空，不能打印
Please_enter_the_question_content=请输入提问内容
bom.item.use.account = 物料代码 {0},Bom分阶用量：{1},Bom用量:{2}
bom.exception.message= 料单代码 {0} BOM分阶异常 {1} 请检查该料单,料单级前加工数据！
please.input.one.condition=请至少输入一个查询条件
the_model_code_is_empty= 模型编码为空，不能分配

resource.no.rule.invalid=资源号规则不是有效的正则表达式，请确认！
resource.step.is.different=输入的单台用量与申请单的单台用量不一致，请确认！
params.rule.error=参数规则输入错误，请确认！
no.mac.available.resource=MAC资源数量不足，请确认！
no.gpon.sn.available.resource=GPON-SN资源数量不足，请确认！
no.available.resource=无可用{0}资源
random.numbers.must.greater.than.or.equal.3=随机位数必须大于等于3
item.no.is.invalid=物料代码：{0} 不存在，请确认
task.is.generating=该任务个参正在生成中
apply.qty.is.exceed.max=申请数量不能超过{0}
emp.no.has.undone.task=该工号有未完成的任务，请先完成！
aoi.img.not.exist=料单对应AOI图片不存在
eigen.value.exceed.max=移动SN特征值[{0}]剩余可用数值不足，已累计值为{1}
sub.customer.config.lost=子公司{0}没有配置客户名称配置项{1}
ucs.login.error=UCS系统登录异常，请确认！
aps.task.no.is.null=计划跟踪单号不能为空，请确认！
task.no.info.is.null=计划跟踪单号在中心工厂未找到对应信息，请确认！
aps.task.no.info.factory.id.is.null=计划跟踪单号对应的任务信息中工厂id为空，请确认！
kafka.msg.save.database.derivativeCode.update=衍生码kafka消息 消费失败，已经存入本地消息表，请及时分析处理,消息id:{0}
task.no.info.not.exist=计划跟踪单号{0}在中心工厂未找到对应信息，请确认！
task.no.transferring=计划跟踪单号已产生或者正在产生调拨单或者套料单，请确认！
no_derivative_code_change_information_found=未查询到APS衍生码变更信息
original_bom_information_not_queried=未查询到料单原始BOM信息
this_task_version_has_already_been_processed=此任务+版本已处理过
consent.available.quantity.is.insufficient=入网批文资源号可用数量不足
write.back.erp.error=工时回写ERP错误！{0}
net.work.params.must.existing.simultaneously=入网参数{NASN,ScramblingCode,NACC}必须同时存在
variable.does.not.exist=参数变量{0}不存在，请确认
user.no.permissions = 当前用户无api修改权限，请联系项目组处理!
param_zte_code_not_blank=入参ZTE代码不能为空
pack_list_already_existed=客户名称{0}，客户部件类型{1}箱单信息已存在，请确认!
failed_to_obtain_ucs_public_key=获取ucs公钥失败
cpqd.instance.no.lost=CPQD 产品实例:{0} FIXBOM编码缺失!
icc.mbom.lost=ICC 客户{0} 二/三段码{1} Mbom数据缺失!
pdm.mbom.lost=PDM 料单{0} 物料清单缺失！
pdm.mbom.item.icc.mbom.lost=PDM 料单{0}中物料{1}在ICC Mbom中缺失！
task.fix.bom.error=任务{0} fixBom 生成失败原因:{1}.
task.fix.bom.subject=任务FixBom生成失败请关注!
task.list.not.blank=任务号数组不能为空!
fix.bom.detail.lost=任务{0} FixBom 数据缺失请确认!
fix.bom.incomplete={0}任务未获取到客户指令信息，不允许发放至本地工厂生产
split.not.allowed=任务{0}不允许拆分
customer.to.lookupType.config.lost=未配置客户{0}与数据字典编号的映射关系
get.url.null=未获取数据字典:{0}的URL
erpstock.null=未获取仓库代码:{0}的ERP子库存
level.zero.fix.bom.detail.error=0 层 FixBom 数据缺失请确认!
zte.code.fixbomid.is.null=ZTE物料代码 {0} 不存在对应fixbomid
delivery.feedback.is.must=必须选择反馈时间
max.search.tasks=最大查询:{0}个任务号
sn.data.size.over=数据量不能超过{0}条
task.no.repeat=导入的excel中任务号不能重复！请用户检查后上传！
abnormal.no.repeat=延期编号不能重复！请检查！
submit.failed=提交失败,原因:{0}
task.no.not.empty=任务号不能为空
task.no.status=任务号状态必须为已开工、已排产和已发放
delivery.feedback.date.not.empty=预计完工日期、厂商自供料预计齐套日期、全部物料预计齐套日期、厂商自供料实际齐套日期、全部物料实际齐套日期、预计投产日期、实际投产日期不能为空
task.delayed.reason.not.empty=任务号延期，需要必填延期原因等
custom.no.not.alibaba=必须是阿里任务
delivery.feedback.no.receiver=生产交期反馈邮件配置目录代码:{0},没有维护接收人
customer.code.name.not.match=没有维护客户编码:{0} 与名称的对应关系
task.no.not.match.record=任务号:{0}找不到对应的生产交期反馈记录customer.to.lookupType.config.lost=未配置客户{0}与数据字典编号的映射关系
failed_to_call_mds_interface=调MDS接口失败:{0}
sn.push.std.model.lost=条码{0}推送表数据不存在
required.task.no.or.fix.bom.id=任务号和fixbomid至少输一个
abnormal.category.first.error=一级原因分类不正确
abnormal.category.second.error=二级原因分类不正确
abnormal.category.third.error=三级原因分类不正确
timeproduction.need.later.than.dateallmaterialprepared=实际投产日期{0}需晚于全部物料实际齐套日期{1}
timeproduction.need.later.than.timematerialprepared=实际投产日期{0}需晚于厂商自供料实际齐套日期{1}
timeproduction.need.later.than.firstpickingdate=实际投产日期{0}需晚于首次领料日期{1}
stbidtype.num.overone=当前模板的STBID参数多于一个
stbidtype.noauto=当前系统不支持与数字化平台对接
stbidtype.nostbidprefix=未获取到STBID参数前缀信息
stbidtype.stbidcfg.error=输入字符串格式不正确，应为BBBB-CCCCCC-DDD-E-FFFF格式
stbidtype.stbidtemplate.error=STBID参数模板配置错误
generate.task.no.redis.key.error=获取生成任务号redis锁失败
syncmds.fmtype.error=获取MDS对应产品大类失败,当前传递IMES大类编码为:{0}
syncmds.fmtype.repeatoperation.error=正在同步给MDS,请勿重复操作
syncmds.specialityparamid.empty.error=Specialityparamid不能为空.
syncmds.specialityparamid.repeat.error=已同步给MDS,请勿重复操作
no.match.sn=无匹配条码! 物料代码: {0} 上层虚拟条码: {1}
speciality.resource.abnormality=该模板未包含资源参数，请确认！
cf.api.resource.empty = IMES前端页面配置的资源不能为空!
baggage.param.lost=领料单号/领料时间 不能全为空!
baggage.param.time.over=查询时间范围不能超过{0}天
row.col.read.error=第{0}行，第{1}列读取错误，请确认
