package com.zte.common.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.km.udm.common.util.JacksonJsonConverUtil;
import com.zte.util.BaseTestCase;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

@PrepareForTest({MicroServiceRestUtil.class, JacksonJsonConverUtil.class})
public class NetworkLicenseUploadB2BTest extends BaseTestCase {

	@InjectMocks
	private NetworkLicenseUploadB2B networkLicenseUploadB2B;
	@Mock
	private JsonNode json;
	@Mock
	private ObjectMapper mapperInstance;

	@Test
	public void testGetRequestBody() {
		Assert.assertNotNull(networkLicenseUploadB2B.getRequestBody(new Object()));
	}
	@Test
	public void testHandleResponse() throws Exception {
		PowerMockito.mockStatic(JacksonJsonConverUtil.class);
		try{
			networkLicenseUploadB2B.handleResponse("");
		}catch(Exception e){
			Assert.assertTrue(e.getMessage().length()>0);
		}
		String response = "{\"code\":{\"code\":\"0001\",\"msgId\":\"\"," +
				"\"msg\":\"ERROR MESSAGE-{\\\"ErrorInfo\\\":\\\"文件已经上传过\\\",\\\"RspCode\\\":\\\"0001\\\"}-\"},\"bo\":\"{\\\"ErrorInfo\\\":\\\"文件已经上传过\\\",\\\"RspCode\\\":\\\"0001\\\"}\",\"other\":\"\"}";
		try{
			networkLicenseUploadB2B.handleResponse(response);
		}catch(Exception e){
			Assert.assertTrue(e.getMessage().length()>0);
		}
		PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
		try{
			PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(null);}
		catch (Exception e){
			Assert.assertTrue(e.getMessage().length()>0);
		}
		PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
		PowerMockito.when(json.get(Mockito.any())).thenReturn(json);

		PowerMockito.when(json.asText()).thenReturn("0001");
		try{
			networkLicenseUploadB2B.handleResponse(response);
		}catch(Exception e){
			Assert.assertTrue(e.getMessage().length()>0);
		}


		PowerMockito.when(json.asText()).thenReturn("0008");
		try{
			networkLicenseUploadB2B.handleResponse(response);
		}catch(Exception e){
			Assert.assertTrue(e.getMessage().length()>0);
		}

		String response1 = "{\"code\":{\"code\":\"0005\",\"msgId\":\"\"," +
				"\"msg\":\"ERROR MESSAGE-{\\\"ErrorInfo\\\":\\\"文件已经上传过\\\",\\\"RspCode\\\":\\\"0001\\\"}-\"},\"bo\":\"{\\\"ErrorInfo\\\":\\\"文件已经上传过\\\",\\\"RspCode\\\":\\\"0001\\\"}\",\"other\":\"\"}";
		networkLicenseUploadB2B.handleResponse(response1);
	}
}