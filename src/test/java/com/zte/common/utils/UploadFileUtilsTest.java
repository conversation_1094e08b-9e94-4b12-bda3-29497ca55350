package com.zte.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.zte.domain.model.MdsResponse;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.apache.http.HttpEntity;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.doNothing;
import static org.powermock.api.mockito.PowerMockito.mockStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpClients.class, LoggerFactory.class})
public class UploadFileUtilsTest {

    @Mock
    private CloseableHttpClient httpClient;
    @Mock
    private CloseableHttpResponse httpResponse;
    @Mock
    private StatusLine statusLine;
    @Mock
    private HttpEntity httpEntity;
    @Mock
    private Logger logger;

    @Test
    public void testUploadFile_Success() throws Exception {
        mockStatic(HttpClients.class);
        when(HttpClients.createDefault()).thenReturn(httpClient);
        doNothing().when(httpResponse).close();

        MultipartFile multipartFile = new MockMultipartFile("test", "test", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "test".getBytes());
        // 模拟HTTP响应
        StatusLine statusLine = Mockito.mock(StatusLine.class);
        when(httpResponse.getStatusLine()).thenReturn(statusLine);
        when(statusLine.getStatusCode()).thenReturn(200);
        // 模拟HTTP响应
        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpResponse.getEntity()).thenReturn(httpEntity);
        // 模拟成功的JSON响应
        MdsResponse mdsResponse = new MdsResponse();
        MdsResponse.Code code = new MdsResponse.Code();
        code.setCode(Constant.SUCCESS);
        mdsResponse.setCode(code);
        mdsResponse.setBo("successResult");
        String jsonResponse = JSONObject.toJSONString(mdsResponse);
        when(httpEntity.getContent()).thenReturn(new ByteArrayInputStream(jsonResponse.getBytes()));

        // 执行测试
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("fmType", "fmType");
        paramMap.put("taskNo", "task");
        String result = UploadFileUtils.uploadFile("uploadUrl", "fileName", multipartFile, paramMap);
        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(result, "successResult");
    }

    @Test
    public void testUploadFile_BusinessError() throws Exception {
        mockStatic(HttpClients.class);
        when(HttpClients.createDefault()).thenReturn(httpClient);
        doNothing().when(httpResponse).close();

        MultipartFile multipartFile = new MockMultipartFile("test", "test", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "test".getBytes());
        // 模拟HTTP响应
        StatusLine statusLine = Mockito.mock(StatusLine.class);
        when(httpResponse.getStatusLine()).thenReturn(statusLine);
        when(statusLine.getStatusCode()).thenReturn(200);
        // 模拟HTTP响应
        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpResponse.getEntity()).thenReturn(httpEntity);
        // 模拟成功的JSON响应
        MdsResponse mdsResponse = new MdsResponse();
        MdsResponse.Code code = new MdsResponse.Code();
        code.setCode("-1");
        code.setMsg("mdsErrorInfo");
        mdsResponse.setCode(code);
        String jsonResponse = JSONObject.toJSONString(mdsResponse);
        when(httpEntity.getContent()).thenReturn(new ByteArrayInputStream(jsonResponse.getBytes()));

        // 执行测试
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("fmType", "fmType");
        paramMap.put("taskNo", "task");
        try {
            String result = UploadFileUtils.uploadFile("uploadUrl", "fileName", multipartFile, paramMap);
        } catch (MesBusinessException er) {
            Assert.assertNotNull(er.getMessage());
            Assert.assertEquals(er.getMessage(), "uploadUrl:mdsErrorInfo");
        }

    }

    @Test
    public void testUploadFile_HttpError() throws Exception {
        mockStatic(HttpClients.class);
        when(HttpClients.createDefault()).thenReturn(httpClient);
        doNothing().when(httpResponse).close();

        MultipartFile multipartFile = new MockMultipartFile("test", "test", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "test".getBytes());
        // 模拟HTTP响应
        StatusLine statusLine = Mockito.mock(StatusLine.class);
        when(httpResponse.getStatusLine()).thenReturn(statusLine);
        when(statusLine.getStatusCode()).thenReturn(400);
        // 模拟HTTP响应
        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpResponse.getEntity()).thenReturn(httpEntity);
        // 模拟成功的JSON响应
        MdsResponse mdsResponse = new MdsResponse();
        MdsResponse.Code code = new MdsResponse.Code();
        code.setCode("-1");
        code.setMsg("mdsErrorInfo");
        mdsResponse.setCode(code);
        String jsonResponse = JSONObject.toJSONString(mdsResponse);
        when(httpEntity.getContent()).thenReturn(new ByteArrayInputStream(jsonResponse.getBytes()));

        // 执行测试
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("fmType", "fmType");
        paramMap.put("taskNo", "task");
        try {
            String result = UploadFileUtils.uploadFile("uploadUrl", "fileName", multipartFile, paramMap);
        } catch (RuntimeException er) {
            Assert.assertNotNull(er.getMessage());
        }

    }


}