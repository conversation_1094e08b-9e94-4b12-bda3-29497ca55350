package com.zte.common.utils;
/* Started by AICoder, pid:m78c2o8e4cb521e143860a471160aa1a17742b6e */

import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisLock;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.function.Supplier;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisLock.class, SyncUtils.class})
public class SyncUtilsTest {

    @Mock
    private RedisLock mockRedisLock;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testSyncHandle_whenLockAcquired_thenReturnsSupplierValue() throws Exception {
        // Arrange
        String key = "testKey";
        Supplier<String> supplier = () -> "result";
        PowerMockito.whenNew(RedisLock.class).withArguments(key).thenReturn(mockRedisLock);
        when(mockRedisLock.lock()).thenReturn(true);

        // Act
        String result = SyncUtils.syncHandle(key, supplier);

        // Assert
        assertEquals("result", result);
        verify(mockRedisLock).unlock();
    }

    @Test
    public void testSyncHandle_whenLockNotAcquired_thenReturnsNull() throws Exception {
        // Arrange
        String key = "testKey";
        Supplier<String> supplier = () -> "result";
        PowerMockito.whenNew(RedisLock.class).withArguments(key).thenReturn(mockRedisLock);
        when(mockRedisLock.lock()).thenReturn(false);

        // Act
        String result = SyncUtils.syncHandle(key, supplier);

        // Assert
        assertNull(result);
        verify(mockRedisLock, never()).unlock();
    }

    @Test
    public void testSyncHandleWithException_whenLockAcquired_thenReturnsSupplierValue() throws Exception {
        // Arrange
        String key = "testKey";
        Supplier<String> supplier = () -> "result";
        PowerMockito.whenNew(RedisLock.class).withArguments(key).thenReturn(mockRedisLock);
        when(mockRedisLock.lock()).thenReturn(true);

        // Act
        String result = SyncUtils.syncHandleWithException(key, supplier);

        // Assert
        assertEquals("result", result);
        verify(mockRedisLock).unlock();
    }

    @Test(expected = MesBusinessException.class)
    public void testSyncHandleWithException_whenLockNotAcquired_thenThrowsException() throws Exception {
        // Arrange
        String key = "testKey";
        Supplier<String> supplier = () -> "result";
        PowerMockito.whenNew(RedisLock.class).withArguments(key).thenReturn(mockRedisLock);
        when(mockRedisLock.lock()).thenReturn(false);

        // Act & Assert
        SyncUtils.syncHandleWithException(key, supplier);
    }

    @Test
    public void testSyncHandle_withSupplierThrowingException_thenReturnsNull() throws Exception {
        // Arrange
        String key = "testKey";
        Supplier<String> supplier = () -> { throw new RuntimeException("Error"); };
        PowerMockito.whenNew(RedisLock.class).withArguments(key).thenReturn(mockRedisLock);
        when(mockRedisLock.lock()).thenReturn(false);

        // Act
        String result = SyncUtils.syncHandle(key, supplier);

        // Assert
        assertNull(result);
    }

    @Test(expected = MesBusinessException.class)
    public void testSyncHandleWithException_withSupplierThrowingException_thenThrowsException() throws Exception {
        // Arrange
        String key = "testKey";
        Supplier<String> supplier = () -> { throw new RuntimeException("Error"); };
        PowerMockito.whenNew(RedisLock.class).withArguments(key).thenReturn(mockRedisLock);
        when(mockRedisLock.lock()).thenReturn(false);

        // Act & Assert
        SyncUtils.syncHandleWithException(key, supplier);
    }
}

/* Ended by AICoder, pid:m78c2o8e4cb521e143860a471160aa1a17742b6e */