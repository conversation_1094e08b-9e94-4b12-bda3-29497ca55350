package com.zte.common.utils;

import org.junit.jupiter.api.Assertions;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
public class TripleDESUtilsTest {

    @org.junit.Test(expected = Exception.class)
    public void testDecryptThreeDESECB_validInput() throws Exception {
        // 假设加密后的密文和密钥
        String key = "123456789012345678901234"; // 24字节3DES密钥
        String plainText = "HelloWorld";
        // 这里需要有一个加密后的密文，建议用TripleDESUtils加密后再测试解密
        String encrypted = "加密后的base64密文"; // 需要替换为实际密文

        // 解密
        String decrypted = TripleDESUtils.decryptThreeDESECB(encrypted, key);

        // 断言
        Assertions.assertEquals(plainText, decrypted);
    }

    @org.junit.Test
    public void testDecryptThreeDESECB_invalidKey() {
        String encrypted = "无效密文";
        String key = "无效密钥";
        Assertions.assertThrows(Exception.class, () -> {
            TripleDESUtils.decryptThreeDESECB(encrypted, key);
        });
    }
}