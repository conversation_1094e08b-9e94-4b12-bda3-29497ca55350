package com.zte.common.utils;

import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.reflect.Whitebox;

import java.io.ByteArrayInputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.InetSocketAddress;
import java.net.Socket;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @Date 2024/8/22 10:25
 */
public class SocketUtilsTest extends BaseTestCase {

    @InjectMocks
    private SocketUtils socketUtils;

    @Mock
    private Socket mockSocket;

    @Mock
    private DataOutputStream mockOutputStream;

    @Mock
    private DataInputStream mockInputStream;

    @Test
    public void readInputStream() throws Exception {
        try (InputStream inputStream = new ByteArrayInputStream(new byte[]{123})) {
            Assert.assertNotNull(Whitebox.invokeMethod(socketUtils, "readInputStream", inputStream, 1));
        }
    }

    /*Started by AICoder, pid:ka8b2d94102f9d914a07097cb1f8ba07cfb2923d*/
    @Test(expected = MesBusinessException.class)
    public void testSendBySocketIOException() throws Exception {
        when(mockSocket.getOutputStream()).thenThrow(new IOException());
        try (Socket socket = mockSocket) {
            SocketUtils.sendBySocket("request", "127.0.0.1", 80, 100);
        }
    }

    @Test(expected = MesBusinessException.class)
    public void testReadInputStreamDataLenZero() throws Exception {
        when(mockInputStream.readInt()).thenReturn(0);
        try (Socket socket = mockSocket) {
            when(socket.getInputStream()).thenReturn(mockInputStream);
            when(socket.getOutputStream()).thenReturn(mockOutputStream);
            socket.connect(new InetSocketAddress("127.0.0.1", 80), 100);
            SocketUtils.sendBySocket("request", "127.0.0.1", 80, 100);
        }
    }

    @Test(expected = MesBusinessException.class)
    public void testReadInputStreamIncompleteData() throws Exception {
        when(mockInputStream.readInt()).thenReturn(10);
        when(mockInputStream.read(any(byte[].class))).thenReturn(-1);
        try (Socket socket = mockSocket) {
            when(socket.getInputStream()).thenReturn(mockInputStream);
            when(socket.getOutputStream()).thenReturn(mockOutputStream);
            socket.connect(new InetSocketAddress("127.0.0.1", 80), 100);
            SocketUtils.sendBySocket("request", "127.0.0.1", 80, 100);
        }
    }

    @Test
    public void testSendBySocketSuccess() throws Exception {
        byte[] responseData = "response".getBytes("UTF-8");
        when(mockInputStream.readInt()).thenReturn(responseData.length);
        when(mockInputStream.read(any(byte[].class), anyInt(), anyInt())).thenAnswer(invocation -> {
            byte[] buffer = invocation.getArgument(0);
            int offset = invocation.getArgument(1);
            int length = Math.min(responseData.length - offset, invocation.getArgument(2));
            System.arraycopy(responseData, 0, buffer, offset, length);
            return length;
        });

        try (Socket socket = mockSocket) {
            when(socket.getInputStream()).thenReturn(mockInputStream);
            when(socket.getOutputStream()).thenReturn(mockOutputStream);
            socket.connect(new InetSocketAddress("127.0.0.1", 80), 100);
            try {
                String result = SocketUtils.sendBySocket("request", "127.0.0.1", 80, 100);
            } catch (Exception e) {
                Assert.assertEquals("socket.io.exception", e.getMessage());
            }
        }
    }
    /*Ended by AICoder, pid:ka8b2d94102f9d914a07097cb1f8ba07cfb2923d*/
}
