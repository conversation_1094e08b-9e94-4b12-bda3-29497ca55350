package com.zte.common.utils;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class, JacksonJsonConverUtil.class})
public class NetworkLicenseUploadResultB2BTest extends BaseTestCase {
    @InjectMocks
    private NetworkLicenseUploadResultB2B networkLicenseUploadResultB2B;
    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private JsonNode json;
    @Test
    public void getRequestBody() {
        Assert.assertNotNull(networkLicenseUploadResultB2B.getRequestBody(new Object()));
    }

    @Test
    public void handleResponse() throws Exception {
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        try{
            networkLicenseUploadResultB2B.handleResponse("");
        }catch(Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        String response = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":{\"RspCode\":\"0001\",\"Remark\":\"任务不存在！\"},\"other\":null}";
        try{
            networkLicenseUploadResultB2B.handleResponse(response);
        }catch(Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.get(Mockito.any())).thenReturn(json);

        PowerMockito.when(json.asText()).thenReturn("0001");
        try{
            networkLicenseUploadResultB2B.handleResponse(response);
        }catch(Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }


        PowerMockito.when(json.asText()).thenReturn("0008");
        try{
            networkLicenseUploadResultB2B.handleResponse(response);
        }catch(Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }

    }
}