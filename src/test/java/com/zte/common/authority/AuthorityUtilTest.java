/*Started by AICoder, pid:e210fr7ef8vf13d144360acdd0c93d514469185b*/
package com.zte.common.authority;

import com.zte.itp.msa.util.encrypt.secure.SecureEncryptorUtils;
import com.zte.util.BaseTestCase;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.lang.reflect.Method;
import java.security.MessageDigest;

import static org.junit.Assert.assertNotEquals;
import static org.junit.Assert.assertNotNull;

@PrepareForTest({SecureEncryptorUtils.class})
public class AuthorityUtilTest extends BaseTestCase {

    @InjectMocks
    private AuthorityUtil authorityUtil;
    @Mock
    SysAuthorityConfig sysAuthorityConfig;
    @Test
    public void initValue() throws Exception {
        PowerMockito.when(sysAuthorityConfig.getAccessKey()).thenReturn("2");
        PowerMockito.when(sysAuthorityConfig.getSecKey()).thenReturn("2");
        PowerMockito.mockStatic(SecureEncryptorUtils.class);
        assertNotNull(sysAuthorityConfig.getAccessKey());
    }
    @Test(timeout = 8000)
    public void testCacuAuthValue_WithNonNullSecKey() throws Exception {
        String result = authorityUtil.cacuAuthValue("1", "accessKey", "uuid", "secKey");
        assertNotNull(result);
        assertNotEquals("", result);
    }

    @Test(timeout = 8000)
    public void testCacuAuthValue_WithNullSecKey() throws Exception {
        String result = authorityUtil.cacuAuthValue("1", "accessKey", "uuid", null);
        assertNotNull(result);
        assertNotEquals("", result);
    }

    @Test(timeout = 8000)
    public void testCacuAuthValue_WithException() throws Exception {
        Method method =
                MessageDigest.class.getDeclaredMethod(
                        "getInstance", new Class[] {String.class});
        method.setAccessible(true);
        method.invoke(null, "SHA-256");
        String result = authorityUtil.cacuAuthValue("1", "accessKey", "uuid", "secKey");
        assertNotNull(result);
        assertNotEquals("", result);
    }
}
/*Ended by AICoder, pid:e210fr7ef8vf13d144360acdd0c93d514469185b*/