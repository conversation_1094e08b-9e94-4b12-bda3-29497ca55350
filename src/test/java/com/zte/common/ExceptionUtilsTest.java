package com.zte.common;
/* Started by AICoder, pid:27dccled2f3c470146ee088ef0438f6e0735fea4 */

import cn.hutool.core.exceptions.ExceptionUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class, ExceptionUtil.class})
public class ExceptionUtilsTest {

    @InjectMocks
    private ExceptionUtils exceptionUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 getMessage 方法，当异常是 MesBusinessException 时。
     */
    @Test
    public void testGetMessage_MesBusinessException() {
        MesBusinessException mesBusinessException = new MesBusinessException("error.code", "Error Message");
        mesBusinessException.setExMsgId("error.code");
        mesBusinessException.setParams(new Object[]{"param1", "param2"});
        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.getLmbMessage(anyString(), any(String[].class))).thenReturn("Localized Error Message");

        String result = ExceptionUtils.getMessage(mesBusinessException);

        assertEquals("Localized Error Message", result);
    }

    /**
     * 测试 getMessage 方法，当异常不是 MesBusinessException 时。
     */
    @Test
    public void testGetMessage_OtherException() {
        RuntimeException runtimeException = new RuntimeException("Runtime Error");
        PowerMockito.mockStatic(ExceptionUtil.class);
        when(ExceptionUtil.getMessage(any(Throwable.class))).thenReturn("Runtime Error Message");

        String result = ExceptionUtils.getMessage(runtimeException);

        assertEquals("Runtime Error Message", result);
    }
}

/* Ended by AICoder, pid:27dccled2f3c470146ee088ef0438f6e0735fea4 */