package com.zte.common.excel;

import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.dto.AbnormalMaterialBillDetailDTO;
import com.zte.springbootframe.util.FileUtils;
import com.zte.util.BaseTestCase;
import javassist.bytecode.stackmap.BasicBlock;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.List;

@PrepareForTest({Constant.class, SXSSFWorkbook.class, FileUtils.class,ExcelCommonUtils.class})
public class ExcelCommonUtilsTest{
    @Mock
    private SXSSFWorkbook workbook;
    @Mock
    private SXSSFSheet sheet;
    @Mock
    private SXSSFRow rowHead;
    @Mock
    private SXSSFCell cell;

    @Test
    public void testAppendSXSSFWorkbookByRows() {
        AbnormalMaterialBillDetailDTO abnormalMaterialBillDetailDTO = new AbnormalMaterialBillDetailDTO();
        abnormalMaterialBillDetailDTO.setSn("test");
        abnormalMaterialBillDetailDTO.setItemCode("test");
        abnormalMaterialBillDetailDTO.setQty(1);
        abnormalMaterialBillDetailDTO.setAbnormalType("测试不通过");
        abnormalMaterialBillDetailDTO.setAbnormalDescription("测试不通过");
        List<AbnormalMaterialBillDetailDTO> detailDTOList = new ArrayList<>();
        detailDTOList.add(abnormalMaterialBillDetailDTO);
        String[] valueFields = {};
        try {
            ExcelCommonUtils.appendSXSSFWorkbookByRows(sheet,detailDTOList,valueFields,1);
        } catch (Exception e){
            Assert.assertEquals(null, e.getMessage());
        }
    }
}