/*Started by AICoder, pid:a0348tcb91708b314326081231d5e5788233dd5b*/
package com.zte.common.excel;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ExcelCommonUtilsTest2 {

    @InjectMocks
    private ExcelCommonUtils excelCommonUtils;

    @Mock
    private SXSSFSheet sheetMock;

    @Mock
    private SXSSFRow rowMock;

    @Mock
    private SXSSFCell cellMock;

    @Before
    public void setUp() {
        // Setup any necessary mocks before each test
    }

    @Test
    public void testWriteSheetWithHeadersAndData() {
        String[] headers = {"Header1", "Header2"};
        String[] props = {"prop1", "prop2"};
        List<Object> dataList = Arrays.asList(
            new TestData("value1", "value2"),
            new TestData("value3", null)
        );

        when(sheetMock.createRow(0)).thenReturn(rowMock);
        when(sheetMock.createRow(1)).thenReturn(rowMock);
        when(sheetMock.createRow(2)).thenReturn(rowMock);
        when(rowMock.createCell(anyInt())).thenReturn(cellMock);

        excelCommonUtils.writeSheet(sheetMock, dataList, headers, props);


        verify(cellMock).setCellValue("Header1");
        verify(cellMock).setCellValue("Header2");
        verify(cellMock).setCellValue("value1");
        verify(cellMock).setCellValue("value2");
    }

    @Test
    public void testWriteSheetWithEmptyDataList() {
        String[] headers = {"Header1", "Header2"};
        String[] props = {"prop1", "prop2"};
        List<Object> dataList = Arrays.asList();

        when(sheetMock.createRow(0)).thenReturn(rowMock);
        when(rowMock.createCell(anyInt())).thenReturn(cellMock);

        excelCommonUtils.writeSheet(sheetMock, dataList, headers, props);

        verify(rowMock, times(2)).createCell(anyInt());
        verify(cellMock).setCellValue("Header1");
        verify(cellMock).setCellValue("Header2");
    }

    // Helper class for testing
    private static class TestData {
        private String prop1;
        private String prop2;
        private Date dateField;
        private String nullField;

        public TestData(String prop1, String prop2) {
            this.prop1 = prop1;
            this.prop2 = prop2;
        }

        public TestData(Date dateField, String nullField) {
            this.dateField = dateField;
            this.nullField = nullField;
        }

        public String getProp1() {
            return prop1;
        }

        public String getProp2() {
            return prop2;
        }

        public Date getDateField() {
            return dateField;
        }

        public String getNullField() {
            return nullField;
        }
    }
}
/*Ended by AICoder, pid:a0348tcb91708b314326081231d5e5788233dd5b*/