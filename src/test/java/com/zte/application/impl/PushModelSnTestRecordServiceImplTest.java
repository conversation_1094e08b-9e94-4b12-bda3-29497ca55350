/*Started by AICoder, pid:887f81985em282d14b3a0a520178c4327dc9231e*/
package com.zte.application.impl;

import com.zte.application.TradeDataLogService;
import com.zte.domain.model.PushModelSnTestRecord;
import com.zte.domain.model.PushModelSnTestRecordRepository;
import com.zte.domain.model.PushStdModelSnDataRepository;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.MdsFeedbackProductionStationFileDTO;
import com.zte.interfaces.dto.PushModelSnTestRecordDTO;
import com.zte.springbootframe.util.IdGenerator;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PushModelSnTestRecordServiceImplTest {

    @Mock
    private PushModelSnTestRecordRepository pushModelSnTestRecordRepository;

    @Mock
    private IdGenerator idGenerator;

    @InjectMocks
    private PushModelSnTestRecordServiceImpl pushModelSnTestRecordService;

    @Mock
    private TradeDataLogService tradeDataLogService;

    @Mock
    MdsRemoteService mdsRemoteService;

    @Mock
    PushStdModelSnDataRepository pushStdModelSnDataRepository;

    @Before
    public void setUp()throws Exception {
        when(idGenerator.snowFlakeIdStr()).thenReturn("1234567890");
        PowerMockito.field(PushModelSnTestRecordServiceImpl.class,"pageSize").set(pushModelSnTestRecordService,100);
    }

    @Test
    public void testGetById() {
        PowerMockito.when(pushModelSnTestRecordRepository.selectById(Mockito.any())).thenReturn(new PushModelSnTestRecord());
        pushModelSnTestRecordService.getById(null);
        assertNotNull(new ArrayList());
    }

    @Test
    public void testAdd() {
        PowerMockito.when(pushModelSnTestRecordRepository.insert(Mockito.any())).thenReturn(1L);
        pushModelSnTestRecordService.add(new PushModelSnTestRecord());
        assertNotNull(new ArrayList());
    }

    @Test
    public void testDeleteByIds() {
        PowerMockito.when(pushModelSnTestRecordRepository.deleteByIds(Mockito.any())).thenReturn(1L);
        pushModelSnTestRecordService.deleteByIds(new ArrayList());
        assertNotNull(new ArrayList());
    }

    @Test
    public void testUploadTheResultsOfStationAnalysis_NonEmptyList_WithRecords() throws Exception {
        List<String> snList = Arrays.asList("SN1", "SN2");

        PushModelSnTestRecord record1 = mock(PushModelSnTestRecord.class);
        when(record1.getRequestId()).thenReturn("REQ123");
        when(record1.getWorkorderId()).thenReturn("WO123");
        when(record1.getActionCode()).thenReturn("AC123");
        when(record1.getMessage()).thenReturn("Message123");

        List<PushModelSnTestRecord> records = new ArrayList<>();
        records.add(record1);

        when(pushModelSnTestRecordRepository.queryUploadSuccessList(any())).thenReturn(records, Collections.emptyList());

        pushModelSnTestRecordService.uploadTheResultsOfStationAnalysis(snList);

        verify(pushModelSnTestRecordRepository, times(2)).queryUploadSuccessList(any());
        verify(tradeDataLogService).pushDataOfExceptionRollback(anyList());
    }

    @Test
    public void testUploadTheResultsOfStationAnalysis_EmptyList() throws Exception {
        List<String> snList = Collections.emptyList();

        pushModelSnTestRecordService.uploadTheResultsOfStationAnalysis(snList);

        assertNotNull(snList);
    }

    @Test
    public void testUploadTheResultsOfStationAnalysis_NonEmptyList_NoRecords() throws Exception {
        List<String> snList = Arrays.asList("SN1", "SN2");

        when(pushModelSnTestRecordRepository.queryUploadSuccessList(any())).thenReturn(Collections.emptyList());

        pushModelSnTestRecordService.uploadTheResultsOfStationAnalysis(snList);

        verify(pushModelSnTestRecordRepository).queryUploadSuccessList(any());
        verify(tradeDataLogService, never()).pushDataOfExceptionRollback(anyList());
    }
    /*Ended by AICoder, pid:887f81985em282d14b3a0a520178c4327dc9231e*/
    /* Started by AICoder, pid:q86f0d8becx192514da90899308bcd2497702391 */
    @Test
    public void updateCurrProcessBySnAndTaskNo() throws Exception {
        PushModelSnTestRecord pushModelSnTestRecord = new PushModelSnTestRecord();

        // 调用 updateCurrProcessBySnAndTaskNo 方法，参数为 false
        Whitebox.invokeMethod(pushModelSnTestRecordService, "updateCurrProcessBySnAndTaskNo", false, pushModelSnTestRecord);
        // 验证调用后 pushModelSnTestRecord 的状态
        assertNotNull(pushModelSnTestRecord.getSn(), "Expected some field to be set after update with false");

        // 调用 updateCurrProcessBySnAndTaskNo 方法，参数为 true
        Whitebox.invokeMethod(pushModelSnTestRecordService, "updateCurrProcessBySnAndTaskNo", true, pushModelSnTestRecord);
        // 验证调用后 pushModelSnTestRecord 的状态
        assertNotNull(pushModelSnTestRecord.getSn(), "Expected another field to be set after update with true");
    }

    /* Ended by AICoder, pid:q86f0d8becx192514da90899308bcd2497702391 */

    /*Started by AICoder, pid:0fc4e72d2f0c55514bd40b0b20b57a7f4d58b213*/
    @Test
    public void testBatchInsertOrUpdate_SingleBatch() {
        // Given
        List<PushModelSnTestRecordDTO> records = Arrays.asList(new PushModelSnTestRecordDTO(), new PushModelSnTestRecordDTO());
        when(pushModelSnTestRecordRepository.batchInsertOrUpdate(anyList())).thenReturn(1L);

        // When
        pushModelSnTestRecordService.batchInsertOrUpdate(records);

        // Then
        verify(pushModelSnTestRecordRepository).batchInsertOrUpdate(records);
    }

    @Test
    public void testBatchInsertOrUpdate_MultipleBatches() {
        // Given
        List<PushModelSnTestRecordDTO> records = Arrays.asList(
                new PushModelSnTestRecordDTO(), new PushModelSnTestRecordDTO(),
                new PushModelSnTestRecordDTO(), new PushModelSnTestRecordDTO(),
                new PushModelSnTestRecordDTO(), new PushModelSnTestRecordDTO()
        );
        when(pushModelSnTestRecordRepository.batchInsertOrUpdate(anyList())).thenReturn(1L);

        // When
        pushModelSnTestRecordService.batchInsertOrUpdate(records);

        // Then
        verify(pushModelSnTestRecordRepository, Mockito.times(1)).batchInsertOrUpdate(anyList());
    }

    /*Ended by AICoder, pid:0fc4e72d2f0c55514bd40b0b20b57a7f4d58b213*/

    @Test
    public void getByIdList() {
        // Given
        List<String> records = Arrays.asList("new PushModelSnTestRecordDTO()");
        pushModelSnTestRecordService.getByIdList(null);
        Assert.assertNotNull(records);

        pushModelSnTestRecordService.getByIdList(records);
        Assert.assertNotNull(records);

        PowerMockito.when(pushModelSnTestRecordRepository.selectByIdList(anyList())).thenReturn(Arrays.asList(new PushModelSnTestRecord()));
        pushModelSnTestRecordService.getByIdList(records);
        Assert.assertNotNull(records);
    }


    /*Started by AICoder, pid:35ca8te1abic1ad14dd9088011e1236b9e23b929*/
    @Test
    public void testOssLogFileUpload_EmptyList() throws Exception {
        List<String> snList = Arrays.asList("SN1", "SN2");
        // Given
        when(pushModelSnTestRecordRepository.queryUploadSuccessList(any())).thenReturn(Collections.emptyList());

        // When
        pushModelSnTestRecordService.ossLogFileUpload(snList);

        // Then
        verify(tradeDataLogService, never()).pushDataOfExceptionRollback(anyList());
    }

    @Test
    public void testOssLogFileUpload_NonEmptyList() throws Exception {
        List<String> snList = Arrays.asList("SN1", "SN2");
        // Given
        List<PushModelSnTestRecord> records = new ArrayList<>();
        PushModelSnTestRecord record1 = new PushModelSnTestRecord();
        record1.setSn("SN1");
        record1.setRequestId("REQ1");
        record1.setWorkorderId("WO1");

        PushModelSnTestRecord record2 = new PushModelSnTestRecord();
        record2.setSn("SN2");
        record2.setRequestId("REQ2");
        record2.setWorkorderId("WO2");

        records.add(record1);
        records.add(record2);

        when(pushModelSnTestRecordRepository.queryUploadSuccessList(any())).thenReturn(records).thenReturn(Collections.emptyList());

        MdsFeedbackProductionStationFileDTO fileDto1 = new MdsFeedbackProductionStationFileDTO();
        fileDto1.setStationId("STATION1");

        MdsFeedbackProductionStationFileDTO fileDto2 = new MdsFeedbackProductionStationFileDTO();
        fileDto2.setStationId("STATION2");

        when(mdsRemoteService.feedbackCompleteMachineTestingGenerateFile(anyString(), anyList(), anyBoolean()))
                .thenReturn(Arrays.asList(fileDto1, fileDto2));

        //when(idGenerator.snowFlakeIdStr()).thenReturn("ID1", "ID2");

        // When
        pushModelSnTestRecordService.ossLogFileUpload(snList);

        // Then
        Assert.assertNotNull(snList);
    }
    /*Ended by AICoder, pid:35ca8te1abic1ad14dd9088011e1236b9e23b929*/

    @Test
    public void pushData()throws Exception {
        List<PushModelSnTestRecord> pushModelSnTestRecordList = new ArrayList<>();
        pushModelSnTestRecordList.add(new PushModelSnTestRecord(){{setRequestId("1");setStationId("1");}});
        pushModelSnTestRecordList.add(new PushModelSnTestRecord(){{setRequestId("0");setStationId("0");}});
        Map<String, List<MdsFeedbackProductionStationFileDTO>> stationMap= new HashMap<>();
        stationMap.put("1",Arrays.asList(new MdsFeedbackProductionStationFileDTO(){{setLogName("sn.log");}}));
        stationMap.put("2",Arrays.asList(new MdsFeedbackProductionStationFileDTO()));
        stationMap.put("3",Arrays.asList(new MdsFeedbackProductionStationFileDTO()));
        Whitebox.invokeMethod(pushModelSnTestRecordService,"pushData",pushModelSnTestRecordList,stationMap);
        Assert.assertNotNull(pushModelSnTestRecordList);

        pushModelSnTestRecordList.clear();
        pushModelSnTestRecordList.add(new PushModelSnTestRecord(){{setRequestId("0");setStationId("0");}});
        Whitebox.invokeMethod(pushModelSnTestRecordService,"pushData",pushModelSnTestRecordList,stationMap);
        Assert.assertNotNull(pushModelSnTestRecordList);

        pushModelSnTestRecordList.add(new PushModelSnTestRecord(){{setRequestId("0");setStationId("1");setNodeSn("sn");}});
        Whitebox.invokeMethod(pushModelSnTestRecordService,"pushData",pushModelSnTestRecordList,stationMap);
        Assert.assertNotNull(pushModelSnTestRecordList);
    }
}
