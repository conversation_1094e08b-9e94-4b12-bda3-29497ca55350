/*Started by AICoder, pid:ye317005c16e99c14f840956307b637d8982af64*/
package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.domain.model.PushStdModelSnDataRepository;
import com.zte.interfaces.dto.PushStdModelSnDataHandleDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
public class ProductSnReportDataHandleServiceImpl_handlePushStdModelSnDatas_1_Test {

    @InjectMocks
    private ProductSnReportDataHandleServiceImpl service;

    @Mock
    private PushStdModelSnDataRepository pushStdModelSnDataRepository;

    @Test
    public void testHandlePushStdModelSnDatas_EmptyList() {
        // Given
        List<PushStdModelSnDataHandleDTO> emptyList = Collections.emptyList();

        // When
        boolean result = service.handlePushStdModelSnDatas(emptyList);

        // Then
        assertFalse(result);
    }

    @Test
    public void testHandlePushStdModelSnDatas_NonEmptyList() {
        // Given
        List<PushStdModelSnDataHandleDTO> nonEmptyList = Lists.newArrayList(new PushStdModelSnDataHandleDTO());

        // Mocking the internal method call
        when(pushStdModelSnDataRepository.selectExtByPrimaryKey(any())).thenReturn(null);
        // When
        boolean result = service.handlePushStdModelSnDatas(nonEmptyList);

        // Then
        assertTrue(result);
    }
}
/*Ended by AICoder, pid:ye317005c16e99c14f840956307b637d8982af64*/