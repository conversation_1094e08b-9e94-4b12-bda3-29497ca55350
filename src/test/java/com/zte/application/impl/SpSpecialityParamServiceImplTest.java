package com.zte.application.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.BsItemInfoService;
import com.zte.application.ResourceApplicationService;
import com.zte.application.ResourceDetailService;
import com.zte.application.SpSpecialityParamItemService;
import com.zte.application.SpTemplateService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.SpecialityParamConstant;
import com.zte.domain.model.BsItemInfoRepository;
import com.zte.domain.model.DsnCUCCParams;
import com.zte.domain.model.ResourceApplicationRepository;
import com.zte.domain.model.ResourceDetail;
import com.zte.domain.model.ResourceInfoDetailRepository;
import com.zte.domain.model.ResourceInfoRepository;
import com.zte.domain.model.SpSpecialityParam;
import com.zte.domain.model.SpSpecialityParamItem;
import com.zte.domain.model.SpSpecialityParamRepository;
import com.zte.infrastructure.feign.DatawbFeignService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.BaseTestCase;
import org.apache.commons.lang3.StringUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.zte.common.utils.Constant.GenerationMethod.STBID_MANUAL;
import static com.zte.common.utils.Constant.GenerationMethod.TELMEX_GPON_SN;
import static com.zte.common.utils.Constant.MAC_START_FUNCTION;
import static com.zte.common.utils.Constant.STBID_FUNCTION;
import static com.zte.common.utils.Constant.STRING_FIVE;
import static com.zte.common.utils.Constant.USAGE_SCOPE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;

/**
 * SpSpecialityParamServiceImpl Tester.
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @since <pre>08/24/2022</pre>
 */
@PrepareForTest({HttpClientUtil.class, MESHttpHelper.class, RedisCacheUtils.class,SpSpecialityParamServiceImpl.class,SpringContextUtil.class, RedisHelper.class})
public class SpSpecialityParamServiceImplTest extends BaseTestCase {

    @InjectMocks
    private SpSpecialityParamServiceImpl service;
    @Mock
    private ResourceInfoServiceImpl resourceInfoServiceImpl;
    @Mock
    private BsItemInfoService bsItemInfoService;
    @Mock
    private SpSpecialityParamRepository spSpecialityParamRepository;
    @Mock
    private ResourceApplicationRepository resourceApplicationRepository;
    @Mock
    private ResourceInfoDetailRepository infoDetailRepository;
    @Mock
    private ResourceInfoRepository resourceInfoRepository;
    @Mock
    private BsItemInfoRepository bsItemInfoRepository;
    @Mock
    private SpSpecialityParamItemService spSpecialityParamItemService;
    @Mock
    private ResourceApplicationService resourceApplicationService;
    @Mock
    private SpTemplateService spTemplateService;
    @Mock
    private ResourceDetailService resourceDetailService;
    @Mock
    private DatawbFeignService datawbFeignService;
    @Mock
    private RedisLock redisLock;
    @Mock
    private RedisTemplate<String,String > redisTemplate;
    @Mock
    private ValueOperations<String , String> valueOperations;


    @Before
    public void before() throws Exception {
//        PowerMockito.mockStatic(RedisCacheUtils.class);
    }

    @After
    public void after() throws Exception {
    }

    @Test
    public void testQueryPage() throws Exception {
        PowerMockito.when(spSpecialityParamRepository.countPage(Mockito.any())).thenReturn(1L);
        PowerMockito.when(spSpecialityParamRepository.queryPage(Mockito.any())).thenReturn(new ArrayList<SpSpecialityParam>());
        PageRows<SpSpecialityParam> pageRows = service.queryPage(new SpSpecialityParamPageQueryDTO());
        Assert.assertTrue(pageRows.getRows().size() >= 0);
    }

    /**
     * Method: countByApplyTask(String applyTask)
     */
    @Test
    public void testCountByApplyTask() throws Exception {
        List<SpSpecialityParamItem> list = new ArrayList<>();
        PowerMockito.when(spSpecialityParamRepository.countPage(Mockito.any())).thenReturn(1L);
        Assert.assertNotNull(service.countByApplyTask("test"));
    }


    /**
     * Method: getById(String specialityParamId)
     */
    @Test
    public void testGetById() throws Exception {
        PowerMockito.when(spSpecialityParamRepository.selectById(Mockito.any())).thenReturn(new SpSpecialityParam());
        Assert.assertNotNull(service.getById("test"));
    }

    @Test
    public void testQueryList() throws Exception {
        PowerMockito.when(spSpecialityParamRepository.selectList(Mockito.any())).thenReturn(new  ArrayList<SpSpecialityParam>());
        Assert.assertNotNull(service.queryList(new SpSpecialityParamDTO()));
    }

    @Test
    public void testGetGenerateResult() throws Exception {
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        PowerMockito.when(spSpecialityParamRepository.selectById(Mockito.any())).thenReturn(new SpSpecialityParam(){{setProgress(0);}});
        Assert.assertNotNull(service.getGenerateResult("test"));
    }

    @Test(expected = MesBusinessException.class)
    public void testGetGenerateResult2() throws Exception {
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        PowerMockito.when(valueOperations.get(Mockito.any())).thenReturn("test");
        PowerMockito.when(redisTemplate.delete(Mockito.anyString())).thenReturn(true);
        service.getGenerateResult("test");
    }

    @Test(expected = MesBusinessException.class)
    public void getRedisId() throws Exception {
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);
        Whitebox.invokeMethod(service, "getRedisId", "test");
    }

    @Test(expected = MesBusinessException.class)
    public void ruleHander() throws Exception {
        JSONObject object = new JSONObject();
        object.put("Stest",null);
        Whitebox.invokeMethod(service, "ruleHandle", "", "Stest", object, Long.parseLong("1"));
    }

    @Test(expected = MesBusinessException.class)
    public void ruleHander2() throws Exception {
        JSONObject object = new JSONObject();
        object.put("Stest","test");
        Whitebox.invokeMethod(service, "ruleHandle", "", "Stest", object, Long.parseLong("1"));
    }

    @Test()
    public void getBarcode() throws Exception {
        SpSpecialityParam spSpecialityParam = new SpSpecialityParam();
        spSpecialityParam.setBarcode(1L);
        List<SpTemplateItemDTO> itemList = new ArrayList<>();
        SpTemplateItemDTO spTemplateItemDTO = new SpTemplateItemDTO();
        spTemplateItemDTO.setParamRule(SpecialityParamConstant.FUNCTION_GETWHOLEDEVICECODE + SpecialityParamConstant.PARENTHESES);
        itemList.add(spTemplateItemDTO);

        PowerMockito.doReturn(null).when(spSpecialityParamItemService).getMaxBarcode(Mockito.any());
        Whitebox.invokeMethod(service, "getBarcode", spSpecialityParam,itemList,0L);

        PowerMockito.doReturn(1L).when(spSpecialityParamItemService).getMaxBarcode(Mockito.any());
        Assert.assertNotNull(Whitebox.invokeMethod(service, "getBarcode", spSpecialityParam,itemList,0L));

        PowerMockito.when(resourceApplicationRepository.isHaveApplicationInfo(Mockito.any())).thenReturn(1);
        Whitebox.invokeMethod(service, "deleteParamById", new SpSpecialityParam());
        PowerMockito.when(resourceApplicationRepository.isHaveApplicationInfo(Mockito.any())).thenReturn(null);
        Whitebox.invokeMethod(service, "deleteParamById", new SpSpecialityParam());
    }

    /**
     * Method: generate(SpSpecialityParam spSpecialityParam)
     */
    @Test
    public void testGenerate() throws Exception {
        // 1
        SpSpecialityParam spSpecialityParam = new SpSpecialityParam();
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        service.generate(spSpecialityParam, false);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        spSpecialityParam.setApplyQty(11);
        ReflectionTestUtils.setField(service, "applyQtyMax", 10);
        try {
            service.generate(spSpecialityParam, false);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.APPLY_QTY_IS_EXCEED_MAX, e.getMessage());
        }
        spSpecialityParam.setApplyQty(5);
        PowerMockito.when(spSpecialityParamRepository.countPage(Mockito.any())).thenReturn(1L);
        try {
            service.generate(spSpecialityParam, false);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_APPLYTASK_GENERATE, e.getMessage());
        }

        PowerMockito.when(spSpecialityParamRepository.countPage(Mockito.any())).thenReturn(0L);
        PowerMockito.when(spSpecialityParamRepository.countUndoneByCreator(Mockito.any())).thenReturn(1L);
        try {
            service.generate(spSpecialityParam, false);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.EMP_NO_IS_NULL, e.getMessage());
        }
        spSpecialityParam.setCreateBy("00000000");
        try {
            service.generate(spSpecialityParam, false);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.EMP_NO_HAS_UNDONE_TASK, e.getMessage());
        }
        PowerMockito.when(bsItemInfoService.checkItemNoIsValid(Mockito.any())).thenReturn(false);
        PowerMockito.when(spSpecialityParamRepository.countUndoneByCreator(Mockito.any())).thenReturn(0L);
        try {
            service.generate(spSpecialityParam, true);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.ITEM_NO_IS_INVALID, e.getMessage());
        }
        PowerMockito.when(bsItemInfoService.checkItemNoIsValid(Mockito.any())).thenReturn(true);
        List<ResourceApplicationEntityDTO> applyingList = new ArrayList() {{
            add(new ResourceApplicationEntityDTO() {{
                setResourceId("11111");
            }});
        }};
        PowerMockito.when(resourceApplicationService.getApplyingList(Mockito.any())).thenReturn(applyingList);
        try {
            service.generate(spSpecialityParam, true);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_NOT_EXIST, e.getMessage());
        }
        List<SpTemplateDTO> templateDetails = new ArrayList() {{
            add(new SpTemplateDTO());
        }};
        PowerMockito.when(spTemplateService.getDetail(Mockito.any())).thenReturn(templateDetails);
        try {
            service.generate(spSpecialityParam, true);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_NOT_EXIST, e.getMessage());
        }

        List<SpTemplateDTO> templateDetails1 = new ArrayList() {{
            add(new SpTemplateDTO() {{
                setItemList(new ArrayList() {{
                    add(new SpTemplateItemDTO() {{
                        setParamName("TEST");
                    }});
                }});
            }});
        }};
        PowerMockito.when(spTemplateService.getDetail(Mockito.any())).thenReturn(templateDetails1);
        try {
            service.generate(spSpecialityParam, false);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_NOT_EXIST, e.getMessage());
        }
        PowerMockito.when(spSpecialityParamRepository.countPage(Mockito.any())).thenReturn(1L);
        try {
            service.generate(spSpecialityParam, false);
        } catch (MesBusinessException e) {
            Assert.assertEquals( MessageId.SPECIALITY_PARAM_APPLYTASK_GENERATE, e.getMessage());
        }
        PowerMockito.when(resourceApplicationRepository.isHaveApplicationInfo(Mockito.any())).thenReturn(null);
        PowerMockito.when(spSpecialityParamRepository.countPage(Mockito.any())).thenReturn(0L);
        service.generate(spSpecialityParam, false);
        service.generate(spSpecialityParam, true);
    }

    /**
     * Method: generateItem(SpSpecialityParam spSpecialityParam, List<SpTemplateItemDTO> itemList, List<ResourceApplicationEntityDTO> applyList)
     */
    @Test
    public void testGenerateItem() throws Exception {
        SpSpecialityParam spSpecialityParam = new SpSpecialityParam();
        spSpecialityParam.setSpecialityParamId("1");
        spSpecialityParam.setItemNum(0L);
        spSpecialityParam.setUsageScope("5");

        List<SpTemplateItemDTO> itemList = new ArrayList<>();
        SpTemplateItemDTO item = new SpTemplateItemDTO();
        item.setParamRule(SpecialityParamConstant.FUNCTION_GETWHOLEDEVICECODE + SpecialityParamConstant.PARENTHESES);
        itemList.add(item);
        SpTemplateItemDTO item1 = new SpTemplateItemDTO();
        item1.setParamType(Constant.ParamType.WHOLE_DEVICE_CODE);
        item1.setParamRule("123456");
        itemList.add(item1);
        SpTemplateItemDTO item2 = new SpTemplateItemDTO();
        item2.setParamType(Constant.ParamType.NASN);
        item2.setParamRule("123456");
        itemList.add(item2);
        SpTemplateItemDTO item3 = new SpTemplateItemDTO();
        item3.setParamType(Constant.ParamType.SCRAMBLING_CODE);
        item3.setParamRule("123456");
        itemList.add(item3);
        SpTemplateItemDTO item4 = new SpTemplateItemDTO();
        item4.setParamType(Constant.ParamType.NACC);
        item4.setParamRule("123456");
        itemList.add(item4);
        PowerMockito.when(spSpecialityParamRepository.getEigenValueIndex(Mockito.any())).thenReturn(null);

        List<ResourceApplicationEntityDTO> applyList = new ArrayList() {{
            add(new ResourceApplicationEntityDTO() {{
                setResourceType(MpConstant.MAC);
                setDistributableQty(1);
            }});
            add(new ResourceApplicationEntityDTO() {{
                setResourceType(MpConstant.MAC);
                setDistributableQty(2);
            }});
        }};

        PowerMockito.when(resourceDetailService.getResourceDetailCount(Mockito.any())).thenReturn(1L);
        PowerMockito.when(resourceDetailService.getPageList(Mockito.any())).thenReturn(new ArrayList<ResourceDetail>() {{
            add(new ResourceDetail() {{
                setAdditional("{}");
            }});
        }});

        PowerMockito.when(datawbFeignService.generateBarcode(Mockito.any())).thenReturn(new ServiceData<Long>() {{
            setBo(1L);
        }});
        PowerMockito.when(spSpecialityParamRepository.updateById(Mockito.any())).thenReturn(1L);
        PowerMockito.mockStatic(SpringContextUtil.class);

        PowerMockito.doNothing().when(spSpecialityParamItemService).addBatch(Mockito.any());
        PowerMockito.when(spSpecialityParamRepository.updateById(Mockito.any())).thenReturn(1L);
        PowerMockito.doNothing().when(resourceDetailService).updateBatchOfStatus(Mockito.any());
        PowerMockito.when(SpringContextUtil.getBean("spSpecialityParamService")).thenReturn(service);
        SpSpecialityParam params = new SpSpecialityParam();
        params.setProgress(100);
        PowerMockito.when(spSpecialityParamRepository.selectIdByTask(Mockito.any())).thenReturn(params);

        PowerMockito.when(spSpecialityParamRepository.getEigenValueIndex(Mockito.any())).thenReturn(1L);
        service.generateItem(spSpecialityParam, itemList, applyList);
        params.setProgress(0);
        PowerMockito.when(spSpecialityParamRepository.selectIdByTask(Mockito.any())).thenReturn(params);
        service.generateItem(spSpecialityParam, itemList, applyList);
        service.generateItem(spSpecialityParam, itemList, applyList);
        Assert.assertNotNull(spSpecialityParam);

        PowerMockito.when(resourceDetailService.getResourceDetailCount(Mockito.any())).thenReturn(0L);
        service.generateItem(spSpecialityParam, itemList, applyList);
        spSpecialityParam.setProgress(100);
        service.generateItem(spSpecialityParam, itemList, applyList);
        spSpecialityParam.setProgress(90);
        service.generateItem(spSpecialityParam, itemList, applyList);
    }

    @Test
    public void testGenerateItemStbid() throws Exception {
        String spSpecialityParamJson = "{\"applyQty\":2,\"applyTask\":\"25061601\",\"createBy\":\"10338918\",\"destinedArea\":\"\",\"firstEigenValue\":false,\"fixedStr\":\"\",\"itemCode\":\"012100100122\",\"itemName\":\"\",\"itemNum\":0,\"itemUnit\":\"\",\"itemVersion\":\"\",\"lastUpdatedBy\":\"10338918\",\"operator\":\"\",\"productBigClass\":\"1\",\"productSmallClass\":\"3\",\"productionUnit\":\"3\",\"progress\":0,\"separator\":\"\",\"specialityParamId\":\"20250616000000\",\"stbidCheckAA\":\"Y\",\"stbidPrefix\":\"111122322233302506\",\"step\":\"1\",\"taskId\":\"25061601\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"templateItem\":\"MacStart,STBID\",\"templateName\":\"dhome-test2\",\"usageScope\":\"2\"}";
        SpSpecialityParam spSpecialityParam = JSONObject.parseObject(spSpecialityParamJson,SpSpecialityParam.class);
        String itemListJson = "[{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"MacStart\",\"itemId\":\"372599a8-8d3c-4e4e-90c9-2ccdf8473f22\",\"orderNum\":1,\"paramName\":\"MacStart\",\"paramRule\":\"MacStart()\",\"paramType\":\"MAC\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false},{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"ad264d76-0f33-4d88-8d3a-71445a35ef87\",\"orderNum\":2,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart,1111-22#222-333-0-####,Y)\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false}]";
        List<SpTemplateItemDTO> itemList = JSONArray.parseArray(itemListJson,SpTemplateItemDTO.class);
        String applyListJson ="[{\"applyAmount\":2,\"applyBillNo\":\"1-20250616001\",\"applyId\":\"573642fb-7dc7-4cbc-a19d-ae82bd9351ee\",\"applyQty\":2,\"applyTask\":\"25061601\",\"createDate\":1750041565000,\"createUser\":\"10338918\",\"enableFlag\":\"Y\",\"isDistributed\":\"Y\",\"isExported\":\"N\",\"page\":1,\"resourceEnd\":\"00-19-C6-56-00-09\",\"resourceId\":\"05225f25-07ef-45a4-ad4b-4beaa16f6000\",\"resourceStart\":\"00-19-C6-56-00-08\",\"resourceStatus\":\"APPLYING\",\"resourceType\":\"MAC\",\"rows\":10,\"searchCount\":true,\"standardQty\":1,\"updateDate\":1750041566000,\"updateUser\":\"10338918\",\"usageScope\":\"2\",\"userType\":\"generalUser\"}]";
        List<ResourceApplicationEntityDTO> applyList = JSONArray.parseArray(applyListJson,ResourceApplicationEntityDTO.class);

        String resourceDetailsJson ="[{\"resourceSn\":\"0019C6560008\",\"resourceStart\":\"0019C6560008\",\"resourceStatus\":\"APPLYING\",\"resourceStep\":1},{\"resourceSn\":\"0019C6560009\",\"resourceStart\":\"0019C6560009\",\"resourceStatus\":\"APPLYING\",\"resourceStep\":1}]";
        List<ResourceDetail> resourceDetails = JSONArray.parseArray(resourceDetailsJson,ResourceDetail.class);
        PowerMockito.when(resourceDetailService.getPageList(Mockito.any())).thenReturn(resourceDetails);
        PowerMockito.when(resourceDetailService.getResourceDetailCount(Mockito.any())).thenReturn(1L);
        PowerMockito.mockStatic(SpringContextUtil.class);

        PowerMockito.when(SpringContextUtil.getBean("spSpecialityParamService")).thenReturn(service);
        PowerMockito.doNothing().when(spSpecialityParamItemService).addBatch(Mockito.any());
        PowerMockito.when(resourceApplicationService.update(Mockito.any())).thenReturn(1L);
        PowerMockito.when(spSpecialityParamRepository.updateById(Mockito.any())).thenReturn(1L);
        PowerMockito.doNothing().when(resourceDetailService).updateBatchOfStatus(Mockito.any());

        SpSpecialityParam params = new SpSpecialityParam();
        params.setProgress(0);
        PowerMockito.when(spSpecialityParamRepository.selectIdByTask(Mockito.any())).thenReturn(params);
        Assert.assertNotNull(params);
        spSpecialityParam.setUsageScope("5");
        service.generateItem(spSpecialityParam, itemList, applyList);

    }

    @Test
    public void getStbidPrefix() throws Exception {
        String spSpecialityParamJson = "{\"applyQty\":2,\"applyTask\":\"25061601\",\"createBy\":\"10338918\",\"destinedArea\":\"\",\"firstEigenValue\":false,\"fixedStr\":\"\",\"itemCode\":\"012100100122\",\"itemName\":\"\",\"itemNum\":0,\"itemUnit\":\"\",\"itemVersion\":\"\",\"lastUpdatedBy\":\"10338918\",\"operator\":\"\",\"productBigClass\":\"1\",\"productSmallClass\":\"3\",\"productionUnit\":\"3\",\"progress\":0,\"separator\":\"\",\"specialityParamId\":\"20250616000000\",\"stbidCheckAA\":\"Y\",\"stbidPrefix\":\"111122322233302506\",\"step\":\"1\",\"taskId\":\"25061601\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"templateItem\":\"MacStart,STBID\",\"templateName\":\"dhome-test2\",\"usageScope\":\"2\"}";
        SpSpecialityParam spSpecialityParam = JSONObject.parseObject(spSpecialityParamJson,SpSpecialityParam.class);
        String itemListJson = "[{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"MacStart\",\"itemId\":\"372599a8-8d3c-4e4e-90c9-2ccdf8473f22\",\"orderNum\":1,\"paramName\":\"MacStart\",\"paramRule\":\"MacStart()\",\"paramType\":\"MAC\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false},{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"ad264d76-0f33-4d88-8d3a-71445a35ef87\",\"orderNum\":2,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart,1111-22#222-333-0-####,Y)\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false}]";
        List<SpTemplateItemDTO> itemList = JSONArray.parseArray(itemListJson,SpTemplateItemDTO.class);
        Whitebox.invokeMethod(service, "getStbidPrefix",spSpecialityParam, itemList);
        Assert.assertNotNull(itemList);
        Whitebox.invokeMethod(service, "getStbidPrefix",spSpecialityParam, new ArrayList<>());
        String itemList1Json = "[{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"MacStart\",\"itemId\":\"372599a8-8d3c-4e4e-90c9-2ccdf8473f22\",\"orderNum\":1,\"paramName\":\"MacStart\",\"paramRule\":\"MacStart()\",\"paramType\":\"MAC\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false},{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"数字化平台获取\",\"itemId\":\"ad264d76-0f33-4d88-8d3a-71445a35ef87\",\"orderNum\":2,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart,1111-22#222-333-0-####,Y)\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false}]";
        List<SpTemplateItemDTO> itemList1 = JSONArray.parseArray(itemList1Json,SpTemplateItemDTO.class);
        try {
            Whitebox.invokeMethod(service, "getStbidPrefix",spSpecialityParam, itemList1);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.STBIDTYPE_NOAUTO, e.getMessage());
        }
        String itemList2Json = "[{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"MacStart\",\"itemId\":\"372599a8-8d3c-4e4e-90c9-2ccdf8473f22\",\"orderNum\":1,\"paramName\":\"MacStart\",\"paramRule\":\"MacStart()\",\"paramType\":\"MAC\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false},{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"ad264d76-0f33-4d88-8d3a-71445a35ef87\",\"orderNum\":2,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart,111122#222-333-0-####,Y)\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false}]";
        List<SpTemplateItemDTO> itemList2 = JSONArray.parseArray(itemList2Json,SpTemplateItemDTO.class);
        try {
            Whitebox.invokeMethod(service, "getStbidPrefix",spSpecialityParam, itemList2);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.STBIDTYPE_STBIDCFG_ERROR, e.getMessage());
        }
        String itemList3Json = "[{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"MacStart\",\"itemId\":\"372599a8-8d3c-4e4e-90c9-2ccdf8473f22\",\"orderNum\":1,\"paramName\":\"MacStart\",\"paramRule\":\"MacStart()\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false},{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"ad264d76-0f33-4d88-8d3a-71445a35ef87\",\"orderNum\":2,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart,1111-22#222-333-0-####,Y)\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false}]";
        List<SpTemplateItemDTO> itemList3 = JSONArray.parseArray(itemList3Json,SpTemplateItemDTO.class);
        try {
            Whitebox.invokeMethod(service, "getStbidPrefix",spSpecialityParam, itemList3);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.STBIDTYPE_NUM_OVERONE, e.getMessage());
        }
        String itemList4Json = "[{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"MacStart\",\"itemId\":\"372599a8-8d3c-4e4e-90c9-2ccdf8473f22\",\"orderNum\":1,\"paramName\":\"MacStart\",\"paramRule\":\"MacStart()\",\"paramType\":\"Mac\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false},{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"ad264d76-0f33-4d88-8d3a-71445a35ef87\",\"orderNum\":2,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart1111-22#222-333-0-####,Y)\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false}]";
        List<SpTemplateItemDTO> itemList4 = JSONArray.parseArray(itemList4Json,SpTemplateItemDTO.class);
        try {
            Whitebox.invokeMethod(service, "getStbidPrefix",spSpecialityParam, itemList4);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.STBIDTYPE_STBIDTEMPLATE_ERROR, e.getMessage());
        }
        String itemList5Json = "[{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"MacStart\",\"itemId\":\"372599a8-8d3c-4e4e-90c9-2ccdf8473f22\",\"orderNum\":1,\"paramName\":\"MacStart\",\"paramRule\":\"MacStart()\",\"paramType\":\"Mac\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false},{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"ad264d76-0f33-4d88-8d3a-71445a35ef87\",\"orderNum\":2,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart,1111-2#222-333-0-####,Y)\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false}]";
        List<SpTemplateItemDTO> itemList5 = JSONArray.parseArray(itemList5Json,SpTemplateItemDTO.class);
        try {
            Whitebox.invokeMethod(service, "getStbidPrefix",spSpecialityParam, itemList5);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.STBIDTYPE_STBIDCFG_ERROR, e.getMessage());
        }
        String itemList6Json = "[{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"MacStart\",\"itemId\":\"372599a8-8d3c-4e4e-90c9-2ccdf8473f22\",\"orderNum\":1,\"paramName\":\"MacStart\",\"paramRule\":\"MacStart()\",\"paramType\":\"MAC\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false},{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"ad264d76-0f33-4d88-8d3a-71445a35ef87\",\"orderNum\":2,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart,1111-222222-333-0-1111,Y)\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false}]";
        List<SpTemplateItemDTO> itemList6 = JSONArray.parseArray(itemList6Json,SpTemplateItemDTO.class);
        Whitebox.invokeMethod(service, "getStbidPrefix",spSpecialityParam, itemList6);
    }

    @Test
    public void setStbidParam() throws Exception {
        String itemJson = "{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"ad264d76-0f33-4d88-8d3a-71445a35ef87\",\"orderNum\":2,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart,1111-22#222-333-0-####,Y)\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false}";
        SpTemplateItemDTO itemDTO = JSONObject.parseObject(itemJson,SpTemplateItemDTO.class);
        JSONObject itemData = new JSONObject();
        itemData.put("MacStart", "AAAAAAAAAAAA");
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put(Constant.SPPARAM_KEY_STBID_PRRFIX,"111122222233345555");
        paramsMap.put(Constant.SPPARAM_KEY_STBID_CHECKAA,"N");
        Whitebox.invokeMethod(service, "setStbidParam", itemDTO, itemData, paramsMap);

        String item1Json = "{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"ad264d76-0f33-4d88-8d3a-71445a35ef87\",\"orderNum\":2,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart1111-22#222-333-0-####,Y)\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false}";
        SpTemplateItemDTO itemDTO1 = JSONObject.parseObject(item1Json,SpTemplateItemDTO.class);
        try {
            Whitebox.invokeMethod(service, "setStbidParam", itemDTO1, itemData, paramsMap);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getMessage().contains("STBID参数模板配置错误"));
        }
        String item2Json = "{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"ad264d76-0f33-4d88-8d3a-71445a35ef87\",\"orderNum\":2,\"paramName\":\"STBID\",\"paramRule\":\"STBID(macStart,1111-22#222-333-0-####,Y)\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false}";
        SpTemplateItemDTO itemDTO2 = JSONObject.parseObject(item2Json,SpTemplateItemDTO.class);
        try {
            Whitebox.invokeMethod(service, "setStbidParam", itemDTO2, itemData, paramsMap);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getMessage().contains("variable.does.not.exist"));
        }
    }

    public void testGenerateItem2() throws Exception {
        SpSpecialityParam spSpecialityParam = new SpSpecialityParam();
        spSpecialityParam.setSpecialityParamId("1");
        spSpecialityParam.setItemNum(0L);

        List<SpTemplateItemDTO> itemList = new ArrayList<>();
        SpTemplateItemDTO item = new SpTemplateItemDTO();
        item.setParamRule(SpecialityParamConstant.FUNCTION_GETWHOLEDEVICECODE + SpecialityParamConstant.PARENTHESES);
        itemList.add(item);

        List<ResourceApplicationEntityDTO> applyList = new ArrayList() {{
            add(new ResourceApplicationEntityDTO() {{
                setResourceType(MpConstant.MAC);
                setDistributableQty(1);
            }});
            add(new ResourceApplicationEntityDTO() {{
                setResourceType(MpConstant.MAC);
                setDistributableQty(2);
            }});
        }};

        PowerMockito.when(resourceDetailService.getResourceDetailCount(Mockito.any())).thenReturn(1L);
        PowerMockito.when(resourceDetailService.getPageList(Mockito.any())).thenReturn(new ArrayList<ResourceDetail>() {{
            add(new ResourceDetail() {{
                setAdditional("{}");
            }});
        }});

        PowerMockito.when(datawbFeignService.generateBarcode(Mockito.any())).thenReturn(new ServiceData<Long>() {{
            setCode(new RetCode("0002","dd"));
            setBo(1L);
        }});
        PowerMockito.when(spSpecialityParamRepository.updateById(Mockito.any())).thenReturn(1L);
        PowerMockito.mockStatic(SpringContextUtil.class);

        PowerMockito.doNothing().when(spSpecialityParamItemService).addBatch(Mockito.any());
        PowerMockito.when(spSpecialityParamRepository.updateById(Mockito.any())).thenReturn(1L);
        PowerMockito.doNothing().when(resourceDetailService).updateBatchOfStatus(Mockito.any());
        SpSpecialityParam params = new SpSpecialityParam();
        params.setProgress(100);
        PowerMockito.when(spSpecialityParamRepository.selectIdByTask(Mockito.any())).thenReturn(params);
        Assert.assertNotNull(params);
        service.generateItem(spSpecialityParam, itemList, applyList);

    }

    @Test
    public void paramsGenerate() throws Exception {
        SpSpecialityParam spSpecialityParam = new SpSpecialityParam();
        spSpecialityParam.setApplyQty(10L);
        List<ResourceApplicationEntityDTO> applyList = new ArrayList() {{
            add(new ResourceApplicationEntityDTO() {{
                setResourceType(MpConstant.MAC);
            }});
        }};
        PowerMockito.when(resourceApplicationService.getList(Mockito.any())).thenReturn(null);
        try {
            service.paramsGenerate(spSpecialityParam, null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPECIALITY_RESOURCE_ABNORMALITY, e.getMessage());
        }

        PowerMockito.when(resourceApplicationService.getList(Mockito.any())).thenReturn(applyList);
        List<SpTemplateDTO> templateDetails = new ArrayList() {{
            add(new SpTemplateDTO() {{
                setItemList(new ArrayList() {{
                    add(new SpTemplateItemDTO() {{
                        setParamRule("");
                    }});
                }});
            }});
        }};
        SpSpecialityParam params = new SpSpecialityParam();
        params.setProgress(100);
        PowerMockito.when(spSpecialityParamRepository.selectIdByTask(Mockito.any())).thenReturn(params);
        service.paramsGenerate(spSpecialityParam, templateDetails);
    }

    @Test
    public void generateApplicationInfo() throws Exception {
        SpSpecialityParam spSpecialityParam = new SpSpecialityParam();
        List<SpTemplateItemDTO> itemList = new ArrayList() {{
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.MAC);
                setGenerationMethod(Constant.GenerationMethod.MAC_START);
                setParamRule("MacStart(-,3)");
            }});add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.MAC);
                setGenerationMethod(Constant.GenerationMethod.MAC_START);
                setParamRule("MacStart(-)");
            }});
            add(new SpTemplateItemDTO() {{
                setGenerationMethod(Constant.GenerationMethod.MAC_END);
                setParamRule("MacEnd(16)");
            }});
            add(new SpTemplateItemDTO() {{
                setGenerationMethod(Constant.GenerationMethod.GPON_SN);
                setParamRule("GponSn(TEST)");
            }});
        }};

        List<ResourceForApplicationDTO> list = new ArrayList() {{
            add(new ResourceForApplicationDTO() {{
                setResourceType(Constant.ResourceType.GPON_SN);
                setResourceQty(1L);
            }});
        }};

        // PowerMockito.when(resourceApplicationService.validApplyQty(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(list);
        service.generateApplicationInfo(spSpecialityParam, itemList);
        Assert.assertNotNull(spSpecialityParam);
        itemList.add(new SpTemplateItemDTO() {{
            setGenerationMethod(Constant.GenerationMethod.MAC_END);
            setParamRule("MacEnd()");
        }});
        try {
            service.generateApplicationInfo(spSpecialityParam, itemList);
            Assert.assertNotNull(spSpecialityParam);
        } catch (Exception e) {
            Assert.assertNotNull(e.getMessage());
        }
        itemList.add(new SpTemplateItemDTO() {{
            setGenerationMethod(Constant.GenerationMethod.MAC_END);
            setParamRule("MacEnd(0)");
        }});
        try {
            service.generateApplicationInfo(spSpecialityParam, itemList);
            Assert.assertNotNull(spSpecialityParam);
        } catch (Exception e) {
            Assert.assertNotNull(e.getMessage());
        }
    }

    @Test
    public void setApplicationInfo() throws Exception {
        ResourceForApplicationDTO dto = new ResourceForApplicationDTO();
        dto.setResourceType(Constant.ResourceType.MAC);
        dto.setResourceQty(1L);
        SpSpecialityParam specialityParam = new SpSpecialityParam();
        specialityParam.setStep("1");
        Whitebox.invokeMethod(service, "setApplicationInfo", specialityParam, dto, new ArrayList<>());
        dto.setResourceType(Constant.ResourceType.GPON_SN);
        Whitebox.invokeMethod(service, "setApplicationInfo", specialityParam, dto, new ArrayList<>());
        Assert.assertNotNull(dto);
    }

    @Test
    public void generateSpItem() throws Exception {
        List<SpTemplateItemDTO> itemList = new ArrayList() {{
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.MAC);
                setParamName("MacStart");
                setParamRule("MacStart(-,3)");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
                setParamName("test2");
                setGenerationMethod(Constant.GenerationMethod.DSN_CTCC_FTTO);
                setParamRule("ZTEFQFT2");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType("TEST");
                setParamName("12");
                setParamRule("FIXEDSTR('得到','1',1,5)");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.RANDOM_TYPE);
                setParamName("test1234");
                setGenerationMethod(Constant.GenerationMethod.RANDOM_PREFIX);
                setParamRule("Random(前缀,G,%,10,0,后缀)");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.MAC);
                setParamName("test1234");
                setGenerationMethod(Constant.GenerationMethod.MAC_ADD);
                setParamRule("MacStart,2");
            }});
        }};
        List<ResourceApplicationEntityDTO> applyList = new ArrayList() {{
            add(new ResourceApplicationEntityDTO() {{
                setResourceType(Constant.ResourceType.MAC);
            }});
        }};
        SpSpecialityParam specialityParam = new SpSpecialityParam();
        specialityParam.setProductionUnit("K");
        specialityParam.setWdcEigenValue("123456");
        specialityParam.setWdcTotal(0L);
        HashMap<String, List<ResourceDetail>> resources = new HashMap<>();
        resources.put(Constant.ResourceType.MAC, new ArrayList() {{
            add(new ResourceDetail() {{
                setResourceStart("00-19-C6-51-08-34");
            }});
        }});
        Assert.assertNotNull(resources);
        List<Long> numList = new ArrayList() {{
            add(1L);
            add(2L);
        }};
        PowerMockito.when(spSpecialityParamRepository.getTelmexEigenValueIndex(Mockito.any())).thenReturn(null);
        specialityParam.setUsageScope("5");
        Whitebox.invokeMethod(service, "generateSpItem",specialityParam , itemList, applyList, resources, numList);
        itemList.add(new SpTemplateItemDTO() {{
            setParamType(Constant.ParamType.MAC);
            setGenerationMethod(Constant.GenerationMethod.MAC_START);
            setParamName("MacStart");
            setParamRule("MacStart(-,3)");
        }});
        Whitebox.invokeMethod(service, "generateSpItem",specialityParam , itemList, applyList, resources, numList);
        PowerMockito.when(spSpecialityParamRepository.getTelmexEigenValueIndex(Mockito.any())).thenReturn(100L);
        Whitebox.invokeMethod(service, "generateSpItem",specialityParam , itemList, applyList, resources, numList);
        specialityParam.setNasnList(new ArrayList() {{
            add("11");
        }});
        Whitebox.invokeMethod(service, "generateSpItem",specialityParam , itemList, applyList, resources, numList);
        specialityParam.setNaccParamName("nacc");
        specialityParam.setScrambleCodeParamName("sc");
        Whitebox.invokeMethod(service, "generateSpItem",specialityParam , itemList, applyList, resources, numList);
        itemList.add(new SpTemplateItemDTO() {{
            setParamType(Constant.ParamType.CUSTOMIZE);
            setParamName("cu");
            setParamRule("SUBSTR([MacStart],1,6)");
        }});
        itemList.add(new SpTemplateItemDTO() {{
            setParamType(Constant.ParamType.GPON_SN);
            setParamName("test123");
            setGenerationMethod(Constant.GenerationMethod.TELMEX_GPON_SN);
            setParamRule("usetr123");
        }});
        try {
            Whitebox.invokeMethod(service, "generateSpItem",specialityParam , itemList, applyList, resources, numList);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void setMapInfo() throws Exception {
        SpSpecialityParam specialityParam = new SpSpecialityParam();
        specialityParam.setProductionUnit("K");
        List<SpTemplateItemDTO> itemList = new ArrayList() {{
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.MAC);
                setGenerationMethod(Constant.GenerationMethod.MAC_START);
                setParamRule("-");
                setParamName("MacStart");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.MAC);
                setGenerationMethod(Constant.GenerationMethod.CUSTOMIZED_MAC_START);
                setParamRule("-,3");
                setParamName("MacStart");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.MAC);
                setGenerationMethod(Constant.GenerationMethod.MAC_END);
                setParamName("MacEnd");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.GPON_SN);
                setParamName("GPON-SN");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.WHOLE_DEVICE_CODE);
                setParamRule("ZTEFQX2");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType("TEST");
                setParamRule("PASS");
            }});
        }};
        Assert.assertNotNull(itemList);
        Whitebox.invokeMethod(service, "setMapInfo",specialityParam , itemList, new HashMap<>(), new HashMap<>());
    }

    @Test
    public void setParamsMapByDSN() throws Exception {
        SpSpecialityParam specialityParam = new SpSpecialityParam();
        specialityParam.setProductionUnit("K");
        SpTemplateItemDTO itemDTO = new SpTemplateItemDTO();
        itemDTO.setParamRule("ZTEFQFT2");
        itemDTO.setGenerationMethod(Constant.GenerationMethod.DSN_CTCC_FTTO);
        Assert.assertNotNull(specialityParam);
        Whitebox.invokeMethod(service, "setParamsMapByDSN", new HashMap<>(), itemDTO);
        itemDTO.setGenerationMethod(Constant.GenerationMethod.DSN_CMCC);
        Whitebox.invokeMethod(service, "setParamsMapByDSN", new HashMap<>(), itemDTO);
        itemDTO.setParamRule("8,00");
        itemDTO.setGenerationMethod(Constant.GenerationMethod.DSN_CTCC);
        Whitebox.invokeMethod(service, "setParamsMapByDSN", new HashMap<>(), itemDTO);
        itemDTO.setParamRule("FIX,D DD,EE,1");
        itemDTO.setGenerationMethod(Constant.GenerationMethod.DSN_CUCC);
        Whitebox.invokeMethod(service, "setParamsMapByDSN", new HashMap<>(), itemDTO);
        itemDTO.setParamRule("D DD,EE,1");
        itemDTO.setGenerationMethod(Constant.GenerationMethod.DSN_CUCC);
        Whitebox.invokeMethod(service, "setParamsMapByDSN", new HashMap<>(), itemDTO);
    }

    @Test
    public void preItemListGenData() throws Exception {
        HashMap<String, List<ResourceDetail>> resources = new HashMap<>();
        resources.put(Constant.ResourceType.MAC, new ArrayList() {{
            add(new ResourceDetail() {{
                setResourceStart("test");
            }});
        }});
        resources.put(Constant.ResourceType.GPON_SN, new ArrayList() {{
            add(new ResourceDetail() {{
                setResourceStart("test");
            }});
        }});
        resources.put(Constant.ResourceType.CMEI, new ArrayList() {{
            add(new ResourceDetail() {{
                setResourceStart("test");
            }});
        }});
        List<SpTemplateItemDTO> preItemList = new ArrayList() {{
            add(new SpTemplateItemDTO() {{
                setParamRule(SpecialityParamConstant.RESOURCE_POOL_MACSTART);
            }});
            add(new SpTemplateItemDTO() {{
                setParamRule(SpecialityParamConstant.RESOURCE_POOL_MACEND);
            }});
            add(new SpTemplateItemDTO() {{
                setParamRule(SpecialityParamConstant.RESOURCE_POOL_GPONSN);
            }});
            add(new SpTemplateItemDTO() {{
                setParamRule(SpecialityParamConstant.RESOURCE_POOL_WHOLEDEVICECODE);
            }});
            add(new SpTemplateItemDTO() {{
                setParamRule(SpecialityParamConstant.RESOURCE_POOL_DEVICETYPE);
            }});
            add(new SpTemplateItemDTO() {{
                setParamRule(SpecialityParamConstant.RESOURCE_POOL_CMEI);
            }});
            add(new SpTemplateItemDTO() {{
                setParamRule(SpecialityParamConstant.RESOURCE_POOL_DEVICE_SERIAL_NUMBER);
            }});
            add(new SpTemplateItemDTO() {{
                setParamRule(SpecialityParamConstant.RESOURCE_POOL_GPONSN);
                setGenerationMethod(TELMEX_GPON_SN);
            }});
            add(new SpTemplateItemDTO() {{
                setParamRule(SpecialityParamConstant.RESOURCE_POOL_DB);
                setParamRule("DB(CMEI,0001)");
            }});
        }};
        Assert.assertNotNull(resources);
        Map<String, Long> map= new LinkedHashMap<>();
        map.put("A",65536L);
        map.put("B",65535L);
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("num", "0");
        paramsMap.put(USAGE_SCOPE, "5");
        Whitebox.invokeMethod(service, "preItemListGenData",resources , preItemList, new JSONObject(),
                paramsMap, map);
    }

    @Test
    public void setCUCCData() throws Exception {
        Map<String, String> paramsMap = new HashMap<>();
        Whitebox.invokeMethod(service, "setCUCCData",new ArrayList<>(), paramsMap);
        paramsMap.put("haveCUCCParams", "false");
        Whitebox.invokeMethod(service, "setCUCCData",new ArrayList<>(), paramsMap);
        paramsMap.put("haveCUCCParams", "true");
        List<SpSpecialityParamItem> spSpecialityParamItems = new ArrayList() {{
            add(new SpSpecialityParamItem() {{
                setItemData("{\"CTCC\":\"E86FC2-01FFFFFFFFFZTEGC2897197\"}");
            }});
            add(new SpSpecialityParamItem() {{
                setItemData("{\"CUCC\":\"CUCC:E86FC2-01FFFFFFFFFZTEGC2897197\"}");
            }});
        }};
        Assert.assertNotNull(spSpecialityParamItems);
        Whitebox.invokeMethod(service, "setCUCCData",spSpecialityParamItems, paramsMap);
    }

    @Test
    public void setDSNData() throws Exception {
        SpTemplateItemDTO itemDTO = new SpTemplateItemDTO();
        itemDTO.setGenerationMethod(Constant.GenerationMethod.DSN_CTCC_FTTO);
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("dsnCMCC","");
        paramsMap.put("dsnFTTO","");
        Map<String, String> paramTypeToNameMap = new HashMap<>();
        paramTypeToNameMap.put(Constant.ParamType.MAC, Constant.ParamType.MAC);
        paramTypeToNameMap.put(Constant.ParamType.GPON_SN,Constant.ParamType.GPON_SN);
        JSONObject itemData = new JSONObject();
        itemData.put("MacStart", "00-00-00-00-00-01");
        Assert.assertNotNull(paramTypeToNameMap);
        Whitebox.invokeMethod(service, "setDSNData",itemDTO, itemData, paramsMap, paramTypeToNameMap);
        paramTypeToNameMap.put(Constant.GenerationMethod.MAC_START, Constant.GenerationMethod.MAC_START);
        Whitebox.invokeMethod(service, "setDSNData",itemDTO, itemData, paramsMap, paramTypeToNameMap);
        itemDTO.setGenerationMethod(Constant.GenerationMethod.DSN_CMCC);
        Whitebox.invokeMethod(service, "setDSNData",itemDTO, itemData, paramsMap, paramTypeToNameMap);
        itemData.put(Constant.ParamType.MAC,"00-19-C6-51-08-34");
        itemData.put(Constant.ParamType.GPON_SN,"ZTEGC0300101");
        Whitebox.invokeMethod(service, "setDSNData",itemDTO, itemData, paramsMap, paramTypeToNameMap);
        paramsMap.put("dsnCMCC","TEST");
        Whitebox.invokeMethod(service, "setDSNData",itemDTO, itemData, paramsMap, paramTypeToNameMap);
        itemDTO.setGenerationMethod(Constant.GenerationMethod.DSN_CTCC);
        paramsMap.put("dsnCTCCProductType","TEST");
        paramsMap.put("dsnCTCCProvinceCode","TEST");
        itemData.clear();
        Whitebox.invokeMethod(service, "setDSNData",itemDTO, itemData, paramsMap, paramTypeToNameMap);
        itemData.put(Constant.ParamType.MAC,"00-19-C6-51-08-34");
        itemData.put(Constant.ParamType.GPON_SN,"ZTEGC0300101");
        Whitebox.invokeMethod(service, "setDSNData",itemDTO, itemData, paramsMap, paramTypeToNameMap);
        itemDTO.setGenerationMethod(Constant.GenerationMethod.DSN_CUCC);
        itemData.clear();
        itemData.put(Constant.ParamType.MAC,"00-19-C6-51-08-34");
        itemData.put(Constant.ParamType.GPON_SN,"ZTEGC0300101");
        paramsMap.put("dsnCUCCFixedValue","0");
        Whitebox.invokeMethod(service, "setDSNData",itemDTO, itemData, paramsMap, paramTypeToNameMap);
        paramsMap.put("dsnCUCCSn","TEST");
        paramsMap.put("dsnCUCCVariable","1");
        Whitebox.invokeMethod(service, "setDSNData",itemDTO, itemData, paramsMap, paramTypeToNameMap);
        Whitebox.invokeMethod(service, "setDSNData",itemDTO, itemData, paramsMap, paramTypeToNameMap);
        itemData.clear();
        Whitebox.invokeMethod(service, "setDSNData",itemDTO, itemData, paramsMap, paramTypeToNameMap);
        itemDTO.setGenerationMethod(Constant.GenerationMethod.NASN);
        Whitebox.invokeMethod(service, "setDSNData",itemDTO, itemData, paramsMap, paramTypeToNameMap);
    }

    @Test
    public void getEigenValue() throws Exception {
        List<SpTemplateItemDTO> itemList = new ArrayList() {{
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.WHOLE_DEVICE_CODE);
                setParamRule("");
            }});
        }};
        Whitebox.invokeMethod(service, "getEigenValue",new SpSpecialityParam(), itemList, new SpSpecialityParam());
        PowerMockito.when(spSpecialityParamRepository.getEigenValueIndex(Mockito.any())).thenReturn(null);
        itemList.add(new SpTemplateItemDTO() {{
            setParamType(Constant.ParamType.WHOLE_DEVICE_CODE);
            setParamRule("123456");
        }});
        Assert.assertNotNull(itemList);
        Whitebox.invokeMethod(service, "getEigenValue",new SpSpecialityParam(), itemList, new SpSpecialityParam());
        PowerMockito.when(spSpecialityParamRepository.getEigenValueIndex(Mockito.any())).thenReturn(1L);
        Whitebox.invokeMethod(service, "getEigenValue",new SpSpecialityParam(), itemList, new SpSpecialityParam());
    }

    @Test
    public void saveGenerateData() throws Exception {
        SpSpecialityParam updateParam = new SpSpecialityParam();
        service.saveGenerateData(new ArrayList<>(), updateParam, new ArrayList<>(), new HashMap<>());
        updateParam.setWdcEigenValue("123456");
        service.saveGenerateData(new ArrayList<>(), updateParam, new ArrayList<>(), new HashMap<>());
        updateParam.setFirstEigenValue(true);
        Assert.assertNotNull(updateParam);
        service.saveGenerateData(new ArrayList<>(), updateParam, new ArrayList<>(), new HashMap<>());

        HashMap<String, List<ResourceDetail>> resources = new HashMap() {{
            put(Constant.ResourceType.NETWORK_ACCESS, new ArrayList() {{
                add(new ResourceDetail().setResourceStart("100001"));
            }});
            put(Constant.ResourceType.MAC, new ArrayList() {{
                add(new ResourceDetail().setResourceStart("100002"));
            }});
        }};
        List<ResourceApplicationEntityDTO> applyList = new ArrayList() {{
            add(new ResourceApplicationEntityDTO() {{
                setResourceType(Constant.ResourceType.NETWORK_ACCESS);
            }});
            add(new ResourceApplicationEntityDTO() {{
                setResourceType(Constant.ResourceType.MAC);
            }});
        }};
        updateParam.setProgress(0);
        service.saveGenerateData(new ArrayList<>(), updateParam, applyList, resources);
    }

    @Test
    public void checkWholeDeviceCodeEigenValue() throws Exception {
        SpSpecialityParam spSpecialityParam = new SpSpecialityParam();
        spSpecialityParam.setApplyQty(NumConstant.LONG_1000000000);
        List<SpTemplateItemDTO> itemList = new ArrayList() {{
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.MAC);
                setParamRule("123321");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.WHOLE_DEVICE_CODE);
                setParamRule("123321");
            }});
        }};
        PowerMockito.when(spSpecialityParamRepository.getEigenValueIndex(Mockito.any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(service, "checkWholeDeviceCodeEigenValue",spSpecialityParam, itemList);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(),MessageId.EIGEN_VALUE_EXCEED_MAX);
        }

        PowerMockito.when(spSpecialityParamRepository.getEigenValueIndex(Mockito.any())).thenReturn(1L);
        try {
            Whitebox.invokeMethod(service, "checkWholeDeviceCodeEigenValue",spSpecialityParam, itemList);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(),MessageId.EIGEN_VALUE_EXCEED_MAX);
        }
        spSpecialityParam.setApplyQty(10L);
        Whitebox.invokeMethod(service, "checkWholeDeviceCodeEigenValue",spSpecialityParam, itemList);
        List<SpTemplateItemDTO> itemList1 = new ArrayList() {{
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.MAC);
                setParamRule("123321");
            }});
        }};
        Whitebox.invokeMethod(service, "checkWholeDeviceCodeEigenValue",spSpecialityParam, itemList1);
    }

    @Test
    public void setOUIData() throws Exception {
        SpTemplateItemDTO itemDTO = new SpTemplateItemDTO();
        itemDTO.setParamRule("OUI(pre-,MACStart,1/6,-WIFI)");
        JSONObject itemData = new JSONObject();
        Whitebox.invokeMethod(service, "setOUIData", itemDTO, itemData);

        itemData.put("MACStart", null);
        Whitebox.invokeMethod(service, "setOUIData", itemDTO, itemData);

        itemData.put("MACStart", "96AB56A9");
        Assert.assertNotNull(itemDTO);
        Whitebox.invokeMethod(service, "setOUIData", itemDTO, itemData);
    }

    @Test
    public void setNetWorkAccessInfo() throws Exception {
        HashMap<String, List<ResourceDetail>> resources = new HashMap(){{
            put(MpConstant.MAC, new ArrayList<ResourceDetail>(){{
                add(new ResourceDetail(){{
                    setScramblingCode("1");
                    setModelNumAndCode("1");
                    setResourceStart("1");
                }});
            }});
        }};
        Assert.assertNotNull(resources);
        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put(USAGE_SCOPE, "5");
        Whitebox.invokeMethod(service, "setNetWorkAccessInfo", resources, new JSONObject(), paramsMap, new SpTemplateItemDTO(), 0);

        paramsMap.put(USAGE_SCOPE, "4");
        SpTemplateItemDTO spTemplateItemDTO = new SpTemplateItemDTO();
        spTemplateItemDTO.setParamType(Constant.ParamType.NASN);
        Whitebox.invokeMethod(service, "setNetWorkAccessInfo", resources, new JSONObject(), paramsMap, spTemplateItemDTO, 0);

        resources.put(MpConstant.NAL, new ArrayList<ResourceDetail>(){{
            add(new ResourceDetail(){{
                setScramblingCode("1");
                setModelNumAndCode("1");
                setResourceStart("1");
            }});
        }});
        Whitebox.invokeMethod(service, "setNetWorkAccessInfo", resources, new JSONObject(), paramsMap, spTemplateItemDTO, 0);


        spTemplateItemDTO.setParamType(Constant.ParamType.SCRAMBLING_CODE);
        Whitebox.invokeMethod(service, "setNetWorkAccessInfo", resources, new JSONObject(), paramsMap, spTemplateItemDTO, 0);

        spTemplateItemDTO.setParamType(Constant.ParamType.NACC);
        Whitebox.invokeMethod(service, "setNetWorkAccessInfo", resources, new JSONObject(), paramsMap, spTemplateItemDTO, 0);
    }

    @Test
    public void getRandomType() throws Exception {
        JSONObject itemData = new JSONObject();
        SpTemplateItemDTO itemDTO = new SpTemplateItemDTO();
        itemDTO.setParamName("name");
        itemDTO.setParamType("type");
        itemDTO.setGenerationMethod("4");
        Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);

        itemDTO.setParamType("随机");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        itemDTO.setParamRule("Random()");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        itemDTO.setParamRule("Random(前缀,,,,,后缀)");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        // 纯随机
        itemDTO.setParamRule("Random(前缀,,,,0,后缀)");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        itemDTO.setParamRule("Random(前缀,1aA,,10,0,后缀)");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        itemDTO.setGenerationMethod(Constant.GenerationMethod.RANDOM_PREFIX);
        itemDTO.setParamRule("Random(前缀,1aA,,10,0,后缀)");
        Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        itemDTO.setGenerationMethod(Constant.GenerationMethod.RANDOM_SUFFIX);
        Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        itemDTO.setGenerationMethod(Constant.GenerationMethod.RANDOM_PREFIX_SUFFIX);
        Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        itemDTO.setGenerationMethod(Constant.GenerationMethod.RANDOM);
        Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        itemDTO.setParamRule("Random(前缀,1aA,%,10,0,后缀)");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        itemDTO.setParamRule("Random(前缀,\u200B 1aA,\u200B %,10,0,后缀)");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        itemDTO.setParamRule("Random(前缀,G,%,10,0,后缀)");
        Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);


        // 三选二
        itemDTO.setParamRule("Random(前缀,,,,1,后缀)");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        itemDTO.setParamRule("Random(前缀,12,,,1,后缀)");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        itemDTO.setParamRule("Random(前缀,,,10,1,后缀)");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        itemDTO.setParamRule("Random(前缀,1,%$,10,1,后缀)");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        itemDTO.setParamRule("Random(前缀,1a,,10,1,后缀)");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        itemDTO.setParamRule("Random(前缀,1aA,,10,1,后缀)");
        Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        itemDTO.setParamRule("Random(前缀,1a,%$,10,1,后缀)");
        Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        itemDTO.setParamRule("Random(前缀,\u200B 1a,\u200B %$,10,1,后缀)");
        Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);

        // 四选三
        itemDTO.setParamRule("Random(前缀,,,,2,后缀)");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        itemDTO.setParamRule("Random(前缀,12,,,2,后缀)");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        itemDTO.setParamRule("Random(前缀,12abAB,%#,,2,后缀)");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        itemDTO.setParamRule("Random(前缀,1aA,%#,10,2,后缀)");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        itemDTO.setParamRule("Random(前缀,\u200B 1aA,\u200B %#,10,2,后缀)");
        try {
            Whitebox.invokeMethod(service, "getRandomType",itemDTO,itemData);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        Assert.assertNotNull(itemDTO);
    }

    @Test
    public void setCTCCSnEmptlGponDeviceInfo() throws Exception {
        JSONObject itemData = new JSONObject();
        List<SpTemplateItemDTO> itemList = new ArrayList<>();
        Whitebox.invokeMethod(service, "setCTCCSnInfo", itemData, itemList);

        SpTemplateItemDTO spTemplateItemDTO = new SpTemplateItemDTO();
        spTemplateItemDTO.setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
        spTemplateItemDTO.setParamRule("DSN_CTCC_SN(111)");
        spTemplateItemDTO.setGenerationMethod(Constant.GenerationMethod.DSN_CTCC_SN_EMPTY);
        itemList.add(spTemplateItemDTO);
        try {
            Whitebox.invokeMethod(service, "setCTCCSnInfo", itemData, itemList);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }

        SpTemplateItemDTO spTemplateItemDTO1 = new SpTemplateItemDTO();
        spTemplateItemDTO1.setParamName("111");
        spTemplateItemDTO1.setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
        spTemplateItemDTO1.setParamRule("DSN_SN(111)");
        spTemplateItemDTO1.setGenerationMethod(Constant.GenerationMethod.DSN_CTCC_FTTO);
        itemList.add(spTemplateItemDTO1);

        try {
            Whitebox.invokeMethod(service, "setCTCCSnInfo", itemData, itemList);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR);
        }
        itemData.put("111", "123456-12345678901234567");
        Whitebox.invokeMethod(service, "setCTCCSnInfo", itemData, itemList);
        Assert.assertNotNull(itemList);
    }

    @Test
    public void variableItemListGenData() throws Exception {
        SpTemplateItemDTO itemDTO = new SpTemplateItemDTO();
        itemDTO.setParamType(Constant.ParamType.ASSIGNMENT);
        itemDTO.setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_TEXT);
        itemDTO.setParamRule("MacStart");
        Whitebox.invokeMethod(service, "variableItemListGenData", itemDTO, new JSONObject(), new HashMap<>());

        itemDTO.setParamType(Constant.ParamType.ASSIGNMENT);
        itemDTO.setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_VARIABLE);
        try {
            Whitebox.invokeMethod(service, "variableItemListGenData", itemDTO, new JSONObject(), new HashMap<>());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.VARIABLE_DOES_NOT_EXIST, e.getMessage());
        }

        JSONObject itemData = new JSONObject();
        itemData.put("MacStart", "001122334455");
        Whitebox.invokeMethod(service, "variableItemListGenData", itemDTO, itemData, new HashMap<>());

        itemDTO.setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_TEXT_AND_RANGE);
        itemDTO.setParamRule("TextAndRange(pre,MacStart/1/6,MacEnd/1/6,-end)");
        try {
            Whitebox.invokeMethod(service, "variableItemListGenData", itemDTO, itemData, new HashMap<>());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.VARIABLE_DOES_NOT_EXIST, e.getMessage());
        }
        itemData.put("MacEnd", "00-00-00-00-01");
        Whitebox.invokeMethod(service, "variableItemListGenData", itemDTO, itemData, new HashMap<>());

        itemDTO.setParamType(Constant.ParamType.INTERVAL_VALUE);
        itemDTO.setParamRule("IntervalValue(pre-,MacStart/1/6,,-end)");
        Whitebox.invokeMethod(service, "variableItemListGenData", itemDTO, itemData, new HashMap<>());
        itemDTO.setParamRule("IntervalValue(pre-,MacStart/1/6,0,-end)");
        Whitebox.invokeMethod(service, "variableItemListGenData", itemDTO, itemData, new HashMap<>());

        itemDTO.setParamRule("IntervalValue(pre-,MacStart/1/6,1,-end)");
        Whitebox.invokeMethod(service, "variableItemListGenData", itemDTO, itemData, new HashMap<>());

        itemDTO.setParamType(Constant.ParamType.MAC);
        itemDTO.setGenerationMethod(Constant.GenerationMethod.MAC_ADD);
        itemDTO.setParamRule("MacEnd1,4");
        try {
            Whitebox.invokeMethod(service, "variableItemListGenData", itemDTO, itemData, new HashMap<>());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.VARIABLE_DOES_NOT_EXIST, e.getMessage());
        }

        itemDTO.setParamRule("MacEnd,4");
        Whitebox.invokeMethod(service, "variableItemListGenData", itemDTO, itemData, new HashMap<>());

        Map<String, String> paramsMap = new HashMap<>();
        paramsMap.put("separator", ":");
        Whitebox.invokeMethod(service, "variableItemListGenData", itemDTO, itemData, paramsMap);
    }

    @Test
    public void setCTCCSnInfo() throws Exception {
        List<SpTemplateItemDTO> itemList = new ArrayList() {{
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.GPON_SN);
                setGenerationMethod(Constant.GenerationMethod.GPON_SN);
            }});
        }};
        Whitebox.invokeMethod(service, "setCTCCSnInfo", new JSONObject(), itemList);

        itemList.add(new SpTemplateItemDTO() {{
            setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
            setGenerationMethod(Constant.GenerationMethod.DSN_CTCC_SN_EMPTY);
            setParamRule("FTTO");
        }});
        try {
            Whitebox.invokeMethod(service, "setCTCCSnInfo", new JSONObject(), itemList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, e.getMessage());
        }

        itemList.add(new SpTemplateItemDTO() {{
            setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
            setGenerationMethod(Constant.GenerationMethod.DSN_CTCC_FTTO);
            setParamRule("test");
            setParamName("FTTO");
        }});
        itemList.add(new SpTemplateItemDTO() {{
            setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
            setGenerationMethod(Constant.GenerationMethod.DSN_CTCC);
            setParamRule("test");
            setParamName("CTCC");
        }});
        try {
            Whitebox.invokeMethod(service, "setCTCCSnInfo", new JSONObject(), itemList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, e.getMessage());
        }

        JSONObject itemData = new JSONObject();
        itemData.put("FTTO", "123456-12345678901234567");
        Whitebox.invokeMethod(service, "setCTCCSnInfo", itemData, itemList);
    }

    @Test
    public void updateTelmexEigenValueIndex() throws Exception {
        Whitebox.invokeMethod(service, "updateTelmexEigenValueIndex", new LinkedHashMap<>(), new LinkedHashMap<>(), new SpSpecialityParam());

        LinkedHashMap<String, Long> telmexEigenValMap = new LinkedHashMap<>();
        telmexEigenValMap.put("1", 1L);
        telmexEigenValMap.put("2", 2L);
        LinkedHashMap<String, Long> telmexOldEigenValMap = new LinkedHashMap<>();
        telmexOldEigenValMap.put("1", 1L);
        Assert.assertNotNull(telmexEigenValMap);
        Whitebox.invokeMethod(service, "updateTelmexEigenValueIndex", telmexEigenValMap, telmexOldEigenValMap, new SpSpecialityParam());
    }

    @Test
    public void getTelmexEigenValMap() throws Exception {
        PowerMockito.when(spSpecialityParamRepository.getTelmexEigenValueIndex(Mockito.any())).thenReturn(null);
        SpSpecialityParam specialityParam = new SpSpecialityParam();
        specialityParam.setStep("1");
        Assert.assertNotNull(specialityParam);
        Whitebox.invokeMethod(service, "getTelmexEigenValMap", new LinkedHashMap<>(), new LinkedHashMap<>(), specialityParam);
    }

    @Test
    public void setTempCUCCData() throws Exception {
        JSONObject itemData = new JSONObject();
        itemData.put(Constant.GenerationMethod.MAC_START, "00-00-00-00-00-01");
        Map<String, String> paramTypeToNameMap = new HashMap<>();
        paramTypeToNameMap.put(Constant.GenerationMethod.MAC_START, Constant.GenerationMethod.MAC_START);
        DsnCUCCParams dsnCUCCParams = new DsnCUCCParams();
        dsnCUCCParams.setDsnCUCCVariable("MacEnd");
        Whitebox.invokeMethod(service, "setTempCUCCData", new SpTemplateItemDTO(), itemData, paramTypeToNameMap, dsnCUCCParams);

        dsnCUCCParams.setDsnCUCCVariable("MacStart");
        Assert.assertNotNull(dsnCUCCParams);
        Whitebox.invokeMethod(service, "setTempCUCCData", new SpTemplateItemDTO(), itemData, paramTypeToNameMap, dsnCUCCParams);
    }

    @Test
    public void isContinueGen() throws Exception {
        PowerMockito.when(resourceApplicationService.getApplyingList(Mockito.any())).thenReturn(null);
        SpSpecialityParam specialityParam = new SpSpecialityParam();
        specialityParam.setMCode("123");
        Assert.assertNotNull(specialityParam);
        Whitebox.invokeMethod(service, "isContinueGen", specialityParam, true, new ArrayList<>());
    }

    @Test
    public void saveApplicationInfo() throws Exception {
        List<ResourceApplicationEntityDTO> applicationList = new ArrayList() {{
            add(new ResourceApplicationEntityDTO() {{
                setMCode("123");
            }});
        }};
        Assert.assertNotNull(applicationList);
        Whitebox.invokeMethod(service, "saveApplicationInfo", applicationList);
    }

    @Test
    public void handleByMac() throws Exception {
        JSONObject itemData = new JSONObject();
        itemData.put("mac", "00-00-00-00-00-01");
        String[] splits = new String[]{"mac"};
        Assert.assertNotNull(itemData);
        Whitebox.invokeMethod(service, "handleByMac", itemData, splits, new SpTemplateItemDTO());
        itemData.put("mac", "00:00:00:00:00:01");
        Whitebox.invokeMethod(service, "handleByMac", itemData, splits, new SpTemplateItemDTO());
    }

    @Test
    public void setAssignmentParams() throws Exception {
        SpTemplateItemDTO itemDTO = new SpTemplateItemDTO() {{
            setGenerationMethod(Constant.GenerationMethod.DSN_CUCC);
        }};
        Assert.assertNotNull(itemDTO);
        Whitebox.invokeMethod(service, "setAssignmentParams", itemDTO, new JSONObject());

        itemDTO.setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_TEXT_AND_RANGE);
        itemDTO.setParamRule("test,,,end");
        Whitebox.invokeMethod(service, "setAssignmentParams", itemDTO, new JSONObject());
    }

    @Test
    public void generateTaskNo() throws Exception {
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);
        try {
            service.generateTaskNo();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.GENERATE_TASK_NO_REDIS_KEY_ERROR, e.getMessage());
        }
        PowerMockito.when(redisLock.lock()).thenReturn(true);
        service.generateTaskNo();
    }
}
