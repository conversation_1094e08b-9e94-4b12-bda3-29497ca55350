package com.zte.application.impl;

import com.zte.application.CommonTransactionalService;
import com.zte.application.IMESLogService;
import com.zte.domain.model.BBomDetail;
import com.zte.domain.model.BBomDetailRepository;
import com.zte.domain.model.BBomHeader;
import com.zte.domain.model.BBomHeaderRepository;
import com.zte.domain.model.BPcbLocationDetailRepository;
import com.zte.domain.model.BProdBomDetailRepository;
import com.zte.domain.model.BProdBomHeaderRepository;
import com.zte.domain.model.BsBomHierarchicalDetail;
import com.zte.domain.model.BsBomHierarchicalHead;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.BsItemInfoRepository;
import com.zte.domain.model.BsPremanuBomInfo;
import com.zte.domain.model.BsPremanuBomInfoRepository;
import com.zte.domain.model.BsPremanuItemInfo;
import com.zte.domain.model.BsPremanuItemInfoRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BPcbLocationDetailDTO;
import com.zte.interfaces.dto.BProdBomDetailDTO;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

public class BsPreItemInfoAsyncServiceImplTest extends BaseTestCase {
    @Mock
    private BsItemInfoRepository bsItemInfoRepository;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private BsBomHierarchicalServiceImpl bsBomHierarchicalService;
    @Mock
    private BsPremanuBomInfoRepository bsPremanuBomInfoRepository;
    @Mock
    private BsPremanuItemInfoRepository bsPremanuItemInfoRepository;
    @Mock
    private BPcbLocationDetailRepository bPcbLocationDetailRepository;
    @InjectMocks
    private BsPreItemInfoAsyncServiceImpl bsPreItemInfoAsyncServiceImpl;
    @Mock
    private BBomHeaderRepository bomHeaderRepository;
    @Mock
    private BBomDetailRepository bBomDetailRepository;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private CommonTransactionalService commonTransactionalService;
    @Mock
    private ValueOperations valueOperations;
    @Mock
    private IMESLogService imesLogService;
    @Mock
    private BProdBomHeaderRepository bProdBomHeaderRepository;
    @Mock
    private BProdBomDetailRepository bProdBomDetailRepository;

    @Test
    public void selectSubLevelBatch() throws Exception {
        List<BsBomHierarchicalDetail> detailList = new ArrayList<>();
        BsBomHierarchicalDetail detail = new BsBomHierarchicalDetail();
        detail.setBomCode("123");
        BsBomHierarchicalDetail detail1 = new BsBomHierarchicalDetail();
        detail1.setBomCode("123");
        detailList.add(detail);
        detailList.add(detail1);
        List<BsBomHierarchicalHead> bomHierarchicalHeadList = new ArrayList<>();
        List<BsBomHierarchicalDetail> tempDetailList = new ArrayList<>();
        BsBomHierarchicalHead bsBomHierarchicalHead = new BsBomHierarchicalHead();
        bsBomHierarchicalHead.setBomCode("123");
        BsBomHierarchicalDetail bsBomHierarchicalDetail = new BsBomHierarchicalDetail();
        bsBomHierarchicalDetail.setTypeCode("123");
        tempDetailList.add(bsBomHierarchicalDetail);
        bsBomHierarchicalHead.setDetails(tempDetailList);
        bomHierarchicalHeadList.add(bsBomHierarchicalHead);
        PowerMockito.when(bsBomHierarchicalService.selectHeadAndDetailBatch(Mockito.any())).thenReturn(bomHierarchicalHeadList);

        List<SysLookupValues> dirList = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        a1.setLookupMeaning("true");
        dirList.add(a1);
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.any())).thenReturn(dirList);

        List<BBomHeader> headerList = new LinkedList<>();
        BBomHeader b1 = new BBomHeader();
        b1.setProductCode("123");
        b1.setChiDesc("123");
        b1.setIncludePcbFpc(0);
        headerList.add(b1);
        PowerMockito.when(bomHeaderRepository.getList(Mockito.any())).thenReturn(headerList);
        PowerMockito.when(bomHeaderRepository.selectBBomHeaderByProductCodeList(Mockito.any())).thenReturn(headerList);

        List<BBomDetail> bBomDetails = new LinkedList<>();
        BBomDetail c1 = new BBomDetail();
        c1.setItemCode("124");
        c1.setUsageCount(new BigDecimal("24"));
        BBomDetail c2 = new BBomDetail();
        c2.setItemCode("123");
        c2.setUsageCount(new BigDecimal("24"));
        BBomDetail c3 = new BBomDetail();
        c3.setItemCode("040");
        c3.setUsageCount(new BigDecimal("24"));
        bBomDetails.add(c1);
        bBomDetails.add(c2);
        bBomDetails.add(c3);
        PowerMockito.when(bBomDetailRepository.selectDetailsByProductCode(Mockito.any())).thenReturn(bBomDetails);

        List<BsItemInfo> infoList = new LinkedList<>();
        BsItemInfo d1 = new BsItemInfo();
        d1.setItemNo("123");
        d1.setAbcType("A");
        d1.setIsSmt(new BigDecimal(1));
        infoList.add(d1);
        BsItemInfo d2 = new BsItemInfo();
        d2.setItemNo("124");
        d2.setAbcType("A");
        d2.setIsSmt(new BigDecimal(1));
        infoList.add(d2);
        BsItemInfo d3 = new BsItemInfo();
        d3.setItemNo("040");
        d3.setAbcType("A");
        d3.setIsSmt(new BigDecimal(1));
        infoList.add(d3);
        PowerMockito.when(bsItemInfoRepository.getInfoList(Mockito.any())).thenReturn(infoList);

        List<BsPremanuBomInfo> preBomDetails = new LinkedList<>();
        BsPremanuBomInfo e1 = new BsPremanuBomInfo();
        e1.setItemNo("124");
        e1.setSortSeq(new BigDecimal("1"));
        e1.setTagNum("gu,HU");
        e1.setBomCode("123");
        preBomDetails.add(e1);
        PowerMockito.when(bsPremanuBomInfoRepository.selectBsPreBomDetailsByBomCode(Mockito.any())).thenReturn(preBomDetails);

        List<BsPremanuItemInfo> preItemInfoList = new LinkedList<>();
        BsPremanuItemInfo f1 = new BsPremanuItemInfo();
        f1.setItemNo("123");
        preItemInfoList.add(f1);
        PowerMockito.when(bsPremanuItemInfoRepository.queryPreManuItemInfoNoPage(Mockito.any())).thenReturn(preItemInfoList);

        List<BPcbLocationDetailDTO> itemList = new LinkedList<>();
        BPcbLocationDetailDTO g1 = new BPcbLocationDetailDTO();
        g1.setItemCode("040");
        g1.setUsageCount("12");
        g1.setCraftSection("SMT-A");
        BPcbLocationDetailDTO g2 = new BPcbLocationDetailDTO();
        g2.setItemCode("125");
        g2.setUsageCount("12");
        g2.setCraftSection("SMT-B");
        itemList.add(g1);
        itemList.add(g2);
        PowerMockito.when(bPcbLocationDetailRepository.querySubLevelList(Mockito.any())).thenReturn(itemList);

        List<BBomDetailDTO> bomItemList = new LinkedList<>();
        BBomDetailDTO h1 = new BBomDetailDTO();
        h1.setItemCode("123");
        h1.setUsageCount(new BigDecimal("24"));
        BBomDetailDTO h2 = new BBomDetailDTO();
        h2.setItemCode("124");
        h2.setUsageCount(new BigDecimal("24"));
        BBomDetailDTO h3 = new BBomDetailDTO();
        h3.setItemCode("040");
        h3.setUsageCount(new BigDecimal("24"));
        bomItemList.add(h1);
        bomItemList.add(h2);
        bomItemList.add(h3);
        PowerMockito.when(bBomDetailRepository.getBomItemList(Mockito.any())).thenReturn(bomItemList);
        List<BProdBomHeaderDTO> changList = new LinkedList<>();
        PowerMockito.when(bProdBomHeaderRepository.queryBProdBomListBatch(any(),any())).thenReturn(changList);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        bsPreItemInfoAsyncServiceImpl.selectSubLevelBatch(detailList);
        detail.setSourceTask("123");
        bsPreItemInfoAsyncServiceImpl.selectSubLevelBatch(detailList);
        detail.setIncludeItemLevelAndTypeCodeEmpty(true);
        detail1.setIncludeItemLevelAndTypeCodeEmpty(true);
        bsPreItemInfoAsyncServiceImpl.selectSubLevelBatch(detailList);
        detailList.remove(detail1);
        bsPreItemInfoAsyncServiceImpl.selectSubLevelBatch(detailList);

        detailList = new ArrayList<>();
        try {
            bsPreItemInfoAsyncServiceImpl.selectSubLevelBatch(detailList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }


    @Test
    public void selectSubLevelBatch2() throws Exception {
        List<BsBomHierarchicalDetail> detailList = new ArrayList<>();
        BsBomHierarchicalDetail detail = new BsBomHierarchicalDetail();
        detail.setBomCode("123");
        BsBomHierarchicalDetail detail1 = new BsBomHierarchicalDetail();
        detail1.setBomCode("123");
        detailList.add(detail);
        detailList.add(detail1);
        List<BsBomHierarchicalHead> bomHierarchicalHeadList = new ArrayList<>();
        List<BsBomHierarchicalDetail> tempDetailList = new ArrayList<>();
        BsBomHierarchicalHead bsBomHierarchicalHead = new BsBomHierarchicalHead();
        bsBomHierarchicalHead.setBomCode("123");
        BsBomHierarchicalDetail bsBomHierarchicalDetail = new BsBomHierarchicalDetail();
        bsBomHierarchicalDetail.setTypeCode("123");
        tempDetailList.add(bsBomHierarchicalDetail);
        bsBomHierarchicalHead.setDetails(tempDetailList);
        bomHierarchicalHeadList.add(bsBomHierarchicalHead);
        PowerMockito.when(bsBomHierarchicalService.selectHeadAndDetailBatch(Mockito.any())).thenReturn(bomHierarchicalHeadList);

        List<SysLookupValues> dirList = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        a1.setLookupMeaning("true");
        dirList.add(a1);
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.any())).thenReturn(dirList);

        List<BProdBomHeaderDTO> bomHeaderDTOS = new ArrayList<>();
        BProdBomHeaderDTO bomHeaderDTO = new BProdBomHeaderDTO();
        bomHeaderDTO.setProductCode("123");
        bomHeaderDTO.setChiDesc("123");
        bomHeaderDTO.setIncludePcbFpc(0);
        bomHeaderDTOS.add(bomHeaderDTO);
        PowerMockito.when(bProdBomHeaderRepository.queryBProdBomHeader(Mockito.any())).thenReturn(Collections.emptyList());

        List<BProdBomDetailDTO> mBomDetailList = new ArrayList<>();
        BProdBomDetailDTO mBomDetailDTO = new BProdBomDetailDTO();
        mBomDetailDTO.setItemCode("124");
        mBomDetailDTO.setUsageCount(new BigDecimal("24"));
        mBomDetailList.add(mBomDetailDTO);
        BProdBomDetailDTO mBomDetailDTO2 = new BProdBomDetailDTO();
        mBomDetailDTO2.setItemCode("123");
        mBomDetailDTO2.setUsageCount(new BigDecimal("23"));
        mBomDetailList.add(mBomDetailDTO2);
        BProdBomDetailDTO mBomDetailDTO3 = new BProdBomDetailDTO();
        mBomDetailDTO3.setItemCode("122");
        mBomDetailDTO3.setUsageCount(new BigDecimal("22"));
        mBomDetailList.add(mBomDetailDTO3);
        PowerMockito.when(bProdBomDetailRepository.getMBomDetailList(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(Collections.emptyList());
        List<BsItemInfo> infoList = new LinkedList<>();
        BsItemInfo d1 = new BsItemInfo();
        d1.setItemNo("123");
        d1.setAbcType("A");
        d1.setIsSmt(new BigDecimal(1));
        infoList.add(d1);
        BsItemInfo d2 = new BsItemInfo();
        d2.setItemNo("124");
        d2.setAbcType("A");
        d2.setIsSmt(new BigDecimal(1));
        infoList.add(d2);
        BsItemInfo d3 = new BsItemInfo();
        d3.setItemNo("040");
        d3.setAbcType("A");
        d3.setIsSmt(new BigDecimal(1));
        infoList.add(d3);
        PowerMockito.when(bsItemInfoRepository.getInfoList(Mockito.any())).thenReturn(infoList);
        List<BsPremanuBomInfo> preBomDetails = new LinkedList<>();
        BsPremanuBomInfo e1 = new BsPremanuBomInfo();
        e1.setItemNo("124");
        e1.setSortSeq(new BigDecimal("1"));
        e1.setTagNum("gu,HU");
        e1.setBomCode("123");
        preBomDetails.add(e1);
        PowerMockito.when(bsPremanuBomInfoRepository.selectBsPreBomDetailsByBomCode(Mockito.any())).thenReturn(preBomDetails);

        List<BsPremanuItemInfo> preItemInfoList = new LinkedList<>();
        BsPremanuItemInfo f1 = new BsPremanuItemInfo();
        f1.setItemNo("123");
        preItemInfoList.add(f1);
        PowerMockito.when(bsPremanuItemInfoRepository.queryPreManuItemInfoNoPage(Mockito.any())).thenReturn(preItemInfoList);

        List<BPcbLocationDetailDTO> itemList = new LinkedList<>();
        BPcbLocationDetailDTO g1 = new BPcbLocationDetailDTO();
        g1.setItemCode("040");
        g1.setUsageCount("12");
        g1.setCraftSection("SMT-A");
        BPcbLocationDetailDTO g2 = new BPcbLocationDetailDTO();
        g2.setItemCode("125");
        g2.setUsageCount("12");
        g2.setCraftSection("SMT-B");
        itemList.add(g1);
        itemList.add(g2);
        PowerMockito.when(bPcbLocationDetailRepository.querySubLevelList(Mockito.any())).thenReturn(itemList);

        List<BBomDetailDTO> bomItemList = new LinkedList<>();
        BBomDetailDTO h1 = new BBomDetailDTO();
        h1.setItemCode("123");
        h1.setUsageCount(new BigDecimal("24"));
        BBomDetailDTO h2 = new BBomDetailDTO();
        h2.setItemCode("124");
        h2.setUsageCount(new BigDecimal("24"));
        BBomDetailDTO h3 = new BBomDetailDTO();
        h3.setItemCode("040");
        h3.setUsageCount(new BigDecimal("24"));
        bomItemList.add(h1);
        bomItemList.add(h2);
        bomItemList.add(h3);
        PowerMockito.when(bBomDetailRepository.getBomItemList(Mockito.any())).thenReturn(bomItemList);
        List<BProdBomHeaderDTO> changList = new LinkedList<>();
        BProdBomHeaderDTO chang = new BProdBomHeaderDTO();
        chang.setProdplanId("123");
        chang.setProductCode("129571751003AOB_1");
        chang.setOriginalProductCode("129571751003AOB");
        BProdBomHeaderDTO chang2 = new BProdBomHeaderDTO();
        chang2.setProdplanId("123");
        chang2.setProductCode("129571751003AOB_1");
        chang2.setOriginalProductCode("129571751003AOB");
        BProdBomHeaderDTO chang3 = new BProdBomHeaderDTO();
        chang3.setProdplanId("1234");
        chang3.setProductCode("129571751003AOB_1");
        chang3.setOriginalProductCode("129571751003AOB");
        changList.add(chang);
        changList.add(chang2);
        changList.add(chang3);
        PowerMockito.when(bProdBomHeaderRepository.queryBProdBomListBatch(any(),any())).thenReturn(changList);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        bsPreItemInfoAsyncServiceImpl.selectSubLevelBatch(detailList);
        detail.setSourceTask("123");
        bsPreItemInfoAsyncServiceImpl.selectSubLevelBatch(detailList);
        PowerMockito.when(bProdBomHeaderRepository.queryBProdBomHeader(Mockito.any())).thenReturn(bomHeaderDTOS);
        bsPreItemInfoAsyncServiceImpl.selectSubLevelBatch(detailList);
        PowerMockito.when(bProdBomDetailRepository.getMBomDetailList(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(mBomDetailList);
        bsPreItemInfoAsyncServiceImpl.selectSubLevelBatch(detailList);
        detail.setIncludeItemLevelAndTypeCodeEmpty(true);
        detail1.setIncludeItemLevelAndTypeCodeEmpty(true);
        bsPreItemInfoAsyncServiceImpl.selectSubLevelBatch(detailList);
        detailList.remove(detail1);
        bsPreItemInfoAsyncServiceImpl.selectSubLevelBatch(detailList);

        detailList = new ArrayList<>();
        try {
            bsPreItemInfoAsyncServiceImpl.selectSubLevelBatch(detailList);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }
}