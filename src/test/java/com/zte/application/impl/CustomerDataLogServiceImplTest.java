package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.zte.aiop.dtems.AlarmHelper;
import com.zte.application.HrmUserCenterService;
import com.zte.application.bytedance.BoardRepairService;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.common.utils.InteractiveB2B;
import com.zte.common.utils.InteractiveTencentB2B;
import com.zte.common.utils.PushDataToB2B;
import com.zte.domain.model.CustomerDataLogRepository;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.InforDatawbRemoteService;
import com.zte.infrastructure.remote.ProductionmgmtRemoteService;
import com.zte.interfaces.dto.ALiResultDTO;
import com.zte.interfaces.dto.AliFailMsgDTO;
import com.zte.interfaces.dto.B2bCallBackDTO;
import com.zte.interfaces.dto.ByteDanceDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerQualityDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.MeiTuanQualityDTO;
import com.zte.interfaces.dto.MeiTuanQualityDataDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.TencentForwardOutDataDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.CommonUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zte.common.utils.Constant.ZTEIMES_ALIBABA_QUALITYCODE;
import static com.zte.common.utils.Constant.ZTEIMES_BYTE_DANCE_SERVERQUALITYCODE;
import static com.zte.common.utils.Constant.ZTEIMES_MEI_TUAN_QUALITYCODE;
import static com.zte.common.utils.Constant.ZTE_IMES_TENCENT_FORWARD_QUERY;
import static com.zte.common.utils.Constant.ZTE_IMES_TENCENT_QUERY_MPT_TEST_DATA;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DateUtil.class, JacksonJsonConverUtil.class, SpringContextUtil.class, ConstantInterface.class, HttpClientUtil.class, CommonUtils.class, JSON.class, AlarmHelper.class, com.zte.common.CommonUtils.class})
public class CustomerDataLogServiceImplTest extends BaseTestCase {

    @InjectMocks
    private CustomerDataLogServiceImpl customerDataLogService;

    @Mock
    private CustomerDataLogRepository customerDataLogRepository;
    @Mock
    private HrmUserCenterService hrmUserCenterService;
    @Mock
    private SysLookupTypesRepository sysLookupTypesRepository;
    @Mock
    private ProductionmgmtRemoteService productionmgmtRemoteService;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private BoardRepairService boardRepairService;
    @Mock
    private PushDataToB2B pushDataToB2B;

    @Mock
    private InteractiveB2B interactiveB2B;

    @Mock
    private AlarmHelper alarmHelper;
    @Mock
    private InforDatawbRemoteService inforDatawbRemoteService;
    @Mock
    private InteractiveTencentB2B interactiveTencentB2B;

    @Mock
    private ObjectMapper mapperInstance;

    @Mock
    private JsonNode json;


    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(JacksonJsonConverUtil.class, SpringContextUtil.class, ConstantInterface.class, HttpClientUtil.class);
    }

    @Test
    public void setFailMsgAndResult() throws Exception {
        B2bCallBackDTO b2bCallBackDTO = new B2bCallBackDTO();
        Whitebox.invokeMethod(customerDataLogService,"setFailMsgAndResult",b2bCallBackDTO,null);
        AliFailMsgDTO aliFailMsgDTO = new AliFailMsgDTO();
        Whitebox.invokeMethod(customerDataLogService,"setFailMsgAndResult",b2bCallBackDTO,aliFailMsgDTO);
        aliFailMsgDTO.setResult(new ALiResultDTO(){{setSuccess(false);}});
        Whitebox.invokeMethod(customerDataLogService,"setFailMsgAndResult",b2bCallBackDTO,aliFailMsgDTO);
        aliFailMsgDTO.setResult(new ALiResultDTO(){{setSuccess(true);}});
        Whitebox.invokeMethod(customerDataLogService,"setFailMsgAndResult",b2bCallBackDTO,aliFailMsgDTO);

        Assert.assertNotNull(aliFailMsgDTO);
    }
    @Test
    public void alibabaMessageSpecialHandling() throws Exception {
        B2bCallBackDTO b2bCallBackDTO = new B2bCallBackDTO();
        AliFailMsgDTO aliFailMsgDTO = new AliFailMsgDTO();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setMessageType(Constant.ZTEIMES_ALIBABA_MCTSERVERPRODUCTINFO);
        Whitebox.invokeMethod(customerDataLogService,"alibabaMessageSpecialHandling",b2bCallBackDTO,customerDataLogDTO);


        aliFailMsgDTO.setResult(new ALiResultDTO(){{setSuccess(false);}});
        b2bCallBackDTO.setFailMsg(JSON.toJSONString(aliFailMsgDTO));
        PowerMockito.when(JacksonJsonConverUtil.beanToJson(any())).thenReturn(JSON.toJSONString(aliFailMsgDTO));
        Whitebox.invokeMethod(customerDataLogService,"alibabaMessageSpecialHandling",b2bCallBackDTO,customerDataLogDTO);

        aliFailMsgDTO.setResult(new ALiResultDTO(){{setSuccess(true);}});
        b2bCallBackDTO.setFailMsg(JacksonJsonConverUtil.beanToJson(aliFailMsgDTO));
        PowerMockito.when(JacksonJsonConverUtil.beanToJson(any())).thenReturn(JSON.toJSONString(aliFailMsgDTO));
        Whitebox.invokeMethod(customerDataLogService,"alibabaMessageSpecialHandling",b2bCallBackDTO,customerDataLogDTO);
        Assert.assertNotNull(customerDataLogDTO);
    }

    @Test
    public void testQueryLogData() throws Exception {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        try {
            customerDataLogService.queryLogData(customerDataLogDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOG_DATA_QUERY_PARAM_NULL, e.getMessage());
        }
        customerDataLogDTO.setStatus("PN");
        try {
            customerDataLogService.queryLogData(customerDataLogDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOG_DATA_QUERY_PARAM_NOT_WITH_TIME, e.getMessage());
        }
        customerDataLogDTO.setLastUpdatedStartDate(new Date(5000));
        customerDataLogDTO.setLastUpdatedEndDate(new Date());
        try {
            customerDataLogService.queryLogData(customerDataLogDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PUSH_TIME_WITHIN_180_DAYS, e.getMessage());
        }
        customerDataLogDTO.setLastUpdatedStartDate(new Date());
        customerDataLogDTO.setLastUpdatedBy("00000000");
        customerDataLogDTO.setSn("12345");
        List<CustomerDataLogDTO> qryList = new ArrayList<>();
        qryList.add(customerDataLogDTO);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("张三");
        hrmPersonInfoDTOMap.put("00000000", null);
        PowerMockito.when(customerDataLogRepository.pageList(any())).thenReturn(qryList);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);
        customerDataLogService.queryLogData(customerDataLogDTO);

        hrmPersonInfoDTOMap.put("00000000", hrmPersonInfoDTO);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);
        customerDataLogService.queryLogData(customerDataLogDTO);


    }

    @Test
    public void testRepushCustomerLogDataMethod() throws Exception {
        List<CustomerDataLogDTO> list = new ArrayList<>();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setMessageType("1");
        customerDataLogDTO.setContractNo("12345");
        customerDataLogDTO.setFactoryId(1);
        customerDataLogDTO.setOrigin("iMES");
        list.add(customerDataLogDTO);
        List<SysLookupTypesDTO> valuesList = new ArrayList<>();
        Map<String, Object> requestParam = new HashMap<>(Constant.INT_2);
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_3410);
        Map<String, Object> urlParam = new HashMap<>(Constant.INT_2);
        urlParam.put("lookupType", Constant.LOOK_UP_CODE_1245);
        try {
            PowerMockito.when(sysLookupTypesRepository.getList(requestParam)).thenReturn(valuesList);
            customerDataLogService.repushCustomerLogDataMethod(list, "00000000");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOG_DATA_LOOKUP_TYPE_ERROR, e.getMessage());
        }
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("0");
        valuesList.add(sysLookupTypesDTO);
        try {
            PowerMockito.when(sysLookupTypesRepository.getList(requestParam)).thenReturn(valuesList);
            PowerMockito.when(sysLookupTypesRepository.getList(urlParam)).thenReturn(new ArrayList<>());
            customerDataLogService.repushCustomerLogDataMethod(list, "00000000");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOG_DATA_LOOKUP_TYPE_ERROR, e.getMessage());
        }
        sysLookupTypesDTO.setLookupMeaning("1");
        valuesList.add(sysLookupTypesDTO);
        CustomerDataLogDTO customerDataLogDTO1 = new CustomerDataLogDTO();
        customerDataLogDTO1.setMessageType("1");
        customerDataLogDTO1.setContractNo("12345");
        customerDataLogDTO1.setFactoryId(1);
        customerDataLogDTO1.setOrigin("MES");
        customerDataLogDTO1.setTid("1234");
        CustomerDataLogDTO customerDataLogDTO2 = new CustomerDataLogDTO();
        customerDataLogDTO2.setMessageType("2");
        customerDataLogDTO2.setOrigin("iMES");
        CustomerDataLogDTO customerDataLogDTO3 = new CustomerDataLogDTO();
        customerDataLogDTO3.setMessageType("2");
        customerDataLogDTO3.setOrigin("MES");
        CustomerDataLogDTO customerDataLogDTO4 = new CustomerDataLogDTO();
        customerDataLogDTO4.setOrigin("ASMS");
        list.add(customerDataLogDTO1);
        list.add(customerDataLogDTO2);
        list.add(customerDataLogDTO3);
        list.add(customerDataLogDTO4);
        PowerMockito.when(sysLookupTypesRepository.getList(any())).thenReturn(valuesList);
        customerDataLogService.repushCustomerLogDataMethod(list, "00000000");
        Map<String, Object> testMap = new HashMap<>();
        testMap.put("test", list);
        Whitebox.invokeMethod(customerDataLogService, "repushIMESData", testMap, new ArrayList<>(), new ArrayList<>(), "1234");
        Whitebox.invokeMethod(customerDataLogService, "repushMESData", testMap, new ArrayList<>(), "1234");
        Whitebox.invokeMethod(customerDataLogService, "repushASMSData", testMap);

    }

    @Test
    public void testInsertCustomerDataLog() {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        Assert.assertNotNull(customerDataLogService.insertCustomerDataLogFromMES(customerDataLogDTO));
    }

    @Test
    public void pushDataToB2BTest() throws Exception {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        dataList.add(dto);
        customerDataLogService.pushDataToB2B(dataList);
        Assert.assertNotNull(dataList);
    }

    @Test
    public void insertEntityTest() throws Exception {
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        customerDataLogService.insertEntity(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void batchInsertTest() throws Exception {
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        List<CustomerDataLogDTO> dtoList = new ArrayList<>();
        dtoList.add(dto);
        customerDataLogService.batchInsert(dtoList);
        Assert.assertNotNull(dtoList);
    }

    @Test
    public void updateAfterPushDataFailTest() throws Exception {
        String msg = new String("test");
        String id = "id";
        customerDataLogService.updateAfterPushDataFail(msg, id);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void realTimeInteractiveB2BTest() throws Exception {
        List<String> serverSnList = new ArrayList<>();
        serverSnList.add("test");
        CustomerQualityDTO dto = new CustomerQualityDTO();
        dto.setMessageType("11111");
        dto.setServerSnList(serverSnList);
        List<ByteDanceDTO> byteDanceDTOS = new ArrayList<>();
        try {
            customerDataLogService.realTimeInteractiveB2B(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

        dto.setMessageType(ZTEIMES_BYTE_DANCE_SERVERQUALITYCODE);
        dto.setServerSnList(serverSnList);
        try {
            customerDataLogService.realTimeInteractiveB2B(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

        dto.setMessageType(ZTEIMES_MEI_TUAN_QUALITYCODE);
        MeiTuanQualityDTO meiTuanQualityDTO = new MeiTuanQualityDTO();
        meiTuanQualityDTO.setCode("0");
        String result = "{\"code\":0,\"msg\":\"success\",\"data\":{}}";
        try {
            customerDataLogService.realTimeInteractiveB2B(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

        dto.setMessageType(ZTEIMES_MEI_TUAN_QUALITYCODE);
        serverSnList.add("1111");
        dto.setServerSnList(serverSnList);
        meiTuanQualityDTO.setCode("0");
        result = "{\"code\":0,\"msg\":\"success\",\"data\":{}}";
        try {
            customerDataLogService.realTimeInteractiveB2B(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

        List<TencentForwardOutDataDTO> tencentForwardOutDataDTOList = new ArrayList<>();
        TencentForwardOutDataDTO tencentForwardOutDataDTO = new TencentForwardOutDataDTO();
        tencentForwardOutDataDTO.setSvrSN("111");
        tencentForwardOutDataDTOList.add(tencentForwardOutDataDTO);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.anyString(), Mockito.anyObject())).thenReturn(tencentForwardOutDataDTOList);
        dto.setMessageType(ZTE_IMES_TENCENT_FORWARD_QUERY);
        dto.setServerSnList(serverSnList);
        meiTuanQualityDTO.setCode("0");
        result = "{\"code\":0,\"msg\":\"success\",\"data\":{}}";
        try {
            customerDataLogService.realTimeInteractiveB2B(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

        dto.setMessageType(ZTE_IMES_TENCENT_QUERY_MPT_TEST_DATA);
        dto.setServerSnList(serverSnList);
        meiTuanQualityDTO.setCode("0");
        result = "{\"code\":0,\"msg\":\"success\",\"data\":{}}";
        try {
            customerDataLogService.realTimeInteractiveB2B(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

        MeiTuanQualityDataDTO meiTuanQualityDataDTO = new MeiTuanQualityDataDTO();
        meiTuanQualityDataDTO.setQualityCode("111");
        meiTuanQualityDataDTO.setMsg("1111");
        meiTuanQualityDTO.setData(meiTuanQualityDataDTO);
        result = "{\"code\":0,\"msg\":\"success\",\"data\":{\"checkResult\":true,\"qualityCode\":\"ZTE-2024-12-11-219570641050\",\"msg\":\"校对通过\",\"setCheckResult\":true,\"setQualityCode\":true,\"setMsg\":true}}";
        try {
            customerDataLogService.realTimeInteractiveB2B(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Mock
    private JacksonJsonConverUtil jacksonJsonConverUtil;

    @Test
    public void getTencentDataTest() throws Exception {
        List<TencentForwardOutDataDTO> tencentForwardOutDataDTOList = new ArrayList<>();
        TencentForwardOutDataDTO dto = new TencentForwardOutDataDTO();
        dto.setServerSN("12345");
        dto.setMptResult("PASS");
        dto.setMptResultDesc("Success");
        tencentForwardOutDataDTOList.add(dto);
        TencentForwardOutDataDTO dto1 = new TencentForwardOutDataDTO();
        dto1.setServerSN("12345");
        dto1.setResult("PASS");
        dto1.setMessage("Success");
        tencentForwardOutDataDTOList.add(dto1);
        List<ByteDanceDTO> byteDanceDTOS = new ArrayList<>();
        String result = "[{\"mptResult\":1,\"mptResultDesc\":\"未找到HOST采集文件\",\"partResultList\":[],\"serverSN\":\"219415761672\",\"transitionTips\":\"\"}]";
        String messageType = ZTE_IMES_TENCENT_QUERY_MPT_TEST_DATA;

        when(jacksonJsonConverUtil.jsonToListBean(eq(result), any())).thenReturn(tencentForwardOutDataDTOList);
        try {
            customerDataLogService.getTencentData(byteDanceDTOS, result, messageType);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
        messageType = ZTE_IMES_TENCENT_FORWARD_QUERY;
        try {
            customerDataLogService.getTencentData(byteDanceDTOS, result, messageType);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void handleInteractiveALiBabaTest() throws Exception {
        CustomerQualityDTO dto = new CustomerQualityDTO();
        List<ByteDanceDTO> byteDanceDTOS = new ArrayList<>();
        List<String> serverSnList = new ArrayList<>();
        serverSnList.add("test");
        dto.setMessageType(ZTEIMES_ALIBABA_QUALITYCODE);
        dto.setServerSnList(serverSnList);
        try {
            customerDataLogService.interactiveAliBaba(dto, byteDanceDTOS);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void getAliBabaDataTest() throws Exception {
        List<ByteDanceDTO> byteDanceDTOS = new ArrayList<>();
        String result = "{\"body\":\"{\\\"error_response\\\":{\\\"code\\\":40,\\\"msg\\\":\\\"Missing required arguments:generate_quality_code_params\\\",\\\"request_id\\\":\\\"eg58hdyn3608\\\"}}\",\"code\":40,\"msg\":\"Missing required arguments:generate_quality_code_params\",\"request_id\":\"eg58hdyn3608\",\"success\":false}";
        String sn = "123";
        try {
            customerDataLogService.getAliBabaData(byteDanceDTOS, result, sn);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
        result = "{\n" +
                "    \"body\": \"{\\\"result\\\":{\\\"code\\\":\\\"000000\\\",\\\"data\\\":{\\\"check_result\\\":true,\\\"quality_code\\\":\\\"ALI880207194332768\\\"},\\\"msg\\\":\\\"成功\\\",\\\"success\\\":true},\\\"request_id\\\":\\\"eg58hbo9tyge\\\"}\",\n" +
                "    \"request_id\": \"eg58hbo9tyge\",\n" +
                "    \"result\": {\n" +
                "        \"code\": \"000000\",\n" +
                "        \"data\": null,\n" +
                "        \"msg\": \"成功\",\n" +
                "        \"success\": true\n" +
                "    },\n" +
                "    \"success\": true\n" +
                "}";
        try {
            customerDataLogService.getAliBabaData(byteDanceDTOS, result, sn);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
        result = "{\n" +
                "    \"body\": \"{\\\"result\\\":{\\\"code\\\":\\\"000000\\\",\\\"data\\\":{\\\"check_result\\\":true,\\\"quality_code\\\":\\\"ALI880207194332768\\\"},\\\"msg\\\":\\\"成功\\\",\\\"success\\\":true},\\\"request_id\\\":\\\"eg58hbo9tyge\\\"}\",\n" +
                "    \"request_id\": \"eg58hbo9tyge\",\n" +
                "    \"result\": {\n" +
                "        \"code\": \"000000\",\n" +
                "        \"data\": {\n" +
                "            \"check_result\": true,\n" +
                "            \"quality_code\": \"ALI880207194332768\"\n" +
                "        },\n" +
                "        \"msg\": \"成功\",\n" +
                "        \"success\": true\n" +
                "    },\n" +
                "    \"success\": true\n" +
                "}";
        try {
            customerDataLogService.getAliBabaData(byteDanceDTOS, result, sn);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
        result = "{\n" +
                "\t\"body\": \"{\\\"result\\\":{\\\"code\\\":\\\"000000\\\",\\\"data\\\":{\\\"check_result\\\":false,\\\"fail_code\\\":\\\"QC0003\\\",\\\"msg\\\":\\\"占位测试数据不存在(station test data does not exist)\\\"},\\\"msg\\\":\\\"成功\\\",\\\"success\\\":true},\\\"request_id\\\":\\\"15snjo4qjir72\\\"}\",\n" +
                "\t\"request_id\": \"15snjo4qjir72\",\n" +
                "\t\"result\": {\n" +
                "\t\t\"code\": \"000000\",\n" +
                "\t\t\"data\": {\n" +
                "\t\t\t\"check_result\": false,\n" +
                "\t\t\t\"fail_code\": \"QC0003\",\n" +
                "\t\t\t\"msg\": \"占位测试数据不存在(station test data does not exist)\"\n" +
                "\t\t},\n" +
                "\t\t\"msg\": \"成功\",\n" +
                "\t\t\"success\": true\n" +
                "\t},\n" +
                "\t\"success\": true\n" +
                "}";
        try {
            customerDataLogService.getAliBabaData(byteDanceDTOS, result, sn);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void handleResultOfPushDataToB2BTest() throws Exception {
        B2bCallBackDTO dto = new B2bCallBackDTO();
        dto.setResult("SUCCESS");
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        PowerMockito.when(customerDataLogRepository.getPushedDataById(any())).thenReturn(customerDataLogDTO);
        customerDataLogService.handleResultOfPushDataToB2B(dto);
        customerDataLogDTO.setProjectPhase("WAREHOUSE123");
        PowerMockito.when(customerDataLogRepository.getPushedDataById(any())).thenReturn(customerDataLogDTO);
        customerDataLogService.handleResultOfPushDataToB2B(dto);
        customerDataLogDTO.setProjectPhase("WAREHOUSE");
        dto.setResult("FAILED");
        PowerMockito.when(customerDataLogRepository.getPushedDataById(any())).thenReturn(customerDataLogDTO);
        customerDataLogService.handleResultOfPushDataToB2B(dto);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), Mockito.anyObject())).thenReturn("{\"status\":\"success\",\"message\":\"success\",\"data\":\"123123\",\"traceId\":\"97b5761b84c40ccd0504b711badab8f1\",\"ok\":true}");
        PowerMockito.when(mapperInstance.readTree(anyString())).thenReturn(json);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(json.get(anyString())).thenReturn(json);
        dto.setFailMsg("{\"status\":\"success\",\"message\":\"success\",\"data\":null,\"traceId\":\"97b5761b84c40ccd0504b711badab8f1\",\"ok\":true}");
        customerDataLogService.handleResultOfPushDataToB2B(dto);
        customerDataLogDTO.setStatus("CN");
        customerDataLogService.updateLog(customerDataLogDTO);
        customerDataLogService.handleResultOfPushDataToB2B(dto);
        Assert.assertNotNull(dto);
    }

    /* Started by AICoder, pid:q1f15t0157me6d9144d20988f0c8fc7a1910ef07 */
    @Test
    public void updateLog() {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setStatus(Constant.PUSH_B2B_STATUS.CN);
        customerDataLogService.updateSuccessLog(customerDataLogDTO);
        customerDataLogService.updateLog(customerDataLogDTO);
        customerDataLogDTO.setStatus(Constant.PUSH_B2B_STATUS.CY);
        customerDataLogService.updateFailedLog(customerDataLogDTO);
        customerDataLogService.updateLog(customerDataLogDTO);

        Assert.assertNotNull(customerDataLogDTO);
    }
    /* Ended by AICoder, pid:q1f15t0157me6d9144d20988f0c8fc7a1910ef07 */

    @Test
    public void updateSuccessLog() {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setStatus(Constant.PUSH_B2B_STATUS.CY);
        customerDataLogDTO.setMessageType("1111");
        customerDataLogService.updateSuccessLog(customerDataLogDTO);
        customerDataLogDTO.setMessageType(Constant.MESSAGE_TYPE_B2B_MEITUAN);
        customerDataLogService.updateSuccessLog(customerDataLogDTO);
        customerDataLogDTO.setMessageType(Constant.MEITUAN_TEST_FILE_BYTEDANCE);
        customerDataLogService.updateSuccessLog(customerDataLogDTO);
        customerDataLogDTO.setOrigin("1111");
        customerDataLogDTO.setMessageType("2222222");
        customerDataLogService.updateSuccessLog(customerDataLogDTO);
        customerDataLogDTO.setOrigin(Constant.INFOR_WMS);
        customerDataLogService.updateSuccessLog(customerDataLogDTO);
        customerDataLogDTO.setOrigin(Constant.MES);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        PowerMockito.when(datawbRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        customerDataLogService.updateSuccessLog(customerDataLogDTO);
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setDescription("111111");
        sysLookupValuesDTO.setSortSeq(new BigDecimal(0));
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        PowerMockito.when(datawbRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        customerDataLogService.updateSuccessLog(customerDataLogDTO);
        sysLookupValuesDTOList.clear();
        sysLookupValuesDTO.setDescription("111111");
        sysLookupValuesDTO.setSortSeq(new BigDecimal(1));
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        PowerMockito.when(datawbRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        customerDataLogService.updateSuccessLog(customerDataLogDTO);
        sysLookupValuesDTOList.clear();
        sysLookupValuesDTO.setDescription("2222222");
        sysLookupValuesDTO.setSortSeq(new BigDecimal(0));
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        PowerMockito.when(datawbRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        customerDataLogService.updateSuccessLog(customerDataLogDTO);
        sysLookupValuesDTOList.clear();
        sysLookupValuesDTO.setDescription("2222222");
        sysLookupValuesDTO.setSortSeq(new BigDecimal(1));
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        PowerMockito.when(datawbRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        customerDataLogService.updateSuccessLog(customerDataLogDTO);
        customerDataLogDTO.setMessageType(Constant.ZTE_IMES_BYTEDANCE_MOCOMPLETION_FEEDBACK);
        customerDataLogService.updateSuccessLog(customerDataLogDTO);

        Assert.assertNotNull(customerDataLogDTO);
    }

    @Test
    public void updateFailedLog() {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setStatus(Constant.PUSH_B2B_STATUS.CN);
        customerDataLogDTO.setOrigin("1111");
        customerDataLogDTO.setMessageType("2222222");
        customerDataLogService.updateFailedLog(customerDataLogDTO);
        customerDataLogDTO.setOrigin(Constant.MES);
        List<SysLookupValuesDTO> sysLookupValuesDTOList = new ArrayList<>();
        PowerMockito.when(datawbRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        customerDataLogService.updateFailedLog(customerDataLogDTO);
        SysLookupValuesDTO sysLookupValuesDTO = new SysLookupValuesDTO();
        sysLookupValuesDTO.setDescription("111111");
        sysLookupValuesDTO.setSortSeq(new BigDecimal(0));
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        PowerMockito.when(datawbRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        customerDataLogService.updateFailedLog(customerDataLogDTO);
        sysLookupValuesDTOList.clear();
        sysLookupValuesDTO.setDescription("111111");
        sysLookupValuesDTO.setSortSeq(new BigDecimal(1));
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        PowerMockito.when(datawbRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        customerDataLogService.updateFailedLog(customerDataLogDTO);
        customerDataLogService.updateFailedLog(customerDataLogDTO);
        sysLookupValuesDTOList.clear();
        sysLookupValuesDTO.setDescription("2222222");
        sysLookupValuesDTO.setSortSeq(new BigDecimal(0));
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        PowerMockito.when(datawbRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        customerDataLogService.updateFailedLog(customerDataLogDTO);
        customerDataLogService.updateFailedLog(customerDataLogDTO);
        sysLookupValuesDTOList.clear();
        sysLookupValuesDTO.setDescription("2222222");
        sysLookupValuesDTO.setSortSeq(new BigDecimal(1));
        sysLookupValuesDTOList.add(sysLookupValuesDTO);
        PowerMockito.when(datawbRemoteService.getSysLookupValuesList(Mockito.any())).thenReturn(sysLookupValuesDTOList);
        customerDataLogService.updateFailedLog(customerDataLogDTO);
        customerDataLogDTO.setMessageType(Constant.ZTE_IMES_BYTEDANCE_MOCOMPLETION_FEEDBACK);
        customerDataLogService.updateFailedLog(customerDataLogDTO);

        Assert.assertNotNull(customerDataLogDTO);
    }

    @Test
    public void getPushedSnScanData() {
        customerDataLogService.getPushedSnScanData(null);
        Assert.assertNotNull(customerDataLogService.getPushedSnScanData(Lists.newArrayList("1")));
    }

    @Test
    public void testGetPushedTestProcessSnScanData() {
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        dataList.add(customerDataLogDTO);
        customerDataLogService.getPushedTestProcessSnScanData(null);
        Assert.assertNotNull(customerDataLogService.getPushedTestProcessSnScanData(dataList));
    }

    @Test
    public void repushMESDataTest() throws Exception {
        Map<String, List<CustomerDataLogDTO>> originMap = new HashMap<>();
        List<SysLookupTypesDTO> valuesList = new ArrayList<>();
        String empNo = "1234";
        Whitebox.invokeMethod(customerDataLogService, "repushMESData", originMap, valuesList, empNo);

        List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>();
        CustomerDataLogDTO c1 = new CustomerDataLogDTO();
        c1.setProjectPhase("WAREHOUSE");
        c1.setContractNo("12345");
        c1.setId("1");
        c1.setMessageType("2");
        customerDataLogDTOList.add(c1);
        CustomerDataLogDTO c2 = new CustomerDataLogDTO();
        c2.setProjectPhase("1");
        c2.setContractNo("12345");
        c2.setId("2");
        c2.setMessageType("1");
        customerDataLogDTOList.add(c2);
        CustomerDataLogDTO c3 = new CustomerDataLogDTO();
        c3.setMessageType("2");
        c3.setProjectPhase("WAREHOUSE");
        c3.setContractNo("12345");
        c3.setId("2");
        customerDataLogDTOList.add(c3);
        originMap.put(Constant.SOURCE_SYSTEM_MES, customerDataLogDTOList);
        SysLookupTypesDTO s1 = new SysLookupTypesDTO();
        s1.setLookupMeaning("2");
        valuesList.add(s1);
        SysLookupTypesDTO s2 = new SysLookupTypesDTO();
        s2.setLookupMeaning("1");
        s2.setAttribute4("111");
        valuesList.add(s2);
        Whitebox.invokeMethod(customerDataLogService, "repushMESData", originMap, valuesList, empNo);
        Assert.assertNotNull(originMap);
    }

    @Test
    public void getPushedSnScanDataOfL6() {
        customerDataLogService.getPushedSnScanDataOfL6(null);
        Assert.assertNotNull(customerDataLogService.getPushedSnScanDataOfL6(Lists.newArrayList("1")));
    }
}