/*Started by AICoder, pid:vd68fa364443bc9147060a55418ebd284db4c2e9*/
package com.zte.application.impl;

import com.zte.domain.model.BProdBomChangeDetailRepository;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.springbootframe.common.model.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BProdBomChangeDetailServiceImplTest3 {

    @Mock
    private BProdBomChangeDetailRepository bProdBomChangeDetailRepository;

    @InjectMocks
    private BProdBomChangeDetailServiceImpl bProdBomChangeDetailService;

    private BProdBomChangeDetailDTO params;

    @Before
    public void setUp() {
        params = new BProdBomChangeDetailDTO();
    }

    @Test
    public void testSelectMBomDetailChangeByProductCodesAndItem_BothEmpty() {
        // Given
        params.setProductCode(null);
        params.setProductCodes(Collections.emptyList());

        // When
        Page<BProdBomChangeDetailDTO> result = bProdBomChangeDetailService.selectMBomDetailChangeByProductCodesAndItem(params);
        assertEquals(null, result.getRows());
        // Then
    }

    @Test
    public void testSelectMBomDetailChangeByProductCodesAndItem_ProductCodeEmpty() {
        // Given
        params.setProductCode(null);
        params.setProductCodes(Collections.singletonList("code1"));

        // Mock the repository behavior
        when(bProdBomChangeDetailRepository.selectMBomDetailChangeByProductCodesAndItem(any(Page.class)))
                .thenReturn(Collections.singletonList(new BProdBomChangeDetailDTO()));

        // When
        Page<BProdBomChangeDetailDTO> result = bProdBomChangeDetailService.selectMBomDetailChangeByProductCodesAndItem(params);

        // Then
        assertEquals(1, result.getRows().size());
    }

    @Test
    public void testSelectMBomDetailChangeByProductCodesAndItem_ProductCodesEmpty() {
        // Given
        params.setProductCode("code1");
        params.setProductCodes(Collections.emptyList());

        // Mock the repository behavior
        when(bProdBomChangeDetailRepository.selectMBomDetailChangeByProductCodesAndItem(any(Page.class)))
                .thenReturn(Collections.singletonList(new BProdBomChangeDetailDTO()));

        // When
        Page<BProdBomChangeDetailDTO> result = bProdBomChangeDetailService.selectMBomDetailChangeByProductCodesAndItem(params);

        // Then
        assertEquals(1, result.getRows().size());    }

    @Test
    public void testSelectMBomDetailChangeByProductCodesAndItem_BothNonEmpty() {
        // Given
        params.setProductCode("code1");
        params.setProductCodes(Collections.singletonList("code2"));

        // Mock the repository behavior
        when(bProdBomChangeDetailRepository.selectMBomDetailChangeByProductCodesAndItem(any(Page.class)))
                .thenReturn(Collections.singletonList(new BProdBomChangeDetailDTO()));

        // When
        Page<BProdBomChangeDetailDTO> result = bProdBomChangeDetailService.selectMBomDetailChangeByProductCodesAndItem(params);

        // Then
        assertEquals(1, result.getRows().size());
    }

    @Test
    public void selectOriginalByProductCode() {
        Assert.assertNull(bProdBomChangeDetailService.selectOriginalByProductCode(""));
        Assert.assertNotNull(bProdBomChangeDetailService.selectOriginalByProductCode("123"));
    }
}
/*Ended by AICoder, pid:vd68fa364443bc9147060a55418ebd284db4c2e9*/