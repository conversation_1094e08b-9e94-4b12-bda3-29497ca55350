package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.BBomDetailService;
import com.zte.application.BBomHeaderService;
import com.zte.application.BProdBomChangeDetailService;
import com.zte.application.BProdBomDetailService;
import com.zte.application.CenterFactoryCallSiteService;
import com.zte.application.kafka.producer.KafkaMessageProducer;
import com.zte.application.sncabind.PsTaskService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BBomHeader;
import com.zte.domain.model.BBomHeaderRepository;
import com.zte.domain.model.BProdBomHeader;
import com.zte.domain.model.BProdBomHeaderRepository;
import com.zte.domain.model.KafkaLocalMessageRepository;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.domain.model.datawb.BaBomHeadDTO;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.infrastructure.remote.ApsRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.OpenApiRemoteService;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BBomHeaderDTO;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.interfaces.dto.BProdBomDetailDTO;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.aps.ApsDerivativeCodeDTO;
import com.zte.interfaces.dto.aps.ApsDerivativeCodeQueryDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.AsyncExportFileCommonService;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ReflectUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * ClassName: BProdBomHeaderServiceImplTest
 * Description:
 *
 * <AUTHOR>
 * @date 2024/12/17 下午3:43
 */
@PrepareForTest({CommonUtils.class,HttpRemoteUtil.class, ServiceDataBuilderUtil.class,AsyncExportFileCommonService.class,
        DatawbRemoteService.class,MESHttpHelper.class})
public class BProdBomHeaderServiceImplTest2 extends BaseTestCase {
    @InjectMocks
    private BProdBomHeaderServiceImpl service;

    @Mock
    private BProdBomHeaderRepository bProdBomHeaderRepository;
    @Mock
    private BProdBomDetailService bProdBomDetailService;
    @Mock
    private BProdBomChangeDetailService bProdBomChangeDetailService;
    @Mock
    private CenterFactoryCallSiteService centerFactoryCallSiteService;
    @Mock
    private OpenApiRemoteService openApiRemoteService;
    @Mock
    private PsTaskService psTaskService;
    @Mock
    private KafkaMessageProducer kafkaMessageProducer;
    @Mock
    private KafkaLocalMessageRepository kafkaLocalMessageRepository;
    @Mock
    private SysLookupTypesRepository sysLookupTypesRepository;
    @Mock
    private BBomDetailService bBomDetailService;
    @Mock
    private BBomHeaderService bBomHeaderervice;
    @Mock
    protected IdGenerator idGenerator;
    @Mock
    protected ApsRemoteService apsRemoteService;
    @Mock
    private BBomHeaderRepository bBomHeaderRepository;
    @Mock
    AsyncExportFileCommonService asyncExportFileCommonService;
    /*Started by AICoder, pid:b8b7bo0c3b063ae149cb0af23188985713b1f28e*/

    @Test
    public void testgetMbomOrBomHeader() {
        BProdBomHeaderDTO bProdBomHeaderDTO = new BProdBomHeaderDTO();
        try {
            service.getMbomOrBomHeader(bProdBomHeaderDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARMS_ERR,e.getMessage());
        }
        bProdBomHeaderDTO.setPage(1);
        bProdBomHeaderDTO.setRows(10);
        bProdBomHeaderDTO.setProductCode("123");
        bProdBomHeaderDTO.setDevProductCode("dev123");

        try {
            service.getMbomOrBomHeader(bProdBomHeaderDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARMS_ERR,e.getMessage());
        }
        PowerMockito.when(bProdBomHeaderRepository.queryMBomHeadIdByProdplanIdAndProductCode(Mockito.anyString(), Mockito.anyString())).thenReturn("test");
    }
    @Test
    public void testgetMbomOrBomHeader1() {
        BProdBomHeaderDTO bProdBomHeaderDTO = new BProdBomHeaderDTO();
        Page<BProdBomHeaderDTO> mbomOrBomHeader;

        bProdBomHeaderDTO.setPage(1);
        bProdBomHeaderDTO.setRows(10);
        bProdBomHeaderDTO.setProductCode("123");

        List<BProdBomHeaderDTO> bProdBomHeaderDTOs = new ArrayList<>();
        PowerMockito.when(bProdBomHeaderRepository.queryMBomHeaderLike(Mockito.any())).thenReturn(bProdBomHeaderDTOs);
        mbomOrBomHeader = service.getMbomOrBomHeader(bProdBomHeaderDTO);
        assertEquals(mbomOrBomHeader.getRows(), null);

        BProdBomHeaderDTO bProdBomHeaderDTO1 = new BProdBomHeaderDTO();
        bProdBomHeaderDTOs.add(bProdBomHeaderDTO1);
        mbomOrBomHeader = service.getMbomOrBomHeader(bProdBomHeaderDTO);
        assertEquals(mbomOrBomHeader.getRows().size(), 1);
    }
    @Test
    public void testgetMbomOrBomHeader2() {
        BProdBomHeaderDTO bProdBomHeaderDTO = new BProdBomHeaderDTO();
        Page<BProdBomHeaderDTO> mbomOrBomHeader;

        bProdBomHeaderDTO.setPage(1);
        bProdBomHeaderDTO.setRows(10);
        bProdBomHeaderDTO.setDevProductCode("123");

        List<BBomHeader> bomList  = new ArrayList<>();
        PowerMockito.when(bBomHeaderRepository.queryBomHeaderLike(Mockito.any())).thenReturn(bomList);
        mbomOrBomHeader = service.getMbomOrBomHeader(bProdBomHeaderDTO);
        assertEquals(mbomOrBomHeader.getRows(), null);

        BBomHeader bBomHeader1 = new BBomHeader();
        bomList.add(bBomHeader1);
        mbomOrBomHeader = service.getMbomOrBomHeader(bProdBomHeaderDTO);
        assertEquals(mbomOrBomHeader.getRows().size(), 1);
    }
    /*Ended by AICoder, pid:rdf77t46a6oa67414032092cd1a9290a04e005eb*/

    @Test
    public void exportExcel() throws Exception{
        PowerMockito.mockStatic(AsyncExportFileCommonService.class);
        HttpServletResponse response = null;
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dto.setProductCode("123");
        dto.setDevProductCode("123");
        try {
            service.exportExcel(response, dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
        dto.setProductCode("");
        dto.setDevProductCode("");
        try {
            service.exportExcel(response, dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.PARMS_ERR,e.getMessage());
        }
        dto.setProductCode("123");
        List<BProdBomHeaderDTO> bProdBomHeaderDTOs = new ArrayList<>();
        PowerMockito.when(bProdBomHeaderRepository.queryMBomHeaderLike(Mockito.any())).thenReturn(bProdBomHeaderDTOs);
        service.exportExcel(response, dto);
        BProdBomHeaderDTO bProdBomHeaderDTO = new BProdBomHeaderDTO();
        bProdBomHeaderDTO.setProductCode("111");
        bProdBomHeaderDTOs.add(bProdBomHeaderDTO);
        Page<BProdBomDetailDTO> mbomOrBomDetailList = new Page<>();
        PowerMockito.when(bProdBomDetailService.getMbomOrBomDetailList(Mockito.any())).thenReturn(mbomOrBomDetailList);

        try {
            service.exportExcel(response, dto);
        }catch (Exception e){
            Assert.assertEquals(e.getMessage(), MessageId.PRODUCT_CODE_NOT_EXIST);
        }

        dto.setProductCode("");
        dto.setDevProductCode("234");
        List<BBomHeader> bomList  = new ArrayList<>();
        PowerMockito.when(bBomHeaderRepository.queryBomHeaderLike(Mockito.any())).thenReturn(bomList);
        try {
            service.exportExcel(response, dto);
        }catch (Exception e){
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void exportExcel2() throws Exception{
        PowerMockito.mockStatic(DatawbRemoteService.class);
        PowerMockito.mockStatic(AsyncExportFileCommonService.class);
        HttpServletResponse response = null;
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dto.setDevProductCode("123");
        List<BBomHeader> bProdBomHeaderDTOs = new ArrayList<>();
        PowerMockito.when(bBomHeaderRepository.queryBomHeaderLike(Mockito.any())).thenReturn(bProdBomHeaderDTOs);
        service.exportExcel(response, dto);
        BBomHeader bProdBomHeaderDTO = new BBomHeader();
        bProdBomHeaderDTO.setProductCode("111");
        bProdBomHeaderDTOs.add(bProdBomHeaderDTO);
        Page<BProdBomDetailDTO> mbomOrBomDetailList = new Page<>();
        List<BProdBomDetailDTO> bProdBomHeaderDTOs1 = new ArrayList<>();
        mbomOrBomDetailList.setRows(bProdBomHeaderDTOs1);

        List<BaBomHeadDTO> baBomHeadDTOList = new ArrayList<>();
        BaBomHeadDTO baBomHeadDTO = new BaBomHeadDTO();
        baBomHeadDTO.setBomNo("duct1");
        baBomHeadDTO.setIsLead("1");
        baBomHeadDTOList.add(baBomHeadDTO);
        BaBomHeadDTO baBomHeadDTO2 = new BaBomHeadDTO();
        baBomHeadDTO2.setBomNo("duct2");
        baBomHeadDTOList.add(baBomHeadDTO2);
        PowerMockito.when(DatawbRemoteService.queryBomInfoByBomNoList(any())).thenReturn(baBomHeadDTOList);
        PowerMockito.when(bProdBomDetailService.selectDetailByProductCodeListAndItemCode(Mockito.any())).thenReturn(mbomOrBomDetailList);
        bProdBomHeaderDTOs1.add(new BProdBomDetailDTO());
        PowerMockito.when(bProdBomDetailService.selectDetailByProductCodeListAndItemCode(Mockito.any())).thenReturn(mbomOrBomDetailList);
        try {
            service.exportExcel(response, dto);
        }catch (Exception e){
            Assert.assertEquals(e.getMessage(),MessageId.PRODUCT_CODE_NOT_EXIST);
        }
    }
    @Test
    public void testGetBomInfoByBomNoList() throws Exception{
        PowerMockito.mockStatic(DatawbRemoteService.class);
        List<BProdBomHeaderDTO> mBomList = new ArrayList<>();
        List<String> productCodeList = new ArrayList<>();
        Whitebox.invokeMethod(service, "getBomInfoByBomNoList", mBomList,productCodeList);

        BProdBomHeaderDTO bProdBomHeaderDTO = new BProdBomHeaderDTO();
        bProdBomHeaderDTO.setProductCode("duct1");
        mBomList.add(bProdBomHeaderDTO);
        BProdBomHeaderDTO bProdBomHeaderDTO2 = new BProdBomHeaderDTO();
        bProdBomHeaderDTO2.setOriginalProductCode("orgDuct1");
        mBomList.add(bProdBomHeaderDTO2);
        Whitebox.invokeMethod(service, "getBomInfoByBomNoList", mBomList,productCodeList);
        productCodeList.add("duct1");
        Whitebox.invokeMethod(service, "getBomInfoByBomNoList", Collections.emptyList(),productCodeList);

        List<BaBomHeadDTO> baBomHeadDTOList = new ArrayList<>();
        BaBomHeadDTO baBomHeadDTO = new BaBomHeadDTO();
        baBomHeadDTO.setBomNo("duct1");
        baBomHeadDTO.setIsLead("1");
        baBomHeadDTOList.add(baBomHeadDTO);
        BaBomHeadDTO baBomHeadDTO2 = new BaBomHeadDTO();
        baBomHeadDTO2.setBomNo("duct2");
        baBomHeadDTOList.add(baBomHeadDTO2);
        List<SysLookupTypesDTO> valuesList = new ArrayList<>();
        SysLookupTypesDTO typesDTO = new SysLookupTypesDTO();
        SysLookupTypesDTO typesDTO1 = new SysLookupTypesDTO();
        typesDTO.setLookupMeaning("1");
        typesDTO.setDescriptionChinV("1");
        typesDTO1.setLookupMeaning("2");
        typesDTO1.setDescriptionChinV("2");
        valuesList.add(typesDTO);
        valuesList.add(typesDTO1);
        PowerMockito.when(sysLookupTypesRepository.getList(any())).thenReturn(valuesList);
        PowerMockito.when(DatawbRemoteService.queryBomInfoByBomNoList(any())).thenReturn(baBomHeadDTOList);

        Whitebox.invokeMethod(service, "getBomInfoByBomNoList", mBomList,productCodeList);
        Assert.assertEquals(productCodeList.size(),1);
    }
}
