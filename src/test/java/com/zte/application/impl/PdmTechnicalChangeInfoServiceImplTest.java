package com.zte.application.impl;

import com.zte.application.IMESLogService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.kafka.producer.PdmTechnicalChangeInfoProducer;
import com.zte.application.sncabind.PsTaskService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.domain.model.PdmTechnicalChangeInfoRepository;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.domain.model.TechnicalSummaryInfoRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanScheduleRemoteService;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.util.BaseTestCase;
import junit.framework.TestCase;
import org.apache.commons.math3.analysis.function.Pow;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.regex.Matcher;

import static org.mockito.ArgumentMatchers.any;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpClientUtil.class, CommonUtils.class, RedisHelper.class,
        BasicsettingRemoteService.class, ConstantInterface.class, DatawbRemoteService.class})
public class PdmTechnicalChangeInfoServiceImplTest extends BaseTestCase {

    @InjectMocks
    private PdmTechnicalChangeInfoServiceImpl pdmTechnicalChangeInfoService;
    @Mock
    private PdmTechnicalChangeInfoRepository pdmTechnicalChangeInforepository;

    @Mock
    LocaleMessageSourceBean lmb;
    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Mock
    private PsTaskService psTaskService;
    @Mock
    private IMESLogService iMESLogService;
    @Mock
    private PdmTechnicalChangeInfoProducer pdmTechnicalChangeInfoProducer;
    @Mock
    private SysLookupTypesRepository sysLookupTypesRepository;
    @Mock
    private PlanScheduleRemoteService planScheduleRemoteService;

    @Mock
    SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private DatawbRemoteService datawbRemoteService;

    @Mock
    private TechnicalSummaryInfoRepository technicalSummaryInfoRepository;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> redisOpsValue;
    @Mock
    private Matcher matcher;

    @Test
    public void testSendEmail() throws Exception {
        Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "sendEmail", "张三","123","123","55");
        String updateBy = "张三1234";
        PowerMockito.when(matcher.group()).thenReturn("1234");
        Assert.assertNull(Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "sendEmail", updateBy,"123","123","55"));
    }
}