/*Started by AICoder, pid:rae96ac9a0i4f9e149380b1ae13be32689d788b3*/
package com.zte.application.impl;

import com.zte.common.utils.Constant;
import com.zte.domain.model.BProdBomChangeDetailRepository;
import com.zte.domain.model.BsBomHierarchicalDetail;
import com.zte.domain.model.BsBomHierarchicalHead;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.BsItemInfoRepository;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class CommonServiceImplTest extends BaseTestCase {

    @InjectMocks
    private CommonServiceImpl commonService;

    @Mock
    private IdGenerator idGenerator;

    @Mock
    private BProdBomChangeDetailRepository bProdBomChangeDetailRepository;
    @Mock
    private BsItemInfoRepository bsItemInfoRepository;

    private BsBomHierarchicalHead hierarchicalHead;
    private List<BsBomHierarchicalDetail> detailList;

    @Before
    public void setUp() {
        hierarchicalHead = new BsBomHierarchicalHead();
        detailList = new ArrayList<>();
        hierarchicalHead.setDetails(detailList);
        when(idGenerator.snowFlakeIdStr()).thenReturn("123456");
    }

    @Test
    public void testGeneratedProdBomHierarchical_SourceTaskBlank() {
        // Given
        String sourceTask = "";

        // When
        BsBomHierarchicalHead head = commonService.generatedProdBomHierarchical(hierarchicalHead, sourceTask);
        Assert.assertTrue(Objects.isNull(head));
    }

    @Test
    public void testGeneratedProdBomHierarchical_NoChangeRecords() {
        // Given
        String sourceTask = "validTask";
        when(bProdBomChangeDetailRepository.queryBProdBomDetailChangeList(any())).thenReturn(Collections.emptyList());

        // When
        BsBomHierarchicalHead head = commonService.generatedProdBomHierarchical(hierarchicalHead, sourceTask);
        Assert.assertTrue(Objects.isNull(head));

    }

    @Test
    public void testGeneratedProdBomHierarchical_WithChangeRecords() {
        // Given
        String sourceTask = "validTask";
        List<BProdBomChangeDetailDTO> changeList = new LinkedList<>();
        BProdBomChangeDetailDTO changeRecord = new BProdBomChangeDetailDTO();
        changeRecord.setProductCode("newProductCode");
        changeRecord.setItemCode("234");
        changeRecord.setOriginalItemCode("123");
        changeList.add(changeRecord);
        BProdBomChangeDetailDTO changeRecord2 = new BProdBomChangeDetailDTO();
        changeRecord2.setProductCode("newProductCode");
        changeRecord2.setItemCode("23456");
        changeRecord2.setOriginalItemCode("234567");
        changeList.add(changeRecord2);
        BProdBomChangeDetailDTO changeRecord3 = new BProdBomChangeDetailDTO();
        changeRecord3.setProductCode("newProductCode");
        changeRecord3.setItemCode("234569");
        changeRecord3.setOriginalItemCode("2345678");
        changeList.add(changeRecord3);
        BProdBomChangeDetailDTO changeRecord4 = new BProdBomChangeDetailDTO();
        changeRecord4.setProductCode("newProductCode");
        changeRecord4.setItemCode("234569");
        changeRecord4.setOriginalItemCode("2345679");
        changeList.add(changeRecord4);



        when(bProdBomChangeDetailRepository.queryBProdBomDetailChangeList(any()))
                .thenReturn(changeList);

        BsBomHierarchicalDetail detail = new BsBomHierarchicalDetail();
        detail.setItemNo("123");
        detail.setItemQty(new BigDecimal(1));
        detail.setDeliveryProcess("SMT-A");
        detailList.add(detail);
        // When
        BsBomHierarchicalHead head = commonService.generatedProdBomHierarchical(hierarchicalHead, sourceTask);
        Assert.assertTrue(Objects.nonNull(head));

        BsBomHierarchicalDetail detail1 = new BsBomHierarchicalDetail();
        detail1.setItemNo("123");
        detail1.setItemQty(new BigDecimal(1));
        detail1.setDeliveryProcess("SMT-B");
        detailList.add(detail1);
        BsBomHierarchicalDetail detail2 = new BsBomHierarchicalDetail();
        detail2.setItemNo("123");
        detail2.setItemQty(new BigDecimal(1));
        detail2.setDeliveryProcess("SMT配送");
        detailList.add(detail2);
        BsBomHierarchicalDetail detail3 = new BsBomHierarchicalDetail();
        detail3.setItemNo("234");
        detail3.setItemQty(new BigDecimal(1));
        detail3.setDeliveryProcess("SMT配送");
        detailList.add(detail3);
        BsBomHierarchicalDetail detail4 = new BsBomHierarchicalDetail();
        detail4.setItemNo("234");
        detail4.setItemQty(new BigDecimal(1));
        detail4.setDeliveryProcess("SMT-A");
        detailList.add(detail4);
        BsBomHierarchicalDetail detail5 = new BsBomHierarchicalDetail();
        detail5.setItemNo("2345");
        detail5.setItemQty(new BigDecimal(1));
        detail5.setDeliveryProcess("SMT-A");
        detailList.add(detail5);
        BsBomHierarchicalDetail detail6 = new BsBomHierarchicalDetail();
        detail6.setItemNo("23456");
        detail6.setItemQty(new BigDecimal(2));
        detail6.setDeliveryProcess(Constant.DIP_DELIVERY);
        detailList.add(detail6);
        BsBomHierarchicalDetail detail7 = new BsBomHierarchicalDetail();
        detail7.setItemNo("234567");
        detail7.setItemQty(new BigDecimal(2));
        detail7.setDeliveryProcess(Constant.DIP_DELIVERY);
        detailList.add(detail7);

        // 同代码多前加工
        BsBomHierarchicalDetail detail8 = new BsBomHierarchicalDetail();
        detail8.setItemNo("2345678");
        detail8.setItemQty(new BigDecimal(2));
        detail8.setTypeCode("XP");
        detail8.setTagNum("A12");
        detail8.setSortSeq(new BigDecimal(1));
        detailList.add(detail8);
        BsBomHierarchicalDetail detail9 = new BsBomHierarchicalDetail();
        detail9.setItemNo("2345678");
        detail9.setItemQty(new BigDecimal(2));
        detail9.setTypeCode("XP");
        detail9.setTagNum("A12");
        detail9.setSortSeq(new BigDecimal(2));
        detailList.add(detail9);
        BsBomHierarchicalDetail detail10 = new BsBomHierarchicalDetail();
        detail10.setItemNo("2345678");
        detail10.setItemQty(new BigDecimal(2));
        detail10.setTypeCode("XP");
        detail10.setTagNum("A17");
        detail10.setSortSeq(new BigDecimal(1));
        detailList.add(detail10);
        BsBomHierarchicalDetail detail11 = new BsBomHierarchicalDetail();
        detail11.setItemNo("2345679");
        detail11.setItemQty(new BigDecimal(2));
        detail11.setTypeCode("XP");
        detail11.setTagNum("A12");
        detail11.setSortSeq(new BigDecimal(1));
        detailList.add(detail11);
        BsBomHierarchicalDetail detail12 = new BsBomHierarchicalDetail();
        detail12.setItemNo("2345679");
        detail12.setItemQty(new BigDecimal(2));
        detail12.setTypeCode("XP");
        detail12.setTagNum("A12");
        detail12.setSortSeq(new BigDecimal(2));
        detailList.add(detail12);
        BsBomHierarchicalDetail detail13 = new BsBomHierarchicalDetail();
        detail13.setItemNo("2345679");
        detail13.setItemQty(new BigDecimal(2));
        detail13.setTypeCode("XP");
        detail13.setTagNum("A17");
        detail13.setSortSeq(new BigDecimal(1));
        detailList.add(detail13);
        BsBomHierarchicalDetail detail14 = new BsBomHierarchicalDetail();
        detail14.setItemNo("2345679");
        detail14.setItemQty(new BigDecimal(2));
        detail14.setTypeCode("XP");
        detail14.setTraceCode("XP1");
        detail14.setTagNum("A17");
        detail14.setSortSeq(new BigDecimal(2));
        detailList.add(detail14);

        List<BsItemInfo> itemInfoList = new LinkedList<>();
        BsItemInfo a1 = new BsItemInfo();
        a1.setAbcType("C");
        a1.setItemNo("234");
        itemInfoList.add(a1);
        BsItemInfo a2 = new BsItemInfo();
        a2.setItemNo("23456");
        itemInfoList.add(a2);
        BsItemInfo a3 = new BsItemInfo();
        a3.setAbcType("C");
        a3.setItemNo("234569");
        itemInfoList.add(a3);
        PowerMockito.when(bsItemInfoRepository.getBsItemInfoByItemNo(Mockito.anyList()))
                .thenReturn(itemInfoList);

        commonService.generatedProdBomHierarchical(hierarchicalHead, sourceTask);
    }
}
/*Ended by AICoder, pid:rae96ac9a0i4f9e149380b1ae13be32689d788b3*/