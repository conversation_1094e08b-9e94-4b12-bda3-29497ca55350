package com.zte.application.impl;

/**
 * {@code @description }
 *
 * <AUTHOR>
 * @date 2025/5/16 下午5:06
 */
/* Started by AICoder, pid:f7477511f7a51de147d509c4d3606a3b5121f7b3 */

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.aiop.dtems.AlarmHelper;
import com.zte.application.TradeDataLogService;
import com.zte.application.impl.helper.DeliveryFeedbackServiceHelper;
import com.zte.common.CommonUtils;
import com.zte.common.enums.LiabilityEnum;
import com.zte.common.enums.OperateTypeEnum;
import com.zte.common.enums.OrderTypeEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsDeliveryFeedbackDO;
import com.zte.domain.model.PsDeliveryFeedbackRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.domain.model.sncabind.PsTaskRepository;
import com.zte.gei.common.utils.JsonUtils;
import com.zte.interfaces.VO.OrderTaskInfoVO;
import com.zte.interfaces.VO.PsDeliveryFeedbackDownImportVO;
import com.zte.interfaces.VO.PsDeliveryFeedbackVO;
import com.zte.interfaces.VO.PsDeliveryRemarkVO;
import com.zte.interfaces.dto.B2bCallBackNewDTO;
import com.zte.interfaces.dto.PsDeliveryFeedbackAliDTO;
import com.zte.interfaces.dto.PsDeliveryFeedbackSearchDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.MESHttpHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.common.logging.LogUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.powermock.api.mockito.PowerMockito.verifyStatic;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class, ObjectUtils.class,Objects.class,StringUtils.class,EasyExcel.class,MESHttpHelper.class,
        JsonUtils.class, CollectionUtils.class, JacksonJsonConverUtil.class,BeanUtil.class,LiabilityEnum.class,
        JSON.class, AlarmHelper.class, LogUtils.class, OrderTypeEnum.class})
public class DeliveryFeedbackServiceImplTest {
    @InjectMocks
    private PsDeliveryFeedbackServiceImpl service;
    @Mock
    private PsDeliveryFeedbackRepository psDeliveryFeedbackRepository;
    @Mock
    private  DeliveryFeedbackServiceHelper serviceHelper;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private PsTaskRepository taskRepository;
    @Mock
    private TradeDataLogService tradeDataLogService;
    @Mock
    private HrmUserCenterServiceImpl hrmUserInfoService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);

        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("msg");
    }

    /* Started by AICoder, pid:v09d4h0634ree46144000bbbc04e8b51c9a84f77 */
    /**
     * 测试 save 方法，当数据校验失败时。
     */
    @Test(expected = MesBusinessException.class)
    public void testSave_ValidationFails() {
        PowerMockito.mockStatic(BeanUtil.class);
        PsDeliveryFeedbackDO record = new PsDeliveryFeedbackDO();
        record.setOrderNo("order1");

        when(serviceHelper.listAlibabaOrderByTask(any())).thenReturn(Collections.emptyMap());
        when(serviceHelper.validateData(any(PsDeliveryFeedbackDownImportVO.class), anyMap())).thenReturn("Validation failed");
        PowerMockito.when(BeanUtil.copyProperties(record, PsDeliveryFeedbackDownImportVO.class)).thenReturn(new PsDeliveryFeedbackDownImportVO());

        service.save(record);
    }

    /**
     * 测试 save 方法，当任务列表为空时。
     */
    @Test
    public void testSave_NoTasks() {
        PowerMockito.mockStatic(BeanUtil.class);
        PsDeliveryFeedbackDO record = new PsDeliveryFeedbackDO();
        record.setOrderNo("order1");
        record.setAbnormalCategory(Lists.newArrayList("aa", "bb", "cc"));

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        when(serviceHelper.listAlibabaOrderByTask(any())).thenReturn(orderTaskInfoVOMap);

        PowerMockito.when(BeanUtil.copyProperties(record, PsDeliveryFeedbackDownImportVO.class)).thenReturn(new PsDeliveryFeedbackDownImportVO());
        when(serviceHelper.validateData(any(PsDeliveryFeedbackDownImportVO.class), anyMap())).thenReturn(null);

        int result = service.save(record);
        assertEquals(0, result);
    }

    /**
     * 测试 save 方法，当任务列表不为空时。
     */
    @Test
    public void testSave_TasksExist() {
        PowerMockito.mockStatic(BeanUtil.class);
        PsDeliveryFeedbackDO record = new PsDeliveryFeedbackDO();
        record.setOrderNo("order1");
        record.setAbnormalCategory(Lists.newArrayList("aa", "bb", "cc"));

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        orderTaskInfoVOMap.put("order1", new OrderTaskInfoVO());

        when(serviceHelper.listAlibabaOrderByTask(any())).thenReturn(orderTaskInfoVOMap);

        PowerMockito.when(BeanUtil.copyProperties(record, PsDeliveryFeedbackDownImportVO.class)).thenReturn(new PsDeliveryFeedbackDownImportVO());
        when(serviceHelper.validateData(any(PsDeliveryFeedbackDownImportVO.class), anyMap())).thenReturn(null);

        PowerMockito.when(BeanUtil.copyToList(Collections.singletonList(record), OrderTaskInfoVO.class)).thenReturn(Collections.singletonList(new OrderTaskInfoVO()));

        when(serviceHelper.sendEmailOrPushAliSaveMessage(anyList(), eq(false))).thenReturn(1);

        int result = service.save(record);
        assertEquals(1, result);
    }
    /* Ended by AICoder, pid:v09d4h0634ree46144000bbbc04e8b51c9a84f77 */

    /**
     * 测试 listByPage 方法。
     */
    @Test
    public void testListByPage_ShouldReturnPageRows() throws Exception {
        PsDeliveryFeedbackSearchDTO searchDTO = new PsDeliveryFeedbackSearchDTO();
        searchDTO.setPage(1);
        searchDTO.setRows(10);

        PsDeliveryFeedbackVO deliveryFeedbackVO = new PsDeliveryFeedbackVO();
        deliveryFeedbackVO.setPushStatus("1");
        List<PsDeliveryFeedbackVO> feedbackVOS = Collections.singletonList(deliveryFeedbackVO);
        when(psDeliveryFeedbackRepository.listByPage(any())).thenReturn(feedbackVOS);
        when(hrmUserInfoService.getHrmPersonInfo(anyList())).thenReturn(Collections.emptyMap());

        Page<PsDeliveryFeedbackVO> result = service.listByPage(searchDTO);
        assertNotNull(result);
        assertEquals(1, result.getRows().size());
    }

    /**
     * 测试 listByPage 方法。
     */
    @Test
    public void testListByPage_ShouldReturnPageRows1() throws Exception {
        PsDeliveryFeedbackSearchDTO searchDTO = new PsDeliveryFeedbackSearchDTO();
        searchDTO.setPage(1);
        searchDTO.setRows(10);

        PsDeliveryFeedbackVO deliveryFeedbackVO = new PsDeliveryFeedbackVO();
        deliveryFeedbackVO.setPushStatus("1");
        deliveryFeedbackVO.setLiability("1");
        deliveryFeedbackVO.setAbnormalCategoryFirst("1");
        deliveryFeedbackVO.setAbnormalCategorySecond("2");
        deliveryFeedbackVO.setAbnormalCategoryThird("3");

        List<PsDeliveryFeedbackVO> feedbackVOS = Collections.singletonList(deliveryFeedbackVO);
        when(psDeliveryFeedbackRepository.listByPage(any())).thenReturn(feedbackVOS);
        when(hrmUserInfoService.getHrmPersonInfo(anyList())).thenReturn(Collections.emptyMap());

        PsDeliveryRemarkVO thirdDeliveryRemarkVO = new PsDeliveryRemarkVO();
        thirdDeliveryRemarkVO.setValue("3");
        PsDeliveryRemarkVO secondDeliveryRemarkVO = new PsDeliveryRemarkVO();
        secondDeliveryRemarkVO.setValue("2");
        secondDeliveryRemarkVO.setChildren(Collections.singletonList(thirdDeliveryRemarkVO));
        PsDeliveryRemarkVO firstDeliveryRemarkVO = new PsDeliveryRemarkVO();
        firstDeliveryRemarkVO.setValue("1");
        firstDeliveryRemarkVO.setChildren(Collections.singletonList(secondDeliveryRemarkVO));

        List<PsDeliveryRemarkVO> psDeliveryRemarkVOS = Collections.singletonList(firstDeliveryRemarkVO);
        when(serviceHelper.getClassNodes(anyString(), anyString(), anyString())).thenReturn(psDeliveryRemarkVOS);

        Page<PsDeliveryFeedbackVO> result = service.listByPage(searchDTO);
        assertNotNull(result);
        assertEquals(1, result.getRows().size());
    }

    /**
     * 测试 listByPage 方法。
     */
    @Test
    public void testListByPage_ShouldReturnPageRows2() throws Exception {
        PsDeliveryFeedbackSearchDTO searchDTO = new PsDeliveryFeedbackSearchDTO();
        searchDTO.setPage(1);
        searchDTO.setRows(10);

        PsDeliveryFeedbackVO deliveryFeedbackVO = new PsDeliveryFeedbackVO();
        deliveryFeedbackVO.setPushStatus("1");
        deliveryFeedbackVO.setLiability("1");
        deliveryFeedbackVO.setAbnormalCategoryFirst("1");
        deliveryFeedbackVO.setAbnormalCategorySecond("2");
        deliveryFeedbackVO.setAbnormalCategoryThird("3");

        List<PsDeliveryFeedbackVO> feedbackVOS = Collections.singletonList(deliveryFeedbackVO);
        when(psDeliveryFeedbackRepository.listByPage(any())).thenReturn(feedbackVOS);
        when(hrmUserInfoService.getHrmPersonInfo(anyList())).thenReturn(Collections.emptyMap());

        PsDeliveryRemarkVO secondDeliveryRemarkVO = new PsDeliveryRemarkVO();
        secondDeliveryRemarkVO.setValue("2");
        PsDeliveryRemarkVO firstDeliveryRemarkVO = new PsDeliveryRemarkVO();
        firstDeliveryRemarkVO.setValue("1");
        firstDeliveryRemarkVO.setChildren(Collections.singletonList(secondDeliveryRemarkVO));

        List<PsDeliveryRemarkVO> psDeliveryRemarkVOS = Collections.singletonList(firstDeliveryRemarkVO);
        when(serviceHelper.getClassNodes(anyString(), anyString(), anyString())).thenReturn(psDeliveryRemarkVOS);

        Page<PsDeliveryFeedbackVO> result = service.listByPage(searchDTO);
        assertNotNull(result);
        assertEquals(1, result.getRows().size());
    }

    /**
     * 测试 listByPage 方法。
     */
    @Test
    public void testListByPage_ShouldReturnPageRows3() throws Exception {
        PsDeliveryFeedbackSearchDTO searchDTO = new PsDeliveryFeedbackSearchDTO();
        searchDTO.setPage(1);
        searchDTO.setRows(10);

        PsDeliveryFeedbackVO deliveryFeedbackVO = new PsDeliveryFeedbackVO();
        deliveryFeedbackVO.setPushStatus("1");
        deliveryFeedbackVO.setLiability("1");
        deliveryFeedbackVO.setAbnormalCategoryFirst("1");
        deliveryFeedbackVO.setAbnormalCategorySecond("2");
        deliveryFeedbackVO.setAbnormalCategoryThird("3");

        List<PsDeliveryFeedbackVO> feedbackVOS = Collections.singletonList(deliveryFeedbackVO);
        when(psDeliveryFeedbackRepository.listByPage(any())).thenReturn(feedbackVOS);
        when(hrmUserInfoService.getHrmPersonInfo(anyList())).thenReturn(Collections.emptyMap());

        PsDeliveryRemarkVO firstDeliveryRemarkVO = new PsDeliveryRemarkVO();
        firstDeliveryRemarkVO.setValue("1");
        firstDeliveryRemarkVO.setChildren(Collections.emptyList());

        List<PsDeliveryRemarkVO> psDeliveryRemarkVOS = Collections.singletonList(firstDeliveryRemarkVO);
        when(serviceHelper.getClassNodes(anyString(), anyString(), anyString())).thenReturn(psDeliveryRemarkVOS);

        Page<PsDeliveryFeedbackVO> result = service.listByPage(searchDTO);
        assertNotNull(result);
        assertEquals(1, result.getRows().size());
    }

    /* Started by AICoder, pid:m996fe1bd7nd76a1461909a0c01fc25003937f36 */

    /**
     * 测试 validateQuery 方法，当业务场景和创建人不为空，但创建开始时间和结束时间为空时。
     */
    @Test(expected = MesBusinessException.class)
    public void testValidateQuery_BusinessSceneAndCreateByNotEmptyButDatesNull() {
        PowerMockito.mockStatic(StringUtils.class);
        PowerMockito.mockStatic(ObjectUtils.class);
        PowerMockito.mockStatic(CollectionUtils.class);

        PsDeliveryFeedbackSearchDTO dto = new PsDeliveryFeedbackSearchDTO();
        dto.setBusinessScene("scene1");
        dto.setCreateBy("user1");

        when(StringUtils.isAllBlank(anyString(), anyString())).thenReturn(false);
        when(ObjectUtils.allNull(any(), any())).thenReturn(true);

        service.validateQuery(dto);
    }

    /**
     * 测试 validateQuery 方法，当任务号为空时。
     */
    @Test
    public void testValidateQuery_TaskNoEmpty() {
        PowerMockito.mockStatic(StringUtils.class);
        PowerMockito.mockStatic(ObjectUtils.class);
        PowerMockito.mockStatic(CollectionUtils.class);

        PsDeliveryFeedbackSearchDTO dto = new PsDeliveryFeedbackSearchDTO();

        assertDoesNotThrow(() -> service.validateQuery(dto));
    }

    /**
     * 测试 validateQuery 方法，当任务号数量不超过限制时。
     */
    @Test
    public void testValidateQuery_TaskNoCountWithinLimit() {
        PowerMockito.mockStatic(StringUtils.class);
        PowerMockito.mockStatic(ObjectUtils.class);
        PowerMockito.mockStatic(CollectionUtils.class);

        PsDeliveryFeedbackSearchDTO dto = new PsDeliveryFeedbackSearchDTO();
        dto.setTaskNo("task1,task2");

        when(StringUtils.isNotBlank(anyString())).thenReturn(true);
        when(CollectionUtils.size(anyList())).thenReturn(2);

        assertDoesNotThrow(() -> service.validateQuery(dto));
        assertEquals(Arrays.asList("task1", "task2"), dto.getTaskNos());
    }

    /**
     * 测试 validateQuery 方法，当任务号数量超过限制时。
     */
    @Test(expected = MesBusinessException.class)
    public void testValidateQuery_TaskNoCountExceedsLimit() {
        PowerMockito.mockStatic(StringUtils.class);
        PowerMockito.mockStatic(ObjectUtils.class);
        PowerMockito.mockStatic(CollectionUtils.class);

        PsDeliveryFeedbackSearchDTO dto = new PsDeliveryFeedbackSearchDTO();
        dto.setTaskNo(String.join(",", Collections.nCopies(101, "task")));

        when(StringUtils.isNotBlank(anyString())).thenReturn(true);
        when(CollectionUtils.size(anyList())).thenReturn(101);

        service.validateQuery(dto);
    }
    /* Ended by AICoder, pid:m996fe1bd7nd76a1461909a0c01fc25003937f36 */

    /**
     * 测试 exportTemplate 方法。
     */
    @Test
    public void testExportTemplate_ShouldExportTemplate() throws IOException {
        HttpServletResponse response = mock(HttpServletResponse.class);

        doNothing().when(response).setContentType(anyString());
        doNothing().when(response).setCharacterEncoding(anyString());
        doNothing().when(response).setHeader(anyString(), anyString());

        try (ServletOutputStream outputStream = mock(ServletOutputStream.class)) {
            when(response.getOutputStream()).thenReturn(outputStream);
            doNothing().when(outputStream).write(any(byte[].class), anyInt(), anyInt());

            service.exportTemplate(response);
        }

        verify(response).setContentType("application/vnd.ms-excel");
        verify(response).setCharacterEncoding("utf-8");
        verify(response).setHeader(eq("Content-disposition"), anyString());
    }
    /* Ended by AICoder, pid:f7477511f7a51de147d509c4d3606a3b5121f7b3 */

    /* Started by AICoder, pid:p422d8e260i985914924086a80994e5ea1f888f3 */
    /**
     * 测试 handleB2bCallback 方法，当回调成功时。
     */
    @Test
    public void testHandleB2bCallback_Success() {
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.mockStatic(AlarmHelper.class);
        PowerMockito.mockStatic(LogUtils.class);
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setSuccess(true);
        b2bCallBackNewDTO.setCode(RetCode.SUCCESS_CODE);
        b2bCallBackNewDTO.setKeywords("task1");

        PsDeliveryFeedbackVO deliveryFeedbackVO = new PsDeliveryFeedbackVO();
        deliveryFeedbackVO.setId("1");
        deliveryFeedbackVO.setOrderNo("task1");

        when(psDeliveryFeedbackRepository.listByTask(anyList())).thenReturn(Collections.singletonList(deliveryFeedbackVO));

        assertDoesNotThrow(() -> service.handleB2bCallback(b2bCallBackNewDTO));
        verify(psDeliveryFeedbackRepository, times(1)).updateByPrimaryKeySelective(any(PsDeliveryFeedbackDO.class));
        verifyStatic(AlarmHelper.class, never());
        AlarmHelper.alarm(anyString(), anyString(), any(), anyString(), anyString());
    }

    /**
     * 测试 handleB2bCallback 方法，当回调失败时。
     */
    @Test
    public void testHandleB2bCallback_Failure() {
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.mockStatic(AlarmHelper.class);
        PowerMockito.mockStatic(LogUtils.class);
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setSuccess(false);
        b2bCallBackNewDTO.setCode("ERROR_CODE");
        b2bCallBackNewDTO.setKeywords("task1");

        PsDeliveryFeedbackVO deliveryFeedbackVO = new PsDeliveryFeedbackVO();
        deliveryFeedbackVO.setId("1");
        deliveryFeedbackVO.setOrderNo("task1");

        when(psDeliveryFeedbackRepository.listByTask(anyList())).thenReturn(Collections.singletonList(deliveryFeedbackVO));

        assertDoesNotThrow(() -> service.handleB2bCallback(b2bCallBackNewDTO));
        verify(psDeliveryFeedbackRepository, times(1)).updateByPrimaryKeySelective(any(PsDeliveryFeedbackDO.class));
    }

    /**
     * 测试 handleB2bCallback 方法，当任务号不匹配时。
     */
    @Test
    public void testHandleB2bCallback_TaskNoNotMatch() {
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setSuccess(true);
        b2bCallBackNewDTO.setCode(RetCode.SUCCESS_CODE);
        b2bCallBackNewDTO.setKeywords("task1");

        when(psDeliveryFeedbackRepository.listByTask(anyList())).thenReturn(Collections.emptyList());

        assertThrows(MesBusinessException.class, () -> service.handleB2bCallback(b2bCallBackNewDTO));
    }
    /* Ended by AICoder, pid:p422d8e260i985914924086a80994e5ea1f888f3 */

    @Test
    public void testResolveResultThrowException() {
        assertThrows(MesBusinessException.class, () -> service.resolve(null));
    }

    /* Started by AICoder, pid:a8c6bc09cb94d3e141dc0aa2c0ed7061cf22df73 */
    /**
     * 测试 getPsDeliveryFeedbackDownImportVOS 方法，当导入数据为空时。
     */
    @Test
    public void testGetPsDeliveryFeedbackDownImportVOS_ImportDataIsEmpty() {
        List<PsDeliveryFeedbackDownImportVO> importVOList = Collections.emptyList();

        assertThrows(MesBusinessException.class, () -> service.getPsDeliveryFeedbackDownImportVOS(importVOList));
    }

    /**
     * 测试 getPsDeliveryFeedbackDownImportVOS 方法，当导入数据超过限制时。
     */
    @Test
    public void testGetPsDeliveryFeedbackDownImportVOS_ImportDataExceedsLimit() {
        List<PsDeliveryFeedbackDownImportVO> importVOList = Lists.newArrayList(
                new PsDeliveryFeedbackDownImportVO(), new PsDeliveryFeedbackDownImportVO(),
                new PsDeliveryFeedbackDownImportVO(), new PsDeliveryFeedbackDownImportVO(),
                new PsDeliveryFeedbackDownImportVO(), new PsDeliveryFeedbackDownImportVO()
        );

        while (importVOList.size() < 1001) {
            importVOList.add(new PsDeliveryFeedbackDownImportVO());
        }

        assertThrows(MesBusinessException.class, () -> service.getPsDeliveryFeedbackDownImportVOS(importVOList));
    }

    /**
     * 测试 getPsDeliveryFeedbackDownImportVOS 方法，当任务号重复时。
     */
    @Test
    public void testGetPsDeliveryFeedbackDownImportVOS_TaskNoRepeats() {
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("task1");
        PsDeliveryFeedbackDownImportVO importVO1 = new PsDeliveryFeedbackDownImportVO();
        importVO1.setOrderNo("task1");
        List<PsDeliveryFeedbackDownImportVO> importVOList = Lists.newArrayList(
                importVO,
                importVO1
        );

        assertThrows(MesBusinessException.class, () -> service.getPsDeliveryFeedbackDownImportVOS(importVOList));
    }

    /**
     * 测试 getPsDeliveryFeedbackDownImportVOS 方法，当延期编号重复时。
     */
    @Test
    public void testGetPsDeliveryFeedbackDownImportVOS_abnormalNoRepeats() {
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setAbnormalNo("task1");
        PsDeliveryFeedbackDownImportVO importVO1 = new PsDeliveryFeedbackDownImportVO();
        importVO1.setAbnormalNo("task1");
        List<PsDeliveryFeedbackDownImportVO> importVOList = Lists.newArrayList(
                importVO,
                importVO1
        );

        assertThrows(MesBusinessException.class, () -> service.getPsDeliveryFeedbackDownImportVOS(importVOList));
    }

    /**
     * 测试 getPsDeliveryFeedbackDownImportVOS 方法，正常情况。
     */
    @Test
    public void testGetPsDeliveryFeedbackDownImportVOS_NormalCase() {
        PowerMockito.mockStatic(JsonUtils.class);
        PowerMockito.mockStatic(LiabilityEnum.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("task1");
        importVO.setLiability("liability1");
        PsDeliveryFeedbackDownImportVO importVO1 = new PsDeliveryFeedbackDownImportVO();
        importVO1.setOrderNo("task2");
        importVO1.setLiability("liability2");

        List<PsDeliveryFeedbackDownImportVO> importVOList = Arrays.asList(
                importVO,
                importVO1
        );

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = Collections.emptyMap();
        when(serviceHelper.listAlibabaOrderByTask(any())).thenReturn(orderTaskInfoVOMap);
        when(serviceHelper.validateData(any(PsDeliveryFeedbackDownImportVO.class), anyMap())).thenReturn(null);
        when(LiabilityEnum.getNameFromCode(anyString())).thenReturn("LiabilityName");

        List<PsDeliveryFeedbackDownImportVO> result = service.getPsDeliveryFeedbackDownImportVOS(importVOList);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("liability1", result.get(0).getLiabilityName());
        assertEquals("liability2", result.get(1).getLiabilityName());
    }
    /* Ended by AICoder, pid:a8c6bc09cb94d3e141dc0aa2c0ed7061cf22df73 */

    @Test(expected = MesBusinessException.class)
    public void testEmptyListThrowsException() {
        List<PsDeliveryFeedbackDownImportVO> input = new ArrayList<>();
        try {
            service.getPsDeliveryFeedbackDownImportVOS(input);
        } catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
            assertEquals(MessageId.FILE_PARSING_ERROR, e.getExMsgId());
            throw e;
        }
    }

    @Test(expected = MesBusinessException.class)
    public void testSizeOverLimitThrowsException() {
        List<PsDeliveryFeedbackDownImportVO> input = new ArrayList<>();
        for (int i = 0; i < 1001; i++) {
            PsDeliveryFeedbackDownImportVO vo = new PsDeliveryFeedbackDownImportVO();
            vo.setOrderNo("ORDER" + i);
            input.add(vo);
        }
        try {
            service.getPsDeliveryFeedbackDownImportVOS(input);
        } catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
            assertEquals(MessageId.SN_DATA_SIZE_OVER, e.getExMsgId());
            throw e;
        }
    }

    @Test(expected = MesBusinessException.class)
    public void testDuplicateOrderNoThrowsException() {
        List<PsDeliveryFeedbackDownImportVO> input = new ArrayList<>();
        PsDeliveryFeedbackDownImportVO vo1 = new PsDeliveryFeedbackDownImportVO();
        vo1.setOrderNo("ORDER1");
        vo1.setAbnormalNo("aa");
        PsDeliveryFeedbackDownImportVO vo2 = new PsDeliveryFeedbackDownImportVO();
        vo2.setOrderNo("ORDER1");
        vo2.setAbnormalNo("bb");

        input.add(vo1);
        input.add(vo2);

        try {
            service.getPsDeliveryFeedbackDownImportVOS(input);
        } catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
            assertEquals(MessageId.TASK_NO_REPEAT, e.getExMsgId());
            throw e;
        }
    }

    /* Started by AICoder, pid:kc128k6217n1b08143200857b010395887171943 */
    /**
     * 测试 batchSubmit 方法，当数据校验失败时。
     */
    @Test(expected = MesBusinessException.class)
    public void testBatchSubmit_ValidationFails() {
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");

        List<PsDeliveryFeedbackDownImportVO> importVOList = Collections.singletonList(importVO);

        when(serviceHelper.listAlibabaOrderByTask(any())).thenReturn(Collections.emptyMap());
        when(serviceHelper.validateData(any(PsDeliveryFeedbackDownImportVO.class), anyMap())).thenReturn("Validation failed");

        service.batchSubmit(importVOList);
    }

    /**
     * 测试 batchSubmit 方法，当任务列表为空时。
     */
    @Test
    public void testBatchSubmit_NoTasks() {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setLiability("1");
        importVO.setAbnormalCategoryFirst("1");
        importVO.setAbnormalCategorySecond("2");
        importVO.setAbnormalCategoryThird("3");

        List<PsDeliveryFeedbackDownImportVO> importVOList = Collections.singletonList(importVO);

        when(serviceHelper.listAlibabaOrderByTask(any())).thenReturn(Collections.emptyMap());
        when(serviceHelper.validateData(any(PsDeliveryFeedbackDownImportVO.class), anyMap())).thenReturn(null);

        PsDeliveryRemarkVO thirdDeliveryRemarkVO = new PsDeliveryRemarkVO();
        thirdDeliveryRemarkVO.setValue("3");
        PsDeliveryRemarkVO secondDeliveryRemarkVO = new PsDeliveryRemarkVO();
        secondDeliveryRemarkVO.setValue("2");
        secondDeliveryRemarkVO.setChildren(Collections.singletonList(thirdDeliveryRemarkVO));
        PsDeliveryRemarkVO firstDeliveryRemarkVO = new PsDeliveryRemarkVO();
        firstDeliveryRemarkVO.setValue("1");
        firstDeliveryRemarkVO.setChildren(Collections.singletonList(secondDeliveryRemarkVO));

        List<PsDeliveryRemarkVO> psDeliveryRemarkVOS = Collections.singletonList(firstDeliveryRemarkVO);
        when(serviceHelper.getClassNodes(anyString(), anyString(), anyString())).thenReturn(psDeliveryRemarkVOS);

        int result = service.batchSubmit(importVOList);
        assertEquals(0, result);
    }

    /**
     * 测试 batchSubmit 方法，当任务列表为空时。
     */
    @Test
    public void testBatchSubmit_NoTasks1() {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");

        List<PsDeliveryFeedbackDownImportVO> importVOList = Collections.emptyList();

        when(serviceHelper.listAlibabaOrderByTask(any())).thenReturn(Collections.emptyMap());
        when(serviceHelper.validateData(any(PsDeliveryFeedbackDownImportVO.class), anyMap())).thenReturn(null);

        int result = service.batchSubmit(importVOList);
        assertEquals(0, result);
    }

    /**
     * 测试 batchSubmit 方法，当任务列表不为空时。
     */
    @Test
    public void testBatchSubmit_TasksExist() {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setLiability("1");
        importVO.setAbnormalCategoryFirst("1");
        importVO.setAbnormalCategorySecond("2");
        importVO.setAbnormalCategoryThird("3");

        List<PsDeliveryFeedbackDownImportVO> importVOList = Collections.singletonList(importVO);

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = Collections.singletonMap("order1", new OrderTaskInfoVO());

        when(serviceHelper.listAlibabaOrderByTask(any())).thenReturn(orderTaskInfoVOMap);
        when(serviceHelper.validateData(any(PsDeliveryFeedbackDownImportVO.class), anyMap())).thenReturn(null);

        when(serviceHelper.sendEmailOrPushAliSaveMessage(anyList(), eq(false))).thenReturn(1);


        PsDeliveryRemarkVO thirdDeliveryRemarkVO = new PsDeliveryRemarkVO();
        thirdDeliveryRemarkVO.setLabel("3");
        PsDeliveryRemarkVO secondDeliveryRemarkVO = new PsDeliveryRemarkVO();
        secondDeliveryRemarkVO.setLabel("2");
        secondDeliveryRemarkVO.setChildren(Collections.singletonList(thirdDeliveryRemarkVO));
        PsDeliveryRemarkVO firstDeliveryRemarkVO = new PsDeliveryRemarkVO();
        firstDeliveryRemarkVO.setLabel("1");
        firstDeliveryRemarkVO.setChildren(Collections.singletonList(secondDeliveryRemarkVO));

        List<PsDeliveryRemarkVO> psDeliveryRemarkVOS = Collections.singletonList(firstDeliveryRemarkVO);
        when(serviceHelper.getClassNodes(anyString(), anyString(), anyString())).thenReturn(psDeliveryRemarkVOS);

        int result = service.batchSubmit(importVOList);
        assertEquals(1, result);
    }

    /**
     * 测试 batchSubmit 方法，当任务列表不为空时。
     */
    @Test
    public void testBatchSubmit_TasksExist1() {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setLiability("1");
        importVO.setAbnormalCategoryFirst("11");
        importVO.setAbnormalCategorySecond("2");
        importVO.setAbnormalCategoryThird("3");

        List<PsDeliveryFeedbackDownImportVO> importVOList = Collections.singletonList(importVO);

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = Collections.singletonMap("order1", new OrderTaskInfoVO());

        when(serviceHelper.listAlibabaOrderByTask(any())).thenReturn(orderTaskInfoVOMap);
        when(serviceHelper.validateData(any(PsDeliveryFeedbackDownImportVO.class), anyMap())).thenReturn(null);

        when(serviceHelper.sendEmailOrPushAliSaveMessage(anyList(), eq(false))).thenReturn(1);


        PsDeliveryRemarkVO thirdDeliveryRemarkVO = new PsDeliveryRemarkVO();
        thirdDeliveryRemarkVO.setLabel("3");
        PsDeliveryRemarkVO secondDeliveryRemarkVO = new PsDeliveryRemarkVO();
        secondDeliveryRemarkVO.setLabel("2");
        secondDeliveryRemarkVO.setChildren(Collections.singletonList(thirdDeliveryRemarkVO));
        PsDeliveryRemarkVO firstDeliveryRemarkVO = new PsDeliveryRemarkVO();
        firstDeliveryRemarkVO.setLabel("1");
        firstDeliveryRemarkVO.setChildren(Collections.singletonList(secondDeliveryRemarkVO));

        List<PsDeliveryRemarkVO> psDeliveryRemarkVOS = Collections.singletonList(firstDeliveryRemarkVO);
        when(serviceHelper.getClassNodes(anyString(), anyString(), anyString())).thenReturn(psDeliveryRemarkVOS);

        int result = service.batchSubmit(importVOList);
        assertEquals(1, result);
    }

    /**
     * 测试 batchSubmit 方法，当任务列表不为空时。
     */
    @Test
    public void testBatchSubmit_TasksExist2() {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setLiability("1");
        importVO.setAbnormalCategoryFirst("1");
        importVO.setAbnormalCategorySecond("22");
        importVO.setAbnormalCategoryThird("3");

        List<PsDeliveryFeedbackDownImportVO> importVOList = Collections.singletonList(importVO);

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = Collections.singletonMap("order1", new OrderTaskInfoVO());

        when(serviceHelper.listAlibabaOrderByTask(any())).thenReturn(orderTaskInfoVOMap);
        when(serviceHelper.validateData(any(PsDeliveryFeedbackDownImportVO.class), anyMap())).thenReturn(null);

        when(serviceHelper.sendEmailOrPushAliSaveMessage(anyList(), eq(false))).thenReturn(1);


        PsDeliveryRemarkVO thirdDeliveryRemarkVO = new PsDeliveryRemarkVO();
        thirdDeliveryRemarkVO.setLabel("3");
        PsDeliveryRemarkVO secondDeliveryRemarkVO = new PsDeliveryRemarkVO();
        secondDeliveryRemarkVO.setLabel("2");
        secondDeliveryRemarkVO.setChildren(Collections.singletonList(thirdDeliveryRemarkVO));
        PsDeliveryRemarkVO firstDeliveryRemarkVO = new PsDeliveryRemarkVO();
        firstDeliveryRemarkVO.setLabel("1");
        firstDeliveryRemarkVO.setChildren(Collections.singletonList(secondDeliveryRemarkVO));

        List<PsDeliveryRemarkVO> psDeliveryRemarkVOS = Collections.singletonList(firstDeliveryRemarkVO);
        when(serviceHelper.getClassNodes(anyString(), anyString(), anyString())).thenReturn(psDeliveryRemarkVOS);

        int result = service.batchSubmit(importVOList);
        assertEquals(1, result);
    }
    /**
     * 测试 batchSubmit 方法，当任务列表不为空时。
     */
    @Test
    public void testBatchSubmit_TasksExist3() {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setLiability("1");
        importVO.setAbnormalCategoryFirst("1");
        importVO.setAbnormalCategorySecond("2");
        importVO.setAbnormalCategoryThird("33");

        List<PsDeliveryFeedbackDownImportVO> importVOList = Collections.singletonList(importVO);

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = Collections.singletonMap("order1", new OrderTaskInfoVO());

        when(serviceHelper.listAlibabaOrderByTask(any())).thenReturn(orderTaskInfoVOMap);
        when(serviceHelper.validateData(any(PsDeliveryFeedbackDownImportVO.class), anyMap())).thenReturn(null);

        when(serviceHelper.sendEmailOrPushAliSaveMessage(anyList(), eq(false))).thenReturn(1);


        PsDeliveryRemarkVO thirdDeliveryRemarkVO = new PsDeliveryRemarkVO();
        thirdDeliveryRemarkVO.setLabel("3");
        PsDeliveryRemarkVO secondDeliveryRemarkVO = new PsDeliveryRemarkVO();
        secondDeliveryRemarkVO.setLabel("2");
        secondDeliveryRemarkVO.setChildren(Collections.singletonList(thirdDeliveryRemarkVO));
        PsDeliveryRemarkVO firstDeliveryRemarkVO = new PsDeliveryRemarkVO();
        firstDeliveryRemarkVO.setLabel("1");
        firstDeliveryRemarkVO.setChildren(Collections.singletonList(secondDeliveryRemarkVO));

        List<PsDeliveryRemarkVO> psDeliveryRemarkVOS = Collections.singletonList(firstDeliveryRemarkVO);
        when(serviceHelper.getClassNodes(anyString(), anyString(), anyString())).thenReturn(psDeliveryRemarkVOS);

        int result = service.batchSubmit(importVOList);
        assertEquals(1, result);
    }
    /* Ended by AICoder, pid:kc128k6217n1b08143200857b010395887171943 */

    /* Started by AICoder, pid:m9662c9f87na7f1140df0861a066e153720194c2 */
    /**
     * 测试 schedule 方法，当没有阿里任务时。
     */
    @Test
    public void testSchedule_NoAlibabaTasks() {
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("ALI_CUSTOMER");
        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(Collections.singletonList(sysLookupValues));
        when(taskRepository.listAlibabaTask(any(PsDeliveryFeedbackAliDTO.class))).thenReturn(Collections.emptyList());

        assertDoesNotThrow(() -> service.schedule());
        verify(serviceHelper, never()).sendEmailOrPushAliSaveMessage(anyList(), anyBoolean());
    }

    /**
     * 测试 schedule 方法，当没有最终的阿里任务时。
     */
    @Test
    public void testSchedule_NoFinalAlibabaTasks() {
        OrderTaskInfoVO task1 = new OrderTaskInfoVO();
        task1.setLastUpdatedDate(new Date());
        task1.setOrderNo("task1");

        List<OrderTaskInfoVO> alibabaTasks = Lists.newArrayList(task1);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("ALI_CUSTOMER");
        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(Collections.singletonList(sysLookupValues));
        when(taskRepository.listAlibabaTask(any(PsDeliveryFeedbackAliDTO.class))).thenReturn(alibabaTasks);
        when(psDeliveryFeedbackRepository.listTaskNosOfScheduleExclude(anyList())).thenReturn(Lists.newArrayList("task1"));

        assertDoesNotThrow(() -> service.schedule());
        verify(serviceHelper, never()).sendEmailOrPushAliSaveMessage(anyList(), anyBoolean());
    }

    /**
     * 测试 schedule 方法，当有最终的阿里任务时。
     */
    @Test
    public void testSchedule_WithAlibabaTasks() {
        List<OrderTaskInfoVO> alibabaTasks = Lists.newArrayList();
        for (int i = 0; i < 12; i++) {
            OrderTaskInfoVO task1 = new OrderTaskInfoVO();
            task1.setLastUpdatedDate(new Date());
            task1.setTaskId("task1");
            task1.setOrderNo("taskNo");
            alibabaTasks.add(task1);
        }

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("ALI_CUSTOMER");
        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(Collections.singletonList(sysLookupValues));
        when(taskRepository.listAlibabaTask(any(PsDeliveryFeedbackAliDTO.class))).thenReturn(alibabaTasks, Collections.emptyList());

        PsDeliveryFeedbackVO psDeliveryFeedbackVO = new PsDeliveryFeedbackVO();
        psDeliveryFeedbackVO.setOrderNo("taskNo");
        when(psDeliveryFeedbackRepository.listByTask(anyList())).thenReturn(Collections.singletonList(psDeliveryFeedbackVO));

        assertDoesNotThrow(() -> service.schedule());
        verify(serviceHelper, times(1)).sendEmailOrPushAliSaveMessage(eq(alibabaTasks), eq(true));
    }

    /**
     * 测试 schedule 方法，当有最终的阿里任务时。
     */
    @Test
    public void testSchedule_WithAlibabaTasks1() {
        List<OrderTaskInfoVO> alibabaTasks = Lists.newArrayList();
        for (int i = 0; i < 12; i++) {
            OrderTaskInfoVO task1 = new OrderTaskInfoVO();
            task1.setLastUpdatedDate(new Date());
            task1.setTaskId("task1");
            task1.setOrderNo("taskNo");
            task1.setDateEstimatedCompletion(new Date());
            task1.setDateMaterialEstimatedPrepared(new Date());
            task1.setDateAllMaterialEstimatedPrepared(new Date());
            task1.setDateScheduledProduction(new Date());
            alibabaTasks.add(task1);
        }

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("ALI_CUSTOMER");
        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(Collections.singletonList(sysLookupValues));
        when(taskRepository.listAlibabaTask(any(PsDeliveryFeedbackAliDTO.class))).thenReturn(alibabaTasks, Collections.emptyList());

        PsDeliveryFeedbackVO psDeliveryFeedbackVO = new PsDeliveryFeedbackVO();
        psDeliveryFeedbackVO.setOrderNo("taskNo");
        psDeliveryFeedbackVO.setOperateType(OperateTypeEnum.SCHEDULED_TASK.getCode());
        when(psDeliveryFeedbackRepository.listByTask(anyList())).thenReturn(Collections.singletonList(psDeliveryFeedbackVO));

        assertDoesNotThrow(() -> service.schedule());
        verify(serviceHelper, times(1)).sendEmailOrPushAliSaveMessage(eq(alibabaTasks), eq(true));
    }

    /**
     * 测试 schedule 方法，当有最终的阿里任务时。
     */
    @Test
    public void testSchedule_WithAlibabaTasks2() {
        List<OrderTaskInfoVO> alibabaTasks = Lists.newArrayList();
        for (int i = 0; i < 12; i++) {
            OrderTaskInfoVO task1 = new OrderTaskInfoVO();
            task1.setLastUpdatedDate(new Date());
            task1.setTaskId("task1");
            task1.setOrderNo("taskNo");
            alibabaTasks.add(task1);
        }

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("ALI_CUSTOMER");
        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(Collections.singletonList(sysLookupValues));
        when(taskRepository.listAlibabaTask(any(PsDeliveryFeedbackAliDTO.class))).thenReturn(alibabaTasks, Collections.emptyList());

        PsDeliveryFeedbackVO psDeliveryFeedbackVO = new PsDeliveryFeedbackVO();
        psDeliveryFeedbackVO.setOrderNo("taskNo");
        psDeliveryFeedbackVO.setOperateType(OperateTypeEnum.SCHEDULED_TASK.getCode());
        when(psDeliveryFeedbackRepository.listByTask(anyList())).thenReturn(Collections.singletonList(psDeliveryFeedbackVO));

        assertDoesNotThrow(() -> service.schedule());
        verify(serviceHelper, times(1)).sendEmailOrPushAliSaveMessage(eq(alibabaTasks), eq(true));
    }
    /* Ended by AICoder, pid:m9662c9f87na7f1140df0861a066e153720194c2 */

    /* Started by AICoder, pid:j513ch49919a44e14d4e081d10f0ba5c49f52156 */
    /**
     * 测试 queryExportData 方法，正常情况。
     */
    @Test
    public void testQueryExportData_NormalCase() {
        PowerMockito.mockStatic(OrderTypeEnum.class);
        PowerMockito.mockStatic(LiabilityEnum.class);
        PsDeliveryFeedbackSearchDTO searchDTO = new PsDeliveryFeedbackSearchDTO();
        searchDTO.setBusinessScene("scene1");
        searchDTO.setCreateBy("user1");
        searchDTO.setCreateStartDate(new Date());

        int pageNo = 1;
        int pageSize = 10;

        Page<PsDeliveryFeedbackVO> page = new Page<>(pageNo, pageSize);
        page.setParams(searchDTO);

        PsDeliveryFeedbackVO vo1 = new PsDeliveryFeedbackVO();
        vo1.setOrderType("orderType1");
        vo1.setLiability("liability1");
        vo1.setPushStatus("1");

        List<PsDeliveryFeedbackVO> psDeliveryFeedbackVOS = Arrays.asList(vo1);

        when(psDeliveryFeedbackRepository.listByPage(any(Page.class))).thenReturn(psDeliveryFeedbackVOS);
        when(OrderTypeEnum.getNameFromCode("orderType1")).thenReturn("OrderTypeName");
        when(LiabilityEnum.getNameFromCode("liability1")).thenReturn("LiabilityName");

        List<PsDeliveryFeedbackVO> result = service.queryExportData(searchDTO, pageNo, pageSize);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("OrderTypeName", result.get(0).getOrderTypeName());
        assertEquals("LiabilityName", result.get(0).getLiabilityName());
    }

    /**
     * 测试 queryExportData 方法，当查询结果为空时。
     */
    @Test
    public void testQueryExportData_EmptyResult() {
        PowerMockito.mockStatic(OrderTypeEnum.class);
        PowerMockito.mockStatic(LiabilityEnum.class);
        PsDeliveryFeedbackSearchDTO searchDTO = new PsDeliveryFeedbackSearchDTO();
        searchDTO.setBusinessScene("scene1");
        searchDTO.setCreateBy("user1");
        searchDTO.setCreateStartDate(new Date());

        int pageNo = 1;
        int pageSize = 10;

        Page<PsDeliveryFeedbackVO> page = new Page<>(pageNo, pageSize);
        page.setParams(searchDTO);

        when(psDeliveryFeedbackRepository.listByPage(any(Page.class))).thenReturn(Collections.emptyList());

        List<PsDeliveryFeedbackVO> result = service.queryExportData(searchDTO, pageNo, pageSize);

        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
    /* Ended by AICoder, pid:j513ch49919a44e14d4e081d10f0ba5c49f52156 */

    /* Started by AICoder, pid:nb05b1319bq15a4147b70add3068a83e8941c43a */
    /**
     * 测试 countExportTotal 方法，正常情况。
     */
    @Test
    public void testCountExportTotal_NormalCase() {
        PsDeliveryFeedbackSearchDTO searchDTO = new PsDeliveryFeedbackSearchDTO();
        searchDTO.setBusinessScene("scene1");
        searchDTO.setCreateBy("user1");
        searchDTO.setCreateStartDate(new Date());

        when(psDeliveryFeedbackRepository.countSparePartAllocationQuery(any(PsDeliveryFeedbackSearchDTO.class))).thenReturn(10);

        Integer result = service.countExportTotal(searchDTO);

        assertEquals(Integer.valueOf(10), result);
        verify(psDeliveryFeedbackRepository, times(1)).countSparePartAllocationQuery(any(PsDeliveryFeedbackSearchDTO.class));
    }

    /**
     * 测试 countExportTotal 方法，当查询结果为空时。
     */
    @Test
    public void testCountExportTotal_EmptyResult() {
        PsDeliveryFeedbackSearchDTO searchDTO = new PsDeliveryFeedbackSearchDTO();
        searchDTO.setBusinessScene("scene1");
        searchDTO.setCreateBy("user1");
        searchDTO.setCreateStartDate(new Date());

        when(psDeliveryFeedbackRepository.countSparePartAllocationQuery(any(PsDeliveryFeedbackSearchDTO.class))).thenReturn(0);

        Integer result = service.countExportTotal(searchDTO);

        assertEquals(Integer.valueOf(0), result);
        verify(psDeliveryFeedbackRepository, times(1)).countSparePartAllocationQuery(any(PsDeliveryFeedbackSearchDTO.class));
    }
    /* Ended by AICoder, pid:nb05b1319bq15a4147b70add3068a83e8941c43a */

    /* Started by AICoder, pid:e43cfmcc21755e6148da09a8502d85303911dc9c */
    @Test
    public void testSearchRemark_CommandLiability() {
        /** 测试当liability为COMMAND时的情况 */
        when(serviceHelper.getClassNodes(anyString(), anyString(), anyString()))
                .thenReturn(Collections.singletonList(new PsDeliveryRemarkVO()));

        List<PsDeliveryRemarkVO> result = service.searchRemark(LiabilityEnum.COMMAND.getCode());

        assertEquals(1, result.size());
        verify(serviceHelper).getClassNodes(Constant.LOOK_UP_CODE_2025060301, Constant.LOOK_UP_CODE_2025060302, Constant.LOOK_UP_CODE_2025060303);
    }

    @Test
    public void testSearchRemark_WorkOrderLiability() {
        /** 测试当liability为WORK_ORDER时的情况 */
        when(serviceHelper.getClassNodes(anyString(), anyString(), anyString()))
                .thenReturn(Collections.singletonList(new PsDeliveryRemarkVO()));

        List<PsDeliveryRemarkVO> result = service.searchRemark(LiabilityEnum.WORK_ORDER.getCode());

        assertEquals(1, result.size());
        verify(serviceHelper).getClassNodes(Constant.LOOK_UP_CODE_2025060311, Constant.LOOK_UP_CODE_2025060312, Constant.LOOK_UP_CODE_2025060313);
    }

    @Test
    public void testSearchRemark_UnknownLiability() {
        /** 测试当liability为未知值时的情况 */
        List<PsDeliveryRemarkVO> result = service.searchRemark("UNKNOWN");

        assertEquals(0, result.size());
    }
    /* Ended by AICoder, pid:e43cfmcc21755e6148da09a8502d85303911dc9c */
}

