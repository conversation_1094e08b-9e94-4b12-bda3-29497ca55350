package com.zte.application.impl;

import com.zte.application.OfflineExportService;
import com.zte.domain.model.OfflineExport;
import com.zte.domain.model.SpRecovery;
import com.zte.domain.model.SpRecoveryRepository;
import com.zte.interfaces.dto.SpRecoveryPageQueryDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.common.Constants;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/10/19 20:53
 * @Description
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({SpRecoveryServiceImpl.class, FileUtils.class})
public class SpRecoveryServiceImplTest extends BaseTestCase {

    @InjectMocks
    private SpRecoveryServiceImpl service;
    @Mock
    private SpRecoveryRepository repository;
    @Mock
    private RedisLock redisLock;
    @Mock
    private OfflineExportService offlineExportService;
    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Test
    public void queryPage() {
        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(1L);
        PowerMockito.when(repository.queryPage(Mockito.any())).thenReturn(new ArrayList<SpRecovery>());
        PageRows<SpRecovery> pageRows = service.queryPage(new SpRecoveryPageQueryDTO());
        Assert.assertTrue(pageRows.getRows().size() >= 0);
    }

    @Test
    public void export() throws Exception {
        ArrayList<SpRecovery> list = new ArrayList();
        SpRecovery spRecovery = new SpRecovery();
        spRecovery.setResourceType("mac");
        list.add(spRecovery);

        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(1L);
        PowerMockito.when(repository.queryPage(Mockito.any())).thenReturn(list);

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);

        PowerMockito.doNothing().when(offlineExportService).add(Mockito.any());
        try {
            Map<String, String> header = new HashMap<>();
            header.put(Constants.HTTP_HEADER_X_REAL_EMP_NO.toLowerCase(), "1111");
            PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);

            HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
            service.export(response, new SpRecoveryPageQueryDTO());
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void export2() throws Exception {
        ArrayList<SpRecovery> list = new ArrayList();
        SpRecovery spRecovery = new SpRecovery();
        spRecovery.setResourceType("mac");
        list.add(spRecovery);

        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(0L);
        try {
            HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
            service.export(response, new SpRecoveryPageQueryDTO());
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(1L);
        PowerMockito.when(repository.queryPage(Mockito.any())).thenReturn(list);

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);

        PowerMockito.doNothing().when(offlineExportService).add(Mockito.any());
        try {
            HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
            service.export(response, new SpRecoveryPageQueryDTO());
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void epxortThread() throws Exception {
        ArrayList<SpRecovery> list = new ArrayList();
        SpRecovery spRecovery = new SpRecovery();
        spRecovery.setResourceType("mac");
        list.add(spRecovery);

        PowerMockito.mockStatic(FileUtils.class);
        PowerMockito.when(FileUtils.createFilePathAndCheck(Mockito.any())).thenReturn("/usr/xx.xlsx");

        PowerMockito.doNothing().when(offlineExportService).updateById(Mockito.any());
        PowerMockito.doReturn(list).when(repository).queryPage(Mockito.any());
        PowerMockito.doReturn("").when(cloudDiskHelper).fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt());
        try {
            Whitebox.invokeMethod(service, "exportThread", new SpRecoveryPageQueryDTO(), 10L, "10", redisLock, new OfflineExport());
        }catch (Exception e){
            Assert.assertEquals(RetCode.SERVERERROR_CODE, e.getMessage());
        }
    }

    @Test
    public void exportThread2() throws Exception {
        PowerMockito.mockStatic(FileUtils.class);
        PowerMockito.when(FileUtils.createFilePathAndCheck(Mockito.any())).thenReturn("/usr/xx.xlsx");

        PowerMockito.doNothing().when(offlineExportService).updateById(Mockito.any());
        PowerMockito.doThrow(new MesBusinessException("", "")).when(repository).queryPage(Mockito.any());
        PowerMockito.doReturn("").when(cloudDiskHelper).fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt());
        try {
            Whitebox.invokeMethod(service, "exportThread", new SpRecoveryPageQueryDTO(), 10L, "10", redisLock, new OfflineExport());
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

    }
}
