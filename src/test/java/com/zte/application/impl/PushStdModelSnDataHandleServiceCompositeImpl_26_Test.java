/*Started by AICoder, pid:i9f4bb19351334b14cc7095e10ec8c357f24cb79*/
package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.application.PushStdModelSnDataHandleService;
import com.zte.interfaces.dto.PushStdModelSnDataHandleDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
public class PushStdModelSnDataHandleServiceCompositeImpl_26_Test {

    @Mock
    private List<PushStdModelSnDataHandleService> pushStdModelSnDataHandleServices;

    @InjectMocks
    private PushStdModelSnDataHandleServiceCompositeImpl pushStdModelSnDataHandleServiceComposite;

    @Test
    public void testMatch_AlwaysReturnsFalse() {
        // Given
        PushStdModelSnDataHandleDTO dto = new PushStdModelSnDataHandleDTO();

        // When
        boolean result = pushStdModelSnDataHandleServiceComposite.match(null);

        // Then
        assertFalse(result);
    }

    /*Started by AICoder, pid:i3d6b5ab16703b9144e1085350b7ca87f4f398d9*/
    @Test
    public void testHandlePushStdModelSnData_SecondMatchHandlesFalse() {
        PushStdModelSnDataHandleDTO dto = new PushStdModelSnDataHandleDTO();

        // Mocking match and handle methods for the second service
        when(pushStdModelSnDataHandleServices.get(0).match(any())).thenReturn(false);
        when(pushStdModelSnDataHandleServices.get(1).match(any())).thenReturn(true);
        when(pushStdModelSnDataHandleServices.get(1).handlePushStdModelSnData(any())).thenReturn(false);

        boolean result = pushStdModelSnDataHandleServiceComposite.handlePushStdModelSnData(dto);

        assertFalse(result);
        verify(pushStdModelSnDataHandleServices.get(1), times(1)).handlePushStdModelSnData(dto);
    }

    @Test
    public void testHandlePushStdModelSnData_NoMatch() {
        PushStdModelSnDataHandleDTO dto = new PushStdModelSnDataHandleDTO();
        
        // Mocking match method to return false for both services
        when(pushStdModelSnDataHandleServices.get(0).match(any())).thenReturn(false);
        when(pushStdModelSnDataHandleServices.get(1).match(any())).thenReturn(false);

        boolean result = pushStdModelSnDataHandleServiceComposite.handlePushStdModelSnData(dto);

        assertFalse(result);
    }

    @Test
    public void testHandlePushStdModelSnData_FirstMatchHandlesTrue() {
        PushStdModelSnDataHandleDTO dto = new PushStdModelSnDataHandleDTO();

        // Mocking match and handle methods for the first service
        when(pushStdModelSnDataHandleServices.get(0).match(any())).thenReturn(true);
        when(pushStdModelSnDataHandleServices.get(0).handlePushStdModelSnData(any())).thenReturn(true);

        boolean result = pushStdModelSnDataHandleServiceComposite.handlePushStdModelSnData(dto);

        assertTrue(result);
        verify(pushStdModelSnDataHandleServices.get(0), times(1)).handlePushStdModelSnData(dto);
    }

    @Before
    public void setUp() {
        // Initialize the list of services with mocks
        PushStdModelSnDataHandleService service1 = mock(PushStdModelSnDataHandleService.class);
        PushStdModelSnDataHandleService service2 = mock(PushStdModelSnDataHandleService.class);
        pushStdModelSnDataHandleServices = Lists.newArrayList(service1, service2);
        ReflectionTestUtils.setField(pushStdModelSnDataHandleServiceComposite, "pushStdModelSnDataHandleServices", pushStdModelSnDataHandleServices);
    }
    /*Ended by AICoder, pid:i3d6b5ab16703b9144e1085350b7ca87f4f398d9*/
}
/*Ended by AICoder, pid:i9f4bb19351334b14cc7095e10ec8c357f24cb79*/