package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.application.*;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.ModelRcvBillRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.datawb.ProdPickListMainDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.Constants;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.util.NoticeCenterUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@PrepareForTest({DatawbRemoteService.class, JSON.class, SpringContextUtil.class, JacksonJsonConverUtil.class})
@RunWith(PowerMockRunner.class)
public class ModelRcvBillServiceImplTest {

    @InjectMocks
    private ModelRcvBillServiceImpl service;
    @Mock
    private ModelRcvBillService modelRcvBillService;
    @Mock
    private ModelRcvBillRepository modelRcvBillRepository;
    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private PsTaskExtendedService psTaskExtendedService;
    @Mock
    private CustomerItemsService customerItemsService;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Mock
    private TradeDataLogService tradeDataLogService;
    @Mock
    private NoticeCenterService noticeCenterService;
    @Mock
    private LocaleMessageSourceBean lmb;
    @Mock
    private InoneRemoteService inoneRemoteService;
    @Mock
    private NoticeCenterUtils noticeCenterUtils;
    @Mock
    private IscpRemoteService iscpRemoteService;


    @Before
    public void init() {
        PowerMockito.mockStatic(DatawbRemoteService.class, JSON.class, SpringContextUtil.class, JacksonJsonConverUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(Constants.LOCALE_RESOLVER_MES)).thenReturn(lmb);
    }

    @Test
    public void test_filterSendRecordList() throws Exception {
        List<EdiSoSDTO> sendRecordList = new ArrayList<>();
        EdiSoSDTO ediSoSDTO = new EdiSoSDTO();
        ediSoSDTO.setExternalorderkey2("111");
        ediSoSDTO.setStatus("9");
        sendRecordList.add(ediSoSDTO);
        Map<String, String> invalidMap = new HashMap<>();
        Whitebox.invokeMethod(service, "filterSendRecordList", sendRecordList, invalidMap, lmb);
        Assert.assertNotNull(sendRecordList);
    }

    @Test
    public void test_next_error() throws Exception {
        Map<String, List<BarcodeExpandVO>> cartonNoMap = new HashMap<>();
        cartonNoMap.put("1",new ArrayList<>());
        Map<String, EdiSoSDTO> fromIdMap = new HashMap<>();
        fromIdMap.put("2",new EdiSoSDTO());
        Set<String> errList = new HashSet<>();
        errList.add("1");
        Map<String, String> invalidMap = new HashMap<>();
        PowerMockito.doNothing().when(modelRcvBillService).pushB2bAndHandleData(anyMap(),anyMap());
        Whitebox.invokeMethod(service, "next", cartonNoMap, fromIdMap, errList, invalidMap);
        Assert.assertNotNull(cartonNoMap);
    }

    @Test
    public void test_getSendMaterials_error() throws Exception {
        List<String> completeBillNos = new ArrayList<>();
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(null);
        try {
            Whitebox.invokeMethod(service, "getSendMaterialsList", completeBillNos);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);
        try {
            Whitebox.invokeMethod(service, "getSendMaterialsList", completeBillNos);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
    }

    @Test
    public void test_judgeSendEmail_error() throws Exception {
        Map<String, ModelChangedCartonDTO> newCartonNoMap = new HashMap<>();
        ModelChangedCartonDTO modelChangedCartonDTO = new ModelChangedCartonDTO();
        modelChangedCartonDTO.setCreateDate(new Date());
        newCartonNoMap.put("1", modelChangedCartonDTO);
        List<String> cartonNos = new ArrayList<>();
        cartonNos.add("1");
        String cartonNo = "1";
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(null);
        try {
            Whitebox.invokeMethod(service, "judgeSendEmail", newCartonNoMap, cartonNo, cartonNos);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);
        try {
            Whitebox.invokeMethod(service, "judgeSendEmail", newCartonNoMap, cartonNo, cartonNos);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
        sysLookupValues.setLookupMeaning("10");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);
        Whitebox.invokeMethod(service, "judgeSendEmail", newCartonNoMap, cartonNo, cartonNos);
        Assert.assertTrue(true);
    }

    @Test
    public void test_pushModelChangeCartonToKafka_equalsSn() throws Exception {
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("1");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);
        PowerMockito.when(modelRcvBillRepository.getChangeSNByCondition(Mockito.any())).thenReturn(Collections.emptyList());
        Assert.assertNotNull(service.pushModelChangeCartonToKafka());
        List<ModelChangedCartonDTO> changedCartonList = new ArrayList<>();
        ModelChangedCartonDTO modelChangedCartonDTO = new ModelChangedCartonDTO();
        modelChangedCartonDTO.setCartonNo("220002044800");
        ModelChangedCartonDTO modelChangedCartonDTO1 = new ModelChangedCartonDTO();
        modelChangedCartonDTO1.setCartonNo("220002044800");
        changedCartonList.add(modelChangedCartonDTO);
        changedCartonList.add(modelChangedCartonDTO1);
        when(modelRcvBillRepository.getChangeSNByCondition(any())).thenReturn(changedCartonList);
        List<ModelRcvBillDetailDTO> modelRcvBillDetailList = new ArrayList<>();
        ModelRcvBillDetailDTO modelRcvBillDetailDTO = new ModelRcvBillDetailDTO();
        modelRcvBillDetailDTO.setCartonNo("220002044800");
        modelRcvBillDetailDTO.setSnNo("1");
        modelRcvBillDetailDTO.setOrderNo("GL19090300002");
        modelRcvBillDetailDTO.setMaterialMpn("itemNo");
        ModelRcvBillDetailDTO modelRcvBillDetailDTO1 = new ModelRcvBillDetailDTO();
        modelRcvBillDetailDTO1.setCartonNo("220002044800");
        modelRcvBillDetailDTO1.setSnNo("2");
        modelRcvBillDetailDTO1.setOrderNo("GL19090300002");
        modelRcvBillDetailDTO1.setMaterialMpn("itemNo");
        modelRcvBillDetailList.add(modelRcvBillDetailDTO);
        modelRcvBillDetailList.add(modelRcvBillDetailDTO1);
        when(modelRcvBillRepository.getModelRcvBillDetailByCartonNo(anyList())).thenReturn(modelRcvBillDetailList);
        List<CustomerItemsDTO> customerItemList = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("itemNo");
        customerItemsDTO.setCustomerComponentType("server");
        customerItemList.add(customerItemsDTO);
        when(customerItemsService.queryListByCustomerList(any())).thenReturn(customerItemList);
        List<BarcodeExpandVO> barCodeList = new ArrayList<>();
        BarcodeExpandVO barcodeExpandVO = new BarcodeExpandVO();
        barcodeExpandVO.setBarcode("1");
        BarcodeExpandVO barcodeExpandVO1 = new BarcodeExpandVO();
        barcodeExpandVO1.setBarcode("3");
        barCodeList.add(barcodeExpandVO);
        barCodeList.add(barcodeExpandVO1);
        when(barcodeCenterRemoteService.getSnByBarCodeAndRcvNo(any(), any())).thenReturn(barCodeList);
        doNothing().when(modelRcvBillService).pushB2BAndUpdateChangeCartons(Mockito.anyList(), anyList());
        Assert.assertNotNull(service.pushModelChangeCartonToKafka());
    }

    @Test
    public void test_pushModelChangeCartonToKafka_equalsSnButEmpty() throws Exception {
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("1");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);
        List<ModelChangedCartonDTO> changedCartonList = new ArrayList<>();
        ModelChangedCartonDTO modelChangedCartonDTO = new ModelChangedCartonDTO();
        modelChangedCartonDTO.setCartonNo("220002044800");
        changedCartonList.add(modelChangedCartonDTO);
        when(modelRcvBillRepository.getChangeSNByCondition(any())).thenReturn(changedCartonList);
        List<ModelRcvBillDetailDTO> modelRcvBillDetailList = new ArrayList<>();
        ModelRcvBillDetailDTO modelRcvBillDetailDTO = new ModelRcvBillDetailDTO();
        modelRcvBillDetailDTO.setCartonNo("220002044800");
        modelRcvBillDetailDTO.setSnNo("1");
        modelRcvBillDetailDTO.setOrderNo("GL19090300002");
        modelRcvBillDetailDTO.setMaterialMpn("itemNo");
        ModelRcvBillDetailDTO modelRcvBillDetailDTO1 = new ModelRcvBillDetailDTO();
        modelRcvBillDetailDTO1.setCartonNo("220002044800");
        modelRcvBillDetailDTO1.setSnNo("2");
        modelRcvBillDetailDTO1.setOrderNo("GL19090300002");
        modelRcvBillDetailDTO1.setMaterialMpn("itemNo");
        modelRcvBillDetailList.add(modelRcvBillDetailDTO);
        modelRcvBillDetailList.add(modelRcvBillDetailDTO1);
        when(modelRcvBillRepository.getModelRcvBillDetailByCartonNo(anyList())).thenReturn(modelRcvBillDetailList);
        List<CustomerItemsDTO> customerItemList = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("itemNo");
        customerItemsDTO.setCustomerComponentType("server");
        customerItemList.add(customerItemsDTO);
        when(customerItemsService.queryListByCustomerList(any())).thenReturn(customerItemList);
        List<BarcodeExpandVO> barCodeList = new ArrayList<>();
        BarcodeExpandVO barcodeExpandVO = new BarcodeExpandVO();
        barcodeExpandVO.setBarcode("1");
        BarcodeExpandVO barcodeExpandVO1 = new BarcodeExpandVO();
        barcodeExpandVO1.setBarcode("2");
        barCodeList.add(barcodeExpandVO);
        barCodeList.add(barcodeExpandVO1);
        when(barcodeCenterRemoteService.getSnByBarCodeAndRcvNo(any(),any())).thenReturn(barCodeList);
        Assert.assertNotNull(service.pushModelChangeCartonToKafka());
    }

    @Test
    public void test_pushModelChangeCartonToKafka_lessThanSn() throws Exception {
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("0");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);
        List<ModelChangedCartonDTO> changedCartonList = new ArrayList<>();
        ModelChangedCartonDTO modelChangedCartonDTO = new ModelChangedCartonDTO();
        modelChangedCartonDTO.setCartonNo("220002044800");
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -(7));
        modelChangedCartonDTO.setCreateDate(cal.getTime());
        changedCartonList.add(modelChangedCartonDTO);
        when(modelRcvBillRepository.getChangeSNByCondition(any())).thenReturn(changedCartonList);
        List<ModelRcvBillDetailDTO> modelRcvBillDetailList = new ArrayList<>();
        ModelRcvBillDetailDTO modelRcvBillDetailDTO = new ModelRcvBillDetailDTO();
        modelRcvBillDetailDTO.setCartonNo("220002044800");
        modelRcvBillDetailDTO.setSnNo("1");
        modelRcvBillDetailDTO.setOrderNo("GL19090300002");
        modelRcvBillDetailDTO.setMaterialMpn("itemNo");
        ModelRcvBillDetailDTO modelRcvBillDetailDTO1 = new ModelRcvBillDetailDTO();
        modelRcvBillDetailDTO1.setCartonNo("220002044800");
        modelRcvBillDetailDTO1.setSnNo("2");
        modelRcvBillDetailDTO1.setOrderNo("GL19090300002");
        modelRcvBillDetailDTO1.setMaterialMpn("itemNo");
        modelRcvBillDetailList.add(modelRcvBillDetailDTO);
        modelRcvBillDetailList.add(modelRcvBillDetailDTO1);
        when(modelRcvBillRepository.getModelRcvBillDetailByCartonNo(anyList())).thenReturn(modelRcvBillDetailList);
        List<CustomerItemsDTO> customerItemList = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("itemNo");
        customerItemsDTO.setCustomerComponentType("server");
        customerItemList.add(customerItemsDTO);
        when(customerItemsService.queryListByCustomerList(any())).thenReturn(customerItemList);
        List<BarcodeExpandVO> barCodeList = new ArrayList<>();
        BarcodeExpandVO barcodeExpandVO = new BarcodeExpandVO();
        barcodeExpandVO.setBarcode("1");
        barCodeList.add(barcodeExpandVO);
        when(barcodeCenterRemoteService.getSnByBarCode(anyString(),anyString())).thenReturn(barCodeList);
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);
        doNothing().when(noticeCenterService).sendEmail(anyString(), anyString(), anyMap());
        Assert.assertNotNull(service.pushModelChangeCartonToKafka());
    }

    @Test
    public void test_pushModelChangeCartonToKafka_greaterThanSn() throws Exception {
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("0");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);
        List<ModelChangedCartonDTO> changedCartonList = new ArrayList<>();
        ModelChangedCartonDTO modelChangedCartonDTO = new ModelChangedCartonDTO();
        modelChangedCartonDTO.setCartonNo("220002044800");
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -(7));
        modelChangedCartonDTO.setCreateDate(cal.getTime());
        changedCartonList.add(modelChangedCartonDTO);
        when(modelRcvBillRepository.getChangeSNByCondition(any())).thenReturn(changedCartonList);
        List<ModelRcvBillDetailDTO> modelRcvBillDetailList = new ArrayList<>();
        ModelRcvBillDetailDTO modelRcvBillDetailDTO = new ModelRcvBillDetailDTO();
        modelRcvBillDetailDTO.setCartonNo("220002044800");
        modelRcvBillDetailDTO.setSnNo("1");
        modelRcvBillDetailDTO.setOrderNo("GL19090300002");
        modelRcvBillDetailDTO.setMaterialMpn("itemNo");
        ModelRcvBillDetailDTO modelRcvBillDetailDTO1 = new ModelRcvBillDetailDTO();
        modelRcvBillDetailDTO1.setCartonNo("220002044800");
        modelRcvBillDetailDTO1.setSnNo("2");
        modelRcvBillDetailDTO1.setOrderNo("GL19090300002");
        modelRcvBillDetailDTO1.setMaterialMpn("itemNo");
        modelRcvBillDetailList.add(modelRcvBillDetailDTO);
        modelRcvBillDetailList.add(modelRcvBillDetailDTO1);
        when(modelRcvBillRepository.getModelRcvBillDetailByCartonNo(anyList())).thenReturn(modelRcvBillDetailList);
        List<CustomerItemsDTO> customerItemList = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("itemNo");
        customerItemsDTO.setCustomerComponentType("server");
        customerItemList.add(customerItemsDTO);
        when(customerItemsService.queryListByCustomerList(any())).thenReturn(customerItemList);
        List<BarcodeExpandVO> barCodeList = new ArrayList<>();
        BarcodeExpandVO barcodeExpandVO = new BarcodeExpandVO();
        barcodeExpandVO.setBarcode("1");
        BarcodeExpandVO barcodeExpandVO1 = new BarcodeExpandVO();
        barcodeExpandVO1.setBarcode("2");
        BarcodeExpandVO barcodeExpandVO2 = new BarcodeExpandVO();
        barcodeExpandVO2.setBarcode("3");
        barCodeList.add(barcodeExpandVO);
        barCodeList.add(barcodeExpandVO1);
        barCodeList.add(barcodeExpandVO2);
        when(barcodeCenterRemoteService.getSnByBarCode(anyString(),anyString())).thenReturn(barCodeList);
        doNothing().when(noticeCenterService).sendEmail(anyString(), anyString(), anyMap());
        Assert.assertNotNull(service.pushModelChangeCartonToKafka());
    }


    @Test
    public void test_pushPackListAndSnToKafka_error() throws Exception {
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(null);
        try {
            service.pushPackListAndSnToKafka();
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);
        try {
            service.pushPackListAndSnToKafka();
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }

        sysLookupValues.setLookupMeaning("1");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);
        PowerMockito.when(modelRcvBillRepository.getByCondition(Mockito.any())).thenReturn(Collections.emptyList());
        Assert.assertNotNull(service.pushPackListAndSnToKafka());

        List<ModelRcvBillDTO> modelRcvBillDTOS = new ArrayList<>();
        ModelRcvBillDTO modelRcvBillDTO = new ModelRcvBillDTO();
        modelRcvBillDTO.setBillNo("GL19090300002");
        modelRcvBillDTO.setTaskNo("taskNo");
        modelRcvBillDTO.setRcvNo("rcvNo");
        modelRcvBillDTO.setPushStatus(0);
        modelRcvBillDTOS.add(modelRcvBillDTO);
        PowerMockito.when(modelRcvBillRepository.getByCondition(Mockito.any())).thenReturn(modelRcvBillDTOS);
        PowerMockito.when(DatawbRemoteService.getMaterialsWarehouses(Mockito.any())).thenReturn(Collections.emptyList());
        Assert.assertNotNull(service.pushPackListAndSnToKafka());
        List<EdiSoSDTO> ediSoSDTOList = new ArrayList<>();
        EdiSoSDTO ediSoSDTOTest = new EdiSoSDTO();
        ediSoSDTOTest.setExternalorderkey2("GL19090300002");
        ediSoSDTOTest.setSku("082740302565");
        ediSoSDTOTest.setStatus("9");
        ediSoSDTOTest.setQty(new BigDecimal("1"));
        ediSoSDTOTest.setWhseId("WMWHSE6");
        ediSoSDTOTest.setExternalorderkey2("rcvNo");
        ediSoSDTOList.add(ediSoSDTOTest);
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupMeaning("1");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any())).thenReturn(sysLookupValues1);
        List<EdiSoSDTO> sendMaterials = new ArrayList<>();
        EdiSoSDTO ediSoSDTO = new EdiSoSDTO();
        ediSoSDTO.setExternalorderkey2("GL19090300002");
        ediSoSDTO.setFromId("11111");
        ediSoSDTO.setWhseId("WMWHSE6");
        ediSoSDTO.setSku("045020400177");
        ediSoSDTO.setQty(new BigDecimal("0"));
        EdiSoSDTO ediSoSDTO1 = new EdiSoSDTO();
        ediSoSDTO1.setExternalorderkey2("GL19090300002");
        ediSoSDTO1.setFromId("11111");
        ediSoSDTO1.setWhseId("WMWHSE6");
        ediSoSDTO1.setSku("045020400177");
        ediSoSDTO1.setQty(new BigDecimal("0"));
        EdiSoSDTO ediSoSDTO2 = new EdiSoSDTO();
        ediSoSDTO2.setExternalorderkey2("GL19090300002");
        ediSoSDTO2.setFromId("11111");
        ediSoSDTO2.setWhseId("WMWHSE6");
        ediSoSDTO2.setSku("04502040017777");
        ediSoSDTO2.setQty(new BigDecimal("0"));
        sendMaterials.add(ediSoSDTO);
        sendMaterials.add(ediSoSDTO1);
        sendMaterials.add(ediSoSDTO2);
        PowerMockito.when(DatawbRemoteService.getSendMaterials(any())).thenReturn(sendMaterials);
        List<CustomerItemsDTO> customerItemsDTOS = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setCooperationMode("B&S");
        customerItemsDTO.setCustomerCode("045020400177");
        customerItemsDTO.setCustomerComponentType("CPU");
        customerItemsDTO.setZteCode("045020400177");
        customerItemsDTOS.add(customerItemsDTO);
        CustomerItemsDTO customerItemsDTO1 = new CustomerItemsDTO();
        customerItemsDTO1.setCooperationMode("test");
        customerItemsDTO1.setCustomerCode("045020400177");
        customerItemsDTO1.setCustomerComponentType("CPU");
        customerItemsDTO1.setZteCode("1");
        CustomerItemsDTO customerItemsDTO2 = new CustomerItemsDTO();
        customerItemsDTO2.setCooperationMode(null);
        customerItemsDTO2.setCustomerCode("045020400177");
        customerItemsDTO2.setCustomerComponentType("CPU");
        customerItemsDTO2.setZteCode("1");
        customerItemsDTOS.add(customerItemsDTO);
        customerItemsDTOS.add(customerItemsDTO1);
        customerItemsDTOS.add(customerItemsDTO2);
        List<CivControlInfoDTO> infoDTOList = new ArrayList<>();
        CivControlInfoDTO civControlInfoDTO = new CivControlInfoDTO();
        civControlInfoDTO.setItemNo("045020400177");
        civControlInfoDTO.setBarcodeControlType(40);
        CivControlInfoDTO civControlInfoDTO1 = new CivControlInfoDTO();
        civControlInfoDTO1.setItemNo("11111111111");
        civControlInfoDTO1.setBarcodeControlType(30);
        infoDTOList.add(civControlInfoDTO);
        infoDTOList.add(civControlInfoDTO1);
        PowerMockito.when(iscpRemoteService.queryAliControlInfoList(Mockito.any())).thenReturn(infoDTOList);
        PowerMockito.when(customerItemsService.queryListByCustomerList(Mockito.any())).thenReturn(customerItemsDTOS);
        List<BarcodeExpandVO> snList = new ArrayList<>();
        BarcodeExpandVO barcodeExpandVO1 = new BarcodeExpandVO();
        barcodeExpandVO1.setBarcode("1");
        snList.add(barcodeExpandVO1);
        PowerMockito.when(barcodeCenterRemoteService.getSnByBarCode(Mockito.any(),anyString())).thenReturn(snList);
        doNothing().when(tradeDataLogService).pushDataOfExceptionRollback(Mockito.anyList());
        doNothing().when(modelRcvBillService).pushB2bAndHandleData(anyMap(), anyMap());
        Assert.assertNotNull(service.pushPackListAndSnToKafka());
    }

    @Test
    public void test_pushPackListAndSnToKafka() throws Exception {
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("1");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);
        List<ModelRcvBillDTO> modelRcvBillDTOS = new ArrayList<>();
        ModelRcvBillDTO modelRcvBillDTO = new ModelRcvBillDTO();
        modelRcvBillDTO.setBillNo("GL19090300002");
        modelRcvBillDTO.setTaskNo("taskNo");
        modelRcvBillDTO.setRcvNo("rcvNo");
        modelRcvBillDTO.setPushStatus(0);
        modelRcvBillDTOS.add(modelRcvBillDTO);
        PowerMockito.when(modelRcvBillRepository.getByCondition(Mockito.any())).thenReturn(modelRcvBillDTOS);
        List<EdiSoSDTO> ediSoSDTOList = new ArrayList<>();
        EdiSoSDTO ediSoSDTOTest = new EdiSoSDTO();
        ediSoSDTOTest.setOrderNo("GL19090300002");
        ediSoSDTOTest.setSku("082740302565");
        ediSoSDTOTest.setExternalorderkey2("rcvNo");
        ediSoSDTOTest.setStatus("9");
        ediSoSDTOTest.setWhseId("WMWHSE6");
        ediSoSDTOList.add(ediSoSDTOTest);
        PowerMockito.when(DatawbRemoteService.getSendMaterials(any())).thenReturn(ediSoSDTOList);
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupMeaning("1");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any())).thenReturn(sysLookupValues1);
        List<EdiSoSDTO> sendMaterials = new ArrayList<>();
        EdiSoSDTO ediSoSDTO = new EdiSoSDTO();
        ediSoSDTO.setExternalorderkey2("GL19090300002");
        ediSoSDTO.setFromId("11111");
        ediSoSDTO.setWhseId("WMWHSE6");
        ediSoSDTO.setExternalorderkey2("rcvNo");
        ediSoSDTO.setStatus("9");
        ediSoSDTO.setSku("045020400177");
        ediSoSDTO.setQty(new BigDecimal("1"));
        sendMaterials.add(ediSoSDTO);
        PowerMockito.when(DatawbRemoteService.getSendMaterials(any())).thenReturn(sendMaterials);
        List<CustomerItemsDTO> customerItemsDTOS = new ArrayList<>();
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setCooperationMode("B&S");
        customerItemsDTO.setCustomerCode("045020400177");
        customerItemsDTO.setCustomerComponentType("CPU");
        customerItemsDTO.setZteCode("045020400177");
        CustomerItemsDTO customerItemsDTO1 = new CustomerItemsDTO();
        customerItemsDTO1.setCooperationMode("B&S");
        customerItemsDTO1.setCustomerCode("045020400177");
        customerItemsDTO1.setCustomerComponentType("CPU");
        customerItemsDTO1.setZteCode("11");
        customerItemsDTOS.add(customerItemsDTO);
        customerItemsDTOS.add(customerItemsDTO1);
        List<CivControlInfoDTO> infoDTOList = new ArrayList<>();
        CivControlInfoDTO civControlInfoDTO = new CivControlInfoDTO();
        civControlInfoDTO.setItemNo("045020400177");
        civControlInfoDTO.setBarcodeControlType(40);
        CivControlInfoDTO civControlInfoDTO1 = new CivControlInfoDTO();
        civControlInfoDTO1.setItemNo("4234234242");
        civControlInfoDTO1.setBarcodeControlType(null);
        infoDTOList.add(civControlInfoDTO);
        infoDTOList.add(civControlInfoDTO1);
        List<BarcodeExpandVO> snList = new ArrayList<>();
        BarcodeExpandVO barcodeExpandVO1 = new BarcodeExpandVO();
        barcodeExpandVO1.setBarcode("1");
        snList.add(barcodeExpandVO1);
        PowerMockito.when(barcodeCenterRemoteService.getSnByBarCode(anyString(), anyString())).thenReturn(snList);
        PowerMockito.when(iscpRemoteService.queryAliControlInfoList(Mockito.any())).thenReturn(infoDTOList);
        PowerMockito.when(customerItemsService.queryListByCustomerList(Mockito.any())).thenReturn(customerItemsDTOS);
        doNothing().when(tradeDataLogService).pushDataOfExceptionRollback(Mockito.anyList());
        doNothing().when(noticeCenterService).sendEmail(anyString(), anyString(), anyMap());
        doNothing().when(modelRcvBillService).pushB2bAndHandleData(anyMap(), anyMap());
        Assert.assertNotNull(service.pushPackListAndSnToKafka());
    }

    @Test
    public void test_pushB2BAndHandleData() {
        Map<String, List<BarcodeExpandVO>> cartonNoMap = new HashMap<>();
        Map<String, EdiSoSDTO> fromIdMap = new HashMap<>();
        List<BarcodeExpandVO> barcodeExpandVOList = new ArrayList<>();
        BarcodeExpandVO barcodeExpandVO = new BarcodeExpandVO();
        barcodeExpandVOList.add(barcodeExpandVO);
        cartonNoMap.put("11111", barcodeExpandVOList);
        EdiSoSDTO ediSoSDTO = new EdiSoSDTO();
        ediSoSDTO.setExternalorderkey2("CN223424");
        fromIdMap.put("11111", ediSoSDTO);
        service.pushB2bAndHandleData(cartonNoMap, fromIdMap);
        Assert.assertTrue(true);
    }

    @Test
    public void test_pushB2BAndHandleData_empty() {
        Map<String, List<BarcodeExpandVO>> cartonNoMap = new HashMap<>();
        Map<String, EdiSoSDTO> fromIdMap = new HashMap<>();
        List<BarcodeExpandVO> barcodeExpandVOList = new ArrayList<>();
        BarcodeExpandVO barcodeExpandVO = new BarcodeExpandVO();
        barcodeExpandVOList.add(barcodeExpandVO);
        cartonNoMap.put("11111", barcodeExpandVOList);
        EdiSoSDTO ediSoSDTO = new EdiSoSDTO();
        ediSoSDTO.setExternalorderkey2("CN223424");
        fromIdMap.put("11111", ediSoSDTO);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("Y");
        when(sysLookupValuesService.findByLookupCode(anyInt())).thenReturn(sysLookupValues);
        service.pushB2bAndHandleData(cartonNoMap, fromIdMap);
        Assert.assertTrue(true);
    }

    @Test
    public void test_queryMESModelRcvBill_error() {
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(null);
        try {
            service.queryMesModelRcvBill();
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);
        try {
            service.queryMesModelRcvBill();
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
        sysLookupValues.setLookupMeaning("1");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);
        PowerMockito.when(DatawbRemoteService.queryPickListByTaskNos(Mockito.any())).thenReturn(Collections.emptyList());
        PowerMockito.when(inoneRemoteService.getPicklistMain(Mockito.any())).thenReturn(Collections.emptyList());
        Assert.assertNotNull(service.queryMesModelRcvBill());
        List<ProdPickListMainDTO> prodPickListMains = new ArrayList<>();
        ProdPickListMainDTO prodPickListMainDTO = new ProdPickListMainDTO();
        prodPickListMainDTO.setBillNumber("1");
        prodPickListMains.add(prodPickListMainDTO);
        PowerMockito.when(DatawbRemoteService.queryPickListByTaskNos(Mockito.any())).thenReturn(prodPickListMains);
        List<PsTaskExtendedDTO> psTaskExtendedDTOS = new ArrayList<>();
        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setTaskNo("SXCS-201908-01");
        psTaskExtendedDTO.setCustomerNo("CN000000164259");
        psTaskExtendedDTOS.add(psTaskExtendedDTO);
        PsTaskExtendedDTO psTaskExtendedDTO1 = new PsTaskExtendedDTO();
        psTaskExtendedDTO1.setTaskNo("SXCS-201908-01");
        psTaskExtendedDTO1.setCustomerNo(null);
        PsTaskExtendedDTO psTaskExtendedDTO2 = new PsTaskExtendedDTO();
        psTaskExtendedDTO2.setTaskNo("SXS-201908-01");
        psTaskExtendedDTO2.setCustomerNo("CN000000164259");
        psTaskExtendedDTOS.add(psTaskExtendedDTO);
        psTaskExtendedDTOS.add(psTaskExtendedDTO1);
        psTaskExtendedDTOS.add(psTaskExtendedDTO2);
        PowerMockito.when(psTaskExtendedService.queryByTaskNos(Mockito.anyList())).thenReturn(psTaskExtendedDTOS);
        List<SysLookupValues> typesDTOList = new ArrayList<>();
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupMeaning("CN000000164259");
        typesDTOList.add(sysLookupValues1);
        PowerMockito.when(sysLookupValuesService.selectValuesByType(Mockito.anyInt())).thenReturn(typesDTOList);
        try {
            service.queryMesModelRcvBill();
        }catch (Exception e) {
            Assert.assertNotNull(e);
        }
    }

    @Test
    public void test_queryMESModelRcvBill() {
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("1");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);
        List<ProdPickListMainDTO> prodPickListMainDTOS = new ArrayList<>();
        ProdPickListMainDTO prodPickListMainDTO = new ProdPickListMainDTO();
        prodPickListMainDTO.setTaskNo("SXCS-201908-01");
        prodPickListMainDTO.setTaskType("标模任务");
        prodPickListMainDTO.setBillNumber("GL19090300002");
        prodPickListMainDTO.setBillStatus("拟制");
        prodPickListMainDTO.setProductAddress("中兴通讯总部地址");
        prodPickListMainDTO.setSubStock("NJ100_CL");
        prodPickListMainDTO.setPickType("配送库");
        prodPickListMainDTOS.add(prodPickListMainDTO);
        PowerMockito.when(DatawbRemoteService.queryPickListByTaskNos(Mockito.any())).thenReturn(prodPickListMainDTOS);
        PowerMockito.when(inoneRemoteService.getPicklistMain(Mockito.any())).thenReturn(prodPickListMainDTOS);
        List<EdiSoSDTO> ediSosDtoList = new ArrayList<>();
        EdiSoSDTO ediSoSDTO = new EdiSoSDTO();
        ediSoSDTO.setOrderNo("GL19090300002");
        ediSoSDTO.setExternalorderkey2("GL19090300002485734985734895");
        EdiSoSDTO ediSoSDTO1 = new EdiSoSDTO();
        ediSoSDTO1.setOrderNo("GL19090300002");
        ediSoSDTO1.setExternalorderkey2("GL19090300002485734985734895");
        EdiSoSDTO ediSoSDTO2 = new EdiSoSDTO();
        ediSoSDTO2.setOrderNo("GL19090300003");
        ediSoSDTO2.setExternalorderkey2(null);
        ediSosDtoList.add(ediSoSDTO);
        ediSosDtoList.add(ediSoSDTO1);
        ediSosDtoList.add(ediSoSDTO2);
        PowerMockito.when(DatawbRemoteService.getSendMaterials(Mockito.any())).thenReturn(ediSosDtoList);
        List<PsTaskExtendedDTO> psTaskExtendedDTOS = new ArrayList<>();
        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setTaskNo("SXCS-201908-01");
        psTaskExtendedDTO.setCustomerNo("CN000000164259");
        psTaskExtendedDTOS.add(psTaskExtendedDTO);
        PowerMockito.when(psTaskExtendedService.queryByTaskNos(Mockito.anyList())).thenReturn(psTaskExtendedDTOS);
        List<SysLookupValues> typesDTOList = new ArrayList<>();
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupMeaning("CN000000164259");
        typesDTOList.add(sysLookupValues1);
        PowerMockito.when(sysLookupValuesService.selectValuesByType(Mockito.anyInt())).thenReturn(typesDTOList);
        PowerMockito.when(modelRcvBillRepository.batchInsertIgnoreExisted(Mockito.anyList())).thenReturn(1);
        Assert.assertNotNull(service.queryMesModelRcvBill());
    }

    @Test
    public void testPushB2BAndUpdateChangeCartons() {
        List<String> uploadedCartonNos = new ArrayList<>();
        uploadedCartonNos.add("1");
        service.pushB2BAndUpdateChangeCartons(uploadedCartonNos, new ArrayList<>());
        Assert.assertTrue(true);
        List<CustomerDataLogDTO> resultList = new ArrayList<>();
        resultList.add(new CustomerDataLogDTO());
        PowerMockito.when(modelRcvBillRepository.updateStatusByCartonNo(anyList())).thenReturn(1);
        doNothing().when(tradeDataLogService).pushDataOfExceptionRollback(anyList());
        service.pushB2BAndUpdateChangeCartons(uploadedCartonNos, resultList);
        Assert.assertTrue(true);
    }

    @Test
    public void testDispatchManufactureOrderCallBack_Success() {
        B2bCallBackNewDTO dto1 = new B2bCallBackNewDTO();
        dto1.setCode("0000");
        dto1.setSuccess(true);
        dto1.setKeywords("task123");
        dto1.setMessageType("ZTEiMES-Alibaba-CreateCartonRelation");
        dto1.setData("{\"messageId\":\"task123\", \"success\":true}");

        WorkOrderWriteCallBackDTO workOrderWriteCallBackDTO = new WorkOrderWriteCallBackDTO();
        workOrderWriteCallBackDTO.setData("{\"success\": true, \"result\": {\"success\": true, \"msg\": \"Success\"}}");
        WorkOrderWriteCallBackDataDTO workOrderWriteCallBackDataDTO = new WorkOrderWriteCallBackDataDTO();
        workOrderWriteCallBackDataDTO.setSuccess(true);
        workOrderWriteCallBackDataDTO.setResult("{\"success\": true, \"msg\": \"Success\"}");
        WorkOrderWriteCallBackResultDTO workOrderWriteCallBackResultDTO = new WorkOrderWriteCallBackResultDTO();
        workOrderWriteCallBackResultDTO.setSuccess(true);

        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDTO.class))).thenReturn(workOrderWriteCallBackDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDataDTO.class))).thenReturn(workOrderWriteCallBackDataDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackResultDTO.class))).thenReturn(workOrderWriteCallBackResultDTO);

        service.b2bCallBackUpdateStatus(dto1);

        verify(modelRcvBillRepository, times(1)).updateStatus(anyList());
    }

    @Test
    public void testDispatchManufactureOrderCallBack_Success1() {
        B2bCallBackNewDTO dto1 = new B2bCallBackNewDTO();
        dto1.setCode("0000");
        dto1.setSuccess(true);
        dto1.setKeywords("task123");
        dto1.setMessageType("ZTEiMES-Alibaba-CreateCartonRelation");
        dto1.setData("{\"messageId\":\"task123\", \"success\":true}");

        WorkOrderWriteCallBackDTO workOrderWriteCallBackDTO = new WorkOrderWriteCallBackDTO();
        workOrderWriteCallBackDTO.setData("{\"success\": true, \"result\": {\"success\": true, \"msg\": \"Success\"}}");
        WorkOrderWriteCallBackDataDTO workOrderWriteCallBackDataDTO = new WorkOrderWriteCallBackDataDTO();
        workOrderWriteCallBackDataDTO.setSuccess(true);
        workOrderWriteCallBackDataDTO.setResult("{\"success\": false, \"msg\": \"Success\"}");
        WorkOrderWriteCallBackResultDTO workOrderWriteCallBackResultDTO = new WorkOrderWriteCallBackResultDTO();
        workOrderWriteCallBackResultDTO.setSuccess(false);

        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDTO.class))).thenReturn(workOrderWriteCallBackDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDataDTO.class))).thenReturn(workOrderWriteCallBackDataDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackResultDTO.class))).thenReturn(workOrderWriteCallBackResultDTO);

        service.b2bCallBackUpdateStatus(dto1);

        verify(modelRcvBillRepository, times(1)).updateStatus(anyList());
    }

    @Test
    public void testDispatchManufactureOrderCallBack_Success2() {
        B2bCallBackNewDTO dto1 = new B2bCallBackNewDTO();
        dto1.setCode("0000");
        dto1.setSuccess(true);
        dto1.setKeywords("task123");
        dto1.setMessageType("ZTEiMES-Alibaba-CartonRelation");
        dto1.setData("{\"messageId\":\"task123\", \"success\":false}");

        WorkOrderWriteCallBackDTO workOrderWriteCallBackDTO = new WorkOrderWriteCallBackDTO();
        workOrderWriteCallBackDTO.setData("{\"success\": true, \"result\": {\"success\": true, \"msg\": \"Success\"}}");
        WorkOrderWriteCallBackDataDTO workOrderWriteCallBackDataDTO = new WorkOrderWriteCallBackDataDTO();
        workOrderWriteCallBackDataDTO.setSuccess(false);
        workOrderWriteCallBackDataDTO.setResult("{\"success\": false, \"msg\": \"Success\"}");
        WorkOrderWriteCallBackResultDTO workOrderWriteCallBackResultDTO = new WorkOrderWriteCallBackResultDTO();
        workOrderWriteCallBackResultDTO.setSuccess(false);

        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDTO.class))).thenReturn(workOrderWriteCallBackDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDataDTO.class))).thenReturn(workOrderWriteCallBackDataDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackResultDTO.class))).thenReturn(workOrderWriteCallBackResultDTO);

        service.b2bCallBackUpdateStatus(dto1);
        Assert.assertTrue(true);
    }

    @Test
    public void testDispatchManufactureOrderCallBack_Failure() {
        B2bCallBackNewDTO dto1 = new B2bCallBackNewDTO();
        dto1.setCode("0000");
        dto1.setSuccess(true);
        dto1.setMessageType("ZTEiMES-Alibaba-CreateCartonRelation");
        dto1.setData("{\"messageId\":\"task123\", \"success\":true}");

        B2bCallBackNewDTO dto2 = new B2bCallBackNewDTO();
        dto2.setKeywords("task456");
        dto2.setCode("0001");
        dto2.setMessageType("ZTEiMES-Alibaba-CreateCartonRelation");
        dto2.setSuccess(false);
        dto2.setData("{\"messageId\":\"task456\", \"success\":false}");
        List<B2bCallBackNewDTO> dataList = Arrays.asList(dto1, dto2);

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("1");
        when(sysLookupValuesService.findByLookupCode(Constant.LOOK_UP_VALUE_7599002)).thenReturn(sysLookupValues);

        dataList.forEach(d -> service.b2bCallBackUpdateStatus(d));

        verify(modelRcvBillRepository, times(1)).updateStatus(anyList());
    }

    @Test
    public void testDispatchManufactureOrderCallBack() {
        B2bCallBackNewDTO dto1 = new B2bCallBackNewDTO();
        dto1.setCode("0000");
        dto1.setSuccess(true);
        dto1.setMessageType("ZTEiMES-Alibaba-UpdateCartonRelation");
        dto1.setData("{\"messageId\":\"task123\", \"success\":true}");

        B2bCallBackNewDTO dto2 = new B2bCallBackNewDTO();
        dto2.setKeywords("task456");
        dto2.setCode("0001");
        dto2.setMessageType("ZTEiMES-Alibaba-UpdateCartonRelation");
        dto2.setSuccess(false);
        dto2.setData("{\"messageId\":\"task456\", \"success\":false}");
        List<B2bCallBackNewDTO> dataList = Arrays.asList(dto1, dto2);

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("1");
        when(sysLookupValuesService.findByLookupCode(Constant.LOOK_UP_VALUE_7599002)).thenReturn(sysLookupValues);

        dataList.forEach(d -> service.b2bCallBackUpdateStatus(d));

        verify(modelRcvBillRepository, times(1)).updateStatusByCartonNo(anyList());
    }

    @Test
    public void test_addPushDataAndUpdateStatus() throws Exception {
        List<ModelRcvBillDetailDTO> ModelRcvBillDetailList = new ArrayList<>();
        Whitebox.invokeMethod(service, "addPushDataAndUpdateStatus", ModelRcvBillDetailList);
        Assert.assertTrue(true);
    }

    @Test
    public void test_consumeChangeCartonNo() throws Exception {
        service.consumeChangeCartonNo(null);
        Assert.assertTrue(true);

        String msg = "1";
        when(JacksonJsonConverUtil.jsonToBean(anyString(), any(TypeReference.class))).thenReturn(null);
        service.consumeChangeCartonNo("1");
        Assert.assertTrue(true);

        ChangeCartonNoDTO changeCartonNoDTO = new ChangeCartonNoDTO();
        when(JacksonJsonConverUtil.jsonToBean(anyString(), any(TypeReference.class))).thenReturn(changeCartonNoDTO);
        service.consumeChangeCartonNo("1");
        Assert.assertTrue(true);

        List<String> changedContainerBarcodeList = new ArrayList<>();
        changedContainerBarcodeList.add("CN656");
        changeCartonNoDTO.setChangedContainerBarcodeList(changedContainerBarcodeList);
        when(JacksonJsonConverUtil.jsonToBean(anyString(), any(TypeReference.class))).thenReturn(changeCartonNoDTO);
        when(modelRcvBillRepository.batchInsertChangeCartonNo(anyList())).thenReturn(1);
        service.consumeChangeCartonNo("1");
        Assert.assertTrue(true);
    }

}

