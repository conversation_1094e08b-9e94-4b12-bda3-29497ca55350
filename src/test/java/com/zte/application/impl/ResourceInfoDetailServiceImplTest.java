package com.zte.application.impl;
import com.zte.application.HrmUserCenterService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.*;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.ResourceInfoDetailDTO;
import com.zte.interfaces.dto.ResourceInfoEntityDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.FileUtils;
import com.zte.util.BaseTestCase;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.io.BufferedWriter;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisHelper.class, RedisCacheUtils.class, SpringContextUtil.class, FileUtils.class, DateUtils.class,ResourceInfoServiceImpl.class})
public class ResourceInfoDetailServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ResourceInfoDetailServiceImpl service;
    @Mock
    private ResourceWarningRecordRepository resourceWarningRecordRepository;
    @Mock
    private ResourceInfoServiceImpl resourceInfoServiceImpl;
    @Mock
    private ResourceInfoRepository resourceInfoRepository;
    @Mock
    private ResourceInfoDetailRepository infoDetailRepository;
    @Mock
    private HrmUserCenterService hrmUserCenterService;
    @Mock
    private SysLookupValuesRepository lookupValuesRepository;


    @Before
    public void init() {
        PowerMockito.mockStatic(ResourceInfoServiceImpl.class);
    }


    @Test
    public void writerDataAndUpload()throws Exception{
        BufferedWriter txtFileOutputStream=null;
        Map<String, Object> map=new HashMap<>();
        int times=0;
        String fileName="123";
        StringBuilder sb=new StringBuilder();
        Whitebox.invokeMethod(service,"writerDataAndUpload",txtFileOutputStream,map,times,fileName,sb);
        map.put("resourceNo",123);
        try {
            Whitebox.invokeMethod(service,"writerDataAndUpload",txtFileOutputStream,map,times,fileName,sb);
        }catch (Exception e){
            Assert.assertEquals("Y", Constant.FLAG_Y);
        }
    }
    @Test
    public void countExportTotal1() {
        ResourceInfoDetailDTO info = new ResourceInfoDetailDTO();
        info.setResourceType(MpConstant.NAL);
        info.setResourceNo("123");
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }
        info.setResourceStr("123");
        service.countExportTotal(info);
    }

    @Test
    public void countExportTotal() {
        ResourceInfoDetailDTO info = new ResourceInfoDetailDTO();
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.RESOURCE_TYPE_CAN_NOT_BE_NULL, e.getMessage());
        }
        info.setResourceType(MpConstant.NAL);
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }
        info.setStartTime("2023-12-19 00:00:00");
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }
        info.setStartTime(null);
        info.setEndTime("2024-01-19 23:59:59");
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }
        info.setStartTime("2023-12-19 00:00:00");
        info.setEndTime("2025-01-19 23:59:59");
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_INTERVAL_ERROR, e.getMessage());
        }
        info.setEndTime("2024-01-19 23:59:59");

        //ResourceStr为空
        service.countExportTotal(info);
        //ResourceType
        info.setResourceType("122");
        service.countExportTotal(info);
        info.setResourceStr("123");
        PowerMockito.when(infoDetailRepository.findResourceNoByNum(Mockito.anyString())).thenReturn(null);
        List<ResourceInfoEntityDTO> pageInfo = new ArrayList<>();
        PowerMockito.when(resourceInfoRepository.pageList(Mockito.any())).thenReturn(pageInfo);
        PowerMockito.when(ResourceInfoServiceImpl.transformHexadecimal(Mockito.any(),Mockito.any())).thenReturn(new HashMap<>());
        service.countExportTotal(info);
    }

    @Test
    public void queryExportData() throws Exception {
        ResourceInfoDetailDTO info = new ResourceInfoDetailDTO();
        List<ResourceInfoDetailDTO> pageInfo = new ArrayList<>();
        List<ResourceInfoEntityDTO> entityDTOList = new ArrayList<>();
        ResourceInfoDetailDTO dto = new ResourceInfoDetailDTO();
        dto.setCreateBy("123");
        pageInfo.add(dto);
        PowerMockito.when(resourceInfoRepository.pageList(Mockito.any())).thenReturn(null);
        service.queryExportData(info, 1, 10);
        ResourceInfoEntityDTO dto1 = new ResourceInfoEntityDTO();
        dto1.setResourceNo("123");
        entityDTOList.add(dto1);
        PowerMockito.when(resourceInfoRepository.pageList(Mockito.any())).thenReturn(entityDTOList);

        List<SysLookupValues> valuesList = new ArrayList<>();
        PowerMockito.when(lookupValuesRepository.getList(Mockito.any())).thenReturn(valuesList);
        try {
            service.queryExportData(info, 1, 10);
        }catch (Exception e){
            Assert.assertNotNull(e.getMessage());
        }
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setAttribute1("111");
        valuesList.add(sysLookupValues);
        service.queryExportData(info, 1, 10);
        sysLookupValues.setLookupMeaning("123456");
        sysLookupValues.setDescriptionChin("中文");
        valuesList.add(sysLookupValues);
        info.setResourceStatus("123456");
        info.setResourceType("123456");

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("GY");

        hrmPersonInfoDTOMap.put("123",hrmPersonInfoDTO);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.any())).thenReturn(hrmPersonInfoDTOMap);

        PowerMockito.when(resourceInfoRepository.pageList(Mockito.any())).thenReturn(entityDTOList);
        Assert.assertNotNull(service.queryExportData(info, 1, 10));
        dto.setCreateBy("123");
        service.queryExportData(info, 1, 10);
    }
    

}
