package com.zte.application.impl;

import com.zte.application.HrmUserCenterService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.PackingListConfigRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.PackingListConfigDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class PackingListConfigServiceImplTest {
    @InjectMocks
    private PackingListConfigServiceImpl service;

    @Mock
    private PackingListConfigRepository packingListConfigRepository;
    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private HrmUserCenterService hrmUserCenterService;

    @Test
    public void test_validParams() throws Exception {
        PackingListConfigDTO dto = new PackingListConfigDTO();
        try {
            Whitebox.invokeMethod(service, "validParams", dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARMS_ERR, e.getExMsgId());
        }
        dto.setCustomerCode("1");
        try {
            Whitebox.invokeMethod(service, "validParams", dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARMS_ERR, e.getExMsgId());
        }
        dto.setCustomerComponentType("1");
        try {
            Whitebox.invokeMethod(service, "validParams", dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARMS_ERR, e.getExMsgId());
        }
        dto.setRequireMaterialUpload("1");
        try {
            Whitebox.invokeMethod(service, "validParams", dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARMS_ERR, e.getExMsgId());
        }
        dto.setIsPricedMaterial("1");
        try {
            Whitebox.invokeMethod(service, "validParams", dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARMS_ERR, e.getExMsgId());
        }
    }

    @Test
    public void test_deleteById() {
        when(packingListConfigRepository.deleteById(any())).thenReturn(1);
        Assert.assertEquals(1, service.deleteById("1"));
    }

    @Test
    public void test_update() {
        PackingListConfigDTO packingListConfigDTO = new PackingListConfigDTO();
        packingListConfigDTO.setCustomerCode("1");
        packingListConfigDTO.setCustomerComponentType("1");
        packingListConfigDTO.setIsPricedMaterial("1");
        packingListConfigDTO.setPriority(1);
        packingListConfigDTO.setRequireMaterialUpload("1");
        when(packingListConfigRepository.countExistPackListInfo(any())).thenReturn(new PackingListConfigDTO());
        try {
            service.update(packingListConfigDTO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PACK_LIST_IS_EXISTED, e.getExMsgId());
        }
        when(packingListConfigRepository.countExistPackListInfo(any())).thenReturn(null);
        when(packingListConfigRepository.update(any())).thenReturn(1);
        Assert.assertEquals(1, service.update(packingListConfigDTO));
    }


    @Test
    public void test_save() {
        PackingListConfigDTO packingListConfigDTO = new PackingListConfigDTO();
        packingListConfigDTO.setCustomerCode("1");
        packingListConfigDTO.setCustomerComponentType("1");
        packingListConfigDTO.setIsPricedMaterial("1");
        packingListConfigDTO.setPriority(1);
        packingListConfigDTO.setRequireMaterialUpload("1");
        when(packingListConfigRepository.countExistPackListInfo(any())).thenReturn(new PackingListConfigDTO());
        try {
            service.save(packingListConfigDTO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PACK_LIST_IS_EXISTED, e.getExMsgId());
        }
        when(packingListConfigRepository.countExistPackListInfo(any())).thenReturn(null);
        when(packingListConfigRepository.save(any())).thenReturn(1);
        Assert.assertEquals(1, service.save(packingListConfigDTO));
    }

    @Test
    public void test_queryPage() throws Exception {
        List<PackingListConfigDTO> list = new ArrayList<>();
        PackingListConfigDTO packingListConfigDTO = new PackingListConfigDTO();
        packingListConfigDTO.setCreateBy("10351947");
        packingListConfigDTO.setLastUpdatedBy("10351947");
        PackingListConfigDTO packingListConfigDTO1 = new PackingListConfigDTO();
        packingListConfigDTO1.setCreateBy("10351946");
        packingListConfigDTO1.setLastUpdatedBy("10351946");
        list.add(packingListConfigDTO);
        list.add(packingListConfigDTO1);
        when(packingListConfigRepository.selectPage(any())).thenReturn(list);
        Map<String, HrmPersonInfoDTO> data = new HashMap<>();
        data.put("10351947", new HrmPersonInfoDTO());
        when(hrmUserCenterService.getHrmPersonInfo(anyList())).thenReturn(data);
        List<SysLookupValues> customerNameList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setAttribute2("111");
        sysLookupValues.setLookupMeaning("阿里");
        SysLookupValues sysLookupValues2 = new SysLookupValues();
        sysLookupValues2.setAttribute2("111");
        sysLookupValues2.setLookupMeaning("阿里");
        customerNameList.add(sysLookupValues);
        customerNameList.add(sysLookupValues2);
        when(sysLookupValuesService.findByLookupType(any())).thenReturn(customerNameList);
        List<SysLookupValues> customerCopomentList = new ArrayList<>();
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupMeaning("CPU");
        sysLookupValues1.setDescriptionChin("CPU");
        SysLookupValues sysLookupValues3 = new SysLookupValues();
        sysLookupValues3.setLookupMeaning("CPU");
        sysLookupValues3.setDescriptionChin("CPU");
        customerCopomentList.add(sysLookupValues1);
        customerCopomentList.add(sysLookupValues3);
        when(sysLookupValuesService.selectValuesByType(any())).thenReturn(customerCopomentList);
        Assert.assertNotNull(service.queryPage(packingListConfigDTO));
    }
}
