/*Started by AICoder, pid:82925u5862u537314e0c0beff126dd3c4521e6df*/
package com.zte.application.impl;

import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.zte.domain.model.PushStdModelSnDataRepository;
import com.zte.interfaces.dto.PushStdModelSnDataDTO;
import com.zte.interfaces.dto.PushStdModelSnDataQueryDTO;
import com.zte.itp.msa.core.model.PageRows;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class PushStdModelSnDataServiceImpl_queryPushStdModelSnData_1_Test {

    @Mock
    private PushStdModelSnDataRepository pushStdModelSnDataRepository;

    @InjectMocks
    private PushStdModelSnDataServiceImpl pushStdModelSnDataService;

    private PushStdModelSnDataQueryDTO query;

    @Before
    public void setUp() {
        query = new PushStdModelSnDataQueryDTO();
    }

    @Test
    public void testQueryPushStdModelSnData_DefaultPageNumAndPageSize() {
        // Given
        query.setSnFrom("test");
        query.setLastUpdatedDateStart(new Date());
        Page<PushStdModelSnDataDTO> page = new Page<>(1, 1, true);
        page.setTotal(1);
        page.addAll(Lists.newArrayList(new PushStdModelSnDataDTO()));
        when(pushStdModelSnDataRepository.selectByQuery(any(PushStdModelSnDataQueryDTO.class)))
                .thenReturn(page);

        // When
        PageRows<PushStdModelSnDataDTO> result = pushStdModelSnDataService.queryPushStdModelSnData(query);

        // Then
        assertEquals(1, result.getCurrent());
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getRows().size());
    }

    @Test
    public void testQueryPushStdModelSnData_CustomPageNumAndPageSize() {
        // Given
        query.setSnFrom("test");
        query.setPageNum(2);
        query.setPageSize(10);
        Page<PushStdModelSnDataDTO> page = new Page<>(2, 10, true);
        page.setTotal(100);
        page.addAll(Lists.newArrayList(new PushStdModelSnDataDTO()));
        when(pushStdModelSnDataRepository.selectByQuery(any(PushStdModelSnDataQueryDTO.class)))
                .thenReturn(page);

        // When
        PageRows<PushStdModelSnDataDTO> result = pushStdModelSnDataService.queryPushStdModelSnData(query);

        // Then
        assertEquals(2, result.getCurrent());
        assertEquals(100, result.getTotal());
        assertEquals(1, result.getRows().size());
    }

    @Test
    public void testQueryPushStdModelSnData_NullPageNumAndPageSize() {
        // Given
        query.setPageNum(null);
        query.setPageSize(null);
        Page<PushStdModelSnDataDTO> page = new Page<>(1, 500, true);
        page.setTotal(1);
        page.addAll(Lists.newArrayList(new PushStdModelSnDataDTO()));
        when(pushStdModelSnDataRepository.selectByQuery(any(PushStdModelSnDataQueryDTO.class)))
                .thenReturn(page);

        // When
        PageRows<PushStdModelSnDataDTO> result = pushStdModelSnDataService.queryPushStdModelSnData(query);

        // Then
        assertEquals(1, result.getCurrent());
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getRows().size());
    }

    @Test
    public void testQueryPushStdModelSnData_InvalidPageNumAndPageSize() {
        // Given
        query.setPageNum(0);
        query.setPageSize(600);
        Page<PushStdModelSnDataDTO> page = new Page<>(1, 500, true);
        page.setTotal(1);
        page.addAll(Lists.newArrayList(new PushStdModelSnDataDTO()));
        when(pushStdModelSnDataRepository.selectByQuery(any(PushStdModelSnDataQueryDTO.class)))
                .thenReturn(page);

        // When
        PageRows<PushStdModelSnDataDTO> result = pushStdModelSnDataService.queryPushStdModelSnData(query);

        // Then
        assertEquals(1, result.getCurrent());
        assertEquals(1, result.getTotal());
        assertEquals(1, result.getRows().size());
    }
}
/*Ended by AICoder, pid:82925u5862u537314e0c0beff126dd3c4521e6df*/