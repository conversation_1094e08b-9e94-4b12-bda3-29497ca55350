/*Started by AICoder, pid:i2ca7o777fs4d2b14a9e08bde083a52a87141501*/
package com.zte.application.impl;
import com.alibaba.fastjson.JSON;
import com.zte.common.model.MessageId;
import com.zte.domain.model.ResourceInfoRepository;
import com.zte.infrastructure.remote.CallingB2BUniversalRemoteService;
import com.zte.interfaces.dto.ModelNumberLicenceInfoDTO;
import com.zte.interfaces.dto.ModelNumberParamDTO;
import com.zte.interfaces.dto.ModelNumberResponseDTO;
import com.zte.interfaces.dto.ResourceInfoEntityDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.redis.RedisHelper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@PrepareForTest({RedisHelper.class,SpringContextUtil.class})
@RunWith(PowerMockRunner.class)
public class ResourceInfoServiceImplByAITest {
    @InjectMocks private ResourceInfoServiceImpl resourceInfoService;
    @Mock private ResourceInfoRepository resourceInfoRepository;
    @Mock private CallingB2BUniversalRemoteService callingB2BUniversalRemoteService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    @Test(timeout = 8000)
    public void testUpdateModelNumber() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class, SpringContextUtil.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(false);
        try {
            resourceInfoService.updateModelNumber("123");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.OPERATION_IN_PROGRESS_PLEASE_WAIT, e.getMessage());
        }
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        try {
            resourceInfoService.updateModelNumber("123");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        List<ResourceInfoEntityDTO> resourceNoList = new ArrayList<>();
        resourceNoList.add(new ResourceInfoEntityDTO(){{setResourceNo("no1");}});
        PowerMockito.when(resourceInfoRepository.getModelNumberEmptyList(eq(""),eq("123"))).thenReturn(resourceNoList);
        //PowerMockito.when(resourceInfoRepository.getModelNumberEmptyList(eq("no1"),eq("123"))).thenReturn(resourceNoList);
        resourceInfoService.updateModelNumber("123");
        Assert.assertNotNull(resourceNoList);
    }
    /*Ended by AICoder, pid:i2ca7o777fs4d2b14a9e08bde083a52a87141501*/

    /*Started by AICoder, pid:y7d4ct37bf093f014f700b6b61d81d540749fcf0*/
    @Test(timeout = 8000)
    public void testBatchUpdateEmptyList() {
        List<ResourceInfoEntityDTO> updateList = new ArrayList<>();
        resourceInfoService.batchUpdate(updateList);
        Assert.assertNotNull(updateList);
    }

    @Test(timeout = 8000)
    public void testBatchUpdateSingleBatch() {
        List<ResourceInfoEntityDTO> updateList = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            updateList.add(new ResourceInfoEntityDTO());
        }
        resourceInfoService.batchUpdate(updateList);
        Assert.assertNotNull(updateList);
    }
    /*Ended by AICoder, pid:y7d4ct37bf093f014f700b6b61d81d540749fcf0*/


    /*Started by AICoder, pid:f171fo8152k905014aad0b5ef0bb2d432b894a4a*/
    @Test(timeout = 8000)
    public void testGetLicenceInfo_WithModelNumber() throws Exception {
        ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
        ModelNumberParamDTO dto = new ModelNumberParamDTO();
        List<ResourceInfoEntityDTO> updateList = new ArrayList<>();
        // TODO: Setup the mock call to callingB2BUniversalRemoteService.callB2B method
        PowerMockito.when(callingB2BUniversalRemoteService.callB2B(anyString(), any())).thenReturn("");
        resourceInfoService.updateLicenceInfo(resourceInfoEntityDTO, dto);
        // TODO: Verify the result
        Assert.assertNotNull(updateList);
    }

    @Test(timeout = 8000)
    public void testGetLicenceInfo_WithoutModelNumber() throws Exception {
        ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
        ModelNumberParamDTO dto = new ModelNumberParamDTO();
        List<ResourceInfoEntityDTO> updateList = new ArrayList<>();

        // TODO: Setup the mock call to callingB2BUniversalRemoteService.callB2B method
        PowerMockito.when(callingB2BUniversalRemoteService.callB2B(anyString(), any())).thenReturn(JSON.toJSONString(new ModelNumberResponseDTO()));
        resourceInfoService.updateLicenceInfo(resourceInfoEntityDTO, dto);
        // TODO: Verify the result
        Assert.assertNotNull(updateList);
        PowerMockito.when(callingB2BUniversalRemoteService.callB2B(anyString(), any())).thenReturn(JSON.toJSONString(new ModelNumberResponseDTO(){{setLicenceInfo(new ModelNumberLicenceInfoDTO());}}));
        resourceInfoService.updateLicenceInfo(resourceInfoEntityDTO, dto);
        Assert.assertNotNull(updateList);
        PowerMockito.when(callingB2BUniversalRemoteService.callB2B(anyString(), any())).thenReturn("{\"licenceInfo\":{\"EquipmentNum\":\"number\"}}");
        resourceInfoService.updateLicenceInfo(resourceInfoEntityDTO, dto);
        Assert.assertNotNull(updateList);
    }
    /*Ended by AICoder, pid:f171fo8152k905014aad0b5ef0bb2d432b894a4a*/

    /*Started by AICoder, pid:f7c5fs823a33f5314ff80b4640e2308f67d238e6*/
    @Test(timeout = 8000)
    public void testGetLicenceInfo_Success() throws Exception {
        ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
        ModelNumberParamDTO dto = new ModelNumberParamDTO();
        List<ResourceInfoEntityDTO> updateList = new ArrayList<>();
        ModelNumberResponseDTO modelNumberResponseDTO = new ModelNumberResponseDTO();
        modelNumberResponseDTO.setLicenceInfo(new ModelNumberLicenceInfoDTO());
        modelNumberResponseDTO.getLicenceInfo().setEquipmentNum("equipmentNum");

        String result = "{}"; // Assuming that the result is a JSON string
        PowerMockito.when(callingB2BUniversalRemoteService.callB2B(anyString(), any())).thenReturn(JSON.toJSONString(modelNumberResponseDTO));
        resourceInfoService.updateLicenceInfo(resourceInfoEntityDTO, dto);
        assertNotNull(resourceInfoEntityDTO);
    }

    @Test(timeout = 8000)
    public void testGetLicenceInfo_NoEquipmentNum() throws Exception {
        ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
        ModelNumberParamDTO dto = new ModelNumberParamDTO();
        List<ResourceInfoEntityDTO> updateList = new ArrayList<>();
        ModelNumberResponseDTO modelNumberResponseDTO = new ModelNumberResponseDTO();
        modelNumberResponseDTO.setLicenceInfo(new ModelNumberLicenceInfoDTO());
        modelNumberResponseDTO.getLicenceInfo().setEquipmentNum("");

        String result = "{}"; // Assuming that the result is a JSON string

        resourceInfoService.updateLicenceInfo(resourceInfoEntityDTO, dto);

        assertNull(resourceInfoEntityDTO.getModelNumber());
    }

    @Test(timeout = 8000)
    public void testGetLicenceInfo_ExceptionThrown() throws Exception {
        ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
        ModelNumberParamDTO dto = new ModelNumberParamDTO();
        List<ResourceInfoEntityDTO> updateList = new ArrayList<>();

        when(callingB2BUniversalRemoteService.callB2B(anyString(), any())).thenThrow(new Exception("Error"));

        try {
            resourceInfoService.updateLicenceInfo(resourceInfoEntityDTO, dto);
            fail("Should have thrown an exception");
        } catch (Exception e) {
            // Test passed
        }
    }
    /*Ended by AICoder, pid:f7c5fs823a33f5314ff80b4640e2308f67d238e6*/
}