package com.zte.application.impl;
/* Started by AICoder, pid:2cdb8af4593648e08481f775236498e0 */

import com.zte.application.CustomerDataLogService;
import com.zte.application.CustomerItemsService;
import com.zte.application.PushBoardDataHeadService;
import com.zte.application.PushBoardDataProcessService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.PushBoardDataDetailRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.MdsAoiTestDataDTO;
import com.zte.interfaces.dto.MdsRepairInfoResponseDTO;
import com.zte.interfaces.dto.PushBoardDataDetailDTO;
import com.zte.interfaces.dto.PushBoardDataHeadDTO;
import com.zte.interfaces.dto.PushBoardDataProcessDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.NoticeCenterUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class PushBoardDataDetailServiceImplTest extends BaseTestCase {
    @InjectMocks
    private PushBoardDataDetailServiceImpl service;
    @Mock
    private PushBoardDataDetailRepository pushBoardDataDetailRepository;
    @Mock
    private PushBoardDataProcessService pushBoardDataProcessService;
    @Mock
    private MdsRemoteService mdsRemoteService;
    @Mock
    private CustomerItemsService customerItemsService;

    @Mock
    private CustomerDataLogService customerDataLogService;
    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private NoticeCenterUtils noticeCenterUtils;

    @Mock
    private PushBoardDataHeadService pushBoardDataHeadService;

    /* Started by AICoder, pid:bf38d894a7h445c149100b18f018b8765559482d */
    @Test
    public void merge() {
        List<PushBoardDataDetailDTO> list = new ArrayList<>();
        int count = service.merge(list);
        Assert.assertEquals(0, count);

        PushBoardDataDetailDTO dto = new PushBoardDataDetailDTO();
        dto.setSn("7***********");
        dto.setProdplanId("7777666");
        list.add(dto);

        List<String> existSnList = new ArrayList<>();
        when(pushBoardDataDetailRepository.getExistSn(Mockito.anyList())).thenReturn(existSnList);
        when(pushBoardDataDetailRepository.batchInsert(Mockito.anyList())).thenReturn(1);
        count = service.merge(list);
        Assert.assertEquals(1, count);

        existSnList.add("7***********");
        count = service.merge(list);
        Assert.assertEquals(0, count);
    }

    @Test
    public void pushAoiDataToB2B() {
        List<PushBoardDataDetailDTO> detailList = new ArrayList<>();
        when(pushBoardDataDetailRepository.getNeedPushSn(Mockito.any(), Mockito.eq(""), Mockito.anyInt())).thenReturn(detailList);
        service.pushAoiDataToB2B(0);

        PushBoardDataDetailDTO detailDTO = new PushBoardDataDetailDTO();
        detailDTO.setSn("7***********");
        detailDTO.setCreateDate(new Date());
        detailList.add(detailDTO);
        List<PushBoardDataProcessDTO> needPushDataList = new ArrayList<>();
        when(pushBoardDataProcessService.getAndInitNeedPushData(Mockito.anyList(), Mockito.anyString())).thenReturn(needPushDataList);
        service.pushAoiDataToB2B(0);

        PushBoardDataProcessDTO pushBoardDataProcessDTO = new PushBoardDataProcessDTO();
        pushBoardDataProcessDTO.setId("1");
        pushBoardDataProcessDTO.setBusinessType("AOI");
        pushBoardDataProcessDTO.setSn("7***********");
        pushBoardDataProcessDTO.setProdplanId("7777666");
        pushBoardDataProcessDTO.setItemNo("123456789123ABC");
        pushBoardDataProcessDTO.setFactoryId(58);
        pushBoardDataProcessDTO.setPushFailCount(0);
        needPushDataList.add(pushBoardDataProcessDTO);

        List<CustomerItemsDTO> customerItemsDTOS = new ArrayList<>();
        when(customerItemsService.queryListByZteCodes(Mockito.anyList())).thenReturn(customerItemsDTOS);
        List<MdsAoiTestDataDTO> aoiTestDataList = new ArrayList<>();
        when(mdsRemoteService.getAoiTestData(Mockito.anyString())).thenReturn(aoiTestDataList);
        try {
            service.pushAoiDataToB2B(1);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }

        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any())).thenReturn(sysLookupValues);
        try {
            service.pushAoiDataToB2B(1);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
        sysLookupValues.setLookupMeaning("123456");
        service.pushAoiDataToB2B(1);

        Map<String, String> sideToStationMap = new HashMap<>();
        sideToStationMap.put("SMT-A", "AOI-T");
        sideToStationMap.put("SMT-B", "AOI-B");
        ReflectionTestUtils.setField(service, "sideToStationMap", sideToStationMap);
        Map<Integer, String> factoryIdToNameMap = new HashMap<>();
        factoryIdToNameMap.put(58, "南京工厂ZTE101");
        ReflectionTestUtils.setField(service, "factoryIdToNameMap", factoryIdToNameMap);
        ReflectionTestUtils.setField(service, "pushFailThreshold", 3);
        service.pushAoiDataToB2B(1);

        MdsAoiTestDataDTO mdsAoiTestDataDTO = new MdsAoiTestDataDTO();
        mdsAoiTestDataDTO.setStationName("AOI-T");
        mdsAoiTestDataDTO.setResult("Fail");
        aoiTestDataList.add(mdsAoiTestDataDTO);
        pushBoardDataProcessDTO.setPushFailCount(3);
        mdsAoiTestDataDTO.setFinishedTime(new Date());
        service.pushAoiDataToB2B(1);

        pushBoardDataProcessDTO.setSides("SMT-B");
        mdsAoiTestDataDTO.setStationName("AOI-B");
        mdsAoiTestDataDTO.setResult("Pass");
        service.pushAoiDataToB2B(1);

        mdsAoiTestDataDTO.setResult("Fail");
        mdsAoiTestDataDTO.setInspectId("111");
        MdsAoiTestDataDTO mdsAoiTestDataDTO1 = new MdsAoiTestDataDTO();
        mdsAoiTestDataDTO1.setStationName("AOI-B");
        mdsAoiTestDataDTO1.setResult("Pass");
        mdsAoiTestDataDTO1.setFinishedTime(new Date());
        aoiTestDataList.add(mdsAoiTestDataDTO1);
        List<MdsRepairInfoResponseDTO> repairInfoList = new ArrayList<>();
        when(mdsRemoteService.getRepairInfoBySn(Mockito.anyString())).thenReturn(repairInfoList);
        service.pushAoiDataToB2B(1);

        MdsRepairInfoResponseDTO repairInfo = new MdsRepairInfoResponseDTO();
        repairInfo.setInspectId("222");
        repairInfoList.add(repairInfo);
        service.pushAoiDataToB2B(1);

        repairInfo.setInspectId("111");
        service.pushAoiDataToB2B(1);

        mdsAoiTestDataDTO1.setStartedTime(new Date());
        service.pushAoiDataToB2B(1);

        repairInfo.setNewComponentSn("123456");
        service.pushAoiDataToB2B(1);
        Assert.assertEquals(0, 0);
    }
    /* Ended by AICoder, pid:bf38d894a7h445c149100b18f018b8765559482d */

    @Test
    public void testSyncDetailPushStatus() {
        // 构造参数
        PushBoardDataProcessDTO param = new PushBoardDataProcessDTO();
        param.setBeforeDay(0);

        // 1. 测试无数据场景
        List<PushBoardDataDetailDTO> detailList = new ArrayList<>();
        when(pushBoardDataDetailRepository.getNotPushedList(Mockito.any(), Mockito.eq(""), Mockito.anyInt())).thenReturn(detailList);
        service.syncDetailPushStatus(param);

        param.setBeforeDay(30);
        // 2. 测试有数据但不满足更新条件
        PushBoardDataDetailDTO detailDTO = new PushBoardDataDetailDTO();
        detailDTO.setSn("SN001");
        detailDTO.setLastUpdatedDate(new Date());
        detailList.add(detailDTO);
        // 只返回一次，后续返回空，模拟分页
        when(pushBoardDataDetailRepository.getNotPushedList(Mockito.any(), Mockito.eq(""), Mockito.anyInt()))
                .thenReturn(detailList)
                .thenReturn(new ArrayList<>());
        // 业务类型
        List<String> needPushBusiTypeList = new ArrayList<>();
        needPushBusiTypeList.add("AOI");
        needPushBusiTypeList.add("ICT");
        ReflectionTestUtils.setField(service, "needPushBusiTypeList", needPushBusiTypeList);
        // 进程数据，只有一条业务类型，不能更新
        List<PushBoardDataProcessDTO> processList = new ArrayList<>();
        PushBoardDataProcessDTO processDTO = new PushBoardDataProcessDTO();
        processDTO.setSn("SN001");
        processDTO.setBusinessType("AOI");
        processList.add(processDTO);
        when(pushBoardDataProcessService.getDataBySnAndBusinessType(Mockito.anyList(), Mockito.anyList())).thenReturn(processList);
        service.syncDetailPushStatus(param);

        // 3. 测试满足所有业务类型，触发更新
        PushBoardDataProcessDTO processDTO2 = new PushBoardDataProcessDTO();
        processDTO2.setSn("SN001");
        processDTO2.setBusinessType("ICT");
        processList.add(processDTO2);
        service.syncDetailPushStatus(param);
        Assert.assertTrue(true);
    }

    @Test
    public void testSyncHeadPushStatus() {
        // 构造参数
        PushBoardDataProcessDTO param = new PushBoardDataProcessDTO();
        param.setBeforeDay(0);

        // 1. 无数据场景
        List<PushBoardDataHeadDTO> headList = new ArrayList<>();
        when(pushBoardDataHeadService.getNotPushedList(Mockito.any(), Mockito.eq(""), Mockito.anyInt())).thenReturn(headList);
        service.syncHeadPushStatus(param);

        // 2. 有数据但无已完成批次
        PushBoardDataHeadDTO headDTO = new PushBoardDataHeadDTO();
        headDTO.setProdplanId("PLAN001");
        headDTO.setLastUpdatedDate(new Date());
        headList.add(headDTO);
        when(pushBoardDataHeadService.getNotPushedList(Mockito.any(), Mockito.eq(""), Mockito.anyInt()))
            .thenReturn(headList)
            .thenReturn(new ArrayList<>());
        when(pushBoardDataDetailRepository.getPushDoneProdplanId(Mockito.anyList())).thenReturn(new ArrayList<>());
        service.syncHeadPushStatus(param);

        // 3. 有数据且有已完成批次
        List<String> doneList = new ArrayList<>();
        doneList.add("PLAN001");
        when(pushBoardDataDetailRepository.getPushDoneProdplanId(Mockito.anyList())).thenReturn(doneList);
        service.syncHeadPushStatus(param);
        Assert.assertTrue(true);
    }
}
