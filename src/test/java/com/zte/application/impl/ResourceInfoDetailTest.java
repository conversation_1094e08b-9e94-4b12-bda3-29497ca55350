package com.zte.application.impl;

import cn.hutool.core.lang.hash.Hash;
import com.zte.common.CommonUtils;
import com.zte.common.ExcelUtils;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.common.utils.NumConstant;
import com.zte.common.utils.constant.SnCaConstant;
import com.zte.domain.model.ResourceInfoDetailRepository;
import com.zte.domain.model.ResourceInfoRepository;
import com.zte.interfaces.dto.ResourceInfoDetailDTO;
import com.zte.interfaces.dto.ResourceInfoDetailExportDTo;
import com.zte.interfaces.dto.ResourceInfoEntityDTO;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.*;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.BeanUtils;

import java.io.*;
import java.util.*;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>  魏振东
 * @description: TODO
 * @date 2023/9/20 下午7:42
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BeanUtils.class, MESHttpHelper.class, UUID.class, CommonUtils.class, ExcelUtils.class, SnCaConstant.class, EasyExcelUtils.class,CommonUtils.class,FileUtils.class})
public class ResourceInfoDetailTest {

    @InjectMocks
    ResourceInfoDetailServiceImpl service;

    @Mock
    ResourceInfoDetailRepository repository;

    @Mock
    ResourceInfoServiceImpl infoService;

    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Mock
    ResourceInfoRepository resourceInfoRepository;

    @Mock
    EmailUtils emailUtils;

    @Test
    public void batchInserOrUpdate(){
        List<ResourceInfoDetailDTO> list = new ArrayList<>();
        list.add(new ResourceInfoDetailDTO(){{setResourceNo("no1");}});
        Assert.assertEquals(NumConstant.NUM_ZERO,service.batchInserOrUpdate(null));

        PowerMockito.when(repository.batchInserOrUpdate(any())).thenReturn(NumConstant.NUM_ONE);
        Assert.assertEquals(NumConstant.NUM_ONE,service.batchInserOrUpdate(list));
    }
    @Test
    public void pageTest(){
        ResourceInfoDetailDTO dto = new ResourceInfoDetailDTO();
        service.page(dto);
        PowerMockito.when(repository.page(any())).thenReturn(new ArrayList<>());
        dto.setResourceNo("123");
        service.page(dto);
        dto.setStatus("0");
        ResourceInfoEntityDTO info = new ResourceInfoEntityDTO();
        PowerMockito.when(infoService.findDeviceByResourceNo(any())).thenReturn(null);
        service.page(dto);
        PowerMockito.when(infoService.findDeviceByResourceNo(any())).thenReturn(info);
        service.page(dto);
        info.setResourceStatus("LOSE_EFFICACY");
        PowerMockito.when(infoService.findDeviceByResourceNo(any())).thenReturn(info);
        service.page(dto);
        dto.setStatus("9");
        PowerMockito.when(infoService.findDeviceByResourceNo(any())).thenReturn(info);
        service.page(dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void exportTxtTest() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.anyString())).thenReturn("test");
        RetCode retCode = PowerMockito.mock(RetCode.class);
        PowerMockito.whenNew(RetCode.class).withAnyArguments().thenReturn(retCode);

        ResourceInfoDetailExportDTo resourceInfoDetailExportDTo = new ResourceInfoDetailExportDTo();
        List<String> list = new ArrayList<>();
        list.add("00-0000-000811");

        resourceInfoDetailExportDTo.setResourceNos(list);
        resourceInfoDetailExportDTo.setEmailTo("10331519");
        service.exportTxt(resourceInfoDetailExportDTo);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void exportTxtThreadTest() throws Exception {
        ResourceInfoDetailExportDTo resourceInfoDetailExportDTo = new ResourceInfoDetailExportDTo();
        List<String> list = new ArrayList<>();
        resourceInfoDetailExportDTo.setResourceNos(list);
        Whitebox.invokeMethod(service,"exportTxtThread",resourceInfoDetailExportDTo);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
        list.add("00-0000-000811");
        resourceInfoDetailExportDTo.setResourceNos(list);
        resourceInfoDetailExportDTo.setEmailTo("10331519");

        PowerMockito.when(repository.countByResourceNo(any())).thenReturn(5000);
        PowerMockito.when(repository.pageByResourceNo(any())).thenReturn(new ArrayList<>());
        PowerMockito.when(emailUtils.sendMail(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(true);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.anyString())).thenReturn("test");
        try {
            Whitebox.invokeMethod(service,"exportTxtThread",resourceInfoDetailExportDTo);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.SERVERERROR_MSGID,e.getMessage());
        }
    }

    @Test
    public void writerDataAndUploadTest() throws Exception {
        ResourceInfoDetailExportDTo resourceInfoDetailExportDTo = new ResourceInfoDetailExportDTo();
        List<String> list = new ArrayList<>();
        list.add("00-0000-000811");
        resourceInfoDetailExportDTo.setResourceNos(list);
        resourceInfoDetailExportDTo.setEmailTo("10331519");

        PowerMockito.when(repository.pageByResourceNo(any())).thenReturn(new ArrayList<>());
        PowerMockito.when(cloudDiskHelper.fileUpload(anyString(), anyString(), anyInt())).thenReturn("123");
        PowerMockito.when(cloudDiskHelper.getFileDownloadUrl(anyString(), any(), anyString())).thenReturn("http://123");
        PowerMockito.when(emailUtils.sendMail(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(true);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.anyString())).thenReturn("test");
        File file = new File("test.txt");
        OutputStreamWriter osw = new OutputStreamWriter(new FileOutputStream(file, false), MpConstant.BYTE_UTF_8);
        BufferedWriter txtFileOutputStream = new BufferedWriter(osw, NumConstant.NUM_1024);
        Map<String, Object> map = new HashMap<>();
        map.put("status","8");
        map.put("resourceNo","00-0000-000811");
        map.put("rows", Constant.INT_5000);
        int times = 1;
        String fileName = "00-0000-000811.txt";
        StringBuilder sb = new StringBuilder();
        try {
            Whitebox.invokeMethod(service,"writerDataAndUpload", txtFileOutputStream, map, 1, fileName, sb);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.SERVERERROR_MSGID,e.getMessage());
        }
        List<String> resourceNums = new ArrayList<>();
        resourceNums.add("214221126050000");
        PowerMockito.when(repository.pageByResourceNo(any())).thenReturn(resourceNums);
        try {
            Whitebox.invokeMethod(service,"writerDataAndUpload", txtFileOutputStream, map, 2, fileName, sb);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.SERVERERROR_MSGID,e.getMessage());
        }
    }

    @Test
    public void extractedTest() throws Exception {
        BufferedWriter txtFileOutputStream = null;
        OutputStreamWriter osw = null;
        Whitebox.invokeMethod(service,"extracted",txtFileOutputStream);

        osw = new OutputStreamWriter(new FileOutputStream(FileUtils.createFilePathAndCheck("test.txt")), MpConstant.BYTE_UTF_8);
        txtFileOutputStream = new BufferedWriter(osw, NumConstant.NUM_1024);
        Whitebox.invokeMethod(service,"extracted",txtFileOutputStream);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void writerDataTest() throws Exception {
        File txtFile = new File("test.txt");
        OutputStream out = new FileOutputStream(txtFile, false);
        OutputStreamWriter osw = new OutputStreamWriter(out, MpConstant.BYTE_UTF_8);
        BufferedWriter txtFileOutputStream = new BufferedWriter(osw, NumConstant.NUM_1024);
        List<String> list = new ArrayList<>();
        list.add("a");
        list.add("b");
        Whitebox.invokeMethod(service,"writerData",txtFileOutputStream,list);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

}
