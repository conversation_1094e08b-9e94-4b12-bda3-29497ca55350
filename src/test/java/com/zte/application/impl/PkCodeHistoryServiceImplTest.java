package com.zte.application.impl;

import com.zte.application.HrmUserCenterService;
import com.zte.application.PkCodeInfoService;
import com.zte.application.StItemBarcodeService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.PkCodeHistory;
import com.zte.domain.model.PkCodeHistoryRepository;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.PkCodeHistoryDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@PrepareForTest({DatawbRemoteService.class})
public class PkCodeHistoryServiceImplTest extends BaseTestCase {
    @InjectMocks
    private PkCodeHistoryServiceImpl pkCodeHistoryService;
    @Mock
    private PkCodeHistoryRepository pkCodeHistoryRepository;
    @Mock
    private BProdBomHeaderServiceImpl prodBomHeaderService;
    @Mock
    private StItemBarcodeService stItemBarcodeService;
    @Mock
    private PkCodeInfoService pkCodeInfoService;
    @Mock
    private HrmUserCenterService hrmUserCenterService;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;

    /* Started by AICoder, pid:4de2aeea93ide2b1483e0a897055ce233059a06f */
    @Test
    public void syncReelIdHistoryFormInfo() throws Exception {
        // Mock static method and set field value
        PowerMockito.mockStatic(DatawbRemoteService.class);
        PowerMockito.field(PkCodeHistoryServiceImpl.class, "syncReelIdHistoryMinBefore")
                .set(pkCodeHistoryService, 1);

        // Test case 1: No configuration scenario
        PkCodeHistory pkCodeHistory = new PkCodeHistory();
        try {
            pkCodeHistoryService.syncReelIdHistoryFormInfo(pkCodeHistory);
            Assert.fail("Expected exception was not thrown");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        // Test case 2: Configuration scenario
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("2024-09-09 12:00:00");
        PowerMockito.when(sysLookupValuesRepository.selectValuesByLookUpTypeAndAttribute1(Mockito.any(), Mockito.anyString()))
                .thenReturn(sysLookupValues);
        pkCodeHistoryService.syncReelIdHistoryFormInfo(pkCodeHistory);

        // Test case 3: Data retrieval scenario
        List<PkCodeHistory> pkCodeHistories = Collections.singletonList(new PkCodeHistory());
        Page<PkCodeHistory> page = new Page<>();
        PowerMockito.when(DatawbRemoteService.selectByPageReelHis(Mockito.any()))
                .thenReturn(page);
        pkCodeHistoryService.syncReelIdHistoryFormInfo(pkCodeHistory);

        page.setRows(pkCodeHistories);
        pkCodeHistoryService.syncReelIdHistoryFormInfo(pkCodeHistory);

        pkCodeHistory = new PkCodeHistory();
        sysLookupValues.setLookupMeaning("2025-09-09 12:00:00");
        pkCodeHistoryService.syncReelIdHistoryFormInfo(pkCodeHistory);
    }

    /* Ended by AICoder, pid:4de2aeea93ide2b1483e0a897055ce233059a06f */

    @Test
    public void getListByUuid() {
        PkCodeHistoryDTO pkCodeHistoryDTO = new PkCodeHistoryDTO();
        List<PkCodeInfo> resultList =new ArrayList<>();
        PkCodeInfo pkCodeHistory = new PkCodeInfo();
        pkCodeHistory.setHistoryId("11");
        resultList.add(pkCodeHistory);
        PowerMockito.when(pkCodeHistoryRepository.getListByUuid(Mockito.any())).thenReturn(resultList);
        PowerMockito.when(stItemBarcodeService.pkCodeChange(Mockito.any())).thenReturn(resultList);
        Assert.assertNotNull(pkCodeHistoryService.getListByUuid(pkCodeHistoryDTO));
    }

    @Test
    public void reelidRegisterPageQuery() throws Exception {
        PkCodeHistoryDTO dto = new PkCodeHistoryDTO();
        List<PkCodeInfo> list = new ArrayList<>();
        PowerMockito.when(pkCodeHistoryRepository.reelidRegisterPageQuery(Mockito.any())).thenReturn(list);
        Page<PkCodeInfo> page = pkCodeHistoryService.reelidRegisterPageQuery(dto);
        Assert.assertTrue(CollectionUtils.isEmpty(page.getRows()));

        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setObjectId("PK123");
        pkCodeInfo.setCreateBy("00000000");
        pkCodeInfo.setLastUpdatedBy("11111111");
        list.add(pkCodeInfo);

        pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setObjectId("PK234");
        list.add(pkCodeInfo);

        Map<String, HrmPersonInfoDTO> hrmPersonInfo = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("000");
        hrmPersonInfo.put("00000000", hrmPersonInfoDTO);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.any())).thenReturn(hrmPersonInfo);

        page = pkCodeHistoryService.reelidRegisterPageQuery(dto);
        Assert.assertTrue(!CollectionUtils.isEmpty(page.getRows()));

        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.any())).thenThrow(new Exception("error"));
        page = pkCodeHistoryService.reelidRegisterPageQuery(dto);
        Assert.assertTrue(!CollectionUtils.isEmpty(page.getRows()));
    }

    @Test
    public void getCountByUuid() {
        PkCodeHistoryDTO pkCodeHistoryDTO = new PkCodeHistoryDTO();
        PowerMockito.when(pkCodeHistoryRepository.getCountByUuid(Mockito.any())).thenReturn(new Long("1"));
        Assert.assertNotNull(pkCodeHistoryService.getCountByUuid(pkCodeHistoryDTO));
    }

    @Test
    public void getPageByUuid() {
        PkCodeHistoryDTO pkCodeHistoryDTO = new PkCodeHistoryDTO();
        List<PkCodeInfo> resultList =new ArrayList<>();
        PkCodeInfo pkCodeHistory = new PkCodeInfo();
        pkCodeHistory.setHistoryId("11");
        resultList.add(pkCodeHistory);
        PowerMockito.when(pkCodeHistoryRepository.getPageByUuid(Mockito.any())).thenReturn(resultList);
        PowerMockito.when(stItemBarcodeService.pkCodeChange(Mockito.any())).thenReturn(resultList);
        Assert.assertNotNull(pkCodeHistoryService.getPageByUuid(pkCodeHistoryDTO));
    }

    /*Started by AICoder, pid:u18829a3d1r52531421e0822118d9a6657a00797*/
    @Test
    public void testBatchUpdateQtyTaskByPkCodesWithEmptyList() {
        // Given
        // No additional setup needed as we are using an empty list

        // When
        pkCodeInfoService.batchUpdateQtyTaskByPkCodes(null);

        // Then
        List<PkCodeInfo> list = new LinkedList<>();
        PkCodeInfo a1 = new PkCodeInfo();
        list.add(a1);
        pkCodeInfoService.batchUpdateQtyTaskByPkCodes(list);
        Assert.assertTrue(!CollectionUtils.isEmpty(list));
    }
    /*Ended by AICoder, pid:u18829a3d1r52531421e0822118d9a6657a00797*/
    /* Started by AICoder, pid:k4ec5v36e6x76f4140f3086de036b22e8882e3f1 */

    @Test
    public void setMBomTest() throws Exception {
        Whitebox.invokeMethod(pkCodeHistoryService, "setMbom", null);
        Assert.assertTrue(1==1);
        List<PkCodeInfo> list = new ArrayList<>();
        PkCodeInfo entity = new PkCodeInfo();
        entity.setSourceTask("1234567");
        list.add(entity);
        PkCodeInfo entity1 = new PkCodeInfo();
        entity1.setSourceTask("12345671");
        entity1.setItemNo("itemNo");
        list.add(entity1);
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = new ArrayList<>();
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dto.setProdplanId("1234567");
        dto.setProductCode("test");
        bProdBomHeaderDTOS.add(dto);
        PowerMockito.when(prodBomHeaderService.queryProductCodeByProdPlanIdList(Mockito.anyList())).thenReturn(bProdBomHeaderDTOS);
        Whitebox.invokeMethod(pkCodeHistoryService, "setMbom", list);
        Assert.assertTrue(list.get(0).getMbom().equals("test"));
        Assert.assertTrue(list.get(1).getMbom().equals("itemNo"));
    }
    /* Ended by AICoder, pid:k4ec5v36e6x76f4140f3086de036b22e8882e3f1 */
}