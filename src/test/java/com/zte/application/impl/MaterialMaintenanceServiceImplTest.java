package com.zte.application.impl;

import com.zte.domain.model.MaterialMaintenanceRepository;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class, HttpHeaderUtil.class})
public class MaterialMaintenanceServiceImplTest extends BaseTestCase {
    @InjectMocks
    private MaterialMaintenanceServiceImpl materialService;

    @Mock
    private MaterialMaintenanceRepository materialRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(HttpHeaderUtil.class);

    }
    @org.junit.Test
    public void queryMaterialByGlue() {
        List<String> list = new ArrayList<>();
        PowerMockito.when(materialRepository.queryMaterialByGlue()).thenReturn(list);
        materialService.queryMaterialByGlue();
        Assert.assertNotNull(list);
    }

    @org.junit.Test
    public void queryItemCodeByItemName() {
        List<String> list = new ArrayList<>();
        String itemName = "itemName";
        PowerMockito.when(materialRepository.queryItemCodeByItemName(Mockito.anyString())).thenReturn(list);
        materialService.queryItemCodeByItemName(itemName);
        Assert.assertNotNull(itemName);
    }

    @org.junit.Test
    public void querySupplierByItemNo() {
        List<String> list = new ArrayList<>();
        String itemNo= "itemNo";
        PowerMockito.when(materialRepository.querySupplierByItemNo(Mockito.anyString())).thenReturn(list);
        materialService.querySupplierByItemNo(itemNo);
        Assert.assertNotNull(itemNo);
    }
}