package com.zte.application.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.AbnormalMaterialBillHeadService;
import com.zte.application.HrmUserCenterService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.KafkaConstant;
import com.zte.domain.model.AbnormalMaterialBillHeadRepository;
import com.zte.domain.model.ApprovalProcessInfoRepository;
import com.zte.domain.model.part.SparePartHeadRepository;
import com.zte.infrastructure.remote.ApprovalRemoteService;
import com.zte.infrastructure.remote.ApsRemoteService;
import com.zte.interfaces.dto.ApprovalNodeDTO;
import com.zte.interfaces.dto.ApprovalProcessInfoEntityDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.iss.approval.sdk.bean.*;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.*;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.doNothing;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ApprovalFlowClient.class})
public class ApprovalProcessInfoServiceImplTest extends BaseTestCase {
    @InjectMocks
    private ApprovalProcessInfoServiceImpl service;
    @Mock
    private ApprovalProcessInfoRepository approvalProcessInforepository;
    @Mock
    private AbnormalMaterialBillHeadRepository abnormalMaterialBillHeadRepository;
    @Mock
    private SparePartHeadRepository sparePartHeadRepository;
    @Mock
    private HrmUserCenterService hrmUserCenterService;
    @Mock
    private ApprovalRemoteService approvalRemoteService;
    @Mock
    private AbnormalMaterialBillHeadService abnormalMaterialBillHeadService;

    @Test
    public void agreeToRejectCompletion() throws Exception {
        Whitebox.invokeMethod(service,"agreeToRejectCompletion", Constant.ApprovalStatus.AGREE);
        Whitebox.invokeMethod(service,"agreeToRejectCompletion", Constant.ApprovalStatus.APPROVAL_COMPLETED);
        Assert.assertTrue(Whitebox.invokeMethod(service,"agreeToRejectCompletion", Constant.ApprovalStatus.REFUSE));
    }
    @Test
    public void showFlowProcessInfo() throws Exception {
        PowerMockito.mockStatic(ApprovalFlowClient.class);
        List<ApprovalProcessDTO> approvalProcessDTOList = new ArrayList<>();
        ApprovalProcessDTO approvalProcessDTO=new ApprovalProcessDTO();
        approvalProcessDTO.setFlowInstanceId("0123130");
        approvalProcessDTOList.add(approvalProcessDTO);
        List<ApprovalNodeInfoDTO> approvalNodeInfoDTOList = new ArrayList<>();
        List<ApproverInfo> approverInfoList =new ArrayList<>();
        ApproverInfo approverInfo = new ApproverInfo();
        approverInfo.setApproverId("00286523");
        approverInfo.setResult("Y");
        approverInfo.setCreateDate(new Date());
        approverInfo.setOpinion("nu");
        approverInfo.setStatus("0");
        approverInfo.setLastUpdateDate(new Date());
        approverInfoList.add(approverInfo);
        ApprovalNodeInfoDTO approvalNodeInfoDTO =new ApprovalNodeInfoDTO();
        approvalNodeInfoDTO.setNodeKey("00");
        approvalNodeInfoDTO.setNodeName("11");
        approvalNodeInfoDTO.setNodeType("00");
        approvalNodeInfoDTO.setId("00");
        approvalNodeInfoDTO.setApproverList(approverInfoList);
        approvalNodeInfoDTOList.add(approvalNodeInfoDTO);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO=new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setId("00286523");
        hrmPersonInfoDTO.setEmpName("00286523");
        hrmPersonInfoDTOMap.put("00286523",hrmPersonInfoDTO);

        List<ApprovalProcessInfoEntityDTO> approvalProcessInfoEntityDTOList = new ArrayList<>();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO =new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTO.setApproverId("00286523");
        approvalProcessInfoEntityDTOList.add(approvalProcessInfoEntityDTO);
        PowerMockito.when(ApprovalFlowClient.queryProcess((ProcessBusinessIdDTO) any())).thenReturn(approvalProcessDTOList);
        PowerMockito.when(ApprovalFlowClient.showFlowInstancePanorama((FlowPanoramaDTO) any())).thenReturn(approvalNodeInfoDTOList);
        PowerMockito.when(approvalProcessInforepository.getList(Mockito.any())).thenReturn(approvalProcessInfoEntityDTOList);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.any())).thenReturn(hrmPersonInfoDTOMap);

        service.showFlowProcessInfo("00286523","00","scrappingOfToolingBarcode");
        Assert.assertNotNull(service.showFlowProcessInfo("00286523","00","toolingApplication"));

        service.showFlowProcessInfo("00286523","00","toolingVerify");
        Assert.assertNotNull(service.showFlowProcessInfo("00286523","00","toolingApplication"));
    }

    @Test
    public void auxScrapFlowWithdraw() throws Exception {
        PowerMockito.when(approvalRemoteService.revoke(Mockito.any(),Mockito.anyString(),Mockito.anyString())).thenReturn("12345643132435");
        service.auxScrapFlowWithdraw("123","00286523");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void auxVerifyFlowWithdraw() throws Exception {
        PowerMockito.when(approvalRemoteService.revoke(Mockito.any(),Mockito.anyString(),Mockito.anyString())).thenReturn("12345643132435");
        try{
            service.auxVerifyFlowWithdraw("","");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
        PowerMockito.when(approvalRemoteService.revoke(Mockito.any(),Mockito.anyString(),Mockito.anyString())).thenReturn("12345643132435");
        try{
            service.auxVerifyFlowWithdraw("","00286523");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
        PowerMockito.when(approvalRemoteService.revoke(Mockito.any(),Mockito.anyString(),Mockito.anyString())).thenReturn("12345643132435");
        try{
            service.auxVerifyFlowWithdraw("123","");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
        PowerMockito.when(approvalRemoteService.revoke(Mockito.any(),Mockito.anyString(),Mockito.anyString())).thenReturn("12345643132435");
        service.auxVerifyFlowWithdraw("123","00286523");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void queryApprovalRecordTest() throws Exception {
        ApprovalNodeDTO dto = new ApprovalNodeDTO();
        dto.setPage(1);
        dto.setRow(100);
        PowerMockito.when(approvalProcessInforepository.getCountByBillNo(Mockito.any())).thenReturn(0);
        service.queryApprovalRecord(dto);
        PowerMockito.when(approvalProcessInforepository.getCountByBillNo(Mockito.any())).thenReturn(1);
        List<ApprovalNodeDTO> list = new ArrayList<>();
        list.add(new ApprovalNodeDTO());
        PowerMockito.when(approvalProcessInforepository.getPageByBillNo(Mockito.any())).thenReturn(list);
        Page<ApprovalNodeDTO> result = service.queryApprovalRecord(dto);
        Assert.assertTrue(result.getRows().size() >= 0);
    }

    @Test
    public void updateCenterFactoryBillStatus() throws Exception {
        JSONObject data = new JSONObject();
        data.put("extendedCode","test");
        data.put(KafkaConstant.BUSINESS_ID,"test");
        data.put("approver","00286569");
        data.put("result","Y");
        PowerMockito.doNothing().when(sparePartHeadRepository).updateBillHeadStatus(Mockito.any());
        Assert.assertNull(Whitebox.invokeMethod
                (service, "updateCenterFactoryBillStatus", data,"zte-mes-manufactureshare-abnormal-material-entry","00286569","test","同意"));
        Assert.assertNull(Whitebox.invokeMethod
                (service, "updateCenterFactoryBillStatus", data,"zte-mes-manufactureshare-abnormal-material-entry","00286569","test","审批完成"));
        Assert.assertNull(Whitebox.invokeMethod
                (service, "updateCenterFactoryBillStatus", data,"zte-mes-manufactureshare-abnormal-material-entry","00286569","test","拒绝"));
        Assert.assertNull(Whitebox.invokeMethod
                (service, "updateCenterFactoryBillStatus", data,"zte-mes-manufactureshare-abnormal-material-entry","00286569","test","转交"));

        PowerMockito.field(ApprovalProcessInfoServiceImpl.class, "abnormalMaterialFlowCode").set(service, "zte-mes-manufactureshare-abnormal-material-entry");
        PowerMockito.when(abnormalMaterialBillHeadRepository.updateSelectiveById(Mockito.any())).thenReturn(1l);
        doNothing().when(abnormalMaterialBillHeadService).sendEmailAfterApproval(Mockito.any());
        Assert.assertNull(Whitebox.invokeMethod
                (service, "updateCenterFactoryBillStatus", data,"zte-mes-manufactureshare-abnormal-material-entry","00286569","test","同意"));
        Assert.assertNull(Whitebox.invokeMethod
                (service, "updateCenterFactoryBillStatus", data,"zte-mes-manufactureshare-abnormal-material-entry","00286569","test","拒绝"));
        Assert.assertNull(Whitebox.invokeMethod
                (service, "updateCenterFactoryBillStatus", data,"zte-mes-manufactureshare-abnormal-material-entry","00286569","test","转交"));
    }
}