package com.zte.application.impl;
import com.zte.domain.model.ServiceLog;
import com.zte.domain.model.ServiceLogRepository;
import com.zte.util.BaseTestCase;
import org.checkerframework.checker.units.qual.A;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/24
 * @Description
 */
@RunWith(PowerMockRunner.class)
public class ServiceLogServiceImplTest extends BaseTestCase {
    @InjectMocks
    ServiceLogServiceImpl serviceLogServiceImpl;
    @Mock
    ServiceLogRepository serviceLogRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void testAddBatch() throws Exception {
        List<ServiceLog> records = new ArrayList<>();
        serviceLogServiceImpl.batchInsert(records);

        ServiceLog serviceLog = new ServiceLog();
        String factoryId = "52";
        serviceLog.setFactoryId(factoryId);
        serviceLog.setServiceName("centerfactory");
        serviceLog.setRequestUrl("http://aaa.com");
        serviceLog.setReqDate(new Date());
        records.add(serviceLog);
        serviceLogServiceImpl.batchInsert(records);

        for (int i = 0; i < 101; i++) {
            serviceLog = new ServiceLog();
            factoryId = "52";
            serviceLog.setFactoryId(factoryId);
            serviceLog.setServiceName("centerfactory");
            serviceLog.setRequestUrl("http://aaa.com");
            serviceLog.setReqDate(new Date());
            records.add(serviceLog);
        }
        serviceLogServiceImpl.batchInsert(records);
        Assert.assertNotNull(records);
    }
}
