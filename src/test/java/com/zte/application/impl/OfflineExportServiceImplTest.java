package com.zte.application.impl;

import com.zte.common.utils.Constant;
import com.zte.domain.model.OfflineExport;
import com.zte.domain.model.OfflineExportRepository;
import com.zte.interfaces.dto.OfflineExportPageQueryDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/11/5 13:37
 * @Description
 */
@RunWith(PowerMockRunner.class)
public class OfflineExportServiceImplTest extends BaseTestCase {

    @InjectMocks
    private OfflineExportServiceImpl service;
    @Mock
    private OfflineExportRepository repository;

    @Test
    public void testQueryPage() throws Exception {
        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(1L);
        PowerMockito.when(repository.queryPage(Mockito.any())).thenReturn(new ArrayList<OfflineExport>());
        PageRows<OfflineExport> pageRows = service.queryPage(new OfflineExportPageQueryDTO());
        Assert.assertTrue(pageRows.getRows().size() >= 0);
    }

    @Test
    public void testAdd() throws Exception {
        PowerMockito.when(repository.insertSelective(Mockito.any())).thenReturn(1L);
        service.add(new OfflineExport());
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void testUpdateById() throws Exception {
        PowerMockito.when(repository.updateById(Mockito.any())).thenReturn(1L);
        service.updateById(new OfflineExport());
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void disabledHistoryJob() {
        Integer beforeDate = null;
        service.disabledHistoryJob(beforeDate);
        beforeDate = 5;
        service.disabledHistoryJob(beforeDate);
        Assert.assertNotNull(beforeDate);
    }

    @Test
    public void queryOfflineExportById() {
        Assert.assertNull(service.queryOfflineExportById("2"));
    }


}
