package com.zte.application.impl;
/* Started by AICoder, pid:2cdb8af4593648e08481f775236498e0 */

import com.zte.domain.model.PushBoardDataHeadRepository;
import com.zte.interfaces.dto.PushBoardDataHeadDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class PushBoardDataHeadServiceImplTest extends BaseTestCase {
    @InjectMocks
    private PushBoardDataHeadServiceImpl service;
    @Mock
    private PushBoardDataHeadRepository pushBoardDataHeadrepository;

    @Test
    public void merge() {
        List<PushBoardDataHeadDTO> list = new ArrayList<>();
        int count = service.merge(list);
        Assert.assertEquals(0, count);

        PushBoardDataHeadDTO dto = new PushBoardDataHeadDTO();
        dto.setProdplanId("7777666");
        list.add(dto);

        List<String> existProdplanIdList = new ArrayList<>();
        when(pushBoardDataHeadrepository.getExistProdplanId(Mockito.anyList())).thenReturn(existProdplanIdList);
        when(pushBoardDataHeadrepository.batchInsert(Mockito.anyList())).thenReturn(1);
        count = service.merge(list);
        Assert.assertEquals(1, count);

        existProdplanIdList.add("7777666");
        count = service.merge(list);
        Assert.assertEquals(0, count);
    }


    @Test
    public void getNotPushDoneProdplanId() {
        List<String> customerNameList = new ArrayList<>();
        int rows = 0;
        int page = 0;
        List<String> result = service.getNotPushDoneProdplanId(customerNameList, rows, page);
        Assert.assertTrue(result.isEmpty());

        customerNameList.add("ali");
        result = service.getNotPushDoneProdplanId(customerNameList, rows, page);
        Assert.assertTrue(result.isEmpty());

        rows = 10000;
        page = 1;
        result = service.getNotPushDoneProdplanId(customerNameList, rows, page);
        Assert.assertTrue(result.isEmpty());

        rows = 100;
        result = service.getNotPushDoneProdplanId(customerNameList, rows, page);
        Assert.assertTrue(result.isEmpty());
    }
}
