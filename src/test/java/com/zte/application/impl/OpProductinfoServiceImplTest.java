package com.zte.application.impl;

import com.zte.domain.model.OpProductinfo;
import com.zte.domain.model.OpProductinfoRepository;
import junit.framework.TestCase;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
public class OpProductinfoServiceImplTest extends TestCase {

    @InjectMocks
    private OpProductinfoServiceImpl service;
    @Mock
    private OpProductinfoRepository repository;

    public void getProductClassList() {
        service.getProductClassList();
    }

    public void getProductSmlClassList() {
        OpProductinfo opProductinfo = new OpProductinfo();
        opProductinfo.setProductClass("123");
        List<OpProductinfo> opProductinfos = new ArrayList<>();
        opProductinfos.add(opProductinfo);
        PowerMockito.when(repository.getProductSmlClassList(Mockito.any())).thenReturn(opProductinfos);
        service.getProductSmlClassList(opProductinfo);
    }
}