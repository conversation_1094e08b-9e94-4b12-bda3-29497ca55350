package com.zte.application.impl;

import com.zte.application.TradeDataLogService;
import com.zte.domain.model.QualityCodeInfoRepository;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.B2bCallBackNewDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.QualityCodeInfoDTO;
import com.zte.interfaces.dto.SaveQualityCodeDTO;
import com.zte.interfaces.dto.SspTaskInfoDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.JsonConvertUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/* Started by AICoder, pid:6dbb6jc2cds6dff14c1f0a7c519029245d97445a */

public class QualityCodeInfoServiceImplTest extends BaseTestCase {
    @InjectMocks
    private QualityCodeInfoServiceImpl qualityCodeInfoService;
    @Mock
    private QualityCodeInfoRepository qualityCodeInfoRepository;
    @Mock
    private TradeDataLogService tradeDataLogService;
    @Mock
    MdsRemoteService mdsRemoteService;
    @Mock
    protected IdGenerator idGenerator;

    @Test(expected = MesBusinessException.class)
    public void saveQualityCode_WithEmpty () throws Exception {
        SaveQualityCodeDTO dto = new SaveQualityCodeDTO();
        qualityCodeInfoService.saveQualityCode(dto);
    }
    @Test(expected = MesBusinessException.class)
    public void saveQualityCode_WithEmptyQualityCodeInfoList () throws Exception {
        SaveQualityCodeDTO dto = new SaveQualityCodeDTO();
        dto.setCustomerName("gfgf");
        qualityCodeInfoService.saveQualityCode(dto);
    }

    @Test
    public void saveQualityCode() throws Exception {
        List<QualityCodeInfoDTO> qualityCodeInfoList = new ArrayList<>();
        QualityCodeInfoDTO qualityCodeInfoDTO = new QualityCodeInfoDTO();
        qualityCodeInfoDTO.setSn("sn");
        qualityCodeInfoList.add(qualityCodeInfoDTO);
        qualityCodeInfoList.add(new QualityCodeInfoDTO());

        SaveQualityCodeDTO dto = new SaveQualityCodeDTO();
        dto.setCustomerName("gfgf");
        dto.setQualityCodeInfoList(qualityCodeInfoList);
        PowerMockito.doNothing().when(tradeDataLogService).pushDataOfExceptionRollback(Mockito.anyList());
        qualityCodeInfoService.saveQualityCode(dto);
        Assert.assertNotNull(dto);
        List<SspTaskInfoDTO> sspTaskInfoDTOList = new ArrayList<>();
        sspTaskInfoDTOList.add(new SspTaskInfoDTO(){{setSn("sn");}});
        sspTaskInfoDTOList.add(new SspTaskInfoDTO(){{setSn("sn");setNodeNumber("CN1");}});
        sspTaskInfoDTOList.add(new SspTaskInfoDTO(){{setSn("sn");setNodeNumber("CN2");}});
        sspTaskInfoDTOList.add(new SspTaskInfoDTO(){{setSn("sn");setNodeNumber("CN");}});
        PowerMockito.when(mdsRemoteService.querySspTaskInfoForAli(any())).thenReturn(sspTaskInfoDTOList);

        qualityCodeInfoService.saveQualityCode(dto);
        Assert.assertNotNull(dto);

    }
    @Test
    public void parseAliBabaData() throws Exception {
        List<QualityCodeInfoDTO> existQualityCodeInfoList = new ArrayList<>();
        QualityCodeInfoDTO qualityCodeInfo = new QualityCodeInfoDTO();
        String result = "{\"body\":\"{\\\"error_response\\\":{\\\"code\\\":40,\\\"msg\\\":\\\"Missing required arguments:generate_quality_code_params\\\",\\\"request_id\\\":\\\"eg58hdyn3608\\\"}}\",\"code\":40,\"msg\":\"Missing required arguments:generate_quality_code_params\",\"request_id\":\"eg58hdyn3608\",\"success\":false}";
        String qualityCode = qualityCodeInfoService.parseAliBabaData(result);
        result = "{\n" +
                "    \"body\": \"{\\\"result\\\":{\\\"code\\\":\\\"000000\\\",\\\"data\\\":{\\\"check_result\\\":true,\\\"quality_code\\\":\\\"ALI880207194332768\\\"},\\\"msg\\\":\\\"成功\\\",\\\"success\\\":true},\\\"request_id\\\":\\\"eg58hbo9tyge\\\"}\",\n" +
                "    \"request_id\": \"eg58hbo9tyge\",\n" +
                "    \"result\": {\n" +
                "        \"code\": \"000000\",\n" +
                "        \"data\": {\n" +
                "            \"check_result\": true,\n" +
                "            \"quality_code\": \"\"\n" +
                "        },\n" +
                "        \"msg\": \"成功\",\n" +
                "        \"success\": true\n" +
                "    },\n" +
                "    \"success\": true\n" +
                "}";
        qualityCode = qualityCodeInfoService.parseAliBabaData(result);
        result = "{\n" +
                "    \"body\": \"{\\\"result\\\":{\\\"code\\\":\\\"000000\\\",\\\"data\\\":{\\\"check_result\\\":true,\\\"quality_code\\\":\\\"ALI880207194332768\\\"},\\\"msg\\\":\\\"成功\\\",\\\"success\\\":true},\\\"request_id\\\":\\\"eg58hbo9tyge\\\"}\",\n" +
                "    \"request_id\": \"eg58hbo9tyge\",\n" +
                "    \"result\": {\n" +
                "        \"code\": \"000000\",\n" +
                "        \"data\": {\n" +
                "            \"check_result\": true,\n" +
                "            \"quality_code\": \"ALI880207194332768\"\n" +
                "        },\n" +
                "        \"msg\": \"成功\",\n" +
                "        \"success\": true\n" +
                "    },\n" +
                "    \"success\": true\n" +
                "}";
        qualityCode = qualityCodeInfoService.parseAliBabaData(result);
        Assert.assertEquals("ALI880207194332768", qualityCode);
    }
    @Test(expected = MesBusinessException.class)
    public void getSnExistQualityCode_WithEmpty () throws Exception {
        List<QualityCodeInfoDTO> qualityCodeInfoList = new ArrayList<>();
        qualityCodeInfoService.getSnExistQualityCode(qualityCodeInfoList);
    }
    @Test
    public void getSnExistQualityCode ()  {
        List<QualityCodeInfoDTO> qualityCodeInfoList = new ArrayList<>();
        QualityCodeInfoDTO qualityCodeInfoDTO = new QualityCodeInfoDTO();
        qualityCodeInfoList.add(qualityCodeInfoDTO);
        PowerMockito.when(qualityCodeInfoRepository.getSnExistQualityCode(Mockito.anyList())).thenReturn(null);
        qualityCodeInfoService.getSnExistQualityCode(qualityCodeInfoList);
        Mockito.verify(qualityCodeInfoRepository,Mockito.times(1)).getSnExistQualityCode(Mockito.anyList());
    }

    @Test
    public void filtSnNotExistQualityCode () {
        List<QualityCodeInfoDTO> qualityCodeInfoList = new ArrayList<>();
        List<String> result = qualityCodeInfoService.filtSnNotExistQualityCode(qualityCodeInfoList);
        Assert.assertTrue(result.isEmpty());

        QualityCodeInfoDTO qualityCodeInfoDTO = new QualityCodeInfoDTO();
        qualityCodeInfoDTO.setSn("sn1");
        qualityCodeInfoDTO.setTaskNo("taskNo1");
        qualityCodeInfoList.add(qualityCodeInfoDTO);
        List<QualityCodeInfoDTO> list = new ArrayList<>();
        PowerMockito.when(qualityCodeInfoRepository.getQualityCodeBySnAndTaskNo(Mockito.anyList())).thenReturn(list);
        result = qualityCodeInfoService.filtSnNotExistQualityCode(qualityCodeInfoList);
        Assert.assertTrue(result.contains("sn1"));
    }

    @Test
    public void saveQualityCodeCallBack()  throws Exception {
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        qualityCodeInfoService.saveQualityCodeCallBack(b2bCallBackNewDTO);

        b2bCallBackNewDTO.setMessageId("FDF");
        qualityCodeInfoService.saveQualityCodeCallBack(b2bCallBackNewDTO);

        b2bCallBackNewDTO.setSuccess(true);
        qualityCodeInfoService.saveQualityCodeCallBack(b2bCallBackNewDTO);

        String data = "{\"code\":\"0000\",\"data\":\"{\\\"result\\\":{\\\"code\\\":\\\"000000\\\",\\\"data\\\":{\\\"check_result\\\":true,\\\"quality_code\\\":\\\"ALI993033252032768\\\"},\\\"msg\\\":\\\"成功\\\",\\\"success\\\":true},\\\"request_id\\\":\\\"eg59htitzymz\\\"}\",\"keywords\":\"56e359c4-aa62-4c5f-8d70-711c836133b2\",\"messageId\":\"56e359c4-aa62-4c5f-8d70-711c836133b2\",\"messageType\":\"ZTEiMES-Alibaba-QualityCodePublic\",\"msg\":\"\",\"requestId\":\"eg59htitzymz\",\"source\":\"Alibaba\",\"success\":true}";
        PowerMockito.doNothing().when(qualityCodeInfoRepository).saveQualityCode(Mockito.anyList());
        PowerMockito.when(tradeDataLogService.getTradeDataLogById(Mockito.anyString())).thenReturn(new CustomerDataLogDTO());
        qualityCodeInfoService.saveQualityCodeCallBack(JsonConvertUtil.jsonToBean(data, B2bCallBackNewDTO.class));
        Assert.assertNotNull(data);
    }

    /* Started by AICoder, pid:cf61740f7aw9ff6147e80be4f0c7003e6a195643 */
    @Test
    public void parseAliBabaData_withEmpty() throws Exception {
        String result = "{\n" +
                "    \"body\": \"{\\\"result\\\":{\\\"code\\\":\\\"000000\\\",\\\"data\\\":{\\\"check_result\\\":true,\\\"quality_code\\\":\\\"ALI880207194332768\\\"},\\\"msg\\\":\\\"成功\\\",\\\"success\\\":true},\\\"request_id\\\":\\\"eg58hbo9tyge\\\"}\",\n" +
                "    \"request_id\": \"eg58hbo9tyge\",\n" +
                "    \"result\": {\n" +
                "        \"code\": \"000000\",\n" +
                "        \"msg\": \"成功\",\n" +
                "        \"success\": true\n" +
                "    },\n" +
                "    \"success\": true\n" +
                "}";
        String qualityCode = qualityCodeInfoService.parseAliBabaData(result);
        Assert.assertEquals("", qualityCode);
    }

    @Test
    public void getQualityCode() throws Exception {
        PowerMockito.when(qualityCodeInfoService.getQualityCode(any())).thenReturn(null);

        QualityCodeInfoDTO qualityCode = qualityCodeInfoService.getQualityCode(any());
        Assert.assertNull(qualityCode);
    }

    /* Started by AICoder, pid:getSnInc_test */
    @Test
    public void getSnInc_WithNullLastUpdatedDate() throws Exception {
        // Given
        String lastUpdatedDate = null;
        String lastSn = "SN001";
        int limit = 100;

        // When
        List<QualityCodeInfoDTO> result = qualityCodeInfoService.getSnInc(lastUpdatedDate, lastSn, limit);

        // Then
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void getSnInc_WithZeroLimit() throws Exception {
        // Given
        String lastUpdatedDate = "2025-01-27 10:00:00";
        String lastSn = "SN001";
        int limit = 0;

        List<QualityCodeInfoDTO> expectedResult = new ArrayList<>();
        QualityCodeInfoDTO dto1 = new QualityCodeInfoDTO();
        dto1.setSn("SN002");
        dto1.setQualityCode("QC001");
        expectedResult.add(dto1);

        PowerMockito.when(qualityCodeInfoRepository.getSnInc(lastUpdatedDate, lastSn, 500)).thenReturn(expectedResult);

        // When
        List<QualityCodeInfoDTO> result = qualityCodeInfoService.getSnInc(lastUpdatedDate, lastSn, limit);

        // Then
        Assert.assertEquals(expectedResult, result);
        Mockito.verify(qualityCodeInfoRepository, Mockito.times(1)).getSnInc(lastUpdatedDate, lastSn, 500);
    }

    @Test
    public void getSnInc_WithNegativeLimit() throws Exception {
        // Given
        String lastUpdatedDate = "2025-01-27 10:00:00";
        String lastSn = "SN001";
        int limit = -10;

        List<QualityCodeInfoDTO> expectedResult = new ArrayList<>();
        QualityCodeInfoDTO dto1 = new QualityCodeInfoDTO();
        dto1.setSn("SN002");
        dto1.setQualityCode("QC001");
        expectedResult.add(dto1);

        PowerMockito.when(qualityCodeInfoRepository.getSnInc(lastUpdatedDate, lastSn, 500)).thenReturn(expectedResult);

        // When
        List<QualityCodeInfoDTO> result = qualityCodeInfoService.getSnInc(lastUpdatedDate, lastSn, limit);

        // Then
        Assert.assertEquals(expectedResult, result);
        Mockito.verify(qualityCodeInfoRepository, Mockito.times(1)).getSnInc(lastUpdatedDate, lastSn, 500);
    }

    @Test
    public void getSnInc_WithLimitGreaterThan500() throws Exception {
        // Given
        String lastUpdatedDate = "2025-01-27 10:00:00";
        String lastSn = "SN001";
        int limit = 1000;

        List<QualityCodeInfoDTO> expectedResult = new ArrayList<>();
        QualityCodeInfoDTO dto1 = new QualityCodeInfoDTO();
        dto1.setSn("SN002");
        dto1.setQualityCode("QC001");
        expectedResult.add(dto1);

        PowerMockito.when(qualityCodeInfoRepository.getSnInc(lastUpdatedDate, lastSn, 500)).thenReturn(expectedResult);

        // When
        List<QualityCodeInfoDTO> result = qualityCodeInfoService.getSnInc(lastUpdatedDate, lastSn, limit);

        // Then
        Assert.assertEquals(expectedResult, result);
        Mockito.verify(qualityCodeInfoRepository, Mockito.times(1)).getSnInc(lastUpdatedDate, lastSn, 500);
    }

    @Test
    public void getSnInc_WithValidLimit() throws Exception {
        // Given
        String lastUpdatedDate = "2025-01-27 10:00:00";
        String lastSn = "SN001";
        int limit = 100;

        List<QualityCodeInfoDTO> expectedResult = new ArrayList<>();
        QualityCodeInfoDTO dto1 = new QualityCodeInfoDTO();
        dto1.setSn("SN002");
        dto1.setQualityCode("QC001");
        expectedResult.add(dto1);

        QualityCodeInfoDTO dto2 = new QualityCodeInfoDTO();
        dto2.setSn("SN003");
        dto2.setQualityCode("QC002");
        expectedResult.add(dto2);

        PowerMockito.when(qualityCodeInfoRepository.getSnInc(lastUpdatedDate, lastSn, limit)).thenReturn(expectedResult);

        // When
        List<QualityCodeInfoDTO> result = qualityCodeInfoService.getSnInc(lastUpdatedDate, lastSn, limit);

        // Then
        Assert.assertEquals(expectedResult, result);
        Mockito.verify(qualityCodeInfoRepository, Mockito.times(1)).getSnInc(lastUpdatedDate, lastSn, limit);
    }

    @Test
    public void getSnInc_WithNullLastSn() throws Exception {
        // Given
        String lastUpdatedDate = "2025-01-27 10:00:00";
        String lastSn = null;
        int limit = 100;

        List<QualityCodeInfoDTO> expectedResult = new ArrayList<>();
        QualityCodeInfoDTO dto1 = new QualityCodeInfoDTO();
        dto1.setSn("SN001");
        dto1.setQualityCode("QC001");
        expectedResult.add(dto1);

        PowerMockito.when(qualityCodeInfoRepository.getSnInc(lastUpdatedDate, lastSn, limit)).thenReturn(expectedResult);

        // When
        List<QualityCodeInfoDTO> result = qualityCodeInfoService.getSnInc(lastUpdatedDate, lastSn, limit);

        // Then
        Assert.assertEquals(expectedResult, result);
        Mockito.verify(qualityCodeInfoRepository, Mockito.times(1)).getSnInc(lastUpdatedDate, lastSn, limit);
    }

    @Test
    public void getSnInc_WithEmptyLastSn() throws Exception {
        // Given
        String lastUpdatedDate = "2025-01-27 10:00:00";
        String lastSn = "";
        int limit = 100;

        List<QualityCodeInfoDTO> expectedResult = new ArrayList<>();
        QualityCodeInfoDTO dto1 = new QualityCodeInfoDTO();
        dto1.setSn("SN001");
        dto1.setQualityCode("QC001");
        expectedResult.add(dto1);

        PowerMockito.when(qualityCodeInfoRepository.getSnInc(lastUpdatedDate, lastSn, limit)).thenReturn(expectedResult);

        // When
        List<QualityCodeInfoDTO> result = qualityCodeInfoService.getSnInc(lastUpdatedDate, lastSn, limit);

        // Then
        Assert.assertEquals(expectedResult, result);
        Mockito.verify(qualityCodeInfoRepository, Mockito.times(1)).getSnInc(lastUpdatedDate, lastSn, limit);
    }

    @Test
    public void getSnInc_WithRepositoryReturningNull() throws Exception {
        // Given
        String lastUpdatedDate = "2025-01-27 10:00:00";
        String lastSn = "SN001";
        int limit = 100;

        PowerMockito.when(qualityCodeInfoRepository.getSnInc(lastUpdatedDate, lastSn, limit)).thenReturn(null);

        // When
        List<QualityCodeInfoDTO> result = qualityCodeInfoService.getSnInc(lastUpdatedDate, lastSn, limit);

        // Then
        Assert.assertNull(result);
        Mockito.verify(qualityCodeInfoRepository, Mockito.times(1)).getSnInc(lastUpdatedDate, lastSn, limit);
    }

    @Test
    public void getSnInc_WithRepositoryReturningEmptyList() throws Exception {
        // Given
        String lastUpdatedDate = "2025-01-27 10:00:00";
        String lastSn = "SN001";
        int limit = 100;

        List<QualityCodeInfoDTO> expectedResult = new ArrayList<>();
        PowerMockito.when(qualityCodeInfoRepository.getSnInc(lastUpdatedDate, lastSn, limit)).thenReturn(expectedResult);

        // When
        List<QualityCodeInfoDTO> result = qualityCodeInfoService.getSnInc(lastUpdatedDate, lastSn, limit);

        // Then
        Assert.assertEquals(expectedResult, result);
        Assert.assertTrue(result.isEmpty());
        Mockito.verify(qualityCodeInfoRepository, Mockito.times(1)).getSnInc(lastUpdatedDate, lastSn, limit);
    }

    @Test
    public void getSnInc_WithLargeDataSet() throws Exception {
        // Given
        String lastUpdatedDate = "2025-01-27 10:00:00";
        String lastSn = "SN001";
        int limit = 200;

        List<QualityCodeInfoDTO> expectedResult = new ArrayList<>();
        for (int i = 1; i <= 200; i++) {
            QualityCodeInfoDTO dto = new QualityCodeInfoDTO();
            dto.setSn("SN" + String.format("%03d", i));
            dto.setQualityCode("QC" + String.format("%03d", i));
            expectedResult.add(dto);
        }

        PowerMockito.when(qualityCodeInfoRepository.getSnInc(lastUpdatedDate, lastSn, limit)).thenReturn(expectedResult);

        // When
        List<QualityCodeInfoDTO> result = qualityCodeInfoService.getSnInc(lastUpdatedDate, lastSn, limit);

        // Then
        Assert.assertEquals(expectedResult, result);
        Assert.assertEquals(200, result.size());
        Mockito.verify(qualityCodeInfoRepository, Mockito.times(1)).getSnInc(lastUpdatedDate, lastSn, limit);
    }

    @Test
    public void getSnInc_WithBoundaryLimitValues() throws Exception {
        // Given
        String lastUpdatedDate = "2025-01-27 10:00:00";
        String lastSn = "SN001";

        // Test with limit = 1 (minimum valid value)
        int limit1 = 1;
        List<QualityCodeInfoDTO> expectedResult1 = new ArrayList<>();
        QualityCodeInfoDTO dto1 = new QualityCodeInfoDTO();
        dto1.setSn("SN002");
        dto1.setQualityCode("QC001");
        expectedResult1.add(dto1);

        PowerMockito.when(qualityCodeInfoRepository.getSnInc(lastUpdatedDate, lastSn, limit1)).thenReturn(expectedResult1);

        // When
        List<QualityCodeInfoDTO> result1 = qualityCodeInfoService.getSnInc(lastUpdatedDate, lastSn, limit1);

        // Then
        Assert.assertEquals(expectedResult1, result1);
        Mockito.verify(qualityCodeInfoRepository, Mockito.times(1)).getSnInc(lastUpdatedDate, lastSn, limit1);

        // Test with limit = 500 (maximum valid value)
        int limit2 = 500;
        List<QualityCodeInfoDTO> expectedResult2 = new ArrayList<>();
        for (int i = 1; i <= 500; i++) {
            QualityCodeInfoDTO dto = new QualityCodeInfoDTO();
            dto.setSn("SN" + String.format("%03d", i));
            dto.setQualityCode("QC" + String.format("%03d", i));
            expectedResult2.add(dto);
        }

        PowerMockito.when(qualityCodeInfoRepository.getSnInc(lastUpdatedDate, lastSn, limit2)).thenReturn(expectedResult2);

        // When
        List<QualityCodeInfoDTO> result2 = qualityCodeInfoService.getSnInc(lastUpdatedDate, lastSn, limit2);

        // Then
        Assert.assertEquals(expectedResult2, result2);
        Assert.assertEquals(500, result2.size());
        Mockito.verify(qualityCodeInfoRepository, Mockito.times(1)).getSnInc(lastUpdatedDate, lastSn, limit2);
    }
    /* Ended by AICoder, pid:getSnInc_test */
    /* Ended by AICoder, pid:cf61740f7aw9ff6147e80be4f0c7003e6a195643 */
}

/* Ended by AICoder, pid:6dbb6jc2cds6dff14c1f0a7c519029245d97445a */
