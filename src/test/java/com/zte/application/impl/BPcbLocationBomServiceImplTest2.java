package com.zte.application.impl;

import com.zte.application.BsItemInfoService;
import com.zte.common.CommonUtils;
import com.zte.domain.model.*;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.itp.msa.core.locale.LocaleMessageSourceBean;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.anyList;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class, SpringContextUtil.class})
public class BPcbLocationBomServiceImplTest2 extends BaseTestCase {

    @InjectMocks
    private BPcbLocationBomServiceImpl service;

    @Mock
    private BBomDetailRepository bBomDetailRepository;

    @Mock
    private BPcbLocationDetailRepository bPcbLocationDetailRepository;

    @Mock
    private BsItemInfoService bsItemInfoService; // Assuming you have a service for BsItemInfo

    @Mock
    private LocaleMessageSourceBean localeMessageSourceBean;

    @Mock
    private BsItemInfoRepository bsItemInfoRepository;

    @Mock
    private BsItemInfo itemInfo;

    @Mock
    private LocaleMessageSourceBean lmb;


    @Test
    public void test001() throws Exception {
        List<BBomHeader> bomHeaderList = new ArrayList<>();
        BBomHeader bBomHeader = new BBomHeader();
        bBomHeader.setProductCode("122993351061ACB");
        bBomHeader.setBomHeaderId("18477411");
        bomHeaderList.add(bBomHeader);
        List<BBomDetailDTO> bBomDetailList11 = new ArrayList<>();
        BBomDetailDTO bBomDetailDTO3 = new BBomDetailDTO();
        bBomDetailDTO3.setItemCode("014030200021");
        bBomDetailDTO3.setPositionExt("，D9-D1");
        bBomDetailDTO3.setProductCode("123");
        bBomDetailList11.add(bBomDetailDTO3);
        Assert.assertEquals(1,1);
        PowerMockito.when(bBomDetailRepository,"selectDetailByHeaderIdS",bomHeaderList).thenReturn(bBomDetailList11);
        List<BsItemInfo> itemInfoList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("123");
        itemInfoList.add(bsItemInfo);
        PowerMockito.when(bsItemInfoRepository,"selectBsItemInfoInItemCode",anyList()).thenReturn(itemInfoList);
        service.batchInsertPcbLocation(bomHeaderList,"123");
    }


    @Test
    public void processProductCodeTest_WithValidData() throws Exception {
        List<BBomDetailDTO> detailList = new ArrayList<>();

        // 添加正常的测试数据
        BBomDetailDTO bBomDetailDTO = new BBomDetailDTO();
        bBomDetailDTO.setBomHeaderId("123");
        bBomDetailDTO.setPositionExt("R1");
        bBomDetailDTO.setItemCode("123");
        detailList.add(bBomDetailDTO);

        Map<String, BsItemInfo> itemInfoMap = new HashMap<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("123");
        bsItemInfo.setProductType("123");
        bsItemInfo.setAbcType("A");
        bsItemInfo.setSourceItemId(new BigDecimal(123));
        itemInfoMap.put("123", bsItemInfo);

        List<BPcbLocationDetail> insertOneList = new ArrayList<>();
        service.processProductCode(insertOneList, detailList, "123", "213", itemInfoMap);

        // 触发 errMsg != null 的情况
        BBomDetailDTO bBomDetailDTO1 = new BBomDetailDTO();
        bBomDetailDTO1.setBomHeaderId("123");
        bBomDetailDTO1.setPositionExt("C107A89-C107A86-C107A80");
        bBomDetailDTO1.setItemCode("123");
        detailList.clear();

        detailList.add(bBomDetailDTO1);
        try {
            service.processProductCode(insertOneList, detailList, "123", "213", itemInfoMap);
        } catch (MesBusinessException e) {
            Assert.assertEquals(e.getExCode(), RetCode.BUSINESSERROR_CODE);
        }
        // 添加 errMsg == null && locationNo == null 的情况
        BBomDetailDTO bBomDetailDTO2 = new BBomDetailDTO();
        bBomDetailDTO2.setBomHeaderId("123");
        bBomDetailDTO2.setPositionExt("R2");  // 假设这个位置号不会生成有效的locationNo
        bBomDetailDTO2.setItemCode("999"); // 假设999在itemInfoMap中没有对应的BsItemInfo
        detailList.clear();
        detailList.add(bBomDetailDTO2);
        // 运行测试，应该不会抛出异常
        service.processProductCode(insertOneList, detailList, "123", "213", itemInfoMap);
    }

    @Test
    public void processProductCodeTest_EdgeCases() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        List<BBomDetailDTO> detailList = new ArrayList<>();

        // 情况 1: errMsg != null
        BBomDetailDTO bBomDetailDTO1 = new BBomDetailDTO();
        bBomDetailDTO1.setBomHeaderId("123");
        bBomDetailDTO1.setPositionExt("C107A89-C107A86-C107A80"); // 会导致getNoList产生errMsg
        bBomDetailDTO1.setItemCode("123");
        detailList.add(bBomDetailDTO1);

        List<BPcbLocationDetail> insertOneList = new ArrayList<>();
        Map<String, BsItemInfo> itemInfoMap = new HashMap<>();
        itemInfoMap.put("123", new BsItemInfo()); // 添加一个空的itemInfo

        try {
            service.processProductCode(insertOneList, detailList, "123", "213", itemInfoMap);
        } catch (MesBusinessException e) {
            Assert.assertEquals(e.getExCode(), RetCode.BUSINESSERROR_CODE);
        }

        // 情况 2: locationNo == null
        BBomDetailDTO bBomDetailDTO2 = new BBomDetailDTO();
        bBomDetailDTO2.setBomHeaderId("124");
        bBomDetailDTO2.setPositionExt("R3");  // 假设这个位置号不会生成有效的locationNo
        bBomDetailDTO2.setItemCode("999"); // 假设999在itemInfoMap中没有对应的BsItemInfo
        detailList.clear();
        detailList.add(bBomDetailDTO2);

        // 运行测试，应该正常执行
        insertOneList.clear(); // 清空插入列表
        service.processProductCode(insertOneList, detailList, "123", "213", itemInfoMap);

        // 情况 3: locationNo.length == 0
        BBomDetailDTO bBomDetailDTO3 = new BBomDetailDTO();
        bBomDetailDTO3.setBomHeaderId("125");
        bBomDetailDTO3.setPositionExt("R4");  // 假设这个位置号也不会生成有效的locationNo
        bBomDetailDTO3.setItemCode("888"); // 假设888在itemInfoMap中没有对应的BsItemInfo
        detailList.clear();
        detailList.add(bBomDetailDTO3);

        // 运行测试，应该正常执行
        insertOneList.clear(); // 清空插入列表
        service.processProductCode(insertOneList, detailList, "123", "213", itemInfoMap);

        // 情况 3: locationNo.length == 0
        BBomDetailDTO bBomDetailDTO4 = new BBomDetailDTO();
        bBomDetailDTO4.setBomHeaderId("125");
        bBomDetailDTO4.setPositionExt("，");
        bBomDetailDTO4.setItemCode("888");
        detailList.clear();
        detailList.add(bBomDetailDTO4);

        // 运行测试，应该正常执行
        insertOneList.clear(); // 清空插入列表
        PowerMockito.when(CommonUtils.getLmbMessage("chinese.comma.existed")).thenReturn("");
        try {
            service.processProductCode(insertOneList, detailList, "123", "213", itemInfoMap);
        } catch (MesBusinessException e) {
            Assert.assertEquals(e.getExCode(), RetCode.BUSINESSERROR_CODE);
        }

        // 情况 6: 空的位号
        BBomDetailDTO bBomDetailDTO6 = new BBomDetailDTO();
        bBomDetailDTO6.setBomHeaderId("128");
        bBomDetailDTO6.setPositionExt(",D3, ,D4"); // 设置为空
        bBomDetailDTO6.setItemCode("888");
        BBomDetailDTO bBomDetailDTO7 = new BBomDetailDTO();
        bBomDetailDTO7.setBomHeaderId("128");
        bBomDetailDTO7.setPositionExt(" "); // 设置为空
        bBomDetailDTO7.setItemCode("888");
        detailList.clear();
        detailList.add(bBomDetailDTO6);
        detailList.add(bBomDetailDTO7);

        // 运行测试，应该正常执行
        insertOneList.clear(); // 清空插入列表
        PowerMockito.when(CommonUtils.getLmbMessage("chinese.comma.existed")).thenReturn("");
        try {
            service.processProductCode(insertOneList, detailList, "123", "213", itemInfoMap);
        } catch (MesBusinessException e) {
            Assert.assertEquals(e.getExCode(), RetCode.BUSINESSERROR_CODE);
        }
    }



    @Test
    public void batchInsertPcbLocationTest() throws Exception {
        String empNo = "18477411";

        List<BBomHeader> bomHeaderList = Arrays.asList(new BBomHeader());
        PowerMockito.when(bBomDetailRepository.selectDetailByHeaderIdS(bomHeaderList)).thenReturn(Collections.emptyList());

        try{
            service.batchInsertPcbLocation(bomHeaderList, empNo);
        }catch (Exception e){
            assertTrue(true);
        }

        BBomDetailDTO detail1 = new BBomDetailDTO();
        detail1.setProductCode("122993351061ACB");
        detail1.setItemCode("045020200153");

        List<BBomDetailDTO> bBomDetailList1 = Arrays.asList(detail1);
        PowerMockito.when(bBomDetailRepository.selectDetailByHeaderIdS(bomHeaderList)).thenReturn(bBomDetailList1);
        List<BsItemInfo> itemInfoList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("123");
        itemInfoList.add(bsItemInfo);
        PowerMockito.when(bsItemInfoRepository.selectBsItemInfoInItemCode(anyList())).thenReturn(itemInfoList);

        service.batchInsertPcbLocation(bomHeaderList, empNo);
    }
}