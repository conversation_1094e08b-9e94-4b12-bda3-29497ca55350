package com.zte.application.impl;

import com.sun.mail.smtp.SMTPAddressFailedException;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.MailLog;
import com.zte.domain.model.MailLogRepository;
import com.zte.interfaces.dto.MailLogPageQueryDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.mail.MailSendException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.MimeMessageHelper;

import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMessage;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;

/**
 * 邮件日志
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-28 17:02:02
 */
@RunWith(PowerMockRunner.class)
public class MailLogServiceImplTest extends BaseTestCase {

    @InjectMocks
    private MailLogServiceImpl service;
    @Mock
    private MailLogRepository repository;
    @Mock
    private JavaMailSender javaMailSender;
    @Mock
    private MimeMessage mimeMessage;
    @Mock
    private MimeMessageHelper mimeMessageHelper;
    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Test
    public void testQueryPage() {
        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(1L);
        PowerMockito.when(repository.queryPage(Mockito.any())).thenReturn(new ArrayList<MailLog>());
        PageRows<MailLog> pageRows = service.queryPage(new MailLogPageQueryDTO());
        Assert.assertTrue(pageRows.getRows().size() >= 0);
    }

    @Test
    public void testGetById() {
        PowerMockito.when(repository.selectById(Mockito.any())).thenReturn(new MailLog());
        Assert.assertNotNull(service.getById("test"));
    }

    @Test
    public void testSend() {
        PowerMockito.when(repository.insert(Mockito.any())).thenReturn(1L);
        service.add(new MailLog());
        MailLog mailLog = new MailLog();
        Assert.assertNotNull(mailLog);
    }

    @Test
    public void testAdd() throws Exception {
        PowerMockito.when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
        PowerMockito.whenNew(MimeMessageHelper.class).withAnyArguments().thenReturn(mimeMessageHelper);
        PowerMockito.doNothing().when(javaMailSender).send(mimeMessage);
        PowerMockito.when(repository.insertSelective(Mockito.any())).thenReturn(1L);
        MailLog mailLog = new MailLog();
        mailLog.setMailFrom("<EMAIL>");
        mailLog.setMailTo("<EMAIL>;10275508");
        mailLog.setMailCc("<EMAIL>");
        mailLog.setMailBcc("<EMAIL>");
        mailLog.setLang(3);
        mailLog.setSubject("测试邮件");
        mailLog.setText("测试邮件");
        mailLog.setLinkUrl("https://linkUrl");
        mailLog.setSubjectEn("测试邮件");
        mailLog.setTextEn("测试邮件");
        mailLog.setLinkUrlEn("https://linkUrl");
        service.send(mailLog);
        mailLog.setMailCc(null);
        mailLog.setMailBcc(null);
        service.send(mailLog);
        try {
            mailLog.setMailTo(null);
            service.send(mailLog);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.EMAIL_TO_IS_EMPTY, e.getMessage());
        }
    }

    @Test
    public void testAddOfException() throws Exception {
        PowerMockito.when(javaMailSender.createMimeMessage()).thenReturn(mimeMessage);
        PowerMockito.whenNew(MimeMessageHelper.class).withAnyArguments().thenReturn(mimeMessageHelper);
        Map<Object, Exception> failedMessages = new HashMap<>();
        failedMessages.put("test", new SMTPAddressFailedException(new InternetAddress("127.0.0.1"), "", 1, ""));
        MailSendException mailSendException = new MailSendException(failedMessages);
        PowerMockito.doThrow(mailSendException).doThrow(mailSendException).doNothing().when(javaMailSender).send(mimeMessage);
        PowerMockito.when(repository.insertSelective(Mockito.any())).thenReturn(1L);
        MailLog mailLog = new MailLog();
        mailLog.setMailFrom("<EMAIL>");
        mailLog.setMailTo("<EMAIL>;10275508");
        mailLog.setMailCc("<EMAIL>");
        mailLog.setMailBcc("<EMAIL>");
        mailLog.setLang(3);
        mailLog.setSubject("测试邮件");
        mailLog.setText("测试邮件");
        mailLog.setLinkUrl("https://linkUrl");
        mailLog.setSubjectEn("测试邮件");
        mailLog.setTextEn("测试邮件");
        mailLog.setLinkUrlEn("https://linkUrl");
        service.send(mailLog);
        Assert.assertNotNull(mailLog);
    }

    @Test
    public void testUpdateById() {
        PowerMockito.when(repository.updateById(Mockito.any())).thenReturn(1L);
        service.updateById(new MailLog());
        MailLog mailLog = new MailLog();
        Assert.assertNotNull(mailLog);
    }

    @Test
    public void testDeleteByIds() {
        PowerMockito.when(repository.deleteByIds(Mockito.any())).thenReturn(1L);
        service.deleteByIds(new ArrayList());
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void testMailSendExceptionHandler() throws Exception {
        Assert.assertFalse(Whitebox.invokeMethod(service, "mailSendExceptionHandler", new MailLog(),mimeMessage,mimeMessageHelper,new HashSet<>(),new RuntimeException()));
    }

    @Test
    public void getFileOfCloudDiskByDocIdTest() throws Exception {
        HttpServletResponse response = null;
        String docId = "test";
        service.getFileOfCloudDiskByDocId(response, docId);
        Assert.assertNotNull(docId);
    }
}

