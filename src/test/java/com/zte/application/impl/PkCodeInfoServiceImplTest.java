package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BBomDetailRepository;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PkCodeInfoRepository;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.BarcodeExpandQueryDTO;
import com.zte.interfaces.dto.BarcodeExpandVO;
import com.zte.interfaces.dto.BsPremanuItemInfoDTO;
import com.zte.interfaces.dto.TableColumnsDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceDiscoveryInvoker;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.util.Pair;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;


@PrepareForTest({RetCode.class, SpringContextUtil.class, RequestHeadValidationUtil.class,
        MicroServiceDiscoveryInvoker.class, MicroServiceRestUtil.class,CommonUtils.class,
        MESHttpHelper.class, HttpClientUtil.class,ServiceDataBuilderUtil.class,HttpRemoteService.class,JacksonJsonConverUtil.class})
public class PkCodeInfoServiceImplTest extends BaseTestCase {
    @InjectMocks
    private  PkCodeInfoServiceImpl pkCodeInfoService;
    @Mock
    private BarcodeCenterServiceImpl barcodeCenterService;

    @Before
    public void init() {
        PowerMockito.mockStatic(RetCode.class, SpringContextUtil.class, RequestHeadValidationUtil.class,
                MicroServiceDiscoveryInvoker.class, MicroServiceRestUtil.class,CommonUtils.class,
                MESHttpHelper.class, HttpClientUtil.class, ServiceDataBuilderUtil.class,HttpRemoteService.class,
                JacksonJsonConverUtil.class);
    }
    @Test
    public void setBomInfo()throws Exception {
        PkCodeInfo resInfo = new PkCodeInfo();
        resInfo.setSourceBatchCode("220220");
        List<BsPremanuItemInfoDTO > bomInfoList = new ArrayList<>();
        pkCodeInfoService.setBomInfo(resInfo,null);

        BsPremanuItemInfoDTO dto = new BsPremanuItemInfoDTO();
        dto.setStyle("orStyle");
        dto.setTagNum("tagnum");
        bomInfoList.add(dto);
        List<String> barcodeList = new ArrayList<>();
        barcodeList.add("220220");
        BarcodeExpandQueryDTO barcodeExpandQueryDTO = new BarcodeExpandQueryDTO();
        barcodeExpandQueryDTO.setBarcodeList(barcodeList);
        PowerMockito.when(barcodeCenterService.barcodeQuery(any())).thenReturn(null);
        pkCodeInfoService.setBomInfo(resInfo,bomInfoList);

        List<BarcodeExpandVO> expandVOS = new ArrayList<>();
        BarcodeExpandVO barcodeExpandVO = new BarcodeExpandVO();
        barcodeExpandVO.setBarcode("220220");
        barcodeExpandVO.setSpecModel("styleModel");
        expandVOS.add(barcodeExpandVO);
        PowerMockito.when(barcodeCenterService.barcodeQuery(any())).thenReturn(expandVOS);
        pkCodeInfoService.setBomInfo(resInfo,bomInfoList);
        Assert.assertNotNull(dto);
    }
}