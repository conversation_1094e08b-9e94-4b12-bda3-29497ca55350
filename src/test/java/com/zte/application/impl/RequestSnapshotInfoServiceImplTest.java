package com.zte.application.impl;

import com.zte.common.CommonUtils;
import com.zte.domain.model.RequestSnapshotInfoRepository;
import com.zte.interfaces.dto.RequestSnapshotInfo;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * ClassName: RequestSnapshotInfoServiceImplTest
 * Description:
 *
 * <AUTHOR>
 * @date 2024/3/25 上午9:21
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class RequestSnapshotInfoServiceImplTest {
    @InjectMocks
    private RequestSnapshotInfoServiceImpl snapshotInfoService;
    @Mock
    private RequestSnapshotInfoRepository repository;
    @Test
    public void batchInsertTest() {
        List<RequestSnapshotInfo> list = new ArrayList<>();
        snapshotInfoService.batchInsert(list);
        Assert.assertTrue(1==1);
        RequestSnapshotInfo info = new RequestSnapshotInfo();
        info.setRequestId("test");
        list.add(info);
        snapshotInfoService.batchInsert(list);
        Assert.assertTrue(1==1);
        for (int i = 0; i < 105; i++) {
            list.add(info);
        }
        snapshotInfoService.batchInsert(list);
        Assert.assertTrue(1==1);
    }
}
