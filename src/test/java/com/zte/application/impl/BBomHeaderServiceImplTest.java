package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BBomHeader;
import com.zte.domain.model.BBomHeaderRepository;
import com.zte.domain.model.datawb.BaBomHeadDTO;
import com.zte.infrastructure.feign.DatawbFeignService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.BBomHeaderDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DatawbRemoteService.class})
public class BBomHeaderServiceImplTest extends BaseTestCase {

    @InjectMocks
    private BBomHeaderServiceImpl bBomHeaderService;
    @Mock
    private BBomHeaderRepository bBomHeaderRepository;
    @Mock
    DatawbFeignService datawbFeignService;

    @Test
    public void testUpdateBBomHeaderByIdSelective() throws Exception {
        PowerMockito.when(bBomHeaderRepository.selectBatchByHeadId(any())).thenReturn(new BBomHeader());
        PowerMockito.when(bBomHeaderRepository.updateBBomHeaderByIdSelective(any())).thenReturn(1);
        bBomHeaderService.updateBBomHeaderByIdSelective(new BBomHeader());

        BBomHeader bBomHeader = new BBomHeader();
        bBomHeader.setZjSubcardFlag(Constant.FLAG_Y);
        PowerMockito.when(bBomHeaderRepository.selectBatchByHeadId(anyString())).thenReturn(bBomHeader);
        ServiceData serviceData = new ServiceData();
        RetCode retCode = new RetCode();
        retCode.setCode(RetCode.SUCCESS_CODE);
        serviceData.setCode(retCode);
        PowerMockito.when(datawbFeignService.syssubbominfoSave(any())).thenReturn(serviceData);
        bBomHeaderService.updateBBomHeaderByIdSelective(new BBomHeader() {{
            setZjSubcardFlag(Constant.FLAG_Y);
        }});

        RetCode retCode2 = new RetCode();
        retCode2.setCode(RetCode.AUTHFAILED_CODE);
        serviceData.setCode(retCode2);
        PowerMockito.when(datawbFeignService.syssubbominfoSave(any())).thenReturn(serviceData);
        try {
            bBomHeaderService.updateBBomHeaderByIdSelective(new BBomHeader() {{
                setZjSubcardFlag(Constant.FLAG_Y);
            }});
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void testSelectBoardPrice() throws Exception {
        PowerMockito.mockStatic(DatawbRemoteService.class);
        BBomHeaderDTO record1 = new BBomHeaderDTO();
        record1.setChiDesc("ZXR10 8912E FANC/8912E");
        List<String> inProductCodes = new ArrayList<>();
        inProductCodes.add("122096851132AGB");
        record1.setInProductCodes(inProductCodes);
        List<BBomHeader> bomList = new ArrayList<>();
        BBomHeader record = new BBomHeader();
        record.setChiDesc("ZXR10 8912E FANC/8912E");
        record.setProductCode("122096851132AGB");
        bomList.add(record);
        PowerMockito.when(bBomHeaderRepository.selectBBomHeaderByCondition(Mockito.any())).thenReturn(bomList);
        List<BaBomHeadDTO> baBomHeadDTOList = new ArrayList<>();
        BaBomHeadDTO dto = new BaBomHeadDTO();
        dto.setLastPrice("test");
        dto.setBomNo("122096851132AGB");
        baBomHeadDTOList.add(dto);
        PowerMockito.when(DatawbRemoteService.getBomInfoByBomNoList(anyList())).thenReturn(baBomHeadDTOList);
        Page<BBomHeaderDTO> pageInfo = bBomHeaderService.selectBoardPrice(record1);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }

    @Test
    public void testSelectBoardPriceTwo() throws Exception {
        PowerMockito.mockStatic(DatawbRemoteService.class);
        BBomHeaderDTO record1 = new BBomHeaderDTO();
        record1.setChiDesc("ZXR10 8912E FANC/8912E");
        List<String> inProductCodes = new ArrayList<>();
        inProductCodes.add("122096851132AGB");
        inProductCodes.add("122096851132AGB");
        inProductCodes.add("122096851132AGB");
        inProductCodes.add("122096851132AGB");
        inProductCodes.add("122096851132AGB");
        inProductCodes.add("122096851132AGB");
        inProductCodes.add("122096851132AGB");
        inProductCodes.add("122096851132AGB");
        inProductCodes.add("122096851132AGB");
        inProductCodes.add("122096851132AGB");
        inProductCodes.add("122096851132AGB");
        inProductCodes.add("122096851132AGB");
        inProductCodes.add("122096851132AGB");
        inProductCodes.add("122096851132AGB");
        record1.setInProductCodes(inProductCodes);

        try {
            bBomHeaderService.selectBoardPrice(record1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ITEM_NOS_MORE_THAN_TEN, e.getMessage());
        }
    }

    @Test
    public void testSelectBoardPriceThree() throws Exception {
        PowerMockito.mockStatic(DatawbRemoteService.class);
        BBomHeaderDTO record1 = new BBomHeaderDTO();
        record1.setChiDesc("ZXR10 8912E FANC/8912E");
        List<String> inProductCodes = new ArrayList<>();
        inProductCodes.add("122096851132AGB");
        record1.setInProductCodes(inProductCodes);
        List<BBomHeader> bomList = new ArrayList<>();
        BBomHeader record = new BBomHeader();
        bomList.add(record);
        PowerMockito.when(bBomHeaderRepository.selectBBomHeaderByCondition(Mockito.any())).thenReturn(bomList);
        try {
            bBomHeaderService.selectBoardPrice(record1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ITEM_NOS_MORE_THAN_TEN, e.getMessage());
        }
    }

    @Test
    public void testSelectBoardPriceFour() throws Exception {
        PowerMockito.mockStatic(DatawbRemoteService.class);
        BBomHeaderDTO record1 = new BBomHeaderDTO();
        List<String> inProductCodes = new ArrayList<>();
        record1.setInProductCodes(inProductCodes);
        try {
            bBomHeaderService.selectBoardPrice(record1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAM_CAN_NOT_BE_NULL, e.getMessage());
        }
    }

    @Test
    public void testBBomHeaderListLike() throws Exception {
        List<BBomHeader> bBomHeaders = new ArrayList<>();
        BBomHeader bBomHeader = new BBomHeader();
        bBomHeaders.add(bBomHeader);
        PowerMockito.when(bBomHeaderRepository.bBomHeaderListLike(Mockito.any())).thenReturn(bBomHeaders);
        bBomHeaderService.bBomHeaderListLike(new BBomHeaderDTO());
        Assert.assertTrue(true);
    }
}
