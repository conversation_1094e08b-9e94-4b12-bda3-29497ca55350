package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.authentication.impl.SysAuthorityServiceImpl;
import com.zte.common.authority.AuthorityUtil;
import com.zte.common.authority.SysAuthorityConfig;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.infrastructure.remote.UcsRemoteService;
import com.zte.interfaces.dto.SysAuthorityLoginDTO;
import com.zte.itp.authorityclient.entity.output.ReturnResourceEntity;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.security.util.SecuredEncUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.ums.zenap.util.cipher.keycenter.agent.utils.GCMHelper;
import org.apache.commons.math3.analysis.function.Pow;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.google.inject.matcher.Matchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({SecuredEncUtil.class,GCMHelper.class})
public class SysAuthorityServiceImplTest {
    @InjectMocks
    private SysAuthorityServiceImpl sysAuthorityServiceImpl;
    @Mock
    private SysAuthorityConfig sysAuthorityConfig;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;

    @Mock
    private HttpServletRequest request;

    @Mock
    UcsRemoteService ucsRemoteService;

    @Mock
    AuthorityUtil authorityUtil;


    @Before
    public void init(){

    }

    @Test
    public void getImesPdaResourceEntryByModuleId(){
        PowerMockito.when(sysAuthorityConfig.getModuleId()).thenReturn("123");

        List<SysLookupValues> lookupList=new ArrayList<>();
        SysLookupValues lookupValues=new SysLookupValues();
        lookupValues.setLookupMeaning("123");
        lookupValues.setAttribute1("IMESPDA");
        lookupList.add(lookupValues);
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.any())).thenReturn(lookupList);

        Map<String, ReturnResourceEntity> mapReturnResourceEntity=new HashMap<>();
        ReturnResourceEntity dto=new ReturnResourceEntity();
        dto.setResourceCode("IMESPDA");
        mapReturnResourceEntity.put("IMESPDA",dto);

        Assert.assertNotNull(sysAuthorityServiceImpl.getImesPdaResourceEntryByModuleId(mapReturnResourceEntity));

    }


    @Test
    @PrepareForTest({MESHttpHelper.class, HttpClientUtil.class, JSONObject.class})
    public void queryPersonGeneralInfo() {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(JSONObject.class);
        try {
            sysAuthorityServiceImpl.queryPersonGeneralInfo("123");

        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }

    }

    @Test
    public void login()throws Exception{
        PowerMockito.when(ucsRemoteService.encryptTemporaryKey(ArgumentMatchers.any(),anyString())).thenReturn("2");
        PowerMockito.when(ucsRemoteService.encryptParams(ArgumentMatchers.any(), ArgumentMatchers.any())).thenReturn("cipherHelper");
        PowerMockito.mockStatic(SecuredEncUtil.class,GCMHelper.class);
        PowerMockito.when(GCMHelper.generateAESKey(anyInt())).thenReturn("0jFlfde7wSFS3iGeRCPalbzyWuNlRMmMQ6EHMsSeDAk=");
        ReflectionTestUtils.setField(sysAuthorityServiceImpl,"ucsSwitch","Y",String.class);
        ReflectionTestUtils.setField(sysAuthorityServiceImpl,"ucsDomainUrl","url",String.class);
        PowerMockito.when(ucsRemoteService.getPublicKey()).thenReturn("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnVwKdMLLvugC8Jh5ANocw0sST6oiUXa65M+seqYHDJax/w7D2bGYwKo9Rw8KU5BkwWZUKz2ko2yv+biJVTaJDvurudP0vCrLmgtTZQqkpKSr/lt0dePM+sCAvq7EJwFE31FV+HDG704KPDm898rZhOnzd+2xhwTrCRDXWCAkuFNbsYnTjcvmiBpHQNUCdvanajUX+lEhcLZuZ1f8wPlRu+rwKwmLX62ulfiuDounksIS0z0O0Hy0XGKr2tCoaX2rZh6Sc3slJlPqhofCsMRx9t81in1ZQo7NHuokispR1OUfio8VAqMdYa2oDelZZZ22y7uN2F+OWxVpSQfHaIhs3wIDAQAB");
        SysAuthorityLoginDTO sysAuthorityLoginDTO = new SysAuthorityLoginDTO();
        sysAuthorityServiceImpl.login(request,sysAuthorityLoginDTO);
        Assert.assertNotNull(sysAuthorityLoginDTO);

        ReflectionTestUtils.setField(sysAuthorityServiceImpl,"ucsSwitch","N",String.class);
        PowerMockito.when(SecuredEncUtil.invokeHttp(anyString(),anyString(),anyString(),anyMap())).thenReturn(JSON.toJSONString(new ServiceData<>()));
        sysAuthorityServiceImpl.login(request,sysAuthorityLoginDTO);
        Assert.assertNotNull(sysAuthorityLoginDTO);
    }

}
