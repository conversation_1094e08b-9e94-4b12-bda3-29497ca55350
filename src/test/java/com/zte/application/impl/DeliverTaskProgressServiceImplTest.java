package com.zte.application.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteWorkbook;
import com.google.common.collect.Lists;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.interfaces.dto.DeliverTaskProgressDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisHelper.class, ImesExcelUtil.class, EasyExcelFactory.class})
public class DeliverTaskProgressServiceImplTest extends BaseTestCase {

    @InjectMocks
    private DeliverTaskProgressServiceImpl service;
    @Mock
    private DeliverTaskProgressRepository deliverTaskProgressRepository;

    @Test
    public void deliverTaskProgressInfo() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        List<DeliverTaskProgressDTO> dataInfos = new ArrayList<>();
        Whitebox.invokeMethod(service, "deliverTaskProgressInfo", dataInfos);
        DeliverTaskProgressDTO dto = new DeliverTaskProgressDTO();
        dto.setProdplanNo("1233");
        dataInfos.add(dto);
        service.deliverTaskProgressInfo(dataInfos);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        try {
            service.deliverTaskProgressInfo(dataInfos);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void queryDeliverTaskProgressInfo() throws Exception {
        DeliverTaskProgressDTO dto = new DeliverTaskProgressDTO();
        dto.setProdplanNo("1233");
        List<DeliverTaskProgressDTO> outList = new ArrayList<>();
        outList.add(dto);
        PowerMockito.when(deliverTaskProgressRepository.queryDeliverTaskProgressInfo(Mockito.any())).thenReturn(outList);
        ServiceData res = service.queryDeliverTaskProgressInfo(dto);
        Assert.assertNotNull(res);
    }

    @Test
    public void writeExcelData() throws Exception {
        service.writeDeliverExcelData(new DeliverTaskProgressDTO(), new ExcelWriter(new WriteWorkbook()), 1, 1);
        PowerMockito.when(deliverTaskProgressRepository.queryDeliverTaskProgressInfo(any()))
                .thenReturn(Lists.newArrayList(new DeliverTaskProgressDTO()));
        service.writeDeliverExcelData(new DeliverTaskProgressDTO(), new ExcelWriter(new WriteWorkbook()), 1, 1);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void exportDeliverTaskProgressInfo() throws Exception {
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
        try {
            service.exportDeliverTaskProgressInfo(new DeliverTaskProgressDTO(), response);
            String runNormal = "Y";
            Assert.assertEquals(Constant.Y_STATUS, runNormal);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }


}
