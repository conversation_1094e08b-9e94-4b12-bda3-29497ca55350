package com.zte.application.impl;

import com.zte.application.impl.user.CfUserMenuFavoritesServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.user.CfUserMenuFavoritesRepository;
import com.zte.interfaces.dto.user.CfUserMenuFavoritesDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.util.Pair;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-11-15 16:48
 */
@PrepareForTest({RequestHeadValidationUtil.class})
public class CfUserMenuFavoritesServiceImplTest extends BaseTestCase {
    @InjectMocks
    private CfUserMenuFavoritesServiceImpl cfUserMenuFavoritesService;
    @Mock
    private CfUserMenuFavoritesRepository cfUserMenuFavoritesRepository;
    @Mock
    private Integer cfUserMenuMaxCount;
    @Mock
    private IdGenerator idGenerator;

    @Before
    public void init() {
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);

        PowerMockito.when(RequestHeadValidationUtil.validaFactoryIdAndEmpno())
                .thenReturn(Pair.of("52", "44"));
    }

    @Test
    public void insertFavoritesOneOrDeleteOne() throws Exception {
        CfUserMenuFavoritesDTO cfUserMenuFavoritesDTO = new CfUserMenuFavoritesDTO();
        PowerMockito.when(cfUserMenuFavoritesRepository.selectUserMenuAll(Mockito.anyString(), Mockito.any()))
                .thenReturn(null);
        cfUserMenuFavoritesService.insertFavoritesOneOrDeleteOne(cfUserMenuFavoritesDTO);

        List<CfUserMenuFavoritesDTO> list = new LinkedList<>();
        CfUserMenuFavoritesDTO a1 = new CfUserMenuFavoritesDTO();
        a1.setFavoritesId("123");
        list.add(a1);
        PowerMockito.when(cfUserMenuFavoritesRepository.selectUserMenuAll(Mockito.anyString(), Mockito.any()))
                .thenReturn(list);
        PowerMockito.field(CfUserMenuFavoritesServiceImpl.class, "cfUserMenuMaxCount")
                .set(cfUserMenuFavoritesService,0);
        try {
            cfUserMenuFavoritesService.insertFavoritesOneOrDeleteOne(cfUserMenuFavoritesDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CF_USER_MENU_MAX_COUNT, e.getMessage());
        }

        PowerMockito.field(CfUserMenuFavoritesServiceImpl.class, "cfUserMenuMaxCount")
                .set(cfUserMenuFavoritesService,1);
        PowerMockito.when(idGenerator.snowFlakeIdStr()).thenReturn("123");
        PowerMockito.when(cfUserMenuFavoritesRepository.deleteFavoritesOne(Mockito.any())).thenReturn(1);
        cfUserMenuFavoritesService.insertFavoritesOneOrDeleteOne(cfUserMenuFavoritesDTO);
    }

    @Test
    public void insertFavoritesOne() {
        CfUserMenuFavoritesDTO cfUserMenuFavoritesDTO = new CfUserMenuFavoritesDTO();
        cfUserMenuFavoritesService.insertFavoritesOne(cfUserMenuFavoritesDTO);
        Assert.assertNotNull(cfUserMenuFavoritesDTO);
    }

    @Test
    public void deleteFavoritesOne() {
        CfUserMenuFavoritesDTO cfUserMenuFavoritesDTO = new CfUserMenuFavoritesDTO();
        cfUserMenuFavoritesService.deleteFavoritesOne(cfUserMenuFavoritesDTO);
        Assert.assertNotNull(cfUserMenuFavoritesDTO);
    }

    @Test
    public void queryPageMenuFavoritesList() {
        Page<CfUserMenuFavoritesDTO>  pageInfo = cfUserMenuFavoritesService.queryPageMenuFavoritesList(new Page<>());
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }

    @Test
    public void selectUserMenuAll() {
        Assert.assertNotNull(cfUserMenuFavoritesService.selectUserMenuAll());
    }
}
