package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.domain.model.ApprovalConfig;
import com.zte.domain.model.ApprovalConfigRepository;
import com.zte.interfaces.dto.ApprovalConfigDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;


@RunWith(PowerMockRunner.class)
public class ApprovalConfigServiceImplTest extends BaseTestCase {
    @InjectMocks
    private ApprovalConfigServiceImpl service;

    @Mock
    ApprovalConfigRepository approvalConfigRepository;

    @Test
    public void getApprovalConfigPage() {
        ApprovalConfigDTO dto = new ApprovalConfigDTO();
        try {
            service.getApprovalConfigPage(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getExMsgId());
        }

        dto.setFlowCode("FlowCode");

        PowerMockito.when(approvalConfigRepository.getApprovalConfigCount(Mockito.any())).thenReturn(1L);

        List<ApprovalConfig> configs = new ArrayList<>();
        ApprovalConfig config = new ApprovalConfig();
        configs.add(config);
        PowerMockito.when(approvalConfigRepository.getApprovalConfig(Mockito.any())).thenReturn(configs);

        PageRows<ApprovalConfigDTO> approvalConfigPage = service.getApprovalConfigPage(dto);
        Assert.assertEquals(1, approvalConfigPage.getRows().size());


        dto.setFlowCode(null);
        dto.setConfigId("aa");
        approvalConfigPage = service.getApprovalConfigPage(dto);
        Assert.assertEquals(1, approvalConfigPage.getRows().size());

        dto.setConfigId(null);
        dto.setNodeCode("aa");
        approvalConfigPage = service.getApprovalConfigPage(dto);
        Assert.assertEquals(1, approvalConfigPage.getRows().size());

        dto.setNodeCode(null);
        dto.setNodeName("aa");
        approvalConfigPage = service.getApprovalConfigPage(dto);
        Assert.assertEquals(1, approvalConfigPage.getRows().size());

        dto.setNodeName(null);
        dto.setStartTime("aa");
        dto.setEndTime("aa");
        approvalConfigPage = service.getApprovalConfigPage(dto);
        Assert.assertEquals(1, approvalConfigPage.getRows().size());

        dto.setStartTime(null);
        try {
            service.getApprovalConfigPage(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getExMsgId());
        }

        dto.setStartTime("aa");
        dto.setEndTime(null);
        try {
            service.getApprovalConfigPage(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getExMsgId());
        }
    }

    @Test
    public void setApprovalConfig() {
        ApprovalConfigDTO dto = new ApprovalConfigDTO();
        dto.setConfigId("aaaa");
        PowerMockito.when(approvalConfigRepository.getApprovalConfig(Mockito.any())).thenReturn(null);
        try {
            service.setApprovalConfig(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getExMsgId());
        }
        dto.setUpdate(false);
        try {
            service.setApprovalConfig(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getExMsgId());
        }

        dto.setUpdate(true);
        PowerMockito.when(approvalConfigRepository.updateByPrimaryKey(Mockito.any())).thenReturn(1);
        int i = service.setApprovalConfig(dto);
        Assert.assertEquals(1, 1);

        List<ApprovalConfig> configs = new ArrayList<>();
        ApprovalConfig config = new ApprovalConfig();
        configs.add(config);
        PowerMockito.when(approvalConfigRepository.getApprovalConfig(Mockito.any())).thenReturn(configs);
        PowerMockito.when(approvalConfigRepository.insert(Mockito.any())).thenReturn(1);
        i = service.setApprovalConfig(dto);
        Assert.assertEquals(1, 1);

        dto.setUpdate(null);
        try {
            service.setApprovalConfig(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getExMsgId());
        }

        dto.setUpdate(false);
        try {
            service.setApprovalConfig(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getExMsgId());
        }
    }

    @Test
    public void deleteApprovalConfig() {
        PowerMockito.when(approvalConfigRepository.deleteByConfigId("1111")).thenReturn(1);
        Assert.assertNotNull(service.deleteApprovalConfig("1111"));
    }


    @Test
    public void getNodeNameToCode() {

        Map<String, String> a = service.getNodeNameToCode("");
        Assert.assertEquals(0, a.size());

        List<ApprovalConfig> configs = new ArrayList<>();
        ApprovalConfig config = new ApprovalConfig();
        config.setNodeName("222");
        config.setNodeCode("111");
        configs.add(config);
        PowerMockito.when(approvalConfigRepository.getApprovalConfig(Mockito.any())).thenReturn(configs);
        Map<String, String> b = service.getNodeNameToCode("111");
        Assert.assertEquals(1, b.size());

        PowerMockito.when(approvalConfigRepository.getApprovalConfig(Mockito.any())).thenReturn(new ArrayList<>());
        Map<String, String> c = service.getNodeNameToCode("111");
        Assert.assertEquals(0, c.size());

        ApprovalConfig config1 = new ApprovalConfig();
        config1.setNodeCode("111");
        configs.add(config1);
        PowerMockito.when(approvalConfigRepository.getApprovalConfig(Mockito.any())).thenReturn(configs);
        Map<String, String> d = service.getNodeNameToCode("111");
        Assert.assertEquals(1, d.size());

        ApprovalConfig config2 = new ApprovalConfig();
        config2.setNodeName("111");
        configs.add(config2);
        PowerMockito.when(approvalConfigRepository.getApprovalConfig(Mockito.any())).thenReturn(configs);
        Map<String, String> e = service.getNodeNameToCode("111");
        Assert.assertEquals(1, e.size());
    }

}