package com.zte.application.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.impl.busmanage.BmEmployeeInfoServiceImpl;
import com.zte.interfaces.dto.busmanage.BmEmployeeInfoDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/2/11 16:28
 */
@RunWith(PowerMockRunner.class)
public class BmEmployeeInfoServiceImplTest {

    @InjectMocks
    private BmEmployeeInfoServiceImpl service;

    @Test
    @PrepareForTest({MESHttpHelper.class, JacksonJsonConverUtil.class, HttpClientUtil.class})
    public void getUserInfoMessage () throws Exception{
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        List<String> ids = new ArrayList<>();
        try {
            Assert.assertNull(Whitebox.invokeMethod(service, "getUserInfoMessage", ids));

        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }
    @Test
    @PrepareForTest({MESHttpHelper.class, JacksonJsonConverUtil.class, HttpClientUtil.class})
    public void getEmployeeInfo () throws Exception{
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        BmEmployeeInfoDTO dto = new BmEmployeeInfoDTO();
        try {
            Assert.assertNull(Whitebox.invokeMethod(service, "getEmpInfoByWorkNo", dto));
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }
}
