/*Started by AICoder, pid:e5db7h1548bf06514e290a43318dfa39f6e7da9d*/
package com.zte.application.impl;
import com.zte.application.BProdBomDetailService;
import com.zte.domain.model.BBomDetailRepository;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BProdBomDetailDTO;
import com.zte.util.BaseTestCase;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)

public class BBomDetailServiceImplTest  extends BaseTestCase {

    @InjectMocks
    private BBomDetailServiceImpl bBomDetailService;

    @Mock
    private BProdBomDetailService bProdBomDetailService;

    @Mock
    private BBomDetailRepository bBomDetailRepository;

    private BBomDetailDTO dto;

    @Before
    public void setUp() {
        dto = new BBomDetailDTO();
    }

    @Test
    public void testGetBomDetailItemList_ProdplanIdNotEmpty_BProdBomDetailListNotEmpty() {
        // Given
        String prodplanId = "testPlanId";
        dto.setProdplanId(prodplanId);

        BProdBomDetailDTO bProdBomDetailDTO = new BProdBomDetailDTO();
        bProdBomDetailDTO.setProdplanId(prodplanId);

        List<BProdBomDetailDTO> bProdBomDetailItemList = new ArrayList<>();
        bProdBomDetailItemList.add(bProdBomDetailDTO);

        when(bProdBomDetailService.getBProdBomDetailItemList(any(BProdBomDetailDTO.class)))
                .thenReturn(bProdBomDetailItemList);

        // When
        List<BBomDetailDTO> result = bBomDetailService.getBomDetailItemList(dto);

        // Then
        assertEquals(1, result.size());
        verify(bProdBomDetailService, times(1)).getBProdBomDetailItemList(any(BProdBomDetailDTO.class));
        verify(bBomDetailRepository, never()).getBomDetailItemList(any(BBomDetailDTO.class));
    }

    @Test
    public void testGetBomDetailItemList_ProdplanIdNotEmpty_BProdBomDetailListEmpty() {
        // Given
        String prodplanId = "testPlanId";
        dto.setProdplanId(prodplanId);

        when(bProdBomDetailService.getBProdBomDetailItemList(any(BProdBomDetailDTO.class)))
                .thenReturn(Collections.emptyList());

        List<BBomDetailDTO> repositoryList = new ArrayList<>();
        when(bBomDetailRepository.getBomDetailItemList(any(BBomDetailDTO.class)))
                .thenReturn(repositoryList);

        // When
        List<BBomDetailDTO> result = bBomDetailService.getBomDetailItemList(dto);

        // Then
        assertEquals(0, result.size());
        verify(bProdBomDetailService, times(1)).getBProdBomDetailItemList(any(BProdBomDetailDTO.class));
        verify(bBomDetailRepository, times(1)).getBomDetailItemList(any(BBomDetailDTO.class));
    }

    @Test
    public void testGetBomDetailItemList_ProdplanIdEmpty() {
        // Given
        dto.setProdplanId("");

        List<BBomDetailDTO> repositoryList = new ArrayList<>();
        when(bBomDetailRepository.getBomDetailItemList(any(BBomDetailDTO.class)))
                .thenReturn(repositoryList);

        // When
        List<BBomDetailDTO> result = bBomDetailService.getBomDetailItemList(dto);

        // Then
        assertEquals(0, result.size());
        verify(bProdBomDetailService, never()).getBProdBomDetailItemList(any(BProdBomDetailDTO.class));
        verify(bBomDetailRepository, times(1)).getBomDetailItemList(any(BBomDetailDTO.class));
    }

    @Test
    public void testGetBomItemList() {
        // Given
        BBomDetailDTO record = new BBomDetailDTO();
        record.setProdplanId("123");
        List<BBomDetailDTO> repositoryList = new ArrayList<>();
        PowerMockito.when(bBomDetailRepository.getBomDetailItemList(any()))
                .thenReturn(repositoryList);

        List<BProdBomDetailDTO> bProdBomDetailItemList = new ArrayList<>();
        PowerMockito.when(bProdBomDetailService.getBProdBomDetailItemList(any(BProdBomDetailDTO.class)))
                .thenReturn(bProdBomDetailItemList);
        // When
        List<BBomDetailDTO> result = bBomDetailService.getBomItemList(record);
        // Then
        assertEquals(0, result.size());

        BProdBomDetailDTO bProdBomDetailDTO = new BProdBomDetailDTO();
        bProdBomDetailDTO.setUsageCount(new BigDecimal("1"));
        bProdBomDetailDTO.setItemCode("itemcode");
        bProdBomDetailItemList.add(bProdBomDetailDTO);

        // When
        result = bBomDetailService.getBomItemList(record);
        // Then
        assertEquals(1, result.size());
        record.setProdplanId("");
        // When
        result = bBomDetailService.getBomItemList(record);
        // Then
        assertEquals(0, result.size());
    }
}
/*Ended by AICoder, pid:e5db7h1548bf06514e290a43318dfa39f6e7da9d*/