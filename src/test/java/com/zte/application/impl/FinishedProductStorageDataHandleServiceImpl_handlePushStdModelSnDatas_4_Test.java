/*Started by AICoder, pid:ub286x73b56faa714a36090d014aab08b6e5e7e7*/
package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.application.PushStdModelSnDataService;
import com.zte.application.TradeDataLogService;
import com.zte.application.cbom.FixBomCommonService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PushStdModelSnDataRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.infrastructure.feign.ApsInOneClient;
import com.zte.interfaces.dto.ApsCustInstructBomDTO;
import com.zte.interfaces.dto.ApsCustomerTaskDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.PushStdModelSnDataDTO;
import com.zte.interfaces.dto.PushStdModelSnDataExtDTO;
import com.zte.interfaces.dto.PushStdModelSnDataHandleDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.IdGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class FinishedProductStorageDataHandleServiceImpl_handlePushStdModelSnDatas_4_Test {

    @Mock
    private PushStdModelSnDataRepository pushStdModelSnDataRepository;

    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;

    @Mock
    private FixBomCommonService fixBomCommonService;
    @Mock
    private ApsInOneClient apsInOneClient;
    @Mock
    private IdGenerator idGenerator;
    @Mock
    private TradeDataLogService tradeDataLogService;
    @Mock
    PushStdModelSnDataService pushStdModelSnDataService;

    @InjectMocks
    private FinishedProductStorageDataHandleServiceImpl finishedProductStorageDataHandleService;

    @Before
    public void setUp() {
        // Setup code if needed
    }

    @Test
    public void testHandlePushStdModelSnDatas_EmptyList() {
        List<PushStdModelSnDataHandleDTO> pushStdModelSnDataHandles = Collections.emptyList();
        boolean result = finishedProductStorageDataHandleService.handlePushStdModelSnDatas(pushStdModelSnDataHandles);
        assertFalse(result);
    }

    @Test
    public void testHandlePushStdModelSnDatas_NoDataInDatabase() {
        List<PushStdModelSnDataHandleDTO> pushStdModelSnDataHandles = Arrays.asList(new PushStdModelSnDataHandleDTO() {{setId("1");}});
        when(pushStdModelSnDataRepository.selectExtByPrimaryKeys(anyList())).thenReturn(Collections.emptyList());

        boolean result = finishedProductStorageDataHandleService.handlePushStdModelSnDatas(pushStdModelSnDataHandles);

        assertFalse(result);
        verify(pushStdModelSnDataRepository, times(1)).selectExtByPrimaryKeys(anyList());
    }

    @Test
    public void testHandlePushStdModelSnDatas_DataInDatabasePushed() {
        List<PushStdModelSnDataHandleDTO> pushStdModelSnDataHandles = Arrays.asList(new PushStdModelSnDataHandleDTO() {{setId("1");}});
        when(pushStdModelSnDataRepository.selectExtByPrimaryKeys(anyList())).thenReturn(Collections.singletonList(new PushStdModelSnDataExtDTO() {{ setPushStatus(1); }}));

        boolean result = finishedProductStorageDataHandleService.handlePushStdModelSnDatas(pushStdModelSnDataHandles);

        assertFalse(result);
        verify(pushStdModelSnDataRepository, times(1)).selectExtByPrimaryKeys(anyList());
    }

    @Test
    public void testHandlePushStdModelSnDatas_WithDataInDatabase_TaskNoHasNotCallbackData() {
        List<PushStdModelSnDataHandleDTO> pushStdModelSnDataHandles = Arrays.asList(
                new PushStdModelSnDataHandleDTO() {{setId("3"); setStockName("subStock1"); setWipExtendIdentifications(Lists.newArrayList());}}
        );
        List<PushStdModelSnDataExtDTO> pushStdModelSnDataList = Arrays.asList(
                new PushStdModelSnDataExtDTO() {{setId("3"); setPushStatus(0); setTaskNo("taskNo1"); setTaskFixBomId("fixBomId1");}}
        );
        List<FixBomDetailDTO> fixBomDetails = Collections.singletonList(new FixBomDetailDTO());

        when(pushStdModelSnDataRepository.selectExtByPrimaryKeys(anyList())).thenReturn(pushStdModelSnDataList);
        when(pushStdModelSnDataRepository.selectByQuery(any()))
                .thenReturn(Lists.newArrayList(new PushStdModelSnDataDTO()));


        SysLookupValues prodStorageMaterialSignLookupValues = new SysLookupValues();
        prodStorageMaterialSignLookupValues.setLookupMeaning("child11ItemSupplierNo");

        SysLookupValues stockConfig = new SysLookupValues();
        stockConfig.setAttribute2("subStock");
        stockConfig.setLookupCode(new BigDecimal(2));
        stockConfig.setRemark(Constant.STR_DEFECTIVE_PRODUCT_WAREHOUSE);

        when(sysLookupValuesRepository.selectValuesByType(anyInt()))
                .thenReturn(Lists.newArrayList(stockConfig))
                .thenReturn(Lists.newArrayList(prodStorageMaterialSignLookupValues));
        when(fixBomCommonService.queryTreeNodeByFixBomId(anyString())).thenReturn(fixBomDetails);

        ApsCustomerTaskDTO apsCustomerTask = new ApsCustomerTaskDTO();
        ApsCustInstructBomDTO apsCustInstructBom = new ApsCustInstructBomDTO();
        apsCustInstructBom.setMaterialSign("materialSign");
        apsCustInstructBom.setMaterialBillType("NEW_INSTRUCT_BOM");
        apsCustomerTask.setBomList(Lists.newArrayList(apsCustInstructBom));

        Page<ApsCustomerTaskDTO> page = mock(Page.class);
        when(page.getRows()).thenReturn(Lists.newArrayList(apsCustomerTask));
        when(apsInOneClient.customerTaskQueryWithDetail(any())).thenReturn(page);

        boolean result = finishedProductStorageDataHandleService.handlePushStdModelSnDatas(pushStdModelSnDataHandles);

        assertTrue(result);
        verify(pushStdModelSnDataRepository, times(1)).selectExtByPrimaryKeys(anyList());
        verify(sysLookupValuesRepository, times(1)).selectValuesByType(anyInt());


    }

    @Test
    public void testHandlePushStdModelSnDatas_WithDataInDatabase_PushException() {
        List<PushStdModelSnDataHandleDTO> pushStdModelSnDataHandles = Arrays.asList(
                new PushStdModelSnDataHandleDTO() {{setId("1"); setWipExtendIdentifications(Lists.newArrayList());}});
        List<PushStdModelSnDataExtDTO> pushStdModelSnDataList = Arrays.asList(
                new PushStdModelSnDataExtDTO() {{setId("1"); setPushStatus(0); setTaskNo("taskNo"); setTaskFixBomId("fixBomId"); setTaskCustomerPartType("test"); setTaskCustomerItemName("taskCustomerItemName"); }}
        );
        List<FixBomDetailDTO> fixBomDetails = Collections.singletonList(new FixBomDetailDTO() {{ setCustomerComponentType("test"); }});

        when(pushStdModelSnDataRepository.selectExtByPrimaryKeys(anyList())).thenReturn(pushStdModelSnDataList);
        when(pushStdModelSnDataRepository.selectByQuery(any())).thenReturn(Lists.newArrayList());


        SysLookupValues prodStorageMaterialSignLookupValues = new SysLookupValues();
        prodStorageMaterialSignLookupValues.setLookupMeaning("child11ItemSupplierNo");

        SysLookupValues stockConfig = new SysLookupValues();
        stockConfig.setAttribute2("subStock");
        stockConfig.setRemark(Constant.STR_DEFECTIVE_PRODUCT_WAREHOUSE);

        when(sysLookupValuesRepository.selectValuesByType(anyInt()))
                .thenReturn(Lists.newArrayList(stockConfig))
                .thenReturn(Lists.newArrayList(prodStorageMaterialSignLookupValues));
        when(fixBomCommonService.queryTreeNodeByFixBomId(anyString())).thenReturn(fixBomDetails);

        ApsCustomerTaskDTO apsCustomerTask = new ApsCustomerTaskDTO();
        ApsCustInstructBomDTO apsCustInstructBom = new ApsCustInstructBomDTO();
        apsCustInstructBom.setMaterialSign("materialSign");
        apsCustInstructBom.setMaterialBillType("NEW_INSTRUCT_BOM");
        apsCustomerTask.setBomList(Lists.newArrayList(apsCustInstructBom));

        Page<ApsCustomerTaskDTO> page = mock(Page.class);
        when(page.getRows()).thenReturn(Lists.newArrayList(apsCustomerTask));
        when(apsInOneClient.customerTaskQueryWithDetail(any())).thenReturn(page);
        PowerMockito.doThrow(new RuntimeException()).when(tradeDataLogService).pushDataOfExceptionRollback(any(CustomerDataLogDTO.class));

        boolean result = finishedProductStorageDataHandleService.handlePushStdModelSnDatas(pushStdModelSnDataHandles);

        assertTrue(result);
        verify(pushStdModelSnDataRepository, times(1)).selectExtByPrimaryKeys(anyList());
    }

    @Test
    public void testHandlePushStdModelSnDatas_WithDataInDatabase() {
        List<PushStdModelSnDataHandleDTO> pushStdModelSnDataHandles = Arrays.asList(
                new PushStdModelSnDataHandleDTO() {{setId("1"); setWipExtendIdentifications(Lists.newArrayList());}},
                new PushStdModelSnDataHandleDTO() {{setId("4"); setWipExtendIdentifications(Lists.newArrayList());}},
                new PushStdModelSnDataHandleDTO() {{setId("3"); setStockName("subStock1"); setWipExtendIdentifications(Lists.newArrayList());}}
        );
        List<PushStdModelSnDataExtDTO> pushStdModelSnDataList = Arrays.asList(
                new PushStdModelSnDataExtDTO() {{setId("1"); setPushStatus(0); setTaskNo("taskNo"); setTaskFixBomId("fixBomId"); setTaskCustomerPartType("test"); }},
                new PushStdModelSnDataExtDTO() {{setId("2"); setPushStatus(0); setTaskNo("taskNo"); setTaskCustomerPartType("test"); }},
                new PushStdModelSnDataExtDTO() {{setId("3"); setPushStatus(0); setTaskNo("taskNo1"); setTaskFixBomId("fixBomId1"); setTaskCustomerPartType("test"); setTaskCustomerItemName("taskCustomerItemName"); }},
                new PushStdModelSnDataExtDTO() {{setId("4"); setPushStatus(0); setTaskNo("taskNo2"); setTaskFixBomId("fixBomId2"); }},
                new PushStdModelSnDataExtDTO() {{setId("5"); setPushStatus(0); setTaskNo("taskNo3"); setTaskFixBomId("fixBomId3"); }}
        );
        List<FixBomDetailDTO> fixBomDetails = Collections.singletonList(new FixBomDetailDTO() {{ setCustomerComponentType("test"); }});

        when(pushStdModelSnDataRepository.selectExtByPrimaryKeys(anyList())).thenReturn(pushStdModelSnDataList);
        when(pushStdModelSnDataRepository.selectByQuery(any())).thenReturn(Lists.newArrayList());


        SysLookupValues prodStorageMaterialSignLookupValues = new SysLookupValues();
        prodStorageMaterialSignLookupValues.setLookupMeaning("child11ItemSupplierNo");

        SysLookupValues stockConfig = new SysLookupValues();
        stockConfig.setAttribute2("subStock");
        stockConfig.setLookupCode(new BigDecimal(Constant.LOOKUP_CODE_BAD_WAREHOUSE));
        stockConfig.setRemark(Constant.STR_DEFECTIVE_PRODUCT_WAREHOUSE);

        when(sysLookupValuesRepository.selectValuesByType(anyInt()))
                .thenReturn(Lists.newArrayList(stockConfig))
                .thenReturn(Lists.newArrayList(prodStorageMaterialSignLookupValues));
        when(fixBomCommonService.queryTreeNodeByFixBomId(anyString())).thenReturn(fixBomDetails);

        ApsCustomerTaskDTO apsCustomerTask = new ApsCustomerTaskDTO();
        ApsCustInstructBomDTO apsCustInstructBom = new ApsCustInstructBomDTO();
        apsCustInstructBom.setMaterialSign("materialSign");
        apsCustInstructBom.setMaterialBillType("NEW_INSTRUCT_BOM");
        apsCustomerTask.setBomList(Lists.newArrayList(apsCustInstructBom));

        Page<ApsCustomerTaskDTO> page = mock(Page.class);
        when(page.getRows()).thenReturn(Lists.newArrayList(apsCustomerTask));
        when(apsInOneClient.customerTaskQueryWithDetail(any())).thenThrow(new RuntimeException()).thenReturn(page);
        PowerMockito.doNothing().when(tradeDataLogService).pushDataOfExceptionRollback(any(CustomerDataLogDTO.class));

        boolean result = finishedProductStorageDataHandleService.handlePushStdModelSnDatas(pushStdModelSnDataHandles);

        assertTrue(result);
        verify(pushStdModelSnDataRepository, times(1)).selectExtByPrimaryKeys(anyList());
    }
}
/*Ended by AICoder, pid:ub286x73b56faa714a36090d014aab08b6e5e7e7*/