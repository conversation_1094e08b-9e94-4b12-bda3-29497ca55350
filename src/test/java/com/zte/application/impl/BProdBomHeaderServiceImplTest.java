package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.BBomDetailService;
import com.zte.application.BBomHeaderService;
import com.zte.application.BProdBomChangeDetailService;
import com.zte.application.BProdBomDetailService;
import com.zte.application.CenterFactoryCallSiteService;
import com.zte.application.kafka.producer.KafkaMessageProducer;
import com.zte.application.sncabind.PsTaskService;
import com.zte.common.CommonUtils;
import com.zte.domain.model.BBomHeader;
import com.zte.domain.model.BProdBomHeader;
import com.zte.domain.model.BProdBomHeaderRepository;
import com.zte.domain.model.KafkaLocalMessageRepository;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.infrastructure.remote.ApsRemoteService;
import com.zte.infrastructure.remote.OpenApiRemoteService;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.aps.ApsDerivativeCodeDTO;
import com.zte.interfaces.dto.aps.ApsDerivativeCodeQueryDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ReflectUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * ClassName: BProdBomHeaderServiceImplTest
 * Description:
 *
 * <AUTHOR>
 * @date 2024/12/17 下午3:43
 */
@PrepareForTest({CommonUtils.class,HttpRemoteUtil.class, ServiceDataBuilderUtil.class,MESHttpHelper.class})
public class BProdBomHeaderServiceImplTest extends BaseTestCase {
    @InjectMocks
    private BProdBomHeaderServiceImpl service;

    @Mock
    private BProdBomHeaderRepository bProdBomHeaderRepository;
    @Mock
    private BProdBomDetailService bProdBomDetailService;
    @Mock
    private BProdBomChangeDetailService bProdBomChangeDetailService;
    @Mock
    private CenterFactoryCallSiteService centerFactoryCallSiteService;
    @Mock
    private OpenApiRemoteService openApiRemoteService;
    @Mock
    private PsTaskService psTaskService;
    @Mock
    private KafkaMessageProducer kafkaMessageProducer;
    @Mock
    private KafkaLocalMessageRepository kafkaLocalMessageRepository;

    @Mock
    private BBomDetailService bBomDetailService;
    @Mock
    private BBomHeaderService bBomHeaderervice;
    @Mock
    protected IdGenerator idGenerator;
    @Mock
    protected ApsRemoteService apsRemoteService;

    @Test
    public void deleteByProdplanIds()throws Exception {
        List<String> prodList = new ArrayList<>();
        Whitebox.invokeMethod(service,"deleteByProdplanIds",prodList);
        assertEquals(prodList.size(), 0);
        prodList.add("21");
        Whitebox.invokeMethod(service,"deleteByProdplanIds",prodList);
        assertEquals(prodList.size(), 1);

    }
    @Test
    public void deleteTheOriginalManufacturingBOM()throws Exception {
        List<ApsDerivativeCodeQueryDTO> apsDerivativeCodeDTOList = new ArrayList<>();
        apsDerivativeCodeDTOList.add(new ApsDerivativeCodeQueryDTO(){{setProdplanId("4");setItemNo("5");}});
        apsDerivativeCodeDTOList.add(new ApsDerivativeCodeQueryDTO(){{setProdplanId("3");setItemNo("2");}});

        List<BProdBomHeaderDTO> bProdBomHeaderDTOList = new ArrayList<>();
        bProdBomHeaderDTOList.add(new BProdBomHeaderDTO(){{setOriginalProductCode("3");setProdplanId("2");}});
        bProdBomHeaderDTOList.add(new BProdBomHeaderDTO(){{setOriginalProductCode("2");setProdplanId("3");setOriginalItemCode("4");setItemCode("5");}});
        PowerMockito.when(bProdBomHeaderRepository.queryByItemNoList(any())).thenReturn(bProdBomHeaderDTOList);
        Whitebox.invokeMethod(service,"deleteTheOriginalManufacturingBOM",apsDerivativeCodeDTOList);
        assertEquals(apsDerivativeCodeDTOList.size(), 2);

    }
    @Test
    public void getSameBProdHeader()throws Exception {
        List<BProdBomHeaderDTO> bProdBomHeaderDTOList = new ArrayList<>();
        bProdBomHeaderDTOList.add(new BProdBomHeaderDTO(){{setProdplanId("2");}});
        bProdBomHeaderDTOList.add(new BProdBomHeaderDTO(){{setProdplanId("3");setOriginalItemCode("4");setItemCode("5");}});
        List<ApsDerivativeCodeDTO> apsDerivativeCodeDTOList = new ArrayList<>();
        apsDerivativeCodeDTOList.add(new ApsDerivativeCodeDTO(){{setDeriveItemNo("4");setItemNo("5");}});
        apsDerivativeCodeDTOList.add(new ApsDerivativeCodeDTO(){{setDeriveItemNo("3");setItemNo("2");}});
        Whitebox.invokeMethod(service,"getSameBProdHeader",bProdBomHeaderDTOList,apsDerivativeCodeDTOList);
        assertEquals(bProdBomHeaderDTOList.size(), 2);

        bProdBomHeaderDTOList.add(new BProdBomHeaderDTO(){{setProdplanId("3");setOriginalItemCode("4");setItemCode("5");}});
        Whitebox.invokeMethod(service,"getSameBProdHeader",bProdBomHeaderDTOList,apsDerivativeCodeDTOList);
        assertEquals(bProdBomHeaderDTOList.size(), 3);

    }

    /*Started by AICoder, pid:b8b7bo0c3b063ae149cb0af23188985713b1f28e*/
    @Test
    public void testQueryProductCodeByTaskNoList_MultipleBatches() {
        List<String> taskNoList = Arrays.asList("task1", "task2", "task3", "task4", "task5", "task6", "task7", "task8", "task9", "task100", "task101");
        List<BProdBomHeaderDTO> batch1Result = Arrays.asList(new BProdBomHeaderDTO(), new BProdBomHeaderDTO());
        List<BProdBomHeaderDTO> batch2Result = Arrays.asList(new BProdBomHeaderDTO());

        List<BProdBomHeaderDTO> result = service.queryProductCodeByTaskNoList(taskNoList);
        assertTrue(CollectionUtils.isEmpty(result));
        List<BProdBomHeaderDTO> tempResult = new LinkedList<>();
        BProdBomHeaderDTO a1 = new BProdBomHeaderDTO();
        tempResult.add(a1);
        PowerMockito.when(bProdBomHeaderRepository.queryProductCodeByTaskNoList(Mockito.any()))
                .thenReturn(tempResult);
        service.queryProductCodeByTaskNoList(taskNoList);

        service.queryProductCodeByTaskNoList(null);
    }

    @Test
    public void deleteProdBomByProdplanIdList()throws Exception {
        List<String> taskNoList = Arrays.asList("task1", "task2");

        Whitebox.invokeMethod(service,"deleteProdBomByProdplanIdList",false,new ArrayList<>());
        assertEquals(taskNoList.size(), 2);

        Whitebox.invokeMethod(service,"deleteProdBomByProdplanIdList",true,new ArrayList<>());
        assertEquals(taskNoList.size(), 2);

        Whitebox.invokeMethod(service,"deleteProdBomByProdplanIdList",true,taskNoList);
        assertEquals(taskNoList.size(), 2);

        PowerMockito.when(bProdBomHeaderRepository.getBProdListByProdplanIdList(anyList())).thenReturn(new ArrayList(){{add(new BProdBomHeaderDTO());}});
        Whitebox.invokeMethod(service,"deleteProdBomByProdplanIdList",true,taskNoList);
        assertEquals(taskNoList.size(), 2);
    }

    @Test
    public void testQueryProductCodeByTaskNoList_EmptyBatchResult() {
        List<String> taskNoList = Arrays.asList("task1", "task2");

        when(bProdBomHeaderRepository.queryProductCodeByTaskNoList(anyList()))
                .thenReturn(Collections.emptyList());

        List<BProdBomHeaderDTO> result = service.queryProductCodeByTaskNoList(taskNoList);
        assertEquals(Collections.emptyList(), result);
    }
    /*Ended by AICoder, pid:b8b7bo0c3b063ae149cb0af23188985713b1f28e*/

    /* Started by AICoder, pid:kb9cdq41da592a81485a08be3058168c18b3ca45 */
    @Test
    public void setProductCode() throws Exception {
        Map<String, List<BProdBomHeaderDTO>> bProdBomHeaderDTOHashMap = new HashMap<>();
        List<BProdBomHeaderDTO> bProdBomHeaderDTOList = new ArrayList<>();
        bProdBomHeaderDTOList.add(new BProdBomHeaderDTO() {{
            setOriginalProductCode("taskNo1");
            setProductCode("taskNo1-1");
        }});
        bProdBomHeaderDTOList.add(new BProdBomHeaderDTO() {{
            setOriginalProductCode("taskNo2");
            setProductCode("taskNo1-1");
        }});
        bProdBomHeaderDTOHashMap.put("taskNo1", bProdBomHeaderDTOList);
        Whitebox.invokeMethod(service, "setProductCode", bProdBomHeaderDTOHashMap, new BProdBomHeader() {{
            setOriginalProductCode("taskNo1");
        }});
        Assert.assertTrue(bProdBomHeaderDTOHashMap.size() == 1);
    }
    /* Ended by AICoder, pid:kb9cdq41da592a81485a08be3058168c18b3ca45 */
    /* Started by AICoder, pid:395cfxf1ffn8048145510aa850eac47dc6f801fb */
    @Test
    public void writeBackAPS() throws Exception {
        List<ApsDerivativeCodeQueryDTO> apsDerivativeCodeQueryDTOList = new ArrayList<>();
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo1");
        }});
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo1");
            setErrorMsg("2");
        }});
        Whitebox.invokeMethod(service, "writeBackAPS", "", apsDerivativeCodeQueryDTOList);
        Assert.assertTrue(apsDerivativeCodeQueryDTOList.size() == 2);
    }

    /* Ended by AICoder, pid:395cfxf1ffn8048145510aa850eac47dc6f801fb */
    /* Started by AICoder, pid:j20fe14e65z2cb414a780b5ff2c2a95fde3874b8 */
    @Test
    public void apsDerivedCodeConsumer() throws Exception {
        ReflectUtil.setFieldValue(service, "getDerivedCodeUrl", "getDerivedCodeUrl");
        ReflectUtil.setFieldValue(service, "maxNum", "1000");
        PowerMockito.mockStatic(CommonUtils.class, HttpRemoteUtil.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("2");
        List<ApsDerivativeCodeQueryDTO> apsDerivativeCodeQueryDTOList = new ArrayList<>();
        service.apsDerivedCodeConsumer("");
        Assert.assertTrue(apsDerivativeCodeQueryDTOList.size() == 0);
        service.apsDerivedCodeConsumer(JSON.toJSONString(apsDerivativeCodeQueryDTOList));
        Assert.assertTrue(apsDerivativeCodeQueryDTOList.size() == 0);
        for (int i = 0; i < 1002; i++) {
            apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            }});
        }
        service.apsDerivedCodeConsumer(JSON.toJSONString(apsDerivativeCodeQueryDTOList));
        Assert.assertTrue(apsDerivativeCodeQueryDTOList.size() == 1002);
        apsDerivativeCodeQueryDTOList.clear();
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo1");
        }});
        service.apsDerivedCodeConsumer(JSON.toJSONString(apsDerivativeCodeQueryDTOList));
        Assert.assertTrue(apsDerivativeCodeQueryDTOList.size() == 1);

        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo3");
        }});
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo2");
        }});
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("7777666");
        psTask.setTaskNo("taskNo1");
        psTask.setTaskQty(BigDecimal.TEN);
        psTaskList.add(psTask);
        PsTask psTask1 = new PsTask();
        psTask1.setProdplanId("7777445");
        psTask1.setTaskQty(BigDecimal.ONE);
        psTask1.setTaskNo("taskNo2");
        psTask1.setFactoryId(BigDecimal.ONE);
        psTaskList.add(psTask1);
        PowerMockito.when(psTaskService.queryFactoryIdByTaskNoList(any())).thenReturn(psTaskList);
        service.apsDerivedCodeConsumer(JSON.toJSONString(apsDerivativeCodeQueryDTOList));
        Assert.assertTrue(apsDerivativeCodeQueryDTOList.size() == 3);

        List<PsTask> psTaskFromSiteList = new ArrayList<>();
        PsTask psTask2 = new PsTask();
        psTask2.setProdplanId("7777666");
        psTask2.setInforExe("3");
        psTask2.setTaskNo("taskNo1");
        psTask2.setTaskQty(BigDecimal.TEN);
        psTaskFromSiteList.add(psTask2);
        PowerMockito.when(openApiRemoteService.queryInfoTransferOrder(any())).thenReturn(psTaskFromSiteList);
        service.apsDerivedCodeConsumer(JSON.toJSONString(apsDerivativeCodeQueryDTOList));
        Assert.assertTrue(apsDerivativeCodeQueryDTOList.size() == 3);

        PsTask psTask3 = new PsTask();
        psTask3.setProdplanId("7777666");
        psTask3.setTaskNo("taskNo1");
        psTask3.setTaskQty(BigDecimal.TEN);
        psTask3.setInforExe("1");
        psTaskFromSiteList.add(psTask3);
        PsTask psTask4 = new PsTask();
        psTask4.setProdplanId("7777666");
        psTask4.setTaskNo("taskNo2");
        psTask4.setTaskQty(BigDecimal.TEN);
        psTask4.setInforExe("1");
        psTaskFromSiteList.add(psTask4);
        psTaskList.add(psTask4);
        PowerMockito.when(openApiRemoteService.queryInfoTransferOrder(any())).thenReturn(psTaskFromSiteList);
        PowerMockito.when(apsRemoteService.getApsDerivativeCodeDTOS(any())).thenReturn(new ArrayList<>());
        service.apsDerivedCodeConsumer(JSON.toJSONString(apsDerivativeCodeQueryDTOList));
        Assert.assertTrue(apsDerivativeCodeQueryDTOList.size() == 3);

        List<ApsDerivativeCodeDTO> apsDerivativeCodeDTOList = new ArrayList<>();
        apsDerivativeCodeDTOList.add(new ApsDerivativeCodeDTO() {{
            setProdplanNo("taskNo2");
            setDeriveItemNo("itemNo2");
            setItemNo("itemNo1");
        }});
        PowerMockito.when(apsRemoteService.getApsDerivativeCodeDTOS(any())).thenReturn(apsDerivativeCodeDTOList);
        service.apsDerivedCodeConsumer(JSON.toJSONString(apsDerivativeCodeQueryDTOList));
        Assert.assertTrue(apsDerivativeCodeQueryDTOList.size() == 3);

        psTask2.setInforExe("32");
        psTask3.setInforExe("10");
        psTask4.setInforExe("3");
        psTaskFromSiteList.add(psTask4);

        PowerMockito.mockStatic(HttpRemoteUtil.class, MESHttpHelper.class);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        PowerMockito.when(apsRemoteService.getApsDerivativeCodeDTOS(any())).thenReturn(apsDerivativeCodeDTOList);
        service.dealApsDerivedCode(apsDerivativeCodeQueryDTOList,true);
        Assert.assertTrue(apsDerivativeCodeQueryDTOList.size() == 3);


    }

    @Test
    public void dealApsDerivedCode() {
        PowerMockito.mockStatic(CommonUtils.class, HttpRemoteUtil.class, MESHttpHelper.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("erroramsg");
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        List<ApsDerivativeCodeQueryDTO> apsDerivativeCodeQueryDTOList = new ArrayList<>();
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo3");
            setItemNo("itemNo3");
        }});
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo2");
            setItemNo("itemNo2");
        }});

        List<PsTask> psTaskFromSiteList = new ArrayList<>();
        PsTask psTask2 = new PsTask();
        psTask2.setProdplanId("7777666");
        psTask2.setInforExe("3");
        psTask2.setTaskNo("taskNo1");
        psTask2.setTaskQty(BigDecimal.TEN);
        psTaskFromSiteList.add(psTask2);

        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("7777666");
        psTask.setTaskNo("taskNo1");
        psTask.setTaskQty(BigDecimal.TEN);
        psTask.setItemNo("itemNo2");
        psTaskList.add(psTask);
        PsTask psTask1 = new PsTask();
        psTask1.setProdplanId("7777445");
        psTask1.setTaskQty(BigDecimal.ONE);
        psTask1.setTaskNo("taskNo2");
        psTask1.setItemNo("itemNo3");
        psTask1.setFactoryId(BigDecimal.ONE);
        psTaskList.add(psTask1);
        PowerMockito.when(psTaskService.queryFactoryIdByTaskNoList(any())).thenReturn(psTaskList);

        List<ApsDerivativeCodeDTO> apsDerivativeCodeDTOList = new ArrayList<>();
        apsDerivativeCodeDTOList.add(new ApsDerivativeCodeDTO() {{
            setProdplanNo("taskNo2");
            setDeriveItemNo("itemNo2");
            setItemNo("itemNo1");
        }});
        PowerMockito.when(apsRemoteService.getApsDerivativeCodeDTOS(any())).thenReturn(new ArrayList<>());
        ReflectUtil.setFieldValue(service, "getDerivedCodeUrl", "getDerivedCodeUrl");
        service.dealApsDerivedCode(apsDerivativeCodeQueryDTOList,true);
        Assert.assertTrue(apsDerivativeCodeQueryDTOList.size() == 2);
        apsDerivativeCodeQueryDTOList.clear();
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo3");
            setItemNo("itemNo3");
        }});
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo2");
            setItemNo("itemNo2");
        }});
        PowerMockito.when(apsRemoteService.getApsDerivativeCodeDTOS(any())).thenReturn(apsDerivativeCodeDTOList);
        service.dealApsDerivedCode(apsDerivativeCodeQueryDTOList,true);
        Assert.assertTrue(apsDerivativeCodeQueryDTOList.size() == 2);
        List<BBomHeader> bBomHeaderList = new ArrayList<>();
        bBomHeaderList.add(new BBomHeader() {{
            setProductCode("2");
            setBomHeaderId("headerId1");
        }});
        PowerMockito.when(bBomHeaderervice.selectBBomHeaderByProductCodeList(any())).thenReturn(bBomHeaderList);

        apsDerivativeCodeQueryDTOList.clear();
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo3");
            setItemNo("itemNo3");
        }});
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo2");
            setItemNo("itemNo2");
        }});
        bBomHeaderList.add(new BBomHeader() {{
            setProductCode("itemNo3");
            setBomHeaderId("headerId1");
        }});
        PowerMockito.when(bBomHeaderervice.selectBBomHeaderByProductCodeList(any())).thenReturn(bBomHeaderList);
        service.dealApsDerivedCode(apsDerivativeCodeQueryDTOList,true);
        Assert.assertTrue(apsDerivativeCodeQueryDTOList.size() == 2);

        List<BBomDetailDTO> bBomDetailDTOList = new ArrayList<>();
        bBomDetailDTOList.add(new BBomDetailDTO() {{
            setItemCode("itemNo3");
            setBomHeaderId("headerId1");
        }});
        PowerMockito.when(bBomDetailService.selectDetailByHeaderId(any())).thenReturn(bBomDetailDTOList);

        apsDerivativeCodeQueryDTOList.clear();
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo3");
            setItemNo("itemNo3");
        }});
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo2");
            setItemNo("itemNo2");
        }});
        service.dealApsDerivedCode(apsDerivativeCodeQueryDTOList,true);
        Assert.assertTrue(apsDerivativeCodeQueryDTOList.size() == 2);

        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = new ArrayList<>();
        bProdBomHeaderDTOS.add(new BProdBomHeaderDTO() {{
            setProdplanId("2");
            setBomHeaderId("2");
            setProductCode("AB_2");
            setOriginalProductCode("AB");
        }});
        PowerMockito.when(bProdBomHeaderRepository.queryByProductCodeList(any())).thenReturn(bProdBomHeaderDTOS);
        apsDerivativeCodeQueryDTOList.clear();
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo3");
            setItemNo("itemNo3");
        }});
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo2");
            setItemNo("itemNo2");
        }});
        service.dealApsDerivedCode(apsDerivativeCodeQueryDTOList,true);
        Assert.assertTrue(apsDerivativeCodeQueryDTOList.size() == 2);

    }

    @Test
    public void generateManufacturingBOM() throws Exception {
        List<BBomDetailDTO> bBomDetailDTOList = new ArrayList<>();
        bBomDetailDTOList.add(new BBomDetailDTO() {{
            setItemCode("itemNo3");
            setBomHeaderId("headerId1");
            setUsageCount(BigDecimal.ZERO);
        }});
        bBomDetailDTOList.add(new BBomDetailDTO() {{
            setItemCode("itemNo2");
            setBomHeaderId("headerId1");
            setUsageCount(BigDecimal.ZERO);
        }});

        List<ApsDerivativeCodeDTO> apsDerivativeCodeDTOList = new ArrayList<>();
        apsDerivativeCodeDTOList.add(new ApsDerivativeCodeDTO() {{
            setProdplanNo("taskNo2");
            setDeriveItemNo("itemNo2");
            setItemNo("itemNo1");
        }});
        apsDerivativeCodeDTOList.add(new ApsDerivativeCodeDTO() {{
            setProdplanNo("taskNo2");
            setDeriveItemNo("itemNo2");
            setItemNo("itemNo3");
        }});

        List<ApsDerivativeCodeQueryDTO> surplusCorrectList = new ArrayList<>();
        surplusCorrectList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo3");
            setItemNo("itemNo3");
        }});
        surplusCorrectList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo2");
            setItemNo("itemNo2");
            setProdplanId("2");
            setBBomHeader(new BBomHeader());
        }});
        surplusCorrectList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo2");
            setItemNo("itemNo2");
            setProdplanId("3");
            setBBomHeader(new BBomHeader());
        }});
        surplusCorrectList.add(new ApsDerivativeCodeQueryDTO() {{
            setApsDerivativeCodeDTOList(apsDerivativeCodeDTOList);
            setProdplanNo("taskNo2");
            setItemNo("itemNo2");
            setProdplanId("4");
            setBBomHeader(new BBomHeader());
            setBBomDetailList(bBomDetailDTOList);
        }});
        Map<String, BProdBomHeaderDTO> bProdBomHeaderDTOHashMap = new HashMap<>();
        Whitebox.invokeMethod(service, "generateManufacturingBOM", surplusCorrectList, bProdBomHeaderDTOHashMap);
        Assert.assertEquals(surplusCorrectList.size(), 4);
        bProdBomHeaderDTOHashMap.put("2", new BProdBomHeaderDTO() {{
            setBomHeaderId("2");
        }});
        bProdBomHeaderDTOHashMap.put("3", new BProdBomHeaderDTO() {{
            setBomHeaderId("2");
            setProductCode("3");
        }});
        bProdBomHeaderDTOHashMap.put("4", new BProdBomHeaderDTO() {{
            setBomHeaderId("2");
            setProductCode("4_1");
        }});
        Whitebox.invokeMethod(service, "generateManufacturingBOM", surplusCorrectList, bProdBomHeaderDTOHashMap);
        Assert.assertTrue(surplusCorrectList.size() == 4);

        surplusCorrectList.add(new ApsDerivativeCodeQueryDTO() {{
            setApsDerivativeCodeDTOList(apsDerivativeCodeDTOList);
            setProdplanNo("taskNo2");
            setItemNo("itemNo2");
            setProdplanId("4");
            setBBomHeader(new BBomHeader());
            setBBomDetailList(bBomDetailDTOList);
        }});

    }
    /* Ended by AICoder, pid:j20fe14e65z2cb414a780b5ff2c2a95fde3874b8 */
    /* Started by AICoder, pid:w87cax276f5cfe214cdc0afd409ebd519ef4b8d0 */
    @Test
    public void filterIfItHasBeenProcessed() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        List<ApsDerivativeCodeQueryDTO> apsDerivativeCodeQueryDTOList = new ArrayList<>();
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanId("taskNo3");
            setChangeVersion("ver1");
        }});
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO() {{
            setProdplanId("taskNo2");
            setChangeVersion("ver1");
        }});
        BProdBomChangeDetailDTO detail = new BProdBomChangeDetailDTO() {{
            setProdplanId("taskNo2");
            setChangeId("ver2");
        }};
        BProdBomChangeDetailDTO detail1 = new BProdBomChangeDetailDTO() {{
            setProdplanId("taskNo3");
            setChangeId("ver1");
        }};
        List<BProdBomChangeDetailDTO> singleItemList = Arrays.asList(detail, detail1);
        PowerMockito.when(bProdBomChangeDetailService.queryByProdplanIdAndChangeVersion(any())).thenReturn(singleItemList);
        Whitebox.invokeMethod(service, "filterIfItHasBeenProcessed", apsDerivativeCodeQueryDTOList);
        Assert.assertTrue(apsDerivativeCodeQueryDTOList.size() == 2);

    }
    /* Ended by AICoder, pid:w87cax276f5cfe214cdc0afd409ebd519ef4b8d0 */
    @Test
    public void convertAndIntegrateMBomTest() {
        String prodplanId = "test";
        List<String> originalItemNoList = new ArrayList<>();
        originalItemNoList.add("code1");
        originalItemNoList.add("code3");
        originalItemNoList.add("code2");
        List<BProdBomHeaderDTO> tempList = new ArrayList<>();
        BProdBomHeaderDTO dto1 = new BProdBomHeaderDTO();
        dto1.setProductCode("code11");
        dto1.setOriginalProductCode("code1");

        BProdBomHeaderDTO dto2 = new BProdBomHeaderDTO();
        dto2.setProductCode("code22");
        dto2.setOriginalProductCode("code2");
        tempList.add(dto1);
        tempList.add(dto2);
        PowerMockito.when(bProdBomHeaderRepository.getBProdBomList(Mockito.anyString(), Mockito.anyList())).thenReturn(tempList);
        List<String> strings = service.convertAndIntegrateMBom(prodplanId, originalItemNoList);
        Assert.assertTrue(strings.size() == 3);
    }

    /* Started by AICoder, pid:aed1a56b62367f61409a0866a04c8d1edef68759 */
    @Test
    public void testGetMbomList() {
        BProdBomHeaderDTO params = new BProdBomHeaderDTO();
        params.setProductCode("121270351048APB_1");
        List<BProdBomHeaderDTO> bProdBomChangeDetailDTOs = new ArrayList<>();
        bProdBomChangeDetailDTOs.add(params);
        PowerMockito.when(bProdBomHeaderRepository.getMbomList(any())).thenReturn(bProdBomChangeDetailDTOs);
        Page<BProdBomHeaderDTO> bProdBomChangeDetailDTOPage = service.getMbomHeader(params);
        Assert.assertEquals(bProdBomChangeDetailDTOPage.getRows().get(0).getProductCode(), "121270351048APB_1");
    }
    /* Ended by AICoder, pid:aed1a56b62367f61409a0866a04c8d1edef68759 */

    /* Started by AICoder, pid:26babfe150i5bfa14b8608bb10f31e0b6429fcaf */
    @Test
    public void queryMBomHeadIdByProdplanIdAndProductCodeTest() {
        String prodplanId = "test";
        String productCode = "test";
        PowerMockito.when(bProdBomHeaderRepository.queryMBomHeadIdByProdplanIdAndProductCode(Mockito.anyString(), Mockito.anyString())).thenReturn("test");

        String s = service.queryMBomHeadIdByProdplanIdAndProductCode(prodplanId, productCode);
        Assert.assertTrue("test".equals(s));
    }
    /* Ended by AICoder, pid:26babfe150i5bfa14b8608bb10f31e0b6429fcaf */



    /*Started by AICoder, pid:rdf77t46a6oa67414032092cd1a9290a04e005eb*/
    @Test
    public void testQueryProductCodeByProdPlanIdList_EmptyList() {
        List<String> prodPlanIdList = Collections.emptyList();

        List<BProdBomHeaderDTO> result = service.queryProductCodeByProdPlanIdList(prodPlanIdList);

        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryProductCodeByProdPlanIdList_SingleBatch() {
        List<String> prodPlanIdList = Arrays.asList("1", "2", "3");

        List<BProdBomHeaderDTO> mockResult = Lists.newArrayList(new BProdBomHeaderDTO(), new BProdBomHeaderDTO());
        when(bProdBomHeaderRepository.queryBProdBomListBatch(anyList(), isNull()))
                .thenReturn(mockResult);

        List<BProdBomHeaderDTO> result = service.queryProductCodeByProdPlanIdList(prodPlanIdList);

        assertEquals(mockResult, result);
        verify(bProdBomHeaderRepository, times(1)).queryBProdBomListBatch(anyList(), isNull());
    }

    @Test
    public void testQueryProductCodeByProdPlanIdList_MultipleBatches() {
        List<String> prodPlanIdList = Lists.newArrayListWithExpectedSize(200);
        for (int i = 0; i < 200; i++) {
            prodPlanIdList.add(String.valueOf(i));
        }

        List<BProdBomHeaderDTO> mockResult = Lists.newArrayList(new BProdBomHeaderDTO(), new BProdBomHeaderDTO());
        when(bProdBomHeaderRepository.queryBProdBomListBatch(anyList(), isNull()))
                .thenReturn(mockResult);

        List<BProdBomHeaderDTO> result = service.queryProductCodeByProdPlanIdList(prodPlanIdList);

        assertEquals(2 * 2, result.size());
        verify(bProdBomHeaderRepository, times(2)).queryBProdBomListBatch(anyList(), isNull());
    }

    @Test
    public void testQueryProductCodeByProdPlanIdList_NoResults() {
        List<String> prodPlanIdList = Arrays.asList("1", "2", "3");

        when(bProdBomHeaderRepository.queryBProdBomListBatch(anyList(), isNull()))
                .thenReturn(Collections.emptyList());

        List<BProdBomHeaderDTO> result = service.queryProductCodeByProdPlanIdList(prodPlanIdList);

        assertEquals(Collections.emptyList(), result);
        verify(bProdBomHeaderRepository, times(1)).queryBProdBomListBatch(anyList(), isNull());
    }
    /*Ended by AICoder, pid:rdf77t46a6oa67414032092cd1a9290a04e005eb*/
}
