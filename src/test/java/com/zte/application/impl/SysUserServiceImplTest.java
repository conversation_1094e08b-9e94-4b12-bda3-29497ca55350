package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.TokenUtils;
import com.zte.infrastructure.remote.UcsRemoteService;
import com.zte.interfaces.dto.TokenRenewalDTO;
import com.zte.interfaces.dto.TokenVerifyDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.encrypt.secure.SecureEncryptorUtils;
import com.zte.itp.msa.util.security.util.SecuredEncUtil;
import com.zte.ums.zenap.util.cipher.keycenter.agent.utils.CipherHelperInterface;
import com.zte.ums.zenap.util.cipher.keycenter.agent.utils.GCMHelper;
import com.zte.util.BaseTestCase;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Matchers.any;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/12/31 17:48
 * @Description
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class,GCMHelper.class, HttpClientUtil.class, TokenUtils.class,SecureEncryptorUtils.class, SecuredEncUtil.class})
public class SysUserServiceImplTest extends BaseTestCase {

    @InjectMocks
    private SysUserServiceImpl service;
    @Mock
    UcsRemoteService ucsRemoteService;


    @Before
    public void before() throws Exception {
        FieldUtils.writeField(service, "ucsSecretKey", "123", true);
    }
    @Test
    public void init() throws Exception {
        PowerMockito.mockStatic(SecureEncryptorUtils.class);
        assertNotNull(service);
    }
    @Test
    public void testUcsVerify() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(new HashMap<>());}}));
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(new HashMap<>());}}));
        try{
            service.ucsVerify(new TokenVerifyDTO(), "");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.USER_IS_NOT_EXISTS , e.getMessage());
        }
    }

    @Test
    public void testUcsGetDetailByAccountId() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpGet(any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData()));
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(new HashMap(){{put("refreshUcsToken","");}});}}));
        Assert.assertNotNull(service.ucsGetDetailByAccountId("********", ""));
    }

    @Test
    public void testUcsRenewal() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpGet(any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData()));
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(new HashMap(){{put("refreshUcsToken","");}});}}));
        Assert.assertNotNull(service.ucsRenewal(new TokenRenewalDTO(){{setAccountId("112");}}, ""));
    }

    @Test
    public void testUcsCheck() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(new HashMap<>());}}));
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        RetCode code = new RetCode("0005", "RetCode.Fail");

        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(new HashMap<>()); setCode(code);}}));
        try {
            service.ucsCheck("zjx", "2312");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PSW_IS_WRONG, e.getMessage());
        }
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(new HashMap<>());}}));
        service.ucsCheck("zjx", "2312");

        PowerMockito.when(ucsRemoteService.getPublicKey()).thenReturn("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnVwKdMLLvugC8Jh5ANocw0sST6oiUXa65M+seqYHDJax/w7D2bGYwKo9Rw8KU5BkwWZUKz2ko2yv+biJVTaJDvurudP0vCrLmgtTZQqkpKSr/lt0dePM+sCAvq7EJwFE31FV+HDG704KPDm898rZhOnzd+2xhwTrCRDXWCAkuFNbsYnTjcvmiBpHQNUCdvanajUX+lEhcLZuZ1f8wPlRu+rwKwmLX62ulfiuDounksIS0z0O0Hy0XGKr2tCoaX2rZh6Sc3slJlPqhofCsMRx9t81in1ZQo7NHuokispR1OUfio8VAqMdYa2oDelZZZ22y7uN2F+OWxVpSQfHaIhs3wIDAQAB");

        PowerMockito.mockStatic(SecuredEncUtil.class,GCMHelper.class);
        ReflectionTestUtils.setField(service,"ucsSwitch","Y",String.class);
        PowerMockito.when(GCMHelper.generateAESKey(anyInt())).thenReturn("6k4tTfCCKYmfURXhUZhm38z+6yDsMrPnsrDzMWdC0IA=");
        PowerMockito.when(ucsRemoteService.encryptTemporaryKey(any(),anyString())).thenReturn("2");
        PowerMockito.when(ucsRemoteService.encryptParams(any(),any())).thenReturn("cipherHelper");
        PowerMockito.when(SecuredEncUtil.invokeHttp(anyString(),anyString(),anyString(),anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{setBo("2");}}));
        service.ucsCheck("zjx", "2312");
        Assert.assertNotNull(code);

    }
    @Test
    public void testUcsLogin() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class, TokenUtils.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(new HashMap<>());}}));
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        RetCode code = new RetCode("0005", "RetCode.Fail");

        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(new HashMap<>()); setCode(code);}}));
        service.ucsLogin("zjx", "2312");
        Map<Object, Object> boMap = new HashMap<>();
        boMap.put(BusinessConstant.ACCOUNT_ID, 123);
        boMap.put(BusinessConstant.USER_ID, 123);
        RetCode sucCode = new RetCode("0000", "RetCode.Success");
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(boMap);setCode(sucCode);}}));
        Assert.assertNotNull(service.ucsLogin("zjx", "2312"));

        PowerMockito.when(ucsRemoteService.getPublicKey()).thenReturn("MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAnVwKdMLLvugC8Jh5ANocw0sST6oiUXa65M+seqYHDJax/w7D2bGYwKo9Rw8KU5BkwWZUKz2ko2yv+biJVTaJDvurudP0vCrLmgtTZQqkpKSr/lt0dePM+sCAvq7EJwFE31FV+HDG704KPDm898rZhOnzd+2xhwTrCRDXWCAkuFNbsYnTjcvmiBpHQNUCdvanajUX+lEhcLZuZ1f8wPlRu+rwKwmLX62ulfiuDounksIS0z0O0Hy0XGKr2tCoaX2rZh6Sc3slJlPqhofCsMRx9t81in1ZQo7NHuokispR1OUfio8VAqMdYa2oDelZZZ22y7uN2F+OWxVpSQfHaIhs3wIDAQAB");

        PowerMockito.mockStatic(SecuredEncUtil.class,GCMHelper.class);
        ReflectionTestUtils.setField(service,"ucsSwitch","Y",String.class);
        PowerMockito.when(GCMHelper.generateAESKey(anyInt())).thenReturn("6k4tTfCCKYmfURXhUZhm38z+6yDsMrPnsrDzMWdC0IA=");
        PowerMockito.when(ucsRemoteService.encryptTemporaryKey(any(),anyString())).thenReturn("2");
        PowerMockito.when(ucsRemoteService.encryptParams(any(),any())).thenReturn("cipherHelper");

        PowerMockito.when(SecuredEncUtil.invokeHttp(anyString(),anyString(),anyString(),anyMap())).thenReturn(JSON.toJSONString(new ServiceData<>()));
        service.ucsLogin("zjx", "2312");
        Assert.assertNotNull(boMap);

    }
}

