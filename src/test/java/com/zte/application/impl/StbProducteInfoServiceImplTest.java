package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.zte.domain.model.StbProducteInfo;
import com.zte.domain.model.StbProducteInfoRepository;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName: StbProducteInfoServiceImplTest
 * Description:
 *
 * <AUTHOR>
 * @date 2023/7/26 上午10:20
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class})
public class StbProducteInfoServiceImplTest extends BaseTestCase {
    @InjectMocks
    private StbProducteInfoServiceImpl service;

    @Mock
    private StbProducteInfoRepository stbProducteInfoRepository;
    @Test
    public void exportExcelTest() throws Exception {
        Map<String, Object> record = new HashMap<>();
        String empNo = "sadasdasd";
        String email = "<EMAIL>";
        List<StbProducteInfo> list = new ArrayList<>();
        list.add(new StbProducteInfo());
        PowerMockito.when(stbProducteInfoRepository.getStbProducteInfo(Mockito.anyMap())).thenReturn(list);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(),Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap())).thenReturn(
                        JSON.toJSONString(new ServiceData() {{
            setBo("1051654456");
        }}));
        Assert.assertNotNull(service.exportExcel(record, empNo, email));
    }
}
