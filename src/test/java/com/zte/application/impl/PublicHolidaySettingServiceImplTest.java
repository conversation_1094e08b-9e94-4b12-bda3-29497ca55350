package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.domain.model.PublicHolidaySettingRepository;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.PublicHolidaySettingDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.util.BaseTestCase;
import junit.framework.TestCase;
import org.apache.commons.math3.analysis.function.Pow;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static org.mockito.ArgumentMatchers.any;

@RunWith(PowerMockRunner.class)
public class PublicHolidaySettingServiceImplTest extends BaseTestCase {

    @InjectMocks
    private PublicHolidaySettingServiceImpl publicHolidaySettingService;

    @Mock
    private PublicHolidaySettingRepository publicHolidaySettingRepository;
    @Mock
    private SysLookupTypesRepository sysLookupTypesRepository;
    @Mock
    private HrmUserCenterServiceImpl hrmUserCenterService;

    @Test
    public void testPageList() throws Exception {
        PublicHolidaySettingDTO publicHolidaySettingDTO = new PublicHolidaySettingDTO();
        try {
            publicHolidaySettingService.pageList(publicHolidaySettingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.HOLIDAY_PARAMS_ERROR, e.getMessage());
        }
        publicHolidaySettingDTO.setYear(2023);
        publicHolidaySettingDTO.setHolidayType("Spring-Festival");
        publicHolidaySettingDTO.setStartDate(new Date());
        publicHolidaySettingDTO.setEndDate(new Date());
        publicHolidaySettingService.pageList(publicHolidaySettingDTO);

        List<SysLookupTypesDTO> valuesList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("Spring-Festival");
        sysLookupTypesDTO.setDescriptionChinV("春节");
        valuesList.add(sysLookupTypesDTO);
        List<PublicHolidaySettingDTO> queryList = new ArrayList<>();
        PublicHolidaySettingDTO publicHolidaySettingDTO1 = new PublicHolidaySettingDTO();
        PublicHolidaySettingDTO publicHolidaySettingDTO2 = new PublicHolidaySettingDTO();
        queryList.add(publicHolidaySettingDTO2);
        publicHolidaySettingDTO1.setHolidayType("guo-jie-le");
        publicHolidaySettingDTO1.setStartDate(new Date());
        publicHolidaySettingDTO1.setEndDate(new Date());queryList.add(publicHolidaySettingDTO);
        queryList.add(publicHolidaySettingDTO1);
        PowerMockito.when(publicHolidaySettingRepository.pageList(any())).thenReturn(queryList);
        PowerMockito.when(sysLookupTypesRepository.getList(any())).thenReturn(valuesList);
        publicHolidaySettingService.pageList(publicHolidaySettingDTO);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("张三");
        publicHolidaySettingDTO.setLastUpdatedBy("123");
        publicHolidaySettingDTO.setCreateBy("123");
        hrmPersonInfoDTOMap.put("123", hrmPersonInfoDTO);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);
        publicHolidaySettingService.pageList(publicHolidaySettingDTO);
    }

    @Test
    public void testAddHoliday() {
        Calendar calendar = new GregorianCalendar();
        PublicHolidaySettingDTO publicHolidaySettingDTO = new PublicHolidaySettingDTO();
        publicHolidaySettingDTO.setStartDate(new Date());
        publicHolidaySettingDTO.setEndDate(new Date());
        try {
            publicHolidaySettingService.addHoliday(publicHolidaySettingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.HOLIDAY_YEAR_ERROR, e.getMessage());
        }
        publicHolidaySettingDTO.setYear(calendar.get(Calendar.YEAR));
        publicHolidaySettingService.addHoliday(publicHolidaySettingDTO);
        List<PublicHolidaySettingDTO> checkList = new ArrayList<>();
        checkList.add(publicHolidaySettingDTO);
        PowerMockito.when(publicHolidaySettingRepository.getRepeatDateRange(any())).thenReturn(checkList);
        try {
            publicHolidaySettingService.addHoliday(publicHolidaySettingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DATE_RANGE_REPEAT, e.getMessage());
        }
        PowerMockito.when(publicHolidaySettingRepository.getRepeatHoliday(any())).thenReturn(checkList);
        try {
            publicHolidaySettingService.addHoliday(publicHolidaySettingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.HOLIDAY_REPEAT, e.getMessage());
        }
    }
    @Test
    public void testEditHoliday() {
        try {
            publicHolidaySettingService.editHoliday(new PublicHolidaySettingDTO());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.HOLIDAY_PARAMS_DATE_NULL, e.getMessage());
        }
        PublicHolidaySettingDTO publicHolidaySettingDTO = new PublicHolidaySettingDTO();
        publicHolidaySettingDTO.setStartDate(new Date());
        publicHolidaySettingDTO.setEndDate(new Date());
        Calendar calendar = new GregorianCalendar();
        publicHolidaySettingDTO.setYear(calendar.get(Calendar.YEAR));
        publicHolidaySettingService.editHoliday(publicHolidaySettingDTO);
    }

    @Test
    public void testDeleteHoliday() {
        Assert.assertNotNull(publicHolidaySettingService.deleteHoliday(new PublicHolidaySettingDTO()));
    }

    @Test
    public void testQueryHolidayForCalendar() {
        PublicHolidaySettingDTO publicHolidaySettingDTO = new PublicHolidaySettingDTO();
        try {
            publicHolidaySettingService.queryHolidayForCalendar(publicHolidaySettingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.HOLIDAY_PARAMS_ERROR, e.getMessage());
        }
        publicHolidaySettingDTO.setCalendarDate("2023-07");
        List<PublicHolidaySettingDTO> holidayList = new ArrayList<>();
        publicHolidaySettingDTO.setYear(2023);
        publicHolidaySettingDTO.setHolidayType("Spring-Festival");
        Calendar calendar = Calendar.getInstance();
        calendar.set(2023, Calendar.JULY,11);
        publicHolidaySettingDTO.setStartDate(calendar.getTime());
        publicHolidaySettingDTO.setEndDate(new Date());
        PublicHolidaySettingDTO publicHolidaySettingDTO1 = new PublicHolidaySettingDTO();
        holidayList.add(publicHolidaySettingDTO1);
        holidayList.add(publicHolidaySettingDTO);
        PowerMockito.when(publicHolidaySettingRepository.queryHolidayForCalendar(any())).thenReturn(holidayList);
        publicHolidaySettingService.queryHolidayForCalendar(publicHolidaySettingDTO);

    }
}