package com.zte.application.impl;

import com.zte.application.SysLookupValuesService;
import com.zte.application.sncabind.PsTaskService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.TechnicalSummaryInfoRepository;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.TechnicalChangeExecInfoEntityDTO;
import com.zte.interfaces.dto.TechnicalSummaryInfoDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;

/**
 * <AUTHOR>
 * @date 2023/02/11
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisHelper.class})
public class TechnicalSummaryInfoServiceImplTest {

    @InjectMocks
    private TechnicalSummaryInfoServiceImpl technicalSummaryInfoService;

    @Mock
    private TechnicalSummaryInfoRepository technicalSummaryInfoRepository;

    @Mock
    private DatawbRemoteService datawbRemoteService;

    @Mock
    private PsTaskService psTaskService;

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Before
    public void init() {
        PowerMockito.mockStatic(RedisHelper.class);
    }

    @Test
    public void  syncTechnicalExecInfo() throws Exception {
        List<TechnicalChangeExecInfoEntityDTO> list = new ArrayList<>();
        try{
            technicalSummaryInfoService.syncTechnicalExecInfo(list);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARAM_SIZE_BETWEEN_1_AND_1000,e.getExMsgId());
        }
        TechnicalChangeExecInfoEntityDTO dto = new TechnicalChangeExecInfoEntityDTO();
        dto.setId("id1");
        dto.setChgReqNo("123");
        dto.setSn("777766600001");
        list.add(dto);
        dto = new TechnicalChangeExecInfoEntityDTO();
        dto.setId("id2");
        dto.setChgReqNo("123");
        dto.setSn("777766600002");
        list.add(dto);


        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        try{
            technicalSummaryInfoService.syncTechnicalExecInfo(list);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.FAILED_TO_GET_REDIS_LOCK,e.getExMsgId());
        }
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);

        List<SysLookupValues> lookupValueList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setAttribute1("55");
        sysLookupValues.setLookupMeaning("855");
        lookupValueList.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.eq(Constant.LOOKUP_TYPE_2222))).thenReturn(lookupValueList);

        List<TechnicalChangeExecInfoEntityDTO> existList = new ArrayList<>();
        dto = new TechnicalChangeExecInfoEntityDTO();
        dto.setId("id2");
        dto.setChgReqNo("123");
        dto.setSn("777766600002");
        existList.add(dto);
        PowerMockito.when(technicalSummaryInfoRepository.getTechChgByIds(Mockito.anyList())).thenReturn(new LinkedList<>());

        technicalSummaryInfoService.syncTechnicalExecInfo(list);

    }

    @Test
    public void  syncTechnicalInfoToMes() throws Exception {
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        try{
            technicalSummaryInfoService.syncTechnicalInfoToMes();
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.FAILED_TO_GET_REDIS_LOCK,e.getExMsgId());
        }

        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(sysLookupValues);
        try{
            technicalSummaryInfoService.syncTechnicalInfoToMes();
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG,e.getExMsgId());
        }
        sysLookupValues.setLookupMeaning("2023-02-10 20:30:22");

        List<TechnicalSummaryInfoDTO> syncDataList = new ArrayList<>();
        PowerMockito.when(technicalSummaryInfoRepository.getSyncDataByLastUpdatedDate(Mockito.any(), Mockito.anyInt(), Mockito.any())).thenReturn(syncDataList);
        technicalSummaryInfoService.syncTechnicalInfoToMes();

        TechnicalSummaryInfoDTO technicalSummaryInfoDTO = new TechnicalSummaryInfoDTO();
        technicalSummaryInfoDTO.setFactoryId("55");
        technicalSummaryInfoDTO.setId("id2");
        technicalSummaryInfoDTO.setProdplanId("7777555");
        technicalSummaryInfoDTO.setTechnicalStatus("3");
        technicalSummaryInfoDTO.setId("id2");
        technicalSummaryInfoDTO.setLastUpdatedDate(new Date());
        syncDataList.add(technicalSummaryInfoDTO);
        technicalSummaryInfoDTO = new TechnicalSummaryInfoDTO();
        technicalSummaryInfoDTO.setFactoryId("55");
        technicalSummaryInfoDTO.setId("id2");
        technicalSummaryInfoDTO.setProdplanId("7777444");
        technicalSummaryInfoDTO.setTechnicalStatus("4");
        technicalSummaryInfoDTO.setId("id3");
        technicalSummaryInfoDTO.setLastUpdatedDate(new Date());
        syncDataList.add(technicalSummaryInfoDTO);

        technicalSummaryInfoDTO = new TechnicalSummaryInfoDTO();
        technicalSummaryInfoDTO.setFactoryId("55");
        technicalSummaryInfoDTO.setId("id1");
        technicalSummaryInfoDTO.setProdplanId("7777666");
        technicalSummaryInfoDTO.setTechnicalStatus("2");
        technicalSummaryInfoDTO.setId("id1");
        technicalSummaryInfoDTO.setLastUpdatedDate(new Date());
        syncDataList.add(technicalSummaryInfoDTO);

        technicalSummaryInfoDTO.setTaskQty(0);
        technicalSummaryInfoService.syncTechnicalInfoToMes();
        technicalSummaryInfoDTO.setTaskQty(1);
        technicalSummaryInfoService.syncTechnicalInfoToMes();

    }
    @Test
    public void  syncTechnicalInfo() throws Exception {
        List<TechnicalSummaryInfoDTO> list = new ArrayList<>();
        try{
            technicalSummaryInfoService.syncTechnicalInfo(list);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PARAM_SIZE_BETWEEN_1_AND_1000,e.getExMsgId());
        }
        TechnicalSummaryInfoDTO technicalSummaryInfoDTO = new TechnicalSummaryInfoDTO();
        technicalSummaryInfoDTO.setFactoryId("55");
        technicalSummaryInfoDTO.setId("id1");
        technicalSummaryInfoDTO.setProdplanId("7777666");
        list.add(technicalSummaryInfoDTO);
        technicalSummaryInfoDTO = new TechnicalSummaryInfoDTO();
        technicalSummaryInfoDTO.setFactoryId("55");
        technicalSummaryInfoDTO.setId("id2");
        technicalSummaryInfoDTO.setProdplanId("7777555");
        list.add(technicalSummaryInfoDTO);
        technicalSummaryInfoDTO = new TechnicalSummaryInfoDTO();
        technicalSummaryInfoDTO.setFactoryId("55");
        technicalSummaryInfoDTO.setId("id2");
        technicalSummaryInfoDTO.setProdplanId("7777444");
        list.add(technicalSummaryInfoDTO);

        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        try{
            technicalSummaryInfoService.syncTechnicalInfo(list);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.FAILED_TO_GET_REDIS_LOCK,e.getExMsgId());
        }

        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);

        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("7777666");
        psTask.setTaskQty(BigDecimal.TEN);
        psTaskList.add(psTask);
        psTask.setProdplanId("7777444");
        psTask.setTaskQty(BigDecimal.ONE);
        psTaskList.add(psTask);
        PowerMockito.when(psTaskService.getTaskQtyByProdplanId(Mockito.anyList())).thenReturn(psTaskList);

        List<SysLookupValues> lookupValueList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setAttribute1("55");
        sysLookupValues.setLookupMeaning("855");
        lookupValueList.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.eq(Constant.LOOKUP_TYPE_2222))).thenReturn(lookupValueList);

        List<TechnicalSummaryInfoDTO> existList = new ArrayList<>();
        technicalSummaryInfoDTO = new TechnicalSummaryInfoDTO();
        technicalSummaryInfoDTO.setFactoryId("55");
        technicalSummaryInfoDTO.setId("id1");
        technicalSummaryInfoDTO.setProdplanId("7777667");
        existList.add(technicalSummaryInfoDTO);
        PowerMockito.when(technicalSummaryInfoRepository.getTechChgByIds(Mockito.anyList())).thenReturn(existList);

        technicalSummaryInfoService.syncTechnicalInfo(list);

    }

    @Test
    public void  getBsPremanuBomInfoPage() throws Exception {
        TechnicalSummaryInfoDTO dto = new TechnicalSummaryInfoDTO();
        dto.setId("dasd1");
        TechnicalChangeExecInfoEntityDTO entityDTO = new TechnicalChangeExecInfoEntityDTO();
        entityDTO.setFactoryId("3333");
        List<TechnicalSummaryInfoDTO> dtoList = new ArrayList<>();
        dtoList.add(dto);
        technicalSummaryInfoService.updateById(dto);
        technicalSummaryInfoService.insertExecInfo(entityDTO);
        Assert.assertNotNull(entityDTO);
    }
}
