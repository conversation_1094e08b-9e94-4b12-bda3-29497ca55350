package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.application.SysLookupValuesService;
import com.zte.application.kafka.producer.BsProcessProducer;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BsProcessRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.BSProcess;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.springbootframe.common.model.FactoryConfig;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.anyMap;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class,BasicsettingRemoteService.class})
public class BsProcessServiceImplTest extends TestCase {

    @InjectMocks
    private BsProcessServiceImpl service;
    @Mock
    private BsProcessRepository repository;
    @Mock
    private FactoryConfig factoryConfig;
    @Mock
    private BsProcessProducer bsProcessProducer;
	@Mock
	private SysLookupValuesService sysLookupValuesService;
    @Test
    public void selectAllByProcessCode() throws Exception {
        List<BSProcess> list = Lists.newArrayList();
        BSProcess process = new BSProcess();
        process.setProcessCode("S0201");
        list.add(process);
        when(repository.selectAllByProcessCode()).thenReturn(list);
        String processCode = service.selectAllByProcessCode();
        Assert.assertNotNull(processCode);
        Assert.assertEquals(processCode, "2000");
    }

    @Test
    public void getList() throws Exception {
        Map<String, Object> map = new HashMap<>();
        String orderField = "";
        String order = "";
        service.getList(map, orderField, order);
        verify(repository, times(1)).getList(anyMap());
    }

    @Test
    public void getPage() throws Exception {

        Map<String, Object> map = new HashMap<>();
        String orderField = "";
        String order = "";
        Long page = 1L;
        Long rows = 10L;
        service.getPage(map, orderField, order, page, rows);
        verify(repository, times(1)).getPage(anyMap());
    }

    @Test
    public void lpad() throws Exception {
        String code = "2000";
        code = service.lpad(code, 4, "0");
        Assert.assertNotNull(code);
        Assert.assertEquals(code, "2000");
    }

    @Test
    public void insert() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessId("123");
        bsProcess.setFactoryId(new BigDecimal(51));
        PowerMockito.when(repository.get(any())).thenReturn(bsProcess);
        service.insert(bsProcess);
        BSProcess bsProcess1 = null;
        List<BSProcess> listEntity = new ArrayList<>();
        listEntity.add(bsProcess);
        PowerMockito.when(repository.get(any())).thenReturn(bsProcess1);
        PowerMockito.when(repository.getList(anyMap())).thenReturn(listEntity);
        service.insert(bsProcess);
        listEntity.remove(0);
        bsProcess.setEntityId(new BigDecimal(1));
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("123");
        PowerMockito.when(repository.insert(any())).thenReturn(1);
        Assert.assertNotNull(service.insert(bsProcess));
    }

	@Test
	public void testGetBsProcessInfoList() throws Exception {

		List<BSProcess> listProcess = new ArrayList<>();

		BSProcess bsProcessDTO = new BSProcess();
		bsProcessDTO.setProcessCode("test1");

		BSProcess bsProcessDTO1 = new BSProcess();
		bsProcessDTO1.setProcessCode("test2");

		BSProcess bsProcessDTO2 = new BSProcess();
		bsProcessDTO2.setProcessCode("test3");

		listProcess.add(bsProcessDTO);
		listProcess.add(bsProcessDTO1);
		listProcess.add(bsProcessDTO2);
		PowerMockito.when(repository.getList(anyMap())).thenReturn(listProcess);

		List<SysLookupValues> lookUpValuesBatch = new LinkedList<>();
		SysLookupValues c1 = new SysLookupValues();
		c1.setLookupMeaning("test");
		c1.setAttribute1("1");
		c1.setLookupType(new BigDecimal("6908"));
		lookUpValuesBatch.add(c1);
		SysLookupValues c2 = new SysLookupValues();
		c2.setLookupMeaning("test1");
		c2.setAttribute1("1");
		c2.setLookupType(new BigDecimal("6908"));
		lookUpValuesBatch.add(c2);
		SysLookupValues C3 = new SysLookupValues();
		C3.setLookupMeaning("test2");
		C3.setAttribute1("2");
		C3.setLookupType(new BigDecimal("6909"));
		lookUpValuesBatch.add(C3);
		SysLookupValues C4 = new SysLookupValues();
		C4.setLookupMeaning("test2");
		C4.setAttribute1("2");
		C4.setLookupType(new BigDecimal("6912"));
		lookUpValuesBatch.add(C4);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("xType", "子工序");
		try{
			PowerMockito.when(sysLookupValuesService.getLookUpValuesBatch(Mockito.any())).thenReturn(lookUpValuesBatch);
			service.getBsProcessInfoList(map,"lastUpdatedDate","desc");
		} catch (Exception e){
			Assert.assertNull(e.getMessage());
		}
		try{
			PowerMockito.when(sysLookupValuesService.getLookUpValuesBatch(Mockito.any())).thenReturn(lookUpValuesBatch);
			service.getBsProcessInfoList(map,"lastUpdatedDate","desc");
		} catch (Exception e){
			Assert.assertNull(e.getMessage());
		}
	}
}