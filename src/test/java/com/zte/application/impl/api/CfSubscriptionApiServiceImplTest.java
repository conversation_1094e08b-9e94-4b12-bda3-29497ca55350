/*Started by AICoder, pid:j0b151ecd1m665d14d0a080f6041b85c6b234564*/
package com.zte.application.impl.api;

import com.alibaba.excel.EasyExcelFactory;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.api.CfSubscriptionApiMapperRepository;
import com.zte.infrastructure.remote.HrmUserInfoService;
import com.zte.interfaces.dto.api.CfSubscriptionApiDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.BaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@PrepareForTest({RequestHeadValidationUtil.class, EasyExcelFactory.class})
public class CfSubscriptionApiServiceImplTest extends BaseTestCase {
    @InjectMocks private CfSubscriptionApiServiceImpl cfSubscriptionApiService;

    @Mock private IdGenerator idGenerator;

    @Mock private CfSubscriptionApiMapperRepository cfSubscriptionApiMapper;

    @Mock
    MultipartFile file;
    @Mock
    InputStream inputStream;
    @Mock
    private HrmUserInfoService hrmUserInfoService;

    @Before
    public void setup() throws Exception{
        PowerMockito.mockStatic(EasyExcelFactory.class);
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);
        PowerMockito.field(CfSubscriptionApiServiceImpl.class,"cfApiUserPermissionsList").set(cfSubscriptionApiService,
                Collections.singletonList("1234"));
        PowerMockito.when(RequestHeadValidationUtil.validaEmpno()).thenReturn("1234");
    }

    @Test
    public void testAdd_WithValidData() {
        CfSubscriptionApiDTO apiBO = new CfSubscriptionApiDTO();
        cfSubscriptionApiService.add(apiBO);
        Assert.assertTrue(Objects.nonNull(apiBO));
    }

    /*Started by AICoder, pid:h0427e8a9chb26d14deb0b34c051245f011685f8*/
    @Test
    public void testUpdateById() {
        CfSubscriptionApiDTO cfSubscriptionApiDTO = new CfSubscriptionApiDTO();
        cfSubscriptionApiDTO.setId("1");
        cfSubscriptionApiService.updateById(cfSubscriptionApiDTO);
        Assert.assertTrue(Objects.nonNull(cfSubscriptionApiDTO));

        cfSubscriptionApiDTO.setSystemCode(Constant.IMES);
        cfSubscriptionApiService.updateById(cfSubscriptionApiDTO);
    }
    /*Ended by AICoder, pid:h0427e8a9chb26d14deb0b34c051245f011685f8*/

    /*Started by AICoder, pid:cc762w387e84a8614901081a20aa585aa1f385f9*/
    @Test
    public void testBatchAdd_EmptyList() throws Exception{
        List<CfSubscriptionApiDTO> list = new ArrayList<>();
        CfSubscriptionApiDTO a1 = new CfSubscriptionApiDTO();
        list.add(a1);
        cfSubscriptionApiService.batchAdd(list);
        Assert.assertTrue(CollectionUtils.isNotEmpty(list));

        PowerMockito.field(CfSubscriptionApiServiceImpl.class,"checkApiResource")
                .set(cfSubscriptionApiService,true);
        CfSubscriptionApiDTO a2 = new CfSubscriptionApiDTO();
        a2.setSystemCode(Constant.IMES);
        list.add(a2);
        try {
            cfSubscriptionApiService.batchAdd(list);
        } catch (Exception e) {
            assertEquals(MessageId.CF_API_RESOURCE_EMPTY, e.getMessage());
        }
    }
    /*Ended by AICoder, pid:cc762w387e84a8614901081a20aa585aa1f385f9*/

    /*Started by AICoder, pid:wc40aj3e01r2ae914735080880bea06b18d4e28b*/
    @Test(timeout = 8000)
    public void batchDeleteById_WithNonEmptyIdList() {
        List<String> idList = Arrays.asList("1", "2", "3");
        when(RequestHeadValidationUtil.validaEmpno()).thenReturn("testEmpno");
        try {
            cfSubscriptionApiService.batchDeleteById(idList);
        } catch (Exception e) {
            assertEquals(MessageId.USER_NO_PERMISSIONS, e.getMessage());
        }
    }

    @Test(timeout = 8000)
    public void batchDeleteById_WithEmptyIdList() {
        List<String> idList = Collections.emptyList();
        cfSubscriptionApiService.batchDeleteById(idList);
        verify(cfSubscriptionApiMapper, never()).batchDeleteById(anyList(), anyString());
    }
    /*Ended by AICoder, pid:wc40aj3e01r2ae914735080880bea06b18d4e28b*/

    /*Started by AICoder, pid:y8d7el933b0197914d400be090315d5ddc46b921*/
    @Test(timeout = 8000)
    public void testQueryByPage_EmptyList() {
        Page<CfSubscriptionApiDTO> page = new Page<>();
        page.setRows(new ArrayList<>());
        when(cfSubscriptionApiMapper.queryByPage(page)).thenReturn(new ArrayList<>());

        Page<CfSubscriptionApiDTO> result = cfSubscriptionApiService.queryByPage(page);
        assertEquals(0, result.getRows().size());
    }
    /*Ended by AICoder, pid:y8d7el933b0197914d400be090315d5ddc46b921*/

    /*Started by AICoder, pid:ea389dc8bd5554a14bc90839200cbf5c9912e92e*/
    @Test(timeout = 8000)
    public void countExportTotal_NullInput_ReturnsZero() {
        Integer result = cfSubscriptionApiService.countExportTotal(null);
        assertEquals(Integer.valueOf(0), result);
    }
    /*Ended by AICoder, pid:ea389dc8bd5554a14bc90839200cbf5c9912e92e*/

    /*Started by AICoder, pid:876belcce9940d0140000b1b2057f25e4c453945*/
    @Test(timeout = 8000)
    public void testQueryExportData_EmptyList() {
        List<CfSubscriptionApiDTO> data = new ArrayList<>();
        when(cfSubscriptionApiMapper.queryByPage(Mockito.any())).thenReturn(data);
        List<CfSubscriptionApiDTO> result =
                cfSubscriptionApiService.queryExportData(new CfSubscriptionApiDTO(), 1, 10);
        assertTrue(result.isEmpty());
    }
    /*Ended by AICoder, pid:876belcce9940d0140000b1b2057f25e4c453945*/

    /*Started by AICoder, pid:7ef2aid18d27842146520b35802737575dd0c3be*/
    @Test
    public void testUpLoadExcelApiData_WithValidFile() throws IOException {
        try {
            List<CfSubscriptionApiDTO> result = cfSubscriptionApiService.upLoadExcelApiData(file);
            assertNotNull(result);
            assertTrue(result.isEmpty() || result.get(0).getErrorMsg() == null);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    @Test
    public void testFilterApi() throws Exception{
        List<CfSubscriptionApiDTO> resultList = new LinkedList<>();
        CfSubscriptionApiDTO a1 = new CfSubscriptionApiDTO();
        resultList.add(a1);
        Whitebox.invokeMethod(cfSubscriptionApiService, "filterApiData",resultList);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));

        resultList.clear();
        Whitebox.invokeMethod(cfSubscriptionApiService, "filterApiData",resultList);
    }

    /*Ended by AICoder, pid:7ef2aid18d27842146520b35802737575dd0c3be*/

    /* Started by AICoder, pid:v597ew57b23823c14be50a2d10dfb43428d46af9 */
    @Test
    public void selectMaxDateByCondition() {
        Date date = cfSubscriptionApiService.selectMaxDateByCondition(new CfSubscriptionApiDTO());
        Assert.assertNull(date);
    }
    /* Ended by AICoder, pid:v597ew57b23823c14be50a2d10dfb43428d46af9 */
}
/*Ended by AICoder, pid:j0b151ecd1m665d14d0a080f6041b85c6b234564*/