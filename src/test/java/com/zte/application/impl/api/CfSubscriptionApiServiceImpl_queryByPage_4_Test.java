/*Started by AICoder, pid:p10d0ha7f92435c1439e09da002dbe6d5d20196b*/
package com.zte.application.impl.api;

import com.zte.domain.model.BsPubHrvOrgId;
import com.zte.domain.model.api.CfSubscriptionApiMapperRepository;
import com.zte.infrastructure.remote.HrmUserInfoService;
import com.zte.interfaces.dto.api.CfSubscriptionApiDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.junit.Assert.assertEquals;

public class CfSubscriptionApiServiceImpl_queryByPage_4_Test extends BaseTestCase {
    @InjectMocks private CfSubscriptionApiServiceImpl cfSubscriptionApiService;
    @Mock private CfSubscriptionApiMapperRepository cfSubscriptionApiMapper;
    @Mock private HrmUserInfoService hrmUserInfoService;

    @Before
    public void setup() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testQueryByPageEmptyList() throws Exception{
        Page<CfSubscriptionApiDTO> page = new Page<>();
        page.setRows(new ArrayList<>());
        PowerMockito.when(cfSubscriptionApiMapper.queryByPage(page)).thenReturn(new ArrayList<>());
        Page<CfSubscriptionApiDTO> result = cfSubscriptionApiService.queryByPage(page);
        assertEquals(0, result.getRows().size());

        List<CfSubscriptionApiDTO> resultList = new LinkedList<>();
        CfSubscriptionApiDTO a1 = new CfSubscriptionApiDTO();
        resultList.add(a1);
        CfSubscriptionApiDTO a2 = new CfSubscriptionApiDTO();
        a2.setCreateBy("123");
        a2.setLastUpdatedBy("234");
        a2.setMatchType("1");
        resultList.add(a2);
        PowerMockito.when(cfSubscriptionApiMapper.queryByPage(page)).thenReturn(resultList);
        cfSubscriptionApiService.queryByPage(page);

        List<BsPubHrvOrgId> bsPubHrvOrgIdInfo = new LinkedList<>();
        BsPubHrvOrgId b1 = new BsPubHrvOrgId();
        b1.setUserId("123");
        bsPubHrvOrgIdInfo.add(b1);
        BsPubHrvOrgId b2 = new BsPubHrvOrgId();
        b2.setUserId("234");
        bsPubHrvOrgIdInfo.add(b2);
        PowerMockito.when(hrmUserInfoService.getBsPubHrvOrgIdInfo(Mockito.anyList())).thenReturn(bsPubHrvOrgIdInfo)
        ;
        cfSubscriptionApiService.queryByPage(page);
    }
}
/*Ended by AICoder, pid:p10d0ha7f92435c1439e09da002dbe6d5d20196b*/