package com.zte.application.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.zte.application.AuxMaterialReqRelChangeRecordService;
import com.zte.application.HrmUserCenterService;
import com.zte.common.ConstantInterface;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.AbnormalMaterialBillHead;
import com.zte.domain.model.ApprovalProcessInfoRepository;
import com.zte.domain.model.AuxMaterialRequisitionRelationship;
import com.zte.domain.model.AuxMaterialRequisitionRelationshipRepository;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.BsItemInfoRepository;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.infrastructure.remote.ApprovalRemoteService;
import com.zte.interfaces.dto.ApprovalProcessInfoEntityDTO;
import com.zte.interfaces.dto.AuxMaterialRequisitionRelationshipPageQueryDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.spare.SparePartAllocationDetailDTO;
import com.zte.interfaces.sncabind.dto.PsTaskDTO;
import com.zte.iss.approval.sdk.bean.TaskVo;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.iss.approval.sdk.client.ApprovalTaskClient;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.util.BaseTestCase;
import org.apache.commons.math3.analysis.function.Pow;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * 辅料领用关系表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-28 16:30:54
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({EasyExcelFactory.class, ApprovalTaskClient.class, ApprovalFlowClient.class, RedisCacheUtils.class, ExcelCommonUtils.class, RedisHelper.class})
public class AuxMaterialRequisitionRelationshipServiceImplTest extends BaseTestCase {

    @InjectMocks
    private AuxMaterialRequisitionRelationshipServiceImpl service;
    @Mock
    private AuxMaterialRequisitionRelationshipRepository repository;

    @Mock
    private AuxMaterialReqRelChangeRecordService auxMaterialReqRelChangeRecordService;
    @Mock
    MultipartFile file;
    @Mock
    MockHttpServletResponse response;
    @Mock
    ExcelWriter excelWriter;
    @Mock
    ExcelWriterBuilder write;
    @Mock
    ServletOutputStream outputStream;
    @Mock
    ExcelWriterSheetBuilder excelWriterSheetBuilder;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private ApprovalRemoteService approvalRemoteService;
    @Mock
    private ApprovalProcessInfoRepository approvalProcessInforepository;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private HrmUserCenterService hrmUserCenterService;
    @Mock
    private BsItemInfoRepository bsItemInfoRepository;
    @Mock
    private RedisLock redisLock;
    @Mock
    CloudDiskHelper cloudDiskHelper;
    @Mock
    private ConstantInterface constantInterface;
    @Test
    public void testQueryPage() throws Exception {
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(new ArrayList<AuxMaterialRequisitionRelationship>());
        service.queryPage(new AuxMaterialRequisitionRelationshipPageQueryDTO());
        AuxMaterialRequisitionRelationshipPageQueryDTO page = new AuxMaterialRequisitionRelationshipPageQueryDTO();
        page.setCreateStartDate(new Date());
        service.queryPage(page);
        page.setCreateEndDate(new Date());
        page.setLastUpdatedStartDate(new Date());
        page.setLastUpdatedEndDate(new Date(System.currentTimeMillis() + 24L * 60 * 60 * 1000 * 367));
        try {
            service.queryPage(page);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.DAY_OVER_ONE_YEAR);
        }
        page.setCreateEndDate(new Date(System.currentTimeMillis() + 24L * 60 * 60 * 1000 * 367));
        try {
            service.queryPage(page);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.DAY_OVER_ONE_YEAR);
        }
        page.setCreateEndDate(new Date());
        page.setLastUpdatedEndDate(new Date());
        page.setHandledByMe(Constant.TRUE);
        service.queryPage(page);
        List<AuxMaterialRequisitionRelationship> relationships = new ArrayList<>();
        AuxMaterialRequisitionRelationship dto = new AuxMaterialRequisitionRelationship();
        relationships.add(dto);
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(relationships);
        service.queryPage(page);
        dto.setCurrentProcessor("10337580");
        dto.setCreateBy("10337580");
        dto.setLastUpdatedBy("10337580");
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrm = new HrmPersonInfoDTO();
        hrm.setEmpName("张三");
        hrmPersonInfoDTOMap.put("10337580", hrm);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);

        List<SysLookupValues> values = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("0");
        sysLookupValues.setDescriptionChin("零");
        values.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesRepository.getList(any())).thenReturn(values);
        dto.setRelationshipDimension(0);
        dto.setAuxMaterialType(0);
        dto.setStatus(0);
        service.queryPage(page);

        service.selectByNo("123");
    }


    @Test
    public void testAdd() throws Exception {
        PowerMockito.mockStatic(RedisCacheUtils.class, RedisHelper.class);
        service.addOperate(new AuxMaterialRequisitionRelationship());
        List<AuxMaterialRequisitionRelationship> relationships = new ArrayList<>();
        AuxMaterialRequisitionRelationship dto = new AuxMaterialRequisitionRelationship();
        relationships.add(dto);
        dto.setCurrentProcessor("10337580");
        dto.setCreateBy("10337580");
        dto.setLastUpdatedBy("10337580");
        dto.setApproveList(relationships);
        try {
            service.addOperate(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.RELATIONSHIP_VERIFY_NOT_PASS);
        }
        dto.setItemNo("1235");
        try {
            service.addOperate(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.RELATIONSHIP_VERIFY_NOT_PASS);
        }
        dto.setAuxMaterialCode("12345");
        dto.setProductCategory("123");
        try {
            service.addOperate(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.RELATIONSHIP_VERIFY_NOT_PASS);
        }
        dto.setRelationshipDimension(1);
        try {
            service.addOperate(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.RELATIONSHIP_VERIFY_NOT_PASS);
        }
        dto.setItemNo(null);
        dto.setProductCategory("1235");
        try {
            service.addOperate(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.RELATIONSHIP_VERIFY_NOT_PASS);
        }
        dto.setItemNo("1234");
        dto.setProductCategory(null);
        try {
            service.addOperate(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.RELATIONSHIP_VERIFY_NOT_PASS);
        }
        dto.setItemNo("123456789012");
        List<BsItemInfo> tempList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("123456789012");
        bsItemInfo.setItemName("123456789012");
        tempList.add(bsItemInfo);
        PowerMockito.when(bsItemInfoRepository.getItemNameAndPCBVersion(any())).thenReturn(tempList);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        try {
            service.addOperate(dto);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        service.addOperate(dto);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrm = new HrmPersonInfoDTO();
        hrm.setEmpName("张三");
        hrmPersonInfoDTOMap.put("10337580", hrm);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);
        service.addOperate(dto);
        PowerMockito.when(cloudDiskHelper.fileUpload(anyString(), anyString(), anyInt())).thenReturn("1234");
        PowerMockito.when(cloudDiskHelper.getFileDownloadUrl(any(), any(), any())).thenReturn("1234");
        dto.setChangeType("0");
        service.addOperate(dto);
        dto.setChangeType("1");
        service.addOperate(dto);
        dto.setChangeType("2");
        service.addOperate(dto);
        dto.setChangeType("3");
        service.addOperate(dto);
        PowerMockito.when(repository.selectByNo(any())).thenReturn(relationships);
        try {
            service.addOperate(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.RELATIONSHIP_VERIFY_NOT_PASS);
        }
    }
    @Test
    public void findMaxCount() throws Exception {
        PowerMockito.mockStatic(RedisCacheUtils.class);
        Assert.assertNotNull(service.findMaxCount("111"));
        PowerMockito.when(RedisCacheUtils.get(Mockito.anyString(), Mockito.any())).thenReturn(9);
        Assert.assertNotNull(service.findMaxCount("111"));
    }

    @Test
    public void testChange() throws Exception {
        PowerMockito.mockStatic(RedisCacheUtils.class, RedisHelper.class);
        service.changeOperate(new AuxMaterialRequisitionRelationship());
        List<AuxMaterialRequisitionRelationship> relationships = new ArrayList<>();
        AuxMaterialRequisitionRelationship dto = new AuxMaterialRequisitionRelationship();
        relationships.add(dto);
        dto.setCurrentProcessor("10337580");
        dto.setCreateBy("10337580");
        dto.setLastUpdatedBy("10337580");
        dto.setApproveList(relationships);
        try {
            service.changeOperate(dto);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        service.changeOperate(dto);
    }
    @Test
    public void getRequisitionRelaByCondition() {
        PsTaskDTO dto = new PsTaskDTO();
        dto.setItemNo("139571751152ZTA");
        dto.setExternalType("DHOME");
        List<AuxMaterialRequisitionRelationship> list = new ArrayList<>();
        PowerMockito.when(repository.queryByCondition(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(service.getRequisitionRelaByCondition(dto));
        AuxMaterialRequisitionRelationship dto1 = new AuxMaterialRequisitionRelationship();
        dto1.setItemNo("139571751152ZTA");
        list.add(dto1);
        Assert.assertNotNull(service.getRequisitionRelaByCondition(dto));
    }

    @Test
    public void getRequisitionRelaByConditionTwo() {
        PsTaskDTO dto = new PsTaskDTO();
        dto.setItemNo("139571751152ZTA");
        dto.setExternalType("DHOME");
        List<AuxMaterialRequisitionRelationship> list = new ArrayList<>();
        AuxMaterialRequisitionRelationship dto1 = new AuxMaterialRequisitionRelationship();
        dto1.setItemNo("139571751152");
        list.add(dto1);
        PowerMockito.when(repository.queryByCondition(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(service.getRequisitionRelaByCondition(dto));
    }

    @Test
    public void getRequisitionRelaByConditionThree() {
        PsTaskDTO dto = new PsTaskDTO();
        dto.setItemNo("139571751152ZTA");
        dto.setExternalType("DHOME");
        List<AuxMaterialRequisitionRelationship> list = new ArrayList<>();
        AuxMaterialRequisitionRelationship dto1 = new AuxMaterialRequisitionRelationship();
        dto1.setProductCategory("DHOME");
        list.add(dto1);
        PowerMockito.when(repository.queryByCondition(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(service.getRequisitionRelaByCondition(dto));
    }

    @Test
    public void getRequisitionRelaByConditionFour() {
        PsTaskDTO dto = new PsTaskDTO();
        dto.setItemNo("139571751152ZTA");
        dto.setExternalType("DHOME");
        List<AuxMaterialRequisitionRelationship> list = new ArrayList<>();
        AuxMaterialRequisitionRelationship dto1 = new AuxMaterialRequisitionRelationship();
        dto1.setItemNo("139571751152ZBA");
        list.add(dto1);
        PowerMockito.when(repository.queryByCondition(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(service.getRequisitionRelaByCondition(dto));
    }


    @Test
    public void testDeleteByIds() {
        PowerMockito.when(repository.deleteByIds(Mockito.any())).thenReturn(1);
        Assert.assertEquals("1",service.deleteByIds(new ArrayList()).toString());
    }

    @Test
    public void testCountExportTotal() {
        PowerMockito.when(repository.selectCount(Mockito.any())).thenReturn(1);
        Assert.assertEquals("1",service.countExportTotal(new AuxMaterialRequisitionRelationshipPageQueryDTO()).toString());
    }

    @Test
    public void testQueryExportData() throws Exception {
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(new ArrayList<AuxMaterialRequisitionRelationship>());
        service.queryExportData(new AuxMaterialRequisitionRelationshipPageQueryDTO(), 1, 10);
        PowerMockito.when(sysLookupValuesRepository.getList(any())).thenThrow(new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.DAY_OVER_ONE_YEAR));
        try {
            service.queryExportData(new AuxMaterialRequisitionRelationshipPageQueryDTO(), 1, 10);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.QUERY_EXPORT_ERROR, e.getMessage());
        }
    }

    @Test
    public void approvalOperateKafka() throws Exception {
        PowerMockito.mockStatic(RedisCacheUtils.class, RedisHelper.class);
        try {
            service.approvalOperateKafka(new AuxMaterialRequisitionRelationship());
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.CHANGE_ORDER_NO_DATA_NULL);
        }
        List<AuxMaterialRequisitionRelationship> relationships = new ArrayList<>();
        AuxMaterialRequisitionRelationship dto = new AuxMaterialRequisitionRelationship();
        relationships.add(dto);
        dto.setCurrentProcessor("10337580");
        dto.setCreateBy("10337580");
        dto.setLastUpdatedBy("10337580");
        PowerMockito.when(repository.selectByNo(any())).thenReturn(relationships);
        try {
            service.approvalOperateKafka(new AuxMaterialRequisitionRelationship());
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.CHANGE_ORDER_NO_DATA_NULL);
        }
        dto.setOperateType("审批完成");
        service.approvalOperateKafka(dto);
    }

    @Test
    public void approvalOperateSys() throws Exception {
        PowerMockito.mockStatic(RedisCacheUtils.class, RedisHelper.class, ApprovalTaskClient.class, ApprovalFlowClient.class);
        List<AuxMaterialRequisitionRelationship> relationships = new ArrayList<>();
        AuxMaterialRequisitionRelationship dto = new AuxMaterialRequisitionRelationship();
        relationships.add(dto);
        dto.setCurrentProcessor("10337580");
        dto.setCreateBy("10337580");
        dto.setLastUpdatedBy("10337580");
        dto.setApproveList(relationships);
        PowerMockito.when(repository.selectByNo(any())).thenReturn(relationships);
        try {
            service.approvalOperateSys(new AuxMaterialRequisitionRelationship());
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.CHANGE_ORDER_NO_DATA_NULL);
        }
        List<ApprovalProcessInfoEntityDTO> approvalProcessLit = new ArrayList<>();
        ApprovalProcessInfoEntityDTO approval = new ApprovalProcessInfoEntityDTO();
        approval.setRemark("0");
        approvalProcessLit.add(approval);
        PowerMockito.when(approvalProcessInforepository.getList(any())).thenReturn(approvalProcessLit);

        PageRows<TaskVo> taskVoPageRows = new PageRows<>();
        TaskVo taskVo = new TaskVo();
        taskVo.setTaskId("test");
        List<TaskVo> taskVoList = new ArrayList<>();
        taskVoList.add(taskVo);
        taskVoPageRows.setTotal(1);
        taskVoPageRows.setRows(taskVoList);
        PowerMockito.when(ApprovalTaskClient.queryTask(any())).thenReturn(taskVoPageRows);
        service.approvalOperateSys(dto);
        dto.setOperateType("同意");
        service.approvalOperateSys(dto);
        approval.setRemark("1");
        dto.setChangeType("1");
        dto.setOperateType("拒绝");
        service.approvalOperateSys(dto);
        dto.setOperateType("同意");
        service.approvalOperateSys(dto);
        approval.setRemark("2");
        dto.setChangeType("2");
        dto.setOperateType("拒绝");
        service.approvalOperateSys(dto);
        dto.setOperateType("同意");
        service.approvalOperateSys(dto);
        approval.setRemark("3");
        dto.setChangeType("3");
        dto.setOperateType("拒绝");
        service.approvalOperateSys(dto);
        dto.setOperateType("同意");
        service.approvalOperateSys(dto);
        approval.setRemark("4");
        try {
            service.approvalOperateSys(dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.THE_APPROVAL_TYPE_IS_INCORRECT);
        }
        dto.setChangeType("3");
        approval.setRemark("0");
        service.approvalOperateSys(dto);
    }

    @Test
    public void getTaskVoForCommon() throws Exception {
        PowerMockito.mockStatic(ApprovalTaskClient.class, ApprovalFlowClient.class);
        AuxMaterialRequisitionRelationship relationship = new AuxMaterialRequisitionRelationship();
        relationship.setLastUpdatedBy("00286569");
        relationship.setChangeOrderNo("test");
        try{
            PageRows<TaskVo> taskVoPageRows = new PageRows<>();
            TaskVo taskVo = new TaskVo();
            List<TaskVo> taskVoList = new ArrayList<>();
            taskVoList.add(taskVo);
            taskVoPageRows.setTotal(1);
            taskVoPageRows.setRows(taskVoList);
            PowerMockito.when(ApprovalTaskClient.queryTask(any())).thenReturn(taskVoPageRows);
            Whitebox.invokeMethod(service, "getTaskVoForCommon", relationship);
        } catch (Exception e){
            Assert.assertEquals(MessageId.NO_CORRESPONDING_DOCUMENTS_OBTAINED_IN_THE_APPROVAL_CENTER, e.getMessage());
        }
        try{
            PageRows<TaskVo> taskVoPageRows = new PageRows<>();
            List<TaskVo> taskVoList = new ArrayList<>();
            taskVoPageRows.setTotal(1);
            taskVoPageRows.setRows(taskVoList);
            PowerMockito.when(ApprovalTaskClient.queryTask(any())).thenReturn(taskVoPageRows);
            Whitebox.invokeMethod(service, "getTaskVoForCommon", relationship);
        } catch (Exception e){
            Assert.assertEquals(MessageId.NO_CORRESPONDING_DOCUMENTS_OBTAINED_IN_THE_APPROVAL_CENTER, e.getMessage());
        }
        try{
            PowerMockito.when(ApprovalTaskClient.queryTask(any())).thenReturn(null);
            Whitebox.invokeMethod(service, "getTaskVoForCommon", relationship);
        } catch (Exception e){
            Assert.assertEquals(MessageId.NO_CORRESPONDING_DOCUMENTS_OBTAINED_IN_THE_APPROVAL_CENTER, e.getMessage());
        }
    }

    @Test
    public void downLoadExcelTemplate() throws Exception {
        PowerMockito.mockStatic(EasyExcelFactory.class);
        PowerMockito.when(response.getOutputStream()).thenReturn(outputStream);
        PowerMockito.when(EasyExcelFactory.write(outputStream, AuxMaterialRequisitionRelationshipPageQueryDTO.class))
                .thenReturn(write);
        PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(write);
        PowerMockito.when(write.build()).thenReturn(excelWriter);
        PowerMockito.when(EasyExcelFactory.writerSheet(Mockito.any(), Mockito.any()))
                .thenReturn(excelWriterSheetBuilder);

        service.downLoadExcelTemplate(response);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void uploadExcel() throws Exception {
        PowerMockito.mockStatic(EasyExcelFactory.class);

        try {
            service.uploadExcel(file);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }
    @Test
    public void transImportName() throws Exception {
        List<AuxMaterialRequisitionRelationship> list = new ArrayList<>();
        Whitebox.invokeMethod(service, "transImportName", list);
        List<SysLookupValues> values = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("0");
        sysLookupValues.setDescriptionChin("零");
        values.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesRepository.getList(any())).thenReturn(values);
        AuxMaterialRequisitionRelationship auxMaterialRequisitionRelationship = new AuxMaterialRequisitionRelationship();
        auxMaterialRequisitionRelationship.setRelationshipDimensionName("料单代码");
        list.add(auxMaterialRequisitionRelationship);
        Whitebox.invokeMethod(service, "transImportName", list);
        Assert.assertEquals(1, list.get(0).getRelationshipDimension());
        auxMaterialRequisitionRelationship.setRelationshipDimensionName("产品大类");
        Whitebox.invokeMethod(service, "transImportName", list);
        auxMaterialRequisitionRelationship.setRelationshipDimensionName("1");
        auxMaterialRequisitionRelationship.setAuxMaterialTypeName("零");
        Whitebox.invokeMethod(service, "transImportName", list);
    }
}

