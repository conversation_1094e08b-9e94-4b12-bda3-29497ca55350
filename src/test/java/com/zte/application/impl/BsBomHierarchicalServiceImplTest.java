/*Started by AICoder, pid:0da2bn81c2a6bd7145f4091751848f166f17192f*/
package com.zte.application.impl;

import com.zte.domain.model.BProdBomHeaderRepository;
import com.zte.domain.model.BsBomHierarchicalDetail;
import com.zte.domain.model.BsBomHierarchicalDetailRepository;
import com.zte.domain.model.BsBomHierarchicalHead;
import com.zte.domain.model.BsBomHierarchicalHeadRepository;
import com.zte.interfaces.dto.BsBomHierarchicalDetailDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class BsBomHierarchicalServiceImplTest extends BaseTestCase {

    @Mock
    private BsBomHierarchicalDetailRepository bsBomHierarchicalDetailRepository;

    @Mock
    private BProdBomHeaderRepository bProdBomHeaderRepository;

    @InjectMocks
    private BsBomHierarchicalServiceImpl service;

    private BsBomHierarchicalDetailDTO dto;
    @Mock
    private BsBomHierarchicalHeadRepository bsBomHierarchicalHeadRepository;

    @Before
    public void setUp() {
        dto = new BsBomHierarchicalDetailDTO();
    }

    /*Started by AICoder, pid:nb3dddff54ac10e14235091b21eafc0f4dc5541a*/
    @Test
    public void testSelectBsBomHierarchicalByPage_WithProdplanId() {
        BsBomHierarchicalDetailDTO params = new BsBomHierarchicalDetailDTO();
        params.setPage(1);
        params.setRows(2);
        // Given
        String prodplanId = "testPlanId";
        String bomCode = "testBomCode";
        params.setProdplanId(prodplanId);

        when(bProdBomHeaderRepository.queryOriginalProductCodeByProdPlanId(prodplanId)).thenReturn(bomCode);
        when(bsBomHierarchicalDetailRepository.selectBsBomHierarchicalByPage(any())).thenReturn(Collections.singletonList(new BsBomHierarchicalDetail()));

        // When
        service.selectBsBomHierarchicalByPage(params);

        // Then
        assertEquals(bomCode, params.getBomCode());
        verify(bProdBomHeaderRepository, times(1)).queryOriginalProductCodeByProdPlanId(prodplanId);
        verify(bsBomHierarchicalDetailRepository, times(1)).selectBsBomHierarchicalByPage(any());
    }

    @Test
    public void testSelectBsBomHierarchicalByPage_WithoutProdplanId() {
        BsBomHierarchicalDetailDTO params = new BsBomHierarchicalDetailDTO();
        params.setPage(1);
        params.setRows(2);
        // Given
        params.setProdplanId(null);

        when(bsBomHierarchicalDetailRepository.selectBsBomHierarchicalByPage(any())).thenReturn(Collections.emptyList());

        // When
        service.selectBsBomHierarchicalByPage(params);

        // Then
        verify(bProdBomHeaderRepository, never()).queryOriginalProductCodeByProdPlanId(anyString());
        verify(bsBomHierarchicalDetailRepository, times(1)).selectBsBomHierarchicalByPage(any());
    }
    /*Ended by AICoder, pid:nb3dddff54ac10e14235091b21eafc0f4dc5541a*/

    @Test
    public void testSelectBomByBomCode_SubLevelsNotEmpty() {
        // Given
        dto.setSubLevels("1,2,3");
        List<String> expectedList = Arrays.asList("1", "2", "3");
        when(bsBomHierarchicalDetailRepository.selectBomByBomCode(any(BsBomHierarchicalDetailDTO.class)))
                .thenReturn(Collections.emptyList());

        // When
        List<BsBomHierarchicalDetailDTO> result = service.selectBomByBomCode(dto);

        // Then
        assertEquals(expectedList, dto.getSubLevelList());
        verify(bsBomHierarchicalDetailRepository, times(1)).selectBomByBomCode(dto);
        verify(bProdBomHeaderRepository, never()).queryOriginalProductCodeByProdPlanId(anyString());
    }

    @Test
    public void testSelectBomByBomCode_ProdplanIdNotBlank() {
        // Given
        dto.setProdplanId("plan1");
        String bomCode = "code1";
        when(bProdBomHeaderRepository.queryOriginalProductCodeByProdPlanId("plan1")).thenReturn(bomCode);
        when(bsBomHierarchicalDetailRepository.selectBomByBomCode(any(BsBomHierarchicalDetailDTO.class)))
                .thenReturn(Collections.emptyList());

        // When
        List<BsBomHierarchicalDetailDTO> result = service.selectBomByBomCode(dto);

        // Then
        assertEquals(bomCode, dto.getBomCode());
        verify(bProdBomHeaderRepository, times(1)).queryOriginalProductCodeByProdPlanId("plan1");
        verify(bsBomHierarchicalDetailRepository, times(1)).selectBomByBomCode(dto);
    }

    @Test
    public void testSelectBomByBomCode_NoSubLevelsAndNoProdplanId() {
        // Given
        when(bsBomHierarchicalDetailRepository.selectBomByBomCode(any(BsBomHierarchicalDetailDTO.class)))
                .thenReturn(Collections.emptyList());

        // When
        List<BsBomHierarchicalDetailDTO> result = service.selectBomByBomCode(dto);

        // Then
        verify(bsBomHierarchicalDetailRepository, times(1)).selectBomByBomCode(dto);
        verify(bProdBomHeaderRepository, never()).queryOriginalProductCodeByProdPlanId(anyString());

        dto.setProdplanId("ddd");
        PowerMockito.when(bProdBomHeaderRepository.queryOriginalProductCodeByProdPlanId(Mockito.any())).thenReturn(null);
        service.selectBomByBomCode(dto);
    }

    @Test
    public void selectHeadAndDetailByProdPlanId(){
        BsBomHierarchicalHead head = service.selectHeadAndDetailByProdPlanId(null);
        Assert.assertTrue(Objects.nonNull(head));

        head = service.selectHeadAndDetailByProdPlanId("344");

        List<BsBomHierarchicalHead> headList = new LinkedList<>();
        BsBomHierarchicalHead a1 = new BsBomHierarchicalHead();
        headList.add(a1);
        PowerMockito.when(bsBomHierarchicalHeadRepository.getListByProductList(Mockito.any()))
                .thenReturn(headList);
        service.selectHeadAndDetailByProdPlanId("344");
    }
}
/*Ended by AICoder, pid:0da2bn81c2a6bd7145f4091751848f166f17192f*/