package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.aiop.dtems.AlarmHelper;
import com.zte.application.HrmUserCenterService;
import com.zte.application.TradeDataLogService;
import com.zte.application.kafka.producer.CommonProducer;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.domain.model.TradeDataLogRepository;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.ICenterRemoteService;
import com.zte.interfaces.dto.B2bCallBackNewDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.message.SpringKafkaProducer;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.CommonUtils;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.apache.commons.lang.reflect.FieldUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/5/3 16:08
 */
@PrepareForTest({DateUtil.class, MESHttpHelper.class, JacksonJsonConverUtil.class, SpringContextUtil.class,
        MicroServiceRestUtil.class,ServiceDataBuilderUtil.class,
        HttpRemoteService.class, HttpClientUtil.class, CommonUtils.class, JSON.class, AlarmHelper.class, com.zte.common.CommonUtils.class})
public class TradeDataLogServiceImplTest extends BaseTestCase {

    @InjectMocks
    private TradeDataLogServiceImpl service;
    @Mock
    private TradeDataLogRepository tradeDataLogRepository;
    @Mock
    protected SpringKafkaProducer kafkaProducer;
    @Mock
    private CommonProducer commonProducer;
    /* Started by AICoder, pid:i8218g5654z62f8147980aa100aadf0a43086cf7 */
    @Mock
    private SysLookupTypesRepository sysLookupTypesRepository;
    @Mock
    private HrmUserCenterService hrmUserCenterService;
    @Mock
    private AlarmHelper alarmHelper;
    @Mock
    private HttpRemoteService httpRemoteService;
    /* Ended by AICoder, pid:i8218g5654z62f8147980aa100aadf0a43086cf7 */
    @Mock
    private JsonNode jsonNode;
    @Mock
    private ICenterRemoteService iCenterRemoteService;
    @Mock
    private Map<String,String> requestDefaultHeadMap;

    @Before
    public void before() throws Exception {
        FieldUtils.writeField(service, "size", 1024000, true);
        FieldUtils.writeField(service, "requestDefaultHeadMap", requestDefaultHeadMap, true);
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(TradeDataLogService.class)).thenReturn(service);
    }

    @Test
    public void testPushDataWithRollback_Success0() throws Exception {
        // 1. 准备测试数据
        CustomerDataLogDTO dto1 = new CustomerDataLogDTO();
        dto1.setId("1");
        dto1.setMessageType("type1");
        dto1.setJsonData("{\"name\": \"test\"}");

        // 3. 模拟 JSON.parseObject
        PowerMockito.mockStatic(JSON.class);
        when(JSON.parseObject(dto1.getJsonData(), Object.class)).thenReturn(new Object());

        // 4. 模拟 Kafka 推送成功
        PowerMockito.doNothing().when(commonProducer).sendMsgSync(anyString(), anyString(), any(), anyString());

        // 5. 模拟 batchInsert 和 updateStatus
        PowerMockito.doNothing().when(tradeDataLogRepository).batchInsert(anyList());
        PowerMockito.doNothing().when(tradeDataLogRepository).updateStatusAndErrMsgById(anyString(), anyString(), anyString());

        // 6. 调用方法
        service.pushDataOfExceptionRollback(dto1);

        // 7. 验证行为
        assertNotNull(dto1);
    }

    @Test
    public void testPushDataWithRollback_Success1() throws Exception {
        // 1. 准备测试数据
        CustomerDataLogDTO dto1 = new CustomerDataLogDTO();
        dto1.setMessageType("type1");
        dto1.setJsonData("{\"name\": \"test\"}");

        // 3. 模拟 JSON.parseObject
        PowerMockito.mockStatic(JSON.class);
        when(JSON.parseObject(dto1.getJsonData(), Object.class)).thenReturn(new Object());

        // 4. 模拟 Kafka 推送成功
        PowerMockito.doNothing().when(commonProducer).sendMsgSync(anyString(), anyString(), any(), anyString());

        // 5. 模拟 batchInsert 和 updateStatus
        PowerMockito.doNothing().when(tradeDataLogRepository).batchInsert(anyList());
        PowerMockito.doNothing().when(tradeDataLogRepository).updateStatusAndErrMsgById(anyString(), anyString(), anyString());

        // 6. 调用方法
        service.pushDataOfExceptionRollback(dto1);

        // 7. 验证行为
        assertNotNull(dto1);
    }

    @Test
    public void testDataOfExceptionRollback_Success() throws Exception {
        // 1. 准备测试数据
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        CustomerDataLogDTO dto1 = new CustomerDataLogDTO();
        dto1.setId("1");
        dto1.setMessageType("type1");
        dto1.setJsonData("{\"name\": \"test\"}");
        dataList.add(dto1);
        CustomerDataLogDTO dto2 = new CustomerDataLogDTO();
        dto2.setMessageType("type1");
        dto2.setJsonData("{\"name\": \"test\"}");
        dataList.add(dto2);

        // 2. 模拟 CommonUtils.splitList
        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.splitList(dataList, Constant.BATCH_SIZE)).thenReturn(Collections.singletonList(dataList));

        // 3. 模拟 JSON.parseObject
        PowerMockito.mockStatic(JSON.class);
        when(JSON.parseObject(dto1.getJsonData(), Object.class)).thenReturn(new Object());

        // 4. 模拟 Kafka 推送成功
        PowerMockito.doNothing().when(commonProducer).sendMsgSync(anyString(), anyString(), any(), anyString());

        // 5. 模拟 batchInsert 和 updateStatus
        PowerMockito.doNothing().when(tradeDataLogRepository).batchInsert(anyList());
        PowerMockito.doNothing().when(tradeDataLogRepository).updateStatusAndErrMsgById(anyString(), anyString(), anyString());

        // 6. 调用方法
        service.pushDataOfExceptionRollback(dataList);

        // 7. 验证行为
        assertNotNull(dataList);
    }

    @Test
    public void testPushData_Success() throws Exception {
        // 1. 准备测试数据
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        CustomerDataLogDTO dto1 = new CustomerDataLogDTO();
        dto1.setId("1");
        dto1.setMessageType("type1");
        dto1.setJsonData("{\"name\": \"test\"}");
        dataList.add(dto1);

        // 2. 模拟 CommonUtils.splitList
        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.splitList(dataList, Constant.BATCH_SIZE)).thenReturn(Collections.singletonList(dataList));

        // 3. 模拟 JSON.parseObject
        PowerMockito.mockStatic(JSON.class);
        when(JSON.parseObject(dto1.getJsonData(), Object.class)).thenReturn(new Object());

        // 4. 模拟 Kafka 推送成功
        PowerMockito.doReturn(true).when(kafkaProducer).sendMessage(anyString(), any(), anyString(), anyString());

        // 5. 模拟 batchInsert 和 updateStatus
        PowerMockito.doNothing().when(tradeDataLogRepository).batchInsert(anyList());
        PowerMockito.doNothing().when(tradeDataLogRepository).updateStatusAndErrMsgById(anyString(), anyString(), anyString());

        // 6. 调用方法
        service.pushData(dataList);

        // 7. 验证行为
        assertNotNull(dataList);
    }

    @Test
    public void testPushData_Success_checkSize() throws Exception {
        // 1. 准备测试数据
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        CustomerDataLogDTO dto1 = new CustomerDataLogDTO();
        dto1.setMessageType("type1");
        dto1.setJsonData("{\"name\": \"test\"}");
        dataList.add(dto1);

        // 2. 模拟 CommonUtils.splitList
        FieldUtils.writeField(service, "size", 1, true);
        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.splitList(dataList, Constant.BATCH_SIZE)).thenReturn(Collections.singletonList(dataList));

        // 3. 模拟 JSON.parseObject
        PowerMockito.mockStatic(JSON.class);
        when(JSON.parseObject(dto1.getJsonData(), Object.class)).thenReturn(new Object());

        // 4. 模拟 Kafka 推送成功
        PowerMockito.doReturn(true).when(kafkaProducer).sendMessage(anyString(), any(), anyString(), anyString());

        // 5. 模拟 batchInsert 和 updateStatus
        PowerMockito.doNothing().when(tradeDataLogRepository).batchInsert(anyList());
        PowerMockito.doNothing().when(tradeDataLogRepository).updateStatusAndErrMsgById(anyString(), anyString(), anyString());

        // 6. 调用方法
        service.pushData(dataList);

        // 7. 验证行为
        assertNotNull(dataList);
    }

    @Test
    public void testPushData_Exception() throws Exception {
        // 1. 准备测试数据
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        CustomerDataLogDTO dto1 = new CustomerDataLogDTO();
        dto1.setId("1");
        dto1.setMessageType("type1");
        dto1.setJsonData("{\"name\": \"test\"}");
        dataList.add(dto1);

        // 2. 模拟 CommonUtils.splitList
        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.splitList(dataList, Constant.BATCH_SIZE)).thenReturn(Collections.singletonList(dataList));

        // 3. 模拟 JSON.parseObject
        PowerMockito.mockStatic(JSON.class);
        when(JSON.parseObject(dto1.getJsonData(), Object.class)).thenReturn(new Object());

        // 4. 模拟 Kafka 推送失败
        PowerMockito.doReturn(true).when(kafkaProducer).sendMessage(anyString(), any(), anyString(), anyString());

        // 5. 模拟 batchInsert
        PowerMockito.doNothing().when(tradeDataLogRepository).batchInsert(anyList());

        // 6. 模拟 alarm
        PowerMockito.mockStatic(AlarmHelper.class);
        PowerMockito.doNothing().when(AlarmHelper.class, "alarm", anyString(), anyString(), any(), anyString(), anyString());

        // 7. 模拟 updateStatus
        PowerMockito.doThrow(new RuntimeException("Kafka error")).doNothing().when(tradeDataLogRepository).updateStatusAndErrMsgById(anyString(), anyString(), anyString());

        // 8. 调用方法
        service.pushData(dataList);

        // 9. 验证行为
        assertNotNull(dataList);
    }

    @Test
    public void testPushData_MesBusinessException() throws Exception {
        // 1. 准备测试数据
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        CustomerDataLogDTO dto1 = new CustomerDataLogDTO();
        dto1.setId("1");
        dto1.setMessageType("type1");
        dto1.setJsonData("{\"name\": \"test\"}");
        dataList.add(dto1);

        // 2. 模拟 CommonUtils.splitList
        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.splitList(dataList, Constant.BATCH_SIZE)).thenReturn(Collections.singletonList(dataList));

        // 3. 模拟 JSON.parseObject
        PowerMockito.mockStatic(JSON.class);
        when(JSON.parseObject(dto1.getJsonData(), Object.class)).thenReturn(new Object());

        // 4. 模拟 Kafka 推送失败
        PowerMockito.doReturn(true).when(kafkaProducer).sendMessage(anyString(), any(), anyString(), anyString());
        PowerMockito.mockStatic(com.zte.common.CommonUtils.class);
        when(com.zte.common.CommonUtils.getLmbMessage(anyString())).thenReturn("test");
        // 5. 模拟 batchInsert
        PowerMockito.doNothing().when(tradeDataLogRepository).batchInsert(anyList());

        // 6. 模拟 alarm
        PowerMockito.mockStatic(AlarmHelper.class);
        PowerMockito.doNothing().when(AlarmHelper.class, "alarm", anyString(), anyString(), any(), anyString(), anyString());

        // 7. 模拟 updateStatus
        PowerMockito.doThrow(new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.SIZE_OF_DATA_TOO_LARGE)).doNothing().when(tradeDataLogRepository).updateStatusAndErrMsgById(anyString(), anyString(), anyString());

        // 8. 调用方法
        service.pushData(dataList);

        // 9. 验证行为
        assertNotNull(dataList);
    }

    /* Started by AICoder, pid:h8218d5654v62f8147980aa100aadf1a43006cf7 */
    @Test
    public void testHandleB2bCallback_Success() {
        /* Started by AICoder, pid:q821875654162f8147980aa100aadf0a43036cf7 */
        B2bCallBackNewDTO successCallback = new B2bCallBackNewDTO();
        successCallback.setSuccess(true);
        successCallback.setCode("0000");
        successCallback.setMessageId("123");
        /* Ended by AICoder, pid:q821875654162f8147980aa100aadf0a43036cf7 */

        PowerMockito.doNothing().when(tradeDataLogRepository).updateStatusAndErrMsgById(anyString(), anyString(), anyString());
        PowerMockito.doNothing().when(tradeDataLogRepository).updateStatusAndRemarkById(anyString(), anyString(), anyString());
        PowerMockito.doNothing().when(tradeDataLogRepository).updateParentStatusById(anyString(), anyString());

        service.handleB2bCallback(successCallback);

        verify(tradeDataLogRepository).updateParentStatusById(Constant.PUSH_B2B_STATUS.CY, "123");
    }
    /* Ended by AICoder, pid:h8218d5654v62f8147980aa100aadf1a43006cf7 */

    @Test
    public void testHandleB2bCallback_Failure() {
        /* Started by AICoder, pid:98218k5654n62f8147980aa100aadf0a43036cf7 */
        B2bCallBackNewDTO failureCallback = new B2bCallBackNewDTO();
        failureCallback.setSuccess(false);
        failureCallback.setCode("0000");
        failureCallback.setMessageId("456");
        /* Ended by AICoder, pid:98218k5654n62f8147980aa100aadf0a43036cf7 */
        PowerMockito.when(SpringContextUtil.getBean(TradeDataLogService.class)).thenReturn(null);
        PowerMockito.doNothing().when(tradeDataLogRepository).updateStatusAndErrMsgById(anyString(), anyString(), anyString());
        PowerMockito.doNothing().when(tradeDataLogRepository).updateStatusAndRemarkById(anyString(), anyString(), anyString());

        service.handleB2bCallback(failureCallback);

        assertNotNull(failureCallback);
    }

    /* Started by AICoder, pid:j821815654i62f8147980aa100aadf3a43026cf7 */
    @Test
    public void testHandleB2bCallback_Failure2() {
        /* Started by AICoder, pid:98218k5654n62f8147980aa100aadf0a43036cf7 */
        B2bCallBackNewDTO failureCallback = new B2bCallBackNewDTO();
        failureCallback.setSuccess(true);
        failureCallback.setCode("0001");
        failureCallback.setMessageId("456");
        /* Ended by AICoder, pid:98218k5654n62f8147980aa100aadf0a43036cf7 */
        PowerMockito.doNothing().when(tradeDataLogRepository).updateStatusAndErrMsgById(anyString(), anyString(), anyString());
        PowerMockito.doNothing().when(tradeDataLogRepository).updateStatusAndRemarkById(anyString(), anyString(), anyString());

        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        PowerMockito.when(tradeDataLogRepository.getTradeDataLogById(Mockito.any())).thenReturn(customerDataLogDTO);
        PowerMockito.doNothing().when(iCenterRemoteService).sendMessage(anyString(), anyString(), anyString());
        service.handleB2bCallback(failureCallback);

        assertNotNull(failureCallback);
    }
    /* Ended by AICoder, pid:j821815654i62f8147980aa100aadf3a43026cf7 */

    @Test(expected = MesBusinessException.class)
    public void testHandleB2bCallback_WithMessageIdIsNull() throws Exception {
        B2bCallBackNewDTO successCallback = new B2bCallBackNewDTO();
        successCallback.setSuccess(true);
        successCallback.setCode("0000");
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_250503);

        SysLookupTypesDTO lookupType = new SysLookupTypesDTO();
        lookupType.setAttribute1("serviceName");
        lookupType.setAttribute2("url");
        lookupType.setAttribute3("sendType");
        lookupType.setLookupMeaning("messageType");

        List<SysLookupTypesDTO> valuesList = Arrays.asList(lookupType);

        when(sysLookupTypesRepository.getList(requestParam)).thenReturn(valuesList);
        PowerMockito.mockStatic(HttpRemoteService.class);
        when(HttpRemoteService.pointToPointSelectiveByObj(anyString(), anyString(), anyString(), anyString(), any())).thenReturn(null);

        successCallback.setMessageType("messageType");
        successCallback.setData("DATA");

        PowerMockito.doReturn(1).when(tradeDataLogRepository).insert(any());

        service.handleB2bCallback(successCallback);
    }

    /* Started by AICoder, pid:z450e84c14u00d8149710b3790f43e79bec5d71e */
    @Test
    public void testHandleB2bCallback_WithMessageIdIsNull2() throws Exception {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        B2bCallBackNewDTO successCallback = new B2bCallBackNewDTO();
        successCallback.setSuccess(true);
        successCallback.setCode("0000");
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_250503);

        SysLookupTypesDTO lookupType = new SysLookupTypesDTO();
        lookupType.setAttribute1("serviceName");
        lookupType.setAttribute2("url");
        lookupType.setAttribute3("sendType");
        lookupType.setLookupMeaning("messageType");

        List<SysLookupTypesDTO> valuesList = Arrays.asList(lookupType);

        when(sysLookupTypesRepository.getList(requestParam)).thenReturn(valuesList);
        PowerMockito.mockStatic(HttpRemoteService.class);
        when(HttpRemoteService.pointToPointSelectiveByObj(anyString(), anyString(), anyString(), anyString(), any())).thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree("{}"));

        successCallback.setMessageType("messageType");
        successCallback.setData("DATA");

        PowerMockito.doReturn(1).when(tradeDataLogRepository).insert(any());

        service.handleB2bCallback(successCallback);

        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        PowerMockito.when(tradeDataLogRepository.getTradeDataLogById(Mockito.any()))
                .thenReturn(customerDataLogDTO);
        service.handleB2bCallback(successCallback);

        customerDataLogDTO.setFactoryId(5);
        service.handleB2bCallback(successCallback);
        Assert.assertTrue(Objects.nonNull(service));
    }
    /* Ended by AICoder, pid:z450e84c14u00d8149710b3790f43e79bec5d71e */

    @Test(expected = MesBusinessException.class)
    public void testHandleB2bCallback_WithHttpRequest_ServiceNamePresent_JsonNodeIsNull() throws Exception {
        B2bCallBackNewDTO successCallback = new B2bCallBackNewDTO();
        successCallback.setSuccess(true);
        successCallback.setCode("0000");
        successCallback.setMessageId("123");
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_250503);

        SysLookupTypesDTO lookupType = new SysLookupTypesDTO();
        lookupType.setAttribute1("serviceName");
        lookupType.setAttribute2("url");
        lookupType.setAttribute3("sendType");
        lookupType.setLookupMeaning("messageType");

        List<SysLookupTypesDTO> valuesList = Arrays.asList(lookupType);

        when(sysLookupTypesRepository.getList(requestParam)).thenReturn(valuesList);
        PowerMockito.mockStatic(HttpRemoteService.class);
        when(HttpRemoteService.pointToPointSelectiveByObj(anyString(), anyString(), anyString(), anyString(), any())).thenReturn(null);

        successCallback.setMessageType("messageType");
        successCallback.setData("DATA");

        service.handleB2bCallback(successCallback);
    }

    @Test
    public void testHandleB2bCallback_WithHttpRequest_ServiceNamePresent_JsonNodeIsNull2() throws Exception {
        B2bCallBackNewDTO successCallback = new B2bCallBackNewDTO();
        successCallback.setSuccess(true);
        successCallback.setCode("0000");
        successCallback.setMessageId("123");
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_250503);

        SysLookupTypesDTO lookupType = new SysLookupTypesDTO();
        lookupType.setAttribute1("serviceName");
        lookupType.setAttribute2("url");
        lookupType.setAttribute3("sendType");
        lookupType.setLookupMeaning("messageType");

        List<SysLookupTypesDTO> valuesList = Arrays.asList(lookupType);

        when(sysLookupTypesRepository.getList(requestParam)).thenReturn(valuesList);
        PowerMockito.mockStatic(HttpRemoteService.class);
        when(HttpRemoteService.pointToPointSelectiveByObj(anyString(), anyString(), anyString(), anyString(), any())).thenReturn(null);

        successCallback.setMessageType("messageType2");
        successCallback.setData("DATA");

        service.handleB2bCallback(successCallback);
        assertNotNull(successCallback);
    }

    @Test(expected = MesBusinessException.class)
    public void testHandleB2bCallback_WithHttpRequest_ServiceNamePresent2() throws Exception {
        B2bCallBackNewDTO successCallback = new B2bCallBackNewDTO();
        successCallback.setSuccess(true);
        successCallback.setCode("0000");
        successCallback.setMessageId("123");
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_250503);

        SysLookupTypesDTO lookupType = new SysLookupTypesDTO();
        lookupType.setAttribute1("serviceName");
        lookupType.setAttribute2("url");
        lookupType.setAttribute3("sendType");
        lookupType.setLookupMeaning("messageType");

        List<SysLookupTypesDTO> valuesList = Arrays.asList(lookupType);

        when(sysLookupTypesRepository.getList(requestParam)).thenReturn(valuesList);
        PowerMockito.mockStatic(HttpRemoteService.class);
        when(HttpRemoteService.pointToPointSelectiveByObj(anyString(), anyString(), anyString(), anyString(), any())).thenReturn(jsonNode);
        when(jsonNode.get(Constant.STR_BO)).thenReturn(null);

        successCallback.setMessageType("messageType");
        successCallback.setData("DATA");

        service.handleB2bCallback(successCallback);
    }

    /* Started by AICoder, pid:ydd2a8c3b2ybdf81485d0acf701fd862a0851ed7 */
    @Test(expected = MesBusinessException.class)
    public void testHandleB2bCallback_WithHttpRequest_ServiceNamePresent() throws Exception {
        B2bCallBackNewDTO successCallback = new B2bCallBackNewDTO();
        successCallback.setSuccess(true);
        successCallback.setCode("0000");
        successCallback.setMessageId("123");
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_250503);

        SysLookupTypesDTO lookupType = new SysLookupTypesDTO();
        lookupType.setAttribute1("serviceName");
        lookupType.setAttribute2("url");
        lookupType.setAttribute3("sendType");
        lookupType.setLookupMeaning("messageType");

        List<SysLookupTypesDTO> valuesList = Arrays.asList(lookupType);

        when(sysLookupTypesRepository.getList(requestParam)).thenReturn(valuesList);
        PowerMockito.mockStatic(HttpRemoteService.class);
        when(HttpRemoteService.pointToPointSelectiveByObj(anyString(), anyString(), anyString(), anyString(), any())).thenReturn(jsonNode);
        when(jsonNode.get(Constant.STR_BO)).thenReturn(mock(JsonNode.class));

        successCallback.setMessageType("messageType");
        successCallback.setData("DATA");

        service.handleB2bCallback(successCallback);
        assertNotNull(successCallback);
    }

    @Test(expected = NullPointerException.class)
    public void testHandleB2bCallback_WithHttpRequest_ServiceNameNotPresent() throws Exception {
        B2bCallBackNewDTO successCallback = new B2bCallBackNewDTO();
        successCallback.setSuccess(true);
        successCallback.setCode("0000");
        successCallback.setMessageId("123");
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_250503);

        SysLookupTypesDTO lookupType = new SysLookupTypesDTO();
        lookupType.setAttribute1(null);
        lookupType.setAttribute2("url");
        lookupType.setAttribute3("sendType");
        lookupType.setLookupMeaning("messageType");

        List<SysLookupTypesDTO> valuesList = Arrays.asList(lookupType);

        when(sysLookupTypesRepository.getList(requestParam)).thenReturn(valuesList);
        PowerMockito.mockStatic(HttpRemoteService.class);
        when(HttpRemoteService.remoteExe(anyMap(), anyMap(), anyString(), anyString())).thenReturn("{\"status\":\"success\"}");

        successCallback.setMessageType("messageType");
        successCallback.setData("DATA");

        service.handleB2bCallback(successCallback);

    }

    @Test(expected = NullPointerException.class)
    public void testHandleB2bCallback_WithHttpRequest_ServiceNameNotPresent2() throws Exception {
        B2bCallBackNewDTO successCallback = new B2bCallBackNewDTO();
        successCallback.setSuccess(true);
        successCallback.setCode("0000");
        successCallback.setMessageId("123");
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_250503);

        SysLookupTypesDTO lookupType = new SysLookupTypesDTO();
        lookupType.setAttribute1(null);
        lookupType.setAttribute2("url");
        lookupType.setAttribute3("sendType");
        lookupType.setAttribute4("appcode");
        lookupType.setLookupMeaning("messageType");

        List<SysLookupTypesDTO> valuesList = Arrays.asList(lookupType);

        when(sysLookupTypesRepository.getList(requestParam)).thenReturn(valuesList);
        PowerMockito.mockStatic(HttpRemoteService.class);
        when(HttpRemoteService.remoteExe(anyMap(), anyMap(), anyString(), anyString())).thenReturn("{\"status\":\"success\"}");

        successCallback.setMessageType("messageType");
        successCallback.setData("DATA");

        service.handleB2bCallback(successCallback);

    }

    @Test
    public void testHandleB2bCallback_WithHttpRequest_NoMatchingLookup() {
        B2bCallBackNewDTO successCallback = new B2bCallBackNewDTO();
        successCallback.setSuccess(true);
        successCallback.setCode("0000");
        successCallback.setMessageId("123");
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_250503);

        when(sysLookupTypesRepository.getList(requestParam)).thenReturn(Arrays.asList());

        successCallback.setMessageType("messageType");
        successCallback.setData("DATA");

        service.handleB2bCallback(successCallback);
        Assert.assertNotNull(successCallback);
    }
    /* Ended by AICoder, pid:ydd2a8c3b2ybdf81485d0acf701fd862a0851ed7 */


    /* Started by AICoder, pid:n06a787c35c53ff140d90bca20911a41a0e06d54 */
    @Test
    public void testGetPushErrorData() {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();

        // 模拟返回的数据
        List<CustomerDataLogDTO> mockResult = Arrays.asList(
                new CustomerDataLogDTO(),
                new CustomerDataLogDTO()
        );

        // 模拟 tradeDataLogRepository.getPushErrorData 的行为
        when(tradeDataLogRepository.getPushErrorData(any(Page.class))).thenReturn(mockResult);

        // 调用被测试的方法
        List<CustomerDataLogDTO> result = service.getPushErrorData(customerDataLogDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());

    }

    @Test
    public void testGetPushErrorData_EmptyResult() {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        // 模拟返回空列表
        List<CustomerDataLogDTO> mockResult = Arrays.asList();

        // 模拟 tradeDataLogRepository.getPushErrorData 的行为
        when(tradeDataLogRepository.getPushErrorData(any(Page.class))).thenReturn(mockResult);

        // 调用被测试的方法
        List<CustomerDataLogDTO> result = service.getPushErrorData(customerDataLogDTO);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());

    }
    /* Ended by AICoder, pid:n06a787c35c53ff140d90bca20911a41a0e06d54 */

    /* Started by AICoder, pid:d77ed71b976e6bb146b50b5eb010c238a9b9c50d */
    @Test
    public void getDataBySn() {
        List<String> snList = new ArrayList<>();
        List<String> projectPhaseList = new ArrayList<>();
        List<CustomerDataLogDTO> result = service.getDataBySn(snList, projectPhaseList);
        Assert.assertTrue(result.isEmpty());

        snList.add("777766600001");
        result = service.getDataBySn(snList, projectPhaseList);
        Assert.assertTrue(result.isEmpty());

        for (int i = 0; i <= 1000; i++) {
            snList.add(i + "");
        }
        try {
            service.getDataBySn(snList, projectPhaseList);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_MSGID, e.getExMsgId());
        }
    }
    /* Ended by AICoder, pid:d77ed71b976e6bb146b50b5eb010c238a9b9c50d */

    /* Started by AICoder, pid:4e5d2zb463pcab814d4f089520bf7814502086f7 */
    @Test
    public void testGetTradeDataLogById_Success() {
        CustomerDataLogDTO expectedLog = new CustomerDataLogDTO();
        when(tradeDataLogRepository.getTradeDataLogById("testId")).thenReturn(expectedLog);

        CustomerDataLogDTO result = service.getTradeDataLogById("testId");

        assertNotNull(result);
        assertEquals(expectedLog, result);
        verify(tradeDataLogRepository).getTradeDataLogById("testId");
    }
    /* Ended by AICoder, pid:4e5d2zb463pcab814d4f089520bf7814502086f7 */

    /* Started by AICoder, pid:g90bd0856bo3555149e30874c058fd31694740ee */

    @Test
    public void testGetPushDataWithData() {
        String uuid = "testUuid";
        String mockData = "{\"key\":\"value\"}";

        when(tradeDataLogRepository.getPushData(uuid)).thenReturn(mockData);

        Object result = service.getPushData(uuid);

        assertEquals(JSON.parse(mockData), result);
        verify(tradeDataLogRepository, times(1)).getPushData(uuid);
    }

    @Test
    public void testGetPushDataWithEmptyData() {
        String uuid = "testUuid";

        when(tradeDataLogRepository.getPushData(uuid)).thenReturn("");

        Object result = service.getPushData(uuid);

        assertNull(result);
        verify(tradeDataLogRepository, times(1)).getPushData(uuid);
    }

    @Test
    public void testGetPushDataWithNullData() {
        String uuid = "testUuid";

        when(tradeDataLogRepository.getPushData(uuid)).thenReturn(null);

        Object result = service.getPushData(uuid);

        assertNull(result);
        verify(tradeDataLogRepository, times(1)).getPushData(uuid);
    }
    /* Ended by AICoder, pid:g90bd0856bo3555149e30874c058fd31694740ee */


    @Test
    public void testRepushWithTid() {
        PowerMockito.mockStatic(MESHttpHelper.class);
        // given
        Map<String, String> headers = new HashMap<>();
        headers.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase(), "testEmpNo");
        when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headers);

        CustomerDataLogDTO item1 = mock(CustomerDataLogDTO.class);
        when(item1.getTid()).thenReturn("tid1");
        when(item1.getId()).thenReturn(null);
        when(item1.getJsonData()).thenReturn("{}");

        List<CustomerDataLogDTO> paramList = Arrays.asList(item1);

        // when
        service.repush(paramList);

        // then
        assertNotNull(headers);
    }

    @Test
    public void testRepushWithoutTid() {
        PowerMockito.mockStatic(MESHttpHelper.class);
        // given
        Map<String, String> headers = new HashMap<>();
        headers.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase(), "testEmpNo");
        when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headers);

        CustomerDataLogDTO item1 = mock(CustomerDataLogDTO.class);
        when(item1.getTid()).thenReturn(null);
        when(item1.getJsonData()).thenReturn("{}");

        List<CustomerDataLogDTO> paramList = Arrays.asList(item1);

        // when
        service.repush(paramList);

        // then
        assertNotNull(headers);
    }


    @Test
    public void testQueryLogData() throws Exception {
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        try {
            service.queryLogData(customerDataLogDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOG_DATA_QUERY_PARAM_NULL, e.getMessage());
        }
        customerDataLogDTO.setEndDate(new Date());
        try {
            service.queryLogData(customerDataLogDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOG_DATA_QUERY_PARAM_NOT_WITH_TIME, e.getMessage());
        }
        customerDataLogDTO.setStartDate(new Date());
        try {
            service.queryLogData(customerDataLogDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOG_DATA_QUERY_PARAM_NOT_WITH_TIME, e.getMessage());
        }
        customerDataLogDTO.setStatus("PN");
        try {
            service.queryLogData(customerDataLogDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.LOG_DATA_QUERY_PARAM_NOT_WITH_TIME, e.getMessage());
        }
        customerDataLogDTO.setStartDate(new Date(5000));
        customerDataLogDTO.setEndDate(new Date());
        try {
            service.queryLogData(customerDataLogDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PUSH_TIME_WITHIN_180_DAYS, e.getMessage());
        }
        customerDataLogDTO.setStartDate(new Date());
        customerDataLogDTO.setCreateBy("00000000");
        customerDataLogDTO.setSn("12345");
        List<CustomerDataLogDTO> qryList = new ArrayList<>();
        qryList.add(customerDataLogDTO);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("张三");
        hrmPersonInfoDTOMap.put("00000000", null);
        PowerMockito.when(tradeDataLogRepository.pageList(any())).thenReturn(qryList);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);
        service.queryLogData(customerDataLogDTO);

        hrmPersonInfoDTOMap.put("00000000", hrmPersonInfoDTO);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);
        service.queryLogData(customerDataLogDTO);


    }

}
