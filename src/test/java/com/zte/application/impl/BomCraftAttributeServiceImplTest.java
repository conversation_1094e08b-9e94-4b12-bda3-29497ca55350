package com.zte.application.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteWorkbook;
import com.google.common.collect.Lists;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BBomHeader;
import com.zte.domain.model.BBomHeaderRepository;
import com.zte.domain.model.BomCraftAttribute;
import com.zte.domain.model.BomCraftAttributeRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.infrastructure.feign.DatawbFeignService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.assembler.BomCraftAttributeAssembler;
import com.zte.interfaces.dto.BomCraftAttributeDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.WorkOrderCraftAttributeDTO;
import com.zte.interfaces.dto.datawb.BoardStoveMaintenanceDTO;
import com.zte.interfaces.sncabind.vo.SpecifiedPsTaskVO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.ImesExcelUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisHelper.class, BomCraftAttributeAssembler.class,
        DatawbRemoteService.class, ImesExcelUtil.class, EasyExcelFactory.class})
public class BomCraftAttributeServiceImplTest {
    @InjectMocks
    private BomCraftAttributeServiceImpl service;
    @Mock
    private BomCraftAttributeRepository bomCraftAttributeRepository;
    @Mock
    private BBomHeaderRepository bBomHeaderRepository;
    @Mock
    private HrmUserCenterServiceImpl hrmUserCenterService;
    @Mock
    DatawbFeignService datawbFeignService;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private CloudDiskHelper cloudDiskHelper;
    @Mock
    EmailUtils emailUtils;

    @Test
    public void insertBomFurnaceTemp_Empty() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        BomCraftAttributeDTO record = new BomCraftAttributeDTO();
        record.setCraftSection("");
        try {
            service.insertBomFurnaceTemp(record);
        } catch (MesBusinessException e) {
            assertEquals(MessageId.PARAM_MISSING,e.getExMsgId());
        }
        record.setCraftSection("SMT");
        record.setLeadFlag("");
        try {
            service.insertBomFurnaceTemp(record);
        } catch (MesBusinessException e) {
            assertEquals(MessageId.PARAM_MISSING,e.getExMsgId());
        }
        record.setLeadFlag("0");
        record.setFurnaceTempName("");
        try {
            service.insertBomFurnaceTemp(record);
        } catch (MesBusinessException e) {
            assertEquals(MessageId.PARAM_MISSING,e.getExMsgId());
        }
        record.setFurnaceTempName("1");
        record.setSurfaceList(new ArrayList<>());
        try {
            service.insertBomFurnaceTemp(record);
        } catch (MesBusinessException e) {
            assertEquals(MessageId.PARAM_MISSING,e.getExMsgId());
        }
        record.setSurfaceList(Arrays.asList("A","B"));
        try {
            service.insertBomFurnaceTemp(record);
        } catch (MesBusinessException e) {
            assertEquals(MessageId.NO_BOM_INFORMATION_IS_FOUND,e.getExMsgId());
        }
    }

    @Test
    public void updateBomFurnaceTemp_Empty() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        BomCraftAttributeDTO record = new BomCraftAttributeDTO();
        record.setLeadFlag("");
        try {
            service.updateBomFurnaceTemp(record);
        } catch (MesBusinessException e) {
            assertEquals(MessageId.PARAM_MISSING,e.getExMsgId());
        }
        record.setLeadFlag("1");
        record.setFurnaceTempName("");
        try {
            service.updateBomFurnaceTemp(record);
        } catch (MesBusinessException e) {
            assertEquals(MessageId.PARAM_MISSING,e.getExMsgId());
        }
        record.setFurnaceTempName("1");
        record.setSurface("");
        try {
            service.updateBomFurnaceTemp(record);
        } catch (MesBusinessException e) {
            assertEquals(MessageId.PARAM_MISSING,e.getExMsgId());
        }
        record.setSurface("A");
        try {
            service.updateBomFurnaceTemp(record);
        } catch (MesBusinessException e) {
            assertEquals(MessageId.BOM_TEMPERATURE_IS_EXITED,e.getExMsgId());
        }
        record.setLeadFlag("0");
        try {
            service.updateBomFurnaceTemp(record);
        } catch (MesBusinessException e) {
            assertEquals(MessageId.BOM_TEMPERATURE_IS_EXITED,e.getExMsgId());
        }
    }

    @Test
    public void checkQueryParam_Empty() throws Exception {
        BomCraftAttributeDTO record = new BomCraftAttributeDTO();
        record.setBomNo(null);
        try {
        Whitebox.invokeMethod(service,"checkQueryParam",record);
        } catch (MesBusinessException e) {
            assertEquals(MessageId.QUERY_PARAMS_EMPTY_EXCEPT_CREATE_BY,e.getExMsgId());
        }
        BomCraftAttributeDTO record1 = new BomCraftAttributeDTO();
        record1.setBomNo("1");
        record1.setFurnaceTempName(null);
        try {
            Whitebox.invokeMethod(service,"checkQueryParam",record1);
        } catch (MesBusinessException e) {
            assertEquals(MessageId.QUERY_PARAMS_EMPTY_EXCEPT_CREATE_BY,e.getExMsgId());
        }
        BomCraftAttributeDTO record2 = new BomCraftAttributeDTO();
        record2.setBomNo("1");
        record2.setFurnaceTempName("1");
        record2.setUpdateStartDate(null);
        try {
            Whitebox.invokeMethod(service,"checkQueryParam",record2);
        } catch (MesBusinessException e) {
            assertEquals(MessageId.QUERY_PARAMS_EMPTY_EXCEPT_CREATE_BY,e.getExMsgId());
        }
        BomCraftAttributeDTO record3 = new BomCraftAttributeDTO();
        record3.setBomNo("1");
        record3.setFurnaceTempName("1");
        record3.setUpdateStartDate("1");
        record3.setUpdateEndDate(null);
        try {
            Whitebox.invokeMethod(service,"checkQueryParam",record3);
        } catch (MesBusinessException e) {
            assertEquals(MessageId.QUERY_PARAMS_EMPTY_EXCEPT_CREATE_BY,e.getExMsgId());
        }
        BomCraftAttributeDTO record4 = new BomCraftAttributeDTO();
        record4.setBomNo("");
        record4.setFurnaceTempName("");
        record4.setUpdateStartDate("");
        try {
            Whitebox.invokeMethod(service,"checkQueryParam",record4);
        } catch (MesBusinessException e) {
            assertEquals(MessageId.QUERY_PARAMS_EMPTY_EXCEPT_CREATE_BY,e.getExMsgId());
        }
        BomCraftAttributeDTO record5 = new BomCraftAttributeDTO();
        record5.setBomNo("1");
        record5.setFurnaceTempName("1");
        record5.setUpdateStartDate("1");
        record5.setUpdateEndDate("1");
        Whitebox.invokeMethod(service,"checkQueryParam",record5);
        Assert.assertTrue(true);
    }

    @Test
    public void getBomFurnaceTempPage() throws Exception {
        PowerMockito.mockStatic(BomCraftAttributeAssembler.class);
        List<BomCraftAttributeDTO> relDTOS = new ArrayList<>();
		BomCraftAttributeDTO record = new BomCraftAttributeDTO();
        record.setPage(1);
        record.setRows(10);
        record.setLastUpdatedBy("00286523");
        record.setCreateBy("00286523");
        record.setLeadFlag("0");
        relDTOS.add(record);
        try{
			service.getBomFurnaceTempPage(record);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.QUERY_PARAMS_EMPTY_EXCEPT_CREATE_BY);
		}
		record.setUpdateEndDate("2023-8-12 12:00:00");
		try{
			service.getBomFurnaceTempPage(record);
		}catch (Exception e){
			Assert.assertEquals(e.getMessage(), MessageId.QUERY_PARAMS_EMPTY_EXCEPT_CREATE_BY);
		}
		record.setUpdateStartDate("2023-8-10 12:00:00");
        BomCraftAttribute bomCraftAttribute = new BomCraftAttribute();
        bomCraftAttribute.setLastUpdatedBy("00286523");
        bomCraftAttribute.setCreateBy("00286523");
        bomCraftAttribute.setLeadFlag("0");
        bomCraftAttribute.setFactoryId(51);
        BomCraftAttribute bomCraftAttribute1 = new BomCraftAttribute();
        bomCraftAttribute1.setLastUpdatedBy("00286523");
        bomCraftAttribute1.setCreateBy("00286523");
        bomCraftAttribute1.setLeadFlag("0");
        List<BomCraftAttribute> bomCraftAttributes = new ArrayList<>();
        bomCraftAttributes.add(bomCraftAttribute);
        bomCraftAttributes.add(bomCraftAttribute1);
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setId("00286523");
        hrmPersonInfoDTO.setEmpName("hl");
        Map<String, HrmPersonInfoDTO> map = new HashMap<>();
        map.put("00286523", hrmPersonInfoDTO);
        PowerMockito.when(bomCraftAttributeRepository.getBomCraftAttributePage(Mockito.any())).thenReturn(bomCraftAttributes);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.any())).thenReturn(map);
        List<SysLookupValues> sysLookupValues = new ArrayList<>();
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupMeaning("51");
        sysLookupValues1.setDescriptionChin("中心工厂");
        SysLookupValues sysLookupValues2 = new SysLookupValues();
        sysLookupValues2.setLookupMeaning("51");
        sysLookupValues2.setDescriptionChin("中心工厂");
        sysLookupValues.add(sysLookupValues1);
        sysLookupValues.add(sysLookupValues2);
        PowerMockito.when(sysLookupValuesRepository.selectValuesByType(Mockito.any())).thenReturn(sysLookupValues);
        service.getBomFurnaceTempPage(record);
        Assert.assertTrue(true);
    }

    @Test
    public void getBomFurnaceTempList() {
        BomCraftAttribute bomCraftAttribute = new BomCraftAttribute();
        bomCraftAttribute.setLastUpdatedBy("00286523");
        bomCraftAttribute.setCreateBy("00286523");
        bomCraftAttribute.setLeadFlag("0");
        List<BomCraftAttribute> bomCraftAttributes = new ArrayList<>();
        bomCraftAttributes.add(bomCraftAttribute);
        PowerMockito.when(bomCraftAttributeRepository.getBomCraftAttributeList(Mockito.any())).thenReturn(bomCraftAttributes);
        BomCraftAttributeDTO record = new BomCraftAttributeDTO();
        Assert.assertNotNull(service.getBomFurnaceTempList(record));
    }

    @Test
    public void updateBomFurnaceTemp() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        BomCraftAttribute bomCraftAttribute = new BomCraftAttribute();
        bomCraftAttribute.setLastUpdatedBy("00286523");
        bomCraftAttribute.setCreateBy("00286523");
        bomCraftAttribute.setLeadFlag("0");
        List<BomCraftAttribute> bomCraftAttributes = new ArrayList<>();
        bomCraftAttributes.add(bomCraftAttribute);
        PowerMockito.when(bomCraftAttributeRepository.getBomCraftAttributeList(Mockito.any())).thenReturn(new ArrayList<>());
        PowerMockito.when(bomCraftAttributeRepository.updateBomCraftAttributeById(Mockito.any())).thenReturn(1);
        BomCraftAttributeDTO record = new BomCraftAttributeDTO();
        record.setLeadFlag("1");
        record.setFurnaceTempName("test");
        record.setSurface("A");
        service.updateBomFurnaceTemp(record);

        PowerMockito.when(bomCraftAttributeRepository.getBomCraftAttributeList(Mockito.any())).thenReturn(bomCraftAttributes);
        try {
            service.updateBomFurnaceTemp(record);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        try {
            service.updateBomFurnaceTemp(record);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        record.setFurnaceTempName(null);
        try {
            service.updateBomFurnaceTemp(record);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        try {
            service.updateBomFurnaceTemp(record);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    @Test
    public void insertBomFurnaceTemp() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        BomCraftAttribute bomCraftAttribute = new BomCraftAttribute();
        bomCraftAttribute.setLastUpdatedBy("00286523");
        bomCraftAttribute.setCreateBy("00286523");
        bomCraftAttribute.setLeadFlag("0");
        List<BomCraftAttribute> bomCraftAttributes = new ArrayList<>();
        List<BBomHeader> tempList = new ArrayList<>();
        BBomHeader bBomHeader = new BBomHeader();
        bBomHeader.setChiDesc("test");
        bBomHeader.setVerNo("1");
        tempList.add(bBomHeader);
        bomCraftAttributes.add(bomCraftAttribute);
        PowerMockito.when(bomCraftAttributeRepository.getBomCraftAttributeList(Mockito.any())).thenReturn(null);
        PowerMockito.when(bomCraftAttributeRepository.updateBomCraftAttributeById(Mockito.any())).thenReturn(0);
        PowerMockito.when(bBomHeaderRepository.selectBBomHeaderByProductCode(Mockito.any())).thenReturn(tempList);
        BomCraftAttributeDTO record = new BomCraftAttributeDTO();
        record.setLeadFlag("1");
        record.setFurnaceTempName("test");
        record.setSurface("A");
        record.setCraftSection("SMT");
        List<String> strings = new ArrayList<>();
        strings.add("A");
        record.setSurfaceList(strings);
        PowerMockito.when(bomCraftAttributeRepository.getBomCraftAttributeList(Mockito.any())).thenReturn(new ArrayList<>());
        service.insertBomFurnaceTemp(record);
        record.setCraftSection("DIP");
        service.insertBomFurnaceTemp(record);
        PowerMockito.when(bomCraftAttributeRepository.getBomCraftAttributeList(Mockito.any())).thenReturn(bomCraftAttributes);
        try {
            service.insertBomFurnaceTemp(record);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        record.setLeadFlag("0");
        try {
            service.insertBomFurnaceTemp(record);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        PowerMockito.when(bBomHeaderRepository.selectBBomHeaderByProductCode(Mockito.any())).thenReturn(null);
        try {
            service.insertBomFurnaceTemp(record);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        try {
            service.insertBomFurnaceTemp(record);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
        record.setFurnaceTempName(null);
        try {
            service.insertBomFurnaceTemp(record);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    @Test
    public void deleteFurnaceTempRel() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        PowerMockito.when(bomCraftAttributeRepository.deleteCraftAttribute(Mockito.any())).thenReturn(1);
        BomCraftAttributeDTO record = new BomCraftAttributeDTO();
        record.setBomNo("1");
        service.deleteFurnaceTempRel(record);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        try {
            service.deleteFurnaceTempRel(record);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }

        record.setBomNo(null);
        try {
            service.deleteFurnaceTempRel(record);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length() > 0);
        }
    }

    @Test
    public void pullFurnaceFromSpm() throws Exception {
        PowerMockito.mockStatic(DatawbRemoteService.class);
        List<SysLookupValues> list = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        a1.setLookupCode(new BigDecimal(1036));
        a1.setLookupType(new BigDecimal(1036));
        a1.setLookupMeaning("0");
        a1.setDescriptionChin("无铅");
        list.add(a1);
        SysLookupValues a2 = new SysLookupValues();
        a2.setLookupCode(new BigDecimal("28650003"));
        a2.setLookupType(new BigDecimal("28650003"));
        a2.setLookupMeaning("0,2");
        a2.setAttribute1("0");
        list.add(a2);
        try{
            PowerMockito.when(sysLookupValuesRepository.selectByTypeBatch(Mockito.anyList()))
                    .thenReturn(list);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }

        Page<BoardStoveMaintenanceDTO> pageResult = new Page<>();
        List<BoardStoveMaintenanceDTO> rows = new LinkedList<>();
        BoardStoveMaintenanceDTO b1 = new BoardStoveMaintenanceDTO();
        b1.setBomNo("yt");
        b1.setSmtStoveType("无铅");
        b1.setSmtAStove("D");
        b1.setSmtBStove("FF");
        rows.add(b1);
        pageResult.setRows(rows);
        PowerMockito.when(DatawbRemoteService.postBoardStoveMaintenanceSpm(1))
                .thenReturn(pageResult)
        ;

        List<SpecifiedPsTaskVO> taskVOS = new LinkedList<>();
        SpecifiedPsTaskVO c1 = new SpecifiedPsTaskVO();
        c1.setProductCode("yt");
        c1.setPcbVersion("g");
        taskVOS.add(c1);
        PowerMockito.when(bBomHeaderRepository.selectBBomHeaderByItemNoList(Mockito.anyList()))
                .thenReturn(taskVOS)
        ;
        service.pullFurnaceFromSpm();
    }

    @Test
    public void getCraftAttributeForWorkOrder() {
        service.getCraftAttributeForWorkOrder(null);
        service.getCraftAttributeForWorkOrder(Lists.newArrayList(new WorkOrderCraftAttributeDTO()));
        PowerMockito.when(sysLookupValuesRepository.selectValuesByType(Integer.parseInt(Constant.LOOK_UP_CODE_28650003)))
                .thenReturn(Lists.newArrayList(new SysLookupValues(){{setLookupMeaning("1,2");setAttribute1("1");}}));
        service.getCraftAttributeForWorkOrder(Lists.newArrayList(new WorkOrderCraftAttributeDTO(){{
            setCraftSection("SMT-A");
        }},new WorkOrderCraftAttributeDTO(){{
            setLeadFlagWorkOrder("3");
            setCraftSection("SMT-A");
        }},new WorkOrderCraftAttributeDTO(){{
            setLeadFlagWorkOrder("1");
            setCraftSection("SMT-C");
        }}));
        Assert.assertNotNull(service.getCraftAttributeForWorkOrder(Lists.newArrayList(new WorkOrderCraftAttributeDTO(){{
            setLeadFlagWorkOrder("1");
            setCraftSection("SMT-A");
        }},new WorkOrderCraftAttributeDTO(){{
            setLeadFlagWorkOrder("2");
            setCraftSection("SMT-B");
        }})));
        PowerMockito.when(bomCraftAttributeRepository.getCraftAttributeForWorkOrder(any()))
                .thenReturn(Lists.newArrayList(new WorkOrderCraftAttributeDTO()));
    }

    @Test
    public void getExportCount() {
		BomCraftAttributeDTO dto = new BomCraftAttributeDTO();
		dto.setBomNo("test");
        Assert.assertNotNull(service.getExportCount(dto));
    }

    @Test
    public void writeExcelData() throws Exception {
        service.writeExcelData(new BomCraftAttributeDTO(), new ExcelWriter(new WriteWorkbook()), 1, 1);
        PowerMockito.when(bomCraftAttributeRepository.getBomCraftAttributePage(any()))
                .thenReturn(Lists.newArrayList(new BomCraftAttribute()));
        service.writeExcelData(new BomCraftAttributeDTO(), new ExcelWriter(new WriteWorkbook()), 1, 1);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void export() throws Exception {
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
        PowerMockito.mockStatic(BomCraftAttributeAssembler.class);
        List<BomCraftAttributeDTO> relDTOS = new ArrayList<>();
        BomCraftAttributeDTO record = new BomCraftAttributeDTO();
        record.setPage(1);
        record.setRows(10);
        record.setLastUpdatedBy("00286523");
        record.setCreateBy("00286523");
        record.setLeadFlag("0");
        relDTOS.add(record);
        BomCraftAttribute bomCraftAttribute = new BomCraftAttribute();
        bomCraftAttribute.setLastUpdatedBy("00286523");
        bomCraftAttribute.setCreateBy("00286523");
        bomCraftAttribute.setLeadFlag("0");
        List<BomCraftAttribute> bomCraftAttributes = new ArrayList<>();
        bomCraftAttributes.add(bomCraftAttribute);
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setId("00286523");
        hrmPersonInfoDTO.setEmpName("hl");
        Map<String, HrmPersonInfoDTO> map = new HashMap<>();
        map.put("00286523", hrmPersonInfoDTO);
        try {
            PowerMockito.when(bomCraftAttributeRepository.getBomCraftAttributePage(Mockito.any())).thenReturn(Collections.emptyList());
            service.export(response, new BomCraftAttributeDTO());
        } catch (Exception e) {
            Assert.assertTrue(true);
        }
        try {
            PowerMockito.when(bomCraftAttributeRepository.getBomCraftAttributePage(Mockito.any())).thenReturn(bomCraftAttributes);
            service.export(response, new BomCraftAttributeDTO());
        } catch (Exception e) {
            Assert.assertTrue(true);
        }
    }

    @Test
    public void exportByEmail() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        try {
            service.exportByEmail(new BomCraftAttributeDTO(), "1");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_GET_REDIS_LOCK, e.getMessage());
        }
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        PowerMockito.when(cloudDiskHelper.fileUpload(anyString(), anyString(), anyInt())).thenReturn("1");
        PowerMockito.when(cloudDiskHelper.getFileDownloadUrl(anyString(), anyString(), anyString())).thenReturn("1");
        PowerMockito.when(emailUtils.sendMail(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(true);
        service.exportByEmail(new BomCraftAttributeDTO(), "1");
    }

    @Test
    public void getItemNoList() {
        String likeItemNo = "1122";
        List<String> itemNoList = new ArrayList<>();
        itemNoList.add(likeItemNo);
        PowerMockito.when(bomCraftAttributeRepository.getItemNoList(Mockito.any())).thenReturn(itemNoList);
        service.getItemNoList(likeItemNo);
        Assert.assertEquals("1122", likeItemNo);
    }

    @Test
    public void bomInfoSearchByItemNo() throws Exception {
        BomCraftAttributeDTO dto = new BomCraftAttributeDTO();
        List<BomCraftAttributeDTO> listTest = new ArrayList<>();
        dto.setBomNo("1212");
        dto.setPage(1);
        dto.setRows(10);
        listTest.add(dto);
        PowerMockito.when(bomCraftAttributeRepository.getBomInfoList(Mockito.any())).thenReturn(listTest);
        Page<BomCraftAttributeDTO> pageInfo = service.bomInfoSearchByItemNo(dto);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }
}
