package com.zte.application.impl;

import com.zte.domain.model.VMesBoxNoCopyRepository;
import com.zte.domain.model.VMesBoxNoHisRepository;
import com.zte.interfaces.dto.VMesBoxNoCopyDTO;
import com.zte.interfaces.dto.VMesBoxNoHisDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
@RunWith(PowerMockRunner.class)
public class VMesBoxNoHisServiceImplTest {
    @InjectMocks
    private VMesBoxNoHisServiceImpl vMesBoxNoHisService;
    @Mock
    private VMesBoxNoHisRepository vMesBoxNoHisRepository;
    @Test
    public void insertHisInfoBatch() {
        VMesBoxNoHisDTO vMesBoxNoHisDTO = new VMesBoxNoHisDTO();
        vMesBoxNoHisDTO.setExternalOrderkey2("bill1");
        vMesBoxNoHisDTO.setFormId("box1");
        List<VMesBoxNoHisDTO> listParam = new ArrayList<>();
        listParam.add(vMesBoxNoHisDTO);
        PowerMockito.when(vMesBoxNoHisRepository.insertHisInfoBatch(Mockito.any())).thenReturn(1);
        Assert.assertTrue(vMesBoxNoHisService.insertHisInfoBatch(listParam) >= 0);
    }

    @Test
    public void getHisInfoList() {
        VMesBoxNoHisDTO vMesBoxNoHisDTO = new VMesBoxNoHisDTO();
        vMesBoxNoHisDTO.setExternalOrderkey2("bill1");
        vMesBoxNoHisDTO.setFormId("box1");
        List<VMesBoxNoHisDTO> listParam = new ArrayList<>();
        listParam.add(vMesBoxNoHisDTO);
        PowerMockito.when(vMesBoxNoHisRepository.getHisInfoList(Mockito.any())).thenReturn(listParam);
        Assert.assertTrue(vMesBoxNoHisService.getHisInfoList(vMesBoxNoHisDTO).size() >= 0);
    }
}