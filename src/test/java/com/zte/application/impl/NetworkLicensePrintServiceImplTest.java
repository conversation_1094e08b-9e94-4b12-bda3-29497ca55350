package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.AsynCompensationCenterService;
import com.zte.application.PrintRecordService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.CommonUtils;
import com.zte.common.authority.HttpConstant;
import com.zte.common.model.MessageId;
import com.zte.domain.model.ResourceInfoDetailRepository;
import com.zte.domain.model.ResourceInfoRepository;
import com.zte.domain.model.ResourceOptLogRepository;
import com.zte.domain.model.ResourceUseInfoRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.MESHttpHelper;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;

/**
 * <AUTHOR>
 * @description
 * @date 2023/9/19 10:34
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BeanUtils.class, MESHttpHelper.class, UUID.class, HttpClientUtil.class, MESHttpHelper.class, CommonUtils.class, RedisHelper.class, HttpClientUtil.class, CommonUtils.class})
public class NetworkLicensePrintServiceImplTest {

    @InjectMocks
    private NetworkLicensePrintServiceImpl networkLicensePrintService;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Mock
    private PrintRecordService printRecordService;
    @Mock
    private ResourceUseInfoRepository resourceUseInfoRepository;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ResourceOptLogRepository resourceOptLogRepository;
    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private IdGenerator idGenerator;
    @Mock
    private ResourceInfoRepository resourceInfoRepository;
    @Mock
    private ResourceInfoDetailRepository resourceInfoDetailRepository;
    @Mock
    private AsynCompensationCenterService asynCompensationCenterService;


    @Test
    public void getTemplateNameList() throws Exception {
        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.getTemplateNameList());
        SysLookupValues lookupValue = new SysLookupValues();
        lookupValue.setLookupMeaning("1212");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(lookupValue);
        networkLicensePrintService.getTemplateNameList();
    }

    @Test
    public void printBlank() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class, MESHttpHelper.class);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        header.put(HttpConstant.X_AUTH_VALUE, "token");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);

        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(JSON.toJSONString("msg"));

        NetworkLicensePrintDTO printDTO = new NetworkLicensePrintDTO();
        printDTO.setPrintType("0");
        printDTO.setPrintNum(2);
        printDTO.setResourceNoList(Arrays.asList("11-1111-111111", "22-2222-222222"));

        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.printBlank(printDTO));
        printDTO.setDeviceType("1");
        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.printBlank(printDTO));
        printDTO.setPrintCount(11);
        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.printBlank(printDTO));
        printDTO.setPrintCount(1);
        List<SysLookupValues> sysLookupValueList = new ArrayList<>();
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupMeaning("0");
        sysLookupValues1.setAttribute1("printer 300DPL");
        sysLookupValueList.add(sysLookupValues1);

        PowerMockito.when(sysLookupValuesService.selectValuesByType(any())).thenReturn(sysLookupValueList);
        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.printBlank(printDTO));

        SysLookupValues lookupValue = new SysLookupValues();
        lookupValue.setLookupMeaning("212");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(lookupValue);

        networkLicensePrintService.printBlank(printDTO);
    }

    @Test
    public void print() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class, MESHttpHelper.class);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        header.put(HttpConstant.X_AUTH_VALUE, "token");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);

        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(JSON.toJSONString("msg"));

        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);

        NetworkLicensePrintDTO printDTO = new NetworkLicensePrintDTO();
        printDTO.setPrintType("0");
        printDTO.setPrintNum(2);
        printDTO.setResourceNoList(Arrays.asList("11-1111-111111", "22-2222-222222"));
        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.print(printDTO));

        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);

        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.print(printDTO));

        List<ResourceInfoEntityDTO> resourceInfoList = new ArrayList<>();
        ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
        ResourceInfoEntityDTO resourceInfoEntityDTO1 = new ResourceInfoEntityDTO();
        resourceInfoEntityDTO.setResourceNo("11-1111-111111");
        resourceInfoEntityDTO1.setResourceNo("22-2222-222222");
        resourceInfoList.add(resourceInfoEntityDTO);
        resourceInfoList.add(resourceInfoEntityDTO1);
        PowerMockito.when(resourceInfoRepository.getListByResource(any())).thenReturn(resourceInfoList);

        List<ResourceInfoDetailDTO> resourceInfoDetailDTOS = new ArrayList<>();
        ResourceInfoDetailDTO resourceInfoDetailDTO = new ResourceInfoDetailDTO();
        resourceInfoDetailDTO.setResourceNo("11-1111-111111");
        resourceInfoDetailDTOS.add(resourceInfoDetailDTO);
        PowerMockito.when(resourceInfoDetailRepository.queryAvailableByResourceNo(any())).thenReturn(resourceInfoDetailDTOS);
        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.print(printDTO));

        ResourceInfoDetailDTO resourceInfoDetailDTO1 = new ResourceInfoDetailDTO();
        resourceInfoDetailDTO1.setResourceNo("22-2222-222222");
        resourceInfoDetailDTOS.add(resourceInfoDetailDTO1);
        PowerMockito.when(resourceInfoDetailRepository.queryAvailableByResourceNo(any())).thenReturn(resourceInfoDetailDTOS);

        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.print(printDTO));

        List<SysLookupValues> sysLookupValueList = new ArrayList<>();
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupMeaning("0");
        sysLookupValues1.setAttribute1("printer 300DPL");
        SysLookupValues sysLookupValues2 = new SysLookupValues();
        sysLookupValues2.setLookupMeaning("1");
        sysLookupValues2.setAttribute1("printer 300DPL");
        sysLookupValueList.add(sysLookupValues2);

        PowerMockito.when(sysLookupValuesService.selectValuesByType(any())).thenReturn(sysLookupValueList);
        PowerMockito.when(resourceInfoDetailRepository.batchAllocation(any())).thenReturn(1);

        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.print(printDTO));
        sysLookupValueList.add(sysLookupValues1);
        PowerMockito.when(sysLookupValuesService.selectValuesByType(any())).thenReturn(sysLookupValueList);
        try {
            networkLicensePrintService.print(printDTO);
        } catch (MesBusinessException e) {
            assertEquals(MessageId.THE_MODEL_CODE_OF_THE_RESOURCE_NUMBER_IS_EMPTY, e.getExMsgId());
        }
    }

    @Test
    public void rePrint() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class, MESHttpHelper.class);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        header.put(HttpConstant.X_AUTH_VALUE, "token");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);

        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(JSON.toJSONString("msg"));

        NetworkLicensePrintDTO printDTO = new NetworkLicensePrintDTO();
        printDTO.setPrintType("0");
        printDTO.setPrintNum(2);
        printDTO.setTemplateName("1212");

        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.rePrint(printDTO));
        printDTO.setResourceNumList(Arrays.asList("111", "222"));

        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.rePrint(printDTO));
        printDTO.setPrintCount(11);
        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.rePrint(printDTO));

        printDTO.setPrintCount(3);
        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.rePrint(printDTO));

        List<SysLookupValues> sysLookupValueList = new ArrayList<>();
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupMeaning("0");
        sysLookupValues1.setAttribute1("printer 300DPL");
        sysLookupValueList.add(sysLookupValues1);

        PowerMockito.when(sysLookupValuesService.selectValuesByType(any())).thenReturn(sysLookupValueList);
        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.rePrint(printDTO));


        List<ResourceOptLogDTO> opts = new ArrayList<>();
        ResourceOptLogDTO resourceOptLogDTO = new ResourceOptLogDTO();
        resourceOptLogDTO.setResourceNum("111");
        NetworkLicenseSubStrDTO networkLicenseSubStrDTO = new NetworkLicenseSubStrDTO();
        networkLicenseSubStrDTO.setTemplateName(printDTO.getTemplateName());

        String subStr = JSON.toJSONString(networkLicenseSubStrDTO);
        resourceOptLogDTO.setRemark(subStr);
        opts.add(resourceOptLogDTO);
        PowerMockito.when(resourceOptLogRepository.getListByResourceNum(any())).thenReturn(opts);
        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.rePrint(printDTO));
        ResourceOptLogDTO resourceOptLogDTO1 = new ResourceOptLogDTO();
        resourceOptLogDTO1.setResourceNum("222");
        resourceOptLogDTO1.setRemark(subStr);
        opts.add(resourceOptLogDTO1);
        PowerMockito.when(resourceOptLogRepository.getListByResourceNum(any())).thenReturn(opts);
        networkLicensePrintService.rePrint(printDTO);
    }

    @Test
    public void rePrint1() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class, MESHttpHelper.class);
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        header.put(HttpConstant.X_AUTH_VALUE, "token");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);

        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(JSON.toJSONString("msg"));

        NetworkLicensePrintDTO printDTO = new NetworkLicensePrintDTO();
        List<String> numList = new ArrayList<>();
        numList.add("111");
        printDTO.setResourceNumList(numList);
        printDTO.setSnList(Arrays.asList("111", "222"));
        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.rePrint(printDTO));
        for (int i = 0; i < 1001; i++) {
            numList.add(i + "a");
        }
        printDTO.setResourceNumList(null);
        printDTO.setSnList(numList);
        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.rePrint(printDTO));
        PowerMockito.when(resourceUseInfoRepository.queryNumByBarcodeList(any())).thenReturn(numList);
        printDTO.setSnList(Arrays.asList("111", "222"));
        Assert.assertThrows(MesBusinessException.class, () -> networkLicensePrintService.rePrint(printDTO));
    }
    /*Started by AICoder, pid:vf90b76936re26a14a85097620e10d8590e01522*/
    @Test(timeout = 8000)
    public void testSetNumber_NullResourceInfoEntityDTO() {
        Map<String, ResourceInfoEntityDTO> resourceMap = new HashMap<>();
        ResourceInfoDetailDTO detail = new ResourceInfoDetailDTO();
        detail.setResourceNo("123");
        NetworkLicenseSubStrDTO networkLicenseSubStrDTO = new NetworkLicenseSubStrDTO();

        networkLicensePrintService.setNumber(resourceMap, detail, networkLicenseSubStrDTO);

        assertNull(networkLicenseSubStrDTO.getCertName());
        assertNull(networkLicenseSubStrDTO.getDeviceType());
        assertNull(networkLicenseSubStrDTO.getNumber());
    }

    @Test(timeout = 8000)
    public void testSetNumber_EmptyModelNumber() {
        Map<String, ResourceInfoEntityDTO> resourceMap = new HashMap<>();
        ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
        resourceInfoEntityDTO.setCertName("certName");
        resourceInfoEntityDTO.setDeviceType("deviceType");
        resourceMap.put("123", resourceInfoEntityDTO);

        ResourceInfoDetailDTO detail = new ResourceInfoDetailDTO();
        detail.setResourceNo("123");
        detail.setScramblingCode("scramblingCode");
        NetworkLicenseSubStrDTO networkLicenseSubStrDTO = new NetworkLicenseSubStrDTO();

        try {
            networkLicensePrintService.setNumber(resourceMap, detail, networkLicenseSubStrDTO);
            fail("Expected MesBusinessException");
        } catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
            assertEquals(MessageId.THE_MODEL_CODE_OF_THE_RESOURCE_NUMBER_IS_EMPTY, e.getExMsgId());
        }
    }

    @Test(timeout = 8000)
    public void testSetNumber_ValidData() {
        Map<String, ResourceInfoEntityDTO> resourceMap = new HashMap<>();
        ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
        resourceInfoEntityDTO.setCertName("certName");
        resourceInfoEntityDTO.setDeviceType("deviceType");
        resourceInfoEntityDTO.setModelNumber("modelNumber");
        resourceMap.put("123", resourceInfoEntityDTO);

        ResourceInfoDetailDTO detail = new ResourceInfoDetailDTO();
        detail.setResourceNo("123");
        detail.setScramblingCode("scramblingCode");
        NetworkLicenseSubStrDTO networkLicenseSubStrDTO = new NetworkLicenseSubStrDTO();

        networkLicensePrintService.setNumber(resourceMap, detail, networkLicenseSubStrDTO);

        assertEquals("certName", networkLicenseSubStrDTO.getCertName());
        assertEquals("deviceType", networkLicenseSubStrDTO.getDeviceType());
        assertEquals("modelNumberscramblingCode", networkLicenseSubStrDTO.getNumber());
    }
    /*Ended by AICoder, pid:vf90b76936re26a14a85097620e10d8590e01522*/

}
