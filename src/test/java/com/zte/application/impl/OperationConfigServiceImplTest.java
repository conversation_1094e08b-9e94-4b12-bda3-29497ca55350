package com.zte.application.impl;

import com.zte.common.utils.Constant;
import com.zte.domain.model.OperationConfig;
import com.zte.domain.model.OperationConfigRepository;
import com.zte.interfaces.dto.OperationConfigPageQueryDTO;
import com.zte.itp.msa.core.model.PageRows;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/3/14 19:11
 */
public class OperationConfigServiceImplTest {
    @Mock
    OperationConfigRepository operationConfigRepository;
    @InjectMocks
    OperationConfigServiceImpl operationConfigServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testQueryPage() throws Exception {
        when(operationConfigRepository.selectPage(any())).thenReturn(Arrays.<OperationConfig>asList(new OperationConfig()));

        PageRows<OperationConfig> result = operationConfigServiceImpl.queryPage(new OperationConfigPageQueryDTO());
        Assert.assertTrue(result.getRows().size() >= 0);
    }

    @Test
    public void testGetById() throws Exception {
        when(operationConfigRepository.selectById(anyString())).thenReturn(new OperationConfig());

        Assert.assertNotNull(operationConfigServiceImpl.getById("id"));
    }

    @Test
    public void testAdd() throws Exception {
        when(operationConfigRepository.insert(any())).thenReturn(0L);

        operationConfigServiceImpl.add(new OperationConfig());
        OperationConfig operationConfig = new OperationConfig();
        Assert.assertNotNull(operationConfig);
    }

    @Test
    public void testUpdateById() throws Exception {
        when(operationConfigRepository.updateById(any())).thenReturn(0L);

        operationConfigServiceImpl.updateById(new OperationConfig());
        OperationConfig operationConfig = new OperationConfig();
        Assert.assertNotNull(operationConfig);
    }

    @Test
    public void testDeleteByIds() throws Exception {
        when(operationConfigRepository.deleteByIds(any())).thenReturn(0L);

        operationConfigServiceImpl.deleteByIds(Arrays.<String>asList("String"));
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }
}