/*Started by AICoder, pid:6a874v673739312148f80abf90c4bf5f3d785425*/
package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.application.PushStdModelSnDataService;
import com.zte.common.enums.BusinessSceneEnum;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.infrastructure.feign.ApsInOneClient;
import com.zte.interfaces.dto.ApsCustInstructBomDTO;
import com.zte.interfaces.dto.ApsCustomerTaskDTO;
import com.zte.interfaces.dto.FinishedProductStorageCargoTransportDTO;
import com.zte.interfaces.dto.FinishedProductStorageDTO;
import com.zte.interfaces.dto.FinishedProductStorageItemDTO;
import com.zte.interfaces.dto.PushStdModelSnDataExtDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.IdGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class FinishedProductStorageDataHandleServiceImpl_98_Test {

    @InjectMocks
    private FinishedProductStorageDataHandleServiceImpl service;

    @Mock
    private PushStdModelSnDataExtDTO pushStdModelSnDataExt;

    @Mock
    private ApsInOneClient apsInOneClient;
    @Mock
    private IdGenerator idGenerator;

    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    PushStdModelSnDataService pushStdModelSnDataService;


    /*Started by AICoder, pid:oe3a2xebfdl0ee9144e4089e70b216768fc91071*/
    @Test
    public void testMatch_True() {
        // Given
        String currProcess = "someProcess";

        // When
        boolean result = service.match(currProcess);

        // Then
        assertTrue(result);
    }

    @Test
    public void testMatch_False() {
        // Given
        String currProcess = "differentProcess";

        // When
        boolean result = service.match(currProcess);

        // Then
        assertFalse(result);
    }

    @Before
    public void setUp() throws Exception{
        // Initialize mocks if needed
            // Initialize any necessary setup here if needed
        ReflectionTestUtils.setField(service, "finishedProductStorageCurrProcess", "someProcess");
        PowerMockito.field(FinishedProductStorageDataHandleServiceImpl.class, "needCustomerComponentTypeList").set(service,
                Arrays.asList("server.ConfigModel"));
    }
    /*Ended by AICoder, pid:oe3a2xebfdl0ee9144e4089e70b216768fc91071*/


    /*Started by AICoder, pid:e7583fff4d7538e1444f083ab294830de8e863ec*/
    @Test
    public void testWrapPushMessageDataWithEmptySubStock() {
        PowerMockito.when(pushStdModelSnDataService.getFinishedProductStorageCargoTransportDTO(anyList(),anyList(),any(),any())).thenReturn(new FinishedProductStorageCargoTransportDTO());
        // Given
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        pushStdModelSnDataExt.setTaskNo("taskNo");
        pushStdModelSnDataExt.setStockOrgId(123);
        pushStdModelSnDataExt.setSn("sn");
        pushStdModelSnDataExt.setTaskBillNo("taskBillNo");
        pushStdModelSnDataExt.setTaskCustomerPartType("taskCustomerPartType");
        pushStdModelSnDataExt.setTaskCustomerItemName("taskCustomerItemName");
        pushStdModelSnDataExt.setStockName("");

        List<WipExtendIdentificationDTO> wipExtendIdentifications = new ArrayList<>();
        List<FixBomDetailDTO> fixBomDetails = new ArrayList<>();

        ApsCustomerTaskDTO apsCustomerTask = new ApsCustomerTaskDTO();
        ApsCustInstructBomDTO apsCustInstructBom = new ApsCustInstructBomDTO();
        apsCustInstructBom.setMaterialSign("materialSign");
        apsCustInstructBom.setMaterialBillType("NEW_INSTRUCT_BOM");
        apsCustomerTask.setBomList(Lists.newArrayList(apsCustInstructBom));

        Page<ApsCustomerTaskDTO> page = mock(Page.class);
        when(page.getRows()).thenReturn(Lists.newArrayList(apsCustomerTask));
        when(apsInOneClient.customerTaskQueryWithDetail(any())).thenReturn(page);

        FixBomDetailDTO fixBomDetail = new FixBomDetailDTO();
        fixBomDetail.setCustomerComponentType("customerComponentType");
        fixBomDetail.setItemSupplierNo("itemSupplierNo");
        fixBomDetails.add(fixBomDetail);

        // When
        FinishedProductStorageDTO result = service.wrapPushMessageData(pushStdModelSnDataExt, wipExtendIdentifications, fixBomDetails);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getMaterialQuality());

        verify(apsInOneClient, times(1)).customerTaskQueryWithDetail(any());
    }

    @Test
    public void testWrapPushMessageDataWithNonEmptySubStock() {
        PowerMockito.when(pushStdModelSnDataService.getFinishedProductStorageCargoTransportDTO(anyList(),anyList(),any(),any())).thenReturn(new FinishedProductStorageCargoTransportDTO());
        // Given
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        pushStdModelSnDataExt.setTaskNo("taskNo");
        pushStdModelSnDataExt.setStockOrgId(123);
        pushStdModelSnDataExt.setSn("sn");
        pushStdModelSnDataExt.setTaskBillNo("taskBillNo");
        pushStdModelSnDataExt.setTaskCustomerPartType("taskCustomerPartType");
        pushStdModelSnDataExt.setTaskCustomerItemName("taskCustomerItemName");
        pushStdModelSnDataExt.setStockName("subStock");

        List<WipExtendIdentificationDTO> wipExtendIdentifications = new ArrayList<>();
        List<FixBomDetailDTO> fixBomDetails = new ArrayList<>();

        ApsCustomerTaskDTO apsCustomerTask = new ApsCustomerTaskDTO();
        apsCustomerTask.setBusinessScene(BusinessSceneEnum.BUFFER.getCode());
        ApsCustInstructBomDTO apsCustInstructBom = new ApsCustInstructBomDTO();
        apsCustInstructBom.setMaterialSign("materialSign");
        apsCustomerTask.setBomList(Lists.newArrayList(apsCustInstructBom));

        Page<ApsCustomerTaskDTO> page = mock(Page.class);
        when(page.getRows()).thenReturn(Lists.newArrayList(apsCustomerTask));
        when(apsInOneClient.customerTaskQueryWithDetail(any())).thenReturn(page);

        FixBomDetailDTO fixBomDetail = new FixBomDetailDTO();
        fixBomDetail.setCustomerComponentType("customerComponentType");
        fixBomDetail.setItemSupplierNo("itemSupplierNo");
        fixBomDetails.add(fixBomDetail);

        List<SysLookupValues> stockConfigs = new ArrayList<>();
        SysLookupValues stockConfig = new SysLookupValues();
        stockConfig.setAttribute2("subStock,gtt");
        stockConfig.setLookupCode(new BigDecimal(Constant.LOOKUP_CODE_BAD_WAREHOUSE));
        stockConfig.setRemark(Constant.STR_DEFECTIVE_PRODUCT_WAREHOUSE);
        stockConfigs.add(stockConfig);

        when(sysLookupValuesRepository.selectValuesByType(anyInt())).thenReturn(stockConfigs);

        // When
        FinishedProductStorageDTO result = service.wrapPushMessageData(pushStdModelSnDataExt, wipExtendIdentifications, fixBomDetails);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getMaterialQuality());

        verify(apsInOneClient, times(1)).customerTaskQueryWithDetail(any());
    }

    @Test
    public void testWrapPushMessageData() {
        PowerMockito.when(pushStdModelSnDataService.getFinishedProductStorageCargoTransportDTO(anyList(),anyList(),any(),any())).thenReturn(new FinishedProductStorageCargoTransportDTO());
        // Given
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        pushStdModelSnDataExt.setTaskNo("taskNo");
        pushStdModelSnDataExt.setStockOrgId(123);
        pushStdModelSnDataExt.setSn("sn");
        pushStdModelSnDataExt.setTaskFixBomId("taskFixBomId");
        pushStdModelSnDataExt.setTaskBillNo("taskBillNo");
        pushStdModelSnDataExt.setTaskCustomerPartType("taskCustomerPartType");
        pushStdModelSnDataExt.setTaskCustomerItemName("taskCustomerItemName");

        List<WipExtendIdentificationDTO> wipExtendIdentifications = new ArrayList<>();
        List<FixBomDetailDTO> fixBomDetails = new ArrayList<>();

        ApsCustomerTaskDTO apsCustomerTask = new ApsCustomerTaskDTO();
        apsCustomerTask.setBusinessScene(BusinessSceneEnum.MANUFACTURE.getCode());
        ApsCustInstructBomDTO apsCustInstructBom = new ApsCustInstructBomDTO();
        apsCustInstructBom.setMaterialSign("materialSign");
        apsCustomerTask.setBomList(Lists.newArrayList(apsCustInstructBom));

        Page<ApsCustomerTaskDTO> page = mock(Page.class);
        when(page.getRows()).thenReturn(Lists.newArrayList(apsCustomerTask));
        when(apsInOneClient.customerTaskQueryWithDetail(any())).thenReturn(page);

        FixBomDetailDTO fixBomDetail = new FixBomDetailDTO();
        fixBomDetail.setCustomerComponentType("customerComponentType");
        fixBomDetail.setItemSupplierNo("itemSupplierNo");
        fixBomDetails.add(fixBomDetail);

        List<SysLookupValues> stockConfigs = new ArrayList<>();
        SysLookupValues stockConfig = new SysLookupValues();
        stockConfig.setAttribute2("subStock");
        stockConfig.setRemark("defectiveProductWarehouse");
        stockConfigs.add(stockConfig);

        when(sysLookupValuesRepository.selectValuesByType(anyInt())).thenReturn(stockConfigs);

        // When
        FinishedProductStorageDTO result = service.wrapPushMessageData(pushStdModelSnDataExt, wipExtendIdentifications, fixBomDetails);

        // Then
        assertNotNull(result);
        assertEquals("taskNo", result.getManufactureOrderNo());
        assertEquals("taskCustomerPartType", result.getCategory());
        assertEquals(1, result.getQuantityCompleted());

        List<FinishedProductStorageItemDTO> materialBillList = result.getMaterialBillList();
        assertNotNull(materialBillList);
        assertEquals(1, materialBillList.size());

        FinishedProductStorageItemDTO topSnItem = materialBillList.get(0);
        assertEquals(null, topSnItem.getSnNo());
        assertEquals(null, topSnItem.getMaterialCategory());

        verify(apsInOneClient, times(1)).customerTaskQueryWithDetail(any());
    }
    /*Ended by AICoder, pid:e7583fff4d7538e1444f083ab294830de8e863ec*/
}
/*Ended by AICoder, pid:6a874v673739312148f80abf90c4bf5f3d785425*/