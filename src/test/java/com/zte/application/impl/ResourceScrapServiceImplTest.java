package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.domain.model.ResourceInfoDetailRepository;
import com.zte.domain.model.ResourceScrapRepository;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.ResourceScrapDTO;
import com.zte.itp.authorityclient.service.ResourceService;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.CommonRedisUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

@PrepareForTest({RedisHelper.class})
public class ResourceScrapServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ResourceScrapServiceImpl resourceScrapService;
    @Mock
    private ResourceScrapRepository resourceScrapRepository;
    @Mock
    private ResourceInfoDetailRepository resourceInfoDetailRepository;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> operations;
    @Mock
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Mock
    private CommonRedisUtil commonRedisUtil;
    @Mock
    private RedisLock redisLock;
    @Mock
    private HrmUserCenterServiceImpl hrmUserCenterService;
    @Mock
    private CloudDiskHelper cloudDiskHelper;
    private ResourceService resourceService;

    @Test
    public void testPageList() throws Exception {
        ResourceScrapDTO param = new ResourceScrapDTO();
        try {
            resourceScrapService.pageList(param);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.QUERY_RESOURCE_SCRAP_PARAMS_ERROR);
        }
        param.setResourceNo("88-99-0001");
        resourceScrapService.pageList(param);
        param.setResourceNo(null);
        param.setLastUpdatedStartDate(new Date());
        param.setLastUpdatedEndDate(new Date());
        resourceScrapService.pageList(param);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTOMap.put("1123", hrmPersonInfoDTO);
        hrmPersonInfoDTO.setEmpName("ZhangSan");
        param.setLastUpdatedBy("1123");
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);
        PowerMockito.when(resourceScrapRepository.pageList(any())).thenReturn(new ArrayList<ResourceScrapDTO>() {{
            add(param);
        }});
        resourceScrapService.pageList(param);
        param.setLastUpdatedStartDate(new Date(50000));
        try {
            resourceScrapService.pageList(param);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.DAY_OVER_ONE_YEAR);
        }
    }

    @Test
    public void testPageDetailList() throws Exception {
        ResourceScrapDTO param = new ResourceScrapDTO();
        Page<ResourceScrapDTO> page = new Page<>(param.getPage(), param.getRows());
        Assert.assertEquals(resourceScrapService.pageDetailList(param), page);
        param.setScrapId("123");
        resourceScrapService.pageDetailList(param);
    }

    @Test
    public void testDownloadScrapHis() throws Exception {
        ResourceScrapDTO param = new ResourceScrapDTO();
        Assert.assertNull(resourceScrapService.downloadScrapHis(param));
        List<String> resourceDetailsForScrap = new ArrayList<>();
        Assert.assertEquals(new ArrayList<>(), resourceDetailsForScrap);
        for (int i = 0; i <= 10000; i++) {
            resourceDetailsForScrap.add(String.valueOf(i));
        }
        PowerMockito.when(resourceScrapRepository.getResourceNumForDownload(any())).thenReturn(resourceDetailsForScrap);
        resourceScrapService.downloadScrapHis(param);
        PowerMockito.when(resourceScrapRepository.updateDocId(any())).thenThrow(new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.ERROR_MSG_SET));
        try {
            resourceScrapService.downloadScrapHis(param);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.ERROR_MSG_SET);
        }
        param.setDocId("12345");
        resourceScrapService.downloadScrapHis(param);
    }

    @Test
    public void testGetDetailListCount() {
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(operations);
        Assert.assertNull(resourceScrapService.checkIsScraping(new ResourceScrapDTO()));
    }

    @Test
    public void testResourceScrap() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        ResourceScrapDTO param = new ResourceScrapDTO();
        param.setScrapNumber(300001);
        try {
            resourceScrapService.resourceScrap(param);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.SCRAP_NUM_OVER_LIMIT);
        }
        param.setScrapNumber(1);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(operations);
        PowerMockito.when(resourceInfoDetailRepository.countByResourceNo(any())).thenReturn(0);
        try {
            resourceScrapService.resourceScrap(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SCRAP_NUM_BIGGER_THAN_DATA_NUM, e.getMessage());
        }
        PowerMockito.when(resourceInfoDetailRepository.countByResourceNo(any())).thenReturn(2);
        operations.set("123", "123");
        PowerMockito.when(redisTemplate.opsForValue().get(any())).thenReturn("123");
        try {
            resourceScrapService.resourceScrap(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CURRENT_RESOURCE_NUMBER_IS_OPERATION, e.getMessage());
        }
        PowerMockito.when(redisTemplate.opsForValue().get(any())).thenReturn(null);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(RedisHelper.setnx(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(false);
        try {
            resourceScrapService.resourceScrap(param);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CURRENT_RESOURCE_NUMBER_IS_OPERATION, e.getMessage());
        }
        PowerMockito.when(RedisHelper.setnx(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt())).thenReturn(true);
        resourceScrapService.resourceScrap(param);
        PowerMockito.when(resourceScrapRepository.batchInsertScrapHisDetails(any(), any())).thenReturn(5000);
        resourceScrapService.resourceScrap(param);
    }

    public void testScrapDataAndInsertRecord() {
        ResourceScrapDTO param = new ResourceScrapDTO();
        int actualCount = 0;
        resourceScrapService.scrapDataAndInsertRecord(param, actualCount);
        List<String> resourceDetailsForScrap = new ArrayList<>();
        Assert.assertEquals(new ArrayList<>(), resourceDetailsForScrap);
        for (int i = 0; i <= 10000; i++) {
            resourceDetailsForScrap.add(String.valueOf(i));
        }
        PowerMockito.when(resourceInfoDetailRepository.getResourceNumForScrap(any())).thenReturn(resourceDetailsForScrap);
        resourceScrapService.scrapDataAndInsertRecord(param, actualCount);
    }
}