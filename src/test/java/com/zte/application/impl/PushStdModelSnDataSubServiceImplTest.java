/*Started by AICoder, pid:83af012d0687e5014f0f099670b52e8039101116*/
package com.zte.application.impl;

import com.zte.application.TradeDataLogService;
import com.zte.domain.model.PushStdModelSnDataSub;
import com.zte.domain.model.PushStdModelSnDataSubRepository;
import com.zte.domain.model.TradeDataLogRepository;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.springbootframe.util.NoticeCenterUtils;
import com.zte.util.BaseTestCase;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class PushStdModelSnDataSubServiceImplTest extends BaseTestCase {

    @Mock
    private PushStdModelSnDataSubRepository pushStdModelSnDataSubRepository;

    @InjectMocks
    private PushStdModelSnDataSubServiceImpl pushStdModelSnDataSubService;

    private PushStdModelSnDataSub insertData;

    @Mock
    private TradeDataLogRepository tradeDataLogRepository;

    @Mock
    private TradeDataLogService tradeDataLogService;
    @Mock
    private NoticeCenterUtils noticeCenterUtils;

    @Before
    public void setUp() {
        insertData = new PushStdModelSnDataSub();
        insertData.setSn("testSn");
        insertData.setPriority(1);
    }

    @Test
    public void sendPushFailMsg(){
        pushStdModelSnDataSubService.sendPushFailMsg(insertData);
        Assert.assertTrue(Objects.nonNull(pushStdModelSnDataSubService));

        List<PushStdModelSnDataSub> result = new LinkedList<>();
        for (int i = 0; i < 10; i++) {
            PushStdModelSnDataSub a1 = new PushStdModelSnDataSub();
            String s = String.valueOf(i);
            a1.setId(s);
            a1.setSn(s);
            a1.setCreateDate(new Date());
            result.add(a1);
        }
        PushStdModelSnDataSub pushStdModelSnDataSub = new PushStdModelSnDataSub();
        PowerMockito.when(pushStdModelSnDataSubRepository.queryFailPushDataList(Mockito.argThat(p->StringUtils.isBlank(p.getId()))))
                .thenReturn(result);
        pushStdModelSnDataSubService.sendPushFailMsg(pushStdModelSnDataSub);
    }

    /*Started by AICoder, pid:sd328918b4c3add1467f0b157179c71482c134e0*/
    @Test
    public void testRetryPushFailData_NoResult() {
        when(pushStdModelSnDataSubRepository.queryPushDataList(any(PushStdModelSnDataSub.class)))
                .thenReturn(Collections.emptyList());

        pushStdModelSnDataSubService.retryPushFailData(insertData);

        verify(pushStdModelSnDataSubRepository, times(1)).queryPushDataList(any(PushStdModelSnDataSub.class));
        verify(tradeDataLogRepository, never()).selectPushFailList(anyList(), any(Date.class));
        verify(tradeDataLogService, never()).repush(anyList());
        verify(pushStdModelSnDataSubRepository, never()).updateDataList(anyList());
    }

    @Test
    public void testRetryPushFailData_WithResult() {
        List<PushStdModelSnDataSub> result = new ArrayList<>();
        PushStdModelSnDataSub firstNode = new PushStdModelSnDataSub();
        firstNode.setCreateDate(new Date());
        firstNode.setPushFailCount(1);
        firstNode.setSn("firstSn");
        firstNode.setId("firstSn");
        firstNode.setTradeDataLogId("logId1");

        PushStdModelSnDataSub lastNode = new PushStdModelSnDataSub();
        lastNode.setCreateDate(new Date());
        lastNode.setSn("lastSn");
        lastNode.setId("lastSn");
        lastNode.setPushFailCount(1);
        lastNode.setTradeDataLogId("logId2");

        result.add(firstNode);
        result.add(lastNode);
        insertData.setId(null);
        when(pushStdModelSnDataSubRepository.queryPushDataList(Mockito.argThat(p -> StringUtils.isBlank(p.getId()))))
                .thenReturn(result);

        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        CustomerDataLogDTO logDto = new CustomerDataLogDTO();
        logDto.setId("logId1");
        dataList.add(logDto);

        when(tradeDataLogRepository.selectPushFailList(anyList(), any(Date.class)))
                .thenReturn(dataList);

        pushStdModelSnDataSubService.retryPushFailData(insertData);

        insertData.setId(null);
        when(tradeDataLogRepository.selectPushFailList(anyList(), any(Date.class)))
                .thenReturn(new LinkedList<>());
        pushStdModelSnDataSubService.retryPushFailData(insertData);
        assertTrue(Objects.nonNull(pushStdModelSnDataSubService));
    }
    /*Ended by AICoder, pid:sd328918b4c3add1467f0b157179c71482c134e0*/


    /*Started by AICoder, pid:v03b78540ffa9dd143a60a7710bb9262aeb85ff2*/

    @Test
    public void testInsertIfNotExists_AlreadyExists() {
        // Given
        when(pushStdModelSnDataSubRepository.selectBySnPriority(anyString(), anyInt(),Mockito.any())).thenReturn(1);

        // When
        int result = pushStdModelSnDataSubService.insertIfNotExists(insertData);

        // Then
        assertEquals(0, result);
        verify(pushStdModelSnDataSubRepository, never()).insertDataList(any());
    }

    @Test
    public void testInsertIfNotExists_NotExists() {
        // Given
        when(pushStdModelSnDataSubRepository.selectBySnPriority(anyString(), anyInt(),Mockito.any())).thenReturn(0);

        // When
        int result = pushStdModelSnDataSubService.insertIfNotExists(insertData);

        // Then
        assertEquals(1, result);
        verify(pushStdModelSnDataSubRepository).insertDataList(Collections.singletonList(insertData));
    }
    /*Ended by AICoder, pid:v03b78540ffa9dd143a60a7710bb9262aeb85ff2*/

    @Test
    public void testInsertDataList_SingleBatch() {
        // Given
        List<PushStdModelSnDataSub> dataList = Arrays.asList(new PushStdModelSnDataSub(), new PushStdModelSnDataSub());
        doNothing().when(pushStdModelSnDataSubRepository).insertDataList(anyList());

        // When
        pushStdModelSnDataSubService.insertDataList(dataList);

        // Then
        verify(pushStdModelSnDataSubRepository, times(1)).insertDataList(anyList());
    }

    @Test
    public void testInsertDataList_MultipleBatches() {
        // Given
        List<PushStdModelSnDataSub> dataList = Arrays.asList(
            new PushStdModelSnDataSub(), new PushStdModelSnDataSub(),
            new PushStdModelSnDataSub(), new PushStdModelSnDataSub(),
            new PushStdModelSnDataSub()
        );
        doNothing().when(pushStdModelSnDataSubRepository).insertDataList(anyList());

        // When
        pushStdModelSnDataSubService.insertDataList(dataList);
        Assert.assertTrue(Objects.nonNull(pushStdModelSnDataSubRepository));
    }

    @Test
    public void testInsertDataList_EmptyList() {
        // Given
        List<PushStdModelSnDataSub> dataList = Arrays.asList();
        doNothing().when(pushStdModelSnDataSubRepository).insertDataList(anyList());

        // When
        pushStdModelSnDataSubService.insertDataList(dataList);

        // Then
        Assert.assertTrue(Objects.nonNull(pushStdModelSnDataSubRepository));
    }
}
/*Ended by AICoder, pid:83af012d0687e5014f0f099670b52e8039101116*/