/*Started by AICoder, pid:p24f6t3ed7qd9531412b0914b130b3248b345ecb*/
package com.zte.application.impl;
import com.google.common.collect.Lists;
import com.zte.common.enums.BusinessSceneEnum;
import com.zte.common.model.MessageId;
import com.zte.infrastructure.feign.ApsInOneClient;
import com.zte.interfaces.dto.ApsCustInstructBomDTO;
import com.zte.interfaces.dto.ApsCustomerTaskDTO;
import com.zte.interfaces.dto.PushStdModelSnDataExtDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class FinishedProductStorageDataHandleServiceImpl_getTopApsCustInstructBomMap_1_Test {

    @Mock
    private ApsInOneClient apsInOneClient;

    @InjectMocks
    private FinishedProductStorageDataHandleServiceImpl service;

    private PushStdModelSnDataExtDTO pushStdModelSnDataExt;

    @Before
    public void setUp() {
        pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        pushStdModelSnDataExt.setTaskNo("task123");
        pushStdModelSnDataExt.setStockOrgId(123);
    }

    @Test
    public void testGetTopApsCustInstructBomMap_TaskEntityClass_FG_DISAS_2() throws Exception {
        pushStdModelSnDataExt.setTaskEntityClass("FG_DISAS_2");
        assertNull(Whitebox.invokeMethod(service, "getTopApsCustInstructBomMap", pushStdModelSnDataExt));
    }

    @Test
    public void testGetTopApsCustInstructBomMap_TaskNotFound() {
        when(apsInOneClient.customerTaskQueryWithDetail(any())).thenReturn(null);

        MesBusinessException exception = assertThrows(MesBusinessException.class, () -> {
            Whitebox.invokeMethod(service, "getTopApsCustInstructBomMap", pushStdModelSnDataExt);
        });

        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
        assertEquals(MessageId.TASK_NO_NOT_EXIST_APS, exception.getExMsgId());
        assertEquals("task123", exception.getParams()[0]);
    }

    @Test
    public void testGetTopApsCustInstructBomMap_EmptyTaskList() {
        when(apsInOneClient.customerTaskQueryWithDetail(any())).thenReturn(new Page<>());

        MesBusinessException exception = assertThrows(MesBusinessException.class,
                () -> Whitebox.invokeMethod(service, "getTopApsCustInstructBomMap", pushStdModelSnDataExt));

        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
        assertEquals(MessageId.TASK_NO_NOT_EXIST_APS, exception.getExMsgId());
        assertEquals("task123", exception.getParams()[0]);
    }

    @Test
    public void testGetTopApsCustInstructBomMap_BomListEmpty() {
        ApsCustomerTaskDTO apsCustomerTask = new ApsCustomerTaskDTO();
        apsCustomerTask.setBomList(Collections.emptyList());

        Page<ApsCustomerTaskDTO> page = new Page<>();
        page.setRows(Lists.newArrayList(apsCustomerTask));
        when(apsInOneClient.customerTaskQueryWithDetail(any())).thenReturn(page);

        MesBusinessException exception = assertThrows(MesBusinessException.class,
                () -> Whitebox.invokeMethod(service, "getTopApsCustInstructBomMap", pushStdModelSnDataExt));

        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
        assertEquals(MessageId.TASK_NO_NOT_EXIST_APS, exception.getExMsgId());
        assertEquals("task123", exception.getParams()[0]);
    }

    @Test
    public void testGetTopApsCustInstructBomMap_TaskManufactureException() {
        ApsCustInstructBomDTO bom = new ApsCustInstructBomDTO();
        bom.setMaterialBillType("OLD_FIX_BOM");
        ApsCustomerTaskDTO apsCustomerTask = new ApsCustomerTaskDTO();
        apsCustomerTask.setBusinessScene(BusinessSceneEnum.MANUFACTURE.getCode());
        apsCustomerTask.setBomList(Lists.newArrayList(bom));

        Page<ApsCustomerTaskDTO> page = new Page<>();
        page.setRows(Lists.newArrayList(apsCustomerTask));
        when(apsInOneClient.customerTaskQueryWithDetail(any())).thenReturn(page);

        assertThrows(MesBusinessException.class,
                () -> Whitebox.invokeMethod(service, "getTopApsCustInstructBomMap", pushStdModelSnDataExt));
    }

    @Test
    public void testGetTopApsCustInstructBomMap_Success() {
        ApsCustInstructBomDTO bom = new ApsCustInstructBomDTO();
        ApsCustomerTaskDTO apsCustomerTask = new ApsCustomerTaskDTO();
        apsCustomerTask.setBomList(Lists.newArrayList(bom));

        Page<ApsCustomerTaskDTO> page = new Page<>();
        page.setRows(Lists.newArrayList(apsCustomerTask));
        when(apsInOneClient.customerTaskQueryWithDetail(any())).thenReturn(page);

        assertThrows(MesBusinessException.class, () -> Whitebox.invokeMethod(service, "getTopApsCustInstructBomMap", pushStdModelSnDataExt));
    }
}
/*Ended by AICoder, pid:p24f6t3ed7qd9531412b0914b130b3248b345ecb*/