/*Started by AICoder, pid:j48087c693f701314ead0a208259e74d54f3938e*/
package com.zte.application.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.BProdBomDetailService;
import com.zte.application.IMESLogService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.sncabind.PsTaskService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BProdBomChangeDetailRepository;
import com.zte.domain.model.BProdBomHeaderRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.interfaces.dto.BProdBomDetailDTO;
import com.zte.interfaces.dto.ErpWriteBackDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class, BProdBomErpWriteBackServiceImpl.class, HttpClientUtil.class,
        JacksonJsonConverUtil.class, DatawbRemoteService.class})
public class BProdBomErpWriteBackServiceImplTest extends BaseTestCase {

    @InjectMocks
    private BProdBomErpWriteBackServiceImpl bProdBomErpWriteBackService;

    @Mock
    private BProdBomHeaderRepository bProdBomHeaderRepository;

    @Mock
    private BProdBomChangeDetailRepository bProdBomChangeDetailRepository;

    @Mock
    private BProdBomDetailService bProdBomDetailService;

    @Mock
    private PsTaskService psTaskService;

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Mock
    private EmailUtils emailUtils;

    @Mock
    private ObjectMapper mapperInstance;

    @Mock
    private JsonNode json;

    @Mock
    private RedisLock redisLock;
    @Mock
    private IMESLogService iMESLogService;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(DatawbRemoteService.class);
    }

    @Test
    public void testWriteBackErp() throws Exception {

        PowerMockito.when(DatawbRemoteService.getSubmitStatusBatch(Mockito.any()))
                .thenReturn(Collections.singletonList("1234567"));
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);
        Assert.assertThrows(MesBusinessException.class, () -> bProdBomErpWriteBackService.writeBackErpToBProdBom());


        Assert.assertTrue(true);

        PowerMockito.when(redisLock.lock()).thenReturn(true);
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(null);
        bProdBomErpWriteBackService.writeBackErpToBProdBom();

        ReflectionTestUtils.setField(bProdBomErpWriteBackService,"bProdBomBrpWriteBackAheadData",30);
        SysLookupValues result1 = new SysLookupValues();
        result1.setLookupCode(new BigDecimal(Constant.LOOK_UP_TYPE_8888001));
        result1.setAttribute1("1");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(result1);
        bProdBomErpWriteBackService.writeBackErpToBProdBom();

        result1.setLookupMeaning("url");
        PowerMockito.when(bProdBomHeaderRepository.queryBProdBomErpProdPlanIdList(any(),any())).thenReturn(null);
        bProdBomErpWriteBackService.writeBackErpToBProdBom();

        result1.setLookupMeaning("10");
        List<String> prodPlanIdList = new ArrayList<>();
        prodPlanIdList.add("1234567");
        prodPlanIdList.add("7654321");
        PowerMockito.when(bProdBomHeaderRepository.queryBProdBomErpProdPlanIdList(any(),any())).thenReturn(prodPlanIdList);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("1234567");
        psTask.setOrgId(new BigDecimal("635"));
        psTask.setTaskNo("12321");
        psTaskList.add(psTask);
        PsTask psTask2 = new PsTask();
        psTask2.setProdplanId("7654321");
        psTask2.setOrgId(new BigDecimal("635"));
        psTask2.setTaskNo("7654321");
        psTaskList.add(psTask2);
        PowerMockito.when(psTaskService.selectProdPlanIDBySpare(any(), any())).thenReturn(Collections.emptyList());
        bProdBomErpWriteBackService.writeBackErpToBProdBom();
        PowerMockito.when(psTaskService.selectProdPlanIDBySpare(any(), any())).thenReturn(psTaskList);
        bProdBomErpWriteBackService.writeBackErpToBProdBom();
        ReflectionTestUtils.setField(bProdBomErpWriteBackService,"bProdBomBrpWriteBackAlarmEmail","email");
        ReflectionTestUtils.setField(bProdBomErpWriteBackService,"bProdBomBrpWriteBackFailedNumber",10);
        List<BProdBomChangeDetailDTO> bProdBomChangeDetailDTOS = new ArrayList<>();
        PowerMockito.when(bProdBomChangeDetailRepository.queryBProdBomDetailChangeByProdplan(any())).thenReturn(bProdBomChangeDetailDTOS);
        bProdBomErpWriteBackService.writeBackErpToBProdBom();

        BProdBomChangeDetailDTO bProdBomChangeDetailDTO = new BProdBomChangeDetailDTO();
        bProdBomChangeDetailDTO.setProdplanId("1234567");
        bProdBomChangeDetailDTO.setItemCode("itemCode");
        bProdBomChangeDetailDTO.setOriginalItemCode("originalItemCode");
        BProdBomChangeDetailDTO bProdBomChangeDetailDTO2 = new BProdBomChangeDetailDTO();
        bProdBomChangeDetailDTO2.setProdplanId("1234567");
        bProdBomChangeDetailDTO2.setItemCode("itemCode2");
        bProdBomChangeDetailDTO2.setOriginalItemCode("originalItemCode2");
        bProdBomChangeDetailDTOS.add(bProdBomChangeDetailDTO);
        bProdBomChangeDetailDTOS.add(bProdBomChangeDetailDTO2);

        List<BProdBomDetailDTO> bProdBomDetailItemList = new ArrayList<>();
        BProdBomDetailDTO bProdBomDetailDTO = new BProdBomDetailDTO();
        bProdBomDetailDTO.setProdplanId("1234567");
        bProdBomDetailDTO.setItemCode("itemCode");
        bProdBomDetailDTO.setUsageCount(new BigDecimal("5"));
        bProdBomDetailItemList.add(bProdBomDetailDTO);
        BProdBomDetailDTO bProdBomDetailDTO2 = new BProdBomDetailDTO();
        bProdBomDetailDTO2.setProdplanId("1234567");
        bProdBomDetailDTO2.setItemCode("originalItemCode");
        bProdBomDetailDTO2.setUsageCount(new BigDecimal("5"));
        bProdBomDetailItemList.add(bProdBomDetailDTO2);
        PowerMockito.when(bProdBomDetailService.getBProdBomDetailItemList(any())).thenReturn(bProdBomDetailItemList);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn("");
        bProdBomErpWriteBackService.writeBackErpToBProdBom();

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn("{}");
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(null);
        bProdBomErpWriteBackService.writeBackErpToBProdBom();

        PowerMockito.when(emailUtils.sendMail(anyString(),anyString(),anyString(),anyString(),anyString())).thenReturn(false);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.asText()).thenReturn("0001");
        PowerMockito.when(bProdBomHeaderRepository.updateErpWriteBackFiledNulber(any())).thenReturn(9);
        bProdBomErpWriteBackService.writeBackErpToBProdBom();

        PowerMockito.when(bProdBomHeaderRepository.updateErpWriteBackFiledNulber(any())).thenReturn(10);
        bProdBomErpWriteBackService.writeBackErpToBProdBom();

        PowerMockito.when(json.asText()).thenReturn("0000");
        PowerMockito.when(json.toString()).thenReturn("{\"processStatus\":\"E\",\"processMessage\":\"error\"}");
        PowerMockito.when(bProdBomHeaderRepository.updateErpWriteBackFiledNulber(any())).thenReturn(9);
        bProdBomErpWriteBackService.writeBackErpToBProdBom();

        PowerMockito.when(bProdBomHeaderRepository.updateErpWriteBackFiledNulber(any())).thenReturn(10);
        bProdBomErpWriteBackService.writeBackErpToBProdBom();

        PowerMockito.when(json.toString()).thenReturn("{\"processStatus\":\"S\",\"processMessage\":null}");
        bProdBomErpWriteBackService.writeBackErpToBProdBom();

        Assert.assertTrue(true);
    }

    @Test
    public void callBackForErp() throws Exception {
        Map<String, ErpWriteBackDTO> param = new HashMap<>();
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(null);
        Assert.assertThrows(MesBusinessException.class, () -> Whitebox.invokeMethod(bProdBomErpWriteBackService, "callBackForErp", param));

        SysLookupValues result1 = new SysLookupValues();
        result1.setLookupCode(new BigDecimal(Constant.LOOK_UP_TYPE_8888001));
        result1.setAttribute1("1");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(result1);
        Assert.assertThrows(MesBusinessException.class, () -> Whitebox.invokeMethod(bProdBomErpWriteBackService, "callBackForErp", param));

        result1.setLookupMeaning("url");
        Assert.assertThrows(MesBusinessException.class, () -> Whitebox.invokeMethod(bProdBomErpWriteBackService, "callBackForErp", param));

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn("");
        Assert.assertThrows(MesBusinessException.class, () -> Whitebox.invokeMethod(bProdBomErpWriteBackService, "callBackForErp", param));

        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn("{}");
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(null);
        Assert.assertThrows(MesBusinessException.class, () -> Whitebox.invokeMethod(bProdBomErpWriteBackService, "callBackForErp", param));
        PowerMockito.when(json.get(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.asText()).thenReturn("0001");
        Assert.assertThrows(MesBusinessException.class, () -> Whitebox.invokeMethod(bProdBomErpWriteBackService, "callBackForErp", param));
    }
}
/*Ended by AICoder, pid:j48087c693f701314ead0a208259e74d54f3938e*/