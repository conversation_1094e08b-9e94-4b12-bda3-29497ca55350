package com.zte.application.impl;

import com.zte.application.BBomDetailService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.domain.model.BBomDetail;
import com.zte.domain.model.BBomDetailRepository;
import com.zte.domain.model.BProdBomChangeDetailRepository;
import com.zte.domain.model.BProdBomDetail;
import com.zte.domain.model.BProdBomDetailRepository;
import com.zte.domain.model.BaBomHead;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.BsItemInfoRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.IscpRemoteService;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.interfaces.dto.BProdBomDetailDTO;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.CivControlInfoDTO;
import com.zte.interfaces.dto.QueryBomDetailIncludeMBomConditionDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.AsyncExportFileCommonService;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class, HttpRemoteUtil.class, ServiceDataBuilderUtil.class, AsyncExportFileCommonService.class,
        DatawbRemoteService.class, MESHttpHelper.class})
public class BProdBomDetailServiceImplTest {

    @InjectMocks
    private BProdBomDetailServiceImpl bProdBomDetailService;
    @Mock
    private BProdBomDetailRepository bProdBomDetailRepository;
    @Mock
    private BProdBomChangeDetailRepository bProdBomChangeDetailRepository;
    @Mock
    private IscpRemoteService iscpRemoteService;
    @Mock
    private BBomDetailService bBomDetailService;
    @Mock
    private BsItemInfoRepository bsItemInfoRepository;
    @Mock
    private BBomDetailRepository bBomDetailRepository;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(DatawbRemoteService.class);
    }

    @Test
    public void testGetMBomDetailListWithValidParams() throws Exception {
        List<BProdBomDetailDTO> result = bProdBomDetailService.getMBomDetailList(null);
        assertEquals(Collections.emptyList(), result);
        BProdBomHeaderDTO validBProdBomHeaderDTO = new BProdBomHeaderDTO();
        result = bProdBomDetailService.getMBomDetailList(validBProdBomHeaderDTO);
        assertEquals(Collections.emptyList(), result);


        List<BProdBomDetailDTO> expectedList = Collections.singletonList(new BProdBomDetailDTO());
        PowerMockito.when(bProdBomDetailRepository.getMBomDetailList(any(), any(), any()))
                .thenReturn(expectedList);
        validBProdBomHeaderDTO.setProdplanId("123");
        result = bProdBomDetailService.getMBomDetailList(validBProdBomHeaderDTO);
        assertEquals(expectedList, result);
        validBProdBomHeaderDTO.setProdplanId("");
        validBProdBomHeaderDTO.setProductCode("ABC");
        result = bProdBomDetailService.getMBomDetailList(validBProdBomHeaderDTO);
        assertEquals(expectedList, result);
        validBProdBomHeaderDTO.setProductCode("");
        validBProdBomHeaderDTO.setOriginalProductCode("XYZ");
        result = bProdBomDetailService.getMBomDetailList(validBProdBomHeaderDTO);
        assertEquals(expectedList, result);
        validBProdBomHeaderDTO.setProdplanId("123");
        result = bProdBomDetailService.getMBomDetailList(validBProdBomHeaderDTO);
        assertEquals(expectedList, result);
        validBProdBomHeaderDTO.setProductCode("XYZ");
        validBProdBomHeaderDTO.setOriginalProductCode("");
        result = bProdBomDetailService.getMBomDetailList(validBProdBomHeaderDTO);
        assertEquals(expectedList, result);
        validBProdBomHeaderDTO.setProdplanId("");
        validBProdBomHeaderDTO.setOriginalProductCode("XYZ");
        result = bProdBomDetailService.getMBomDetailList(validBProdBomHeaderDTO);
        assertEquals(expectedList, result);
        validBProdBomHeaderDTO.setProdplanId("123");
        result = bProdBomDetailService.getMBomDetailList(validBProdBomHeaderDTO);
        assertEquals(expectedList, result);
    }


    /* Started by AICoder, pid:df66b52578gb8721496e098860473a1a58b7fdbd */
    @Test
    public void testSelectMBomDetailByHeaderId() throws Exception {
        BProdBomDetailDTO params = new BProdBomDetailDTO();
        params.setBomHeaderId("1212Q5436QREGTQRW324512346");
        params.setItemCode("013040100001");
        List<BProdBomDetailDTO> bProdBomChangeDetailDTOs = new ArrayList<>();
        bProdBomChangeDetailDTOs.add(params);
        PowerMockito.when(bProdBomDetailRepository.selectDetailByIdAndItemCode(any())).thenReturn(bProdBomChangeDetailDTOs);
        Page<BProdBomDetailDTO> bProdBomChangeDetailDTOPage = bProdBomDetailService.selectMBomDetailByHeaderId(params);
        assertEquals(bProdBomChangeDetailDTOPage.getRows().get(0).getItemCode(), "013040100001");
    }

    /* Ended by AICoder, pid:df66b52578gb8721496e098860473a1a58b7fdbd */

    /* Started by AICoder, pid:d3d41j99ddcbfa51480609c980b0b729c909efd0 */
    @Test
    public void queryBomDetailIncludeMBomConditionTest() {
        QueryBomDetailIncludeMBomConditionDTO dto = new QueryBomDetailIncludeMBomConditionDTO();
        dto.setFlagOfMBom(false);
        Page<BBomDetail> bBomDetailPage = bProdBomDetailService.queryBomDetailIncludeMBomCondition(dto);
        Assert.assertTrue(bBomDetailPage != null);
        dto.setFlagOfMBom(true);
        List<BProdBomDetailDTO> bProdBomDetailList = new ArrayList<>();
        bProdBomDetailList.add(new BProdBomDetailDTO());
        PowerMockito.when(bProdBomDetailRepository.getMBomDetailListByHeaderId(Mockito.anyString(), Mockito.anyString(), Mockito.anyLong(),
                Mockito.anyLong(), Mockito.anyBoolean())).thenReturn(bProdBomDetailList);
        Page<BBomDetail> bBomDetailPage1 = bProdBomDetailService.queryBomDetailIncludeMBomCondition(dto);
        Assert.assertTrue(bBomDetailPage1 != null);
    }

    @Test
    public void getBomDetailIncludeMBomConditionTest() {
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dto.setProductCode("test");
        dto.setProdplanId("test");
        dto.setOriginalProductCode("test");
        List<BBomDetailDTO> bomDetailIncludeMBomCondition = bProdBomDetailService.getBomDetailIncludeMBomCondition(dto);
        Assert.assertTrue(bomDetailIncludeMBomCondition != null);
        List<BProdBomDetailDTO> mBomDetailList = new ArrayList<>();
        mBomDetailList.add(new BProdBomDetailDTO());
        PowerMockito.when(bProdBomDetailRepository.getMBomDetailList(Mockito.anyString(), any(), Mockito.anyString())).thenReturn(mBomDetailList);
        List<BBomDetailDTO> bomDetailIncludeMBomCondition1 = bProdBomDetailService.getBomDetailIncludeMBomCondition(dto);
        Assert.assertTrue(bomDetailIncludeMBomCondition1.size() != 0);
    }

    @Test
    public void getBProdBomDetailItemList() {
        List<BProdBomDetailDTO> mBomDetailList = new ArrayList<>();
        mBomDetailList.add(new BProdBomDetailDTO(){{
            setProductCode("code");
        }});
        PowerMockito.when(bProdBomDetailRepository.getBProdBomDetailItemList(Mockito.any())).thenReturn(mBomDetailList);
        List<BProdBomDetailDTO> bomDetailIncludeMBomCondition1 = bProdBomDetailService.getBProdBomDetailItemList(new BProdBomDetailDTO());
        Assert.assertTrue(bomDetailIncludeMBomCondition1.size() != 0);
    }

    /* Started by AICoder, pid:d2335312acz6c851411a0928402f97352bf44fc7 */
    @Test
    public void testDeleteByHeaderIds() {
        // 测试删除空列表的情况
        int count = bProdBomDetailService.deleteByHeaderIds(Collections.emptyList());
        assertEquals(0, count);

        // 模拟删除操作返回1
        when(bProdBomDetailRepository.deleteByHeaderIds(any())).thenReturn(1);
        count = bProdBomDetailService.deleteByHeaderIds(Arrays.asList("2"));
        assertEquals(1, count);
    }

    /* Ended by AICoder, pid:d2335312acz6c851411a0928402f97352bf44fc7 */

    /* Started by AICoder, pid:k2c9f5b84bhc7d91410a0ba7d04ba2364f942e64 */
    @Test
    public void testBatchInsert() {
        // 测试删除空列表的情况
        List<BProdBomDetail> emptyList = Collections.emptyList();
        bProdBomDetailService.batchInsert(emptyList);
        assertEquals(0, emptyList.size());

        // 测试插入单个元素的情况
        BProdBomDetail detail = new BProdBomDetail();
        List<BProdBomDetail> singleItemList = Arrays.asList(detail);
        bProdBomDetailService.batchInsert(singleItemList);
        assertEquals(1, singleItemList.size());
    }

    @Test
    public void testByCondition() throws Exception {
        List<BProdBomHeaderDTO> bProdBomHeaderDTOs = new ArrayList<>();
        assertTrue(bProdBomDetailService.getMBomDetailListByCondition(bProdBomHeaderDTOs).isEmpty());
        BProdBomHeaderDTO dto1 = new BProdBomHeaderDTO();
        dto1.setProductCode("test");
        dto1.setProdplanId("test");
        BProdBomHeaderDTO dto2 = new BProdBomHeaderDTO();
        dto2.setProductCode("");
        dto2.setProdplanId("");
        bProdBomHeaderDTOs.add(dto1);
        bProdBomHeaderDTOs.add(dto2);
        when(bProdBomDetailRepository.getMBomDetailListByCondition(any())).thenReturn(new ArrayList<>());
        assertTrue(bProdBomDetailService.getMBomDetailListByCondition(bProdBomHeaderDTOs).isEmpty());
    }

    @Test
    public void testGetMbomOrBomDetailList() throws Exception {
        BProdBomDetailDTO bProdBomDetailDTO = new BProdBomDetailDTO();
        Page<BProdBomDetailDTO> mbomOrBomDetailList = bProdBomDetailService.getMbomOrBomDetailList(bProdBomDetailDTO);
        assertEquals(mbomOrBomDetailList.getRows(), null);

        bProdBomDetailDTO.setIsMbom("Y");
        bProdBomDetailDTO.setProductCode("product1");
        when(bProdBomDetailRepository.queryBProdBomDetailItemList(any())).thenReturn(new ArrayList<>());
        mbomOrBomDetailList = bProdBomDetailService.getMbomOrBomDetailList(bProdBomDetailDTO);
        assertEquals(mbomOrBomDetailList.getRows(), null);

        bProdBomDetailDTO.setIsMbom("N");
        Page<BProdBomDetailDTO> bomDetailDTOPage = new Page<>();
        when(bBomDetailService.selectDetailByProductCodeListAndItemCode(any())).thenReturn(bomDetailDTOPage);
        mbomOrBomDetailList = bProdBomDetailService.getMbomOrBomDetailList(bProdBomDetailDTO);
        assertEquals(mbomOrBomDetailList.getRows(), null);
        List<BProdBomDetailDTO> bomDetailDTOList = new ArrayList<>();
        BProdBomDetailDTO bomDetailDTO = new BProdBomDetailDTO();
        bomDetailDTOList.add(bomDetailDTO);
        bomDetailDTOPage.setRows(bomDetailDTOList);
        mbomOrBomDetailList = bProdBomDetailService.getMbomOrBomDetailList(bProdBomDetailDTO);
        assertEquals(mbomOrBomDetailList.getRows().size(), 1);
    }

    @Test
    public void selectMBOMDetailByByProductCodeListAndItemCode() throws Exception {

        BProdBomDetailDTO bProdBomDetailDTO = new BProdBomDetailDTO();

        Whitebox.invokeMethod(bProdBomDetailService,
                "selectMBOMDetailByByProductCodeListAndItemCode", bProdBomDetailDTO);

        bProdBomDetailDTO.setIsMbom("Y");
        bProdBomDetailDTO.setProductCode("product1");
        List<BProdBomDetailDTO> bProdBomDetailDTOS = new ArrayList<>();
        when(bProdBomDetailRepository.queryBProdBomDetailItemList(any())).thenReturn(bProdBomDetailDTOS);
        Whitebox.invokeMethod(bProdBomDetailService,
                "selectMBOMDetailByByProductCodeListAndItemCode", bProdBomDetailDTO);
        BProdBomDetailDTO bProdBomDetailDTO1 = new BProdBomDetailDTO();
        bProdBomDetailDTOS.add(bProdBomDetailDTO1);
        List<BProdBomChangeDetailDTO> bProdBomChangeDetails = new ArrayList<>();
        when(bProdBomChangeDetailRepository.selectMBomDetailChangeByProductCodesAndItem(any())).thenReturn(bProdBomChangeDetails);
        when(bBomDetailService.selectDetailByProductCodeListAndItemCode(any())).thenReturn(new Page<>());
        when(bsItemInfoRepository.selectBsItemInfoByNos(any())).thenReturn(new ArrayList<>());
        List<BaBomHead> bbhList = new ArrayList<>();
        PowerMockito.when(DatawbRemoteService.getParamByItemId(any())).thenReturn(bbhList);

        Whitebox.invokeMethod(bProdBomDetailService,
                    "selectMBOMDetailByByProductCodeListAndItemCode", bProdBomDetailDTO);
        BProdBomChangeDetailDTO bProdBomChangeDetailDTO = new BProdBomChangeDetailDTO();
        bProdBomChangeDetailDTO.setItemCode("itemcode1");
        bProdBomChangeDetailDTO.setOriginalItemCode("orgcode1");
        bProdBomChangeDetails.add(bProdBomChangeDetailDTO);
        BProdBomChangeDetailDTO bProdBomChangeDetailDTO1 = new BProdBomChangeDetailDTO();
        bProdBomChangeDetailDTO1.setItemCode("itemcode1");
        bProdBomChangeDetailDTO1.setOriginalItemCode("orgcode1");
        bProdBomChangeDetails.add(bProdBomChangeDetailDTO1);
        Whitebox.invokeMethod(bProdBomDetailService,
                "selectMBOMDetailByByProductCodeListAndItemCode", bProdBomDetailDTO);
        assertEquals("Y", bProdBomDetailDTO.getIsMbom());
    }

    @Test
    public void testpackageParamByItemId() throws Exception {
        List<BProdBomDetailDTO> bProdBomDetailDTOS = new ArrayList<>();
        BProdBomDetailDTO bProdBomDetailDTO = new BProdBomDetailDTO();
        bProdBomDetailDTO.setItemCode("item");
        bProdBomDetailDTOS.add(bProdBomDetailDTO);
        BProdBomDetailDTO bProdBomDetailDTO1 = new BProdBomDetailDTO();
        bProdBomDetailDTO1.setItemCode("item1");
        bProdBomDetailDTOS.add(bProdBomDetailDTO1);
        List<BsItemInfo> bsItemInfos = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("item1");
        bsItemInfo.setSourceItemId(new BigDecimal("1"));
        BsItemInfo bsItemInfo1 = new BsItemInfo();
        bsItemInfo1.setItemNo("item1");
        BsItemInfo bsItemInfo2 = new BsItemInfo();
        bsItemInfo2.setItemNo("item2");
        bsItemInfo2.setSourceItemId(new BigDecimal("2"));
        BsItemInfo bsItemInfo3 = new BsItemInfo();
        bsItemInfo3.setItemNo("item3");
        bsItemInfo3.setSourceItemId(new BigDecimal("3"));
        bsItemInfos.add(bsItemInfo);
        bsItemInfos.add(bsItemInfo1);
        bsItemInfos.add(bsItemInfo2);
        bsItemInfos.add(bsItemInfo3);
        List<BaBomHead> bbhList = new ArrayList<>();
        BaBomHead baBomHead = new BaBomHead();
        baBomHead.setItemId("1");
        bbhList.add(baBomHead);
        PowerMockito.when(DatawbRemoteService.getParamByItemId(any())).thenReturn(bbhList);
        Whitebox.invokeMethod(bProdBomDetailService,
                "packageParamByItemId", bProdBomDetailDTOS,bsItemInfos);
        assertEquals(2, bProdBomDetailDTOS.size());
    }


    @Test
    public void testselectBsItemInfoByNos() throws Exception {
        List<BProdBomDetailDTO> bProdBomDetailDTOS = new ArrayList<>();
        BProdBomDetailDTO bProdBomDetailDTO = new BProdBomDetailDTO();
        bProdBomDetailDTO.setItemCode("item");
        bProdBomDetailDTOS.add(bProdBomDetailDTO);
        BProdBomDetailDTO bProdBomDetailDTO1 = new BProdBomDetailDTO();
        bProdBomDetailDTO1.setItemCode("item1");
        bProdBomDetailDTOS.add(bProdBomDetailDTO1);
        BProdBomDetailDTO bProdBomDetailDTO2 = new BProdBomDetailDTO();
        bProdBomDetailDTO2.setItemCode("item2");
        bProdBomDetailDTOS.add(bProdBomDetailDTO2);
        List<BsItemInfo> bsItemInfoList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("item");
        bsItemInfoList.add(bsItemInfo);
        BsItemInfo bsItemInfo11 = new BsItemInfo();
        bsItemInfo11.setItemNo("item");
        bsItemInfoList.add(bsItemInfo11);
        BsItemInfo bsItemInfo1 = new BsItemInfo();
        bsItemInfo1.setItemNo("item1");
        bsItemInfo1.setIsSmt(new BigDecimal("1"));
        bsItemInfoList.add(bsItemInfo1);
        BsItemInfo bsItemInfo2 = new BsItemInfo();
        bsItemInfo2.setItemNo("item2");
        bsItemInfo2.setIsSmt(new BigDecimal("2"));
        bsItemInfoList.add(bsItemInfo2);
        when(bsItemInfoRepository.selectBsItemInfoByNos(any())).thenReturn(bsItemInfoList);
        Whitebox.invokeMethod(bProdBomDetailService,
                "selectBsItemInfoByNos", bProdBomDetailDTOS);
        assertEquals(3, bProdBomDetailDTOS.size());
    }

    @Test
    public void testpackageBomDetailByDevProductCode() throws Exception {
        List<BProdBomDetailDTO> bProdBomDetailDTOS = new ArrayList<>();
        BProdBomDetailDTO bProdBomDetailDTO = new BProdBomDetailDTO();
        bProdBomDetailDTO.setOriginalProductCode("orgcode");
        bProdBomDetailDTO.setItemCode("item");
        bProdBomDetailDTOS.add(bProdBomDetailDTO);
        BProdBomDetailDTO bProdBomDetailDTO1 = new BProdBomDetailDTO();
        bProdBomDetailDTO1.setItemCode("item1");
        bProdBomDetailDTOS.add(bProdBomDetailDTO1);
        BProdBomDetailDTO bProdBomDetailDTO2 = new BProdBomDetailDTO();
        bProdBomDetailDTO2.setItemCode("item2");
        bProdBomDetailDTOS.add(bProdBomDetailDTO2);
        Map<String, String> itemChangeMap = new HashMap<>();
        itemChangeMap.put("item","olditem");
        Page<BProdBomDetailDTO> bomDetailDTOPage = new Page<>();
        List<BProdBomDetailDTO> bBomDetailDTOList = new ArrayList<>();
        bomDetailDTOPage.setRows(bBomDetailDTOList);
        BProdBomDetailDTO prodBomDetailDTO = new BProdBomDetailDTO();
        prodBomDetailDTO.setProductCode("orgcode");
        prodBomDetailDTO.setItemCode("item");
        prodBomDetailDTO.setCodeDesc("无铅");
        prodBomDetailDTO.setPositionExt("ext");
        prodBomDetailDTO.setPositionExt0("ext");
        prodBomDetailDTO.setPlaceNoExt1("ext");
        prodBomDetailDTO.setPlaceNoExt2("ext");
        prodBomDetailDTO.setPlaceNoExt3("ext");
        prodBomDetailDTO.setPlaceNoExt4("ext");
        prodBomDetailDTO.setPlaceNoExt5("ext");
        prodBomDetailDTO.setPlaceNoExt6("ext");
        prodBomDetailDTO.setPlaceNoExt7("ext");
        bBomDetailDTOList.add(prodBomDetailDTO);
        BProdBomDetailDTO prodBomDetailDTO1 = new BProdBomDetailDTO();
        prodBomDetailDTO1.setProductCode("orgcode");
        prodBomDetailDTO1.setItemCode("olditem");
        prodBomDetailDTO1.setCodeDesc("无铅");
        prodBomDetailDTO1.setPositionExt("ext");
        prodBomDetailDTO1.setPositionExt0("ext");
        prodBomDetailDTO1.setPlaceNoExt1("ext");
        prodBomDetailDTO1.setPlaceNoExt2("ext");
        prodBomDetailDTO1.setPlaceNoExt3("ext");
        prodBomDetailDTO1.setPlaceNoExt4("ext");
        prodBomDetailDTO1.setPlaceNoExt5("ext");
        prodBomDetailDTO1.setPlaceNoExt6("ext");
        prodBomDetailDTO1.setPlaceNoExt7("ext");
        bBomDetailDTOList.add(prodBomDetailDTO1);
        BProdBomDetailDTO prodBomDetailDTO11 = new BProdBomDetailDTO();
        prodBomDetailDTO11.setProductCode("orgcode");
        prodBomDetailDTO11.setItemCode("item");
        bBomDetailDTOList.add(prodBomDetailDTO11);
        when(bBomDetailService.selectDetailByProductCodeListAndItemCode(any())).thenReturn(bomDetailDTOPage);
        Whitebox.invokeMethod(bProdBomDetailService,
                "packageBomDetailByDevProductCode", bProdBomDetailDTOS,itemChangeMap);
        assertEquals(3, bProdBomDetailDTOS.size());
    }



    @Test
    public void testgetCivControlInfo() throws Exception {
        List<BProdBomDetailDTO> bProdBomDetailDTOS = new ArrayList<>();
        BProdBomDetailDTO bProdBomDetailDTO = new BProdBomDetailDTO();
        bProdBomDetailDTO.setItemCode("item");
        bProdBomDetailDTOS.add(bProdBomDetailDTO);
        BProdBomDetailDTO bProdBomDetailDTO1 = new BProdBomDetailDTO();
        bProdBomDetailDTO1.setItemCode("item1");
        bProdBomDetailDTOS.add(bProdBomDetailDTO1);
        BProdBomDetailDTO bProdBomDetailDTO2 = new BProdBomDetailDTO();
        bProdBomDetailDTO2.setItemCode("item2");
        bProdBomDetailDTOS.add(bProdBomDetailDTO2);

        List<CivControlInfoDTO> infoDTOList = new ArrayList<>();
        when(iscpRemoteService.queryCivControlInfoList(any())).thenReturn(infoDTOList);
        Whitebox.invokeMethod(bProdBomDetailService,
                "getCivControlInfo", bProdBomDetailDTOS);

        CivControlInfoDTO civControlInfoDTO = new CivControlInfoDTO();
        civControlInfoDTO.setItemNo("item");
        civControlInfoDTO.setPropertyName("property");
        infoDTOList.add(civControlInfoDTO);
        CivControlInfoDTO civControlInfoDTO1 = new CivControlInfoDTO();
        civControlInfoDTO1.setItemNo("item");
        infoDTOList.add(civControlInfoDTO1);
        Whitebox.invokeMethod(bProdBomDetailService,
                "getCivControlInfo", bProdBomDetailDTOS);

        assertEquals(3, bProdBomDetailDTOS.size());
    }

    @Test
    public void getMBomDetailListBatch() {
        List<BProdBomHeaderDTO> bProdBomHeaderDTOs = new ArrayList<>();
        assertTrue(bProdBomDetailService.getMBomDetailListBatch(bProdBomHeaderDTOs).isEmpty());
        bProdBomHeaderDTOs.add(new BProdBomHeaderDTO(){{
            setProductCode("123");
        }});
        bProdBomHeaderDTOs.add(new BProdBomHeaderDTO(){{
            setOriginalProductCode("456");
        }});
        List<BProdBomDetailDTO> prodBomDetailDTOList1 = new ArrayList<>();
        when(bProdBomDetailRepository.getProdBomDetailListByProductCodeS(any())).thenReturn(prodBomDetailDTOList1);
        List<BProdBomDetailDTO> prodBomDetailDTOList2 = new ArrayList<>();
        when(bProdBomDetailRepository.getBomDetailListByProductCodeS(any())).thenReturn(prodBomDetailDTOList2);
        assertTrue(bProdBomDetailService.getMBomDetailListBatch(bProdBomHeaderDTOs).isEmpty());
        prodBomDetailDTOList1.add(new BProdBomDetailDTO(){{setProductCode("123");setItemCode("111");}});
        prodBomDetailDTOList2.add(new BProdBomDetailDTO(){{setProductCode("456");setItemCode("222");}});
        List<BProdBomDetailDTO> list = bProdBomDetailService.getMBomDetailListBatch(bProdBomHeaderDTOs);
        assertEquals(2,list.size());
    }
}



