/*Started by AICoder, pid:23ab9e133dka40114d2f09a141db1a7fec0379a1*/
package com.zte.application.impl.cbom;
import com.google.common.collect.Lists;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.PackingListConfigDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.interfaces.dto.mbom.GbomDetailDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.data.util.Pair;

import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class FixBomCommonServiceImpl_setPropertyByItemSupplierNoAndWrapBomRequiredBomDetails_8_Test {

    @InjectMocks
    private FixBomCommonServiceImpl fixBomCommonService;

    @Mock
    private Map<String, CustomerItemsDTO> customerMap;

    private Map<String, PackingListConfigDTO> packMap = new HashMap<>();

    private Map<String, GbomDetailDTO> pdmItemMap = new HashMap<>();

    @Mock
    private List<String> customerCooperationMode;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testSetPropertyByItemSupplierNoAndWrapBomRequiredBomDetails_ItemSupplierNoIsNull() throws Exception {
        FixBomDetailDTO fixBomDetail = new FixBomDetailDTO();
        fixBomDetail.setItemSupplierNo(null);

        List<FixBomDetailDTO> fixOrBoxBomRequiredFixBomDetails = Collections.emptyList();

        Whitebox.invokeMethod(fixBomCommonService, "setPropertyByItemSupplierNoAndWrapBomRequiredBomDetails", fixBomDetail, customerMap, packMap, pdmItemMap, Pair.of(fixOrBoxBomRequiredFixBomDetails,new HashSet()));

        assertNull(fixBomDetail.getZteCode());
        assertEquals(0, fixOrBoxBomRequiredFixBomDetails.size());
    }

    @Test
    public void testSetPropertyByItemSupplierNoAndWrapBomRequiredBomDetails_CustomerItemsDtoIsNull() throws Exception {
        FixBomDetailDTO fixBomDetail = new FixBomDetailDTO();
        fixBomDetail.setItemSupplierNo("123");

        when(customerMap.get(anyString())).thenReturn(null);

        List<FixBomDetailDTO> fixOrBoxBomRequiredFixBomDetails = Collections.emptyList();

        Whitebox.invokeMethod(fixBomCommonService, "setPropertyByItemSupplierNoAndWrapBomRequiredBomDetails", fixBomDetail, customerMap, packMap, pdmItemMap, Pair.of(fixOrBoxBomRequiredFixBomDetails,new HashSet()));

        assertNull(fixBomDetail.getZteCode());
        assertEquals(0, fixOrBoxBomRequiredFixBomDetails.size());
    }

    @Test
    public void testSetPropertyByItemSupplierNoAndWrapBomRequiredBomDetails_FixBomRequiredAndBoxBomRequiredAreN() throws Exception {
        FixBomDetailDTO fixBomDetail = new FixBomDetailDTO();
        fixBomDetail.setItemSupplierNo("123");
        fixBomDetail.setFixBomRequired("N");
        fixBomDetail.setBoxBomRequired("N");

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("zteCode");

        when(customerMap.get(anyString())).thenReturn(customerItemsDTO);

        List<FixBomDetailDTO> fixOrBoxBomRequiredFixBomDetails = Collections.emptyList();

        Whitebox.invokeMethod(fixBomCommonService, "setPropertyByItemSupplierNoAndWrapBomRequiredBomDetails", fixBomDetail, customerMap, packMap, pdmItemMap, Pair.of(fixOrBoxBomRequiredFixBomDetails,new HashSet()));

        assertEquals("zteCode", fixBomDetail.getZteCode());
        assertEquals(0, fixOrBoxBomRequiredFixBomDetails.size());
    }

    @Test
    public void testSetPropertyByItemSupplierNoAndWrapBomRequiredBomDetails_FixBomRequiredIsY() throws Exception {
        FixBomDetailDTO fixBomDetail = new FixBomDetailDTO();
        fixBomDetail.setItemSupplierNo("123");
        fixBomDetail.setFixBomRequired("Y");
        fixBomDetail.setBoxBomRequired("N");

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("zteCode");

        when(customerMap.get(anyString())).thenReturn(customerItemsDTO);

        List<FixBomDetailDTO> fixOrBoxBomRequiredFixBomDetails = new java.util.ArrayList<>();

        Whitebox.invokeMethod(fixBomCommonService, "setPropertyByItemSupplierNoAndWrapBomRequiredBomDetails", fixBomDetail, customerMap, packMap, pdmItemMap, Pair.of(fixOrBoxBomRequiredFixBomDetails,new HashSet()));

        assertEquals("zteCode", fixBomDetail.getZteCode());
        assertEquals(1, fixOrBoxBomRequiredFixBomDetails.size());
        assertEquals(fixBomDetail, fixOrBoxBomRequiredFixBomDetails.get(0));
    }

    @Test
    public void testSetPropertyByItemSupplierNoAndWrapBomRequiredBomDetails_BoxBomRequiredIsY() throws Exception {
        FixBomDetailDTO fixBomDetail = new FixBomDetailDTO();
        fixBomDetail.setItemSupplierNo("123");
        fixBomDetail.setFixBomRequired("N");
        fixBomDetail.setBoxBomRequired("Y");

        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setZteCode("zteCode");

        when(customerMap.get(anyString())).thenReturn(customerItemsDTO);

        List<FixBomDetailDTO> fixOrBoxBomRequiredFixBomDetails = new java.util.ArrayList<>();

        Whitebox.invokeMethod(fixBomCommonService, "setPropertyByItemSupplierNoAndWrapBomRequiredBomDetails", fixBomDetail, customerMap, packMap, pdmItemMap, Pair.of(fixOrBoxBomRequiredFixBomDetails,new HashSet()));

        assertEquals("zteCode", fixBomDetail.getZteCode());
        assertEquals(1, fixOrBoxBomRequiredFixBomDetails.size());
        assertEquals(fixBomDetail, fixOrBoxBomRequiredFixBomDetails.get(0));
    }

    @Test
    public void threePieceCode() throws Exception {
        Map<String, PackingListConfigDTO> packMap = new HashMap<>();
        String customerNo = "";
        CustomerItemsDTO item = new CustomerItemsDTO();
        item.setCustomerCodeList(Lists.newArrayList("2"));
        Map<String, FixBomDetailDTO> customerItemNameMbomItemMap= new HashMap<>();
        customerItemNameMbomItemMap.put("2",new FixBomDetailDTO());

        Whitebox.invokeMethod(fixBomCommonService, "threePieceCode", packMap, customerNo, item, customerItemNameMbomItemMap);

        Whitebox.invokeMethod(fixBomCommonService, "twoPieceCode", item, customerItemNameMbomItemMap);
        item.setCustomerCode("33");
        Whitebox.invokeMethod(fixBomCommonService, "twoPieceCode", item, customerItemNameMbomItemMap);

        Assert.assertNotNull(packMap);
    }
}
/*Ended by AICoder, pid:23ab9e133dka40114d2f09a141db1a7fec0379a1*/