/*Started by AICoder, pid:w71bcoe53dud211142a609ab919b2e9719853c17*/
package com.zte.application.impl.cbom;
import com.zte.common.utils.Constant;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
public class FixBomCommonServiceImpl_wrapParentFixOrBoxBomRequired_11_Test {

    @InjectMocks
    private FixBomCommonServiceImpl fixBomCommonService;

    @Mock
    private FixBomDetailDTO mockFixBomDetailDTO;

    private Map<String, FixBomDetailDTO> itemSeqFixBomDetailMap;

    @Before
    public void setUp() {
        itemSeqFixBomDetailMap = new HashMap<>();
    }

    @Test
    public void testWrapParentFixOrBoxBomRequired_TopLevel() throws Exception {
        // Given
        FixBomDetailDTO fixBomDetail = new FixBomDetailDTO();
        fixBomDetail.setItemSeq("1");
        fixBomDetail.setFixBomRequired("N");
        fixBomDetail.setBoxBomRequired("N");

        // When
        Whitebox.invokeMethod(fixBomCommonService, "wrapParentFixOrBoxBomRequired", itemSeqFixBomDetailMap, fixBomDetail);

        // Then
        assertTrue(itemSeqFixBomDetailMap.isEmpty());
    }

    @Test
    public void testWrapParentFixOrBoxBomRequired_ParentDoesNotExist() throws Exception {
        // Given
        String itemSeq = "1-1";
        String fixBomRequired = "Y";
        String boxBomRequired = "Y";

        // When
        Whitebox.invokeMethod(fixBomCommonService, "wrapParentFixOrBoxBomRequired", itemSeqFixBomDetailMap, itemSeq, fixBomRequired, boxBomRequired);

        // Then
        assertTrue(itemSeqFixBomDetailMap.isEmpty());
    }

    @Test
    public void testWrapParentFixOrBoxBomRequired_ParentExistsButNotComponents() throws Exception {
        // Given
        String itemSeq = "1-1";
        String parentItemSeq = "1";
        String fixBomRequired = "Y";
        String boxBomRequired = "Y";
        String parentItemType = "OTHER_TYPE";

        FixBomDetailDTO parentFixBomDetail = mock(FixBomDetailDTO.class);
        when(parentFixBomDetail.getItemType()).thenReturn(parentItemType);

        itemSeqFixBomDetailMap.put(parentItemSeq, parentFixBomDetail);

        // When
        Whitebox.invokeMethod(fixBomCommonService, "wrapParentFixOrBoxBomRequired", itemSeqFixBomDetailMap, itemSeq, fixBomRequired, boxBomRequired);

        // Then
        verify(parentFixBomDetail, never()).setFixBomRequired(anyString());
        verify(parentFixBomDetail, never()).setBoxBomRequired(anyString());
    }

    @Test
    public void testWrapParentFixOrBoxBomRequired_ParentExistsAndIsComponents_FixBomRequiredNotY() throws Exception {
        // Given
        String itemSeq = "1-1";
        String parentItemSeq = "1";
        String fixBomRequired = "Y";
        String boxBomRequired = "Y";
        String parentItemType = Constant.COMPONENTS_OF_THE_MANUFACTURER;

        FixBomDetailDTO parentFixBomDetail = mock(FixBomDetailDTO.class);
        when(parentFixBomDetail.getItemType()).thenReturn(parentItemType);
        when(parentFixBomDetail.getFixBomRequired()).thenReturn("N");
        when(parentFixBomDetail.getBoxBomRequired()).thenReturn("Y");

        itemSeqFixBomDetailMap.put(parentItemSeq, parentFixBomDetail);

        // When
        Whitebox.invokeMethod(fixBomCommonService, "wrapParentFixOrBoxBomRequired", itemSeqFixBomDetailMap, itemSeq, fixBomRequired, boxBomRequired);

        // Then
        verify(parentFixBomDetail).setFixBomRequired(fixBomRequired);
        verify(parentFixBomDetail, never()).setBoxBomRequired(anyString());
    }

    @Test
    public void testWrapParentFixOrBoxBomRequired_ParentExistsAndIsComponents_BoxBomRequiredNotY() throws Exception {
        // Given
        String itemSeq = "1-1";
        String parentItemSeq = "1";
        String fixBomRequired = "Y";
        String boxBomRequired = "Y";
        String parentItemType = Constant.COMPONENTS_OF_FINISHED_MATERIAL;

        FixBomDetailDTO parentFixBomDetail = mock(FixBomDetailDTO.class);
        when(parentFixBomDetail.getItemType()).thenReturn(parentItemType);
        when(parentFixBomDetail.getFixBomRequired()).thenReturn("Y");
        when(parentFixBomDetail.getBoxBomRequired()).thenReturn("N");

        itemSeqFixBomDetailMap.put(parentItemSeq, parentFixBomDetail);

        // When
        Whitebox.invokeMethod(fixBomCommonService, "wrapParentFixOrBoxBomRequired", itemSeqFixBomDetailMap, itemSeq, fixBomRequired, boxBomRequired);

        // Then
        verify(parentFixBomDetail, never()).setFixBomRequired(anyString());
        verify(parentFixBomDetail).setBoxBomRequired(boxBomRequired);
    }

    @Test
    public void testWrapParentFixOrBoxBomRequired_ParentExistsAndIsComponents_BothNotY() throws Exception {
        // Given
        String itemSeq = "1-1";
        String parentItemSeq = "1";
        String fixBomRequired = "Y";
        String boxBomRequired = "Y";
        String parentItemType = Constant.COMPONENTS_OF_THE_MANUFACTURER;

        FixBomDetailDTO parentFixBomDetail = mock(FixBomDetailDTO.class);
        when(parentFixBomDetail.getItemType()).thenReturn(parentItemType);
        when(parentFixBomDetail.getFixBomRequired()).thenReturn("N");
        when(parentFixBomDetail.getBoxBomRequired()).thenReturn("N");

        itemSeqFixBomDetailMap.put(parentItemSeq, parentFixBomDetail);

        // When
        Whitebox.invokeMethod(fixBomCommonService, "wrapParentFixOrBoxBomRequired", itemSeqFixBomDetailMap, itemSeq, fixBomRequired, boxBomRequired);

        // Then
        verify(parentFixBomDetail).setFixBomRequired(fixBomRequired);
        verify(parentFixBomDetail).setBoxBomRequired(boxBomRequired);
    }
}
/*Ended by AICoder, pid:w71bcoe53dud211142a609ab919b2e9719853c17*/