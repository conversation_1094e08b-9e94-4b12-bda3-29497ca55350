/*Started by AICoder, pid:w39f4dca22wcfb1144dd0ba3a15b3a7e3b38c097*/
package com.zte.application.impl.cbom;
import com.google.common.collect.Lists;
import com.zte.common.model.MessageId;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.PackingListConfigDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
public class FixBomCommonServiceImpl_getExtraCustomerItemNameFixBomDetails_8_Test {

    @InjectMocks
    private FixBomCommonServiceImpl fixBomCommonService;

    @Mock
    private List<String> customerCooperationMode;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetExtraCustomerItemNameFixBomDetails_withValidData() throws Exception {
        // Given
        CustomerItemsDTO customerItems1 = new CustomerItemsDTO();
        customerItems1.setCustomerCode("code1");
        customerItems1.setCustomerCodeList(Lists.newArrayList());
        CustomerItemsDTO customerItems2 = new CustomerItemsDTO();
        customerItems2.setCustomerCode("code2");
        customerItems2.setCustomerCodeList(Lists.newArrayList());
        List<CustomerItemsDTO> extraCustomerItemsList = Arrays.asList(
                customerItems1,
                customerItems2 // This code is missing in FixBomDetailDTO list
        );
        FixBomDetailDTO fixBomDetail1 = new FixBomDetailDTO();
        fixBomDetail1.setItemSupplierNo("code1");
        fixBomDetail1.setItemSeq("1-1");
        FixBomDetailDTO fixBomDetail2 = new FixBomDetailDTO();
        fixBomDetail2.setItemSupplierNo("code2");
        fixBomDetail2.setItemSeq("1-2");
        List<FixBomDetailDTO> customerItemNameFixBomDetails = Arrays.asList(fixBomDetail1, fixBomDetail2);
        Map<String, PackingListConfigDTO> packMap = new HashMap<>();
        String customerNo = "customerNo";

        // When
        List<FixBomDetailDTO> result = Whitebox.invokeMethod(fixBomCommonService, "getExtraCustomerItemNameFixBomDetails",
                extraCustomerItemsList, customerItemNameFixBomDetails, packMap, customerNo);

        // Then
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetExtraCustomerItemNameFixBomDetails_withMissingItem() throws Exception {
        when(customerCooperationMode.contains(anyString())).thenReturn(false);
        // Given
        CustomerItemsDTO customerItems1 = new CustomerItemsDTO();
        customerItems1.setCustomerCode("code1");
        customerItems1.setCustomerCodeList(Lists.newArrayList());
        CustomerItemsDTO customerItems2 = new CustomerItemsDTO();
        customerItems2.setCustomerCode("code3");
        customerItems2.setCustomerComponentType("type3");
        customerItems2.setCustomerCodeList(Lists.newArrayList());
        List<CustomerItemsDTO> extraCustomerItemsList = Arrays.asList(
                customerItems1,
                customerItems2 // This code is missing in FixBomDetailDTO list
        );
        FixBomDetailDTO fixBomDetail1 = new FixBomDetailDTO();
        fixBomDetail1.setItemSupplierNo("code1");
        fixBomDetail1.setItemSeq("seq1");
        FixBomDetailDTO fixBomDetail2 = new FixBomDetailDTO();
        fixBomDetail2.setItemSupplierNo("code2");
        fixBomDetail2.setItemSeq("seq2");
        List<FixBomDetailDTO> customerItemNameFixBomDetails = Arrays.asList(fixBomDetail1, fixBomDetail2);
        Map<String, PackingListConfigDTO> packMap = new HashMap<>();
        String customerNo = "customerNo";

        // When
        List<FixBomDetailDTO> result = Whitebox.invokeMethod(fixBomCommonService, "getExtraCustomerItemNameFixBomDetails",
                extraCustomerItemsList, customerItemNameFixBomDetails, packMap, customerNo);

        // Then
        assertNotNull(result);
    }

    @Test
    public void testGetExtraCustomerItemNameFixBomDetails_withException() {
        when(customerCooperationMode.contains(anyString())).thenReturn(false);
        // Given
        CustomerItemsDTO customerItems1 = new CustomerItemsDTO();
        customerItems1.setCustomerCode("code1");
        customerItems1.setCustomerCodeList(Lists.newArrayList());
        CustomerItemsDTO customerItems2 = new CustomerItemsDTO();
        customerItems2.setCustomerCode("code3");
        customerItems2.setCustomerComponentType("type3");
        customerItems2.setCustomerCodeList(Lists.newArrayList());
        List<CustomerItemsDTO> extraCustomerItemsList = Arrays.asList(
                customerItems1,
                customerItems2 // This code is missing in FixBomDetailDTO list
        );
        FixBomDetailDTO fixBomDetail1 = new FixBomDetailDTO();
        fixBomDetail1.setItemSupplierNo("code1");
        fixBomDetail1.setItemSeq("seq1");
        FixBomDetailDTO fixBomDetail2 = new FixBomDetailDTO();
        fixBomDetail2.setItemSupplierNo("code2");
        fixBomDetail2.setItemSeq("seq2");
        List<FixBomDetailDTO> customerItemNameFixBomDetails = Arrays.asList(fixBomDetail1, fixBomDetail2);
        Map<String, PackingListConfigDTO> packMap = new HashMap<>();
        PackingListConfigDTO value = new PackingListConfigDTO();
        value.setIsPricedMaterial("Y");
        value.setRequireMaterialUpload("Y");
        packMap.put("type3", value);
        String customerNo = "customerNo";

        // When & Then
        MesBusinessException exception = assertThrows(MesBusinessException.class, () -> {
            Whitebox.invokeMethod(fixBomCommonService, "getExtraCustomerItemNameFixBomDetails",
                    extraCustomerItemsList, customerItemNameFixBomDetails, packMap, customerNo);
        });

        // Verify the exception details
        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
        assertEquals(MessageId.PDM_MBOM_ITEM_ICC_MBOM_LOST, exception.getExMsgId());
    }

    @Test
    public void testGetExtraCustomerItemNameFixBomDetails_withException2() {
        when(customerCooperationMode.contains(anyString())).thenReturn(false);
        // Given
        CustomerItemsDTO customerItems1 = new CustomerItemsDTO();
        customerItems1.setCustomerCode("code1");
        customerItems1.setCustomerCodeList(Lists.newArrayList());
        CustomerItemsDTO customerItems2 = new CustomerItemsDTO();
        customerItems2.setCustomerCode("code3");
        customerItems2.setCustomerComponentType("type3");
        customerItems2.setCustomerCodeList(Lists.newArrayList());
        List<CustomerItemsDTO> extraCustomerItemsList = Arrays.asList(
                customerItems1,
                customerItems2 // This code is missing in FixBomDetailDTO list
        );
        FixBomDetailDTO fixBomDetail1 = new FixBomDetailDTO();
        fixBomDetail1.setItemSupplierNo("code1");
        fixBomDetail1.setItemSeq("seq1");
        FixBomDetailDTO fixBomDetail2 = new FixBomDetailDTO();
        fixBomDetail2.setItemSupplierNo("code2");
        fixBomDetail2.setItemSeq("seq2");
        List<FixBomDetailDTO> customerItemNameFixBomDetails = Arrays.asList(fixBomDetail1, fixBomDetail2);
        Map<String, PackingListConfigDTO> packMap = new HashMap<>();
        PackingListConfigDTO value = new PackingListConfigDTO();
        value.setIsPricedMaterial("N");
        value.setRequireMaterialUpload("Y");
        packMap.put("type3", value);
        String customerNo = "customerNo";

        // When & Then
        MesBusinessException exception = assertThrows(MesBusinessException.class, () -> {
            Whitebox.invokeMethod(fixBomCommonService, "getExtraCustomerItemNameFixBomDetails", extraCustomerItemsList, customerItemNameFixBomDetails, packMap, customerNo);
        });

        // Verify the exception details
        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
        assertEquals(MessageId.PDM_MBOM_ITEM_ICC_MBOM_LOST, exception.getExMsgId());
    }
}
/*Ended by AICoder, pid:w39f4dca22wcfb1144dd0ba3a15b3a7e3b38c097*/