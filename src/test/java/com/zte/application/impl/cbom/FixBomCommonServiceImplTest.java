/*Started by AICoder, pid:p78d9qcbdcq6bca146510b096161bc3192d2f26d*/
package com.zte.application.impl.cbom;

import com.google.common.collect.Lists;
import com.zte.application.PsTaskExtendedService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.CustomerItemsRepository;
import com.zte.domain.model.PackingListConfigRepository;
import com.zte.domain.model.PsTaskExtendedRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.domain.model.cbom.FixBomDetailRepository;
import com.zte.domain.model.cbom.FixBomHeadRepository;
import com.zte.infrastructure.remote.CpqdRemoteService;
import com.zte.infrastructure.remote.IccRemoteService;
import com.zte.infrastructure.remote.PdmRemoteService;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.PackingListConfigDTO;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.mbom.CpqdGbomDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.interfaces.dto.mbom.FixBomHeadDTO;
import com.zte.interfaces.dto.mbom.GbomDetailDTO;
import com.zte.interfaces.dto.mbom.MbomResDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.BaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.powermock.api.mockito.PowerMockito.when;

@PrepareForTest({RequestHeadValidationUtil.class, SpringContextUtil.class})
public class FixBomCommonServiceImplTest extends BaseTestCase {

    @InjectMocks
    private FixBomCommonServiceImpl fixBomCommonService;
    @Mock
    private FixBomHeadRepository fixBomHeadRepository;
    @Mock
    private PsTaskExtendedRepository psTaskExtendedRepository;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private IccRemoteService iccRemoteService;
    @Mock
    private CpqdRemoteService cpqdRemoteService;
    @Mock
    private PdmRemoteService pdmRemoteService;
    @Mock
    private CustomerItemsRepository customerItemsRepository;
    @Mock
    private PackingListConfigRepository packingListConfigRepository;
    @Mock
    private IdGenerator idGenerator;
    @Mock
    private FixBomDetailRepository fixBomDetailRepository;
    @Mock
    LocaleMessageSourceBean lmb;
    @Mock
    private PsTaskExtendedService psTaskExtendedService;


    @Before
    public void setUp() throws Exception {
        // Setup code if needed
        PowerMockito.mockStatic(RequestHeadValidationUtil.class, SpringContextUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(FixBomCommonServiceImpl.class)).thenReturn(fixBomCommonService);
        PowerMockito.field(FixBomCommonServiceImpl.class, "refreshFixBomBeforeDay")
                .set(fixBomCommonService, 4);
        PowerMockito.when(SpringContextUtil.getBean(BusinessConstant.RESOURCE_SERVICE_NAME))
                .thenReturn(lmb);
        PowerMockito.field(FixBomCommonServiceImpl.class, "splitCharacters")
                .set(fixBomCommonService, "//");

    }

    @Test
    public void getCustomerItemsDTOMap()throws Exception {
        List<CustomerItemsDTO> customItemsList = new ArrayList<>();
        customItemsList.add(new CustomerItemsDTO(){{setCustomerCode("8331C//J45.F2-TH");setZteCode("zte1");}});
        customItemsList.add(new CustomerItemsDTO());

        Map<String, CustomerItemsDTO> map =  Whitebox.invokeMethod(fixBomCommonService,"getCustomerItemsDTOMap",customItemsList);
        Assert.assertEquals(customItemsList.size(),2);
    }

    /* Started by AICoder, pid:5a573f5f8fhbdf214f9b087ab13c46384b94f060 */
    @Test
    public void reCreateFixBomByTaskNo() {
        String taskNo = "123";
        PowerMockito.when(psTaskExtendedRepository.queryExtendedByTaskNo(Mockito.any())).thenReturn(null);
        try {
            fixBomCommonService.reCreateFixBomByTaskNo(taskNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TASK_NO_IS_NULL, e.getMessage());
        }
        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        PowerMockito.when(psTaskExtendedRepository.queryExtendedByTaskNo(Mockito.any())).thenReturn(psTaskExtendedDTO);
        try {
            fixBomCommonService.reCreateFixBomByTaskNo(taskNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FIX_BOM_DETAIL_LOST, e.getMessage());
        }

        psTaskExtendedDTO.setFixBomId("123");
        psTaskExtendedDTO.setFixBomHeadId("123");
        List<SysLookupValues> list = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        a1.setLookupMeaning("123");
        a1.setAttribute1(Constant.REFRESH_FIX_BOM_ID);
        list.add(a1);
        psTaskExtendedDTO.setCustomerNo("123");
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.any())).thenReturn(list);
        PowerMockito.when(sysLookupValuesRepository.selectByTypeBatch(Mockito.any())).thenReturn(list);
        List<CpqdGbomDTO> cbomList = new LinkedList<>();
        PowerMockito.when(cpqdRemoteService.queryGbomList(Mockito.any())).thenReturn(cbomList);
        PowerMockito.when(lmb.getMessage(Mockito.any(), Mockito.any())).thenReturn("222");
        try {
            fixBomCommonService.reCreateFixBomByTaskNo(taskNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }

        psTaskExtendedDTO.setCustomerNo("233");
        try {
            fixBomCommonService.reCreateFixBomByTaskNo(taskNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FIX_BOM_DETAIL_LOST, e.getMessage());
        }

        psTaskExtendedDTO.setCustomerNo("12366");
        psTaskExtendedDTO.setFixBomId("rrr");
        try {
            fixBomCommonService.reCreateFixBomByTaskNo(taskNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FIX_BOM_DETAIL_LOST, e.getMessage());
        }

        psTaskExtendedDTO.setCustomerNo("123");
        psTaskExtendedDTO.setItemNo("123");
        psTaskExtendedDTO.setCloudType("HC");
        for (int i = 0; i < 4; i++) {
            String s = String.valueOf(i);
            CpqdGbomDTO temp = new CpqdGbomDTO();
            temp.setInstanceNo("12"+s);
            temp.setCustomerItemName(s + "." + s + "." + s + "." + s);
            temp.setCbomCode(s);
            cbomList.add(temp);
        }
        PowerMockito.when(cpqdRemoteService.queryGbomList(Mockito.any())).thenReturn(cbomList);
        List<MbomResDTO> mbomDetail = new LinkedList<>();
        MbomResDTO m1 = new MbomResDTO();
        FixBomHeadDTO headDTO = new FixBomHeadDTO();
        headDTO.setProductName("3.3.3");
        m1.setMbomMain(headDTO);
        List<CustomerItemsDTO> customerItemsDTOList = new LinkedList<>();
        List<FixBomDetailDTO> fixDetailList = new LinkedList<>();
        for (int i = 0; i < 15; i++) {
            String s = String.valueOf(i);
            FixBomDetailDTO temp = new FixBomDetailDTO();
            temp.setItemLevel(s);
            temp.setItemSeq(s + "-" + s);
            CustomerItemsDTO ci = new CustomerItemsDTO();
            ci.setCustomerCode(s);
            ci.setZteCode(s);
            customerItemsDTOList.add(ci);
            if (i % 3 == 0) {
                temp.setItemSupplierNo(s);
                temp.setItemType(Constant.COMPONENTS_OF_THE_MANUFACTURER);
                if (i % 6 == 0) {
                    temp.setItemType(Constant.COMPONENTS_OF_FINISHED_MATERIAL);
                }
            }
            fixDetailList.add(temp);
        }
        m1.setMbomItemList(fixDetailList);
        mbomDetail.add(m1);
        MbomResDTO m2 = new MbomResDTO();
        FixBomHeadDTO headDTO2 = new FixBomHeadDTO();
        headDTO2.setProductName("3.3.3.3");
        m2.setMbomMain(headDTO2);
        m2.setMbomItemList(Lists.newArrayList(new FixBomDetailDTO()));
        mbomDetail.add(m2);
        PowerMockito.when(iccRemoteService.queryMbomDetail(Mockito.any())).thenReturn(mbomDetail);
        List<GbomDetailDTO> gbomDetail = new LinkedList<>();
        for (int i = 0; i < 50; i++) {
            GbomDetailDTO temp = new GbomDetailDTO();
            temp.setPartNo(String.valueOf(i));
            gbomDetail.add(temp);
        }
        PowerMockito.when(pdmRemoteService.queryGbomDetail(Mockito.any())).thenReturn(gbomDetail);
        psTaskExtendedDTO.setFixBomId("123");
        psTaskExtendedDTO.setFixBomHeadId("123");
        fixBomCommonService.reCreateFixBomByTaskNo(taskNo);


        PowerMockito.when(fixBomHeadRepository.selectOneByFixBomId(Mockito.any()))
                .thenReturn(Arrays.asList("12333"));
        psTaskExtendedDTO.setFixBomHeadId("123");
        fixBomCommonService.reCreateFixBomByTaskNo(taskNo);

        PowerMockito.when(customerItemsRepository.queryListByZteCodAndCustomerNumber(Mockito.any(), Mockito.any()))
                .thenReturn(customerItemsDTOList);
        psTaskExtendedDTO.setFixBomHeadId("123");
        fixBomCommonService.reCreateFixBomByTaskNo(taskNo);

    }
    /* Ended by AICoder, pid:5a573f5f8fhbdf214f9b087ab13c46384b94f060 */


    @Test
    public void testInsertAndUpdate_WithAllParameters() {
        // Given
        FixBomHeadDTO fixBomHeadDTO = new FixBomHeadDTO();
        List<FixBomDetailDTO> detailList = Arrays.asList(new FixBomDetailDTO(), new FixBomDetailDTO());
        List<PsTaskExtendedDTO> extendList = Collections.singletonList(new PsTaskExtendedDTO());
        fixBomCommonService.insertAndUpdate(fixBomHeadDTO, detailList, extendList);
        Assert.assertTrue(true);
    }

    @Test
    public void testInsertAndUpdate_WithoutFixBomHeadDTO() {
        // Given
        List<FixBomDetailDTO> detailList = Arrays.asList(new FixBomDetailDTO(), new FixBomDetailDTO());
        List<PsTaskExtendedDTO> extendList = Collections.singletonList(new PsTaskExtendedDTO());
        fixBomCommonService.insertAndUpdate(null, null, extendList);
        Assert.assertTrue(true);
    }

    @Test
    public void testInsertAndUpdate_WithoutDetailList() {
        // Given
        FixBomHeadDTO fixBomHeadDTO = new FixBomHeadDTO();
        List<PsTaskExtendedDTO> extendList = Collections.singletonList(new PsTaskExtendedDTO());

        // When
        fixBomCommonService.insertAndUpdate(fixBomHeadDTO, null, extendList);

        // Then
        verify(fixBomHeadRepository, times(1)).insertBatch(Collections.singletonList(fixBomHeadDTO));
        verify(psTaskExtendedRepository, times(1)).updatePsExtendedBatch(extendList);
    }

    /* Started by AICoder, pid:zaddd364f7275181497f088050975e3c19e98347 */
    @Test
    public void deleteFixBomHeadAndDetailById() {
        fixBomCommonService.deleteFixBomHeadAndDetailById(null);
        List<String> idList = new LinkedList<>();
        idList.add("123");
        fixBomCommonService.deleteFixBomHeadAndDetailById(idList);
        Assert.assertTrue(true);
    }
    /* Ended by AICoder, pid:zaddd364f7275181497f088050975e3c19e98347 */

    /* Started by AICoder, pid:0e2902f34cefde21437a0b1cd19aaf3c2af870cb */
    @Test
    public void whiteBox() throws Exception{
        FixBomDetailDTO fixBomDetailDTO = new FixBomDetailDTO();
        CustomerItemsDTO dto = new CustomerItemsDTO();
        Map<String, PackingListConfigDTO> packMap = new HashMap<>();
        Whitebox.invokeMethod(fixBomCommonService, "setCustomerProperties", fixBomDetailDTO, dto, packMap, new HashMap<>());

        dto.setCustomerComponentType("AVAP");
        PackingListConfigDTO a1 = new PackingListConfigDTO();
        a1.setRequireMaterialUpload("Y");
        a1.setIsPricedMaterial("Y");
        packMap.put("AVAP", a1);

        Whitebox.invokeMethod(fixBomCommonService, "setCustomerProperties", fixBomDetailDTO, dto, packMap,
                new HashMap<>());
        Assert.assertTrue(Objects.nonNull(fixBomCommonService));

        PsTaskExtendedDTO taskExtendedDTO = new PsTaskExtendedDTO();
        taskExtendedDTO.setCustomerItemName("4444.ttt");
        List<MbomResDTO> list = new LinkedList<>();
        PowerMockito.when(iccRemoteService.queryMbomDetail(Mockito.any())).thenReturn(list);
        try {
            Whitebox.invokeMethod(fixBomCommonService, "queryIccBom", taskExtendedDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ICC_MBOM_LOST, e.getMessage());
        }
        MbomResDTO b1 = new MbomResDTO();
        b1.setMbomMain(new FixBomHeadDTO());
        list.add(b1);
        try {
            Whitebox.invokeMethod(fixBomCommonService, "queryIccBom", taskExtendedDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ICC_MBOM_LOST, e.getMessage());
        }

        List<FixBomDetailDTO> mbomDetail = new LinkedList<>();
        for (int i = 0; i < 4; i++) {
            String s = String.valueOf(i);
            FixBomDetailDTO temp = new FixBomDetailDTO();
            temp.setItemSupplierNo(s);
            temp.setItemLevel(s);
            if(i==0){
                temp.setItemSeq(s);
            }else {
                temp.setItemSeq(s+"-"+s);
            }
            mbomDetail.add(temp);
        }
        b1.setMbomItemList(mbomDetail);
        Whitebox.invokeMethod(fixBomCommonService, "queryIccBom", taskExtendedDTO);
        Whitebox.invokeMethod(fixBomCommonService, "buildThenSaveToDataBase", taskExtendedDTO,b1);

        try {
            Whitebox.invokeMethod(fixBomCommonService, "buildMbomDetail", taskExtendedDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PDM_MBOM_LOST, e.getMessage());
        }
        List<GbomDetailDTO> gbomDetail = new LinkedList<>();
        List<CustomerItemsDTO> customerItemsDTOList = new LinkedList<>();
        for (int i = 0; i < 15; i++) {
            GbomDetailDTO temp = new GbomDetailDTO();
            temp.setPartNo(String.valueOf(i));
            if (i == 4) {
                temp.setPartNo(null);
            }
            gbomDetail.add(temp);
            CustomerItemsDTO temp2 = new CustomerItemsDTO();
            temp2.setCustomerComponentType("GTT");
            temp2.setCustomerCode(String.valueOf(i));
            customerItemsDTOList.add(temp2);
        }
        PowerMockito.when(pdmRemoteService.queryGbomDetail(Mockito.any())).thenReturn(gbomDetail);
        PowerMockito.when(customerItemsRepository.queryListByZteCodAndCustomerNumber(Mockito.any(), Mockito.any()))
                .thenReturn(customerItemsDTOList);

        b1.getMbomMain().setProductName("4444");
        MbomResDTO b2 = new MbomResDTO();
        b2.setMbomMain(new FixBomHeadDTO());
        b2.getMbomMain().setProductName(taskExtendedDTO.getCustomerItemName());
        b2.setMbomItemList(Lists.newArrayList(new FixBomDetailDTO()));
        list.add(b2);
        Whitebox.invokeMethod(fixBomCommonService, "buildMbomDetail", taskExtendedDTO);
        Whitebox.invokeMethod(fixBomCommonService, "refreshDeleteFlag", b1);

        MesBusinessException mes = new MesBusinessException(RetCode.BUSINESSERROR_CODE,MessageId.CUSTOMIZE_MSG);
        Whitebox.invokeMethod(fixBomCommonService, "setErrorMsg", taskExtendedDTO, mes);
    }
    /* Ended by AICoder, pid:a201c6b0ef86d81143e00b69a149b6454916e948 */

    /* Started by AICoder, pid:h05a6t7c2710f0b14dd00bb2c1c32e19fdf717b0 */
    @Test
    public void refreshFixBomId() {
        try {
            fixBomCommonService.refreshFixBomId();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        List<SysLookupValues> list = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        a1.setLookupMeaning("123");
        a1.setAttribute1(Constant.REFRESH_FIX_BOM_ID);
        list.add(a1);
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.any())).thenReturn(list);
        try {
            fixBomCommonService.refreshFixBomId();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }

        a1.setLookupMeaning("2024-05-05 12:00:00");
        fixBomCommonService.refreshFixBomId();

        List<PsTaskExtendedDTO> listExtended = new LinkedList<>();
        for (int i = 0; i < 4; i++) {
            PsTaskExtendedDTO temp = new PsTaskExtendedDTO();
            if (i == 3) {
                temp.setEntityClass(Constant.FG_DISAS_2);
            }
            temp.setItemNo(String.valueOf(i));
            listExtended.add(temp);
        }
        PowerMockito.when(psTaskExtendedRepository.queryTaskExtendedList(Mockito.any())).thenReturn(listExtended);
        PowerMockito.when(cpqdRemoteService.queryGbomList(Mockito.any())).thenReturn(new LinkedList<>());
        try {
            fixBomCommonService.refreshFixBomId();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CPQD_INSTANCE_NO_LOST, e.getMessage());
        }

        List<CpqdGbomDTO> cbomList = new LinkedList<>();
        for (int i = 0; i < 3; i++) {
            String s = String.valueOf(i);
            CpqdGbomDTO temp = new CpqdGbomDTO();
            temp.setInstanceNo(s);
            temp.setCustomerItemName(s + "." + s + "." + s + "." + s);
            temp.setCbomCode(s);
            cbomList.add(temp);
        }
        PowerMockito.when(cpqdRemoteService.queryGbomList(Mockito.any())).thenReturn(cbomList);
        List<MbomResDTO> mbomDetail = new LinkedList<>();
        MbomResDTO m1 = new MbomResDTO();
        FixBomHeadDTO headDTO = new FixBomHeadDTO();
        m1.setMbomMain(headDTO);
        List<FixBomDetailDTO> fixDetailList = new LinkedList<>();
        for (int i = 0; i < 15; i++) {
            String s = String.valueOf(i);
            FixBomDetailDTO temp = new FixBomDetailDTO();
            temp.setItemLevel(s);
            temp.setItemSeq(s + "-" + s);
            if (i % 3 == 0) {
                temp.setItemSupplierNo(s);
            }
            fixDetailList.add(temp);
        }
        PowerMockito.when(iccRemoteService.queryMbomDetail(Mockito.any())).thenReturn(mbomDetail);
        try {
            fixBomCommonService.refreshFixBomId();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ICC_MBOM_LOST, e.getMessage());
        }

        mbomDetail.add(m1);
        try {
            fixBomCommonService.refreshFixBomId();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ICC_MBOM_LOST, e.getMessage());
        }

        m1.setMbomItemList(fixDetailList);
        try {
            fixBomCommonService.refreshFixBomId();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PDM_MBOM_LOST, e.getMessage());
        }

        List<GbomDetailDTO> gbomDetail = new LinkedList<>();
        List<CustomerItemsDTO> customerItemsDTOList = new LinkedList<>();
        List<PackingListConfigDTO> packList = new LinkedList<>();
        for (int i = 0; i < 50; i++) {
            GbomDetailDTO temp = new GbomDetailDTO();
            temp.setPartNo(String.valueOf(i));
            gbomDetail.add(temp);
        }
        PowerMockito.when(pdmRemoteService.queryGbomDetail(Mockito.any()))
                .thenReturn(gbomDetail);
        PowerMockito.when(customerItemsRepository.queryListByZteCodAndCustomerNumber(Mockito.any(), Mockito.any()))
                .thenReturn(customerItemsDTOList);
        PowerMockito.when(packingListConfigRepository.queryPackListByCustomer(Mockito.any(), Mockito.any()))
                .thenReturn(packList);
        PowerMockito.when(SpringContextUtil.getBean(FixBomCommonServiceImpl.class)).thenReturn(null);
        try {
            fixBomCommonService.refreshFixBomId();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PDM_MBOM_LOST, e.getMessage());
        }


    }
    /* Ended by AICoder, pid:h05a6t7c2710f0b14dd00bb2c1c32e19fdf717b0 */

    /* Started by AICoder, pid:rbd88j3b73zb82b147cf0bfce00b6c41be06301b */
    @Test
    public void adjustItemNumber() {
        List<FixBomDetailDTO> node = new ArrayList<>();
        fixBomCommonService.adjustItemNumber(node, 1);

        ReflectionTestUtils.setField(fixBomCommonService, "specificItemTypeList", Arrays.asList("成品料"));
        FixBomDetailDTO fixBomDetailDTO = new FixBomDetailDTO();
        fixBomDetailDTO.setItemLevel("0");
        fixBomDetailDTO.setItemSeq("1");
        fixBomDetailDTO.setItemType("成品料");
        node.add(fixBomDetailDTO);
        fixBomDetailDTO = new FixBomDetailDTO();
        fixBomDetailDTO.setItemLevel("1");
        fixBomDetailDTO.setItemSeq("1-2");
        fixBomDetailDTO.setItemType("成品料");
        fixBomDetailDTO.setItemNumber("2");
        node.add(fixBomDetailDTO);
        fixBomDetailDTO = new FixBomDetailDTO();
        fixBomDetailDTO.setItemLevel("2");
        fixBomDetailDTO.setItemSeq("1-2-4");
        fixBomDetailDTO.setItemType("模块");
        fixBomDetailDTO.setItemNumber("1");
        node.add(fixBomDetailDTO);
        fixBomDetailDTO = new FixBomDetailDTO();
        fixBomDetailDTO.setItemLevel("3");
        fixBomDetailDTO.setItemSeq("1-2-4-1");
        fixBomDetailDTO.setItemType("阿里规格料");
        fixBomDetailDTO.setItemNumber("12");
        node.add(fixBomDetailDTO);
        fixBomCommonService.adjustItemNumber(node, 1);
        Assert.assertTrue(true);
    }
    /* Ended by AICoder, pid:rbd88j3b73zb82b147cf0bfce00b6c41be06301b */

    /*Started by AICoder, pid:p78d9qcbdcq6bca146510b096161bc3192d2f26d*/
    @Test
    public void queryFixBomDetailByFixBomId(){
        List<FixBomDetailDTO> result = fixBomCommonService.queryFixBomDetailByFixBomId("");
        Assert.assertTrue(result.isEmpty());

        result = fixBomCommonService.queryFixBomDetailByFixBomId("123");
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void queryTreeNodeByFixBomId(){
        List<FixBomDetailDTO> list = fixBomCommonService.queryTreeNodeByFixBomId("jj");
        Assert.assertTrue(CollectionUtils.isEmpty(list));
        list = new LinkedList<>();
        PowerMockito.when(fixBomDetailRepository.selectIdByFixBomId("jj")).thenReturn(list);

        for (int i = 0; i < 8; i++) {
            FixBomDetailDTO temp = new FixBomDetailDTO();
            temp.setItemLevel(String.valueOf(i+1));
            StringBuilder builder = new StringBuilder();
            for (int j = 0; j <= i; j++) {
                builder.append(1).append("-");
            }
            temp.setItemSeq(builder.substring(0,builder.lastIndexOf("-")));
            list.add(temp);
        }
        fixBomCommonService.queryTreeNodeByFixBomId("jj");
    }
    /* Ended by AICoder, pid:o1d3f80620988c314f720a14e0cbb952295317bb */

    @Test
    public void getFixBomByTaskNo () {
        Assert.assertTrue(fixBomCommonService.getFixBomByTaskNo("").isEmpty());
        Assert.assertTrue(fixBomCommonService.getFixBomByTaskNo("1231").isEmpty());

    }

    private ArrayList<FixBomDetailDTO> buildChild(FixBomDetailDTO root) {
        root.setChildNodes(new ArrayList<>());
        Integer currentLevel = new Integer(root.getItemLevel()) + NumConstant.NUM_ONE;
        if (currentLevel.equals(NumConstant.NUM_FIVE)) {
            return null;
        }
        ArrayList<FixBomDetailDTO> fixBomDetailDTOS = new ArrayList<>();
        fixBomDetailDTOS.add(root);
        for (int i = 0; i < NumConstant.NUM_TWENTY; i++) {
            FixBomDetailDTO childNode = new FixBomDetailDTO();
            childNode.setItemLevel(String.valueOf(currentLevel));
            childNode.setItemSeq(root.getItemSeq() + Constant.LINE + (i + NumConstant.NUM_ONE));
            if (i % 2 == 0) {
                childNode.setDeleteFlag("N");
            } else {
                childNode.setDeleteFlag("Y");
            }
            if (i % 2 == 0) {
                childNode.setItemType(Constant.COMPONENTS_OF_FINISHED_MATERIAL);
                childNode.setItemMaterialType(Constant.ITEM_TYPE_L6_PKG);
            }
            if (i % 3 == 0) {
                childNode.setItemType(Constant.ITEM_TYPE_L6_PKG);
                childNode.setItemType(null);
            }
            if (i % 5 == 0) {
                childNode.setItemMaterialType(Constant.COMPONENTS_OF_FINISHED_MATERIAL);
                childNode.setItemMaterialType(null);
            }
            fixBomDetailDTOS.add(childNode);
            ArrayList<FixBomDetailDTO> bomDetailDTOS = buildChild(childNode);
            if (null != bomDetailDTOS) {
                fixBomDetailDTOS.addAll(bomDetailDTOS);
            }
        }
        return fixBomDetailDTOS;
    }

    /* Started by AICoder, pid:47c06k58c5ge8b1143c809b0601012223f5707ea */
    @Test
    public void testQueryFixBomByTaskNoOrFixBomId() {
        String taskNo = "123";
        String fixBomId = "jj";

        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setTaskNo(taskNo);
        psTaskExtendedDTO.setFixBomId(fixBomId);

        when(psTaskExtendedService.queryExtendedByTaskNoAndFixBomId(taskNo, fixBomId)).thenReturn(psTaskExtendedDTO);

        List<FixBomDetailDTO> fixBomDetailDTOs = new ArrayList<>();
        when(fixBomDetailRepository.selectIdByFixBomId("jj")).thenReturn(fixBomDetailDTOs);

        FixBomDetailDTO temp = new FixBomDetailDTO();
        temp.setItemLevel("1");
        temp.setItemSeq("1");
        fixBomDetailDTOs.addAll(Objects.requireNonNull(buildChild(temp)));

        when(fixBomDetailRepository.getFixBomByTaskNo(taskNo)).thenReturn(fixBomDetailDTOs);

        List<FixBomDetailDTO> result = fixBomCommonService.queryFixBomByTaskNoOrFixBomId(taskNo, fixBomId);

        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test(expected = MesBusinessException.class)
    public void testQueryFixBomByTaskNoOrFixBomId_BothNull() {
        fixBomCommonService.queryFixBomByTaskNoOrFixBomId(null, null);
    }

    @Test
    public void testQueryFixBomByTaskNoOrFixBomId_FixBomNull() {
        String taskNo = "10349620";
        when(psTaskExtendedService.queryExtendedByTaskNoAndFixBomId(taskNo, null)).thenReturn(null);
        assertNull(fixBomCommonService.queryFixBomByTaskNoOrFixBomId(taskNo, null));
    }
    @Test
    public void testQueryFixBomByTaskNoOrFixBomId_TaskNoNull() {
        String fixBom = "10349620";
        assertNull(fixBomCommonService.queryFixBomByTaskNoOrFixBomId(null, fixBom));
    }

    @Test
    public void testQueryFixBomByTaskNoOrFixBomId_NoFixBom() throws Exception {
        String fixBom = "10349620";
        ArrayList<FixBomDetailDTO> fixBomDetailDTOS = new ArrayList<>();

        FixBomDetailDTO fixBomDetailDTO = new FixBomDetailDTO();
        fixBomDetailDTO.setFixBomId(fixBom);
        fixBomDetailDTO.setItemLevel("1");
        fixBomDetailDTO.setItemSeq("1");
        fixBomDetailDTOS.add(fixBomDetailDTO);

        FixBomDetailDTO fixBomDetailDTO2 = new FixBomDetailDTO();
        fixBomDetailDTO2.setFixBomId(fixBom);
        fixBomDetailDTO2.setItemLevel("2");
        fixBomDetailDTO2.setItemSeq("1-2");
        fixBomDetailDTOS.add(fixBomDetailDTO2);
        when(fixBomCommonService,"queryTreeNodeByFixBomId",fixBom).thenReturn(null).thenReturn(fixBomDetailDTOS);

        assertNull(fixBomCommonService.queryFixBomByTaskNoOrFixBomId(null, fixBom));
        assertNotNull(fixBomCommonService.queryFixBomByTaskNoOrFixBomId(null, fixBom));
    }

    @Test
    public void queryFixBomDetailByHeadId() {
        List<FixBomDetailDTO> result = fixBomCommonService.queryFixBomDetailByHeadId("");
        Assert.assertTrue(result.isEmpty());

        result = fixBomCommonService.queryFixBomDetailByHeadId("123");
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void queryTreeNodeByHeadId() {
        PowerMockito.when(fixBomDetailRepository.selectIdByHeadId("jj")).thenReturn(new ArrayList<>());
        List<FixBomDetailDTO> list = fixBomCommonService.queryTreeNodeByHeadId("jj");
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }

    @Test
    public void testQueryFixBomByTaskNoOrHeadId() {
        String taskNo = "123";
        String fixBomId = "jj";

        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setTaskNo(taskNo);
        psTaskExtendedDTO.setFixBomId(fixBomId);
        psTaskExtendedDTO.setFixBomHeadId(fixBomId);

        when(psTaskExtendedService.queryExtendedByTaskNoAndFixBomHeadId(taskNo, fixBomId)).thenReturn(psTaskExtendedDTO);

        List<FixBomDetailDTO> fixBomDetailDTOs = new ArrayList<>();
        when(fixBomDetailRepository.selectIdByHeadId("jj")).thenReturn(fixBomDetailDTOs);

        FixBomDetailDTO temp = new FixBomDetailDTO();
        temp.setItemLevel("1");
        temp.setItemSeq("1");
        fixBomDetailDTOs.addAll(Objects.requireNonNull(buildChild(temp)));

        when(fixBomDetailRepository.getFixBomByTaskNo(taskNo)).thenReturn(fixBomDetailDTOs);

        List<FixBomDetailDTO> result = fixBomCommonService.queryFixBomByTaskNoOrHeadId(taskNo, fixBomId);

        assertNotNull(result);
        assertEquals(1, result.size());
    }

    @Test
    public void testQueryFixBomByTaskNoOrHeadId_BothNull() {
        try {
            fixBomCommonService.queryFixBomByTaskNoOrHeadId(null, null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.REQUIRED_TASK_NO_OR_FIX_BOM_ID, e.getMessage());
        }
    }

    @Test
    public void testQueryFixBomByTaskNoOrHeadId_TaskNoNull() {
        when(fixBomDetailRepository.selectIdByHeadId("test")).thenReturn(new ArrayList<>());
        List<FixBomDetailDTO> resultList = fixBomCommonService.queryFixBomByTaskNoOrHeadId(null, "test");
        Assert.assertEquals(0, resultList.size());
    }

    @Test
    public void testQueryFixBomByTaskNoOrHeadId_TaskExtendEmpty() {
        when(psTaskExtendedService.queryExtendedByTaskNoAndFixBomHeadId(Mockito.any(), Mockito.any())).thenReturn(null);
        when(fixBomDetailRepository.selectIdByHeadId("test")).thenReturn(new ArrayList<>());
        List<FixBomDetailDTO> resultList = fixBomCommonService.queryFixBomByTaskNoOrHeadId(null, "test");
        Assert.assertEquals(0, resultList.size());
    }
}
/*Ended by AICoder, pid:p78d9qcbdcq6bca146510b096161bc3192d2f26d*/