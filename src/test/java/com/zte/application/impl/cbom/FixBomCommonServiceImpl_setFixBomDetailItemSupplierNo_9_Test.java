/*Started by AICoder, pid:sc47aea60bse4ea14652083631158c121428cbdf*/
package com.zte.application.impl.cbom;
import com.google.common.collect.Sets;
import com.zte.common.utils.Constant;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.Collections;
import java.util.Set;

import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
public class FixBomCommonServiceImpl_setFixBomDetailItemSupplierNo_9_Test {

    @InjectMocks
    private FixBomCommonServiceImpl fixBomCommonService;

    @Mock
    private FixBomDetailDTO fixBomDetailDTO;

    @Before
    public void setUp() {
        // Setup code if needed
    }

    @Test
    public void testSetFixBomDetailItemSupplierNo_ItemLevelZero() throws Exception {
        when(fixBomDetailDTO.getItemLevel()).thenReturn("0");
        when(fixBomDetailDTO.getItemType()).thenReturn(Constant.COMPONENTS_OF_FINISHED_MATERIAL);
        String customerItemName = "TestItem";

        Whitebox.invokeMethod(fixBomCommonService, "setFixBomDetailItemSupplierNo", fixBomDetailDTO, customerItemName, Collections.emptySet());

        verify(fixBomDetailDTO).setItemSupplierNo(customerItemName);
    }

    @Test
    public void testSetFixBomDetailItemSupplierNo_NonZeroItemLevel_NotComponentsOfFinishedMaterial() throws Exception {
        when(fixBomDetailDTO.getItemType()).thenReturn("OtherType");

        Whitebox.invokeMethod(fixBomCommonService, "setFixBomDetailItemSupplierNo", fixBomDetailDTO, "TestItem", Collections.emptySet());

        verify(fixBomDetailDTO, never()).setItemSupplierNo(anyString());
    }

    @Test
    public void testSetFixBomDetailItemSupplierNo_NonZeroItemLevel_EmptyItemName() throws Exception {
        when(fixBomDetailDTO.getItemLevel()).thenReturn("1");
        when(fixBomDetailDTO.getItemType()).thenReturn(Constant.COMPONENTS_OF_FINISHED_MATERIAL);
        when(fixBomDetailDTO.getItemName()).thenReturn("");

        Whitebox.invokeMethod(fixBomCommonService, "setFixBomDetailItemSupplierNo", fixBomDetailDTO, "TestItem", Collections.emptySet());

        verify(fixBomDetailDTO, never()).setItemSupplierNo(anyString());
    }

    @Test
    public void testSetFixBomDetailItemSupplierNo_NonZeroItemLevel_EmptyCustomerItemNameFixBomDetailItemNameSet() throws Exception {
        when(fixBomDetailDTO.getItemLevel()).thenReturn("1");
        when(fixBomDetailDTO.getItemType()).thenReturn(Constant.COMPONENTS_OF_FINISHED_MATERIAL);
        when(fixBomDetailDTO.getItemName()).thenReturn("TestItem");

        Whitebox.invokeMethod(fixBomCommonService, "setFixBomDetailItemSupplierNo", fixBomDetailDTO, "TestItem", Collections.emptySet());

        verify(fixBomDetailDTO, never()).setItemSupplierNo(anyString());
    }

    @Test
    public void testSetFixBomDetailItemSupplierNo_NonZeroItemLevel_ValidCustomerItemNameFixBomDetailItemNameSet() throws Exception {
        when(fixBomDetailDTO.getItemLevel()).thenReturn("1");
        when(fixBomDetailDTO.getItemType()).thenReturn(Constant.COMPONENTS_OF_FINISHED_MATERIAL);
        when(fixBomDetailDTO.getItemName()).thenReturn("TestItem.123");

        Set<String> customerItemNameFixBomDetailItemNameSet = Sets.newHashSet("1", "1.2.3", "TestItem.123.1", "AnotherItem.456");

        Whitebox.invokeMethod(fixBomCommonService, "setFixBomDetailItemSupplierNo",fixBomDetailDTO, "TestItem", customerItemNameFixBomDetailItemNameSet);

        verify(fixBomDetailDTO).setItemSupplierNo("TestItem.123.1");
    }
}
/*Ended by AICoder, pid:sc47aea60bse4ea14652083631158c121428cbdf*/