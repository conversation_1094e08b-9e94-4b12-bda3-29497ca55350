/*Started by AICoder, pid:d85f068b446649f1404b0a7f01a26c0272a11b0a*/
package com.zte.application.impl.cbom;

import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.Comparator;

import static org.junit.jupiter.api.Assertions.assertEquals;

@RunWith(PowerMockRunner.class)
public class FixBomCommonServiceImpl_compare_9_Test {

    @InjectMocks
    private FixBomCommonServiceImpl fixBomCommonService;

    @Test
    public void testCompare_SameLevelDifferentNumbers() throws Exception {
        FixBomDetailDTO item1 = new FixBomDetailDTO();
        item1.setItemSeq("1-2");
        
        FixBomDetailDTO item2 = new FixBomDetailDTO();
        item2.setItemSeq("1-3");

        Comparator<FixBomDetailDTO> comparator = Whitebox.invokeMethod(fixBomCommonService, "getFixBomDetailComparator");
        int result = comparator.compare(item1, item2);
        assertEquals(-1, result);
    }

    @Test
    public void testCompare_DifferentLevels() throws Exception {
        FixBomDetailDTO item1 = new FixBomDetailDTO();
        item1.setItemSeq("1-2-3");
        
        FixBomDetailDTO item2 = new FixBomDetailDTO();
        item2.setItemSeq("1-2");

        Comparator<FixBomDetailDTO> comparator = Whitebox.invokeMethod(fixBomCommonService, "getFixBomDetailComparator");
        int result = comparator.compare(item1, item2);
        assertEquals(1, result);
    }

    @Test
    public void testCompare_SameItemSeq() throws Exception {
        FixBomDetailDTO item1 = new FixBomDetailDTO();
        item1.setItemSeq("1-2-3");
        
        FixBomDetailDTO item2 = new FixBomDetailDTO();
        item2.setItemSeq("1-2-3");

        Comparator<FixBomDetailDTO> comparator = Whitebox.invokeMethod(fixBomCommonService, "getFixBomDetailComparator");
        int result = comparator.compare(item1, item2);
        assertEquals(0, result);
    }
}
/*Ended by AICoder, pid:d85f068b446649f1404b0a7f01a26c0272a11b0a*/