/*Started by AICoder, pid:11cf23ddc6l2914148d009a3a190672256a1c589*/
package com.zte.application.impl.cbom;

import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

@RunWith(PowerMockRunner.class)
public class FixBomCommonServiceImpl_doMergeFixBomDetails_9_Test {

    @InjectMocks
    private FixBomCommonServiceImpl fixBomCommonService;

    @Mock
    private FixBomDetailDTO mockFixBomDetailDTO;

    private List<FixBomDetailDTO> customerSubFixBomDetails;
    private List<FixBomDetailDTO> extraMbomItems;

    @Before
    public void setUp() {
        customerSubFixBomDetails = new ArrayList<>();
        extraMbomItems = new ArrayList<>();
    }

    @Test
    public void testDoMergeFixBomDetails_WithParentItemSeq() throws Exception {
        // Given
        FixBomDetailDTO parentItem = new FixBomDetailDTO();
        parentItem.setItemSeq("1-1");
        parentItem.setItemLevel("1");

        FixBomDetailDTO childItem = new FixBomDetailDTO();
        childItem.setItemSeq("1-1-1");
        childItem.setItemLevel("2");

        FixBomDetailDTO childItem2 = new FixBomDetailDTO();
        childItem2.setItemSeq("1-2-1");
        childItem2.setItemLevel("2");

        extraMbomItems.add(parentItem);
        extraMbomItems.add(childItem);
        extraMbomItems.add(childItem2);

        int maxSeqNo = 0;

        // When
        Whitebox.invokeMethod(fixBomCommonService, "doMergeFixBomDetails", customerSubFixBomDetails, extraMbomItems, maxSeqNo);
        // Then
        assertEquals(3, customerSubFixBomDetails.size());
        assertEquals("2", customerSubFixBomDetails.get(1).getItemLevel());
        assertEquals("1-1-1", customerSubFixBomDetails.get(1).getItemSeq());
    }

    @Test
    public void testDoMergeFixBomDetails_WithoutParentItemSeq() throws Exception {
        // Given
        FixBomDetailDTO itemWithoutParent = new FixBomDetailDTO();
        itemWithoutParent.setItemSeq("1-2-3");

        extraMbomItems.add(itemWithoutParent);

        int maxSeqNo = 0;

        // When
        Whitebox.invokeMethod(fixBomCommonService, "doMergeFixBomDetails", customerSubFixBomDetails, extraMbomItems, maxSeqNo);

        // Then
        assertEquals(1, customerSubFixBomDetails.size());
        assertEquals("1", customerSubFixBomDetails.get(0).getItemLevel());
        assertTrue(customerSubFixBomDetails.get(0).getItemSeq().startsWith("1-"));
    }
}
/*Ended by AICoder, pid:11cf23ddc6l2914148d009a3a190672256a1c589*/