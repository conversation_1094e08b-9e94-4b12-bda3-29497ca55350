/*Started by AICoder, pid:7e758t36b158edb1497108d7e192af281059ded1*/
package com.zte.application.impl.cbom;

import com.zte.common.model.MessageId;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.interfaces.dto.mbom.MbomResDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class FixBomCommonServiceImpl_getMbmoFromMapByProductName_19_Test {

    @InjectMocks
    private FixBomCommonServiceImpl fixBomCommonService;

    @Test
    public void testGetMbmoFromMapByProductName_MbomResDTOIsNull() {
        Map<String, MbomResDTO> productNameMbomResMap = new HashMap<>();
        productNameMbomResMap.put("productName", null);

        MesBusinessException exception = assertThrows(MesBusinessException.class, () -> {
            Whitebox.invokeMethod(fixBomCommonService, "getMbmoFromMapByProductName", productNameMbomResMap, "customerNo", "productName");
        });

        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
        assertEquals(MessageId.ICC_MBOM_LOST, exception.getExMsgId());
    }

    @Test
    public void testGetMbmoFromMapByProductName_MbomItemListIsEmpty() {
        MbomResDTO mbomResDTO = mock(MbomResDTO.class);
        when(mbomResDTO.getMbomItemList()).thenReturn(Collections.emptyList());

        Map<String, MbomResDTO> productNameMbomResMap = new HashMap<>();
        productNameMbomResMap.put("productName", mbomResDTO);

        MesBusinessException exception = assertThrows(MesBusinessException.class, () -> {
            Whitebox.invokeMethod(fixBomCommonService, "getMbmoFromMapByProductName",productNameMbomResMap, "customerNo", "productName");
        });

        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
        assertEquals(MessageId.ICC_MBOM_LOST, exception.getExMsgId());
    }

    @Test
    public void testGetMbmoFromMapByProductName_Success() throws Exception {
        MbomResDTO mbomResDTO = mock(MbomResDTO.class);
        when(mbomResDTO.getMbomItemList()).thenReturn(Collections.singletonList(mock(FixBomDetailDTO.class)));

        Map<String, MbomResDTO> productNameMbomResMap = new HashMap<>();
        productNameMbomResMap.put("productName", mbomResDTO);

        MbomResDTO result = Whitebox.invokeMethod(fixBomCommonService, "getMbmoFromMapByProductName",productNameMbomResMap, "customerNo", "productName");

        assertNotNull(result);
        assertEquals(mbomResDTO, result);
    }
}
/*Ended by AICoder, pid:7e758t36b158edb1497108d7e192af281059ded1*/