/*Started by AICoder, pid:ob854a35a01acbf14b0a09d5615f569bcf89ddac*/
package com.zte.application.impl.cbom;
import com.zte.application.PsTaskExtendedService;
import com.zte.domain.model.cbom.FixBomDetailRepository;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import lombok.var;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class FixBomCommonServiceImplAdjustItemNumberByTaskNoTest {

    @Mock
    private PsTaskExtendedService psTaskExtendedService;

    @Mock
    private FixBomDetailRepository fixBomDetailRepository;

    @InjectMocks
    private FixBomCommonServiceImpl service;

    @Test
    public void adjustItemNumberByTaskNo_WhenTaskNoIsNull_ReturnsEmpty() {
        List<FixBomDetailDTO> result = service.adjustItemNumberByTaskNo(null);
        assertTrue(result.isEmpty());
    }

    @Test
    public void adjustItemNumberByTaskNo_WhenTaskNoIsEmpty_ReturnsEmpty() {
        List<FixBomDetailDTO> result = service.adjustItemNumberByTaskNo("");
        assertTrue(result.isEmpty());
    }

    @Test
    public void adjustItemNumberByTaskNo_WhenPsTaskExtendedServiceReturnsEmpty_ReturnsEmpty() {
        when(psTaskExtendedService.queryByTaskNos(anyList())).thenReturn(Collections.emptyList());

        List<FixBomDetailDTO> result = service.adjustItemNumberByTaskNo("TASK123");
        assertTrue(result.isEmpty());
    }

    @Test
    public void adjustItemNumberByTaskNo_WithValidTaskNo_AdjustsCorrectly() throws Exception {
        PsTaskExtendedDTO taskDto = new PsTaskExtendedDTO();
        taskDto.setFixBomId("1L");
        when(psTaskExtendedService.queryByTaskNos(anyList())).thenReturn(Arrays.asList(taskDto));
        when(fixBomDetailRepository.selectIdByFixBomId("1L")).thenReturn(Arrays.asList(new FixBomDetailDTO(){{setItemLevel("1");setItemSeq("1-1");}},new FixBomDetailDTO(){{setItemLevel("2");setItemSeq("1-1");}}));
        ReflectionTestUtils.setField(service,"specificItemTypeList", Arrays.asList("成品料"));

        List<FixBomDetailDTO> result = service.adjustItemNumberByTaskNo("TASK123");
        assertFalse(result.isEmpty());
    }

    @Test
    public void adjustItemNumber_WithSpecificItemType_DoesNotAdjust() {
        FixBomDetailDTO node = new FixBomDetailDTO();
        node.setItemType("成品料");
        node.setItemNumber("5");
        ReflectionTestUtils.setField(service,"specificItemTypeList", Arrays.asList("成品料"));

        service.adjustItemNumber(Arrays.asList(node), 2);

        assertEquals("5", node.getItemNumber());
    }

    @Test
    public void adjustItemNumber_WithNormalItemType_AdjustsCorrectly() {
        FixBomDetailDTO parent = new FixBomDetailDTO();
        parent.setItemType("非成品");
        parent.setItemNumber("3");
        FixBomDetailDTO child = new FixBomDetailDTO();
        child.setItemType("非成品");
        child.setItemNumber("2");
        parent.setChildNodes(Arrays.asList(child));
        ReflectionTestUtils.setField(service,"specificItemTypeList", Arrays.asList("成品料"));
        service.adjustItemNumber(Arrays.asList(parent), 2);

        assertEquals("6", parent.getItemNumber());
        assertEquals("12", child.getItemNumber());
    }

    @Test
    public void adjustItemNumber_WithNullItemNumber_UsesDefault() {
        FixBomDetailDTO node = new FixBomDetailDTO();
        node.setItemType("非成品");
        node.setItemNumber(null);
        ReflectionTestUtils.setField(service,"specificItemTypeList", Arrays.asList("成品料"));
        service.adjustItemNumber(Arrays.asList(node), 3);

        assertEquals("3", node.getItemNumber());
    }

    private FixBomDetailDTO createSampleTree() {
        FixBomDetailDTO root = new FixBomDetailDTO();
        root.setItemType("非成品");
        root.setItemNumber("2");

        FixBomDetailDTO level1 = new FixBomDetailDTO();
        level1.setItemType("非成品");
        level1.setItemNumber("2");
        root.setChildNodes(Arrays.asList(level1));

        FixBomDetailDTO level2 = new FixBomDetailDTO();
        level2.setItemType("非成品");
        level2.setItemNumber("3");
        level1.setChildNodes(Arrays.asList(level2));

        return root;
    }
}
/*Ended by AICoder, pid:ob854a35a01acbf14b0a09d5615f569bcf89ddac*/