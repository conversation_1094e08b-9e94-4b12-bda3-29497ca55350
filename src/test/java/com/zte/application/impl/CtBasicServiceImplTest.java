package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.application.BsItemInfoService;
import com.zte.application.impl.craftTech.CtBasicServiceImpl;
import com.zte.application.kafka.producer.CraftInfoAbolishProducer;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.craftTech.*;
import com.zte.interfaces.dto.BsItemInfoDTO;
import com.zte.interfaces.dto.CtBasicAbolishDTO;
import com.zte.interfaces.dto.CtBasicDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.mockito.Matchers.*;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class CtBasicServiceImplTest extends BaseTestCase {
    @InjectMocks
    private CtBasicServiceImpl ctBasicService;
    @Mock
    private CtBasicRepository ctBasicRepository;
    @Mock
    private HrmUserCenterServiceImpl hrmUserCenterService;

    @Mock
    private CtRouteHeadRepository ctRouteHeadRepository;
    @Mock
    private CtRouteDetailRepository ctRouteDetailRepository;
    @Mock
    private BsItemInfoService itemInfoService;

    @Mock
    private CraftInfoAbolishProducer craftInfoAbolishProducer;

    @Test
    public void getCount() {
        Map<String, Object> record = new HashMap<>();
        record.put("craftId","test123");
        PowerMockito.when(ctBasicRepository.getCountBySelectAll(Mockito.anyMap())).thenReturn(new Long("1"));
        Assert.assertNotNull(ctBasicService.getCount(record,"lastUpdatedDate","desc"));
    }

    @Test
    public void getCraftInfoSynchronizationDTO() throws Exception {
        CtBasicDTO ctBasicDTO = new CtBasicDTO();
        ctBasicDTO.setCraftSection("单板装配");
        ctBasicDTO.setCraftVersion("V.A");
        ctBasicDTO.setCraftId("id");
        PowerMockito.when(ctBasicRepository.getLastVersionByItemNo(anyString())).thenReturn(ctBasicDTO);
        List<CtBasicDTO> ctBasicList = Lists.newArrayList();
        ctBasicList.add(ctBasicDTO);
        PowerMockito.when(ctBasicRepository.getList(anyMap())).thenReturn(ctBasicList);
        List<CtRouteHead> listEntity = new ArrayList<>();
        CtRouteHead record = new CtRouteHead();
        String id = UUID.randomUUID().toString();
        record.setRouteId(id);
        listEntity.add(record);
        when(ctRouteHeadRepository.getRouteHead(any())).thenReturn(listEntity);
        List<CtRouteDetail> ctRouteDetailList = new LinkedList<>();
        CtRouteDetail c1 = new CtRouteDetail();
        c1.setCraftSection("SMT-A");
        c1.setProcessSeq(1);
        ctRouteDetailList.add(c1);
        when(ctRouteDetailRepository.getListByRouteIdList(any())).thenReturn(ctRouteDetailList);
        Assert.assertNotNull(ctBasicService.getCraftInfoSynchronizationDTO("aaa","1"));
    }
    @Test
    public void getPage() throws Exception {
        List<CtBasicDTO> result = new ArrayList<>();
        CtBasicDTO ctBasicDTO = new CtBasicDTO();
        ctBasicDTO.setItemNo("1");
        ctBasicDTO.setCreateBy("00286523");
        ctBasicDTO.setLastUpdatedBy("00286523");
        result.add(ctBasicDTO);
        Map<String, Object> record = new HashMap<>();
        record.put("craftId","test123");
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("hl");
        hrmPersonInfoDTO.setId("00286523");
        hrmPersonInfoDTOMap.put("00286523",hrmPersonInfoDTO);
        PowerMockito.when(ctBasicRepository.getPageBySelectAll(Mockito.anyMap())).thenReturn(result);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.anyList())).thenReturn(hrmPersonInfoDTOMap);
        PowerMockito.when(itemInfoService.getItemNameAndPCBVersion(Mockito.anyList())).thenReturn(Lists.newArrayList());
        ctBasicService.getPage(record,"lastUpdatedDate","desc",new Long("1"),new Long("10"));
        PowerMockito.when(itemInfoService.getItemNameAndPCBVersion(Mockito.anyList())).thenReturn(Lists.newArrayList(new BsItemInfo(){{setItemName("1"); setItemNo("2");}}));
        ctBasicService.getPage(record,"lastUpdatedDate","desc",new Long("1"),new Long("10"));
        PowerMockito.when(itemInfoService.getItemNameAndPCBVersion(Mockito.anyList())).thenReturn(Lists.newArrayList(new BsItemInfo(){{setItemName("1"); setItemNo("1");}}));
        Assert.assertNotNull(ctBasicService.getPage(record,"lastUpdatedDate","desc",new Long("1"),new Long("10")));
    }

    @Test
    public void getList() throws Exception {
        List<CtBasicDTO> result = new ArrayList<>();
        CtBasicDTO ctBasicDTO = new CtBasicDTO();
        ctBasicDTO.setCreateBy("00286523");
        ctBasicDTO.setLastUpdatedBy("00286523");
        result.add(ctBasicDTO);
        Map<String, Object> record = new HashMap<>();
        record.put("craftId","test123");
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("hl");
        hrmPersonInfoDTO.setId("00286523");
        hrmPersonInfoDTOMap.put("00286523",hrmPersonInfoDTO);
        PowerMockito.when(ctBasicRepository.getListBySelectAll(Mockito.anyMap())).thenReturn(result);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.anyList())).thenReturn(hrmPersonInfoDTOMap);
        ctBasicService.getList(record,"lastUpdatedDate","desc");
        Assert.assertNotNull(ctBasicService.getList(record,"","desc"));
    }

    @Test
    public void delete() {
        ctBasicService.delByCraftId("a", "a");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void abolishCraftById() {
        ctBasicService.abolishCraftById(new CtBasicAbolishDTO());
        CtBasicAbolishDTO dto = new CtBasicAbolishDTO();
        Assert.assertNotNull(dto);
    }

    @Test
    public void abolish13Or14ItemCodeCraft() {
        ctBasicService.abolish13Or14ItemCodeCraft();
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }
}