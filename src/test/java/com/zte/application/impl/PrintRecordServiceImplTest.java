package com.zte.application.impl;

import com.zte.application.HrmUserCenterService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.PrintRecord;
import com.zte.domain.model.PrintRecordRepository;
import com.zte.interfaces.dto.PrintRecordDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;



@RunWith(PowerMockRunner.class)
@PrepareForTest(ServiceDataBuilderUtil.class)
public class PrintRecordServiceImplTest extends BaseTestCase {

    @InjectMocks
    private PrintRecordServiceImpl service;

    @Mock
    private PrintRecordRepository recordRepository;

    @Mock
    private HrmUserCenterService hrmUserCenterService;
    @Test
    public void countExportTotal() {
        PrintRecordDTO dto = new PrintRecordDTO();
        try {
            service.countExportTotal(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_ITEMLISTNO_EMPTY, e.getMessage());
        }
        List<String> list = new ArrayList<>();
        list.add("123456");
        dto.setItemCodeList(list);
        try {
            service.countExportTotal(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }

        dto.setUpdateDateStart("2024-05-15 23:59:59");
        try {
            service.countExportTotal(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }
        dto.setUpdateDateStart(null);
        dto.setUpdateDateEnd("2024-05-15 23:59:59");
        try {
            service.countExportTotal(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }
        dto.setUpdateDateStart("2023-04-15 23:59:59");
        try {
            service.countExportTotal(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_DRTAIL_TIME_INTERVAL_ERROR, e.getMessage());
        }

        dto.setUpdateDateStart("2024-04-15 23:59:59");
        service.countExportTotal(dto);
        Page<PrintRecord> pageInfo = new Page<>(dto.getPage(), dto.getRows());
        List<PrintRecord> printRecordList = new ArrayList<>();
        PowerMockito.when(recordRepository.getList(any())).thenReturn(printRecordList);
    }

    @Test
    public void queryExportData() {
        PrintRecordDTO dto = new PrintRecordDTO();
        List<String> list = new ArrayList<>();
        list.add("123456");
        dto.setItemCodeList(list);
        dto.setUpdateDateEnd("2024-05-15 23:59:59");
        dto.setUpdateDateStart("2024-04-15 23:59:59");
        try {
            service.queryExportData(dto,1,1);
        }catch (Exception e){
            Assert.assertEquals(MessageId.FAILED_TO_OBTAIN_HR_INFO, e.getMessage());
        }
        service.queryExportData(dto,1,1);

        Page<PrintRecord> pageInfo = new Page<>(dto.getPage(), dto.getRows());
        List<PrintRecord> printRecordList = new ArrayList<>();
        PowerMockito.when(recordRepository.getList(any())).thenReturn(printRecordList);
    }
}