package com.zte.application.impl;

import com.zte.application.CustomerItemsService;
import com.zte.application.PushModelSnTestRecordService;
import com.zte.application.PushStdModelSnDataHandleService;
import com.zte.application.PushStdModelSnDataService;
import com.zte.application.TradeDataLogService;
import com.zte.application.cbom.FixBomCommonService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PushModelSnTestRecord;
import com.zte.domain.model.PushStdModelSnData;
import com.zte.domain.model.PushStdModelSnDataRepository;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.infrastructure.remote.OpenApiRemoteService;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.FinishedProductStorageDTO;
import com.zte.interfaces.dto.MdsFeedbackProductionStationFileDTO;
import com.zte.interfaces.dto.MdsFeedbackProductionStationTestingInfoDTO;
import com.zte.interfaces.dto.MdsProblemsDTO;
import com.zte.interfaces.dto.MdsRepairInfoDTO;
import com.zte.interfaces.dto.MdsRepairInformationDTO;
import com.zte.interfaces.dto.MdsStationsDTO;
import com.zte.interfaces.dto.PushModelSnTestRecordDTO;
import com.zte.interfaces.dto.PushStdModelSnDataDTO;
import com.zte.interfaces.dto.PushStdModelSnDataExtDTO;
import com.zte.interfaces.dto.PushStdModelSnDataHandleDTO;
import com.zte.interfaces.dto.PushStdModelSnDataPageQueryDTO;
import com.zte.interfaces.dto.SspTaskInfoDTO;
import com.zte.interfaces.dto.WholeMachineDataUploadDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;

/**
 * 标模任务条码推送信息表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-05-14 09:58:57
 */
@PrepareForTest({SpringContextUtil.class,RequestHeadValidationUtil.class})
public class PushStdModelSnDataServiceImplTest extends BaseTestCase {

    @InjectMocks
    private PushStdModelSnDataServiceImpl pushStdModelSnDataService;
    @Mock
    private PushStdModelSnDataRepository pushStdModelSnDataRepository;
    @Mock
    private PushModelSnTestRecordService pushModelSnTestRecordService;
    @Mock
    private MdsRemoteService mdsRemoteService;
    @Mock
    TradeDataLogService tradeDataLogService;
    @Mock
    EmailUtils emailUtils;

    private PushStdModelSnDataPageQueryDTO dto;

    @Mock
    private CustomerItemsService customerItemsService;

    @Mock
    private OpenApiRemoteService openApiRemoteService;
    @Mock
    private PushStdModelSnDataService service;
    @Mock
    private IdGenerator idGenerator;
    @Mock
    List<PushStdModelSnDataHandleService<?>> pushStdModelSnDataHandleServices;
    @Mock
    private FinishedProductStorageDataHandleServiceImpl finishedProductStorageDataHandleService;
    @Mock
    private FixBomCommonService fixBomCommonService;

    @Before
    public void setup() throws Exception {
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);
        PowerMockito.when(RequestHeadValidationUtil.validaFactoryId()).thenReturn("53");
        PowerMockito.field(PushStdModelSnDataServiceImpl.class, "preDays").set(pushStdModelSnDataService, 100);
        PowerMockito.field(PushStdModelSnDataServiceImpl.class, "customerSubName").set(pushStdModelSnDataService, "100");
        PowerMockito.field(PushStdModelSnDataServiceImpl.class, "limit").set(pushStdModelSnDataService, 1);

    }


    @Test
    public void whiteBox() throws Exception {
        // "openDetailList"
        Whitebox.invokeMethod(pushStdModelSnDataService, "openDetailList", null);

        List<FixBomDetailDTO> list = new LinkedList<>();
        FixBomDetailDTO a1 = new FixBomDetailDTO();
        list.add(a1);

        FixBomDetailDTO b2 = new FixBomDetailDTO();
        b2.setChildNodes(list);
        Whitebox.invokeMethod(pushStdModelSnDataService, "openDetailList", Collections.singletonList(b2));

        assertTrue(Objects.nonNull(pushStdModelSnDataService));
    }

    /* Started by AICoder, pid:x6c0f81e2e870b1142a60b6c509e6d9dc038a774 */
    @Test
    public void testBuildPushSnData_HandleServiceNotFound() throws Exception{
        List<PushStdModelSnDataHandleService<?>> pushStdModelSnDataHandleServices = new LinkedList<>();

        PowerMockito.field(PushStdModelSnDataServiceImpl.class,"pushStdModelSnDataHandleServices")
                .set(pushStdModelSnDataService,pushStdModelSnDataHandleServices);
        PushStdModelSnDataHandleDTO pushStdModelSnDataHandle = new PushStdModelSnDataHandleDTO();
        pushStdModelSnDataHandle.setCurrProcess("30");
        try {
            pushStdModelSnDataService.buildPushSnData(pushStdModelSnDataHandle);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAM_MISSING, e.getMessage());
        }

        pushStdModelSnDataHandleServices.add(finishedProductStorageDataHandleService);
        try {
            pushStdModelSnDataService.buildPushSnData(pushStdModelSnDataHandle);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAM_MISSING, e.getMessage());
        }

        PowerMockito.when(finishedProductStorageDataHandleService.match(Mockito.any()))
                .thenReturn(true);
        pushStdModelSnDataHandleServices.add(finishedProductStorageDataHandleService);
        try {
            pushStdModelSnDataService.buildPushSnData(pushStdModelSnDataHandle);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_PUSH_STD_MODEL_LOST, e.getMessage());
        }

        PushStdModelSnDataDTO snDataDTO = new PushStdModelSnDataDTO();
        PowerMockito.when(pushStdModelSnDataRepository.queryPushStdModelSn(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(snDataDTO);
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        PowerMockito.when(pushStdModelSnDataRepository.selectExtByPrimaryKey(Mockito.any()))
                .thenReturn(pushStdModelSnDataExt);

        try {
            pushStdModelSnDataService.buildPushSnData(pushStdModelSnDataHandle);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FIX_BOM_DETAIL_LOST, e.getMessage());
        }

        List<WipExtendIdentificationDTO> wipExtendIdentifications = new ArrayList<>();
        List<FixBomDetailDTO> list = new ArrayList<>();
        for (int i = 0; i < 15; i++) {
            FixBomDetailDTO a1 = new FixBomDetailDTO();
            String s = String.valueOf(i);
            if (i % 2 == 0) {
                a1.setBoxBomRequired("Y");
                a1.setItemNo(s);
                a1.setZteCode(s);
                a1.setBoxPriority(i);
            }
            list.add(a1);
            WipExtendIdentificationDTO b1 = new WipExtendIdentificationDTO();
            b1.setItemNo(s);
            wipExtendIdentifications.add(b1);
        }
        PowerMockito.when(fixBomCommonService.queryFixBomDetailByHeadId(Mockito.any())).thenReturn(list);
        PowerMockito.when(finishedProductStorageDataHandleService.validatePushMessageData(Mockito.any(), Mockito.any()))
                .thenReturn(true);
        pushStdModelSnDataService.buildPushSnData(pushStdModelSnDataHandle);

        PowerMockito.when(finishedProductStorageDataHandleService.validatePushMessageData(Mockito.any(), Mockito.any()))
                .thenReturn(false);
        try {
            pushStdModelSnDataService.buildPushSnData(pushStdModelSnDataHandle);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_RULE_ERROR, e.getMessage());
        }

        PowerMockito.when(finishedProductStorageDataHandleService.validatePushMessageData(Mockito.any(), Mockito.any()))
                .thenReturn(true);
        FinishedProductStorageDTO a1 = new FinishedProductStorageDTO();
        PowerMockito.when(finishedProductStorageDataHandleService.getPushMessageData(Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn(a1);
        pushStdModelSnDataService.buildPushSnData(pushStdModelSnDataHandle);

        pushStdModelSnDataHandle.setCurrProcess("40");
        pushStdModelSnDataService.buildPushSnData(pushStdModelSnDataHandle);

        pushStdModelSnDataHandle.setCurrProcess("30");
        pushStdModelSnDataHandle.setWipExtendIdentifications(wipExtendIdentifications);
        pushStdModelSnDataService.buildPushSnData(pushStdModelSnDataHandle);
    }
    /* Ended by AICoder, pid:x6c0f81e2e870b1142a60b6c509e6d9dc038a774 */

    @Test
    public void testQueryPage() {
        PowerMockito.when(pushStdModelSnDataRepository.selectPage(Mockito.any())).thenReturn(new ArrayList<PushStdModelSnData>());
        pushStdModelSnDataService.queryPage(new PushStdModelSnDataPageQueryDTO());
        Assert.assertNull(dto);
    }

    @Test
    public void testGetById() {
        PowerMockito.when(pushStdModelSnDataRepository.selectById(Mockito.any())).thenReturn(new PushStdModelSnData());
        pushStdModelSnDataService.getById(null);
        Assert.assertNull(dto);
    }

    @Test
    public void testAdd() {
        PowerMockito.when(pushStdModelSnDataRepository.insert(Mockito.any())).thenReturn(1L);
        pushStdModelSnDataService.add(new PushStdModelSnData());
        Assert.assertNull(dto);
    }

    @Test
    public void testUpdateById() {
        PowerMockito.when(pushStdModelSnDataRepository.updateById(Mockito.any())).thenReturn(1L);
        pushStdModelSnDataService.updateById(new PushStdModelSnData());
        Assert.assertNull(dto);
    }

    @Test
    public void testDeleteByIds() {
        PowerMockito.when(pushStdModelSnDataRepository.deleteByIds(Mockito.any())).thenReturn(1L);
        pushStdModelSnDataService.deleteByIds(new ArrayList());
        Assert.assertNull(dto);
    }

    @Test
    public void testCountExportTotal() {
        PowerMockito.when(pushStdModelSnDataRepository.selectCount(Mockito.any())).thenReturn(1);
        pushStdModelSnDataService.countExportTotal(new PushStdModelSnDataPageQueryDTO());
        Assert.assertNull(dto);
    }

    @Test
    public void testQueryExportData() {
        PowerMockito.when(pushStdModelSnDataRepository.selectPage(Mockito.any())).thenReturn(new ArrayList<PushStdModelSnData>());
        pushStdModelSnDataService.queryExportData(new PushStdModelSnDataPageQueryDTO(), 1, 10);
        Assert.assertNull(dto);
    }

    @Test
    public void batchInsertOrUpdate() {
        List<PushStdModelSnData> pushStdModelSnDataList = new ArrayList<>();
        pushStdModelSnDataService.batchInsertOrUpdate(pushStdModelSnDataList);
        Assert.assertNotNull(pushStdModelSnDataList);

        pushStdModelSnDataList.add(new PushStdModelSnData());
        pushStdModelSnDataService.batchInsertOrUpdate(pushStdModelSnDataList);
        Assert.assertNotNull(pushStdModelSnDataList);
    }

    /*Started by AICoder, pid:3c97715e0dh8df114a56086320697990e468ccf7*/
    @Test
    public void testPushCompleteMachineTestData_EmptyList() throws Exception {
        List<String> snList = Collections.emptyList();
        when(pushModelSnTestRecordService.getSysMap(anyString())).thenReturn(new HashMap<>());

        pushStdModelSnDataService.pushCompleteMachineTestData(snList);

        verify(pushModelSnTestRecordService, times(2)).getSysMap(anyString());
    }

    @Test
    public void testPushCompleteMachineTestData_NonEmptyListNoData() throws Exception {
        List<String> snList = Arrays.asList("SN1", "SN2");
        when(pushModelSnTestRecordService.getSysMap(anyString())).thenReturn(new HashMap<>());
        when(pushStdModelSnDataRepository.getNeedPushDataPage(any())).thenReturn(Collections.emptyList());

        pushStdModelSnDataService.pushCompleteMachineTestData(snList);

        verify(pushModelSnTestRecordService, times(2)).getSysMap(anyString());
        verify(pushStdModelSnDataRepository, times(1)).getNeedPushDataPage(any());
    }

    @Test
    public void testPushCompleteMachineTestData_WithData() throws Exception {
        List<String> snList = Arrays.asList("SN1", "SN2");
        Map<String, String> sysMap = new HashMap<>();
        sysMap.put("stationName", "Station Name");

        when(pushModelSnTestRecordService.getSysMap(anyString())).thenReturn(sysMap);
        when(pushStdModelSnDataRepository.getNeedPushDataPage(any())).thenReturn(null);

        pushStdModelSnDataService.pushCompleteMachineTestData(snList);

        verify(pushModelSnTestRecordService, times(2)).getSysMap(anyString());
        verify(pushStdModelSnDataRepository, atLeastOnce()).getNeedPushDataPage(any());
    }
    /*Ended by AICoder, pid:3c97715e0dh8df114a56086320697990e468ccf7*/

    /*Started by AICoder, pid:baab1mf9d3o1a2f1489a08ecd16e5f4c1e64baf1*/
    @Test
    public void testPushCompleteMachineTestData_emptyMdsFeedback() throws Exception {
        List<PushStdModelSnDataDTO> pushStdModelSnDataList = Collections.singletonList(new PushStdModelSnDataDTO());
        Map<String, String> sysMap = new HashMap<>();

        when(mdsRemoteService.feedbackOfProductionStationTestingInformation(anyList())).thenReturn(Collections.emptyList());

        pushStdModelSnDataService.pushCompleteMachineTestData(pushStdModelSnDataList, sysMap, sysMap);

        verify(mdsRemoteService).feedbackOfProductionStationTestingInformation(anyList());
        verifyNoMoreInteractions(openApiRemoteService, customerItemsService, pushModelSnTestRecordService);
    }

    @Test
    public void testPushCompleteMachineTestData_emptyWipExtendInfo() throws Exception {
        List<PushStdModelSnDataDTO> pushStdModelSnDataList = Collections.singletonList(new PushStdModelSnDataDTO() {{
            setFactoryId(55);
        }});
        Map<String, String> sysMap = new HashMap<>();

        when(mdsRemoteService.feedbackOfProductionStationTestingInformation(anyList())).thenReturn(Collections.singletonList(new MdsFeedbackProductionStationTestingInfoDTO()));
        when(openApiRemoteService.getWipExtendInfoByFormSnList(anyList(), anyString())).thenReturn(Collections.emptyList());

        pushStdModelSnDataService.pushCompleteMachineTestData(pushStdModelSnDataList, sysMap, sysMap);

        verify(mdsRemoteService).feedbackOfProductionStationTestingInformation(anyList());
    }

    @Test
    public void testPushCompleteMachineTestData_emptyCustomerItemsInfo() throws Exception {
        List<PushStdModelSnDataDTO> pushStdModelSnDataList = Collections.singletonList(new PushStdModelSnDataDTO() {{
            setFactoryId(55);
        }});
        Map<String, String> sysMap = new HashMap<>();

        when(mdsRemoteService.feedbackOfProductionStationTestingInformation(anyList())).thenReturn(Collections.singletonList(new MdsFeedbackProductionStationTestingInfoDTO()));
        when(openApiRemoteService.getWipExtendInfoByFormSnList(anyList(), anyString())).thenReturn(Collections.singletonList(new WipExtendIdentificationDTO() {{
            setFormSn("2");
        }}));
        when(customerItemsService.getCustomerItemsInfo(any(CustomerItemsDTO.class))).thenReturn(Collections.emptyList());

        pushStdModelSnDataService.pushCompleteMachineTestData(pushStdModelSnDataList, sysMap, sysMap);

        verify(mdsRemoteService).feedbackOfProductionStationTestingInformation(anyList());
        verify(customerItemsService).getCustomerItemsInfo(any(CustomerItemsDTO.class));
        verifyNoMoreInteractions(pushModelSnTestRecordService);
    }

    @Test
    public void testPushCompleteMachineTestData_noStations() throws Exception {
        List<PushStdModelSnDataDTO> pushStdModelSnDataList = Collections.singletonList(new PushStdModelSnDataDTO() {{
            setFactoryId(55);
        }});

        Map<String, String> sysMap = new HashMap<>();

        when(mdsRemoteService.feedbackOfProductionStationTestingInformation(anyList())).thenReturn(Collections.singletonList(new MdsFeedbackProductionStationTestingInfoDTO()));
        when(openApiRemoteService.getWipExtendInfoByFormSnList(anyList(), anyString())).thenReturn(Collections.singletonList(new WipExtendIdentificationDTO() {{
            setFormSn("2");
        }}));
        when(customerItemsService.getCustomerItemsInfo(any(CustomerItemsDTO.class))).thenReturn(Collections.singletonList(new CustomerItemsDTO()));

        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(eq("pushStdModelSnDataService"), any())).thenReturn(service);
        pushStdModelSnDataService.pushCompleteMachineTestData(pushStdModelSnDataList, sysMap, sysMap);

        verify(mdsRemoteService).feedbackOfProductionStationTestingInformation(anyList());
        verify(customerItemsService).getCustomerItemsInfo(any(CustomerItemsDTO.class));
        verifyNoMoreInteractions(pushModelSnTestRecordService);
        when(mdsRemoteService.feedbackOfProductionStationTestingInformation(anyList())).thenReturn(Collections.singletonList(new MdsFeedbackProductionStationTestingInfoDTO(){{setServerSn("sn");}}));

        List<SspTaskInfoDTO> sspTaskInfoDTOList = Arrays.asList(new SspTaskInfoDTO(){{setSn("sn");}});
        PowerMockito.when(mdsRemoteService.querySspTaskInfoForAli(anyList())).thenReturn(sspTaskInfoDTOList);
        pushStdModelSnDataService.pushCompleteMachineTestData(pushStdModelSnDataList, sysMap, sysMap);
        Assert.assertNotNull(sspTaskInfoDTOList);

        sspTaskInfoDTOList = Arrays.asList(new SspTaskInfoDTO(){{setSn("sn");setNodeNumber("CN");}});
        PowerMockito.when(mdsRemoteService.querySspTaskInfoForAli(anyList())).thenReturn(sspTaskInfoDTOList);
        pushStdModelSnDataService.pushCompleteMachineTestData(pushStdModelSnDataList, sysMap, sysMap);
        Assert.assertNotNull(sspTaskInfoDTOList);

        sspTaskInfoDTOList = Arrays.asList(new SspTaskInfoDTO(){{setSn("sn");setNodeNumber("CN1");}},new SspTaskInfoDTO(){{setSn("sn");setNodeNumber("CN2");}});
        PowerMockito.when(mdsRemoteService.querySspTaskInfoForAli(anyList())).thenReturn(sspTaskInfoDTOList);
        pushStdModelSnDataService.pushCompleteMachineTestData(pushStdModelSnDataList, sysMap, sysMap);
        Assert.assertNotNull(sspTaskInfoDTOList);
    }

    @Test
    public void testPushCompleteMachineTestData_withStations() throws Exception {
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(eq("pushStdModelSnDataService"), any())).thenReturn(service);
        List<PushStdModelSnDataDTO> pushStdModelSnDataList = Collections.singletonList(new PushStdModelSnDataDTO() {{
            setFactoryId(55);
        }});

        Map<String, String> sysMap = new HashMap<>();
        sysMap.put("station1", "Station 1");

        when(mdsRemoteService.feedbackOfProductionStationTestingInformation(anyList())).thenReturn(Collections.singletonList(new MdsFeedbackProductionStationTestingInfoDTO()));
        when(openApiRemoteService.getWipExtendInfoByFormSnList(anyList(), anyString())).thenReturn(Collections.singletonList(new WipExtendIdentificationDTO() {{
            setFormSn("2");
        }}));
        when(customerItemsService.getCustomerItemsInfo(any(CustomerItemsDTO.class))).thenReturn(Collections.singletonList(new CustomerItemsDTO()));
        when(mdsRemoteService.getRepairInformation(anyList())).thenReturn(Collections.singletonList(new MdsRepairInformationDTO()));
        when(mdsRemoteService.feedbackCompleteMachineTestingGenerateFile(anyString(), anyList(), anyBoolean())).thenReturn(Collections.singletonList(new MdsFeedbackProductionStationFileDTO()));
        when(pushModelSnTestRecordService.getByIdList(anyList())).thenReturn(Collections.emptyList());

        pushStdModelSnDataService.pushCompleteMachineTestData(pushStdModelSnDataList, sysMap, sysMap);

        Assert.assertNotNull(sysMap);
    }
    /*Ended by AICoder, pid:baab1mf9d3o1a2f1489a08ecd16e5f4c1e64baf1*/

    @Test
    public void assemblyData() throws Exception {
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        customerItemsDTOList.add(new CustomerItemsDTO() {{
            setZteCode("1");
            setProjectType("3");
        }});
        customerItemsDTOList.add(new CustomerItemsDTO() {{
            setZteCode("2");
            setProjectType("5");
        }});

        Map<String, List<WipExtendIdentificationDTO>> wipExtendMap = new HashMap<>();

        WholeMachineDataUploadDTO wholeMachineDataUploadDTO = new WholeMachineDataUploadDTO();
        List<PushModelSnTestRecordDTO> insertList = new ArrayList<>();
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        wholeMachineDataUploadDTO.setMdsDTO(new MdsFeedbackProductionStationTestingInfoDTO());

        Whitebox.invokeMethod(pushStdModelSnDataService, "assemblyData", wholeMachineDataUploadDTO, insertList, dataList);
        Assert.assertNotNull(wholeMachineDataUploadDTO);

        List<MdsStationsDTO> stationsList = Arrays.asList(new MdsStationsDTO() {{
            setStationId("id");
            setName("name");
        }}, new MdsStationsDTO() {{
            setStationId("id");
            setName("name1");
        }});
        wholeMachineDataUploadDTO.setMdsDTO(new MdsFeedbackProductionStationTestingInfoDTO() {{
            setStations(stationsList);
            setServerSn("sn");
        }});

        List<PushModelSnTestRecord> pushModelSnTestRecordList = new ArrayList<>();
        pushModelSnTestRecordList.add(new PushModelSnTestRecord() {{
            setStationId("id");setNodeSn("sn");
        }});
        PowerMockito.when(pushModelSnTestRecordService.getByIdList(anyList())).thenReturn(pushModelSnTestRecordList);
        SspTaskInfoDTO sspTaskInfoDTO = new SspTaskInfoDTO();
        sspTaskInfoDTO.setNodeSn("sn");
        wholeMachineDataUploadDTO.setSspTaskInfoDTO(sspTaskInfoDTO);
        Whitebox.invokeMethod(pushStdModelSnDataService, "assemblyData", wholeMachineDataUploadDTO, insertList, dataList);
        Assert.assertNotNull(wholeMachineDataUploadDTO);

        pushModelSnTestRecordList.clear();
        List<MdsFeedbackProductionStationFileDTO> stationFileDTOList = new ArrayList<>();
        PowerMockito.when(mdsRemoteService.feedbackCompleteMachineTestingGenerateFile(anyString(), anyList(), anyBoolean())).thenReturn(stationFileDTOList);
        PowerMockito.when(pushModelSnTestRecordService.getByIdList(anyList())).thenReturn(pushModelSnTestRecordList);
        Map<String, PushStdModelSnDataDTO> itemMap = new HashMap<>();
        wholeMachineDataUploadDTO.setItemMap(itemMap);
        Whitebox.invokeMethod(pushStdModelSnDataService, "assemblyData", wholeMachineDataUploadDTO, insertList, dataList);
        Assert.assertNotNull(wholeMachineDataUploadDTO);

        wholeMachineDataUploadDTO.setCustomerItemsDTOList(customerItemsDTOList);
        itemMap.put("sn1", new PushStdModelSnDataDTO() {{
            setItemNo("itemNo");
        }});
        wholeMachineDataUploadDTO.setItemMap(itemMap);
        Whitebox.invokeMethod(pushStdModelSnDataService, "assemblyData", wholeMachineDataUploadDTO, insertList, dataList);
        itemMap.put("sn", new PushStdModelSnDataDTO() {{
            setItemNo("itemNo");
        }});
        Assert.assertNotNull(wholeMachineDataUploadDTO);
        Map<String, MdsRepairInformationDTO> repairMap = new HashMap<>();
        wholeMachineDataUploadDTO.setRepairMap(repairMap);
        Map<String, String> sysMap = new HashMap<>();
        sysMap.put("name", "imesName");
        wholeMachineDataUploadDTO.setSysMap(sysMap);
        wholeMachineDataUploadDTO.setSysMapForReasonType(sysMap);
        Whitebox.invokeMethod(pushStdModelSnDataService, "assemblyData", wholeMachineDataUploadDTO, insertList, dataList);
        Assert.assertNotNull(wholeMachineDataUploadDTO);

        List<CustomerItemsDTO> motherboardList = new ArrayList<>();
        motherboardList.add(new CustomerItemsDTO());
        stationFileDTOList.add(new MdsFeedbackProductionStationFileDTO() {{
            setServerSn("sn");
            setStationId("id");
        }});
        PowerMockito.when(mdsRemoteService.feedbackCompleteMachineTestingGenerateFile(anyString(), anyList(), anyBoolean())).thenReturn(stationFileDTOList);
        PowerMockito.when(pushModelSnTestRecordService.getByIdList(anyList())).thenReturn(pushModelSnTestRecordList);

        Whitebox.invokeMethod(pushStdModelSnDataService, "assemblyData", wholeMachineDataUploadDTO, insertList, dataList);
        Assert.assertNotNull(wholeMachineDataUploadDTO);
    }

    @Test
    public void setMdsRepairInfoDTO() throws Exception {
        MdsStationsDTO station = new MdsStationsDTO();
        station.setStationId("id");
        Map<String, MdsRepairInfoDTO> repairInfoDTOMap = new HashMap<>();
        repairInfoDTOMap.put("id", new MdsRepairInfoDTO());
        PushModelSnTestRecordDTO pushModelSnTestRecordDTO = new PushModelSnTestRecordDTO();
        Whitebox.invokeMethod(pushStdModelSnDataService, "setMdsRepairInfoDTO", station, repairInfoDTOMap, pushModelSnTestRecordDTO);
        Assert.assertNotNull(pushModelSnTestRecordDTO);
    }

    @Test
    public void setType() throws Exception {
        PushModelSnTestRecordDTO pushModelSnTestRecordDTO = new PushModelSnTestRecordDTO();
        Whitebox.invokeMethod(pushStdModelSnDataService, "setType", new CustomerItemsDTO(), pushModelSnTestRecordDTO);
        Assert.assertNotNull(pushModelSnTestRecordDTO);
    }

    @Test
    public void getRequestId() throws Exception {
        PushModelSnTestRecordDTO pushModelSnTestRecordDTO = new PushModelSnTestRecordDTO();
        PowerMockito.field(PushStdModelSnDataServiceImpl.class, "requestIdPrefixSwitch").set(pushStdModelSnDataService, true);
        Whitebox.invokeMethod(pushStdModelSnDataService, "getRequestId", new MdsStationsDTO());
        Assert.assertNotNull(pushModelSnTestRecordDTO);
    }

    @Test
    public void insertAndSendToB2B() throws Exception {
        List<CustomerDataLogDTO> dataList = new ArrayList<>();
        dataList.add(new CustomerDataLogDTO());
        Whitebox.invokeMethod(pushStdModelSnDataService, "insertAndSendToB2B", new ArrayList<>(), dataList);
        Assert.assertNotNull(dataList);

        Whitebox.invokeMethod(pushStdModelSnDataService, "insertAndSendToB2B", new ArrayList<>(), new ArrayList<>());
        Assert.assertNotNull(dataList);
    }

    @Test
    public void getRepairInfoDTOMap() throws Exception {
        MdsFeedbackProductionStationTestingInfoDTO mdsDTO = new MdsFeedbackProductionStationTestingInfoDTO();
        mdsDTO.setServerSn("sn");
        Map<String, MdsRepairInformationDTO> repairMap = new HashMap<>();
        repairMap.put("sn", new MdsRepairInformationDTO());
        Whitebox.invokeMethod(pushStdModelSnDataService, "getRepairInfoDTOMap", mdsDTO,repairMap);
        Assert.assertNotNull(mdsDTO);
        List<MdsRepairInfoDTO> repairInfoList = new ArrayList<>();
        repairInfoList.add(new MdsRepairInfoDTO(){{setProblemRequestId("id");}});
        repairMap.put("sn", new MdsRepairInformationDTO(){{setRepairInfoList(repairInfoList);}});
        Whitebox.invokeMethod(pushStdModelSnDataService, "getRepairInfoDTOMap", mdsDTO,repairMap);
        Assert.assertNotNull(mdsDTO);

    }

    @Test
    public void setPhenomenonDetail() throws Exception {
        MdsStationsDTO mdsStationsDTO = new MdsStationsDTO();
        List<MdsProblemsDTO> problems = new ArrayList<>();
        problems.add(new MdsProblemsDTO());
        mdsStationsDTO.setProblems(problems);
        Whitebox.invokeMethod(pushStdModelSnDataService, "setPhenomenonDetail", mdsStationsDTO, new PushModelSnTestRecordDTO());
        Assert.assertNotNull(problems);

    }

    @Test
    public void setActionInfo() throws Exception {
        MdsStationsDTO mdsStationsDTO = new MdsStationsDTO();
        List<MdsProblemsDTO> problems = new ArrayList<>();
        problems.add(new MdsProblemsDTO());
        mdsStationsDTO.setProblems(problems);
        Whitebox.invokeMethod(pushStdModelSnDataService, "setActionInfo", mdsStationsDTO, new PushModelSnTestRecordDTO(),new HashMap<>());
        Assert.assertNotNull(problems);

    }

    @Test
    public void getCustomerItemsDTO() throws Exception {
        List<CustomerItemsDTO> dataList = new ArrayList<>();
        dataList.add(new CustomerItemsDTO(){{setZteCode("no1");}});
        dataList.add(new CustomerItemsDTO(){{setZteCode("no");setProjectType("4");}});
        dataList.add(new CustomerItemsDTO(){{setZteCode("no");setProjectType("3");}});

        Whitebox.invokeMethod(pushStdModelSnDataService, "getCustomerItemsDTO", dataList,new PushStdModelSnDataDTO(){{setItemNo("no");}});
        Assert.assertNotNull(dataList);

    }

    @Test
    public void setTestTime() throws Exception {
        PushModelSnTestRecordDTO pushModelSnTestRecordDTO = new PushModelSnTestRecordDTO();
        PowerMockito.field(PushStdModelSnDataServiceImpl.class, "requestIdPrefixSwitch").set(pushStdModelSnDataService, true);
        Whitebox.invokeMethod(pushStdModelSnDataService, "setTestTime", new MdsStationsDTO(),pushModelSnTestRecordDTO);
        Assert.assertNotNull(pushModelSnTestRecordDTO);

        PowerMockito.field(PushStdModelSnDataServiceImpl.class, "requestIdPrefixSwitch").set(pushStdModelSnDataService, false);
        Whitebox.invokeMethod(pushStdModelSnDataService, "setTestTime", new MdsStationsDTO(),pushModelSnTestRecordDTO);
        Assert.assertNotNull(pushModelSnTestRecordDTO);

        PowerMockito.field(PushStdModelSnDataServiceImpl.class, "requestIdPrefixSwitch").set(pushStdModelSnDataService, false);
        Whitebox.invokeMethod(pushStdModelSnDataService, "setTestTime", new MdsStationsDTO(){{setStartTime(178985585596L);setEndTime(178985585596L);}},pushModelSnTestRecordDTO);
        Assert.assertNotNull(pushModelSnTestRecordDTO);
    }

    @Test
    public void getDirectiveNumber() throws Exception {
        PushStdModelSnDataDTO pushStdModelSnDataDTO = new PushStdModelSnDataDTO(){{setItemNo("no");}};
        Whitebox.invokeMethod(pushStdModelSnDataService, "getDirectiveNumber",pushStdModelSnDataDTO);
        Assert.assertNotNull(pushStdModelSnDataDTO);
        pushStdModelSnDataDTO.setBillNo("no");
        Whitebox.invokeMethod(pushStdModelSnDataService, "getDirectiveNumber",pushStdModelSnDataDTO);
        Assert.assertNotNull(pushStdModelSnDataDTO);

    }

    @Test
    public void getConvertDateToString() throws Exception {
        PushStdModelSnDataDTO pushStdModelSnDataDTO = new PushStdModelSnDataDTO(){{setItemNo("no");}};
        Whitebox.invokeMethod(pushStdModelSnDataService, "getConvertDateToString",null);
        Assert.assertNotNull(pushStdModelSnDataDTO);
        pushStdModelSnDataDTO.setBillNo("no");
        Whitebox.invokeMethod(pushStdModelSnDataService, "getConvertDateToString",1012255555L);
        Assert.assertNotNull(pushStdModelSnDataDTO);

    }

    @Test
    public void getWipExtendIdentificationDTOS() throws Exception {
        List<WipExtendIdentificationDTO> tempWipTendItemList = new ArrayList<>();
        List<String> itemNoForMotherboard = new ArrayList();
        itemNoForMotherboard.add("1");
        Whitebox.invokeMethod(pushStdModelSnDataService, "getWipExtendIdentificationDTOS",tempWipTendItemList,itemNoForMotherboard);
        Assert.assertNotNull(tempWipTendItemList);

        tempWipTendItemList.add(new WipExtendIdentificationDTO(){{setItemNo("2");}});
        tempWipTendItemList.add(new WipExtendIdentificationDTO(){{setItemNo("1");}});
        Whitebox.invokeMethod(pushStdModelSnDataService, "getWipExtendIdentificationDTOS",tempWipTendItemList,itemNoForMotherboard);
        Assert.assertNotNull(tempWipTendItemList);

    }

    @Test
    public void setOssFileKey() throws Exception {
        Map<String, List<MdsFeedbackProductionStationFileDTO>> map = new HashMap<>();
        map.put("id",Arrays.asList(new MdsFeedbackProductionStationFileDTO()));
        map.put("id1",Arrays.asList(new MdsFeedbackProductionStationFileDTO()));
        Whitebox.invokeMethod(pushStdModelSnDataService, "setOssFileKey",new MdsStationsDTO(){{setStationId("id");}},map,new PushModelSnTestRecordDTO());
        Assert.assertNotNull(map);

        Whitebox.invokeMethod(pushStdModelSnDataService, "setOssFileKey",new MdsStationsDTO(){{setStationId("id2");}},map,new PushModelSnTestRecordDTO());
        Assert.assertNotNull(map);

        map.put("id2",Arrays.asList(new MdsFeedbackProductionStationFileDTO(){{setLogName("sn");}}));
        Whitebox.invokeMethod(pushStdModelSnDataService, "setOssFileKey",new MdsStationsDTO(){{setStationId("id2");}},map,new PushModelSnTestRecordDTO(){{setNodeSn("sn");}});
        Assert.assertNotNull(map);

    }

    @Test
    public void setResult() throws Exception {
        MdsStationsDTO station = new MdsStationsDTO();
        PushModelSnTestRecordDTO pushModelSnTestRecordDTO = new PushModelSnTestRecordDTO();
        station.setState(Constant.PASS_STR);
        Whitebox.invokeMethod(pushStdModelSnDataService, "setResult",station,pushModelSnTestRecordDTO);
        Assert.assertNotNull(station);
        station.setState(Constant.PASS_STR+"1");
        Whitebox.invokeMethod(pushStdModelSnDataService, "setResult",station,pushModelSnTestRecordDTO);
        Assert.assertNotNull(station);

    }
    @Test
    public void reportRecords() throws Exception {
        PowerMockito.when(pushStdModelSnDataRepository.reportRecords(any())).thenReturn(null);
        List<PushStdModelSnDataDTO> pushStdModelSnDataDTOS = pushStdModelSnDataService.reportRecords(any());
        Assert.assertNull(pushStdModelSnDataDTOS);

    }

    @Test
    public void getRepairSnList() throws Exception {
        List<MdsFeedbackProductionStationTestingInfoDTO> mdsList = new ArrayList<>();
        mdsList.add(new MdsFeedbackProductionStationTestingInfoDTO());
        MdsStationsDTO station = new MdsStationsDTO();
        mdsList.add(new MdsFeedbackProductionStationTestingInfoDTO(){{setStations(Arrays.asList(station));}});
        MdsStationsDTO station1 = new MdsStationsDTO();
        station1.setState(Constant.PASS_STR);

        mdsList.add(new MdsFeedbackProductionStationTestingInfoDTO(){{setStations(Arrays.asList(station1));}});
        Whitebox.invokeMethod(pushStdModelSnDataService, "getRepairSnList",mdsList);
        Assert.assertNotNull(mdsList);
        MdsStationsDTO station2 = new MdsStationsDTO();
        station2.setState(Constant.F);
        mdsList.add(new MdsFeedbackProductionStationTestingInfoDTO(){{setStations(Arrays.asList(station1,station2));}});
        Whitebox.invokeMethod(pushStdModelSnDataService, "getRepairSnList",mdsList);
        Assert.assertNotNull(mdsList);

    }

    @Test
    public void getVirtualSn_WhenFormSnExists_ReturnsVirtualSn() {
        // 准备模拟数据
        String formSn = "FORM123";
        String expectedVirtualSn = "VSN456";

        // 设置模拟行为
        when(pushStdModelSnDataRepository.getVirtualSn(formSn)).thenReturn(expectedVirtualSn);

        // 执行方法
        String actualVirtualSn = pushStdModelSnDataService.getVirtualSn(formSn);

        // 验证结果
        assertEquals(expectedVirtualSn, actualVirtualSn);
        verify(pushStdModelSnDataRepository).getVirtualSn(formSn);
    }

    @Test
    public void getVirtualSn_WhenFormSnNotExists_ReturnsNull() {
        String formSn = "FORM789";

        // 模拟返回null的情况
        when(pushStdModelSnDataRepository.getVirtualSn(formSn)).thenReturn(null);

        String result = pushStdModelSnDataService.getVirtualSn(formSn);

        assertNull(result);
        verify(pushStdModelSnDataRepository).getVirtualSn(formSn);
    }

    @Test
    public void testDealData_normal() throws Exception {
        List<PushStdModelSnDataDTO> dataList = new ArrayList<>();
        Map<String, String> sysMap = new HashMap<>();
        Map<String, String> sysMapForReasonType = new HashMap<>();
        Whitebox.invokeMethod(pushStdModelSnDataService,"pushCompleteMachineTestData",dataList, sysMap, sysMapForReasonType,new StringBuilder());
        Assert.assertNotNull(dataList);
    }

    @Test
    public void sendEmail() throws Exception {
        String errorStr = "";
        Whitebox.invokeMethod(pushStdModelSnDataService,"sendEmail","");
        Whitebox.invokeMethod(pushStdModelSnDataService,"sendEmail","2");
        PowerMockito.field(PushStdModelSnDataServiceImpl.class, "sendEmailer").set(pushStdModelSnDataService, "100");
        Whitebox.invokeMethod(pushStdModelSnDataService,"sendEmail","2");
        Assert.assertNotNull(errorStr);
    }
}
