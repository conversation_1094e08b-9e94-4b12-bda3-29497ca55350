package com.zte.application.impl;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.PrintRecordService;
import com.zte.common.HttpSoapClientHelper;
import com.zte.common.model.MessageId;
import com.zte.domain.model.PdfConvertResult;
import com.zte.domain.model.PrintTemplateInfo;
import com.zte.domain.model.PrintTemplateInfoRepository;
import com.zte.domain.model.UrlConfig;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.doc.WordProcesser;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/11/23 19:17
 * @Description
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({FileUtils.class,HttpSoapClientHelper.class,PrintTemplateInfoServiceImpl.class,PDDocument.class })
public class PrintTemplateInfoServiceImplTest {

    @InjectMocks
    private PrintTemplateInfoServiceImpl service;

    @Mock
    private PrintTemplateInfoRepository repository;
    @Mock
    private CloudDiskHelper cloudDiskHelper;
    @Mock
    private WordProcesser wordProcesser;
    @Mock
    private PDDocument pdDocument;
    @Mock
    private PDFRenderer pdfRenderer;
    @Mock
    private UrlConfig urlConfig;
    @Mock
    private FileInputStream inputStream;
    @Mock
    private PrintRecordService printRecordService;
    @Mock
    private File file;

    @Test
    public void testGenPrintUrl() throws Exception {
        HashMap map = new HashMap();
        map.put("printType", "1");
        map.put("printScene", "2");
        map.put("lpn", "2");
        List<PrintTemplateInfo> printTemplateInfoList = new ArrayList<>();
        PowerMockito.when(repository.queryTemplateInfoByTypeAndScene(Mockito.any())).thenReturn(printTemplateInfoList);
        try {
            service.genPrintUrl(map, "0000");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_NOT_EXIST, e.getMessage());
        }

        printTemplateInfoList.add(new PrintTemplateInfo(){{setPrintTemplateId("1");setAttribute1("lpn,350,35");}});
        PowerMockito.when(repository.queryTemplateInfoByTypeAndScene(Mockito.any())).thenReturn(printTemplateInfoList);

        PowerMockito.mockStatic(FileUtils.class);
        PowerMockito.when(FileUtils.checkFilePath(Mockito.any())).thenReturn(true);

        PowerMockito.doNothing().when(cloudDiskHelper).downloadFile(Mockito.any(),Mockito.any(),Mockito.any());

        PowerMockito.whenNew(WordProcesser.class).withNoArguments().thenReturn(wordProcesser);
        PowerMockito.doNothing().doThrow(Exception.class).when(wordProcesser).init(Mockito.any());
        PowerMockito.doNothing().when(wordProcesser).replace(Mockito.any(),Mockito.any());
        PowerMockito.doNothing().when(wordProcesser).finish(Mockito.any());

        PowerMockito.when(urlConfig.getPdfConvertUrl()).thenReturn("");
        PowerMockito.whenNew(FileInputStream.class).withAnyArguments().thenReturn(inputStream);
        PowerMockito.mockStatic(HttpSoapClientHelper.class);
        PdfConvertResult pdfConvertResult = new PdfConvertResult();
        pdfConvertResult.setSuccess(false);
        PowerMockito.when(HttpSoapClientHelper.formDataUploadFile(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(JSONObject.toJSONString(pdfConvertResult));

        PowerMockito.whenNew(File.class).withAnyArguments().thenReturn(file);
        PowerMockito.mockStatic(PDDocument.class);
        PowerMockito.when(PDDocument.load(file)).thenReturn(pdDocument);
        PowerMockito.whenNew(PDFRenderer.class).withArguments(pdDocument).thenReturn(pdfRenderer);

        PowerMockito.doReturn("").when(cloudDiskHelper).fileUpload(Mockito.anyString(),Mockito.anyString(),Mockito.anyInt());
        PowerMockito.doReturn("").when(cloudDiskHelper).getFileDownloadUrl(Mockito.any(),Mockito.any(),Mockito.any());
        PowerMockito.doReturn(new ServiceData()).when(printRecordService).insertRecord(Mockito.any());
        Assert.assertNotNull(service.genPrintUrl(map,"0000"));
    }
}
