package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.OfflineExportService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.SpRecoverImportDTO;
import com.zte.interfaces.dto.SpSpecialityParamItemPageQueryDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.FileUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/10/20 10:06
 * @Description
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({FileUtils.class, RedisHelper.class})
public class SpSpecialityParamItemServiceImplTest extends BaseTestCase {

    @InjectMocks
    private SpSpecialityParamItemServiceImpl service;
    @Mock
    private SpSpecialityParamItemRepository repository;
    @Mock
    private SpRecoveryRepository spRecoveryRepository;
    @Mock
    private SpTemplateItemRepository spTemplateItemRepository;
    @Mock
    private SpSpecialityParamRepository spSpecialityParamRepository;
    @Mock
    private RedisTemplate<String, String> redisTemplate;
    @Mock
    private ValueOperations<String, String> valueOperations;
    @Mock
    private RedisLock redisLock;
    @Mock
    private OfflineExportService offlineExportService;
    @Mock
    private CloudDiskHelper cloudDiskHelper;
    @Mock
    private MdsRemoteService mdsRemoteService;

    @Test
    public void testGetMaxBarcode() {
        PowerMockito.when(repository.selectMaxBarcode(Mockito.any())).thenReturn(1L);
        Assert.assertNotNull(service.getMaxBarcode("test"));
    }

    @Test
    public void testQueryPage() {
        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(1L);
        PowerMockito.when(repository.queryPage(Mockito.any())).thenReturn(new ArrayList<SpSpecialityParamItem>());
        PageRows<SpSpecialityParamItem> pageRows = service.queryPage(new SpSpecialityParamItemPageQueryDTO());
        Assert.assertTrue(pageRows.getRows().size() >= 0);
    }

    @Test
    public void testAddBatch() {
        PowerMockito.when(repository.insertBatch(Mockito.any())).thenReturn(1L);
        service.addBatch(new ArrayList<SpSpecialityParamItem>());
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void testGetImportMessage() {
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        PowerMockito.when(valueOperations.get(Mockito.any())).thenReturn("");
        Assert.assertNotNull(service.getImportMessage("test"));
    }

    @Test
    public void testCommitRecover() {
        PowerMockito.when(spSpecialityParamRepository.selectById(Mockito.any())).thenReturn(new SpSpecialityParam() {{
            setTaskId("test");
            setIsRecovery(0);
        }});
        PowerMockito.when(spSpecialityParamRepository.updateById(Mockito.any())).thenReturn(1L);
        PowerMockito.when(spRecoveryRepository.recover(Mockito.any())).thenReturn(1L);
        service.commitRecover("test");

        PowerMockito.when(spSpecialityParamRepository.selectById(Mockito.any())).thenReturn(new SpSpecialityParam() {{
            setTaskId("test");
            setIsRecovery(1);
        }});
        try {
            service.commitRecover("test");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.RECOVERY_FINISHED,e.getMessage());
        }
    }


    @Test
    public void testExportExcel() throws Exception {
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(1L);
        PowerMockito.when(spSpecialityParamRepository.selectById(Mockito.any())).thenReturn(new SpSpecialityParam() {{
            setTaskId("test");
            setIsRecovery(0);
        }});

        List<SpTemplateItem> list = new ArrayList<>();
        list.add(new SpTemplateItem() {{
            setItemId("");
            setParamName("test");
        }});
        PowerMockito.when(spTemplateItemRepository.queryList(Mockito.any())).thenReturn(list);
        List<SpSpecialityParamItem> list2 = new ArrayList<SpSpecialityParamItem>();
        list2.add(new SpSpecialityParamItem() {{
            setItemData("{}");
        }});
        PowerMockito.when(repository.queryPage(Mockito.any())).thenReturn(list2);

//        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
//        PowerMockito.when(redisLock.lock()).thenReturn(true);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);

        PowerMockito.doNothing().when(offlineExportService).add(Mockito.any());

        service.exportExcel(response, "");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void testExportExcel2() throws Exception {
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(NumConstant.LONG_ZERO);

        try {
            service.exportExcel(response, "");
        } catch (Exception er) {
            Assert.assertEquals( MessageId.DATA_IS_EMPTY, er.getMessage());
        }
        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(1L);
        PowerMockito.when(spSpecialityParamRepository.selectById(Mockito.any())).thenReturn(new SpSpecialityParam() {{
            setTaskId("test");
            setIsRecovery(0);
        }});

        List<SpTemplateItem> list = new ArrayList<>();
        list.add(new SpTemplateItem() {{
            setItemId("");
            setParamName("test");
        }});
        PowerMockito.when(spTemplateItemRepository.queryList(Mockito.any())).thenReturn(list);
        List<SpSpecialityParamItem> list2 = new ArrayList<SpSpecialityParamItem>();
        list2.add(new SpSpecialityParamItem() {{
            setItemData("{}");
        }});
        PowerMockito.when(repository.queryPage(Mockito.any())).thenReturn(list2);

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);

        PowerMockito.doNothing().when(offlineExportService).add(Mockito.any());
        try {
            service.exportExcel(response, "");
        } catch (Exception er) {
            Assert.assertEquals(er.getMessage(), er.getMessage());
        }
    }


    @Test
    public void epxortThread() throws Exception {
        PowerMockito.mockStatic(FileUtils.class);
        PowerMockito.when(FileUtils.createFilePathAndCheck(Mockito.any())).thenReturn("/usr/xx.xlsx");

        PowerMockito.doNothing().when(offlineExportService).updateById(Mockito.any());
        PowerMockito.doReturn(new SpSpecialityParam()).when(spSpecialityParamRepository).selectById(Mockito.any());
        List<SpTemplateItem> list = new ArrayList<>();
        list.add(new SpTemplateItem() {{
            setItemId("");
            setParamName("test");
        }});
        PowerMockito.when(spTemplateItemRepository.queryList(Mockito.any())).thenReturn(list);
        List<SpSpecialityParamItem> list2 = new ArrayList<SpSpecialityParamItem>();
        list2.add(new SpSpecialityParamItem() {{
            setItemData("{}");
        }});
        PowerMockito.when(repository.queryPage(Mockito.any())).thenReturn(list2);
        PowerMockito.doReturn("").when(cloudDiskHelper).fileUpload(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt());
        Assert.assertNotNull(list);
    }

    @Test
    public void epxortThread2() throws Exception {
        PowerMockito.doThrow(new MesBusinessException("", "")).when(spSpecialityParamRepository).selectById(Mockito.any());
        try {
            Whitebox.invokeMethod(service, "exportThread", "test", new SpSpecialityParamItemPageQueryDTO(), 10L, "10", redisLock, new OfflineExport());
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void checkAndResetSpItem() throws Exception {
        PowerMockito.when(repository.selectOneBySpecialityParamId(Mockito.any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(service, "checkAndResetSpItem", new SpRecoverImportDTO(){{setSpecialityParamId("test");}});
        } catch (Exception e) {
            Assert.assertEquals(null,e.getMessage());
        }
    }

    @Test
    public void getData() throws Exception {
        try {
            Whitebox.invokeMethod(service, "getData", Lists.newArrayList(), Lists.newArrayList());
        } catch (Exception e) {
            Assert.assertEquals("RetCode.ServerError", e.getMessage());
        }
    }

    @Test
    public void importRecoverExcel() throws Exception {
        SpRecoverImportDTO spRecoverImportDTO = new SpRecoverImportDTO();
        try {
            service.importRecoverExcel(spRecoverImportDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.IMPORT_DATA_IS_EMPTY, e.getMessage());
        }

        try {
            MultipartFile multipartFile = new MockMultipartFile("test", "test", Constant.STRING_EMPTY, "fsdf".getBytes());
            spRecoverImportDTO.setFile(multipartFile);
            service.importRecoverExcel(spRecoverImportDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DATA_IS_EMPTY, e.getMessage());
        }

        try {
            MultipartFile multipartFile2 = new MockMultipartFile("test", "tes", "aaaa", "".getBytes());
            spRecoverImportDTO.setFile(multipartFile2);
            service.importRecoverExcel(spRecoverImportDTO);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void syncDataToMds() throws Exception {
        String spSpecialityParamJson = "{\"applyQty\":0,\"applyTask\":\"20250619000014\",\"createBy\":\"10338918\",\"createDate\":1750335748432,\"destinedArea\":\"\",\"enabledFlag\":\"Y\",\"firstEigenValue\":false,\"fixedStr\":\"\",\"isRecovery\":0,\"itemCode\":\"123699991088\",\"itemName\":\"\",\"itemNum\":6,\"itemUnit\":\"\",\"itemVersion\":\"\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317982,\"operator\":\"\",\"productBigClass\":\"2\",\"productSmallClass\":\"7\",\"productionUnit\":\"0\",\"progress\":100,\"specialityParamId\":\"20250619000018\",\"syncMdsStatus\":\"N\",\"taskId\":\"20250619000014\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"templateItem\":\"DeviceType,MacStart,MacEnd,STBID\",\"templateName\":\"B866V2M泰国\",\"usageScope\":\"2\"}";
        SpSpecialityParam spSpecialityParam = JSON.parseObject(spSpecialityParamJson, SpSpecialityParam.class);
        Mockito.when(spSpecialityParamRepository.selectById(Mockito.any())).thenReturn(spSpecialityParam);
        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(6L);
        String templateItemListJson = "[]";
        List<SpTemplateItem> templateItemList = JSON.parseArray(templateItemListJson, SpTemplateItem.class);
        Mockito.when(spTemplateItemRepository.queryList(Mockito.anyString())).thenReturn(templateItemList);

        String itemListJson = "[{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"3d536acc-16bf-4256-b911-e721b021e5ca\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F1\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F1\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"c0bed385-08e6-405d-b19a-cd203b814790\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F2\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F2\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"e24e408d-493d-497e-98be-e6a35115ef1e\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F3\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F3\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"3836d3cf-1e46-4efb-8355-311e33c6a905\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F4\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F4\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"47ceaf9a-9361-4279-a8ae-afaa60452d52\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F5\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F5\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"5b6dc780-561b-4671-9c66-49181b70f829\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F6\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F6\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"}]";
        List<SpSpecialityParamItem> itemList = JSON.parseArray(itemListJson, SpSpecialityParamItem.class);
        Mockito.when(repository.queryPage(Mockito.any())).thenReturn(itemList);
        Mockito.when(mdsRemoteService.uploadFmParamMesFileInfo(Mockito.any(), Mockito.anyString(), Mockito.anyString())).thenReturn("DHOME/MesParamFileSave/20250619000014/2025061900001820250619000014&123699991088&6.xlsx");
        Mockito.when(spSpecialityParamRepository.updateById(Mockito.any())).thenReturn(1L);
//        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
//        PowerMockito.when(redisLock.lock()).thenReturn(true);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        service.syncDataToMds("20250619000018");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void syncDataToMds1() throws Exception {
        try {
            service.syncDataToMds("");
        } catch (Exception er) {
            Assert.assertEquals(MessageId.SYNCMDS_SPECIALITYPARAMID_EMPTY_ERROR, er.getMessage());
        }
        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(0L);
        try {
            service.syncDataToMds("20250619000018");
        } catch (Exception er) {
            Assert.assertEquals(MessageId.DATA_IS_EMPTY, er.getMessage());
        }
    }

    @Test
    public void syncDataToMds2() throws Exception {
        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(6L);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(false);
        PowerMockito.doNothing().when(offlineExportService).add(Mockito.any());
        try {
            service.syncDataToMds("20250619000018");
        } catch (Exception er) {
            Assert.assertEquals(er.getMessage(), er.getMessage());
        }
    }

    @Test
    public void syncDataToMds3() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        String spSpecialityParamJson = "{\"applyQty\":0,\"applyTask\":\"20250619000014\",\"createBy\":\"10338918\",\"createDate\":1750335748432,\"destinedArea\":\"\",\"enabledFlag\":\"Y\",\"firstEigenValue\":false,\"fixedStr\":\"\",\"isRecovery\":0,\"itemCode\":\"123699991088\",\"itemName\":\"\",\"itemNum\":6,\"itemUnit\":\"\",\"itemVersion\":\"\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317982,\"operator\":\"\",\"productBigClass\":\"2\",\"productSmallClass\":\"7\",\"productionUnit\":\"0\",\"progress\":100,\"specialityParamId\":\"20250619000018\",\"syncMdsStatus\":\"N\",\"taskId\":\"20250619000014\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"templateItem\":\"\",\"templateName\":\"B866V2M泰国\",\"usageScope\":\"2\"}";
        SpSpecialityParam spSpecialityParam = JSON.parseObject(spSpecialityParamJson, SpSpecialityParam.class);
        Mockito.when(spSpecialityParamRepository.selectById(Mockito.any())).thenReturn(spSpecialityParam);
        PowerMockito.when(repository.countPage(Mockito.any())).thenReturn(6L);
        String templateItemListJson = "[{\"createBy\":\"10338918\",\"createTime\":1750323202641,\"enabledFlag\":\"Y\",\"generationMethod\":\"文本\",\"itemId\":\"5e9991c6-f769-4213-a9cc-218dc007ab89\",\"orderNum\":1,\"paramName\":\"DeviceType\",\"paramRule\":\"Text(B866V2M)\",\"paramType\":\"赋值\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"updateBy\":\"10338918\",\"updateTime\":1750381979000},{\"createBy\":\"10338918\",\"createTime\":1750381534872,\"enabledFlag\":\"Y\",\"generationMethod\":\"MacStart\",\"itemId\":\"3c2fb6fc-1f07-412f-8e30-683fba7e1898\",\"orderNum\":2,\"paramName\":\"MacStart\",\"paramRule\":\"MacStart()\",\"paramType\":\"MAC\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"updateBy\":\"10338918\",\"updateTime\":1750381979000},{\"createBy\":\"10338918\",\"createTime\":1750381534872,\"enabledFlag\":\"Y\",\"generationMethod\":\"MacEnd\",\"itemId\":\"b5562b50-b0db-4531-a084-d4f0fdd1e902\",\"orderNum\":3,\"paramName\":\"MacEnd\",\"paramRule\":\"MacEnd(1)\",\"paramType\":\"MAC\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"updateBy\":\"10338918\",\"updateTime\":1750381979000},{\"createBy\":\"10338918\",\"createTime\":1750381979395,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"3e977828-e43c-4eb4-8127-a6219b8b9307\",\"orderNum\":4,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart,1004-990070-636-0-0000,Y)\",\"paramType\":\"STBID\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"updateBy\":\"10338918\",\"updateTime\":1750381979395}]";
        List<SpTemplateItem> templateItemList = JSON.parseArray(templateItemListJson, SpTemplateItem.class);
        Mockito.when(spTemplateItemRepository.queryList(Mockito.anyString())).thenReturn(templateItemList);

        String itemListJson = "[{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"3d536acc-16bf-4256-b911-e721b021e5ca\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F1\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F1\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"c0bed385-08e6-405d-b19a-cd203b814790\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F2\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F2\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"e24e408d-493d-497e-98be-e6a35115ef1e\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F3\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F3\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"3836d3cf-1e46-4efb-8355-311e33c6a905\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F4\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F4\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"47ceaf9a-9361-4279-a8ae-afaa60452d52\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F5\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F5\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"5b6dc780-561b-4671-9c66-49181b70f829\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F6\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F6\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"}]";
        List<SpSpecialityParamItem> itemList = JSON.parseArray(itemListJson, SpSpecialityParamItem.class);
        Mockito.when(repository.queryPage(Mockito.any())).thenReturn(itemList);
        Mockito.when(mdsRemoteService.uploadFmParamMesFileInfo(Mockito.any(), Mockito.anyString(), Mockito.anyString())).thenReturn("DHOME/MesParamFileSave/20250619000014/2025061900001820250619000014&123699991088&6.xlsx");
        Mockito.when(spSpecialityParamRepository.updateById(Mockito.any())).thenReturn(1L);
//        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
//        PowerMockito.when(redisLock.lock()).thenReturn(true);
        service.syncDataToMds("20250619000018");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }


    @Test
    public void syncDataThread() throws Exception {
        String spSpecialityParamJson = "{\"applyQty\":0,\"applyTask\":\"20250619000014\",\"createBy\":\"10338918\",\"createDate\":1750335748432,\"destinedArea\":\"\",\"enabledFlag\":\"Y\",\"firstEigenValue\":false,\"fixedStr\":\"\",\"isRecovery\":0,\"itemCode\":\"123699991088\",\"itemName\":\"\",\"itemNum\":6,\"itemUnit\":\"\",\"itemVersion\":\"\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317982,\"operator\":\"\",\"productBigClass\":\"2\",\"productSmallClass\":\"7\",\"productionUnit\":\"0\",\"progress\":100,\"specialityParamId\":\"20250619000018\",\"syncMdsStatus\":\"N\",\"taskId\":\"20250619000014\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"templateItem\":\"\",\"templateName\":\"B866V2M泰国\",\"usageScope\":\"2\"}";
        SpSpecialityParam spSpecialityParam = JSON.parseObject(spSpecialityParamJson, SpSpecialityParam.class);
        PowerMockito.doThrow(new MesBusinessException("", "")).when(mdsRemoteService).uploadFmParamMesFileInfo(Mockito.any(), Mockito.anyString(), Mockito.anyString());
        try {
            Whitebox.invokeMethod(service, "syncDataThread", "test", 10L, spSpecialityParam, new SpSpecialityParamItemPageQueryDTO(), redisLock);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void syncDataThread1() throws Exception {
        String spSpecialityParamJson = "{\"applyQty\":0,\"applyTask\":\"20250619000014\",\"createBy\":\"10338918\",\"createDate\":1750335748432,\"destinedArea\":\"\",\"enabledFlag\":\"Y\",\"firstEigenValue\":false,\"fixedStr\":\"\",\"isRecovery\":0,\"itemCode\":\"123699991088\",\"itemName\":\"\",\"itemNum\":6,\"itemUnit\":\"\",\"itemVersion\":\"\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317982,\"operator\":\"\",\"productBigClass\":\"2\",\"productSmallClass\":\"7\",\"productionUnit\":\"0\",\"progress\":100,\"specialityParamId\":\"20250619000018\",\"syncMdsStatus\":\"N\",\"taskId\":\"20250619000014\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"templateItem\":\"\",\"templateName\":\"B866V2M泰国\",\"usageScope\":\"2\"}";
        SpSpecialityParam spSpecialityParam = JSON.parseObject(spSpecialityParamJson, SpSpecialityParam.class);
        Mockito.when(spSpecialityParamRepository.selectById(Mockito.any())).thenReturn(spSpecialityParam);

        String templateItemListJson = "[{\"createBy\":\"10338918\",\"createTime\":1750323202641,\"enabledFlag\":\"Y\",\"generationMethod\":\"文本\",\"itemId\":\"5e9991c6-f769-4213-a9cc-218dc007ab89\",\"orderNum\":1,\"paramName\":\"DeviceType\",\"paramRule\":\"Text(B866V2M)\",\"paramType\":\"赋值\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"updateBy\":\"10338918\",\"updateTime\":1750381979000},{\"createBy\":\"10338918\",\"createTime\":1750381534872,\"enabledFlag\":\"Y\",\"generationMethod\":\"MacStart\",\"itemId\":\"3c2fb6fc-1f07-412f-8e30-683fba7e1898\",\"orderNum\":2,\"paramName\":\"MacStart\",\"paramRule\":\"MacStart()\",\"paramType\":\"MAC\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"updateBy\":\"10338918\",\"updateTime\":1750381979000},{\"createBy\":\"10338918\",\"createTime\":1750381534872,\"enabledFlag\":\"Y\",\"generationMethod\":\"MacEnd\",\"itemId\":\"b5562b50-b0db-4531-a084-d4f0fdd1e902\",\"orderNum\":3,\"paramName\":\"MacEnd\",\"paramRule\":\"MacEnd(1)\",\"paramType\":\"MAC\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"updateBy\":\"10338918\",\"updateTime\":1750381979000},{\"createBy\":\"10338918\",\"createTime\":1750381979395,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"3e977828-e43c-4eb4-8127-a6219b8b9307\",\"orderNum\":4,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart,1004-990070-636-0-0000,Y)\",\"paramType\":\"STBID\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"updateBy\":\"10338918\",\"updateTime\":1750381979395}]";
        List<SpTemplateItem> templateItemList = JSON.parseArray(templateItemListJson, SpTemplateItem.class);
        Mockito.when(spTemplateItemRepository.queryList(Mockito.anyString())).thenReturn(templateItemList);

        String itemListJson = "[{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"3d536acc-16bf-4256-b911-e721b021e5ca\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F1\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F1\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"c0bed385-08e6-405d-b19a-cd203b814790\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F2\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F2\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"e24e408d-493d-497e-98be-e6a35115ef1e\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F3\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F3\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"3836d3cf-1e46-4efb-8355-311e33c6a905\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F4\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F4\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"47ceaf9a-9361-4279-a8ae-afaa60452d52\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F5\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F5\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"5b6dc780-561b-4671-9c66-49181b70f829\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F6\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F6\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"}]";
        List<SpSpecialityParamItem> itemList = JSON.parseArray(itemListJson, SpSpecialityParamItem.class);
        Mockito.when(repository.queryPage(Mockito.any())).thenReturn(itemList);

        Mockito.when(mdsRemoteService.uploadFmParamMesFileInfo(Mockito.any(), Mockito.anyString(), Mockito.anyString())).thenReturn("DHOME/MesParamFileSave/20250619000014/2025061900001820250619000014&123699991088&6.xlsx");

        Mockito.when(spSpecialityParamRepository.updateById(Mockito.any())).thenReturn(1L);

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);

        try {
            Whitebox.invokeMethod(service, "syncDataThread", "test", 10L, spSpecialityParam, new SpSpecialityParamItemPageQueryDTO(), redisLock);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void syncDataThread2() throws Exception {
        String spSpecialityParamJson = "{\"applyQty\":0,\"applyTask\":\"20250619000014\",\"createBy\":\"10338918\",\"createDate\":1750335748432,\"destinedArea\":\"\",\"enabledFlag\":\"Y\",\"firstEigenValue\":false,\"fixedStr\":\"\",\"isRecovery\":0,\"itemCode\":\"123699991088\",\"itemName\":\"\",\"itemNum\":6,\"itemUnit\":\"\",\"itemVersion\":\"\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317982,\"operator\":\"\",\"productBigClass\":\"1\",\"productSmallClass\":\"7\",\"productionUnit\":\"0\",\"progress\":100,\"specialityParamId\":\"20250619000018\",\"syncMdsStatus\":\"N\",\"taskId\":\"20250619000014\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"templateItem\":\"\",\"templateName\":\"B866V2M泰国\",\"usageScope\":\"2\"}";
        SpSpecialityParam spSpecialityParam = JSON.parseObject(spSpecialityParamJson, SpSpecialityParam.class);
        Mockito.when(spSpecialityParamRepository.selectById(Mockito.any())).thenReturn(spSpecialityParam);

        String templateItemListJson = "[{\"createBy\":\"10338918\",\"createTime\":1750323202641,\"enabledFlag\":\"Y\",\"generationMethod\":\"文本\",\"itemId\":\"5e9991c6-f769-4213-a9cc-218dc007ab89\",\"orderNum\":1,\"paramName\":\"DeviceType\",\"paramRule\":\"Text(B866V2M)\",\"paramType\":\"赋值\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"updateBy\":\"10338918\",\"updateTime\":1750381979000},{\"createBy\":\"10338918\",\"createTime\":1750381534872,\"enabledFlag\":\"Y\",\"generationMethod\":\"MacStart\",\"itemId\":\"3c2fb6fc-1f07-412f-8e30-683fba7e1898\",\"orderNum\":2,\"paramName\":\"MacStart\",\"paramRule\":\"MacStart()\",\"paramType\":\"MAC\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"updateBy\":\"10338918\",\"updateTime\":1750381979000},{\"createBy\":\"10338918\",\"createTime\":1750381534872,\"enabledFlag\":\"Y\",\"generationMethod\":\"MacEnd\",\"itemId\":\"b5562b50-b0db-4531-a084-d4f0fdd1e902\",\"orderNum\":3,\"paramName\":\"MacEnd\",\"paramRule\":\"MacEnd(1)\",\"paramType\":\"MAC\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"updateBy\":\"10338918\",\"updateTime\":1750381979000},{\"createBy\":\"10338918\",\"createTime\":1750381979395,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"3e977828-e43c-4eb4-8127-a6219b8b9307\",\"orderNum\":4,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart,1004-990070-636-0-0000,Y)\",\"paramType\":\"STBID\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"updateBy\":\"10338918\",\"updateTime\":1750381979395}]";
        List<SpTemplateItem> templateItemList = JSON.parseArray(templateItemListJson, SpTemplateItem.class);
        Mockito.when(spTemplateItemRepository.queryList(Mockito.anyString())).thenReturn(templateItemList);

        String itemListJson = "[{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"3d536acc-16bf-4256-b911-e721b021e5ca\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F1\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F1\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"c0bed385-08e6-405d-b19a-cd203b814790\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F2\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F2\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"e24e408d-493d-497e-98be-e6a35115ef1e\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F3\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F3\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"3836d3cf-1e46-4efb-8355-311e33c6a905\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F4\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F4\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"47ceaf9a-9361-4279-a8ae-afaa60452d52\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F5\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F5\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"5b6dc780-561b-4671-9c66-49181b70f829\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F6\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F6\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"}]";
        List<SpSpecialityParamItem> itemList = JSON.parseArray(itemListJson, SpSpecialityParamItem.class);
        Mockito.when(repository.queryPage(Mockito.any())).thenReturn(itemList);

        Mockito.when(mdsRemoteService.uploadFmParamMesFileInfo(Mockito.any(), Mockito.anyString(), Mockito.anyString())).thenReturn("DHOME/MesParamFileSave/20250619000014/2025061900001820250619000014&123699991088&6.xlsx");

        Mockito.when(spSpecialityParamRepository.updateById(Mockito.any())).thenReturn(1L);

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);

        try {
            Whitebox.invokeMethod(service, "syncDataThread", "test", 10L, spSpecialityParam, new SpSpecialityParamItemPageQueryDTO(), redisLock);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void syncDataThread3() throws Exception {
        String spSpecialityParamJson = "{\"applyQty\":0,\"applyTask\":\"20250619000014\",\"createBy\":\"10338918\",\"createDate\":1750335748432,\"destinedArea\":\"\",\"enabledFlag\":\"Y\",\"firstEigenValue\":false,\"fixedStr\":\"\",\"isRecovery\":0,\"itemCode\":\"123699991088\",\"itemName\":\"\",\"itemNum\":6,\"itemUnit\":\"\",\"itemVersion\":\"\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317982,\"operator\":\"\",\"productBigClass\":\"3\",\"productSmallClass\":\"7\",\"productionUnit\":\"0\",\"progress\":100,\"specialityParamId\":\"20250619000018\",\"syncMdsStatus\":\"N\",\"taskId\":\"20250619000014\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"templateItem\":\"\",\"templateName\":\"B866V2M泰国\",\"usageScope\":\"2\"}";
        SpSpecialityParam spSpecialityParam = JSON.parseObject(spSpecialityParamJson, SpSpecialityParam.class);
        Mockito.when(spSpecialityParamRepository.selectById(Mockito.any())).thenReturn(spSpecialityParam);

        String templateItemListJson = "[{\"createBy\":\"10338918\",\"createTime\":1750323202641,\"enabledFlag\":\"Y\",\"generationMethod\":\"文本\",\"itemId\":\"5e9991c6-f769-4213-a9cc-218dc007ab89\",\"orderNum\":1,\"paramName\":\"DeviceType\",\"paramRule\":\"Text(B866V2M)\",\"paramType\":\"赋值\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"updateBy\":\"10338918\",\"updateTime\":1750381979000},{\"createBy\":\"10338918\",\"createTime\":1750381534872,\"enabledFlag\":\"Y\",\"generationMethod\":\"MacStart\",\"itemId\":\"3c2fb6fc-1f07-412f-8e30-683fba7e1898\",\"orderNum\":2,\"paramName\":\"MacStart\",\"paramRule\":\"MacStart()\",\"paramType\":\"MAC\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"updateBy\":\"10338918\",\"updateTime\":1750381979000},{\"createBy\":\"10338918\",\"createTime\":1750381534872,\"enabledFlag\":\"Y\",\"generationMethod\":\"MacEnd\",\"itemId\":\"b5562b50-b0db-4531-a084-d4f0fdd1e902\",\"orderNum\":3,\"paramName\":\"MacEnd\",\"paramRule\":\"MacEnd(1)\",\"paramType\":\"MAC\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"updateBy\":\"10338918\",\"updateTime\":1750381979000},{\"createBy\":\"10338918\",\"createTime\":1750381979395,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"3e977828-e43c-4eb4-8127-a6219b8b9307\",\"orderNum\":4,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart,1004-990070-636-0-0000,Y)\",\"paramType\":\"STBID\",\"templateId\":\"376e4f3d-92ad-4c9c-a0e8-dda2689c7e24\",\"updateBy\":\"10338918\",\"updateTime\":1750381979395}]";
        List<SpTemplateItem> templateItemList = JSON.parseArray(templateItemListJson, SpTemplateItem.class);
        Mockito.when(spTemplateItemRepository.queryList(Mockito.anyString())).thenReturn(templateItemList);

        String itemListJson = "[{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"3d536acc-16bf-4256-b911-e721b021e5ca\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F1\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F1\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"c0bed385-08e6-405d-b19a-cd203b814790\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F2\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F2\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"e24e408d-493d-497e-98be-e6a35115ef1e\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F3\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F3\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"3836d3cf-1e46-4efb-8355-311e33c6a905\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F4\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F4\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"47ceaf9a-9361-4279-a8ae-afaa60452d52\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F5\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F5\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"},{\"createBy\":\"10338918\",\"createDate\":1750381317993,\"enabledFlag\":\"Y\",\"gponEnd\":\"\",\"gponStart\":\"\",\"id\":\"5b6dc780-561b-4671-9c66-49181b70f829\",\"isUsed\":0,\"itemData\":\"{\\\"DeviceType\\\":\\\"B866V2M\\\",\\\"MacEnd\\\":\\\"11-C1-74-1D-06-F6\\\",\\\"MacStart\\\":\\\"11-C1-74-1D-06-F6\\\"}\",\"lastUpdatedBy\":\"10338918\",\"lastUpdatedDate\":1750381317993,\"macEnd\":\"\",\"macStart\":\"\",\"specialityParamId\":\"20250619000018\"}]";
        List<SpSpecialityParamItem> itemList = JSON.parseArray(itemListJson, SpSpecialityParamItem.class);
        Mockito.when(repository.queryPage(Mockito.any())).thenReturn(itemList);

//        Mockito.when(mdsRemoteService.uploadFmParamMesFileInfo(Mockito.any(), Mockito.anyString(), Mockito.anyString())).thenReturn("DHOME/MesParamFileSave/20250619000014/2025061900001820250619000014&123699991088&6.xlsx");
//
//        Mockito.when(spSpecialityParamRepository.updateById(Mockito.any())).thenReturn(1L);

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);

        try {
            Whitebox.invokeMethod(service, "syncDataThread", "test", 10L, spSpecialityParam, new SpSpecialityParamItemPageQueryDTO(), redisLock);
            String runNormal = "Y";
            Assert.assertEquals(Constant.FLAG_Y, runNormal);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }
}
