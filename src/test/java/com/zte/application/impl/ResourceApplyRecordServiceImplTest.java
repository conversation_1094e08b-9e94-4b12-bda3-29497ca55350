package com.zte.application.impl;

import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.ResourceApplyRecord;
import com.zte.domain.model.ResourceApplyRecordRepository;
import com.zte.interfaces.dto.ResourceApplyRecordPageQueryDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * 资源申请记录
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023-11-30 14:55:23
 */
@RunWith(PowerMockRunner.class)
public class ResourceApplyRecordServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ResourceApplyRecordServiceImpl service;
    @Mock
    private ResourceApplyRecordRepository repository;

    @Test
    public void testQueryPage() {
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(new ArrayList<ResourceApplyRecord>());
        Assert.assertNotNull(service.queryPage(new ResourceApplyRecordPageQueryDTO()));
    }
    @Test
    public void testAdd() {
        PowerMockito.when(repository.insert(Mockito.any())).thenReturn(1);
        Assert.assertEquals(service.insert(null), NumConstant.NUM_ZERO);
        Assert.assertEquals(service.insert(new ResourceApplyRecord()), NumConstant.NUM_ONE);
    }

    @Test
    public void testUpdateById() {
        PowerMockito.when(repository.updateById(Mockito.any())).thenReturn(1);
        Assert.assertEquals(1,service.updateById(new ResourceApplyRecord()));
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void batchUpdateStatusByIdList() {
        PowerMockito.when(repository.batchUpdateStatusByIdList(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(1);
        List<String> ids = new ArrayList<>();
        ids.add("21");
        Assert.assertEquals(1,service.batchUpdateStatusByIdList(ids,"",""));
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void testCountExportTotal() {
        PowerMockito.when(repository.selectCount(Mockito.any())).thenReturn(1);
        Integer count = service.countExportTotal(new ResourceApplyRecordPageQueryDTO());
        Assert.assertEquals(count.intValue(), 1);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void testQueryExportData() {
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(new ArrayList<ResourceApplyRecord>());
        List<ResourceApplyRecord> resourceApplyRecords =  service.queryExportData(new ResourceApplyRecordPageQueryDTO(), 1, 10);
        Assert.assertEquals(resourceApplyRecords.size(),NumConstant.NUM_ZERO);
    }
}

