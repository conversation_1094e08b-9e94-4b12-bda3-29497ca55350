/*Started by AICoder, pid:5cb1845ba1z4a46142080a2310f26b96b0770a03*/
package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.application.PushStdModelSnDataHandleService;
import com.zte.interfaces.dto.PushStdModelSnDataHandleDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.stubbing.Answer;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
public class PushStdModelSnDataHandleServiceCompositeImpl_handlePushStdModelSnDatas_4_Test {

    @InjectMocks
    private PushStdModelSnDataHandleServiceCompositeImpl pushStdModelSnDataHandleServiceComposite;

    @Before
    public void setUp() {
        // Initialize mocks if necessary
    }

    @Test
    public void testHandlePushStdModelSnDatas_EmptyList() {
        List<PushStdModelSnDataHandleDTO> pushStdModelSnDataHandles = Collections.emptyList();

        boolean result = pushStdModelSnDataHandleServiceComposite.handlePushStdModelSnDatas(pushStdModelSnDataHandles);

        assertFalse(result);
    }

    @Test
    public void testHandlePushStdModelSnDatas_NoMatchingService() {
        PushStdModelSnDataHandleDTO dto = mock(PushStdModelSnDataHandleDTO.class);
        when(dto.getCurrProcess()).thenReturn("process1");

        List<PushStdModelSnDataHandleDTO> pushStdModelSnDataHandles = new ArrayList<>();
        pushStdModelSnDataHandles.add(dto);

        PushStdModelSnDataHandleService<?> service = mock(PushStdModelSnDataHandleService.class);
        when(service.match(anyString())).thenReturn(false);

        ReflectionTestUtils.setField(pushStdModelSnDataHandleServiceComposite, "pushStdModelSnDataHandleServices", Lists.newArrayList(service));

        boolean result = pushStdModelSnDataHandleServiceComposite.handlePushStdModelSnDatas(pushStdModelSnDataHandles);

        assertFalse(result);
        verify(service, times(1)).match("process1");
    }

    @Test
    public void testHandlePushStdModelSnDatas_MatchingService() {
        PushStdModelSnDataHandleDTO dto = mock(PushStdModelSnDataHandleDTO.class);
        when(dto.getCurrProcess()).thenReturn("process1");

        List<PushStdModelSnDataHandleDTO> pushStdModelSnDataHandles = new ArrayList<>();
        pushStdModelSnDataHandles.add(dto);

        PushStdModelSnDataHandleService<?> service = mock(PushStdModelSnDataHandleService.class);
        when(service.match(anyString())).thenReturn(true);
        when(service.handlePushStdModelSnDatas(anyList())).thenAnswer((Answer<Boolean>) invocation -> {
            List<PushStdModelSnDataHandleDTO> filteredList = invocation.getArgument(0);
            return !filteredList.isEmpty();
        });

        ReflectionTestUtils.setField(pushStdModelSnDataHandleServiceComposite, "pushStdModelSnDataHandleServices", Lists.newArrayList(service));

        boolean result = pushStdModelSnDataHandleServiceComposite.handlePushStdModelSnDatas(pushStdModelSnDataHandles);

        assertTrue(result);
        verify(service, times(1)).match("process1");
        verify(service, times(1)).handlePushStdModelSnDatas(anyList());
    }
}
/*Ended by AICoder, pid:5cb1845ba1z4a46142080a2310f26b96b0770a03*/