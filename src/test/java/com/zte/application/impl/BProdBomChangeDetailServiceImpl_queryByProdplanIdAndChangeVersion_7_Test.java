/*Started by AICoder, pid:te798996adee1a014b7a0816008b00811c392b17*/
package com.zte.application.impl;
import com.zte.domain.model.BProdBomChangeDetailRepository;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.interfaces.dto.aps.ApsDerivativeCodeQueryDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BProdBomChangeDetailServiceImpl_queryByProdplanIdAndChangeVersion_7_Test {

    @Mock
    private BProdBomChangeDetailRepository bProdBomChangeDetailRepository;

    @InjectMocks
    private BProdBomChangeDetailServiceImpl bProdBomChangeDetailService;

    private List<ApsDerivativeCodeQueryDTO> params;

    @Before
    public void setUp() {
        params = new ArrayList<>();
    }

    @Test
    public void testQueryByProdplanIdAndChangeVersion_EmptyParams() {
        // Given
        params = Collections.emptyList();

        // When
        List<BProdBomChangeDetailDTO> result = bProdBomChangeDetailService.queryByProdplanIdAndChangeVersion(params);

        // Then
        assertEquals(Collections.emptyList(), result);
        verify(bProdBomChangeDetailRepository, never()).queryByProdplanIdAndChangeId(anyList());
    }

    @Test
    public void testQueryByProdplanIdAndChangeVersion_BatchProcessing() {
        // Given
        params.add(new ApsDerivativeCodeQueryDTO());
        params.add(new ApsDerivativeCodeQueryDTO());

        List<BProdBomChangeDetailDTO> batchResults1 = Arrays.asList(new BProdBomChangeDetailDTO(), new BProdBomChangeDetailDTO());
        List<BProdBomChangeDetailDTO> batchResults2 = Collections.singletonList(new BProdBomChangeDetailDTO());

        when(bProdBomChangeDetailRepository.queryByProdplanIdAndChangeId(anyList()))
                .thenReturn(batchResults1)
                .thenReturn(batchResults2);

        // When
        List<BProdBomChangeDetailDTO> result = bProdBomChangeDetailService.queryByProdplanIdAndChangeVersion(params);

        // Then
        assertEquals(2, result.size());
        verify(bProdBomChangeDetailRepository, times(1)).queryByProdplanIdAndChangeId(anyList());
    }

    @Test
    public void testQueryByProdplanIdAndChangeVersion_NoBatchResults() {
        // Given
        params.add(new ApsDerivativeCodeQueryDTO());

        when(bProdBomChangeDetailRepository.queryByProdplanIdAndChangeId(anyList()))
                .thenReturn(Collections.emptyList());

        // When
        List<BProdBomChangeDetailDTO> result = bProdBomChangeDetailService.queryByProdplanIdAndChangeVersion(params);

        // Then
        assertEquals(Collections.emptyList(), result);
        verify(bProdBomChangeDetailRepository, times(1)).queryByProdplanIdAndChangeId(anyList());
    }
}
/*Ended by AICoder, pid:te798996adee1a014b7a0816008b00811c392b17*/