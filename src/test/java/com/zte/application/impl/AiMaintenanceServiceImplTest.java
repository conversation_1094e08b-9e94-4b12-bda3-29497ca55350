package com.zte.application.impl;

import com.zte.application.AoiFileService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.AoiFile;
import com.zte.interfaces.dto.LocRangeDTO;
import com.zte.interfaces.dto.SpcDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.FileUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@RunWith(PowerMockRunner.class)
@PrepareForTest({FileUtils.class, ImageIO.class, AiMaintenanceServiceImpl.class})
public class AiMaintenanceServiceImplTest extends BaseTestCase {
    @InjectMocks
    private AiMaintenanceServiceImpl service;
    @Mock
    private CloudDiskHelper cloudDiskHelper;
    @Mock
    private AoiFileService aoiFileService;
    @Mock
    private File file;
    @Mock
    private BufferedImage img;
    @Mock
    private BufferedImage cutImage;
    @Mock
    private Graphics2D g2d;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(FileUtils.class, ImageIO.class);
    }

    /* Started by AICoder, pid:2388eaebfav2a281429008b1a0cc7382b7840cf2 */
    @Test
    public void markAndCutLocations() throws Exception {
        String bomNo = "123456789123ABC";
        List<String> locations = new ArrayList<>();
        String side = "B";
        String empNo = "00000000";
        try {
            service.markAndCutLocations(bomNo, locations, side, empNo);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.AOI_IMG_NOT_EXIST, e.getExMsgId());
        }

        AoiFile aoiImgFile = new AoiFile();
        PowerMockito.when(aoiFileService.getAoiFileInfoByBomAndType(Mockito.anyString(), Mockito.anyString())).thenReturn(aoiImgFile);
        try {
            service.markAndCutLocations(bomNo, locations, side, empNo);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.AOI_IMG_NOT_EXIST, e.getExMsgId());
        }

        aoiImgFile.setDocId("123456");
        aoiImgFile.setFileName("aa.jpg");
        String imgPath = "/usr/local/temp/aa.jpg";
        PowerMockito.when(FileUtils.createFilePathAndCheck(Mockito.anyString())).thenReturn(imgPath);
        PowerMockito.whenNew(File.class).withAnyArguments().thenReturn(file);
        PowerMockito.when(ImageIO.read(Mockito.any(File.class))).thenReturn(img);
        PowerMockito.when(img.getSubimage(Mockito.anyInt(), Mockito.anyInt(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(cutImage);
        PowerMockito.when(FileUtils.createFilePathAndCheck(Mockito.anyString())).thenReturn("/usr/local/tomcat/1.jpg");
        PowerMockito.when(cloudDiskHelper.fileUpload(Mockito.anyString(), Mockito.anyString())).thenReturn("docId1");
        PowerMockito.when(img.getWidth()).thenReturn(3000);
        PowerMockito.when(img.getHeight()).thenReturn(2000);
        PowerMockito.when(img.createGraphics()).thenReturn(g2d);
        PowerMockito.when(img.getGraphics()).thenReturn(g2d);
        PowerMockito.when(img.getType()).thenReturn(1);
        PowerMockito.whenNew(BufferedImage.class).withAnyArguments().thenReturn(img);
        SpcDTO spcDTO = new SpcDTO();
        PowerMockito.when(aoiFileService.getTagCoordinates(Mockito.anyString(), Mockito.anyString(), Mockito.anyList())).thenReturn(spcDTO);
        Map<String, String> resultMap = service.markAndCutLocations(bomNo, locations, side, empNo);
        Assert.assertEquals("123456", resultMap.get("result"));

        PowerMockito.when(file.length()).thenReturn(2097153L);
        resultMap = service.markAndCutLocations(bomNo, locations, side, empNo);
        Assert.assertEquals("docId1", resultMap.get("result"));

        PowerMockito.when(file.length()).thenReturn(1000L);
        spcDTO.setBoardSizeX(300.1f);
        resultMap = service.markAndCutLocations(bomNo, locations, side, empNo);
        Assert.assertEquals("123456", resultMap.get("result"));

        spcDTO.setBoardSizeY(200.1f);
        resultMap = service.markAndCutLocations(bomNo, locations, side, empNo);
        Assert.assertEquals("123456", resultMap.get("result"));

        List<LocRangeDTO> locRangeDTOList = new ArrayList<>();
        locRangeDTOList.add(new LocRangeDTO("A1B1",100, 100, 1, 1));
        spcDTO.setLocRangeList(locRangeDTOList);

        resultMap = service.markAndCutLocations(bomNo, locations, side, empNo);
        Assert.assertEquals("docId1", resultMap.get("result"));

        PowerMockito.when(file.length()).thenReturn(2097153L);
        resultMap = service.markAndCutLocations(bomNo, locations, side, empNo);
        Assert.assertEquals("docId1", resultMap.get("result"));
    }
    /* Ended by AICoder, pid:2388eaebfav2a281429008b1a0cc7382b7840cf2 */
}