package com.zte.application.impl;

import com.zte.application.PsTaskExtendedService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.InoneRemoteService;
import com.zte.interfaces.dto.BarcodeExpandResponse;
import com.zte.interfaces.dto.BarcodeExpandVO;
import com.zte.interfaces.dto.EdiSoSDTO;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.datawb.BaggageBindingBillboardQueryDTO;
import com.zte.interfaces.dto.datawb.ProcPicklistDetail;
import com.zte.interfaces.dto.datawb.ProdPickListMainDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/26
 */
@PrepareForTest({DatawbRemoteService.class})
public class BaggageBindingBillboardServiceImplTest extends BaseTestCase {
    @InjectMocks
    private BaggageBindingBillboardServiceImpl baggageBindingBillboardService;
    @Mock
    private InoneRemoteService inoneRemoteService;
    @Mock
    private PsTaskExtendedService psTaskExtendedService;
    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Before
    public void init() {
        PowerMockito.mockStatic(DatawbRemoteService.class);
        PowerMockito.when(barcodeCenterRemoteService.queryRegisterNumber(Mockito.any(), Mockito.any()))
                .thenReturn(new BarcodeExpandResponse());
    }

    @Test
    public void queryCurrentDayBoard() throws Exception {
        BaggageBindingBillboardQueryDTO page = new BaggageBindingBillboardQueryDTO();
        try {
            baggageBindingBillboardService.queryCurrentDayBoard(page);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BAGGAGE_PARAM_LOST, e.getMessage());
        }
        page.setStartTime(new Date());
        try {
            baggageBindingBillboardService.queryCurrentDayBoard(page);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BAGGAGE_PARAM_LOST, e.getMessage());
        }
        page.setEndTime(new Date());
        baggageBindingBillboardService.queryCurrentDayBoard(page);

        List<PsTaskExtendedDTO> psTaskExtendedList = new LinkedList<>();
        PsTaskExtendedDTO a1 = new PsTaskExtendedDTO();
        a1.setTaskNo("123");
        a1.setCustomerNo("123");
        psTaskExtendedList.add(a1);
        PsTaskExtendedDTO a2 = new PsTaskExtendedDTO();
        a2.setTaskNo("1234");
        a2.setCustomerNo("1234");
        psTaskExtendedList.add(a2);
        PowerMockito.when(psTaskExtendedService.queryByTaskNos(Mockito.anyList()))
                .thenReturn(psTaskExtendedList);
        List<SysLookupValues> typesDTOList = new LinkedList<>();
        SysLookupValues b1 = new SysLookupValues();
        b1.setLookupMeaning("1234");
        typesDTOList.add(b1);
        PowerMockito.when(sysLookupValuesService.selectValuesByType(Constant.LOOKUP_VALUE_1004115))
                .thenReturn(typesDTOList);

        List<ProdPickListMainDTO> list = new LinkedList<>();
        ProdPickListMainDTO d1 = new ProdPickListMainDTO();
        d1.setTaskNo("1234");
        list.add(d1);
        PowerMockito.when(DatawbRemoteService.queryPickListByTaskNos(Mockito.any()))
                .thenReturn(list);
        List<ProcPicklistDetail> listDetail = new LinkedList<>();
        PowerMockito.when(DatawbRemoteService.queryProcPickDetailBatch(Mockito.anyList()))
                .thenReturn(listDetail);
        try {
            baggageBindingBillboardService.queryCurrentDayBoard(page);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any())).thenReturn(sysLookupValues);
        try {
            baggageBindingBillboardService.queryCurrentDayBoard(page);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        sysLookupValues.setLookupMeaning("23/34");
        baggageBindingBillboardService.queryCurrentDayBoard(page);

        List<EdiSoSDTO> sendMaterials = new LinkedList<>();
        for (int i = 0; i < 10; i++) {
            EdiSoSDTO temp = new EdiSoSDTO();
            temp.setQty(new BigDecimal(i));
            sendMaterials.add(temp);
        }
        PowerMockito.when(DatawbRemoteService.getSendMaterials(Mockito.any()))
                .thenReturn(sendMaterials);
        baggageBindingBillboardService.queryCurrentDayBoard(page);
    }

    @Test
    public void queryBoardPage() throws Exception{
        BaggageBindingBillboardQueryDTO page = new BaggageBindingBillboardQueryDTO();
        try {
            baggageBindingBillboardService.queryBoardPage(page);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BAGGAGE_PARAM_LOST, e.getMessage());
        }

        page.setOrderNo("123");
        try {
            baggageBindingBillboardService.queryBoardPage(page);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BAGGAGE_PARAM_LOST, e.getMessage());
        }

        page.setOrderNo(null);
        page.setEndTime(new Date());
        try {
            baggageBindingBillboardService.queryBoardPage(page);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BAGGAGE_PARAM_LOST, e.getMessage());
        }

        page.setStartTime(new Date());
        try {
            baggageBindingBillboardService.queryBoardPage(page);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BAGGAGE_PARAM_LOST, e.getMessage());
        }

        page.setPageNum(0);
        try {
            baggageBindingBillboardService.queryBoardPage(page);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BAGGAGE_PARAM_LOST, e.getMessage());
        }

        page.setPageNum(1);
        try {
            baggageBindingBillboardService.queryBoardPage(page);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BAGGAGE_PARAM_LOST, e.getMessage());
        }

        page.setPageSize(0);
        try {
            baggageBindingBillboardService.queryBoardPage(page);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BAGGAGE_PARAM_LOST, e.getMessage());
        }

        page.setPageSize(1);
        PowerMockito.field(BaggageBindingBillboardServiceImpl.class,"baggageBindingQueryTimeRange")
                .set(baggageBindingBillboardService,-2);
        try {
            baggageBindingBillboardService.queryBoardPage(page);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BAGGAGE_PARAM_TIME_OVER, e.getMessage());
        }

        PowerMockito.field(BaggageBindingBillboardServiceImpl.class,"baggageBindingQueryTimeRange")
                .set(baggageBindingBillboardService,3);
        try {
            baggageBindingBillboardService.queryBoardPage(page);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BAGGAGE_PARAM_TIME_OVER, e.getMessage());
        }

        page.setOrderNo("124");
        try {
            baggageBindingBillboardService.queryBoardPage(page);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BAGGAGE_PARAM_TIME_OVER, e.getMessage());
        }

        List<ProdPickListMainDTO> pickResultList = new LinkedList<>();
        List<PsTaskExtendedDTO> psTaskExtendedList = new LinkedList();
        List<SysLookupValues> typesDTOList = new LinkedList();
        for (int i = 0; i < 5; i++) {
            ProdPickListMainDTO a1 = new ProdPickListMainDTO();
            a1.setTaskNo(String.valueOf(i));
            a1.setBillNumber(String.valueOf(i));
            pickResultList.add(a1);
            PsTaskExtendedDTO temp = new PsTaskExtendedDTO();
            temp.setTaskNo(String.valueOf(i));
            temp.setCustomerNo(String.valueOf(i));
            psTaskExtendedList.add(temp);
            SysLookupValues c1 = new SysLookupValues();
            c1.setLookupMeaning("2");
            typesDTOList.add(c1);
        }
        PowerMockito.when(DatawbRemoteService.queryPickListCondition(Mockito.any()))
                .thenReturn(pickResultList);
        baggageBindingBillboardService.queryBoardPage(page);

        PowerMockito.when(psTaskExtendedService.queryByTaskNos(Mockito.anyList()))
                .thenReturn(psTaskExtendedList);
        PowerMockito.when(sysLookupValuesService.selectValuesByType(Mockito.any()))
                .thenReturn(typesDTOList);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("33");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Constant.LOOKUP_VALUE_8886002))
                .thenReturn(sysLookupValues);
        baggageBindingBillboardService.queryBoardPage(page);

        List<EdiSoSDTO> sendMaterials = new LinkedList();
        for (int i = 0; i < 10; i++) {
            EdiSoSDTO a1 = new EdiSoSDTO();
            a1.setQty(new BigDecimal(i));
            a1.setOrderNo(String.valueOf(i));
            a1.setSku(String.valueOf(i));
            sendMaterials.add(a1);
        }
        PowerMockito.when(DatawbRemoteService.getSendMaterials(Mockito.any()))
                .thenReturn(sendMaterials);
        baggageBindingBillboardService.queryBoardPage(page);

        BarcodeExpandResponse response = new BarcodeExpandResponse();
        List<BarcodeExpandVO> rows = new LinkedList<>();
        BarcodeExpandVO bar1 = new BarcodeExpandVO();
        rows.add(bar1);
        response.setRows(rows);
        PowerMockito.when(barcodeCenterRemoteService.queryRegisterNumber(Mockito.any(), Mockito.any()))
                .thenReturn(response);
        baggageBindingBillboardService.queryBoardPage(page);
    }

}
