/*Started by AICoder, pid:t2dcbf787ccdf69142ee087950b3bf8ce5466aa4*/
package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.application.PushStdModelSnDataHandleService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
public class PushStdModelSnDataHandleServiceCompositeImpl_validatePushMessageData_3_Test {

    @Mock
    private PushStdModelSnDataHandleService<Object> pushStdModelSnDataHandleService;

    @InjectMocks
    private PushStdModelSnDataHandleServiceCompositeImpl pushStdModelSnDataHandleServiceComposite;

    @Before
    public void setUp() {
        // Initialize any necessary setup here
        ReflectionTestUtils.setField(pushStdModelSnDataHandleServiceComposite, "pushStdModelSnDataHandleServices", Lists.newArrayList(pushStdModelSnDataHandleService));
    }

    @Test
    public void testValidatePushMessageData_MatchFound() {
        // Given
        String currProcess = "testProcess";
        Object pushMessageData = new Object();

        when(pushStdModelSnDataHandleService.match(currProcess)).thenReturn(true);
        when(pushStdModelSnDataHandleService.validatePushMessageData(currProcess, pushMessageData)).thenReturn(true);


        // When
        boolean result = pushStdModelSnDataHandleServiceComposite.validatePushMessageData(currProcess, pushMessageData);

        // Then
        assertTrue(result);
        verify(pushStdModelSnDataHandleService).match(currProcess);
        verify(pushStdModelSnDataHandleService).validatePushMessageData(currProcess, pushMessageData);
    }

    @Test
    public void testValidatePushMessageData_NoMatchFound() {
        // Given
        String currProcess = "testProcess";
        Object pushMessageData = new Object();

        when(pushStdModelSnDataHandleService.match(currProcess)).thenReturn(false);

        // When
        boolean result = pushStdModelSnDataHandleServiceComposite.validatePushMessageData(currProcess, pushMessageData);

        // Then
        assertFalse(result);
        verify(pushStdModelSnDataHandleService).match(currProcess);
    }

    @Test
    public void testValidatePushMessageData_EmptyList() {
        // Given
        String currProcess = "testProcess";
        Object pushMessageData = new Object();

        ReflectionTestUtils.setField(pushStdModelSnDataHandleServiceComposite, "pushStdModelSnDataHandleServices", Lists.newArrayList());

        // When
        boolean result = pushStdModelSnDataHandleServiceComposite.validatePushMessageData(currProcess, pushMessageData);

        // Then
        assertFalse(result);
    }
}
/*Ended by AICoder, pid:t2dcbf787ccdf69142ee087950b3bf8ce5466aa4*/