package com.zte.application.impl.helper;

/**
 * {@code @description }
 *
 * <AUTHOR>
 * @date 2025/5/18 下午6:41
 */
/* Started by AICoder, pid:91e8bf9c4fo8d331496d093300290a9cc3a4c619 */

import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.interfaces.VO.OrderTaskInfoVO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.EmailUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertThrows;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({StringUtils.class, CollectionUtils.class, DateUtil.class, CommonUtils.class})
public class EmailServiceHelperTest {

    @InjectMocks
    private EmailServiceHelper helper;

    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;

    @Mock
    private EmailUtils emailUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // Mock static methods if needed
    }

    /**
     * 测试 sendEmail 方法，当没有接收人时。
     */
    @Test
    public void testSendEmail_NoReceivers() {
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setCustomerNo("customer1");
        orderTaskInfoVO.setCustomerName("customerName");
        List<OrderTaskInfoVO> emailTaskList = Collections.singletonList(orderTaskInfoVO);

        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(Collections.emptyList());

        assertThrows(MesBusinessException.class, () -> helper.sendEmail(emailTaskList));
    }

    /**
     * 测试 sendEmail 方法，当客户编码与名称不匹配时。
     */
    @Test
    public void testSendEmail_CustomerCodeNameNotMatch() {
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setCustomerNo("customer1");
        List<OrderTaskInfoVO> emailTaskList = Collections.singletonList(orderTaskInfoVO);

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupCode(new BigDecimal(Constant.LOOK_UP_CODE_1004116001));
        sysLookupValues.setLookupMeaning("<EMAIL>");

        List<SysLookupValues> emailLookupValues = Collections.singletonList(sysLookupValues);
        List<SysLookupValues> customCodeAndNameLookupValues = Collections.emptyList();

        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(emailLookupValues).thenReturn(customCodeAndNameLookupValues);

        assertThrows(MesBusinessException.class, () -> helper.sendEmail(emailTaskList));
    }

    /**
     * 测试 sendEmail 方法，正常情况。
     */
    @Test
    public void testSendEmail_NormalCase() {
        OrderTaskInfoVO task1 = new OrderTaskInfoVO();
        task1.setOrderNo("order1");
        task1.setCustomerNo("customer1");
        task1.setDateEstimatedCompletion(new Date());
        task1.setDateExpectedCompletion(new Date(System.currentTimeMillis() - 86400000L)); // Yesterday

        List<OrderTaskInfoVO> emailTaskList = Collections.singletonList(task1);

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupCode(new BigDecimal(Constant.LOOK_UP_CODE_1004116001));
        sysLookupValues.setLookupMeaning("<EMAIL>");

        List<SysLookupValues> emailLookupValues = Collections.singletonList(sysLookupValues);
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setDescriptionChin("Customer Name");
        sysLookupValues1.setLookupMeaning("customer1");

        List<SysLookupValues> customCodeAndNameLookupValues = Collections.singletonList(sysLookupValues1);

        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(emailLookupValues).thenReturn(customCodeAndNameLookupValues);

        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(DateUtil.class);
        when(CommonUtils.getStrTransNull(anyString())).thenReturn("Value");
        when(DateUtil.convertDateToString(any(Date.class), anyString())).thenReturn("Formatted Date");

        assertDoesNotThrow(() -> helper.sendEmail(emailTaskList));
        verify(emailUtils, times(1)).sendMail(anyString(), anyString(), anyString(), anyString(), anyString());
    }

    /**
     * 测试 sendEmail 方法，正常情况2。
     */
    @Test
    public void testSendEmail_NormalCase3() {
        OrderTaskInfoVO task1 = new OrderTaskInfoVO();
        task1.setOrderNo("order1");
        task1.setCustomerNo("customer1");

        List<OrderTaskInfoVO> emailTaskList = Collections.singletonList(task1);

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupCode(new BigDecimal(Constant.LOOK_UP_CODE_1004116001));
        sysLookupValues.setLookupMeaning("<EMAIL>");

        List<SysLookupValues> emailLookupValues = Collections.singletonList(sysLookupValues);
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setDescriptionChin("Customer Name");
        sysLookupValues1.setLookupMeaning("customer1");

        List<SysLookupValues> customCodeAndNameLookupValues = Collections.singletonList(sysLookupValues1);

        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(emailLookupValues).thenReturn(customCodeAndNameLookupValues);

        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(DateUtil.class);
        when(CommonUtils.getStrTransNull(anyString())).thenReturn("Value");
        when(DateUtil.convertDateToString(any(Date.class), anyString())).thenReturn("Formatted Date");

        assertDoesNotThrow(() -> helper.sendEmail(emailTaskList));
        verify(emailUtils, times(1)).sendMail(anyString(), anyString(), anyString(), anyString(), anyString());
    }
}

/* Ended by AICoder, pid:91e8bf9c4fo8d331496d093300290a9cc3a4c619 */
