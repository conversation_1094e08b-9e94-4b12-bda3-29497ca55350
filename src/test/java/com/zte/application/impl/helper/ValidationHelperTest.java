package com.zte.application.impl.helper;

import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import javax.validation.ConstraintViolation;
import javax.validation.Validator;
import javax.validation.groups.Default;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class ValidationHelperTest {

    private Validator mockValidator;
    private LocalValidatorFactoryBean mockValidatorFactory;

    @Before
    public void setUp() {
        // 初始化Mock对象
        mockValidator = mock(Validator.class);
        mockValidatorFactory = mock(LocalValidatorFactoryBean.class);

        // 将Mock Validator绑定到FactoryBean
        when(mockValidatorFactory.getValidator()).thenReturn(mockValidator);

        // 通过反射注入Mock对象到静态变量
        Whitebox.setInternalState(ValidationHelper.class, "validator", mockValidatorFactory);
    }

    @Test
    public void testValidate_WithViolations() {
        // 准备测试数据
        Object target = new Object();
        Set<ConstraintViolation<Object>> violations = new HashSet<>();

        // 创建两个Mock违反约束
        ConstraintViolation<Object> violation1 = mock(ConstraintViolation.class);
        when(violation1.getMessage()).thenReturn("字段不能为空");
        violations.add(violation1);

        ConstraintViolation<Object> violation2 = mock(ConstraintViolation.class);
        when(violation2.getMessage()).thenReturn("长度必须大于5");
        violations.add(violation2);

        // 设置Mock行为 - 确保分组参数匹配
        when(mockValidator.validate(eq(target), any(Class[].class))).thenReturn(violations);

        // 执行验证
        String result = ValidationHelper.validate(target); // 使用默认分组

        // 验证结果
        assertTrue(result.contains("字段不能为空;") && result.contains("长度必须大于5;"));
    }

    @Test
    public void testValidate_NoViolations() {
        Object target = new Object();
        when(mockValidator.validate(target)).thenReturn(Collections.emptySet());

        String result = ValidationHelper.validate(target);
        assertEquals("", result);
    }

    @Test
    public void testValidate_WithCustomGroups() {
        // 准备分组参数
        Class<?>[] groups = {GroupA.class, GroupB.class};
        Object target = new Object();

        // 设置Mock行为
        when(mockValidator.validate(target, groups)).thenReturn(Collections.emptySet());

        // 执行验证
        String result = ValidationHelper.validate(target, groups);

        // 验证结果和参数传递
        assertEquals("", result);
    }

    // 定义测试用分组接口
    private interface GroupA {}
    private interface GroupB {}
}