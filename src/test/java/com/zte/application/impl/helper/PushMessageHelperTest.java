package com.zte.application.impl.helper;

/**
 * {@code @description }
 *
 * <AUTHOR>
 * @date 2025/5/18 下午6:57
 */
/* Started by AICoder, pid:qffc6q2db124cf114470090430cac57f9ea79f0a */

import com.zte.application.TradeDataLogService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.interfaces.VO.OrderTaskInfoVO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({JacksonJsonConverUtil.class})
public class PushMessageHelperTest {
    @InjectMocks
    private PushMessageHelper pushMessageHelper;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private TradeDataLogService tradeDataLogService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // Mock static methods if needed
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
    }

    /**
     * 测试 pushMessageToB2B 方法，当调用阿里接口被禁用时。
     */
    @Test
    public void testPushMessageToB2B_InvokeAliDisabled() {
        List<OrderTaskInfoVO> invokeAliTaskList = Collections.singletonList(new OrderTaskInfoVO());
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning(Constant.FLAG_N);
        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(Collections.singletonList(sysLookupValues));

        assertDoesNotThrow(() -> pushMessageHelper.pushMessageToB2B(invokeAliTaskList));
        verify(tradeDataLogService, never()).pushDataOfExceptionRollback(any(CustomerDataLogDTO.class));
    }

    /**
     * 测试 pushMessageToB2B 方法，正常情况。
     */
    @Test
    public void testPushMessageToB2B_NormalCase() {
        OrderTaskInfoVO task1 = new OrderTaskInfoVO();
        task1.setOrderNo("order1");
        task1.setOrderType("2");
        task1.setCategory("category1");
        task1.setQuantity(new BigDecimal(100));
        task1.setDateEstimatedCompletion(new Date());
        task1.setDateMaterialEstimatedPrepared(new Date());
        task1.setDateAllMaterialEstimatedPrepared(new Date());
        task1.setDateScheduledProduction(new Date());
        task1.setTimeMaterialPrepared(new Date());
        task1.setDateAllMaterialPrepared(new Date());
        task1.setTimeProduction(new Date());
        task1.setLiability("2");

        List<OrderTaskInfoVO> invokeAliTaskList = Collections.singletonList(task1);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning(Constant.FLAG_Y);
        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(Collections.singletonList(sysLookupValues));
        when(JacksonJsonConverUtil.beanToJson(any())).thenReturn("{}");

        assertDoesNotThrow(() -> pushMessageHelper.pushMessageToB2B(invokeAliTaskList));
        verify(tradeDataLogService, times(1)).pushData(anyList());
    }

    /**
     * 测试 pushMessageToB2B 方法，正常情况-相关日期为空。
     */
    @Test
    public void testPushMessageToB2B_NormalCase1() {
        OrderTaskInfoVO task1 = new OrderTaskInfoVO();
        task1.setOrderNo("order1");
        task1.setOrderType("2");
        task1.setCategory("category1");
        task1.setQuantity(new BigDecimal(100));
        task1.setLiability("2");

        List<OrderTaskInfoVO> invokeAliTaskList = Collections.singletonList(task1);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning(Constant.FLAG_Y);
        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(Collections.singletonList(sysLookupValues));
        when(JacksonJsonConverUtil.beanToJson(any())).thenReturn("{}");

        assertDoesNotThrow(() -> pushMessageHelper.pushMessageToB2B(invokeAliTaskList));
        verify(tradeDataLogService, times(1)).pushData(anyList());
    }
}

/* Ended by AICoder, pid:qffc6q2db124cf114470090430cac57f9ea79f0a */
