package com.zte.application.impl.helper;

/**
 * {@code @description }
 *
 * <AUTHOR>
 * @date 2025/5/18 下午6:07
 */
/* Started by AICoder, pid:a6306d2e75sb7a5149c7080d4196dc7583b9baee */

import cn.hutool.core.bean.BeanUtil;
import com.google.common.collect.Lists;
import com.zte.application.PsDeliveryFeedbackService;
import com.zte.application.TradeDataLogService;
import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PsDeliveryFeedbackRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.domain.model.sncabind.PsTaskRepository;
import com.zte.gei.common.utils.JsonUtils;
import com.zte.infrastructure.feign.ApsInOneClient;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.VO.OrderTaskInfoVO;
import com.zte.interfaces.VO.PsDeliveryFeedbackDownImportVO;
import com.zte.interfaces.VO.PsDeliveryFeedbackVO;
import com.zte.interfaces.VO.PsDeliveryRemarkVO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.PsDeliveryFeedbackAliDTO;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.aps.TasksQueryDTO;
import com.zte.interfaces.dto.datawb.ProdPickListMainDTO;
import com.zte.springbootframe.common.model.Page;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({StringUtils.class, ObjectUtils.class, CollectionUtils.class, BeanUtil.class, CommonUtils.class, JsonUtils.class,ValidationHelper.class,DatawbRemoteService.class})
public class DeliveryFeedbackServiceHelperTest {

    @InjectMocks
    private DeliveryFeedbackServiceHelper helper;

    @Mock
    private PsDeliveryFeedbackRepository psDeliveryFeedbackRepository;

    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;

    @Mock
    private EmailServiceHelper emailServiceHelper;

    @Mock
    private PushMessageHelper pushMessageHelper;
    @Mock
    private PsTaskRepository taskRepository;
    @Mock
    private PsDeliveryFeedbackService service;

    @Mock
    private TradeDataLogService tradeDataLogService;

    @Mock
    private ApsInOneClient apsInOneClient;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        // Mock static methods if needed
        PowerMockito.mockStatic(ValidationHelper.class);
    }

    /**
     * 测试 listAlibabaOrderByTask 方法，当任务号列表为空时。
     */
    @Test
    public void testListAlibabaOrderByTask_TaskNosEmpty() {
        List<String> taskNos = Collections.emptyList();
        List<OrderTaskInfoVO> orderTaskInfoVOS = Collections.emptyList();

        when(taskRepository.listAlibabaTask(any())).thenReturn(orderTaskInfoVOS);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("ALIBABA");
        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(Collections.singletonList(sysLookupValues));

        PsDeliveryFeedbackAliDTO psDeliveryFeedbackAliDTO  = new PsDeliveryFeedbackAliDTO();
        psDeliveryFeedbackAliDTO.setTaskNos(taskNos);
        Map<String, OrderTaskInfoVO> result = helper.listAlibabaOrderByTask(psDeliveryFeedbackAliDTO);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 listAlibabaOrderByTask 方法，当任务号列表不为空时。
     */
    @Test
    public void testListAlibabaOrderByTask_TaskNosNotEmpty() {
        List<String> taskNos = Arrays.asList("task1", "task2");
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setOrderNo("task1");
        orderTaskInfoVO.setCustomerNo("ALI_CUSTOMER");
        OrderTaskInfoVO orderTaskInfoVO1 = new OrderTaskInfoVO();
        orderTaskInfoVO1.setOrderNo("task2");
        orderTaskInfoVO1.setCustomerNo("NON_ALI_CUSTOMER");

        List<OrderTaskInfoVO> orderTaskInfoVOS = Arrays.asList(
                orderTaskInfoVO,
                orderTaskInfoVO1
        );

        when(taskRepository.listAlibabaTask(any())).thenReturn(orderTaskInfoVOS);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("ALI_CUSTOMER");
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupMeaning("OTHER_CUSTOMER");
        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(Arrays.asList(
                sysLookupValues,
                sysLookupValues1
        ));

        PsDeliveryFeedbackAliDTO psDeliveryFeedbackAliDTO  = new PsDeliveryFeedbackAliDTO();
        psDeliveryFeedbackAliDTO.setTaskNos(taskNos);
        PowerMockito.mockStatic(DatawbRemoteService.class);
        when(DatawbRemoteService.queryPickListByTaskNos(any()))
                .thenReturn(new ArrayList<>());
        Map<String, OrderTaskInfoVO> result = helper.listAlibabaOrderByTask(psDeliveryFeedbackAliDTO);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey("task1"));
        assertEquals(Constant.ALIBABA, result.get("task1").getAliFlag());
    }

    /**
     * 测试 listAlibabaOrderByTask 方法，APS接口返回数据后字段被覆盖。
     */
    @Test
    public void testListAlibabaOrderByTask_WithAps() {
        List<String> taskNos = Arrays.asList("task1", "task2");
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setOrderNo("task1");
        orderTaskInfoVO.setCustomerNo("ALI_CUSTOMER");
        OrderTaskInfoVO orderTaskInfoVO1 = new OrderTaskInfoVO();
        orderTaskInfoVO1.setOrderNo("task2");
        orderTaskInfoVO1.setCustomerNo("NON_ALI_CUSTOMER");
        List<OrderTaskInfoVO> orderTaskInfoVOS = Arrays.asList(orderTaskInfoVO, orderTaskInfoVO1);

        when(taskRepository.listAlibabaTask(any())).thenReturn(orderTaskInfoVOS);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("ALI_CUSTOMER");
        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(Collections.singletonList(sysLookupValues));

        // mock APS返回
        PsTaskExtendedDTO ext = new PsTaskExtendedDTO();
        ext.setTaskNo("task1");
        Date now = new Date();
        ext.setSelfSupplyPrepareDate(now);
        ext.setFullPrepareDate(new Date(now.getTime() + 1000));
        ext.setExpectedCompletedDate(new Date(now.getTime() + 2000));
        ext.setPrepareProductDate(new Date(now.getTime() + 3000));
        ext.setPrepareCompletedDate(new Date(now.getTime() + 4000));
        Page<PsTaskExtendedDTO> page = new Page<>();
        page.setRows(Collections.singletonList(ext));
        when(apsInOneClient.customerTaskQuery(any(TasksQueryDTO.class))).thenReturn(page);

        PsDeliveryFeedbackAliDTO psDeliveryFeedbackAliDTO  = new PsDeliveryFeedbackAliDTO();
        psDeliveryFeedbackAliDTO.setTaskNos(taskNos);
        PowerMockito.mockStatic(DatawbRemoteService.class);
        when(DatawbRemoteService.queryPickListByTaskNos(any()))
                .thenReturn(new ArrayList<>());
        Map<String, OrderTaskInfoVO> result = helper.listAlibabaOrderByTask(psDeliveryFeedbackAliDTO);
        assertNotNull(result);
        assertEquals(2, result.size());
        OrderTaskInfoVO vo = result.get("task1");
        assertNotNull(vo);
        assertEquals(now, vo.getDateMaterialEstimatedPrepared());
        assertEquals(new Date(now.getTime() + 1000), vo.getDateAllMaterialEstimatedPrepared());
        assertEquals(new Date(now.getTime() + 2000), vo.getDateExpectedCompletion());
        assertEquals(new Date(now.getTime() + 3000), vo.getDateScheduledProduction());
        assertEquals(new Date(now.getTime() + 4000), vo.getDateEstimatedCompletion());
    }

    /* Started by AICoder, pid:r191a0aeffx242d140fa0a624162cd4d40b1f884 */
    /**
     * 测试 validateData 方法，当订单为空时。
     */
    @Test
    public void testValidateData_OrderNoEmpty() {
        PowerMockito.mockStatic(CommonUtils.class);

        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();

        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");
        when(ValidationHelper.validate(importVO)).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.contains("Error message,Error message"));
    }

    /**
     * 测试 validateData 方法，当日期字段为空时。
     */
    @Test
    public void testValidateData_DatesNull() {
        PowerMockito.mockStatic(CommonUtils.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setDateEstimatedCompletion(new Date());

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setDateExpectedCompletion(new Date());
        orderTaskInfoVOMap.put("order1", orderTaskInfoVO);

        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.contains("Error message"));
    }

    /**
     * 测试 validateData 方法，当任务状态为空时。
     */
    @Test
    public void testValidateData_TaskStatusNull() {
        PowerMockito.mockStatic(CommonUtils.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setDateEstimatedCompletion(new Date());
        importVO.setDateMaterialEstimatedPrepared(new Date());
        importVO.setDateAllMaterialEstimatedPrepared(new Date());
        importVO.setDateScheduledProduction(new Date());
        importVO.setTimeMaterialPrepared(new Date());
        importVO.setDateAllMaterialPrepared(new Date());
        importVO.setTimeProduction(new Date());

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();

        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.contains("Error message"));
    }

    /**
     * 测试 validateData 方法，当任务不是阿里任务时。
     */
    @Test
    public void testValidateData_NotAlibabaTask() {
        PowerMockito.mockStatic(CommonUtils.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setDateEstimatedCompletion(new Date());
        importVO.setDateMaterialEstimatedPrepared(new Date());
        importVO.setDateAllMaterialEstimatedPrepared(new Date());
        importVO.setDateScheduledProduction(new Date());
        importVO.setTimeMaterialPrepared(new Date());
        importVO.setDateAllMaterialPrepared(new Date());
        importVO.setTimeProduction(new Date());

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setAliFlag("NOT_ALIBABA");
        orderTaskInfoVO.setDateExpectedCompletion(new Date());
        orderTaskInfoVOMap.put("order1", orderTaskInfoVO);

        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.contains("Error message"));
    }

    /**
     * 测试 validateData 方法，当延期原因分类一级分类不正确时。
     */
    @Test
    public void testValidateData_ErrorFistReason() {
        PowerMockito.mockStatic(CommonUtils.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setDateEstimatedCompletion(new Date());
        importVO.setDateMaterialEstimatedPrepared(new Date());
        importVO.setDateAllMaterialEstimatedPrepared(new Date());
        importVO.setDateScheduledProduction(new Date());
        importVO.setTimeMaterialPrepared(new Date());
        importVO.setDateAllMaterialPrepared(new Date());
        importVO.setTimeProduction(new Date());
        importVO.setLiability("liability");
        importVO.setAbnormalNo("abnormalNo");
        importVO.setAbnormalCategoryFirst("category1");
        importVO.setAbnormalCategorySecond("category2");
        importVO.setAbnormalCategoryThird("category3");
        importVO.setRemark("remark");

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setAliFlag(Constant.ALIBABA);
        orderTaskInfoVO.setDateExpectedCompletion(new Date());
        orderTaskInfoVOMap.put("order1", orderTaskInfoVO);

        List<PsDeliveryRemarkVO> deliveryRemarkVOS = getPsDeliveryRemarkVOS();

        when(service.searchRemark(anyString())).thenReturn(deliveryRemarkVOS);
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.contains("Error message,Error message,Error message"));
    }

    /**
     * 测试 validateData 方法，当延期原因分类二级分类不正确时。
     */
    @Test
    public void testValidateData_ErrorSecondReason() {
        PowerMockito.mockStatic(CommonUtils.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setDateEstimatedCompletion(new Date());
        importVO.setDateMaterialEstimatedPrepared(new Date());
        importVO.setDateAllMaterialEstimatedPrepared(new Date());
        importVO.setDateScheduledProduction(new Date());
        importVO.setTimeMaterialPrepared(new Date());
        importVO.setDateAllMaterialPrepared(new Date());
        importVO.setTimeProduction(new Date());
        importVO.setLiability("liability");
        importVO.setAbnormalNo("abnormalNo");
        importVO.setAbnormalCategoryFirst("desc1");
        importVO.setAbnormalCategorySecond("category2");
        importVO.setAbnormalCategoryThird("category3");
        importVO.setRemark("remark");

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setAliFlag(Constant.ALIBABA);
        orderTaskInfoVO.setDateExpectedCompletion(new Date());
        orderTaskInfoVOMap.put("order1", orderTaskInfoVO);

        List<PsDeliveryRemarkVO> deliveryRemarkVOS = getPsDeliveryRemarkVOS();

        when(service.searchRemark(anyString())).thenReturn(deliveryRemarkVOS);
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.contains("Error message,Error message"));
    }

    /**
     * 测试 validateData 方法，当延期原因分类二级分类不正确时。
     */
    @Test
    public void testValidateData_ErrorSecondReason2() {
        PowerMockito.mockStatic(CommonUtils.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setDateEstimatedCompletion(new Date());
        importVO.setDateMaterialEstimatedPrepared(new Date());
        importVO.setDateAllMaterialEstimatedPrepared(new Date());
        importVO.setDateScheduledProduction(new Date());
        importVO.setTimeMaterialPrepared(new Date());
        importVO.setDateAllMaterialPrepared(new Date());
        importVO.setTimeProduction(new Date());
        importVO.setLiability("liability");
        importVO.setAbnormalNo("abnormalNo");
        importVO.setAbnormalCategoryFirst("value_1");
        importVO.setAbnormalCategorySecond("category2");
        importVO.setAbnormalCategoryThird("category3");
        importVO.setRemark("remark");

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setAliFlag(Constant.ALIBABA);
        orderTaskInfoVO.setDateExpectedCompletion(new Date());
        orderTaskInfoVOMap.put("order1", orderTaskInfoVO);

        List<PsDeliveryRemarkVO> deliveryRemarkVOS = getPsDeliveryRemarkVOS();

        when(service.searchRemark(anyString())).thenReturn(deliveryRemarkVOS);
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.contains("Error message,Error message"));
    }

    /**
     * 测试 validateData 方法，当延期原因分类二级分类不正确时。
     */
    @Test
    public void testValidateData_ErrorSecondReason1() {
        PowerMockito.mockStatic(CommonUtils.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setDateEstimatedCompletion(new Date());
        importVO.setDateMaterialEstimatedPrepared(new Date());
        importVO.setDateAllMaterialEstimatedPrepared(new Date());
        importVO.setDateScheduledProduction(new Date());
        importVO.setTimeMaterialPrepared(new Date());
        importVO.setDateAllMaterialPrepared(new Date());
        importVO.setTimeProduction(new Date());
        importVO.setLiability("liability");
        importVO.setAbnormalNo("abnormalNo");
        importVO.setAbnormalCategoryFirst("desc1");
        importVO.setAbnormalCategorySecond("desc3");
        importVO.setAbnormalCategoryThird("category3");
        importVO.setRemark("remark");

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setAliFlag(Constant.ALIBABA);
        orderTaskInfoVO.setDateExpectedCompletion(new Date());
        orderTaskInfoVOMap.put("order1", orderTaskInfoVO);

        List<PsDeliveryRemarkVO> deliveryRemarkVOS = getPsDeliveryRemarkVOSFirstChildren();

        when(service.searchRemark(anyString())).thenReturn(deliveryRemarkVOS);
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 validateData 方法，当延期原因分类三级分类不正确时。
     */
    @Test
    public void testValidateData_ErrorThirdReason() {
        PowerMockito.mockStatic(CommonUtils.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setDateEstimatedCompletion(new Date());
        importVO.setDateMaterialEstimatedPrepared(new Date());
        importVO.setDateAllMaterialEstimatedPrepared(new Date());
        importVO.setDateScheduledProduction(new Date());
        importVO.setTimeMaterialPrepared(new Date());
        importVO.setDateAllMaterialPrepared(new Date());
        importVO.setTimeProduction(new Date());
        importVO.setLiability("liability");
        importVO.setAbnormalNo("abnormalNo");
        importVO.setAbnormalCategoryFirst("value_1");
        importVO.setAbnormalCategorySecond("value_3");
        importVO.setAbnormalCategoryThird("category3");
        importVO.setRemark("remark");

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setAliFlag(Constant.ALIBABA);
        orderTaskInfoVO.setDateExpectedCompletion(new Date());
        orderTaskInfoVOMap.put("order1", orderTaskInfoVO);

        List<PsDeliveryRemarkVO> deliveryRemarkVOS = getPsDeliveryRemarkVOS();

        when(service.searchRemark(anyString())).thenReturn(deliveryRemarkVOS);
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.contains("Error message"));
    }

    /**
     * 测试 validateData 方法，当延期原因分类三级分类不正确时。
     */
    @Test
    public void testValidateData_ErrorThirdReason1() {
        PowerMockito.mockStatic(CommonUtils.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setDateEstimatedCompletion(new Date());
        importVO.setDateMaterialEstimatedPrepared(new Date());
        importVO.setDateAllMaterialEstimatedPrepared(new Date());
        importVO.setDateScheduledProduction(new Date());
        importVO.setTimeMaterialPrepared(new Date());
        importVO.setDateAllMaterialPrepared(new Date());
        importVO.setTimeProduction(new Date());
        importVO.setLiability("liability");
        importVO.setAbnormalNo("abnormalNo");
        importVO.setAbnormalCategoryFirst("desc1");
        importVO.setAbnormalCategorySecond("desc3");
        importVO.setAbnormalCategoryThird("category3");
        importVO.setRemark("remark");

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setAliFlag(Constant.ALIBABA);
        orderTaskInfoVO.setDateExpectedCompletion(new Date());
        orderTaskInfoVOMap.put("order1", orderTaskInfoVO);

        List<PsDeliveryRemarkVO> deliveryRemarkVOS = getPsDeliveryRemarkVOSNoChildren();

        when(service.searchRemark(anyString())).thenReturn(deliveryRemarkVOS);
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 validateData 方法，当延期原因分类三级分类不正确时。
     */
    @Test
    public void testValidateData_ErrorThirdReason2() {
        PowerMockito.mockStatic(CommonUtils.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setDateEstimatedCompletion(new Date());
        importVO.setDateMaterialEstimatedPrepared(new Date());
        importVO.setDateAllMaterialEstimatedPrepared(new Date());
        importVO.setDateScheduledProduction(new Date());
        importVO.setTimeMaterialPrepared(new Date());
        importVO.setDateAllMaterialPrepared(new Date());
        importVO.setTimeProduction(new Date());
        importVO.setLiability("liability");
        importVO.setAbnormalNo("abnormalNo");
        importVO.setAbnormalCategoryFirst("desc1");
        importVO.setAbnormalCategorySecond("desc3");
        importVO.setAbnormalCategoryThird("desc5");
        importVO.setRemark("remark");

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setAliFlag(Constant.ALIBABA);
        orderTaskInfoVO.setDateExpectedCompletion(new Date());
        orderTaskInfoVOMap.put("order1", orderTaskInfoVO);

        List<PsDeliveryRemarkVO> deliveryRemarkVOS = getPsDeliveryRemarkVOS();

        when(service.searchRemark(anyString())).thenReturn(deliveryRemarkVOS);
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }


    @NotNull
    private static List<PsDeliveryRemarkVO> getPsDeliveryRemarkVOS() {
        BigDecimal firstLookupType = new BigDecimal("1");
        BigDecimal secondLookupType = new BigDecimal("2");
        BigDecimal thirdLookupType = new BigDecimal("3");
        SysLookupValues firstLookupValues1 = new SysLookupValues();
        firstLookupValues1.setLookupType(firstLookupType);
        firstLookupValues1.setDescriptionChin("desc1");
        firstLookupValues1.setLookupMeaning("value_1");
        firstLookupValues1.setAttribute1("value1");
        firstLookupValues1.setLookupCode(new BigDecimal("100"));

        SysLookupValues secondLookupValues1 = new SysLookupValues();
        secondLookupValues1.setLookupType(secondLookupType);
        secondLookupValues1.setDescriptionChin("desc3");
        secondLookupValues1.setLookupMeaning("value_3");
        secondLookupValues1.setAttribute1("100");
        secondLookupValues1.setLookupCode(new BigDecimal("101"));

        SysLookupValues thirdLookupValues1 = new SysLookupValues();
        thirdLookupValues1.setLookupType(thirdLookupType);
        thirdLookupValues1.setDescriptionChin("desc5");
        thirdLookupValues1.setLookupMeaning("value_5");
        thirdLookupValues1.setAttribute1("101");

        List<SysLookupValues> firstClassList = Arrays.asList(firstLookupValues1);
        List<SysLookupValues> secondClassList = Arrays.asList(secondLookupValues1);
        List<SysLookupValues> thirdClassList = Arrays.asList(thirdLookupValues1);

        Map<String, List<SysLookupValues>> secondAttributeGroup = secondClassList.stream().collect(Collectors.groupingBy(SysLookupValues::getAttribute1));
        Map<String, List<SysLookupValues>> thirdAttributeGroup = thirdClassList.stream().collect(Collectors.groupingBy(SysLookupValues::getAttribute1));
        List<PsDeliveryRemarkVO> deliveryRemarkVOS = firstClassList.stream().sorted(Comparator.comparingInt(s -> Integer.parseInt(s.getLookupMeaning().split(Constant.UNDER_LINE)[1])))
                .map(sysLookupValues -> {
            PsDeliveryRemarkVO deliveryRemarkVO = new PsDeliveryRemarkVO();
            deliveryRemarkVO.setLabel(sysLookupValues.getDescriptionChin());
            deliveryRemarkVO.setValue(sysLookupValues.getLookupMeaning());
            deliveryRemarkVO.setChildren(secondAttributeGroup.getOrDefault(sysLookupValues.getLookupCode().toString(), Collections.emptyList())
                    .stream().sorted(Comparator.comparingInt(s -> Integer.parseInt(s.getLookupMeaning().split(Constant.UNDER_LINE)[1]))).map(secondLookupValue -> {
                        PsDeliveryRemarkVO secondDeliveryRemarkVO = new PsDeliveryRemarkVO();
                        secondDeliveryRemarkVO.setLabel(secondLookupValue.getDescriptionChin());
                        secondDeliveryRemarkVO.setValue(secondLookupValue.getLookupMeaning());
                        secondDeliveryRemarkVO.setChildren(thirdAttributeGroup.getOrDefault(secondLookupValue.getLookupCode().toString(), Collections.emptyList())
                                .stream().sorted(Comparator.comparingInt(s -> Integer.parseInt(s.getLookupMeaning().split(Constant.UNDER_LINE)[1]))).map(thirdLookupValues -> {
                                    PsDeliveryRemarkVO thirdDeliveryRemarkVO = new PsDeliveryRemarkVO();
                                    thirdDeliveryRemarkVO.setLabel(thirdLookupValues.getDescriptionChin());
                                    thirdDeliveryRemarkVO.setValue(thirdLookupValues.getLookupMeaning());
                                    return thirdDeliveryRemarkVO;
                                }).collect(Collectors.toList()));
                        return secondDeliveryRemarkVO;
                    }).collect(Collectors.toList()));
            return deliveryRemarkVO;
        }).collect(Collectors.toList());
        return deliveryRemarkVOS;
    }

    private static List<PsDeliveryRemarkVO> getPsDeliveryRemarkVOSNoChildren() {
        BigDecimal firstLookupType = new BigDecimal("1");
        BigDecimal secondLookupType = new BigDecimal("2");
        BigDecimal thirdLookupType = new BigDecimal("3");
        SysLookupValues firstLookupValues1 = new SysLookupValues();
        firstLookupValues1.setLookupType(firstLookupType);
        firstLookupValues1.setDescriptionChin("desc1");
        firstLookupValues1.setLookupMeaning("value_11");
        firstLookupValues1.setAttribute1("value1");
        firstLookupValues1.setLookupCode(new BigDecimal("100"));

        SysLookupValues secondLookupValues1 = new SysLookupValues();
        secondLookupValues1.setLookupType(secondLookupType);
        secondLookupValues1.setDescriptionChin("desc3");
        secondLookupValues1.setLookupMeaning("value_31");
        secondLookupValues1.setAttribute1("value_1");
        secondLookupValues1.setLookupCode(new BigDecimal("101"));

        SysLookupValues thirdLookupValues1 = new SysLookupValues();
        thirdLookupValues1.setLookupType(thirdLookupType);
        thirdLookupValues1.setDescriptionChin("desc5");
        thirdLookupValues1.setLookupMeaning("value_5");
        thirdLookupValues1.setAttribute1("101");

        List<SysLookupValues> firstClassList = Arrays.asList(firstLookupValues1);
        List<SysLookupValues> secondClassList = Arrays.asList(secondLookupValues1);
        List<SysLookupValues> thirdClassList = Arrays.asList(thirdLookupValues1);

        Map<String, List<SysLookupValues>> secondAttributeGroup = secondClassList.stream().collect(Collectors.groupingBy(SysLookupValues::getAttribute1));
        Map<String, List<SysLookupValues>> thirdAttributeGroup = thirdClassList.stream().collect(Collectors.groupingBy(SysLookupValues::getAttribute1));
        List<PsDeliveryRemarkVO> deliveryRemarkVOS = firstClassList.stream().sorted(Comparator.comparingInt(s -> Integer.parseInt(s.getLookupMeaning().split(Constant.UNDER_LINE)[1])))
                .map(sysLookupValues -> {
                    PsDeliveryRemarkVO deliveryRemarkVO = new PsDeliveryRemarkVO();
                    deliveryRemarkVO.setLabel(sysLookupValues.getDescriptionChin());
                    deliveryRemarkVO.setValue(sysLookupValues.getLookupMeaning());
                    deliveryRemarkVO.setChildren(secondAttributeGroup.getOrDefault(sysLookupValues.getLookupCode().toString(), Collections.emptyList())
                            .stream().sorted(Comparator.comparingInt(s -> Integer.parseInt(s.getLookupMeaning().split(Constant.UNDER_LINE)[1]))).map(secondLookupValue -> {
                                PsDeliveryRemarkVO secondDeliveryRemarkVO = new PsDeliveryRemarkVO();
                                secondDeliveryRemarkVO.setLabel(secondLookupValue.getDescriptionChin());
                                secondDeliveryRemarkVO.setValue(secondLookupValue.getLookupMeaning());
                                secondDeliveryRemarkVO.setChildren(thirdAttributeGroup.getOrDefault(secondLookupValue.getLookupCode().toString(), Collections.emptyList())
                                        .stream().sorted(Comparator.comparingInt(s -> Integer.parseInt(s.getLookupMeaning().split(Constant.UNDER_LINE)[1]))).map(thirdLookupValues -> {
                                            PsDeliveryRemarkVO thirdDeliveryRemarkVO = new PsDeliveryRemarkVO();
                                            thirdDeliveryRemarkVO.setLabel(thirdLookupValues.getDescriptionChin());
                                            thirdDeliveryRemarkVO.setValue(thirdLookupValues.getLookupMeaning());
                                            return thirdDeliveryRemarkVO;
                                        }).collect(Collectors.toList()));
                                return secondDeliveryRemarkVO;
                            }).collect(Collectors.toList()));
                    return deliveryRemarkVO;
                }).collect(Collectors.toList());
        return deliveryRemarkVOS;
    }

    private static List<PsDeliveryRemarkVO> getPsDeliveryRemarkVOSFirstChildren() {
        BigDecimal firstLookupType = new BigDecimal("1");
        BigDecimal secondLookupType = new BigDecimal("2");
        BigDecimal thirdLookupType = new BigDecimal("3");
        SysLookupValues firstLookupValues1 = new SysLookupValues();
        firstLookupValues1.setLookupType(firstLookupType);
        firstLookupValues1.setDescriptionChin("desc1");
        firstLookupValues1.setLookupMeaning("value_1");
        firstLookupValues1.setAttribute1("value1");

        SysLookupValues secondLookupValues1 = new SysLookupValues();
        secondLookupValues1.setLookupType(secondLookupType);
        secondLookupValues1.setDescriptionChin("desc3");
        secondLookupValues1.setLookupMeaning("value_31");
        secondLookupValues1.setAttribute1("value_1");

        SysLookupValues thirdLookupValues1 = new SysLookupValues();
        thirdLookupValues1.setLookupType(thirdLookupType);
        thirdLookupValues1.setDescriptionChin("desc5");
        thirdLookupValues1.setLookupMeaning("value_5");
        thirdLookupValues1.setAttribute1("value_3");

        List<SysLookupValues> firstClassList = Arrays.asList(firstLookupValues1);
        List<SysLookupValues> secondClassList = Arrays.asList(secondLookupValues1);
        List<SysLookupValues> thirdClassList = Arrays.asList(thirdLookupValues1);

        Map<String, List<SysLookupValues>> secondAttributeGroup = secondClassList.stream().collect(Collectors.groupingBy(SysLookupValues::getAttribute1));
        Map<String, List<SysLookupValues>> thirdAttributeGroup = thirdClassList.stream().collect(Collectors.groupingBy(SysLookupValues::getAttribute1));
        List<PsDeliveryRemarkVO> deliveryRemarkVOS = firstClassList.stream().sorted(Comparator.comparingInt(s -> Integer.parseInt(s.getLookupMeaning().split(Constant.UNDER_LINE)[1])))
                .map(sysLookupValues -> {
                    PsDeliveryRemarkVO deliveryRemarkVO = new PsDeliveryRemarkVO();
                    deliveryRemarkVO.setLabel(sysLookupValues.getDescriptionChin());
                    deliveryRemarkVO.setValue(sysLookupValues.getLookupMeaning());
                    deliveryRemarkVO.setChildren(secondAttributeGroup.getOrDefault(sysLookupValues.getLookupMeaning(), Collections.emptyList())
                            .stream().sorted(Comparator.comparingInt(s -> Integer.parseInt(s.getLookupMeaning().split(Constant.UNDER_LINE)[1]))).map(secondLookupValue -> {
                                PsDeliveryRemarkVO secondDeliveryRemarkVO = new PsDeliveryRemarkVO();
                                secondDeliveryRemarkVO.setLabel(secondLookupValue.getDescriptionChin());
                                secondDeliveryRemarkVO.setValue(secondLookupValue.getLookupMeaning());
                                secondDeliveryRemarkVO.setChildren(thirdAttributeGroup.getOrDefault(secondLookupValue.getLookupMeaning(), Collections.emptyList())
                                        .stream().sorted(Comparator.comparingInt(s -> Integer.parseInt(s.getLookupMeaning().split(Constant.UNDER_LINE)[1]))).map(thirdLookupValues -> {
                                            PsDeliveryRemarkVO thirdDeliveryRemarkVO = new PsDeliveryRemarkVO();
                                            thirdDeliveryRemarkVO.setLabel(thirdLookupValues.getDescriptionChin());
                                            thirdDeliveryRemarkVO.setValue(thirdLookupValues.getLookupMeaning());
                                            return thirdDeliveryRemarkVO;
                                        }).collect(Collectors.toList()));
                                return secondDeliveryRemarkVO;
                            }).collect(Collectors.toList()));
                    return deliveryRemarkVO;
                }).collect(Collectors.toList());
        return deliveryRemarkVOS;
    }

    /**
     * 测试 validateData 方法，当任务延期且缺少延期原因时。
     */
    @Test
    public void testValidateData_TaskDelayedWithoutReason() {
        PowerMockito.mockStatic(CommonUtils.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setDateEstimatedCompletion(new Date(System.currentTimeMillis() + 86400000L)); // Tomorrow
        importVO.setDateMaterialEstimatedPrepared(new Date());
        importVO.setDateAllMaterialEstimatedPrepared(new Date());
        importVO.setDateScheduledProduction(new Date());
        importVO.setTimeMaterialPrepared(new Date());
        importVO.setDateAllMaterialPrepared(new Date());
        importVO.setTimeProduction(new Date());

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setAliFlag(Constant.ALIBABA);
        orderTaskInfoVO.setDateExpectedCompletion(new Date()); // Today
        orderTaskInfoVOMap.put("order1", orderTaskInfoVO);

        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.contains("Error message"));
    }

    /**
     * 测试 validateData 方法，当任务延期且有延期原因时。
     */
    @Test
    public void testValidateData_TaskDelayedWithInReason() {
        PowerMockito.mockStatic(CommonUtils.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setDateEstimatedCompletion(new Date(System.currentTimeMillis() + 86400000L)); // Tomorrow
        importVO.setDateMaterialEstimatedPrepared(new Date());
        importVO.setDateAllMaterialEstimatedPrepared(new Date());
        importVO.setDateScheduledProduction(new Date());
        importVO.setTimeMaterialPrepared(new Date());
        importVO.setDateAllMaterialPrepared(new Date());
        importVO.setTimeProduction(new Date());
        importVO.setLiability("aa");
        importVO.setAbnormalNo("aa");
        importVO.setAbnormalCategoryFirst("aa");
        importVO.setAbnormalCategorySecond("aa");
        importVO.setAbnormalCategoryThird("aa");
        importVO.setRemark("aa");

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setAliFlag(Constant.ALIBABA);
        orderTaskInfoVO.setDateExpectedCompletion(new Date()); // Today
        orderTaskInfoVOMap.put("order1", orderTaskInfoVO);

        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 validateData 方法，当任务延期且有延期原因时。
     */
    @Test
    public void testValidateData_TaskDelayedWithInReason1() {
        PowerMockito.mockStatic(CommonUtils.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setDateMaterialEstimatedPrepared(new Date());
        importVO.setDateAllMaterialEstimatedPrepared(new Date());
        importVO.setDateScheduledProduction(new Date());
        importVO.setTimeMaterialPrepared(new Date());
        importVO.setDateAllMaterialPrepared(new Date());
        importVO.setTimeProduction(new Date());
        importVO.setLiability("aa");
        importVO.setAbnormalNo("aa");
        importVO.setAbnormalCategoryFirst("aa");
        importVO.setAbnormalCategorySecond("aa");
        importVO.setAbnormalCategoryThird("aa");
        importVO.setRemark("aa");

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setAliFlag(Constant.ALIBABA);
        orderTaskInfoVOMap.put("order1", orderTaskInfoVO);

        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 validateData 方法，正常情况。
     */
    @Test
    public void testValidateData_NormalCase() {
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setDateEstimatedCompletion(new Date(System.currentTimeMillis() - 86400000L)); // Yesterday
        importVO.setDateMaterialEstimatedPrepared(new Date());
        importVO.setDateAllMaterialEstimatedPrepared(new Date());
        importVO.setDateScheduledProduction(new Date());
        importVO.setTimeMaterialPrepared(new Date());
        importVO.setDateAllMaterialPrepared(new Date());
        importVO.setTimeProduction(new Date());
        importVO.setLiability("liability");
        importVO.setAbnormalNo("abnormalNo");
        importVO.setAbnormalCategoryFirst("category1");
        importVO.setAbnormalCategorySecond("category2");
        importVO.setAbnormalCategoryThird("category3");
        importVO.setRemark("remark");

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setAliFlag(Constant.ALIBABA);
        orderTaskInfoVO.setDateExpectedCompletion(new Date(System.currentTimeMillis() - 86400000L)); // Yesterday
        orderTaskInfoVOMap.put("order1", orderTaskInfoVO);

        when(service.searchRemark(anyString())).thenReturn(Collections.emptyList());
        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 validateData 方法，正常情况。
     */
    @Test
    public void testValidateData_NormalCase2() {
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setDateMaterialEstimatedPrepared(new Date());
        importVO.setDateAllMaterialEstimatedPrepared(new Date());
        importVO.setDateEstimatedCompletion(new Date());
        importVO.setDateScheduledProduction(new Date());
        importVO.setTimeMaterialPrepared(new Date());
        importVO.setDateAllMaterialPrepared(new Date());
        importVO.setTimeProduction(new Date());
        importVO.setLiability("liability");
        importVO.setAbnormalNo("abnormalNo");
        importVO.setAbnormalCategoryFirst("category1");
        importVO.setAbnormalCategorySecond("category2");
        importVO.setAbnormalCategoryThird("category3");
        importVO.setRemark("remark");

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setAliFlag(Constant.ALIBABA);
        orderTaskInfoVO.setDateExpectedCompletion(new Date(System.currentTimeMillis() - 86400000L)); // Yesterday
        orderTaskInfoVOMap.put("order1", orderTaskInfoVO);

        when(service.searchRemark(anyString())).thenReturn(Collections.emptyList());
        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 validateData 方法，延期编码重复。
     */
    @Test
    public void testValidateData_RepeatAbnormalNo() {
        PowerMockito.mockStatic(CommonUtils.class);
        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");
        importVO.setDateMaterialEstimatedPrepared(new Date());
        importVO.setDateAllMaterialEstimatedPrepared(new Date());
        importVO.setDateScheduledProduction(new Date());
        importVO.setTimeMaterialPrepared(new Date());
        importVO.setDateAllMaterialPrepared(new Date());
        importVO.setTimeProduction(new Date());
        importVO.setDateEstimatedCompletion(new Date());
        importVO.setLiability("liability");
        importVO.setAbnormalNo("abnormalNo");
        importVO.setAbnormalCategoryFirst("category1");
        importVO.setAbnormalCategorySecond("category2");
        importVO.setAbnormalCategoryThird("category3");
        importVO.setRemark("remark");

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setAliFlag(Constant.ALIBABA);
        orderTaskInfoVO.setDateExpectedCompletion(new Date(System.currentTimeMillis() - 86400000L)); // Yesterday
        orderTaskInfoVOMap.put("order1", orderTaskInfoVO);

        when(psDeliveryFeedbackRepository.listByAbnormalNo(any())).thenReturn(Collections.singletonList("aa"));
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");

        String result = helper.validateData(importVO, orderTaskInfoVOMap);
        assertNotNull(result);
        assertTrue(result.contains("Error message"));

        importVO.setAbnormalNo("0");
        ReflectionTestUtils.setField(helper,"canRepeatAbnormalNo","0");
        result = helper.validateData(importVO, orderTaskInfoVOMap);
        Assert.assertFalse(result.contains("Error message"));

    }
    /* Ended by AICoder, pid:r191a0aeffx242d140fa0a624162cd4d40b1f884 */

    /**
     * 测试 sendEmailOrPushAliSaveMessage 方法，当任务列表为空时。
     */
    @Test
    public void testSendEmailOrPushAliSaveMessage_NoTasks() {
        OrderTaskInfoVO task1 = new OrderTaskInfoVO();
        List<OrderTaskInfoVO> alibabaTasks = Collections.singletonList(task1);

        int result = helper.sendEmailOrPushAliSaveMessage(alibabaTasks, false);
        assertEquals(0, result);
        verify(emailServiceHelper, never()).sendEmail(anyList());
        verify(pushMessageHelper, times(1)).pushMessageToB2B(anyList());
    }

    /**
     * 测试 sendEmailOrPushAliSaveMessage 方法，当任务列表为空时。
     */
    @Test
    public void testSendEmailOrPushAliSaveMessage_NoTasks2() {
        int result = helper.sendEmailOrPushAliSaveMessage(Collections.emptyList(), false);
        assertEquals(0, result);
        verify(emailServiceHelper, never()).sendEmail(anyList());
        verify(pushMessageHelper, never()).pushMessageToB2B(anyList());
    }

    /**
     * 测试 sendEmailOrPushAliSaveMessage 方法，当任务延期时。
     */
    @Test
    public void testSendEmailOrPushAliSaveMessage_TaskDelay() {
        OrderTaskInfoVO task1 = new OrderTaskInfoVO();
        List<OrderTaskInfoVO> alibabaTasks = Collections.singletonList(task1);

        int result = helper.sendEmailOrPushAliSaveMessage(alibabaTasks, true);
        assertEquals(0, result);
        verify(emailServiceHelper, times(1)).sendEmail(anyList());
        verify(pushMessageHelper, times(0)).pushMessageToB2B(anyList());
    }

    /**
     * 测试 sendEmailOrPushAliSaveMessage 方法，当任务列表不为空时。
     */
    @Test
    public void testSendEmailOrPushAliSaveMessage_TasksExist() {
        OrderTaskInfoVO task1 = new OrderTaskInfoVO();
        task1.setOrderNo("task1");
        task1.setDateExpectedCompletion(new Date(System.currentTimeMillis() - 86400000L)); // Yesterday
        task1.setDateEstimatedCompletion(new Date()); // Today

        OrderTaskInfoVO task2 = new OrderTaskInfoVO();
        task2.setOrderNo("task2");
        task2.setOrderType("1");
        task2.setCategory("a");
        task2.setQuantity(BigDecimal.ONE);
        task2.setDateExpectedCompletion(new Date(System.currentTimeMillis() + 86400000L)); // Tomorrow
        task2.setDateEstimatedCompletion(new Date()); // Today
        task2.setDateMaterialEstimatedPrepared(new Date());
        task2.setDateAllMaterialEstimatedPrepared(new Date());
        task2.setDateScheduledProduction(new Date());

        List<OrderTaskInfoVO> alibabaTasks = Arrays.asList(task1, task2);

        PsDeliveryFeedbackVO deliveryFeedbackVO = new PsDeliveryFeedbackVO();
        deliveryFeedbackVO.setOrderNo("task1");

        when(psDeliveryFeedbackRepository.listByTask(anyList())).thenReturn(Lists.newArrayList(deliveryFeedbackVO));

        when(psDeliveryFeedbackRepository.batchInsert(anyList())).thenReturn(1);
        when(psDeliveryFeedbackRepository.batchUpdateByPrimaryKey(anyList())).thenReturn(1);

        int result = helper.sendEmailOrPushAliSaveMessage(alibabaTasks, true);
        assertEquals(2, result);
        verify(emailServiceHelper, times(1)).sendEmail(anyList());
        verify(pushMessageHelper, times(1)).pushMessageToB2B(anyList());
    }

    /**
     * 测试 sendEmailOrPushAliSaveMessage 方法，任意一个完工日期为空。
     */
    @Test
    public void testSendEmailOrPushAliSaveMessage_dateCompletionAnyNull() {
        OrderTaskInfoVO task1 = new OrderTaskInfoVO();
        task1.setOrderNo("task1");
        task1.setDateExpectedCompletion(new Date()); // Yesterday
        task1.setDateEstimatedCompletion(new Date(System.currentTimeMillis() - 86400000L)); // Today

        OrderTaskInfoVO task2 = new OrderTaskInfoVO();
        task2.setOrderNo("task2");
        task2.setOrderType("1");
        task2.setCategory("a");
        task2.setQuantity(BigDecimal.ONE);
        task2.setDateExpectedCompletion(new Date(System.currentTimeMillis() + 86400000L)); // Tomorrow
        task2.setDateEstimatedCompletion(new Date()); // Today
        task2.setDateMaterialEstimatedPrepared(new Date());
        task2.setDateAllMaterialEstimatedPrepared(new Date());
        task2.setDateScheduledProduction(new Date());

        List<OrderTaskInfoVO> alibabaTasks = Arrays.asList(task1, task2);

        PsDeliveryFeedbackVO deliveryFeedbackVO = new PsDeliveryFeedbackVO();
        deliveryFeedbackVO.setOrderNo("task1");

        when(psDeliveryFeedbackRepository.listByTask(anyList())).thenReturn(Lists.newArrayList(deliveryFeedbackVO));

        when(psDeliveryFeedbackRepository.batchInsert(anyList())).thenReturn(1);
        when(psDeliveryFeedbackRepository.batchUpdateByPrimaryKey(anyList())).thenReturn(1);


        int result = helper.sendEmailOrPushAliSaveMessage(alibabaTasks, true);
        assertEquals(2, result);
        verify(emailServiceHelper, times(1)).sendEmail(anyList());
        verify(pushMessageHelper, times(1)).pushMessageToB2B(anyList());
    }

    /**
     * 测试 insertOrUpdateDataByOrderNo 方法，新增或更新都为空时。
     */
    @Test
    public void testInsertOrUpdateDataByOrderNo_NullTask() {
        when(psDeliveryFeedbackRepository.listByTask(anyList())).thenReturn(Collections.emptyList());
        int result = helper.insertOrUpdateDataByOrderNo(Collections.emptyList(), true);
        assertEquals(0, result);
    }

    /* Started by AICoder, pid:pdfe6q0784u55a1144100a7610cefe5fb6b7d789 */
    /**
     * 测试 listBusinessScene 方法，正常情况。
     */
    @Test
    public void testListBusinessScene_NormalCase() {
        PowerMockito.mockStatic(JsonUtils.class);
        PowerMockito.mockStatic(StringUtils.class);
        SysLookupValues value1 = new SysLookupValues();
        value1.setLookupMeaning("Value1");

        SysLookupValues value2 = new SysLookupValues();
        value2.setLookupMeaning("Value2");

        List<SysLookupValues> sysList = Arrays.asList(value1, value2);

        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(sysList);
        when(StringUtils.isNotBlank(anyString())).thenReturn(true);

        List<PsDeliveryFeedbackVO> result = helper.listBusinessScene();

        assertEquals(2, result.size());
    }

    /**
     * 测试 listBusinessScene 方法，当查询结果为空时。
     */
    @Test
    public void testListBusinessScene_EmptyResult() {
        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(Collections.emptyList());

        List<PsDeliveryFeedbackVO> result = helper.listBusinessScene();

        assertTrue(result.isEmpty());
    }

    /**
     * 测试 listBusinessScene 方法，当查询结果包含空值时。
     */
    @Test
    public void testListBusinessScene_WithEmptyValues() {
        PowerMockito.mockStatic(JsonUtils.class);
        PowerMockito.mockStatic(StringUtils.class);
        SysLookupValues value1 = new SysLookupValues();
        value1.setLookupMeaning("");

        SysLookupValues value2 = new SysLookupValues();
        value2.setLookupMeaning("Value2");

        List<SysLookupValues> sysList = Arrays.asList(value1, value2);

        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(sysList);
        when(StringUtils.isNotBlank("")).thenReturn(false);
        when(StringUtils.isNotBlank("Value2")).thenReturn(true);

        List<PsDeliveryFeedbackVO> result = helper.listBusinessScene();

        assertEquals(2, result.size());
    }
    /* Ended by AICoder, pid:pdfe6q0784u55a1144100a7610cefe5fb6b7d789 */

    @Test
    public void testGetClassNodes_AllTypesHaveData() {
        /** 测试所有类型都有数据的情况 */
        BigDecimal firstLookupType = new BigDecimal("1");
        BigDecimal secondLookupType = new BigDecimal("2");
        BigDecimal thirdLookupType = new BigDecimal("3");
        SysLookupValues firstLookupValues1 = new SysLookupValues();
        firstLookupValues1.setLookupType(firstLookupType);
        firstLookupValues1.setDescriptionChin("desc1");
        firstLookupValues1.setLookupMeaning("value_1");
        firstLookupValues1.setAttribute1("value1");
        firstLookupValues1.setLookupCode(new BigDecimal("100"));

        SysLookupValues secondLookupValues1 = new SysLookupValues();
        secondLookupValues1.setLookupType(secondLookupType);
        secondLookupValues1.setDescriptionChin("desc3");
        secondLookupValues1.setLookupMeaning("value_3");
        secondLookupValues1.setAttribute1("100");
        secondLookupValues1.setLookupCode(new BigDecimal("101"));

        SysLookupValues thirdLookupValues1 = new SysLookupValues();
        thirdLookupValues1.setLookupType(thirdLookupType);
        thirdLookupValues1.setDescriptionChin("desc5");
        thirdLookupValues1.setLookupMeaning("value_5");
        thirdLookupValues1.setAttribute1("101");

        List<SysLookupValues> firstClassList = Arrays.asList(firstLookupValues1);
        List<SysLookupValues> secondClassList = Arrays.asList(secondLookupValues1);
        List<SysLookupValues> thirdClassList = Arrays.asList(thirdLookupValues1);

        when(sysLookupValuesRepository.selectByTypeBatch(Collections.singletonList(firstLookupType.toString())))
                .thenReturn(firstClassList);
        when(sysLookupValuesRepository.selectByTypeBatch(Collections.singletonList(secondLookupType.toString())))
                .thenReturn(secondClassList);
        when(sysLookupValuesRepository.selectByTypeBatch(Collections.singletonList(thirdLookupType.toString())))
                .thenReturn(thirdClassList);

        List<PsDeliveryRemarkVO> result = helper.getClassNodes(firstLookupType.toString(), secondLookupType.toString(), thirdLookupType.toString());

        assertNotNull(result);
        assertEquals(1, result.size());
        // 进一步验证结果的结构和内容
    }

    @Test
    public void testGetClassNodes_SecondTypeHasNoData() {
        /** 测试第二类型没有数据的情况 */
        BigDecimal firstLookupType = new BigDecimal("1");
        BigDecimal secondLookupType = new BigDecimal("2");
        BigDecimal thirdLookupType = new BigDecimal("3");
        SysLookupValues firstLookupValues1 = new SysLookupValues();
        firstLookupValues1.setLookupType(firstLookupType);
        firstLookupValues1.setDescriptionChin("desc1");
        firstLookupValues1.setLookupMeaning("value1");
        firstLookupValues1.setAttribute1("value1");
        firstLookupValues1.setLookupCode(new BigDecimal("100"));

        SysLookupValues thirdLookupValues1 = new SysLookupValues();
        thirdLookupValues1.setLookupType(thirdLookupType);
        thirdLookupValues1.setDescriptionChin("desc5");
        thirdLookupValues1.setLookupMeaning("value5");
        thirdLookupValues1.setAttribute1("103");

        List<SysLookupValues> firstClassList = Arrays.asList(firstLookupValues1);
        List<SysLookupValues> secondClassList = Collections.emptyList();
        List<SysLookupValues> thirdClassList = Arrays.asList(thirdLookupValues1);

        when(sysLookupValuesRepository.selectByTypeBatch(Collections.singletonList(firstLookupType.toString())))
                .thenReturn(firstClassList);
        when(sysLookupValuesRepository.selectByTypeBatch(Collections.singletonList(secondLookupType.toString())))
                .thenReturn(secondClassList);
        when(sysLookupValuesRepository.selectByTypeBatch(Collections.singletonList(thirdLookupType.toString())))
                .thenReturn(thirdClassList);

        List<PsDeliveryRemarkVO> result = helper.getClassNodes(firstLookupType.toString(), secondLookupType.toString(), thirdLookupType.toString());

        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.get(0).getChildren().isEmpty());
    }

    @Test
    public void testGetClassNodes_ThirdTypeHasNoData() {
        /** 测试第三类型没有数据的情况 */
        BigDecimal firstLookupType = new BigDecimal("1");
        BigDecimal secondLookupType = new BigDecimal("2");
        BigDecimal thirdLookupType = new BigDecimal("3");
        SysLookupValues firstLookupValues1 = new SysLookupValues();
        firstLookupValues1.setLookupType(firstLookupType);
        firstLookupValues1.setDescriptionChin("desc1");
        firstLookupValues1.setLookupMeaning("value1");
        firstLookupValues1.setAttribute1("value1");
        firstLookupValues1.setLookupCode(new BigDecimal("100"));

        SysLookupValues secondLookupValues1 = new SysLookupValues();
        secondLookupValues1.setLookupType(secondLookupType);
        secondLookupValues1.setDescriptionChin("desc3");
        secondLookupValues1.setLookupMeaning("value3");
        secondLookupValues1.setLookupCode(new BigDecimal("101"));
        secondLookupValues1.setAttribute1("100");

        List<SysLookupValues> firstClassList = Arrays.asList(firstLookupValues1);
        List<SysLookupValues> secondClassList = Arrays.asList(secondLookupValues1);

        List<SysLookupValues> thirdClassList = Collections.emptyList();

        when(sysLookupValuesRepository.selectByTypeBatch(Collections.singletonList(firstLookupType.toString())))
                .thenReturn(firstClassList);
        when(sysLookupValuesRepository.selectByTypeBatch(Collections.singletonList(secondLookupType.toString())))
                .thenReturn(secondClassList);
        when(sysLookupValuesRepository.selectByTypeBatch(Collections.singletonList(thirdLookupType.toString())))
                .thenReturn(thirdClassList);

        List<PsDeliveryRemarkVO> result = helper.getClassNodes(firstLookupType.toString(), secondLookupType.toString(), thirdLookupType.toString());

        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getChildren().size());
        assertTrue(result.get(0).getChildren().get(0).getChildren().isEmpty());
    }

    @Test
    public void testListAlibabaOrderByTask_FirstPickingDate() {
        List<String> taskNos = Arrays.asList("task1");
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setOrderNo("task1");
        orderTaskInfoVO.setCustomerNo("ALI_CUSTOMER");
        List<OrderTaskInfoVO> orderTaskInfoVOS = Collections.singletonList(orderTaskInfoVO);

        when(taskRepository.listAlibabaTask(any())).thenReturn(orderTaskInfoVOS);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("ALI_CUSTOMER");
        when(sysLookupValuesRepository.selectByTypeBatch(anyList())).thenReturn(Collections.singletonList(sysLookupValues));

        // mock APS返回
        PsTaskExtendedDTO ext = new PsTaskExtendedDTO();
        ext.setTaskNo("task1");
        Page<PsTaskExtendedDTO> page = new Page<>();
        page.setRows(Collections.singletonList(ext));
        when(apsInOneClient.customerTaskQuery(any(TasksQueryDTO.class))).thenReturn(page);

        // mock tradeDataLogService.selectFirstDateOfTaskNo
        Date firstPickingDate = new Date();
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setTaskNo("task1");
        customerDataLogDTO.setLastUpdatedDate(firstPickingDate);
        when(tradeDataLogService.selectFirstDateOfTaskNo(anyList(), any()))
            .thenReturn(Collections.singletonList(customerDataLogDTO));

        PowerMockito.mockStatic(DatawbRemoteService.class);
        ProdPickListMainDTO prodPickListMainDTO = new ProdPickListMainDTO();
        prodPickListMainDTO.setTaskNo("task1");
        prodPickListMainDTO.setBillNumber("billNumber1");
        when(DatawbRemoteService.queryPickListByTaskNos(any()))
                .thenReturn(Collections.singletonList(prodPickListMainDTO));
        PsDeliveryFeedbackAliDTO psDeliveryFeedbackAliDTO  = new PsDeliveryFeedbackAliDTO();
        psDeliveryFeedbackAliDTO.setTaskNos(taskNos);
        Map<String, OrderTaskInfoVO> result = helper.listAlibabaOrderByTask(psDeliveryFeedbackAliDTO);

        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("task1"));
        assertEquals(firstPickingDate, result.get("task1").getFirstPickingDate());
    }

    @Test
    public void testValidateData_SetFirstPickingDate() {
        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.getLmbMessage(anyString())).thenReturn("Error message");
        when(ValidationHelper.validate(any())).thenReturn("");

        // 构造 orderTaskInfoVO，设置 firstPickingDate
        Date firstPickingDate = new Date();
        OrderTaskInfoVO orderTaskInfoVO = new OrderTaskInfoVO();
        orderTaskInfoVO.setFirstPickingDate(firstPickingDate);

        Map<String, OrderTaskInfoVO> orderTaskInfoVOMap = new HashMap<>();
        orderTaskInfoVOMap.put("order1", orderTaskInfoVO);

        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        importVO.setOrderNo("order1");

        // 由于 checkTimeProduction 需要 timeProduction 不为 null，否则直接 return
        importVO.setTimeProduction(new Date(firstPickingDate.getTime() + 10000));
        importVO.setDateAllMaterialPrepared(new Date(firstPickingDate.getTime() - 10000));
        importVO.setTimeMaterialPrepared(new Date(firstPickingDate.getTime() - 10000));

        String result = helper.validateData(importVO, orderTaskInfoVOMap);

        // 校验首次领料时间被设置
        assertEquals(firstPickingDate, importVO.getFirstPickingDate());
    }

    @Test
    public void testCheckTimeProduction_TimeProductionBeforeFirstPickingDate() {
        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.getLmbMessage(anyString(), any(String[].class))).thenReturn("时间错误提示");

        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        Date firstPickingDate = new Date();
        Date timeProduction = new Date(firstPickingDate.getTime() - 10000); // 比首次领料早
        importVO.setFirstPickingDate(firstPickingDate);
        importVO.setTimeProduction(timeProduction);
        importVO.setDateAllMaterialPrepared(new Date(firstPickingDate.getTime() - 20000));
        importVO.setTimeMaterialPrepared(new Date(firstPickingDate.getTime() - 20000));

        StringBuilder sb = new StringBuilder();
        // 直接调用私有方法
        helper.getClass().getDeclaredMethods();
        // 反射调用私有方法
        try {
            java.lang.reflect.Method method = helper.getClass().getDeclaredMethod("checkTimeProduction", PsDeliveryFeedbackDownImportVO.class, StringBuilder.class);
            method.setAccessible(true);
            method.invoke(helper, importVO, sb);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // 应该有首次领料校验的错误
        assertTrue(sb.toString().contains("时间错误提示"));
    }

    @Test
    public void testCheckTimeProduction_TimeProductionBeforeDateAllMaterialPrepared() {
        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.getLmbMessage(anyString(), any(String[].class))).thenReturn("全部物料实际齐套日期错误");

        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        Date dateAllMaterialPrepared = new Date();
        Date timeProduction = new Date(dateAllMaterialPrepared.getTime() - 10000); // 比全部物料实际齐套日期早
        importVO.setFirstPickingDate(new Date(dateAllMaterialPrepared.getTime() - 20000));
        importVO.setTimeProduction(timeProduction);
        importVO.setDateAllMaterialPrepared(dateAllMaterialPrepared);
        importVO.setTimeMaterialPrepared(new Date(dateAllMaterialPrepared.getTime() - 20000));

        StringBuilder sb = new StringBuilder();
        try {
            java.lang.reflect.Method method = helper.getClass().getDeclaredMethod("checkTimeProduction", PsDeliveryFeedbackDownImportVO.class, StringBuilder.class);
            method.setAccessible(true);
            method.invoke(helper, importVO, sb);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        assertTrue(sb.toString().contains("全部物料实际齐套日期错误"));
    }

    @Test
    public void testCheckTimeProduction_TimeProductionBeforeTimeMaterialPrepared() {
        PowerMockito.mockStatic(CommonUtils.class);
        when(CommonUtils.getLmbMessage(anyString(), any(String[].class))).thenReturn("厂商自供料实际齐套日期错误");

        PsDeliveryFeedbackDownImportVO importVO = new PsDeliveryFeedbackDownImportVO();
        Date timeMaterialPrepared = new Date();
        Date timeProduction = new Date(timeMaterialPrepared.getTime() - 10000); // 比厂商自供料实际齐套日期早
        importVO.setFirstPickingDate(new Date(timeMaterialPrepared.getTime() - 20000));
        importVO.setTimeProduction(timeProduction);
        importVO.setDateAllMaterialPrepared(new Date(timeMaterialPrepared.getTime() - 20000));
        importVO.setTimeMaterialPrepared(timeMaterialPrepared);

        StringBuilder sb = new StringBuilder();
        try {
            java.lang.reflect.Method method = helper.getClass().getDeclaredMethod("checkTimeProduction", PsDeliveryFeedbackDownImportVO.class, StringBuilder.class);
            method.setAccessible(true);
            method.invoke(helper, importVO, sb);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        assertTrue(sb.toString().contains("厂商自供料实际齐套日期错误"));
    }
}

/* Ended by AICoder, pid:a6306d2e75sb7a5149c7080d4196dc7583b9baee */
