/*Started by AICoder, pid:i3ad8zaf2f96c3e14c4209c841ddc53b69c874b4*/
package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PushStdModelSnDataRepository;
import com.zte.interfaces.dto.PushStdModelSnDataDTO;
import com.zte.interfaces.dto.PushStdModelSnDataExtDTO;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RequestHeadValidationUtil.class})
public class PushStdModelSnDataServiceImpl_batchSave_0_Test {

    @Mock
    private PushStdModelSnDataRepository pushStdModelSnDataRepository;

    @Mock
    private IdGenerator idGenerator;

    @InjectMocks
    private PushStdModelSnDataServiceImpl pushStdModelSnDataService;

    @Before
    public void setUp() {
        Mockito.reset(pushStdModelSnDataRepository, idGenerator);
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);
        when(RequestHeadValidationUtil.validaFactoryId()).thenReturn("1");
        ReflectionTestUtils.setField(pushStdModelSnDataService, "productSnReportCurrProcess", "30");
        ReflectionTestUtils.setField(pushStdModelSnDataService, "finishedProductStorageCurrProcess", "40");
        ReflectionTestUtils.setField(pushStdModelSnDataService, "currProcessSortedList", Lists.newArrayList("25", "30", "40"));
    }

    @Test
    public void testBatchSave_EmptyList() {
        List<PushStdModelSnDataExtDTO> pushStdModelSnDataList = Collections.emptyList();
        boolean result = pushStdModelSnDataService.batchSave(pushStdModelSnDataList);
        assertFalse(result);
    }

    @Test
    public void testBatchSave_NoValidItems() {
        List<PushStdModelSnDataExtDTO> pushStdModelSnDataList = Arrays.asList(
                new PushStdModelSnDataExtDTO(),
                new PushStdModelSnDataExtDTO()
        );
        // when(idGenerator.snowFlakeIdStr()).thenReturn("123456");

        boolean result = pushStdModelSnDataService.batchSave(pushStdModelSnDataList);
        assertFalse(result);
    }

    @Test
    public void testBatchSave_AllNewItems() {
        PushStdModelSnDataExtDTO pushStdModelSnData1 = new PushStdModelSnDataExtDTO();
        pushStdModelSnData1.setTaskNo("task1");
        pushStdModelSnData1.setSn("sn1");
        pushStdModelSnData1.setCurrProcess("40");
        pushStdModelSnData1.setTaskEntityClass(Constant.FG_DISAS_2);

        PushStdModelSnDataExtDTO pushStdModelSnData2 = new PushStdModelSnDataExtDTO();
        pushStdModelSnData2.setTaskNo("task2");
        pushStdModelSnData2.setSn("sn2");
        pushStdModelSnData2.setTaskEntityClass(Constant.FG_DISAS_2);
        pushStdModelSnData2.setCurrProcess("40");
        List<PushStdModelSnDataExtDTO> pushStdModelSnDataList = Arrays.asList(pushStdModelSnData1, pushStdModelSnData2);

        when(idGenerator.snowFlakeIdStr()).thenReturn("123456");
        when(pushStdModelSnDataRepository.selectExists(anyList(), anyInt())).thenReturn(Collections.emptyList());

        boolean result = pushStdModelSnDataService.batchSave(pushStdModelSnDataList);
        assertTrue(result);

        verify(pushStdModelSnDataRepository, times(1)).batchInsert(anyList());
        verify(pushStdModelSnDataRepository, never()).batchUpdate(anyList());
    }

    @Test
    public void testBatchSave_MixItems0() {
        PushStdModelSnDataExtDTO pushStdModelSnData1 = new PushStdModelSnDataExtDTO();
        pushStdModelSnData1.setTaskNo("task1");
        pushStdModelSnData1.setSn("sn1");
        pushStdModelSnData1.setCurrProcess("40");
        pushStdModelSnData1.setTaskEntityClass(Constant.FG_DISAS_2);

        PushStdModelSnDataExtDTO pushStdModelSnData2 = new PushStdModelSnDataExtDTO();
        pushStdModelSnData2.setTaskNo("task2");
        pushStdModelSnData2.setSn("sn2");
        pushStdModelSnData2.setTaskEntityClass(Constant.FG_DISAS_2);
        pushStdModelSnData2.setCurrProcess("40");
        List<PushStdModelSnDataExtDTO> pushStdModelSnDataList = Arrays.asList(pushStdModelSnData1, pushStdModelSnData2);

        PushStdModelSnDataDTO existingItem3 = new PushStdModelSnDataDTO();
        existingItem3.setTaskNo("task2");
        existingItem3.setSn("sn2");
        existingItem3.setCurrProcess("40");
        existingItem3.setId("existingId3");
        existingItem3.setPushStatus(Constant.PUSH_STATUS.PUSHED_AND_CALLBACK);
        when(idGenerator.snowFlakeIdStr()).thenReturn("123456");
        when(pushStdModelSnDataRepository.selectExists(anyList(), anyInt())).thenReturn(Collections.singletonList(existingItem3));

        boolean result = pushStdModelSnDataService.batchSave(pushStdModelSnDataList);
        assertTrue(result);

        verify(pushStdModelSnDataRepository, times(1)).batchInsert(anyList());
        verify(pushStdModelSnDataRepository, never()).batchUpdate(anyList());
    }

    @Test
    public void testBatchSave_ExistingItemsToUpdate() {
        PushStdModelSnDataExtDTO pushStdModelSnData1 = new PushStdModelSnDataExtDTO();
        pushStdModelSnData1.setTaskNo("task1");
        pushStdModelSnData1.setSn("sn1");
        pushStdModelSnData1.setCurrProcess("30");

        PushStdModelSnDataExtDTO pushStdModelSnData3 = new PushStdModelSnDataExtDTO();
        pushStdModelSnData3.setTaskNo("task3");
        pushStdModelSnData3.setSn("sn3");
        pushStdModelSnData3.setCurrProcess("30");

        PushStdModelSnDataExtDTO pushStdModelSnData4 = new PushStdModelSnDataExtDTO();
        pushStdModelSnData4.setTaskNo("task4");
        pushStdModelSnData4.setSn("sn4");
        pushStdModelSnData4.setCurrProcess("30");

        List<PushStdModelSnDataExtDTO> pushStdModelSnDataList = Arrays.asList(pushStdModelSnData1, pushStdModelSnData3, pushStdModelSnData4);

        PushStdModelSnDataDTO existingItem1 = new PushStdModelSnDataDTO();
        existingItem1.setTaskNo("task1");
        existingItem1.setSn("sn1");
        existingItem1.setCurrProcess("25");
        existingItem1.setId("existingId1");
        existingItem1.setPushStatus(Constant.PUSH_STATUS.PUSHED_AND_CALLBACK);

        PushStdModelSnDataDTO existingItem3 = new PushStdModelSnDataDTO();
        existingItem3.setTaskNo("task3");
        existingItem3.setSn("sn3");
        existingItem3.setCurrProcess("40");
        existingItem3.setId("existingId3");
        existingItem3.setPushStatus(Constant.PUSH_STATUS.PUSHED_AND_CALLBACK);

        PushStdModelSnDataDTO existingItem4 = new PushStdModelSnDataDTO();
        existingItem4.setTaskNo("task4");
        existingItem4.setSn("sn4");
        existingItem4.setCurrProcess("25");
        existingItem4.setId("existingId4");
        existingItem4.setPushStatus(Constant.PUSH_STATUS.NOT_PUSHED);

        when(idGenerator.snowFlakeIdStr()).thenReturn("123456");
        when(pushStdModelSnDataRepository.selectExists(anyList(), anyInt())).thenReturn(Arrays.asList(existingItem1, existingItem3, existingItem4));

        boolean result = pushStdModelSnDataService.batchSave(pushStdModelSnDataList);
        assertTrue(result);

        verify(pushStdModelSnDataRepository, never()).batchInsert(anyList());
        verify(pushStdModelSnDataRepository, times(1)).batchUpdate(anyList());
    }

    @Test
    public void testBatchSave_MixedItems() {
        PushStdModelSnDataExtDTO pushStdModelSnData1 = new PushStdModelSnDataExtDTO();
        pushStdModelSnData1.setTaskNo("task1");
        pushStdModelSnData1.setSn("sn1");
        pushStdModelSnData1.setCurrProcess("25");

        PushStdModelSnDataExtDTO pushStdModelSnData2 = new PushStdModelSnDataExtDTO();
        pushStdModelSnData2.setTaskNo("task2");
        pushStdModelSnData2.setSn("sn2");
        pushStdModelSnData2.setTaskEntityClass(Constant.FG_DISAS_2);
        pushStdModelSnData2.setCurrProcess("40");

        PushStdModelSnDataExtDTO pushStdModelSnData3 = new PushStdModelSnDataExtDTO();
        pushStdModelSnData3.setTaskNo("task3");
        pushStdModelSnData3.setSn("sn3");
        pushStdModelSnData3.setCurrProcess("40");

        PushStdModelSnDataExtDTO pushStdModelSnData4 = new PushStdModelSnDataExtDTO();
        pushStdModelSnData4.setTaskNo("task4");
        pushStdModelSnData4.setSn("sn4");
        pushStdModelSnData4.setCurrProcess("30");

        PushStdModelSnDataExtDTO pushStdModelSnData5 = new PushStdModelSnDataExtDTO();
        pushStdModelSnData5.setTaskNo("task5");
        pushStdModelSnData5.setSn("sn5");
        pushStdModelSnData5.setCurrProcess("40");

        List<PushStdModelSnDataExtDTO> pushStdModelSnDataList = Arrays.asList(pushStdModelSnData1, pushStdModelSnData2,
                pushStdModelSnData3, pushStdModelSnData4, pushStdModelSnData5);


        PushStdModelSnDataDTO existingItem1 = new PushStdModelSnDataDTO();
        existingItem1.setTaskNo("task1");
        existingItem1.setSn("sn1");
        existingItem1.setCurrProcess("25");
        existingItem1.setId("existingId1");
        existingItem1.setPushStatus(Constant.PUSH_STATUS.PUSHED_AND_CALLBACK);

        PushStdModelSnDataDTO existingItem4 = new PushStdModelSnDataDTO();
        existingItem4.setTaskNo("task4");
        existingItem4.setSn("sn4");
        existingItem4.setCurrProcess("30");
        existingItem4.setId("existingId4");
        existingItem4.setPushStatus(Constant.PUSH_STATUS.PUSHED_AND_CALLBACK);

        PushStdModelSnDataDTO existingItem5 = new PushStdModelSnDataDTO();
        existingItem5.setTaskNo("task5");
        existingItem5.setSn("sn5");
        existingItem5.setCurrProcess("40");
        existingItem5.setId("existingId5");
        existingItem5.setPushStatus(Constant.PUSH_STATUS.PUSHED_AND_CALLBACK);

        when(idGenerator.snowFlakeIdStr()).thenReturn("123456");
        when(pushStdModelSnDataRepository.selectExists(anyList(), anyInt())).thenReturn(Arrays.asList(existingItem1, existingItem4, existingItem5));
        boolean result = pushStdModelSnDataService.batchSave(pushStdModelSnDataList);
        assertTrue(result);

        pushStdModelSnData3.setWhiteTaskNoList(new LinkedList<>());
        pushStdModelSnDataService.batchSave(pushStdModelSnDataList);

        pushStdModelSnData3.setWhiteTaskNoList(Collections.singletonList("task3"));
        pushStdModelSnDataService.batchSave(pushStdModelSnDataList);


        PushStdModelSnDataExtDTO existingItem6 = new PushStdModelSnDataExtDTO();
        existingItem6.setTaskNo("task5");
        existingItem6.setSn("sn5");
        existingItem6.setCurrProcess("30");
        existingItem6.setPushStatus(4);
        existingItem6.setId("existingId5");
        existingItem6.setPushNextProcess("30");
        existingItem6.setPushStatus(Constant.PUSH_STATUS.PUSHED_AND_CALLBACK);
        when(pushStdModelSnDataRepository.selectExists(anyList(), anyInt())).thenReturn(Arrays.asList(existingItem1,
                existingItem4, existingItem5,existingItem6));
        pushStdModelSnDataList = Arrays.asList(pushStdModelSnData1, pushStdModelSnData2,
                pushStdModelSnData3, pushStdModelSnData4, pushStdModelSnData5,existingItem6);
        pushStdModelSnDataService.batchSave(pushStdModelSnDataList);

        existingItem6.setPushStatus(3);
        pushStdModelSnDataService.batchSave(pushStdModelSnDataList);


    }

    @Test
    public void whiteBoxTest() throws Exception {
        PushStdModelSnDataExtDTO item = new PushStdModelSnDataExtDTO();
        PushStdModelSnDataDTO exists = new PushStdModelSnDataDTO();
        List<PushStdModelSnDataDTO> pendingUpdateList = new LinkedList<>();
        Whitebox.invokeMethod(pushStdModelSnDataService, "wrapItemForUpdate", item, exists, pendingUpdateList);
        Assert.assertTrue(true);

        item.setPushNextProcess("30");
        exists.setCurrProcess("30");
        Whitebox.invokeMethod(pushStdModelSnDataService, "wrapItemForUpdate", item, exists, pendingUpdateList);

        exists.setPushStatus(3);
        Whitebox.invokeMethod(pushStdModelSnDataService, "wrapItemForUpdate", item, exists, pendingUpdateList);

        item.setPushNextProcess("40");
        exists.setCurrProcess("40");
        Whitebox.invokeMethod(pushStdModelSnDataService, "wrapItemForUpdate", item, exists, pendingUpdateList);

    }
}
/*Ended by AICoder, pid:i3ad8zaf2f96c3e14c4209c841ddc53b69c874b4*/