package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.zte.application.CustomerDataLogService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.InteractiveB2B;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerQualityDTO;
import com.zte.itp.msa.util.encrypt.secure.SecureEncryptorUtils;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertNotNull;

/**
 * ClassName: InteractiveB2BTest
 * Description:
 *
 * <AUTHOR>
 * @date 2023/5/19 下午2:03
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class,SecureEncryptorUtils.class})
public class InteractiveB2BTest extends BaseTestCase {
    @InjectMocks
    private InteractiveB2B interactiveB2B;

    @Mock
    CustomerDataLogService customerDataLogService;
    @Test
    public void init() throws Exception {
        PowerMockito.mockStatic(SecureEncryptorUtils.class);
        assertNotNull(interactiveB2B);
    }
    @Test
    public void getUrlTest() {
        Assert.assertNull(interactiveB2B.getUrl());
    }

    @Test
    public void getHeadParamsTest() throws Exception {
        Assert.assertNotNull(interactiveB2B.getHeadParams());
    }

    @Test
    public void getRequestBodyTest() throws Exception {
        List<String>  resultList = new ArrayList<>();
        resultList.add("test");
        Map<String, List<String>> params = new HashMap<>();
        params.put(Constant.SERVER_SNS, resultList);
        CustomerQualityDTO customerQualityDTO = new CustomerQualityDTO();
        customerQualityDTO.setCustomerName("111");
        customerQualityDTO.setMessageType("11111");
        customerQualityDTO.setJsonData(JSON.toJSONString(params));
        Assert.assertNotNull(interactiveB2B.getRequestBody(customerQualityDTO));
    }

    @Test
    public void handleExceptionTest() throws Exception {
        Throwable e = new Exception();
        Object data = new CustomerDataLogDTO();
        try {
            interactiveB2B.handleException(e, null);
            interactiveB2B.handleException(e, data);
        }
        catch (Exception e1) {
            Assert.assertEquals(null, e.getMessage());
        }
        Throwable e1 = new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.PARAM_IS_NULL);
        try {
            interactiveB2B.handleException(e1, data);
        } catch (Exception e2) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void beforePushDataTest() throws Exception {
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        interactiveB2B.beforePushData(dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void beforePushAllDataTest() throws Exception {
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        List<Object> dataList = new ArrayList<>();
        dataList.add(dto);
        interactiveB2B.beforePushAllData(dataList);
        Assert.assertNotNull(dataList);
    }

    @Test
    public void handleResponseTest() throws Exception {
        try {
            interactiveB2B.handleResponse(null);
        } catch (MesBusinessException e) {
            Assert.assertTrue(com.zte.domain.model.MessageId.CUSTOMIZE_MSG.equals(e.getExMsgId()));
        }

    }
}
