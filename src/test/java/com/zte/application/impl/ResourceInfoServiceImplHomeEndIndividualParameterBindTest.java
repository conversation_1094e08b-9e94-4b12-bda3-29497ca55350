/*Started by AICoder, pid:70447416b432b13142ce0b27718b987d75712da3*/
package com.zte.application.impl;

import com.zte.application.ResourceUseInfoService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.ResourceInfoDetailRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.BarcodeNetSignDTO;
import com.zte.interfaces.dto.ResourceInfoDetailDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.IdGenerator;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisHelper.class})
public class ResourceInfoServiceImplHomeEndIndividualParameterBindTest {

    @InjectMocks private ResourceInfoServiceImpl resourceInfoService;

    @Mock private SysLookupValuesService sysLookupValuesService;

    @Mock private IdGenerator idGenerator;

    @Mock private ResourceUseInfoService resourceUseInfoService;

    @Mock private DatawbRemoteService datawbRemoteService;

    @Mock private ResourceInfoDetailRepository infoDetailRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test(timeout = 8000)
    public void testHomeEndIndividualParameter_NullSysLookupValue() throws ParseException {
        // Given
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(null);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        // When
        try {
            resourceInfoService.homeEndIndividualParameterBind("123");
        } catch (Exception e) {
            fail("Should not have thrown any exception");
        }
        // Then
    }

    @Test(timeout = 8000)
    public void testHomeEndIndividualParameterBind_EmptyLookupMeaning() throws ParseException {
        // Given
        PowerMockito.mockStatic(RedisHelper.class);
        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(sysLookupValues);
        // When
        try {
            resourceInfoService.homeEndIndividualParameterBind("123");
        } catch (Exception e) {
            fail("Should not have thrown any exception");
        }
        // Then
    }

    @Test(timeout = 8000)
    public void testHomeEndIndividualParameterBind_NotHaveData() throws ParseException {
        // Given
        PowerMockito.mockStatic(RedisHelper.class);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("2011-11-11 11:11:11");
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(sysLookupValues);
        // When
        try {
            resourceInfoService.homeEndIndividualParameterBind("123");
        } catch (Exception e) {
            fail("Should not have thrown any exception");
        }
        // Then
    }

    @Test(timeout = 8000)
    public void testHomeEndIndividualParameterBind_HaveData() throws ParseException {
        // Given
        PowerMockito.mockStatic(RedisHelper.class);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("2011-11-11 11:11:11");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(sysLookupValues);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<BarcodeNetSignDTO> barcodeNetSignList = new ArrayList<>();
        barcodeNetSignList.add(new BarcodeNetSignDTO());
        PowerMockito.when(datawbRemoteService.selectBarAccSignForSchTask(Mockito.any())).thenReturn(barcodeNetSignList);
        // When
        try {
            resourceInfoService.homeEndIndividualParameterBind("123");
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
        // Then
        /*Started by AICoder, pid:3b91740093j5d8a14e8d0a7cc13f7d5d68e2bf41*/

        sysLookupValues.setAttribute1("2012-11-11 11:11:11");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(sysLookupValues);
        try {
            resourceInfoService.homeEndIndividualParameterBind("123");
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
        /*Ended by AICoder, pid:3b91740093j5d8a14e8d0a7cc13f7d5d68e2bf41*/
    }

    @Test(timeout = 8000)
    public void handlerDataTest() throws Exception {
        // Given
        PowerMockito.mockStatic(RedisHelper.class);
        List<ResourceInfoDetailDTO> infoDetailList = new ArrayList<>();
        List<BarcodeNetSignDTO> barcodeNetSignList = new ArrayList<>();
        String empNo = "";
        // When
        try {
            resourceInfoService.handlerData(infoDetailList, barcodeNetSignList, empNo);
        } catch (Exception e) {
            fail("Should not have thrown any exception");
        }

        // Given
        infoDetailList.add(new ResourceInfoDetailDTO());
        infoDetailList.add(null);
        // When
        PowerMockito.when(idGenerator.snowFlakeId()).thenReturn(123L);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        resourceInfoService.handlerData(infoDetailList, barcodeNetSignList, empNo);

        // Given
        ResourceInfoDetailDTO infoDetailDTO = new ResourceInfoDetailDTO();
        infoDetailDTO.setResourceNum("test");
        infoDetailDTO.setResourceNo("test2312312");
        infoDetailList.add(infoDetailDTO);
        // When
        resourceInfoService.handlerData(infoDetailList, barcodeNetSignList, empNo);


    }

    /*Started by AICoder, pid:3dd04bfd2fyd00314c26094b819f7e9fb24206fa*/
    @Test
    public void testHomeEndIndividualParameterBind_ValidData() throws ParseException {
        PowerMockito.mockStatic(RedisHelper.class);
        SysLookupValues sysLookupValue = new SysLookupValues();
        sysLookupValue.setLookupMeaning("2021-08-01 12:00:00");
        sysLookupValue.setAttribute1("2021-08-01 12:00:30");
        Mockito.when(sysLookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(sysLookupValue);
        List<BarcodeNetSignDTO> barcodeNetSignList = new ArrayList<>();
        BarcodeNetSignDTO barcodeNetSignDTO = new BarcodeNetSignDTO();
        barcodeNetSignDTO.setNetAccessSignNum("123");
        barcodeNetSignDTO.setLastUpdatedTime(new Date());
        barcodeNetSignList.add(barcodeNetSignDTO);
        Mockito.when(datawbRemoteService.selectBarAccSignForSchTask(Mockito.any())).thenReturn(barcodeNetSignList);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        // When
        try {
            resourceInfoService.homeEndIndividualParameterBind("123");
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    /*Ended by AICoder, pid:3dd04bfd2fyd00314c26094b819f7e9fb24206fa*/


    /*Started by AICoder, pid:6761cv3b5cx46bf1430a089162dc0e061e5551da*/
    @Test(timeout = 8000)
    public void testHomeEndIndividualParameterBind_NullSysLookupValue() throws ParseException {
        when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(null);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(false);
        try {
            resourceInfoService.homeEndIndividualParameterBind("123");
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.OPERATION_IN_PROGRESS_PLEASE_WAIT.equals(e.getExMsgId()));
        }

    }

    /*Ended by AICoder, pid:6761cv3b5cx46bf1430a089162dc0e061e5551da*/
}
/*Ended by AICoder, pid:70447416b432b13142ce0b27718b987d75712da3*/