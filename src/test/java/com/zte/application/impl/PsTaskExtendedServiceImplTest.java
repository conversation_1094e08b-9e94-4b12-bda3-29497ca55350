package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.domain.model.PsTaskExtendedRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.TaskExtendedDTO;
import com.zte.interfaces.dto.TaskNoFromWmesDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertNotNull;

@PrepareForTest({BasicsettingRemoteService.class})
public class PsTaskExtendedServiceImplTest extends BaseTestCase {
    @InjectMocks
    private PsTaskExtendedServiceImpl psTaskExtendedService;
    @Mock
    private PsTaskExtendedRepository psTaskExtendedRepository;
    @Mock
    private SysLookupValuesServiceImpl lookupValuesService;



    @Test
    public void bulkQueriesByTaskNos(){
        List<TaskExtendedDTO> psTaskExtendedDTOS = new ArrayList<>();
        TaskExtendedDTO psTaskExtendedDTO = new TaskExtendedDTO();
        psTaskExtendedDTO.setTaskNo("123");
        psTaskExtendedDTOS.add(psTaskExtendedDTO);
        PowerMockito.when(psTaskExtendedRepository.bulkQueriesByTaskNos(new ArrayList<>())).thenReturn(psTaskExtendedDTOS);
        List<TaskExtendedDTO> stringPsTaskExtendedDTOMap = psTaskExtendedService.bulkQueriesByTaskNos(Arrays.asList("880542130022240124701170600029"));
        PowerMockito.when(psTaskExtendedRepository.bulkQueriesByTaskNos(new ArrayList<>())).thenReturn(new ArrayList<>());
        List<TaskExtendedDTO> stringPsTaskExtendedDTO = psTaskExtendedService.bulkQueriesByTaskNos(Arrays.asList("880542130022240124701170600029"));

        // 断言结果
        assertNotNull(stringPsTaskExtendedDTOMap);
    }

    /* Started by AICoder, pid:i3da1md7d5j0753141cd090ef0ad552af21508cf */
    @Test
    public void filterListByCustomerNo(){
        List<String> taskNoList = new ArrayList<>();
        List<String> customerNoList = new ArrayList<>();
        List<PsTaskExtendedDTO> result = psTaskExtendedService.filterListByCustomerNo(taskNoList, customerNoList);
        Assert.assertTrue(result.isEmpty());

        taskNoList.add("taskNo1");
        result = psTaskExtendedService.filterListByCustomerNo(taskNoList, customerNoList);
        Assert.assertTrue(result.isEmpty());

        customerNoList.add("alibaba");
        result = psTaskExtendedService.filterListByCustomerNo(taskNoList, customerNoList);
        Assert.assertTrue(result.isEmpty());
    }
    /* Ended by AICoder, pid:i3da1md7d5j0753141cd090ef0ad552af21508cf */

    @Test
    public void queryByTaskNos(){
        List<String> taskNoList = new ArrayList<>();
        List<PsTaskExtendedDTO> result = psTaskExtendedService.queryByTaskNos(taskNoList);
        Assert.assertTrue(result.isEmpty());

        taskNoList.add("taskNo1");
        result = psTaskExtendedService.queryByTaskNos(taskNoList);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void getReconfigurationFlag () {
        Assert.assertFalse(psTaskExtendedService.getReconfigurationFlag("123"));
        PowerMockito.when(psTaskExtendedRepository.getReconfigurationOrSpecificFlag(Mockito.any(), Mockito.any())).thenReturn("1");
        Assert.assertTrue(psTaskExtendedService.getReconfigurationFlag("123"));
    }

    @Test
    public void getSpecificTaskExtended () {
        PsTaskExtendedDTO dto = new PsTaskExtendedDTO();
        try {
            psTaskExtendedService.getSpecificTaskExtended(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        List<SysLookupValues> lookupValues = new ArrayList<>();
        SysLookupValues lookupCode = new SysLookupValues();
        lookupValues.add(lookupCode);
        PowerMockito.when(lookupValuesService.findByLookupType(Mockito.any())).thenReturn(lookupValues);
        try {
            psTaskExtendedService.getSpecificTaskExtended(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        lookupCode.setLookupMeaning("123");
        Assert.assertTrue(psTaskExtendedService.getSpecificTaskExtended(dto).isEmpty());
    }

    /* Started by AICoder, pid:p383bc97eande76148ab0aac6077664df4d1ee56 */
    @Test
    public void filterSpecificTaskNo() {
        List<String> taskNoList = new ArrayList<>();
        List<String> result = psTaskExtendedService.filterSpecificTaskNo(taskNoList);
        Assert.assertTrue(result.isEmpty());

        taskNoList.add("taskNo");
        List<SysLookupValues> lookupValues = new ArrayList<>();
        PowerMockito.when(lookupValuesService.findByLookupType(Mockito.anyString())).thenReturn(lookupValues);
        result = psTaskExtendedService.filterSpecificTaskNo(taskNoList);
        Assert.assertTrue(result.isEmpty());

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("111");
        lookupValues.add(sysLookupValues);
        result = psTaskExtendedService.filterSpecificTaskNo(taskNoList);
        Assert.assertTrue(result.isEmpty());
    }
    /* Ended by AICoder, pid:p383bc97eande76148ab0aac6077664df4d1ee56 */

    /* Started by AICoder, pid:6df6ey553c6e22314e0b0baa0029317a53992f65 */
    @Test(expected = MesBusinessException.class)
    public void getTaskNoFromWmes_WithEmptyTaskNoList ()throws Exception {
        TaskNoFromWmesDTO dto = new TaskNoFromWmesDTO();
        dto.setCustomerNo("gdg");
        List<SysLookupTypesDTO> types = new ArrayList<>();
        SysLookupTypesDTO d1 = new SysLookupTypesDTO();
        d1.setLookupMeaning("#");
        types.add(d1);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(types);
        psTaskExtendedService.getTaskNoFromWmes(dto);
    }
    @Test(expected = MesBusinessException.class)
    public void getTaskNoFromWmes_WithEmptyCustomerNo ()throws Exception {
        TaskNoFromWmesDTO dto = new TaskNoFromWmesDTO();
        List<SysLookupTypesDTO> types = new ArrayList<>();
        SysLookupTypesDTO d1 = new SysLookupTypesDTO();
        d1.setLookupMeaning("#");
        types.add(d1);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(types);
        psTaskExtendedService.getTaskNoFromWmes(dto);
    }
    @Test(expected = MesBusinessException.class)
    public void getTaskNoFromWmes_WithEmpty ()throws Exception {
        TaskNoFromWmesDTO dto = new TaskNoFromWmesDTO();
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(Collections.emptyList());
        psTaskExtendedService.getTaskNoFromWmes(dto);
    }
    @Test
    public void getTaskNoFromWmes () throws Exception {
        TaskNoFromWmesDTO dto = new TaskNoFromWmesDTO();
        List<SysLookupTypesDTO> lookUpCodeList= new ArrayList<>();
        SysLookupTypesDTO lookupCode = new SysLookupTypesDTO();
        lookupCode.setLookupMeaning("123");
        lookUpCodeList.add(lookupCode);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(Mockito.anyMap())).thenReturn(lookUpCodeList);

        List<String> taskNoList = new ArrayList<>(Arrays.asList("task1", "task2"));
        dto.setTaskNoList(taskNoList);
        dto.setCustomerNo("bgfg");
        PowerMockito.when(psTaskExtendedRepository.getTaskNoFromWmes(Mockito.anyString(),Mockito.anyList(),Mockito.anyList())).thenReturn(taskNoList);
        psTaskExtendedService.getTaskNoFromWmes(dto);
        Mockito.verify(psTaskExtendedRepository,Mockito.times(1)).getTaskNoFromWmes(Mockito.anyString(),Mockito.anyList(),Mockito.anyList());

    }
    /* Ended by AICoder, pid:6df6ey553c6e22314e0b0baa0029317a53992f65 */

    @Test
    public void testQueryExtendedByTaskNoAndFixBomHeadId() {
        PowerMockito.when(psTaskExtendedRepository.queryExtendedByTaskNoAndFixBomHeadId(Mockito.anyString(),Mockito.anyString())).thenReturn(null);
        PsTaskExtendedDTO psTaskExtendedDTO = psTaskExtendedService.queryExtendedByTaskNoAndFixBomHeadId("", "");
        Assert.assertNull(psTaskExtendedDTO);
    }
}

