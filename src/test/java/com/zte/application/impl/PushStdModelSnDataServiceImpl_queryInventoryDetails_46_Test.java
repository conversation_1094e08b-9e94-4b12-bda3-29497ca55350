/*Started by AICoder, pid:c824dl83822386714e5b08ca325f7874e4f1af87*/
package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.CustomerItemsService;
import com.zte.application.PsTaskExtendedService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.cbom.FixBomCommonService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.FinishedProductStorageCargoTransportDTO;
import com.zte.interfaces.dto.FinishedProductStorageCargoTransportParamDTO;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceDiscoveryInvoker;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ReflectUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class, MESHttpHelper.class, HttpRemoteService.class, JacksonJsonConverUtil.class, CommonUtils.class, MicroServiceDiscoveryInvoker.class,
        HttpClientUtil.class, ConstantInterface.class, HttpRemoteUtil.class, JSON.class, ServiceDataBuilderUtil.class, MicroServiceRestUtil.class})
public class PushStdModelSnDataServiceImpl_queryInventoryDetails_46_Test {

    @Mock
    private FixBomCommonService fixBomCommonService;

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Mock
    private CustomerItemsService customerItemsService;

    @InjectMocks
    private PushStdModelSnDataServiceImpl service;

    @Mock
    PsTaskExtendedService psTaskExtendedService;

    private static final int NUM_ZERO = 0;

    @Test
    public void testQueryInventoryDetails_WithValidTasks() {
        // Given
        List<FinishedProductStorageCargoTransportParamDTO> inputList = Arrays.asList(
            createWipDto("TASK1", "ITEM1", "SN1"),
            createWipDto("TASK1", "ITEM2", "SN2")
        );

        PsTaskExtendedDTO taskDto = new PsTaskExtendedDTO();
        taskDto.setTaskNo("TASK1");
        taskDto.setFixBomId("BOM1");
        taskDto.setFixBomHeadId("BOM1");

        when(psTaskExtendedService.queryByTaskNos(anyList()))
                .thenReturn(Arrays.asList(taskDto));
        when(fixBomCommonService.queryTreeNodeByHeadId(anyString()))
            .thenReturn(Arrays.asList(createFixBomDetail("ZTE1", "Y", "server.configmodel.moc")));

        when(fixBomCommonService.queryFixBomDetailByHeadId(anyString()))
            .thenReturn(Arrays.asList(createFixBomDetail("ZTE1", "Y", "server.configmodel.moc")));

        when(sysLookupValuesService.findByLookupType(Constant.LOOK_UP_CODE_1004115))
            .thenReturn(Arrays.asList(new SysLookupValues(){{setLookupMeaning("ALI_CODE");}}));

        when(customerItemsService.getSameItemOfZteCode(anyList(), anyList()))
                .thenReturn(Arrays.asList(new CustomerItemsDTO(){{setZteCode("ZTE1");setOriginalCustomerCode("ITEM1");setCustomerCode("ALI_CODE");}}));

        // When
        List<FinishedProductStorageCargoTransportDTO> result = service.queryInventoryDetails(inputList);

        // Then
        assertEquals(2, result.size());
        FinishedProductStorageCargoTransportDTO dto = result.get(0);
        assertEquals("SN1", dto.getBarcode());
    }

    @Test
    public void testQueryInventoryDetails_MissingPsTask() {
        // Given
        List<FinishedProductStorageCargoTransportParamDTO> inputList = Arrays.asList(
            createWipDto("TASK1", "ITEM1", "SN1")
        );

        when(fixBomCommonService.queryTreeNodeByFixBomId(anyString())).thenReturn(Collections.emptyList());

        // When
        List<FinishedProductStorageCargoTransportDTO> result = service.queryInventoryDetails(inputList);

        // Then
        assertTrue(result.isEmpty());
    }

    @Test
    public void testQueryInventoryDetails_DealMocBarcode() {
        // Given
        List<FinishedProductStorageCargoTransportParamDTO> inputList = Arrays.asList(
            createWipDto("TASK1", "ITEM1", "MOC_SN")
        );

        FixBomDetailDTO fixBomDetail = createFixBomDetail("ZTE1", "Y", "server.configmodel.moc");
        when(fixBomCommonService.queryTreeNodeByHeadId(anyString())).thenReturn(Arrays.asList(fixBomDetail));
        when(fixBomCommonService.queryFixBomDetailByHeadId(anyString())).thenReturn(Arrays.asList(fixBomDetail));

        when(sysLookupValuesService.findByLookupType(Constant.LOOK_UP_CODE_1004115))
                .thenReturn(Arrays.asList(new SysLookupValues(){{setLookupMeaning("ALI_CODE");}}));
        when(customerItemsService.getSameItemOfZteCode(anyList(), anyList()))
                .thenReturn(Arrays.asList(new CustomerItemsDTO(){{setZteCode("ZTE1");setOriginalCustomerCode("ITEM1");setCustomerCode("ALI_CODE");}}));
        PsTaskExtendedDTO taskDto = new PsTaskExtendedDTO();
        taskDto.setTaskNo("TASK1");
        taskDto.setFixBomId("BOM1");
        taskDto.setFixBomHeadId("BOM1");

        when(psTaskExtendedService.queryByTaskNos(anyList()))
                .thenReturn(Arrays.asList(taskDto));
        // When
        List<FinishedProductStorageCargoTransportDTO> result = service.queryInventoryDetails(inputList);

        // Then
        assertEquals(1, result.size());
        assertEquals("MOC_SN", result.get(0).getBarcode());
    }

    @Test
    public void testQueryInventoryDetails_DealXnBarcode_NoChildNodes() {
        // Given
        List<FinishedProductStorageCargoTransportParamDTO> inputList = Arrays.asList(
            createWipDto("TASK1", "ITEM1", "SN1")
        );
        PsTaskExtendedDTO taskDto = new PsTaskExtendedDTO();
        taskDto.setTaskNo("TASK1");
        taskDto.setFixBomId("BOM1");
        taskDto.setFixBomHeadId("BOM1");

        when(psTaskExtendedService.queryByTaskNos(anyList()))
                .thenReturn(Arrays.asList(taskDto));
        FixBomDetailDTO fixBomDetail = createFixBomDetail("ZTE1", "Y", "server.configmodel.moc");
        when(fixBomCommonService.queryTreeNodeByHeadId(anyString())).thenReturn(Arrays.asList(fixBomDetail));
        when(fixBomCommonService.queryFixBomDetailByHeadId(anyString())).thenReturn(Arrays.asList(fixBomDetail));

        when(sysLookupValuesService.findByLookupType(Constant.LOOK_UP_CODE_1004115))
                .thenReturn(Arrays.asList(new SysLookupValues(){{setLookupMeaning("ALI_CODE");}}));
        when(customerItemsService.getSameItemOfZteCode(anyList(), anyList()))
            .thenReturn(Arrays.asList(new CustomerItemsDTO(){{setZteCode("ZTE1");setOriginalCustomerCode("ITEM1");setCustomerCode("ALI_CODE");}}));
        // When
        List<FinishedProductStorageCargoTransportDTO> result = service.queryInventoryDetails(inputList);

        // Then
        assertEquals(1, result.size());
    }

    private FinishedProductStorageCargoTransportParamDTO createWipDto(String taskNo, String itemNo, String sn) {
        FinishedProductStorageCargoTransportParamDTO finishedProductStorageCargoTransportParamDTO = new FinishedProductStorageCargoTransportParamDTO();
        finishedProductStorageCargoTransportParamDTO.setTaskNo("TASK1");
        finishedProductStorageCargoTransportParamDTO.setFormSn("SN1");
        WipExtendIdentificationDTO dto = new WipExtendIdentificationDTO();
        dto.setTaskNo(taskNo);
        dto.setItemNo(itemNo);
        dto.setSn(sn);
        dto.setFormSn(sn);
        finishedProductStorageCargoTransportParamDTO.setWipExtendIdentificationList(Arrays.asList(dto));

        return finishedProductStorageCargoTransportParamDTO;
    }

    private FixBomDetailDTO createFixBomDetail(String zteCode, String boxBomRequired, String componentType) {
        FixBomDetailDTO dto = new FixBomDetailDTO();
        dto.setZteCode(zteCode);
        dto.setBoxBomRequired(boxBomRequired);
        dto.setCustomerComponentType(componentType);
        dto.setItemSupplierNo("SUPPLIER1");
        return dto;
    }
    /*Ended by AICoder, pid:c824dl83822386714e5b08ca325f7874e4f1af87*/
    @Test
    public void dealXnBarcodeNew()throws Exception {
        FinishedProductStorageCargoTransportParamDTO finishedProductStorageCargoTransportParamDTO = new FinishedProductStorageCargoTransportParamDTO();
        Whitebox.invokeMethod(service,"dealXnBarcode",Arrays.asList(new WipExtendIdentificationDTO(){{setVirtualSn("sn");}}),new ArrayList<>());
        Assert.assertNotNull(finishedProductStorageCargoTransportParamDTO);
    }
    @Test
    public void getCustomerItemsDTOList()throws Exception {
        ReflectUtil.setFieldValue(service,"customerComponentType","customerComponentType");
        FixBomDetailDTO topFixBomDetailDTO = new FixBomDetailDTO();
        Map<String, CustomerItemsDTO> zteCodeMap = new HashMap<>();
        zteCodeMap.put("itemNo",new CustomerItemsDTO(){{setOriginalCustomerCode("ori");}});
        String itemNo = "";
        Map<String, List<CustomerItemsDTO>> originalCustomerCodeMap = new HashMap<>();
        originalCustomerCodeMap.put("ori",Arrays.asList(new CustomerItemsDTO(){{setOriginalCustomerCode("ori");}}));
        Whitebox.invokeMethod(service,"getCustomerItemsDTOList",zteCodeMap,"itemNo",originalCustomerCodeMap);
        Assert.assertNotNull(originalCustomerCodeMap);
    }

    @Test
    public void dealMocBarcode()throws Exception {
        ReflectUtil.setFieldValue(service,"customerComponentType","customerComponentType");
        FinishedProductStorageCargoTransportDTO finishedProductStorageCargoTransportDTO = new FinishedProductStorageCargoTransportDTO();
        List<FinishedProductStorageCargoTransportDTO> childFinishList = Arrays.asList(finishedProductStorageCargoTransportDTO);
        List<WipExtendIdentificationDTO> wipExtendIdentificationDTOS = new ArrayList<>();
        Whitebox.invokeMethod(service,"dealMocBarcode",wipExtendIdentificationDTOS,new ArrayList<>(),finishedProductStorageCargoTransportDTO,true);
        Whitebox.invokeMethod(service,"dealMocBarcode",wipExtendIdentificationDTOS,new ArrayList<>(),finishedProductStorageCargoTransportDTO,false);
        wipExtendIdentificationDTOS.add(new WipExtendIdentificationDTO(){{setSn("sn");setFixBomDetailDTO(new FixBomDetailDTO());}});
        wipExtendIdentificationDTOS.add(new WipExtendIdentificationDTO(){{setSn("sn");setFixBomDetailDTO(new FixBomDetailDTO());}});
        Whitebox.invokeMethod(service,"dealMocBarcode",wipExtendIdentificationDTOS,new ArrayList<>(),finishedProductStorageCargoTransportDTO,false);
        Assert.assertNotNull(wipExtendIdentificationDTOS);
    }

    @Test
    public void setMaterialName()throws Exception {

        FinishedProductStorageCargoTransportDTO finishedProductStorageCargoTransportDTO = new FinishedProductStorageCargoTransportDTO();
        FixBomDetailDTO fixBomDetailDTO = new FixBomDetailDTO();
        Whitebox.invokeMethod(service,"setMaterialName",true,finishedProductStorageCargoTransportDTO,fixBomDetailDTO);
        Whitebox.invokeMethod(service,"setMaterialName",false,finishedProductStorageCargoTransportDTO,fixBomDetailDTO);
        Assert.assertNotNull(finishedProductStorageCargoTransportDTO);
    }

    @Test
    public void getFinishedProductStorageCargoTransportDTO()throws Exception {
        List<WipExtendIdentificationDTO> wipExtendIdentificationDTOS = new ArrayList<>();
        wipExtendIdentificationDTOS.add(new WipExtendIdentificationDTO());
        FixBomDetailDTO topFixBomDetailDTO = new FixBomDetailDTO();
        topFixBomDetailDTO.setItemLevel("0");
        Whitebox.invokeMethod(service,"getFinishedProductStorageCargoTransportDTO",wipExtendIdentificationDTOS,Lists.newArrayList(topFixBomDetailDTO),true, Constant.CLOUD_TYPE_PC);
        Whitebox.invokeMethod(service,"getFinishedProductStorageCargoTransportDTO",wipExtendIdentificationDTOS,Lists.newArrayList(topFixBomDetailDTO),false, Constant.CLOUD_TYPE_HC);
        Assert.assertNotNull(wipExtendIdentificationDTOS);
    }

}
