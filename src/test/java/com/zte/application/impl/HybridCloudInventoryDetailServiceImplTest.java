package com.zte.application.impl;

import com.alibaba.excel.exception.ExcelGenerateException;
import com.zte.application.HrmUserCenterService;
import com.zte.domain.model.HybridcloudInventoryDetailRepository;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Validator;
import java.util.*;

import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyVararg;
import static org.powermock.api.mockito.PowerMockito.mock;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * 混合云库存明细表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-08-11 16:01:46
 */
@RunWith(PowerMockRunner.class)
public class HybridCloudInventoryDetailServiceImplTest extends BaseTestCase {
    @InjectMocks
    HybridCloudInventoryDetailServiceImpl hybridCloudInventoryDetailServiceImpl;
    @Mock
    Validator globalValidator;
    @Mock
    HybridcloudInventoryDetailRepository hybridcloudInventoryDetailRepository;

    @InjectMocks
    private HybridCloudInventoryDetailServiceImpl service;
    @Mock
    private HybridcloudInventoryDetailRepository repository;
    @Mock
    private HrmUserCenterService hrmUserCenterService;

    @Before
    public void setUp() throws Exception {
        Map<String, HrmPersonInfoDTO> map = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("hl");
        hrmPersonInfoDTO.setId("00286523");
        map.put("00286523", hrmPersonInfoDTO);
        when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(map);
    }

    @Test
    public void testQueryByInventoryDatePage() throws Exception {
        ArrayList<HybridCloudInventoryDetailDTO> value = new ArrayList<>();
        HybridCloudInventoryDetailDTO hybridCloudInventoryDetail = new HybridCloudInventoryDetailDTO();
        hybridCloudInventoryDetail.setCreateBy("00286523");
        hybridCloudInventoryDetail.setLastUpdatedBy("00286523");
        value.add(hybridCloudInventoryDetail);
        when(repository.selectPage(any())).thenReturn(value);
        PageRows<HybridCloudInventoryDetailDTO> result = service.queryPage(new HybridCloudInventoryDetailPageQueryDTO());
        Assert.assertNotNull(result);
    }


    @Test
    public void testCountExportTotal() {
        when(repository.selectCount(any())).thenReturn(1);
        Integer result = service.countExportTotal(new HybridCloudInventoryDetailPageQueryDTO());
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryByInventoryDataExportDate() {
        when(repository.selectPage(any())).thenReturn(new ArrayList<>());
        List<HybridCloudInventoryDetailDTO> result = service.queryExportData(new HybridCloudInventoryDetailPageQueryDTO(), 1, 10);
        Assert.assertNotNull(result);
    }

    @Test
    public void testQueryByInventoryDate() throws Exception {
        when(hybridcloudInventoryDetailRepository.selectByInventoryDate(any(HybridCloudInventoryDetailPageQueryDTO.class))).thenReturn(Collections.singletonList(new HybridCloudInventoryDetailDTO()));
        HybridCloudInventoryDetailPageQueryDTO query = new HybridCloudInventoryDetailPageQueryDTO();
        Assert.assertThrows(MesBusinessException.class, () -> hybridCloudInventoryDetailServiceImpl.queryByInventoryDate(query));
        query.setInventoryDate(new Date());
        List<HybridCloudInventoryDetailDTO> result = hybridCloudInventoryDetailServiceImpl.queryByInventoryDate(query);
        Assert.assertNotNull(result);

    }

    @Test
    public void testImportData() throws Exception {
        when(hybridcloudInventoryDetailRepository.batchInsert(any())).thenReturn(Long.valueOf(1));
        when(globalValidator.validate(any(), anyVararg())).thenReturn(new HashSet<>());
        HybridCloudInventoryDetailImportDTO detailImportDTO = new HybridCloudInventoryDetailImportDTO();
        detailImportDTO.setInventoryDate(new Date());
        ArrayList<HybridCloudInventoryDetailExcelDTO> detail = new ArrayList<>();
        detail.add(new HybridCloudInventoryDetailExcelDTO());
        detailImportDTO.setDetail(detail);
        Long result = hybridCloudInventoryDetailServiceImpl.importData(detailImportDTO);
        Assert.assertNotNull(result);
    }

    @Test
    public void testDownloadTemplate() throws Exception {
        HttpServletRequest request = mock(HttpServletRequest.class);
        HttpServletResponse response = mock(HttpServletResponse.class);
        assertThrows(ExcelGenerateException.class,()->hybridCloudInventoryDetailServiceImpl.downloadTemplate(request, response));
    }
}

