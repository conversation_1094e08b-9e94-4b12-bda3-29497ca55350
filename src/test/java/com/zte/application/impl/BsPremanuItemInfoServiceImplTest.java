/*Started by AICoder, pid:8712a34df2ybc62144830a82d107f8308e632a28*/
package com.zte.application.impl;

import com.zte.domain.model.BProdBomHeaderRepository;
import com.zte.domain.model.BsBomHierarchicalHeadRepository;
import com.zte.domain.model.BsPremanuItemInfo;
import com.zte.domain.model.BsPremanuItemInfoRepository;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.when;

public class BsPremanuItemInfoServiceImplTest extends BaseTestCase {

    @Mock
    private BsPremanuItemInfoRepository bsPremanuItemInfoRepository;

    @Mock
    private BProdBomHeaderRepository bProdBomHeaderRepository;
    @Mock
    private BsBomHierarchicalHeadRepository bsBomHierarchicalHeadRepository;
    @InjectMocks
    private BsPremanuItemInfoServiceImpl service;

    @Before
    public void setUp() {
        // Mockito will inject the mocks into the service before each test
    }

    @Test
    public void testQueryDipBomInfoByProdPlanIdBatch_EmptyList() {
        List<String> prodPlanIdList = Collections.emptyList();

        List<BsPremanuItemInfo> result = service.queryDipBomInfoByProdPlanIdBatch(prodPlanIdList);

        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryDipBomInfoByProdPlanIdBatch_NullList() {
        List<String> prodPlanIdList = null;

        List<BsPremanuItemInfo> result = service.queryDipBomInfoByProdPlanIdBatch(prodPlanIdList);

        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryDipBomInfoByProdPlanIdBatch_ValidList() {
        List<String> prodPlanIdList = Arrays.asList("plan1", "plan2");
        List<String> bomCodeList = Arrays.asList("bom1", "bom2");
        List<BsPremanuItemInfo> expectedBomInfoList = Arrays.asList(new BsPremanuItemInfo(), new BsPremanuItemInfo());

        when(bProdBomHeaderRepository.queryOriginalCodeBatch(prodPlanIdList)).thenReturn(bomCodeList);
        when(bsPremanuItemInfoRepository.getDipBomInfo(bomCodeList)).thenReturn(expectedBomInfoList);

        List<BsPremanuItemInfo> result = service.queryDipBomInfoByProdPlanIdBatch(prodPlanIdList);

        assertEquals(expectedBomInfoList, result);
    }

    @Test
    public void testGetAssembleBomInfo() throws Exception {
        Assert.assertNotNull(service.getAssembleBomInfo(new BsPremanuItemInfo()));
        BsPremanuItemInfo dto = new BsPremanuItemInfo();
        dto.setBomCode("123");
        service.getAssembleBomInfo(dto);
    }
}
/*Ended by AICoder, pid:8712a34df2ybc62144830a82d107f8308e632a28*/