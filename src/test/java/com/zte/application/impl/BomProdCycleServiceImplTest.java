package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.BomProdCycle;
import com.zte.domain.model.BomProdCycleRepository;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.domain.model.sncabind.PsTaskRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.datawb.BomProdCycleMonthDTO;
import com.zte.interfaces.dto.datawb.BomProdCycleSearchDTO;
import com.zte.interfaces.dto.datawb.OpProdplanDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;

import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/2/21 21:43
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({DatawbRemoteService.class})
public class BomProdCycleServiceImplTest {
    @Mock
    BomProdCycleRepository bomProdCycleRepository;
    @Mock
    PsTaskRepository psTaskRepository;
    @InjectMocks
    BomProdCycleServiceImpl bomProdCycleServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testInsertBomProdCycle() throws Exception {
        bomProdCycleServiceImpl.insertBomProdCycle(new BomProdCycle());
        BomProdCycle record = new BomProdCycle();
        Assert.assertNotNull(record);
    }

    @Test
    public void testInsertBomProdCycleSelective() throws Exception {
        bomProdCycleServiceImpl.insertBomProdCycleSelective(new BomProdCycle());
        BomProdCycle record = new BomProdCycle();
        Assert.assertNotNull(record);
    }

    @Test
    public void testDeleteBomProdCycleById() throws Exception {
        bomProdCycleServiceImpl.deleteBomProdCycleById(new BomProdCycle());
        BomProdCycle record = new BomProdCycle();
        Assert.assertNotNull(record);
    }

    @Test
    public void testUpdateBomProdCycleByIdSelective() throws Exception {
        bomProdCycleServiceImpl.updateBomProdCycleByIdSelective(new BomProdCycle());
        BomProdCycle record = new BomProdCycle();
        Assert.assertNotNull(record);
    }

    @Test
    public void testUpdateBomProdCycleById() throws Exception {
        bomProdCycleServiceImpl.updateBomProdCycleById(new BomProdCycle());
        BomProdCycle record = new BomProdCycle();
        Assert.assertNotNull(record);
    }


    @Test
    public void testSelectBomProdCycleById() throws Exception {
        when(bomProdCycleRepository.selectBomProdCycleById(any())).thenReturn(new BomProdCycle());

       Assert.assertNotNull(bomProdCycleServiceImpl.selectBomProdCycleById(new BomProdCycle()));
//        Assert.assertEquals(new BomProdCycle(), result);
    }

    @Test
    public void testInsertByDate() throws Exception {
        PowerMockito.mockStatic(DatawbRemoteService.class);
        when(bomProdCycleRepository.selectByPlanNo(anyString())).thenReturn(new BomProdCycle());
        PsTask psTask = new PsTask(){{setGetDate(new Date());setFirstWarehouseDate(new Date());setProdplanNo("aaaaaa");
        setEntpNo(MpConstant.BM);}};
        when(psTaskRepository.selectListByPartFirstWarehouseDate(anyString())).thenReturn(Arrays.<PsTask>asList(psTask));
        when(psTaskRepository.selectOneByProdplanNo(anyString())).thenReturn(psTask);
        PowerMockito.when(DatawbRemoteService.getProdplanDtoByNo(Mockito.anyString())).thenReturn(new OpProdplanDTO(){{
            setPreReleaseApplyDate(new Date());setPlanOrgId(BigDecimal.ONE);setDemandEndDate(new Date());
            setRealProddate(new Date());}});
        bomProdCycleServiceImpl.insertByDate("2023-02-02");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void testInsertByDates() throws Exception {
        when(bomProdCycleRepository.selectByPlanNo(anyString())).thenReturn(new BomProdCycle());
        when(psTaskRepository.selectListByPartFirstWarehouseDate(anyString())).thenReturn(Arrays.<PsTask>asList(new PsTask()));
        when(psTaskRepository.selectOneByProdplanNo(anyString())).thenReturn(new PsTask());

        bomProdCycleServiceImpl.insertByDates(new GregorianCalendar(2023, Calendar.FEBRUARY, 21, 21, 43).getTime(), new GregorianCalendar(2023, Calendar.FEBRUARY, 21, 21, 43).getTime());
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void testBatchUpdate() throws Exception {
        bomProdCycleServiceImpl.batchUpdate(Arrays.<BomProdCycle>asList(new BomProdCycle()));
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void testBatchInsert() throws Exception {
        bomProdCycleServiceImpl.batchInsert(Arrays.<BomProdCycle>asList(new BomProdCycle()));
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void testListOrganizationUnitName() throws Exception {
        when(bomProdCycleRepository.listOrganizationUnitName()).thenReturn(Arrays.<BomProdCycle>asList(new BomProdCycle()));

        Assert.assertNotNull(bomProdCycleServiceImpl.listOrganizationUnitName());
//        Assert.assertEquals(Arrays.<BomProdCycle>asList(new BomProdCycle()), result);
    }

    @Test
    public void testGetProductClasses() throws Exception {
        when(bomProdCycleRepository.getProductClasses()).thenReturn(Arrays.<BomProdCycle>asList(new BomProdCycle()));

        Assert.assertNotNull(bomProdCycleServiceImpl.getProductClasses());
//        Assert.assertEquals(Arrays.<BomProdCycle>asList(new BomProdCycle()), result);
    }

    @Test
    public void testListEntpName() throws Exception {
        when(bomProdCycleRepository.listEntpName()).thenReturn(Arrays.<BomProdCycle>asList(new BomProdCycle()));

        Assert.assertNotNull(bomProdCycleServiceImpl.listEntpName());
//        Assert.assertEquals(Arrays.<BomProdCycle>asList(new BomProdCycle()), result);
    }

    @Test
    public void testGetPlanOrgIds() throws Exception {
        when(bomProdCycleRepository.getPlanOrgIds()).thenReturn(Arrays.<BomProdCycle>asList(new BomProdCycle()));

        Assert.assertNotNull(bomProdCycleServiceImpl.getPlanOrgIds());
//        Assert.assertEquals(Arrays.<BomProdCycle>asList(new BomProdCycle()), result);
    }

    @Test
    public void testCheckDTO() throws Exception {
        BomProdCycleSearchDTO searchDTO = new BomProdCycleSearchDTO();
        try{
            bomProdCycleServiceImpl.checkDTO(searchDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DATATYPE_ERROR, e.getMessage());
        }
    }

    @Test
    public void testSearchResult() throws Exception {
        when(bomProdCycleRepository.selectByDto(any())).thenReturn(Arrays.<BomProdCycle>asList(new BomProdCycle()));
        when(bomProdCycleRepository.countByDto(any())).thenReturn(0L);
        when(bomProdCycleRepository.selectToPage(any())).thenReturn(Arrays.<BomProdCycle>asList(new BomProdCycle()));
        when(bomProdCycleRepository.selectCycleAvg(any())).thenReturn(Arrays.<BomProdCycleMonthDTO>asList(new BomProdCycleMonthDTO()));
        when(bomProdCycleRepository.select21Count(any())).thenReturn(Arrays.<BomProdCycleMonthDTO>asList(new BomProdCycleMonthDTO()));
        when(bomProdCycleRepository.selectLastDate()).thenReturn(new GregorianCalendar(2023, Calendar.FEBRUARY, 21, 21, 43).getTime());

        Assert.assertNotNull(bomProdCycleServiceImpl.searchResult(new BomProdCycleSearchDTO()));
//        Assert.assertEquals(null, result);
    }

    @Test
    public void searchResultTest() throws Exception {
        BomProdCycleSearchDTO dto = new BomProdCycleSearchDTO();
        dto.setResultType(2);
        dto.setEntpName("na23");
        dto.setOrganizationName("dasdas");
        dto.setUnitName("dasdasd");
        Assert.assertNotNull(bomProdCycleServiceImpl.searchResult(dto));
    }

    @Test
    public void generateParamsTest() throws Exception {
        BigDecimal[] average = new BigDecimal[1];
        BigDecimal[] averageTwo = new BigDecimal[1];
        BigDecimal[] countAverage = new BigDecimal[1];
        BigDecimal[] countAverageTwo = new BigDecimal[1];
        BigDecimal allCounts = new BigDecimal("0");
        Assert.assertNotNull(bomProdCycleServiceImpl.generateParams(average,averageTwo,countAverage,countAverageTwo,allCounts));
    }
    @Test
    public void setArraysTest() throws Exception {
        BigDecimal[] average = new BigDecimal[1];
        BigDecimal[] averageTwo = new BigDecimal[1];
        BigDecimal[] countAverage = new BigDecimal[1];
        BigDecimal[] countAverageTwo = new BigDecimal[1];
        BigDecimal allCounts = new BigDecimal("0");
        Map<String, BigDecimal[]> params = bomProdCycleServiceImpl.generateParams(average,averageTwo,countAverage,countAverageTwo,allCounts);
        boolean isThisYear = false;
        BigDecimal d21Counts = new BigDecimal("1");
        BomProdCycleMonthDTO mDto = new BomProdCycleMonthDTO();
        BomProdCycleMonthDTO d21Dto = new BomProdCycleMonthDTO();
        try {
            bomProdCycleServiceImpl.setArrays(isThisYear, params,d21Counts,mDto,d21Dto);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    /* Started by AICoder, pid:w14ffd6ce8tdcee146e70bdab0d0311e1a699e2f */
    @Test
    public void convertDTOTest() throws Exception {
        BomProdCycleSearchDTO searchDTO = new BomProdCycleSearchDTO();
        Whitebox.invokeMethod(bomProdCycleServiceImpl, "convertDTO", searchDTO);
        List<String> organizationName = searchDTO.getOrganizationNameList();
        List<String> entpName = searchDTO.getEntpNameList();
        List<String> unitName = searchDTO.getUnitNameList();
        Assert.assertTrue(organizationName == null);
        Assert.assertTrue(entpName == null);
        Assert.assertTrue(unitName == null);
        searchDTO.setOrganizationName("'1','2'");
        searchDTO.setUnitName("'1','2'");
        searchDTO.setEntpName("'1','2'");
        Whitebox.invokeMethod(bomProdCycleServiceImpl, "convertDTO", searchDTO);
        organizationName = searchDTO.getOrganizationNameList();
        entpName = searchDTO.getEntpNameList();
        unitName = searchDTO.getUnitNameList();
        Assert.assertTrue(organizationName.size() == 2);
        Assert.assertTrue(entpName.size() == 2);
        Assert.assertTrue(unitName.size() == 2);
    }
    /* Ended by AICoder, pid:w14ffd6ce8tdcee146e70bdab0d0311e1a699e2f */
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme