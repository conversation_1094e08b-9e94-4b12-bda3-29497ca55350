/*Started by AICoder, pid:365f4qff305b17514dd80ada30e41578d3a83a9b*/
package com.zte.application.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.google.common.collect.Lists;
import com.zte.application.PushStdModelSnDataHandleService;
import com.zte.interfaces.dto.PushStdModelSnDataExtDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
public class PushStdModelSnDataHandleServiceCompositeImpl_getPushMessageData_2_Test {

    @Mock
    private PushStdModelSnDataHandleService<Object> pushStdModelSnDataHandleService;

    @InjectMocks
    private PushStdModelSnDataHandleServiceCompositeImpl pushStdModelSnDataHandleServiceComposite;

    @Before
    public void setUp() {
        // Initialize any necessary setup here
        ReflectionTestUtils.setField(pushStdModelSnDataHandleServiceComposite, "pushStdModelSnDataHandleServices", Lists.newArrayList(pushStdModelSnDataHandleService));
    }

    @Test
    public void testGetPushMessageData_MatchFound() throws JsonProcessingException {
        String currProcess = "testProcess";
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        List<WipExtendIdentificationDTO> wipExtendIdentifications = new ArrayList<>();

        when(pushStdModelSnDataHandleService.match(anyString())).thenReturn(true);
        when(pushStdModelSnDataHandleService.getPushMessageData(anyString(), any(PushStdModelSnDataExtDTO.class), anyList()))
                .thenReturn("mockedData");


        Object result = pushStdModelSnDataHandleServiceComposite.getPushMessageData(currProcess, pushStdModelSnDataExt, wipExtendIdentifications);

        assertNotNull(result);
        assertEquals("mockedData", result);
        verify(pushStdModelSnDataHandleService).match(currProcess);
        verify(pushStdModelSnDataHandleService).getPushMessageData(currProcess, pushStdModelSnDataExt, wipExtendIdentifications);
    }

    @Test
    public void testGetPushMessageData_NoMatchFound() throws JsonProcessingException {
        String currProcess = "testProcess";
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        List<WipExtendIdentificationDTO> wipExtendIdentifications = new ArrayList<>();

        PushStdModelSnDataHandleService<?> mockService = mock(PushStdModelSnDataHandleService.class);
        when(mockService.match(anyString())).thenReturn(false);

        Object result = pushStdModelSnDataHandleServiceComposite.getPushMessageData(currProcess, pushStdModelSnDataExt, wipExtendIdentifications);

        assertNull(result);
        verify(pushStdModelSnDataHandleService).match(currProcess);
        verify(pushStdModelSnDataHandleService, never()).getPushMessageData(anyString(), any(PushStdModelSnDataExtDTO.class), anyList());
    }
}
/*Ended by AICoder, pid:365f4qff305b17514dd80ada30e41578d3a83a9b*/