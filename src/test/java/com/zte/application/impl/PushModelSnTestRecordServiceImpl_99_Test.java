/*Started by AICoder, pid:3f7de3effaha30d14cf30802b1b2ef4f63c8f8a7*/
package com.zte.application.impl;

import com.zte.application.SysLookupValuesService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PushModelSnTestRecord;
import com.zte.domain.model.PushModelSnTestRecordRepository;
import com.zte.domain.model.PushStdModelSnDataRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.B2bCallBackNewDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.reflect.Whitebox;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

@RunWith(MockitoJUnitRunner.class)
public class PushModelSnTestRecordServiceImpl_99_Test {

    @Mock
    private PushModelSnTestRecordRepository pushModelSnTestRecordRepository;

    @Mock
    private Logger logger;

    @InjectMocks
    private PushModelSnTestRecordServiceImpl pushModelSnTestRecordService;

    @Mock
    private PushStdModelSnDataRepository pushStdModelSnDataRepository;

    @Mock
    SysLookupValuesService sysLookupValuesService;

        private PushModelSnTestRecord createRecord(String stationName, String uploadStatus, String stationUploadStatus, String fileUploadStatus, String result) {
        PushModelSnTestRecord record = new PushModelSnTestRecord();
        record.setStationName(stationName);
        record.setUploadStatus(uploadStatus);
        record.setStationUploadStatus(stationUploadStatus);
        record.setFileUploadStatus(fileUploadStatus);
        record.setResult(result);
        return record;
    }

    @Test
    public void testProcessOSSFileUpload_BlankId() {
        B2bCallBackNewDTO dto = new B2bCallBackNewDTO();
        dto.setKeywords("");

        pushModelSnTestRecordService.processOSSFileUpload(dto);

        verify(pushModelSnTestRecordRepository, never()).updateSelectiveById(any());
        verify(pushModelSnTestRecordRepository, never()).selectById(anyString());
    }

    @Test
    public void testProcessOSSFileUpload_Success() {
        B2bCallBackNewDTO dto = new B2bCallBackNewDTO();
        dto.setKeywords("123");
        dto.setSuccess(true);
        dto.setMsg("Success message");

        pushModelSnTestRecordService.processOSSFileUpload(dto);

        verify(pushModelSnTestRecordRepository, times(1)).updateSelectiveById(any());
        verify(pushModelSnTestRecordRepository, times(1)).selectById("123");
    }

    @Test
    public void testProcessOSSFileUpload_Failure() {
        B2bCallBackNewDTO dto = new B2bCallBackNewDTO();
        dto.setKeywords("123");
        dto.setSuccess(false);
        dto.setMsg("Failure message");

        pushModelSnTestRecordService.processOSSFileUpload(dto);

        verify(pushModelSnTestRecordRepository, times(1)).updateSelectiveById(any());
        verify(pushModelSnTestRecordRepository, times(1)).selectById("123");
    }

    /*Started by AICoder, pid:x8d41d0eedn685a1485b0816b1a44b59c2a57e32*/
    @Test
    public void testProcessingStationAnalysisResults_Failure() {
        B2bCallBackNewDTO dto = new B2bCallBackNewDTO();
        dto.setKeywords("123");
        dto.setSuccess(false);
        dto.setMsg("Failure message");

        PushModelSnTestRecord record = new PushModelSnTestRecord();
        record.setRequestId("123");
        record.setSn("SN123");
        record.setWorkorderId("WO123");

        Mockito.when(pushModelSnTestRecordRepository.selectById("123")).thenReturn(record);
        Mockito.when(pushModelSnTestRecordRepository.selectBySn("SN123")).thenReturn(null
        );

        try{
            pushModelSnTestRecordService.processingStationAnalysisResults(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        sysLookupValuesList.add(new SysLookupValues(){{setLookupMeaning("name");setAttribute1("imes_name");}});
        PowerMockito.when(sysLookupValuesService.findByLookupType(eq(Constant.LOOKUP_VALUE_6746))).thenReturn(sysLookupValuesList);
        pushModelSnTestRecordService.processingStationAnalysisResults(dto);

        Mockito.when(pushModelSnTestRecordRepository.selectBySn("SN123")).thenReturn(
                java.util.Arrays.asList(
                        new PushModelSnTestRecord(){{setStationName("name");}},
                        new PushModelSnTestRecord(){{setStationName("imes_name");}}
                )
        );
        pushModelSnTestRecordService.processingStationAnalysisResults(dto);
        Assert.assertNotNull(sysLookupValuesList);
    }

    @Test
    public void testProcessingStationAnalysisResults_BlankId() {
        B2bCallBackNewDTO dto = new B2bCallBackNewDTO();
        dto.setKeywords("");

        pushModelSnTestRecordService.processingStationAnalysisResults(dto);

        Mockito.verify(pushModelSnTestRecordRepository, Mockito.never()).updateSelectiveById(any());
        Mockito.verify(pushModelSnTestRecordRepository, Mockito.never()).selectById(any());
    }

    @Test
    public void testProcessingStationAnalysisResults_Success() {
        B2bCallBackNewDTO dto = new B2bCallBackNewDTO();
        dto.setKeywords("123");
        dto.setSuccess(true);
        dto.setMsg("Success message");

        PushModelSnTestRecord record = new PushModelSnTestRecord();
        record.setRequestId("123");
        record.setSn("SN123");
        record.setWorkorderId("WO123");

        Mockito.when(pushModelSnTestRecordRepository.selectById("123")).thenReturn(record);
        Mockito.when(pushModelSnTestRecordRepository.selectBySn("SN123")).thenReturn(null);

        try{
            pushModelSnTestRecordService.processingStationAnalysisResults(dto);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        sysLookupValuesList.add(new SysLookupValues(){{setLookupMeaning("name");setAttribute1("imes_name");}});
        PowerMockito.when(sysLookupValuesService.findByLookupType(eq(Constant.LOOKUP_VALUE_6746))).thenReturn(sysLookupValuesList);
        pushModelSnTestRecordService.processingStationAnalysisResults(dto);
        Mockito.when(pushModelSnTestRecordRepository.selectBySn("SN123")).thenReturn(
                java.util.Arrays.asList(
                        new PushModelSnTestRecord(){{setStationName("name");}},
                        new PushModelSnTestRecord(){{setStationName("imes_name");}}
                )
        );
        pushModelSnTestRecordService.processingStationAnalysisResults(dto);
        Assert.assertNotNull(sysLookupValuesList);
    }

    @Before
    public void setUp() {
        // Setup code if needed
            // Setup code if needed
            // Setup code if needed
    }
    /*Ended by AICoder, pid:x8d41d0eedn685a1485b0816b1a44b59c2a57e32*/


    /*Started by AICoder, pid:449e71462fh6daf145db082c5116e729f2184349*/
    @Test
    public void testProcessingCompleteMachineTestData_WithNonEmptyKeywords_Success() {
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setKeywords("testKeywords");
        b2bCallBackNewDTO.setSuccess(true);
        b2bCallBackNewDTO.setMsg("testMessage");

        pushModelSnTestRecordService.processingCompleteMachineTestData(b2bCallBackNewDTO);

        verify(pushModelSnTestRecordRepository, times(1)).updateSelectiveById(any());
    }

    @Test
    public void testProcessingCompleteMachineTestData_WithEmptyKeywords() {
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setKeywords("");

        pushModelSnTestRecordService.processingCompleteMachineTestData(b2bCallBackNewDTO);

        verify(pushModelSnTestRecordRepository, never()).updateSelectiveById(any());
    }

    @Test
    public void testProcessingCompleteMachineTestData_WithNonEmptyKeywords_Failure() {
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setKeywords("testKeywords");
        b2bCallBackNewDTO.setSuccess(false);
        b2bCallBackNewDTO.setMsg("testMessage");

        pushModelSnTestRecordService.processingCompleteMachineTestData(b2bCallBackNewDTO);

        verify(pushModelSnTestRecordRepository, times(1)).updateSelectiveById(any());
    }
    /*Ended by AICoder, pid:449e71462fh6daf145db082c5116e729f2184349*/
    /*Ended by AICoder, pid:3f7de3effaha30d14cf30802b1b2ef4f63c8f8a7*/

    @Test
    public void getMsg()throws Exception {
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setKeywords("testKeywords");
        b2bCallBackNewDTO.setSuccess(false);
        b2bCallBackNewDTO.setMsg("testMesstestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessagetestMessageage");

        Whitebox.invokeMethod(pushModelSnTestRecordService,"getMsg",b2bCallBackNewDTO);

        Assert.assertNotNull(b2bCallBackNewDTO);
    }

    @Test
    public void checkIfBarcodeUploadIsComplete()throws Exception {
        Set<String> set = new HashSet<>();
        Map<String, List<PushModelSnTestRecord>> pushStdMap = new HashMap<>();
        Whitebox.invokeMethod(pushModelSnTestRecordService,"checkIfBarcodeUploadIsComplete",set,pushStdMap);

        Assert.assertNotNull(set);
        set.add("name");
        set.add("name1");
        set.add("name2");
        List<PushModelSnTestRecord> pushModelSnTestRecordList = new ArrayList<>();
        pushModelSnTestRecordList.add(new PushModelSnTestRecord(){{setUploadStatus("0");setFileUploadStatus("0");setStationUploadStatus("0");}});
        pushStdMap.put("name",pushModelSnTestRecordList);

        Whitebox.invokeMethod(pushModelSnTestRecordService,"checkIfBarcodeUploadIsComplete",set,pushStdMap);

        Assert.assertNotNull(set);
        pushModelSnTestRecordList.add(new PushModelSnTestRecord(){{setUploadStatus("1");setFileUploadStatus("0");setStationUploadStatus("0");}});
        pushStdMap.put("name",pushModelSnTestRecordList);

        Whitebox.invokeMethod(pushModelSnTestRecordService,"checkIfBarcodeUploadIsComplete",set,pushStdMap);

        Assert.assertNotNull(set);
        pushModelSnTestRecordList.add(new PushModelSnTestRecord(){{setUploadStatus("1");setFileUploadStatus("1");setStationUploadStatus("0");}});
        pushStdMap.put("name",pushModelSnTestRecordList);

        Whitebox.invokeMethod(pushModelSnTestRecordService,"checkIfBarcodeUploadIsComplete",set,pushStdMap);

        Assert.assertNotNull(set);

        pushModelSnTestRecordList.add(new PushModelSnTestRecord(){{setUploadStatus("1");setFileUploadStatus("1");setStationUploadStatus("1");}});
        pushStdMap.put("name",pushModelSnTestRecordList);

        Whitebox.invokeMethod(pushModelSnTestRecordService,"checkIfBarcodeUploadIsComplete",set,pushStdMap);

        Assert.assertNotNull(set);
        pushModelSnTestRecordList.add(new PushModelSnTestRecord(){{setUploadStatus("1");setFileUploadStatus("1");setStationUploadStatus("0");setResult(Constant.ALi.FAIL);}});
        pushStdMap.put("name",pushModelSnTestRecordList);

        Whitebox.invokeMethod(pushModelSnTestRecordService,"checkIfBarcodeUploadIsComplete",set,pushStdMap);

        Assert.assertNotNull(set);

        pushModelSnTestRecordList.add(new PushModelSnTestRecord(){{setUploadStatus("1");setFileUploadStatus("1");setStationUploadStatus("0");setResult(Constant.ALi.PASS);}});

        pushStdMap.put("name",pushModelSnTestRecordList);

        Whitebox.invokeMethod(pushModelSnTestRecordService,"checkIfBarcodeUploadIsComplete",set,pushStdMap);

        Assert.assertNotNull(set);
        pushModelSnTestRecordList.add(new PushModelSnTestRecord(){{setUploadStatus("1");setFileUploadStatus("1");setStationUploadStatus("1");}});
        pushStdMap.put("name",pushModelSnTestRecordList);
        List<PushModelSnTestRecord> pushModelSnTestRecordList2 = new ArrayList<>();
        pushModelSnTestRecordList2.add(new PushModelSnTestRecord(){{setUploadStatus("0");setFileUploadStatus("0");setStationUploadStatus("0");}});
        pushModelSnTestRecordList2.add(new PushModelSnTestRecord(){{setUploadStatus("1");setFileUploadStatus("1");setStationUploadStatus("1");}});
        pushStdMap.put("name1",pushModelSnTestRecordList2);
        Whitebox.invokeMethod(pushModelSnTestRecordService,"checkIfBarcodeUploadIsComplete",set,pushStdMap);

        Assert.assertNotNull(set);
    }

}
/*Ended by AICoder, pid:3f7de3effaha30d14cf30802b1b2ef4f63c8f8a7*/