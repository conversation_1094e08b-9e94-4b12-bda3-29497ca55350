/*Started by AICoder, pid:edd28t2aedpf67c147a60be6d0a5699a4ec77edc*/
package com.zte.application.impl;
import com.zte.domain.model.BProdBomChangeDetailRepository;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.domain.model.sncabind.PsTaskRepository;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BProdBomChangeDetailServiceImplTest4 {

    @InjectMocks
    private BProdBomChangeDetailServiceImpl service;

    @Mock
    private BProdBomChangeDetailRepository bProdBomChangeDetailRepository;

    @Mock
    private PsTaskRepository psTaskRepository;

    @Before
    public void setUp() {
        // Setup code if needed
    }

    @Test
    public void testGetmbomChangeByProdplanId_EmptyProdplanId() {
        List<BProdBomChangeDetailDTO> result = service.getmbomChangeByProdplanId("");
        assertTrue(result.isEmpty());
    }

    @Test(expected = MesBusinessException.class)
    public void testGetmbomChangeByProdplanId_NoPsTasks() {
        when(psTaskRepository.selectProdplanNoByProdplanId(Collections.singletonList("validId")))
            .thenReturn(Collections.emptyList());

        service.getmbomChangeByProdplanId("validId");
    }

    @Test
    public void testGetmbomChangeByProdplanId_WithPsTasksAndDetails() {
        PsTask task = new PsTask();
        task.setProdplanId("validId");
        task.setProdplanNo("planNo");

        when(psTaskRepository.selectProdplanNoByProdplanId(Collections.singletonList("validId")))
            .thenReturn(Collections.singletonList(task));

        BProdBomChangeDetailDTO detail = new BProdBomChangeDetailDTO();
        detail.setProdplanId("validId");

        when(bProdBomChangeDetailRepository.queryBProdBomDetailChangeByProdplan(any(BProdBomChangeDetailDTO.class)))
            .thenReturn(Collections.singletonList(detail));

        List<BProdBomChangeDetailDTO> result = service.getmbomChangeByProdplanId("validId");

        assertEquals(1, result.size());
        assertEquals("planNo", result.get(0).getProdplanNo());
    }
}
/*Ended by AICoder, pid:edd28t2aedpf67c147a60be6d0a5699a4ec77edc*/