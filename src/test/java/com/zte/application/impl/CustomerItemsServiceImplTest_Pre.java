package com.zte.application.impl;
/* Started by AICoder, pid:2cdb8af4593648e08481f775236498e0 */

import com.zte.application.HrmUserCenterService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.CustomerItemsParamsRepository;
import com.zte.domain.model.CustomerItemsRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.bytedance.CpuInfoDTO;
import com.zte.interfaces.dto.bytedance.GpuInfoDTO;
import com.zte.interfaces.dto.bytedance.HardDiskInfoDTO;
import com.zte.interfaces.dto.bytedance.MemoryInfoDTO;
import com.zte.interfaces.dto.bytedance.MotherboardInfoDTO;
import com.zte.interfaces.dto.bytedance.NetworkCardInfoDTO;
import com.zte.interfaces.dto.bytedance.RaidCardInfoDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.BigExcelProcesser;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@PrepareForTest({HttpClientUtil.class, RedisHelper.class})
public class CustomerItemsServiceImplTest_Pre extends BaseTestCase {
    @InjectMocks
    private CustomerItemsServiceImpl service;
    @Mock
    private CustomerItemsRepository repository;
    @Mock
    private SysLookupValuesRepository lookupValuesRepository;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private CloudDiskHelper cloudDiskHelper;
    @Mock
    private IdGenerator idGenerator;
    @Mock
    private CustomerItemsParamsRepository customerItemsParamsRepository;
    @Mock
    private HrmUserCenterService hrmUserCenterService;

    @Before
    public void init() {
        PowerMockito.mockStatic(RedisHelper.class);
    }

    /*Started by AICoder, pid:b6f70mb081u056414c2f0bfa517afe0505c5bef6*/
    @Test(timeout = 8000)
    public void testQueryListByCustomerListNullDto() {
        List<CustomerItemsDTO> expected = new ArrayList<>();
        when(repository.queryListByCustomerList(null)).thenReturn(expected);

        List<CustomerItemsDTO> result = service.queryListByCustomerList(null);
        verify(repository, times(1)).queryListByCustomerList(null);
        assert result.equals(expected);
    }

    @Test(timeout = 8000)
    public void testQueryListByCustomerListEmptyDto() {
        CustomerItemsDTO dto = new CustomerItemsDTO();
        List<CustomerItemsDTO> expected = new ArrayList<>();
        when(repository.queryListByCustomerList(dto)).thenReturn(expected);

        List<CustomerItemsDTO> result = service.queryListByCustomerList(dto);
        verify(repository, times(1)).queryListByCustomerList(dto);
        assert result.equals(expected);
    }
    /*Ended by AICoder, pid:b6f70mb081u056414c2f0bfa517afe0505c5bef6*/


    @Test
    public void newCustomerItems() {
        try {
            service.newCustomerItems(null);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.CUSTOMER_ITEMS_PARAMS_NULL.equals(e.getMessage()));
        }

        CustomerItemsDTO dto = new CustomerItemsDTO();
        dto.setProjectType("0");
        try {
            service.newCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.CUSTOMER_PARAMS_NULL_ONE_TWO_ZERO.equals(e.getMessage()));
        }

        dto.setProjType("0");
        try {
            service.newCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.CUSTOMER_PARAMS_NULL_OTHER.equals(e.getMessage()));
        }

        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        dto.setZteCode("0");
        try {
            service.newCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.CUSTOMER_ITEMS_ADD_OR_UPDATE.equals(e.getMessage()));
        }

        Map<String, String> map = new HashMap<>();
        map.put("123", "234");
        dto.setParamsDetail(map);
        try {
            service.newCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.SYS_LOOK_NOT_CONFIG.equals(e.getMessage()));
        }

        List<SysLookupValues> sysLookupValues = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        sysLookupValues.add(a1);
        SysLookupValues a2 = new SysLookupValues();
        a2.setAttribute1(Constant.CustomerParamsTable.CPU_INFO);
        a2.setLookupMeaning("1");
        sysLookupValues.add(a2);
        SysLookupValues a3 = new SysLookupValues();
        a3.setAttribute1(Constant.CustomerParamsTable.GPU_INFO);
        a3.setLookupMeaning("2");
        sysLookupValues.add(a3);
        SysLookupValues a4 = new SysLookupValues();
        a4.setAttribute1(Constant.CustomerParamsTable.MEMORY_INFO);
        a4.setLookupMeaning("3");
        sysLookupValues.add(a4);
        SysLookupValues a5 = new SysLookupValues();
        a5.setAttribute1(Constant.CustomerParamsTable.NETWORK_CARD_INFO);
        a5.setLookupMeaning("4");
        sysLookupValues.add(a5);
        SysLookupValues a6 = new SysLookupValues();
        a6.setAttribute1(Constant.CustomerParamsTable.HARD_DISK_INFO);
        a6.setLookupMeaning("5");
        sysLookupValues.add(a6);
        SysLookupValues a7 = new SysLookupValues();
        a7.setAttribute1(Constant.CustomerParamsTable.RAID_CARD_INFO);
        a7.setLookupMeaning("6");
        sysLookupValues.add(a7);
        SysLookupValues a8 = new SysLookupValues();
        a8.setAttribute1(Constant.CustomerParamsTable.MOTHERBOARD_INFO);
        a8.setLookupMeaning("7");
        sysLookupValues.add(a8);
        SysLookupValues a9 = new SysLookupValues();
        a9.setAttribute1("ddd");
        a9.setLookupMeaning("ddd");
        sysLookupValues.add(a9);
        PowerMockito.when(lookupValuesRepository.selectValuesByType(Mockito.any()))
                .thenReturn(sysLookupValues);
        try {
            service.newCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.SYS_LOOK_NOT_CONFIG.equals(e.getMessage()));
        }

        dto.setCustomerComponentType("1");
        service.newCustomerItems(dto);

        dto.setCustomerComponentType("2");
        service.newCustomerItems(dto);

        dto.setCustomerComponentType("3");
        service.newCustomerItems(dto);

        dto.setCustomerComponentType("4");
        service.newCustomerItems(dto);

        dto.setCustomerComponentType("5");
        service.newCustomerItems(dto);

        dto.setCustomerComponentType("6");
        service.newCustomerItems(dto);

        dto.setCustomerComponentType("7");
        service.newCustomerItems(dto);

        dto.setCustomerComponentType("ddd");
        try {
            service.newCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.CUSTOMER_PARAM_TABLE_ERROR.equals(e.getMessage()));
        }
    }

    @Test
    public void checkProjType() {
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setProjectType("0");
        try {
            service.checkProjType(customerItemsDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_PARAMS_NULL_ONE_TWO_ZERO, e.getMessage());
        }
        customerItemsDTO.setProjectType("1");
        try {
            service.checkProjType(customerItemsDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_PARAMS_NULL_ONE_TWO_ZERO, e.getMessage());
        }
        customerItemsDTO.setProjectType("2");
        try {
            service.checkProjType(customerItemsDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_PARAMS_NULL_ONE_TWO_ZERO, e.getMessage());
        }
        customerItemsDTO.setProjectType("3");
        try {
            service.checkProjType(customerItemsDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_PARAMS_NULL_ONE_TWO_ZERO, e.getMessage());
        }
    }

    @Test
    public void getCustomerItemsInfoSelfDevelopedByItemNoList() {
        List<String> list = new ArrayList<>();
        List<CustomerItemsDTO> tempList = new ArrayList<>();
        tempList.add(new CustomerItemsDTO());
        CustomerItemsDTO customerItemsDTO = new CustomerItemsDTO();
        customerItemsDTO.setItemNoList(list);
        service.getCustomerItemsInfoSelfDevelopedByItemNoList(customerItemsDTO);
        list.add("1");
        service.getCustomerItemsInfoSelfDevelopedByItemNoList(customerItemsDTO);
        PowerMockito.when(repository.getCustomerItemsInfoSelfDevelopedByItemNoList(any())).thenReturn(tempList);
        service.getCustomerItemsInfoSelfDevelopedByItemNoList(customerItemsDTO);
        PowerMockito.when(repository.getCustomerItemsInfoSelfDevelopedByItemNoList(any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(service.getCustomerItemsInfoSelfDevelopedByItemNoList(customerItemsDTO));
    }

    @Test
    public void updateCustomerItems() {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        try {
            service.updateCustomerItems(null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_ITEMS_PARAMS_NULL, e.getMessage());
        }
        CustomerItemsDTO dto = new CustomerItemsDTO();
        dto.setId("123");
        try {
            service.updateCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_TYPE_NULL, e.getMessage());
        }
        dto.setProjectType("4");
        List<SysLookupValues> values = new ArrayList<>();
        PowerMockito.when(lookupValuesRepository.getList(any())).thenReturn(values);
        try {
            service.updateCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_PARAMS_NULL_FOUR, e.getMessage());
        }
        dto.setCustomerName("customer1");

        try {
            service.updateCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_PARAMS_NULL_FOUR, e.getMessage());
        }
        dto.setProjectName("project1");
        try {
            service.updateCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_PARAMS_NULL_FOUR, e.getMessage());
        }
        dto.setZteCode("zte1");
        dto.setSourceZteCode("zteChange1");
        dto.setZteSupplier("oooo");
        dto.setZteBrandStyle("6666");
        try {
            service.updateCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_ITEMS_ADD_OR_UPDATE, e.getMessage());
        }
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        PowerMockito.when(repository.checkItemsExist(any())).thenReturn(1);
        try {
            service.updateCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_ITEMS_EXIST, e.getMessage());
        }
        try {
            service.newCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_ITEMS_EXIST, e.getMessage());
        }
        PowerMockito.when(repository.checkItemsExist(any())).thenReturn(0);
        dto.setId(null);
        service.updateCustomerItems(dto);
        service.newCustomerItems(dto);
        dto.setProjectType("3");
        try {
            service.updateCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ZJ_CUSTOMER_MODEL_NULL, e.getMessage());
        }

        dto.setId("123");
        dto.setProjectType("4");
        try {
            service.updateCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DATA_DELETE_REFRESH_PAGE, e.getMessage());
        }

        List<SysLookupValues> sysLookupValues = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        sysLookupValues.add(a1);
        SysLookupValues a2 = new SysLookupValues();
        a2.setAttribute1(Constant.CustomerParamsTable.CPU_INFO);
        a2.setLookupMeaning("1");
        sysLookupValues.add(a2);
        SysLookupValues a3 = new SysLookupValues();
        a3.setAttribute1(Constant.CustomerParamsTable.GPU_INFO);
        a3.setLookupMeaning("2");
        sysLookupValues.add(a3);
        SysLookupValues a4 = new SysLookupValues();
        a4.setAttribute1(Constant.CustomerParamsTable.MEMORY_INFO);
        a4.setLookupMeaning("3");
        sysLookupValues.add(a4);
        SysLookupValues a5 = new SysLookupValues();
        a5.setAttribute1(Constant.CustomerParamsTable.NETWORK_CARD_INFO);
        a5.setLookupMeaning("4");
        sysLookupValues.add(a5);
        SysLookupValues a6 = new SysLookupValues();
        a6.setAttribute1(Constant.CustomerParamsTable.HARD_DISK_INFO);
        a6.setLookupMeaning("5");
        sysLookupValues.add(a6);
        SysLookupValues a7 = new SysLookupValues();
        a7.setAttribute1(Constant.CustomerParamsTable.RAID_CARD_INFO);
        a7.setLookupMeaning("6");
        sysLookupValues.add(a7);
        SysLookupValues a8 = new SysLookupValues();
        a8.setAttribute1(Constant.CustomerParamsTable.MOTHERBOARD_INFO);
        a8.setLookupMeaning("7");
        sysLookupValues.add(a8);
        SysLookupValues a9 = new SysLookupValues();
        a9.setAttribute1("ddd");
        a9.setLookupMeaning("ddd");
        sysLookupValues.add(a9);
        PowerMockito.when(lookupValuesRepository.selectValuesByType(Mockito.any()))
                .thenReturn(sysLookupValues);
        PowerMockito.when(repository.queryCustomerItemsById(Mockito.any()))
                .thenReturn(dto);
        service.updateCustomerItems(dto);

        CustomerItemsDTO b11 = new CustomerItemsDTO();
        dto.setAdditionalInfoId("31");
        PowerMockito.when(repository.queryCustomerItemsById(Mockito.any()))
                .thenReturn(b11);
        service.updateCustomerItems(dto);

        b11.setCustomerComponentType("1");
        service.updateCustomerItems(dto);

        b11.setAdditionalInfoId("4444");
        service.updateCustomerItems(dto);

        b11.setCustomerComponentType("2");
        service.updateCustomerItems(dto);

        b11.setCustomerComponentType("3");
        service.updateCustomerItems(dto);

        b11.setCustomerComponentType("4");
        service.updateCustomerItems(dto);

        b11.setCustomerComponentType("5");
        service.updateCustomerItems(dto);

        b11.setCustomerComponentType("6");
        service.updateCustomerItems(dto);

        b11.setCustomerComponentType("7");
        service.updateCustomerItems(dto);

        b11.setCustomerComponentType("ddd");
        try {
            service.updateCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.CUSTOMER_PARAM_TABLE_ERROR.equals(e.getMessage()));
        }

        b11.setCustomerComponentType("1");
        Map<String, String> detailMap = new HashMap<>();
        detailMap.put("123", "dd");
        dto.setParamsDetail(detailMap);
        service.updateCustomerItems(dto);

        dto.setAdditionalInfoId(null);
        dto.setCustomerComponentType("1");
        service.updateCustomerItems(dto);

        dto.setAdditionalInfoId("111");
        b11.setCustomerComponentType("1");
        dto.setCustomerComponentType("1");
        service.updateCustomerItems(dto);

        b11.setCustomerComponentType("2");
        dto.setCustomerComponentType("2");
        service.updateCustomerItems(dto);

        b11.setCustomerComponentType("3");
        dto.setCustomerComponentType("3");
        service.updateCustomerItems(dto);

        b11.setCustomerComponentType("4");
        dto.setCustomerComponentType("4");
        service.updateCustomerItems(dto);

        b11.setCustomerComponentType("5");
        dto.setCustomerComponentType("5");
        service.updateCustomerItems(dto);

        b11.setCustomerComponentType("6");
        dto.setCustomerComponentType("6");
        service.updateCustomerItems(dto);

        b11.setCustomerComponentType("7");
        dto.setCustomerComponentType("7");
        service.updateCustomerItems(dto);

        b11.setCustomerComponentType("ddd");
        dto.setCustomerComponentType("ddd");
        try {
            service.updateCustomerItems(dto);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.CUSTOMER_PARAM_TABLE_ERROR.equals(e.getMessage()));
        }
    }

    @Test
    public void pageCustomerItemsInfo() throws Exception {
        CustomerItemsDTO dto = new CustomerItemsDTO();
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_QRY_PARAMS_NULL, e.getMessage());
        }
        dto.setProjectName("project");
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_QRY_PARAMS_NULL, e.getMessage());
        }
        dto.setStartCreateDate(new Date());
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_QRY_PARAMS_NULL, e.getMessage());
        }
        dto.setStartUpdateDate(new Date());
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_QRY_PARAMS_NULL, e.getMessage());
        }
        dto.setStartCreateDate(null);
        dto.setStartUpdateDate(null);
        dto.setEndCreateDate(new Date());
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_QRY_PARAMS_NULL, e.getMessage());
        }
        dto.setEndUpdateDate(new Date());
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_QRY_PARAMS_NULL, e.getMessage());
        }
        DateFormat format = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS);
        dto.setStartCreateDate(format.parse("2020-01-01 00:00:00"));
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.QRY_TIME_CAN_NOT_GREATER_ONE_YEAR, e.getMessage());
        }
        dto.setStartCreateDate(null);
        dto.setStartUpdateDate(format.parse("2020-01-01 00:00:00"));
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.QRY_TIME_CAN_NOT_GREATER_ONE_YEAR, e.getMessage());
        }
        dto.setStartCreateDate(new Date(dto.getEndCreateDate().getTime() - 10));
        dto.setStartUpdateDate(new Date(dto.getEndUpdateDate().getTime() - 10));
        List<CustomerItemsDTO> list = new ArrayList<>();
        dto.setStatus("Y");
        list.add(dto);
        PowerMockito.when(repository.pageCustomerItemsInfo(any())).thenReturn(list);
        service.pageCustomerItemsInfo(dto);
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        try {
            service.exportCustomerItems(new CustomerItemsDTO(), null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_ITEMS_ADD_OR_UPDATE, e.getMessage());
        }
        dto.setStatus("N");
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);

        try {
            service.exportCustomerItems(dto, null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_DATA_TO_EXPORT, e.getMessage());
        }
        Map<String, String> typeMap = new HashMap<>();
        Whitebox.invokeMethod(service, "exportItemsInfo", dto, typeMap, new BigExcelProcesser(), new HashMap<>(), new HashMap<>());

        List<CustomerItemsDTO> dtoList = new LinkedList<>();
        for (int i = 0; i < 8; i++) {
            CustomerItemsDTO b1 = new CustomerItemsDTO();
            b1.setAdditionalInfoId(String.valueOf(i));
            b1.setCustomerComponentType(String.valueOf(i));
            if (i % 2 == 0) {
                b1.setCreateBy("123");
                b1.setLastUpdatedBy("123" + String.valueOf(i));
            }
            dtoList.add(b1);
        }
        PowerMockito.when(repository.pageCustomerItemsInfo(Mockito.any()))
                .thenReturn(dtoList);
        try {
            service.pageCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.SYS_LOOK_NOT_CONFIG.equals(e.getMessage()));
        }

        List<SysLookupValues> sysLookupValues = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        sysLookupValues.add(a1);
        SysLookupValues a2 = new SysLookupValues();
        a2.setAttribute1(Constant.CustomerParamsTable.CPU_INFO);
        a2.setLookupMeaning("1");
        sysLookupValues.add(a2);
        SysLookupValues a3 = new SysLookupValues();
        a3.setAttribute1(Constant.CustomerParamsTable.GPU_INFO);
        a3.setLookupMeaning("2");
        sysLookupValues.add(a3);
        SysLookupValues a4 = new SysLookupValues();
        a4.setAttribute1(Constant.CustomerParamsTable.MEMORY_INFO);
        a4.setLookupMeaning("3");
        sysLookupValues.add(a4);
        SysLookupValues a5 = new SysLookupValues();
        a5.setAttribute1(Constant.CustomerParamsTable.NETWORK_CARD_INFO);
        a5.setLookupMeaning("4");
        sysLookupValues.add(a5);
        SysLookupValues a6 = new SysLookupValues();
        a6.setAttribute1(Constant.CustomerParamsTable.HARD_DISK_INFO);
        a6.setLookupMeaning("5");
        sysLookupValues.add(a6);
        SysLookupValues a7 = new SysLookupValues();
        a7.setAttribute1(Constant.CustomerParamsTable.RAID_CARD_INFO);
        a7.setLookupMeaning("6");
        sysLookupValues.add(a7);
        SysLookupValues a8 = new SysLookupValues();
        a8.setAttribute1(Constant.CustomerParamsTable.MOTHERBOARD_INFO);
        a8.setLookupMeaning("7");
        sysLookupValues.add(a8);
        SysLookupValues a9 = new SysLookupValues();
        a9.setAttribute1("ddd");
        a9.setLookupMeaning("ddd");
        sysLookupValues.add(a9);
        PowerMockito.when(lookupValuesRepository.selectValuesByType(Mockito.any()))
                .thenReturn(sysLookupValues);
        Map<String, HrmPersonInfoDTO> userMap = new HashMap<>();
        userMap.put("123", new HrmPersonInfoDTO());
        userMap.put("1230", new HrmPersonInfoDTO());
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.any())).thenReturn(userMap);

        service.pageCustomerItemsInfo(dto);

        List<CpuInfoDTO> listLid = new LinkedList<>();
        List<GpuInfoDTO> gpuList = new LinkedList<>();
        List<NetworkCardInfoDTO> netList = new LinkedList<>();
        List<MemoryInfoDTO> memoryInfoDTOList = new LinkedList<>();
        List<HardDiskInfoDTO> hardList1 = new LinkedList<>();
        List<RaidCardInfoDTO> raidList = new LinkedList<>();
        List<MotherboardInfoDTO> motherboardInfoDTOList = new LinkedList<>();
        for (int i = 0; i < 8; i++) {
            String id = String.valueOf(i);
            CpuInfoDTO b1 = new CpuInfoDTO();
            b1.setId(id);
            listLid.add(b1);
            GpuInfoDTO b2 = new GpuInfoDTO();
            b2.setId(id);
            gpuList.add(b2);
            NetworkCardInfoDTO b3 = new NetworkCardInfoDTO();
            b3.setId(id);
            netList.add(b3);
            MemoryInfoDTO me = new MemoryInfoDTO();
            me.setId(id);
            memoryInfoDTOList.add(me);
            HardDiskInfoDTO ha1 = new HardDiskInfoDTO();
            ha1.setId(id);
            hardList1.add(ha1);
            RaidCardInfoDTO ra1 = new RaidCardInfoDTO();
            ra1.setId(id);
            raidList.add(ra1);
            MotherboardInfoDTO m1 = new MotherboardInfoDTO();
            m1.setId(id);
            motherboardInfoDTOList.add(m1);
        }
        PowerMockito.when(customerItemsParamsRepository.queryBatchCpuInfoByIds(Mockito.any()))
                .thenReturn(listLid);
        PowerMockito.when(customerItemsParamsRepository.queryBatchGpuInfoByIds(Mockito.any()))
                .thenReturn(gpuList);
        PowerMockito.when(customerItemsParamsRepository.queryBatchHardDiskInfoByIds(Mockito.any()))
                .thenReturn(hardList1);
        PowerMockito.when(customerItemsParamsRepository.queryBatchMemoryInfoByIds(Mockito.any()))
                .thenReturn(memoryInfoDTOList);
        PowerMockito.when(customerItemsParamsRepository.queryBatchNetworkCardInfoByIds(Mockito.any()))
                .thenReturn(netList);
        PowerMockito.when(customerItemsParamsRepository.queryBatchRaidCardInfoByIds(Mockito.any()))
                .thenReturn(raidList);
        PowerMockito.when(customerItemsParamsRepository.queryBatchMotherboardInfoByIds(Mockito.any()))
                .thenReturn(motherboardInfoDTOList);
        service.pageCustomerItemsInfo(dto);
    }

    /* Started by AICoder, pid:p2df7y4c6br3f2414edb09a8a0299f6127759dbb */
    @Test
    public void getCustomerItemsInfo() {
        CustomerItemsDTO dto = new CustomerItemsDTO();
        dto.setCustomerSubName("ziGongSi");
        try {
            service.getCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ITEM_NO_LIST_NULL, e.getMessage());
        }
        List<String> itemNoList = new ArrayList<>();
        itemNoList.add("item1");
        dto.setItemNoList(itemNoList);
        try {
            service.getCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        Map<String, Object> map = new HashMap<>();
        map.put("lookupType",new BigDecimal(7300));
        List<SysLookupValues> values = new ArrayList<>();
        SysLookupValues value = new SysLookupValues();
        value.setLookupMeaning("gongSi");
        value.setAttribute1("ziGongSi1");
        values.add(value);
        PowerMockito.when(lookupValuesRepository.getList(any())).thenReturn(values);
        try {
            service.getCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SUB_CUSTOMER_CONFIG_LOST, e.getMessage());
        }
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("ziGongSi1","gongSi");
        value.setAttribute1("ziGongSi");
        service.getCustomerItemsInfo(dto);
        for (int i = 0; i < 101; i++) {
            itemNoList.add("item" + i);
        }
        try {
            service.getCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MAX_ITEM_NO_IS_ONCE, e.getMessage());
        }

        List<CustomerItemsDTO> resultList = new LinkedList<>();
        CustomerItemsDTO cus1 = new CustomerItemsDTO();
        resultList.add(cus1);
        PowerMockito.when(repository.getSubCustomerItemsInfo(Mockito.any()))
                .thenReturn(resultList);
        service.getCustomerItemsInfo(dto);
    }
    /* Ended by AICoder, pid:p2df7y4c6br3f2414edb09a8a0299f6127759dbb */

    @Test
    public void getCustomerItemsInfo1() {
        CustomerItemsDTO dto = new CustomerItemsDTO();
        dto.setCustomerSubName(null);
        try {
            service.getCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ITEM_NO_LIST_NULL, e.getMessage());
        }
        List<String> itemNoList = new ArrayList<>();
        itemNoList.add("item1");
        dto.setItemNoList(itemNoList);
        try {
            service.getCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        Map<String, Object> map = new HashMap<>();
        map.put("lookupType",new BigDecimal(7300));
        List<SysLookupValues> values = new ArrayList<>();
        SysLookupValues value = new SysLookupValues();
        value.setLookupMeaning("gongSi");
        value.setAttribute1("ziGongSi1");
        values.add(value);
        PowerMockito.when(lookupValuesRepository.getList(any())).thenReturn(values);
        try {
            service.getCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SUB_CUSTOMER_NOT_EXIST, e.getMessage());
        }
        Map<String, String> typeMap = new HashMap<>();
        typeMap.put("ziGongSi1","gongSi");
        value.setAttribute1("ziGongSi");
        service.getCustomerItemsInfo(dto);
        for (int i = 0; i < 101; i++) {
            itemNoList.add("item" + i);
        }
        try {
            service.getCustomerItemsInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MAX_ITEM_NO_IS_ONCE, e.getMessage());
        }
    }
    @Test
    public void deleteCustomerItems() {
        try {
            service.deleteCustomerItems("", "10313234");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_ID_CANNOT_DELETE, e.getMessage());
        }
        service.deleteCustomerItems("6666", "10313234");
    }

    @Test
    public void queryItemBrandName() {
        service.queryItemBrandName("");
        PowerMockito.when(lookupValuesRepository.selectSysLookupValuesById(any())).thenReturn(null);
        try {
            service.queryItemBrandName("123");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupValues lookupValues = new SysLookupValues();
        lookupValues.setLookupCode(new BigDecimal(123));
        PowerMockito.when(lookupValuesRepository.selectSysLookupValuesById(any())).thenReturn(lookupValues);
        try {
            service.queryItemBrandName("123");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), Mockito.anyMap())).thenReturn("{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":{\"pageNum\":1,\"pageSize\":100,\"total\":1,\"pages\":1,\"list\":[{\"itemNo\":\"008020100125\",\"supplierName\":\"Intel Semiconductor (US) LLC\",\"supplierNo\":\"12201601\",\"brandStyle\":\"CM8064402018800 SR22S (938908)\",\"pageNo\":1,\"pageSize\":100}]},\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.CustomerItemsController@queryItemBrandName\",\"code\":\"0000\",\"costTime\":\"907ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Tue May 09 11:07:56 CST 2023\",\"tag\":\"按ZTE代码查供应商\",\"serviceName\":\"zte-mes-manufactureshare-centerfactory\",\"userId\":\"10313234\"}}");
        lookupValues.setLookupMeaning("http://imes.test.zte.com.cn");
        service.queryItemBrandName("6666");
    }
}
/* Ended by AICoder, pid:2cdb8af4593648e08481f775236498e0 */
