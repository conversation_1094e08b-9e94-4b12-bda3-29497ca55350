/*Started by AICoder, pid:b470c2aa0cyb4ef147190986221bbc0eab64e011*/
package com.zte.application.impl;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.zte.domain.model.PushStdModelDataRepository;
import com.zte.interfaces.dto.PushStdModelDataExtDTO;
import com.zte.interfaces.dto.PushStdModelDataQueryDTO;
import com.zte.itp.msa.core.model.PageRows;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class PushStdModelDataServiceImpl_queryPushStdModelDataExt_2_Test {

    @Mock
    private PushStdModelDataRepository pushStdModelDataRepository;

    @InjectMocks
    private PushStdModelDataServiceImpl pushStdModelDataService;

    @Before
    public void setUp() {
        // Setup code if needed
    }

    @Test
    public void testQueryPushStdModelDataExt_WithValidQuery() {
        // Given
        PushStdModelDataQueryDTO query = new PushStdModelDataQueryDTO();
        query.setTaskNoFrom("task1");
        query.setLastUpdatedDateStart(new Date());
        query.setPageNum(2);
        query.setPageSize(10);

        Page<PushStdModelDataExtDTO> mockPage = Mockito.mock(Page.class);
        when(mockPage.getPageNum()).thenReturn(2);
        when(mockPage.getTotal()).thenReturn(100L);
        when(mockPage.getResult()).thenReturn(Lists.newArrayList(new PushStdModelDataExtDTO()));

        when(pushStdModelDataRepository.selectExtByQuery(any(PushStdModelDataQueryDTO.class))).thenReturn(mockPage);

        // When
        PageRows<PushStdModelDataExtDTO> result = pushStdModelDataService.queryPushStdModelDataExt(query);

        // Then
        assertEquals(2, result.getCurrent());
        assertEquals(100, result.getTotal());
        assertEquals(1, result.getRows().size());

        verify(pushStdModelDataRepository).selectExtByQuery(any(PushStdModelDataQueryDTO.class));
    }

    @Test
    public void testQueryPushStdModelDataExt_WithExceedPageNumPageSizeQuery() {
        // Given
        PushStdModelDataQueryDTO query = new PushStdModelDataQueryDTO();
        query.setTaskNoFrom("task1");
        query.setLastUpdatedDateStart(new Date());
        query.setPageNum(0);
        query.setPageSize(600);

        Page<PushStdModelDataExtDTO> mockPage = Mockito.mock(Page.class);
        when(mockPage.getPageNum()).thenReturn(1);
        when(mockPage.getTotal()).thenReturn(100L);
        when(mockPage.getResult()).thenReturn(Lists.newArrayList(new PushStdModelDataExtDTO()));

        when(pushStdModelDataRepository.selectExtByQuery(any(PushStdModelDataQueryDTO.class))).thenReturn(mockPage);

        // When
        PageRows<PushStdModelDataExtDTO> result = pushStdModelDataService.queryPushStdModelDataExt(query);

        // Then
        assertEquals(1, result.getCurrent());
        assertEquals(100, result.getTotal());
        assertEquals(1, result.getRows().size());

        verify(pushStdModelDataRepository).selectExtByQuery(any(PushStdModelDataQueryDTO.class));
    }

    @Test
    public void testQueryPushStdModelDataExt_WithNullTaskNoFrom() {
        // Given
        PushStdModelDataQueryDTO query = new PushStdModelDataQueryDTO();
        query.setLastUpdatedDateStart(new Date());
        query.setPageNum(2);
        query.setPageSize(10);

        Page<PushStdModelDataExtDTO> mockPage = Mockito.mock(Page.class);
        when(mockPage.getPageNum()).thenReturn(2);
        when(mockPage.getTotal()).thenReturn(100L);
        when(mockPage.getResult()).thenReturn(Lists.newArrayList(new PushStdModelDataExtDTO()));

        when(pushStdModelDataRepository.selectExtByQuery(any(PushStdModelDataQueryDTO.class))).thenReturn(mockPage);

        // When
        PageRows<PushStdModelDataExtDTO> result = pushStdModelDataService.queryPushStdModelDataExt(query);

        // Then
        assertEquals(2, result.getCurrent());
        assertEquals(100, result.getTotal());
        assertEquals(1, result.getRows().size());

        verify(pushStdModelDataRepository).selectExtByQuery(any(PushStdModelDataQueryDTO.class));
    }

    @Test
    public void testQueryPushStdModelDataExt_WithNullLastUpdatedDateStart() {
        // Given
        PushStdModelDataQueryDTO query = new PushStdModelDataQueryDTO();
        query.setTaskNoFrom("task1");
        query.setPageNum(2);
        query.setPageSize(10);

        Page<PushStdModelDataExtDTO> mockPage = Mockito.mock(Page.class);
        when(mockPage.getPageNum()).thenReturn(2);
        when(mockPage.getTotal()).thenReturn(100L);
        when(mockPage.getResult()).thenReturn(Lists.newArrayList(new PushStdModelDataExtDTO()));

        when(pushStdModelDataRepository.selectExtByQuery(any(PushStdModelDataQueryDTO.class))).thenReturn(mockPage);

        // When
        PageRows<PushStdModelDataExtDTO> result = pushStdModelDataService.queryPushStdModelDataExt(query);

        // Then
        assertEquals(2, result.getCurrent());
        assertEquals(100, result.getTotal());
        assertEquals(1, result.getRows().size());

        verify(pushStdModelDataRepository).selectExtByQuery(any(PushStdModelDataQueryDTO.class));
    }

    @Test
    public void testQueryPushStdModelDataExt_WithDefaultPageNumAndPageSize() {
        // Given
        PushStdModelDataQueryDTO query = new PushStdModelDataQueryDTO();
        query.setTaskNoFrom("task1");
        query.setLastUpdatedDateStart(new Date());

        Page<PushStdModelDataExtDTO> mockPage = Mockito.mock(Page.class);
        when(mockPage.getPageNum()).thenReturn(1);
        when(mockPage.getTotal()).thenReturn(100L);
        when(mockPage.getResult()).thenReturn(Lists.newArrayList(new PushStdModelDataExtDTO()));

        when(pushStdModelDataRepository.selectExtByQuery(any(PushStdModelDataQueryDTO.class))).thenReturn(mockPage);

        // When
        PageRows<PushStdModelDataExtDTO> result = pushStdModelDataService.queryPushStdModelDataExt(query);

        // Then
        assertEquals(1, result.getCurrent());
        assertEquals(100, result.getTotal());
        assertEquals(1, result.getRows().size());

        verify(pushStdModelDataRepository).selectExtByQuery(any(PushStdModelDataQueryDTO.class));
    }
}
/*Ended by AICoder, pid:b470c2aa0cyb4ef147190986221bbc0eab64e011*/