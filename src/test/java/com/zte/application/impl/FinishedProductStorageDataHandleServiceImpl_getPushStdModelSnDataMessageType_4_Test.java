/*Started by AICoder, pid:53363k035a0f99214bd00a32d014f25e60b2ec66*/
package com.zte.application.impl;
import com.zte.common.utils.Constant;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.jupiter.api.Assertions.assertEquals;

@RunWith(PowerMockRunner.class)
public class FinishedProductStorageDataHandleServiceImpl_getPushStdModelSnDataMessageType_4_Test {

    @InjectMocks
    private FinishedProductStorageDataHandleServiceImpl service;

    @Test
    public void testGetPushStdModelSnDataMessageType() {
        // Given

        // When
        String result = service.getPushStdModelSnDataMessageType();

        // Then
        assertEquals(Constant.MESSAGE_TYPE_FINISHED_PRODUCT_STORAGE, result);
    }
}
/*Ended by AICoder, pid:53363k035a0f99214bd00a32d014f25e60b2ec66*/