package com.zte.application.impl;

import com.zte.common.utils.Constant;
import com.zte.domain.model.AlarmLog;
import com.zte.domain.model.AlarmLogRepository;
import com.zte.interfaces.dto.AlarmLogPageQueryDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023/1/31 11:33
 * @Description
 */
@RunWith(PowerMockRunner.class)
public class AlarmLogServiceImplTest extends BaseTestCase {
    @Mock
    AlarmLogRepository alarmLogRepository;
    @InjectMocks
    AlarmLogServiceImpl alarmLogServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testQueryPage() throws Exception {
        when(alarmLogRepository.countPage(any())).thenReturn(0L);
        when(alarmLogRepository.queryPage(any())).thenReturn(Arrays.<AlarmLog>asList(new AlarmLog()));

        Assert.assertNotNull(alarmLogServiceImpl.queryPage(new AlarmLogPageQueryDTO()));
    }

    @Test
    public void testGetById() throws Exception {
        when(alarmLogRepository.selectById(anyString())).thenReturn(new AlarmLog());

        Assert.assertNotNull(alarmLogServiceImpl.getById("id"));
//        Assert.assertEquals(new AlarmLog(), result);
    }

    @Test
    public void testAdd() throws Exception {
        when(alarmLogRepository.insert(any())).thenReturn(0L);

        alarmLogServiceImpl.add(new AlarmLog());
        AlarmLog alarmLog= new AlarmLog();
        Assert.assertNotNull(alarmLog);
    }

    @Test
    public void testAddBatch() throws Exception {
        when(alarmLogRepository.insertBatch(any())).thenReturn(0L);

        alarmLogServiceImpl.addBatch(Arrays.<AlarmLog>asList(new AlarmLog()));
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void testUpdateById() throws Exception {
        when(alarmLogRepository.updateById(any())).thenReturn(0L);

        alarmLogServiceImpl.updateById(new AlarmLog());
        AlarmLog alarmLog= new AlarmLog();
        Assert.assertNotNull(alarmLog);
    }

    @Test
    public void testDeleteByIds() throws Exception {
        when(alarmLogRepository.deleteByIds(any())).thenReturn(0L);

        alarmLogServiceImpl.deleteByIds(Arrays.<String>asList("String"));
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme