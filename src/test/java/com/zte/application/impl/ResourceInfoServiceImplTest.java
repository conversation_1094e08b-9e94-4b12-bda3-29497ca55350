package com.zte.application.impl;


import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.zte.application.HrmUserCenterService;
import com.zte.application.ProgramVerifyConfirmInfoService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.*;
import com.zte.interfaces.dto.AuxMaterialRequisitionRelationshipPageQueryDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.ResourceInfoEntityDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.FileUtils;
import com.zte.util.BaseTestCase;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.mock.web.MockHttpServletResponse;

import javax.servlet.ServletOutputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisHelper.class, RedisCacheUtils.class, SpringContextUtil.class, FileUtils.class, DateUtils.class,EasyExcelFactory.class})
public class ResourceInfoServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ResourceInfoServiceImpl service;

    @Mock
    private ResourceWarningRecordRepository resourceWarningRecordRepository;

    @Mock
    private ResourceInfoRepository resourceInfoRepository;
    @Mock
    private ResourceInfoDetailRepository infoDetailRepository;
    @Mock
    private ProgramVerifyConfirmInfoService programVerifyConfirmInfoService;
    @Mock
    private ResourceInfoDetailServiceImpl resourceInfoDetailService;
    @Mock
    private SysLookupValuesRepository lookupValuesRepository;

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Mock
    private EmailUtils emailUtils;
    @Mock
    MockHttpServletResponse response;
    @Mock
    ExcelWriter excelWriter;
    @Mock
    ExcelWriterBuilder write;
    @Mock
    ServletOutputStream outputStream;
    @Mock
    ExcelWriterSheetBuilder excelWriterSheetBuilder;
    @Test
    public void pageList() throws Exception {
        ResourceInfoEntityDTO info = new ResourceInfoEntityDTO();
        try {
            service.pageList(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.RESOURCE_NO_STR_TIME_SHOULD_NOT_BE_NULL, e.getMessage());
        }
        info.setStartTime("2023-12-19 00:00:00");
        try {
            service.pageList(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.RESOURCE_NO_STR_TIME_SHOULD_NOT_BE_NULL, e.getMessage());
        }
        info.setStartTime(null);
        info.setEndTime("2027-01-19 23:59:59");
        try {
            service.pageList(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.RESOURCE_NO_STR_TIME_SHOULD_NOT_BE_NULL, e.getMessage());
        }
        info.setStartTime("2023-12-19 00:00:00");
        info.setEndTime("2027-01-19 23:59:59");
        try {
            service.pageList(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.SEARCH_TIME_LIMIT, e.getMessage());
        }
        info.setEndTime("2024-01-19 23:59:59");
        service.pageList(info);
        info.setResourceNo("123");
        service.pageList(info);
        info.setResourceNo(null);
        info.setResourceStr("123");
        try {
            service.pageList(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.RESOURCE_TYPE_SHOULD_NOT_BE_NULL, e.getMessage());
        }

        info.setResourceNo("123");
        info.setResourceStr("123");
        try {
            service.pageList(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.RESOURCE_TYPE_SHOULD_NOT_BE_NULL, e.getMessage());
        }
    }

    @Test
    public void countExportTotal1() {
        ResourceInfoEntityDTO info = new ResourceInfoEntityDTO();
        info.setResourceType(MpConstant.NAL);
        info.setResourceNo("123");
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }
        info.setResourceStr("123");
        service.countExportTotal(info);
    }

    @Test
    public void countExportTotal() {
        ResourceInfoEntityDTO info = new ResourceInfoEntityDTO();
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.RESOURCE_TYPE_CAN_NOT_BE_NULL, e.getMessage());
        }
        info.setResourceType("122");
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }
        info.setStartTime("2023-12-19 00:00:00");
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }
        info.setStartTime(null);
        info.setEndTime("2024-01-19 23:59:59");
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }
        info.setStartTime("2023-12-19 00:00:00");
        info.setEndTime("2025-01-19 23:59:59");
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_INTERVAL_ERROR, e.getMessage());
        }
        info.setEndTime("2024-01-19 23:59:59");

        //ResourceStr为空
        service.countExportTotal(info);
        //ResourceType
        info.setResourceType(MpConstant.NAL);
        service.countExportTotal(info);
        info.setResourceStr("123");
        List<ResourceInfoEntityDTO> pageInfo = new ArrayList<>();
        PowerMockito.when(infoDetailRepository.findResourceNoByNum(Mockito.anyString())).thenReturn(null);
        PowerMockito.when(resourceInfoRepository.pageList(Mockito.any())).thenReturn(pageInfo);
        service.countExportTotal(info);
    }

    @Test
    public void queryExportData() throws Exception {
        ResourceInfoEntityDTO info = new ResourceInfoEntityDTO();
        List<ResourceInfoEntityDTO> pageInfo = new ArrayList<>();
        ResourceInfoEntityDTO dto = new ResourceInfoEntityDTO();
        dto.setCreateBy("123");
        pageInfo.add(dto);
        PowerMockito.when(resourceInfoRepository.pageList(Mockito.any())).thenReturn(null);
        service.queryExportData(info, 1, 10);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("GY");
        hrmPersonInfoDTOMap.put("123",hrmPersonInfoDTO);
        Map<String, String> bsPubHrMap = new HashMap<>();
        PowerMockito.when(programVerifyConfirmInfoService.getUserNew(Mockito.any())).thenReturn(bsPubHrMap);

        List<SysLookupValues> valuesList = new ArrayList<>();
        PowerMockito.when(lookupValuesRepository.getList(Mockito.any())).thenReturn(valuesList);
        try {
            service.queryExportData(info, 1, 10);
        }catch (Exception e){
            Assert.assertNotNull(e.getMessage());
        }
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setAttribute1("111");
        valuesList.add(sysLookupValues);
        service.queryExportData(info, 1, 10);
        sysLookupValues.setLookupMeaning("123456");
        sysLookupValues.setDescriptionChin("中文");
        valuesList.add(sysLookupValues);
        info.setResourceStatus("123456");
        info.setResourceType("123456");

        PowerMockito.when(resourceInfoRepository.pageList(Mockito.any())).thenReturn(pageInfo);
        Assert.assertNotNull(service.queryExportData(info, 1, 10));
    }

    @Test
    public void backPassRatioCalWithResourceNo() {
        SysLookupValues sysTimeRange = new SysLookupValues();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("0.6");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any())).thenReturn(null);
        List<String> resourceNoList = new ArrayList<>();
        PowerMockito.when(infoDetailRepository.resourceNoListByTimeRange(Mockito.any())).thenReturn(resourceNoList);
        List<ResourceInfoEntityDTO> resourceTemp = new ArrayList<>();
        ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
        resourceInfoEntityDTO.setBackPassWarning("Y");
        PowerMockito.when(resourceInfoRepository.resourceNoListInTimeRange(Mockito.any())).thenReturn(new ArrayList<>());
        try{
            service.backPassRatioCalWithResourceNo(new ResourceInfoEntityDTO());
        }catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        resourceNoList.add("test123");
        try{
            service.backPassRatioCalWithResourceNo(new ResourceInfoEntityDTO());
        }catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(infoDetailRepository.calBackPassRatioWithResourceNo(Mockito.any(),Mockito.any())).thenReturn(resourceTemp);
        PowerMockito.when(resourceInfoRepository.batchUpdateBackPassRatio(Mockito.any())).thenReturn(1);
        PowerMockito.when(resourceInfoRepository.resourceNoListInTimeRange(Mockito.any())).thenReturn(resourceNoList);
        try{
            service.backPassRatioCalWithResourceNo(new ResourceInfoEntityDTO());
        }catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any())).thenReturn(sysTimeRange);
        try{
            service.backPassRatioCalWithResourceNo(new ResourceInfoEntityDTO());
        }catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        sysTimeRange.setLookupMeaning("3-12");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any())).thenReturn(sysTimeRange).thenReturn(sysLookupValues);
        try{
            service.backPassRatioCalWithResourceNo(new ResourceInfoEntityDTO());
        }catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        sysLookupValues.setLookupMeaning("");
        resourceTemp.add(resourceInfoEntityDTO);
        try{
            service.backPassRatioCalWithResourceNo(new ResourceInfoEntityDTO());
        }catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        resourceInfoEntityDTO.setTagApplicant("00286523");
        PowerMockito.when(emailUtils.sendMail(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(true);
        try{
            service.backPassRatioCalWithResourceNo(new ResourceInfoEntityDTO());
        }catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void backPassRatioCalForAll() {
        SysLookupValues sysTimeRange = new SysLookupValues();

        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any())).thenReturn(null);
        try{
            service.backPassRatioCalForAll();
        }catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any())).thenReturn(sysTimeRange);
        List<String> resourceNoList = new ArrayList<>();
        String resourceNo="1";
        resourceNoList.add(resourceNo);
        PowerMockito.when(infoDetailRepository.resourceNoListByTimeRange(Mockito.any())).thenReturn(resourceNoList);
        try{
            service.backPassRatioCalForAll();
        }catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        sysTimeRange.setLookupMeaning("3-12");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any())).thenReturn(sysTimeRange);
        PowerMockito.when(infoDetailRepository.backPassNumForAll(Mockito.anyList(),Mockito.any())).thenReturn(100);
        PowerMockito.when(lookupValuesRepository.updateSysLookupValuesById(Mockito.any())).thenReturn(1);
        try{
            service.backPassRatioCalForAll();
        }catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void getProductNameByNum() throws Exception {
        PowerMockito.when(resourceInfoRepository.getProductNameByNum(Mockito.any())).thenReturn("test");
        Assert.assertNotNull(service.getProductNameByNum("test"));
    }

    @Test
    public void setBackPassRatio() {
        try{
            service.setBackPassRatio(new ArrayList<>());
        }catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<ResourceInfoEntityDTO> resourceInfolist = new ArrayList<>();
        ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
        resourceInfoEntityDTO.setBackPassRatio(new BigDecimal(0.00));
        resourceInfolist.add(resourceInfoEntityDTO);
        ResourceInfoEntityDTO resourceInfoEntityDTO1 = new ResourceInfoEntityDTO();
        resourceInfoEntityDTO1.setBackPassRatio(new BigDecimal(0.12));
        resourceInfolist.add(resourceInfoEntityDTO1);
        ResourceInfoEntityDTO resourceInfoEntityDTO2 = new ResourceInfoEntityDTO();
        resourceInfoEntityDTO2.setBackPassRatio(null);
        resourceInfolist.add(resourceInfoEntityDTO2);
        try{
            service.setBackPassRatio(resourceInfolist);
        }catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void downLoadExcelTemplate() throws Exception {
        PowerMockito.mockStatic(EasyExcelFactory.class);
        PowerMockito.when(response.getOutputStream()).thenReturn(outputStream);
        PowerMockito.when(EasyExcelFactory.write(outputStream, NetworkLicenseExcelModelBO.class))
                .thenReturn(write);
        PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(write);
        PowerMockito.when(write.build()).thenReturn(excelWriter);
        PowerMockito.when(EasyExcelFactory.writerSheet(Mockito.any(), Mockito.any()))
                .thenReturn(excelWriterSheetBuilder);

        service.downLoadExcelTemplate(response);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }
}
