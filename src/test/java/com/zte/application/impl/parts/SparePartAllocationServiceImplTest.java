package com.zte.application.impl.parts;

import com.zte.application.impl.HrmUserCenterServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.ApprovalProcessInfoRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.domain.model.part.SparePartDetailRepository;
import com.zte.domain.model.part.SparePartHeadRepository;
import com.zte.infrastructure.remote.ApprovalRemoteService;
import com.zte.infrastructure.remote.OpenApiRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO;
import com.zte.interfaces.dto.spare.SparePartAllocationQueryDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

@PrepareForTest({RequestHeadValidationUtil.class})
public class SparePartAllocationServiceImplTest extends BaseTestCase {

    @InjectMocks
    private SparePartAllocationServiceImpl service;
    @Mock
    private SparePartHeadRepository sparePartHeadRepository;
    @Mock
    private SparePartDetailRepository sparePartDetailRepository;
    @Mock
    private HrmUserCenterServiceImpl hrmUserCenterService;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private ApprovalProcessInfoRepository approvalProcessInfoRepository;
    @Mock
    private OpenApiRemoteService openApiRemoteService;
    @Mock
    private ApprovalRemoteService approvalRemoteService;
    @Before
    public void init(){
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);
    }
    @Test
    public void testQuerySpareHeadInfo() throws Exception {
        SparePartAllocationQueryDTO queryDTO = new SparePartAllocationQueryDTO();
        try {
            service.querySpareHeadInfo(queryDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPARE_PART_ALLOCATION_QUERY_PARAM_NULL, e.getMessage());
        }
        queryDTO.setPartCode("1234567");
        queryDTO.setTransferFactoryId("52");
        queryDTO.setBillStatus("4");
        queryDTO.setPartType("0");
        queryDTO.setCreateBy("74960");
        queryDTO.setTransferBy("74960");
        queryDTO.setReceiveBy("74960");
        queryDTO.setEmailCc("74960,9999");
        PowerMockito.when(sparePartHeadRepository.pageList(any())).thenReturn(new ArrayList<>());
        service.querySpareHeadInfo(queryDTO);

        List<SparePartAllocationQueryDTO> list = new ArrayList<>();
        list.add(queryDTO);
        List<SysLookupValues> sysLookupValues = new ArrayList<>();
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupType(BigDecimal.valueOf(1004106));
        sysLookupValues1.setLookupMeaning("52");
        sysLookupValues1.setDescriptionChin("长沙");
        SysLookupValues sysLookupValues2 = new SysLookupValues();
        sysLookupValues2.setLookupType(BigDecimal.valueOf(1004107));
        sysLookupValues2.setLookupMeaning("0");
        sysLookupValues2.setDescriptionChin("钢网");
        SysLookupValues sysLookupValues3 = new SysLookupValues();
        sysLookupValues3.setLookupType(BigDecimal.valueOf(1004108));
        sysLookupValues3.setLookupMeaning("4");
        sysLookupValues3.setDescriptionChin("待调拨");
        sysLookupValues.add(sysLookupValues1);
        sysLookupValues.add(sysLookupValues2);
        sysLookupValues.add(sysLookupValues3);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("老蔡");
        hrmPersonInfoDTOMap.put("74960", hrmPersonInfoDTO);
        SparePartAllocationQueryDTO queryDTO1 = new SparePartAllocationQueryDTO();
        queryDTO1.setPartCode("1234567");
        queryDTO1.setTransferFactoryId("51");
        queryDTO1.setBillStatus("1");
        queryDTO1.setPartType("1");
        queryDTO1.setCreateBy("9999");
        queryDTO1.setTransferBy("9999");
        queryDTO1.setReceiveBy("9999");
        list.add(queryDTO1);
        PowerMockito.when(sparePartHeadRepository.pageList(any())).thenReturn(list);
        PowerMockito.when(sysLookupValuesRepository.selectByTypeBatch(any())).thenReturn(new ArrayList<>());
        service.querySpareHeadInfo(queryDTO);

        PowerMockito.when(sysLookupValuesRepository.selectByTypeBatch(any())).thenReturn(sysLookupValues);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);
        service.querySpareHeadInfo(queryDTO);
    }

    @Test
    public void testQueryBillDetailByBillNo() {
        SparePartAllocationQueryDTO queryDTO = new SparePartAllocationQueryDTO();
        queryDTO.setDetailId("111");
        queryDTO.setAllocationStatus("5");
        SparePartAllocationQueryDTO queryDTO1 = new SparePartAllocationQueryDTO();
        queryDTO1.setDetailId("222");
        queryDTO1.setAllocationStatus("12");
        SparePartAllocationQueryDTO queryDTO2 = new SparePartAllocationQueryDTO();
        queryDTO2.setDetailId("333");
        queryDTO2.setAllocationStatus("9");
        SparePartAllocationQueryDTO queryDTO3 = new SparePartAllocationQueryDTO();
        queryDTO3.setDetailId("444");
        queryDTO3.setAllocationStatus("1");
        List<SparePartAllocationQueryDTO> list = new ArrayList<>();
        list.add(queryDTO);
        list.add(queryDTO1);
        list.add(queryDTO2);
        list.add(queryDTO3);
        PowerMockito.when(sparePartDetailRepository.queryBillDetailByBillNo(any())).thenReturn(list);
        PowerMockito.when(sparePartDetailRepository.getAllocationQuantity(any())).thenReturn(new ArrayList<>());
        service.queryBillDetailByBillNo(queryDTO);
        PowerMockito.when(sparePartDetailRepository.getAllocationQuantity(any())).thenReturn(list);
        Page<SparePartAllocationQueryDTO> page = service.queryBillDetailByBillNo(queryDTO);
        Assert.assertTrue(page.getRows().size() >= 0);
    }

    @Test
    public void testQueryItemDetailByBillNo() throws Exception {
        SparePartAllocationQueryDTO queryDTO = new SparePartAllocationQueryDTO();
        Page<SparePartAllocationQueryDTO> page = service.queryItemDetailByBillNo(queryDTO);
        Assert.assertTrue(page.getRows().size() >= 0);
    }

    @Test
    public void testCloseSparePartAllocation() {
        try {
            PowerMockito.when(sparePartHeadRepository.checkAllocationItemDetailClose(any())).thenReturn(1);
            service.closeSparePartAllocation("123", "123");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CLOSE_BILL_DETAIL_ALLOCATION_STATUS_WRONG, e.getMessage());
        }
        PowerMockito.when(sparePartHeadRepository.checkAllocationItemDetailClose(any())).thenReturn(0);
        service.closeSparePartAllocation("123", "123");
    }

    @Test
    public void testDeleteSparePartAllocation() {
        SparePartAllocationQueryDTO sparePartAllocationQueryDTO = new SparePartAllocationQueryDTO();
        sparePartAllocationQueryDTO.setBillNo("123");
        service.deleteSparePartAllocation(sparePartAllocationQueryDTO, "123");

        sparePartAllocationQueryDTO.setBillStatus("2");
        Assert.assertNotNull(service.deleteSparePartAllocation(sparePartAllocationQueryDTO, "123"));
    }

    @Test
    public void testRollbackSparePartAllocation() {
        SparePartAllocationQueryDTO queryDTO = new SparePartAllocationQueryDTO();
        Assert.assertNotNull(service.rollbackSparePartAllocation(queryDTO, "123"));
    }

    @Test
    public void testCountExportTotal() {
        SparePartAllocationQueryDTO queryDTO = new SparePartAllocationQueryDTO();
        Assert.assertNotNull(service.countExportTotal(queryDTO));
    }

    @Test
    public void testQueryExportData() {
        SparePartAllocationQueryDTO queryDTO = new SparePartAllocationQueryDTO();
        try {
            service.queryExportData(queryDTO, 1, 10);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPARE_PART_ALLOCATION_QUERY_PARAM_NULL, e.getMessage());
        }
        queryDTO.setPartCode("1234");
        service.queryExportData(queryDTO, 1, 10);
    }

    @Test
    public void testGetApprovalDetailByBillNo() {
        Assert.assertNotNull(service.getApprovalDetailByBillNo("123"));
    }

    @Test
    public void testGetBillNos() throws Exception {
        SparePartAllocationHeadDTO headDTO = new SparePartAllocationHeadDTO();
        headDTO.setStatus("8");
        List<SparePartAllocationHeadDTO> list = new ArrayList<>();
        PowerMockito.when(sparePartHeadRepository.getBillNos(any()))
                .thenReturn(list);
        Assert.assertNotNull(service.getBillNos(headDTO));
    }

    @Test
    public void testGetInfoByBillNo() throws Exception {
        SparePartAllocationHeadDTO headDTO = new SparePartAllocationHeadDTO();
        headDTO.setBillNo("8");
        List<SparePartAllocationHeadDTO> list = new ArrayList<>();
        PowerMockito.when(sparePartHeadRepository.getInfoByBillNo(any()))
                .thenReturn(list);
        Assert.assertNotNull(service.getInfoByBillNo(headDTO));
    }

    @Test
    public void checkItemExist() throws Exception {
        SparePartAllocationHeadDTO headDTO = new SparePartAllocationHeadDTO();
        headDTO.setPartCode("8");
        headDTO.setBillNo("8");
        PowerMockito.when(sparePartHeadRepository.checkItemExist(any()))
                .thenReturn(0);
        try{
            service.getInfoByBillNo(headDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_IN_THE_DOCUMENT_DETAILS, e.getMessage());
        }
    }

    @Test
    public void checkItemExistTwo() throws Exception {
        SparePartAllocationHeadDTO headDTO = new SparePartAllocationHeadDTO();
        headDTO.setPartCode("8");
        headDTO.setBillNo("8");
        PowerMockito.when(sparePartHeadRepository.checkItemExist(any()))
                .thenReturn(1);
        try {
            service.getInfoByBillNo(headDTO);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.NOT_IN_THE_DOCUMENT_DETAILS);
        }
    }

    @Test
    public void testUpdateDetail() throws Exception {
        SparePartAllocationHeadDTO headDTO = new SparePartAllocationHeadDTO();
        headDTO.setTransOutbound(true);
        headDTO.setPartCode("test");
        PowerMockito.when(sparePartHeadRepository.checkDetailId(any()))
                .thenReturn("test");
        PowerMockito.when(sparePartHeadRepository.updateDetailStatus(any()))
                .thenReturn(1);
        Assert.assertNotNull(service.updateDetail(headDTO));
    }

    @Test
    public void testUpdateDetailTwo() throws Exception {
        SparePartAllocationHeadDTO headDTO = new SparePartAllocationHeadDTO();
        headDTO.setTransOutbound(false);
        headDTO.setPartCode("test");
        PowerMockito.when(sparePartHeadRepository.checkDetailId(any()))
                .thenReturn("test");
        PowerMockito.when(sparePartHeadRepository.updateDetailStatus(any()))
                .thenReturn(1);
        Assert.assertNotNull(service.updateDetail(headDTO));
    }

    @Test
    public void testUpdateHeadStatus() throws Exception {
        SparePartAllocationHeadDTO headDTO = new SparePartAllocationHeadDTO();
        headDTO.setTransOutbound(false);
        headDTO.setPartCode("test");
        headDTO.setPartName("test");
        headDTO.setStatus("12");
        List<SparePartAllocationHeadDTO> list = new ArrayList<>();
        list.add(headDTO);
        PowerMockito.when(sparePartHeadRepository.getCountByBillNo(any()))
                .thenReturn(list);
        PowerMockito.when(sparePartHeadRepository.getCountByBillNoAndStatus(any()))
                .thenReturn(1);
        PowerMockito.when(sparePartHeadRepository.updateHeadStatus(any()))
                .thenReturn(1);

        service.updateHeadStatus(headDTO);
        Assert.assertNotNull(headDTO);
    }

    @Test
    public void testUpdateHeadStatusTwo() throws Exception {
        SparePartAllocationHeadDTO headDTO = new SparePartAllocationHeadDTO();
        headDTO.setTransOutbound(false);
        headDTO.setPartCode("test");
        headDTO.setPartName("test");
        headDTO.setStatus("12");
        headDTO.setQuantity(1);
        List<SparePartAllocationHeadDTO> list = new ArrayList<>();
        list.add(headDTO);
        PowerMockito.when(sparePartHeadRepository.getCountByBillNo(any()))
                .thenReturn(list);
        PowerMockito.when(sparePartHeadRepository.getCountByBillNoAndStatus(any()))
                .thenReturn(1);
        PowerMockito.when(sparePartHeadRepository.updateHeadStatus(any()))
                .thenReturn(1);

        service.updateHeadStatus(headDTO);
        Assert.assertNotNull(headDTO);
    }


    @Test
    public void deleteStencilsOriginalInfo() throws Exception {
        EmSmtStencil emSmtStencil = new EmSmtStencil();
        emSmtStencil.setToFactoryId("52");
        emSmtStencil.setStencilsCode("test");
        PowerMockito.doNothing().when(openApiRemoteService).deleteStencilsOriginalInfo(Mockito.any());
        service.deleteStencilsOriginalInfo(emSmtStencil);
        Assert.assertNotNull(emSmtStencil);
    }

    @Test
    public void deleteSolderOriginalInfo() throws Exception {
        SolderInfoDTO solderInfoDTO = new SolderInfoDTO();
        solderInfoDTO.setToFactoryId("52");
        solderInfoDTO.setBarcode("test");
        PowerMockito.doNothing().when(openApiRemoteService).deleteSolderOriginalInfo(Mockito.any());
        service.deleteSolderOriginalInfo(solderInfoDTO);
        Assert.assertNotNull(solderInfoDTO);
    }

    @Test
    public void deleteFixtureOriginalInfo() throws Exception {
        FixtureInfoDetailDTO fixtureInfoDetailDTO = new FixtureInfoDetailDTO();
        fixtureInfoDetailDTO.setToFactoryId("52");
        fixtureInfoDetailDTO.setBarcode("test");
        PowerMockito.doNothing().when(openApiRemoteService).deleteFixtureOriginalInfo(Mockito.any());
        service.deleteFixtureOriginalInfo(fixtureInfoDetailDTO);
        Assert.assertNotNull(fixtureInfoDetailDTO);
    }

    @Test
    public void queryRelPcbInfo() throws Exception {
        EmSmtStencil emSmtStencil = new EmSmtStencil();
        emSmtStencil.setToFactoryId("52");
        emSmtStencil.setStencilsCode("test");
        List<EmSmtStencilDTO> list = new ArrayList<>();
        PowerMockito.when(openApiRemoteService.queryRelPcbInfo(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(service.queryRelPcbInfo(emSmtStencil));
    }

    @Test
    public void cancelTheApproval(){
        PowerMockito.when(RequestHeadValidationUtil.validaEmpno()).thenReturn("123");
        try {
            service.cancelTheApproval(null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAM_IS_NULL, e.getMessage());
        }
        service.cancelTheApproval("123");
    }
}