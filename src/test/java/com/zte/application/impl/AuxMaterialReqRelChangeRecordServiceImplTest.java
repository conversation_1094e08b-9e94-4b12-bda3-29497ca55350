package com.zte.application.impl;

import com.zte.application.HrmUserCenterService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.AuxMaterialReqRelChangeRecord;
import com.zte.domain.model.AuxMaterialReqRelChangeRecordRepository;
import com.zte.domain.model.AuxMaterialRequisitionRelationship;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.interfaces.dto.AuxMaterialReqRelChangeRecordPageQueryDTO;
import com.zte.interfaces.dto.AuxMaterialRequisitionRelationshipPageQueryDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

/**
 * 辅料领用关系变更记录表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-06-28 16:38:05
 */
@RunWith(PowerMockRunner.class)
public class AuxMaterialReqRelChangeRecordServiceImplTest extends BaseTestCase {

    @InjectMocks
    private AuxMaterialReqRelChangeRecordServiceImpl service;
    @Mock
    private AuxMaterialReqRelChangeRecordRepository repository;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private HrmUserCenterService hrmUserCenterService;
    @Test
    public void testQueryPage() throws Exception {
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(new ArrayList<AuxMaterialReqRelChangeRecord>());
        Assert.assertEquals(new ArrayList<>(), service.queryPage(new AuxMaterialReqRelChangeRecordPageQueryDTO()).getRows());
        AuxMaterialReqRelChangeRecordPageQueryDTO page = new AuxMaterialReqRelChangeRecordPageQueryDTO();
        service.queryPage(page);
        List<AuxMaterialReqRelChangeRecord> relationships = new ArrayList<>();
        AuxMaterialReqRelChangeRecord dto = new AuxMaterialReqRelChangeRecord();
        relationships.add(dto);
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(relationships);
        service.queryPage(page);
        dto.setApprovedBy("10337580");
        dto.setCreateBy("10337580");
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrm = new HrmPersonInfoDTO();
        hrm.setEmpName("张三");
        hrmPersonInfoDTOMap.put("10337580", hrm);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);

        List<SysLookupValues> values = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("0");
        sysLookupValues.setDescriptionChin("零");
        values.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesRepository.getList(any())).thenReturn(values);
        dto.setRelationshipDimension(0);
        dto.setAuxMaterialType(0);
        dto.setStatus(0);
        service.queryPage(page);

    }

    @Test
    public void testCountExportTotal() {
        PowerMockito.when(repository.selectCount(Mockito.any())).thenReturn(1);
        Assert.assertEquals(1L, (long) service.countExportTotal(new AuxMaterialReqRelChangeRecordPageQueryDTO()));
    }

    @Test
    public void testQueryExportData() {
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(new ArrayList<AuxMaterialReqRelChangeRecord>());
        Assert.assertEquals(new ArrayList<>(), service.queryExportData(new AuxMaterialReqRelChangeRecordPageQueryDTO(), 1, 10));
    }
    @Test
    public void addNewRecordWhenApprove() {
        service.addNewRecordWhenApprove(new AuxMaterialReqRelChangeRecord());
        AuxMaterialReqRelChangeRecord auxMaterialReqRelChangeRecord = new AuxMaterialReqRelChangeRecord();
        List<AuxMaterialRequisitionRelationship> approveList = new ArrayList<>();
        approveList.add(new AuxMaterialRequisitionRelationship());
        auxMaterialReqRelChangeRecord.setApproveList(approveList);
        service.addNewRecordWhenApprove(auxMaterialReqRelChangeRecord);
        Assert.assertNotNull(auxMaterialReqRelChangeRecord.getApproveList());
    }
}

