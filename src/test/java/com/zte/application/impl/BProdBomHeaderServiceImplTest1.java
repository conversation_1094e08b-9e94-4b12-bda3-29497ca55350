package com.zte.application.impl;

import com.zte.domain.model.BProdBomHeaderRepository;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
public class BProdBomHeaderServiceImplTest1 {

    @Mock
    private BProdBomHeaderRepository bProdBomHeaderRepository;

    @InjectMocks
    private BProdBomHeaderServiceImpl bProdBomHeaderService;


    @Before
    public void setUp() {
    }

    @Test
    public void testGetBProdBomHeaderWithValidDTO() throws Exception {
        BProdBomHeaderDTO invalidBProdBomHeaderDTO = null;
        List<BProdBomHeaderDTO> result = bProdBomHeaderService.getBProdBomHeader(invalidBProdBomHeaderDTO);
        Assert.assertEquals(Collections.emptyList(), result);

        invalidBProdBomHeaderDTO = new BProdBomHeaderDTO();
        invalidBProdBomHeaderDTO.setProdplanId("");
        invalidBProdBomHeaderDTO.setOriginalProductCode("XYZ");
        result = bProdBomHeaderService.getBProdBomHeader(invalidBProdBomHeaderDTO);
        Assert.assertEquals(Collections.emptyList(), result);

        invalidBProdBomHeaderDTO.setProdplanId("1234567");
        BProdBomHeaderDTO validBProdBomHeaderDTO = new BProdBomHeaderDTO();
        validBProdBomHeaderDTO.setProdplanId("123");
        validBProdBomHeaderDTO.setOriginalProductCode("ABC");
        List<BProdBomHeaderDTO> expectedList = Collections.singletonList(validBProdBomHeaderDTO);
        PowerMockito.when(bProdBomHeaderRepository.getBProdBomHeader(anyString(), anyString()))
                .thenReturn(expectedList);
        result = bProdBomHeaderService.getBProdBomHeader(validBProdBomHeaderDTO);
        Assert.assertEquals(expectedList, result);
    }
}
