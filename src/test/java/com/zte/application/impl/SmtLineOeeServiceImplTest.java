package com.zte.application.impl;

import com.zte.domain.model.SmtLineOee;
import com.zte.domain.model.SmtLineOeeRepository;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.springbootframe.common.model.Page;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

@RunWith(PowerMockRunner.class)
public class SmtLineOeeServiceImplTest extends TestCase {

    @InjectMocks
    private SmtLineOeeServiceImpl service;

    @Mock
    private SmtLineOeeRepository smtLineOeeRepository;
    @Mock
    private SysLookupTypesRepository sysLookupTypesRepository;

    @Test
    public void testPageList() {
        SmtLineOee param = new SmtLineOee();
        Page<SmtLineOee> page = new Page<>(param.getPage(), param.getRows());
        page.setParams(param);
        page.setRows(new ArrayList<>());
        Assert.assertEquals(page, service.pageList(param));

    }

    @Test
    public void testQueryOeeInfo() {
        SmtLineOee param = new SmtLineOee();
        Assert.assertEquals(new ArrayList<>(), service.queryOeeInfo(param));
        param.setCurrentDate(new Date());
        service.queryOeeInfo(param);
    }

    @Test
    public void testCountExportTotal() {
        Integer count = 0;
        Assert.assertEquals(count, service.countExportTotal(new SmtLineOee()));
    }

    @Test
    public void testQueryExportData() {
        SmtLineOee param = new SmtLineOee();
        List<SysLookupTypesDTO> valuesList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        valuesList.add(sysLookupTypesDTO);
        PowerMockito.when(sysLookupTypesRepository.getList(any())).thenReturn(valuesList);
        Assert.assertEquals(new ArrayList<>(), service.queryExportData(param, 0, 0));
        sysLookupTypesDTO.setLookupMeaning("55");
        sysLookupTypesDTO.setDescriptionChinV("heyuan");
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        valuesList.add(sysLookupTypesDTO1);
        sysLookupTypesDTO1.setLookupMeaning("52");
        param.setFactoryId(new BigDecimal(55));
        List<SmtLineOee> list = new ArrayList<>();
        list.add(param);
        PowerMockito.when(smtLineOeeRepository.pageList(any())).thenReturn(list);
        service.pageList(param);
        param.setIsDoubleTrack("Y");
        service.pageList(param);
    }
}