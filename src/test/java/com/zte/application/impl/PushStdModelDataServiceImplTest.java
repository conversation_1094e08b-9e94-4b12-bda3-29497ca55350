package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.CenterFactoryCallSiteService;
import com.zte.application.PsTaskExtendedService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.TradeDataLogService;
import com.zte.application.cbom.FixBomCommonService;
import com.zte.common.enums.BusinessSceneEnum;
import com.zte.common.enums.EntityClassEnum;
import com.zte.common.enums.SceneCodeEnum;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PushStdModelConfirmationRepository;
import com.zte.domain.model.PushStdModelDataRepository;
import com.zte.domain.model.PushStdModelSnDataRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.feign.ApsInOneClient;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.IscpRemoteService;
import com.zte.infrastructure.remote.NoticeCenterService;
import com.zte.infrastructure.remote.PdmRemoteService;
import com.zte.interfaces.dto.B2bCallBackNewDTO;
import com.zte.interfaces.dto.CivControlInfoDTO;
import com.zte.interfaces.dto.ManufactureOrderDTO;
import com.zte.interfaces.dto.MaterialBillDTO;
import com.zte.interfaces.dto.PickListResultDTO;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.PushStdDataTaskAndSnNumDTO;
import com.zte.interfaces.dto.PushStdModelConfirmationDTO;
import com.zte.interfaces.dto.PushStdModelDTO;
import com.zte.interfaces.dto.PushStdModelDataDTO;
import com.zte.interfaces.dto.PushStdModelSnDataDTO;
import com.zte.interfaces.dto.WipEntityInfoDTO;
import com.zte.interfaces.dto.WorkOrderWriteCallBackDTO;
import com.zte.interfaces.dto.WorkOrderWriteCallBackDataDTO;
import com.zte.interfaces.dto.WorkOrderWriteCallBackResultDTO;
import com.zte.interfaces.dto.aps.TasksQueryDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.NoticeCenterUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.util.ReflectionTestUtils;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mock;

@PrepareForTest({JSON.class,BasicsettingRemoteService.class, DatawbRemoteService.class, JacksonJsonConverUtil.class, PushStdModelDataServiceImpl.class, Validation.class})
public class PushStdModelDataServiceImplTest extends BaseTestCase {
    @InjectMocks
    private PushStdModelDataServiceImpl pushStdModelDataService;
    @Mock
    private PushStdModelDataRepository pushStdModelDataRepository;
    @Mock
    private PushStdModelConfirmationRepository pushStdModelConfirmationRepository;

    @Mock
    private PsTaskExtendedService psTaskExtendedService;

    /* Started by AICoder, pid:ne562r8f49sda0214be6085820687b0469387886 */
    @Mock
    private CenterFactoryCallSiteService centerFactoryCallSiteService;
    @Mock
    private NoticeCenterService noticeCenterService;
    @Mock
    private NoticeCenterUtils noticeCenterUtils;
    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private TradeDataLogService tradeDataLogService;
    @Mock
    private FixBomCommonService fixBomCommonService;
    @Mock
    private IscpRemoteService iscpRemoteService;

    @Mock
    private PdmRemoteService pdmRemoteService;
    @Mock
    private IdGenerator idGenerator;
    @Mock
    private ApsInOneClient apsInOneClient;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private Validator mockValidator;
    @Mock
    private PushStdModelSnDataRepository pushStdModelSnDataRepository;

    @Mock
    private StringRedisTemplate redisTemplate;
    @Mock
    private ValueOperations<String, String> redisOpsValue;

    private PushStdModelDataDTO pushStdModelDataDTO;
    private Map<String, PsTaskExtendedDTO> taskExtendedMap;
    private List<B2bCallBackNewDTO> dataList;
    /* Ended by AICoder, pid:ne562r8f49sda0214be6085820687b0469387886 */

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(pushStdModelDataService, "alarmTitle", "%s:任务对应厂商自供料预计齐套日期或全部物料预计齐套日期在APS不存在");
    }

    /* Started by AICoder, pid:yea2bm4334va79614a350948004e2d7f6557a63a */
    @Test
    public void mergeStdModelTask(){
        List<PushStdModelDataDTO> list = new ArrayList<>();
        List<String> customerNoList = new ArrayList<>();
        int result = pushStdModelDataService.mergeStdModelTask(list, customerNoList);
        Assert.assertEquals(0, result);

        PushStdModelDataDTO pushStdModelDataDTO = new PushStdModelDataDTO();
        pushStdModelDataDTO.setTaskNo("taskNo1");
        list.add(pushStdModelDataDTO);
        result = pushStdModelDataService.mergeStdModelTask(list, customerNoList);
        Assert.assertEquals(0, result);

        customerNoList.add("alibaba");
        List<PsTaskExtendedDTO> needPushTaskList = new ArrayList<>();
        Mockito.when(psTaskExtendedService.filterListByCustomerNo(anyList(), anyList())).thenReturn(needPushTaskList);
        result = pushStdModelDataService.mergeStdModelTask(list, customerNoList);
        Assert.assertEquals(0, result);

        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setTaskNo("taskNo1");
        psTaskExtendedDTO.setCustomerNo("alibaba");
        needPushTaskList.add(psTaskExtendedDTO);
        List<String> existTaskNoList = new ArrayList<>();
        existTaskNoList.add("taskNo1");
        Mockito.when(pushStdModelDataRepository.getExistTaskNo(anyList())).thenReturn(existTaskNoList);
        result = pushStdModelDataService.mergeStdModelTask(list, customerNoList);
        Assert.assertEquals(0, result);

        existTaskNoList.clear();
        ReflectionTestUtils.setField(pushStdModelDataService,"pushSeq", Arrays.asList("10","20"));
        Mockito.when(pushStdModelDataRepository.batchInsert(anyList())).thenReturn(1);
        result = pushStdModelDataService.mergeStdModelTask(list, customerNoList);
        Assert.assertEquals(1, result);
    }
    /* Ended by AICoder, pid:yea2bm4334va79614a350948004e2d7f6557a63a */

    @Test
    public void orderScheduleCallBack() {
        B2bCallBackNewDTO dto1 = new B2bCallBackNewDTO();
        pushStdModelDataService.orderScheduleCallBack(dto1);
        PowerMockito.mockStatic(JSON.class);
        dto1.setCode("0000");
        dto1.setSuccess(true);
        dto1.setKeywords("task123");
        dto1.setData("{\"messageId\":\"task123\", \"success\":true}");
        pushStdModelDataService.orderScheduleCallBack(dto1);

        dto1.setKeywords("task123,test");
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("123");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any())).thenReturn(sysLookupValues);

        WorkOrderWriteCallBackDTO workOrderWriteCallBackDTO = new WorkOrderWriteCallBackDTO();
        workOrderWriteCallBackDTO.setData("{\"success\": true, \"result\": {\"success\": true, \"msg\": \"Success\"}}");
        WorkOrderWriteCallBackDataDTO workOrderWriteCallBackDataDTO = new WorkOrderWriteCallBackDataDTO();
        workOrderWriteCallBackDataDTO.setSuccess(true);
        workOrderWriteCallBackDataDTO.setResult("{\"success\": true, \"msg\": \"Success\"}");
        WorkOrderWriteCallBackResultDTO workOrderWriteCallBackResultDTO = new WorkOrderWriteCallBackResultDTO();
        workOrderWriteCallBackResultDTO.setSuccess(true);

        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDTO.class))).thenReturn(workOrderWriteCallBackDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDataDTO.class))).thenReturn(workOrderWriteCallBackDataDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackResultDTO.class))).thenReturn(workOrderWriteCallBackResultDTO);
        pushStdModelDataService.orderScheduleCallBack(dto1);

        dto1.setKeywords("task123," + Constant.SCHEDULE_INFO);
        pushStdModelDataService.orderScheduleCallBack(dto1);

        ReflectionTestUtils.setField(pushStdModelDataService, "pushSeq", Arrays.asList("10","20"));
        PushStdModelDataDTO pushStdModelDataDTO = new PushStdModelDataDTO();
        PowerMockito.when(pushStdModelDataRepository.getDataByTaskNo(Mockito.anyString())).thenReturn(pushStdModelDataDTO);
        pushStdModelDataService.orderScheduleCallBack(dto1);

        pushStdModelDataDTO.setCurrProcess("10");
        pushStdModelDataService.orderScheduleCallBack(dto1);

        dto1.setKeywords("task123," + Constant.CONFIRMATION_INFO);
        pushStdModelDataService.orderScheduleCallBack(dto1);

        dto1.setSuccess(false);
        pushStdModelDataService.orderScheduleCallBack(dto1);
        assertTrue(true);
    }

    /* Started by AICoder, pid:x260fw60fca8209145500b2030a7bb6065713dd3 */
    @Test
    public void pushStdModelData() throws Exception {
        List<String> customerNameList = new ArrayList<>();
        int preDays = 0;
        int count = pushStdModelDataService.pushStdModelData(customerNameList, preDays);
        Assert.assertEquals(0, count);

        customerNameList.add("alibaba");
        List<PushStdModelDataDTO> needPushList = new ArrayList<>();
        List<PushStdModelDataDTO> needPushList2 = new ArrayList<>();
        Mockito.when(pushStdModelDataRepository.getNeedPushDataInc(any(), anyList(), Mockito.eq(""), Mockito.anyInt())).thenReturn(needPushList);
        Mockito.when(pushStdModelDataRepository.getNeedPushDataInc(any(), anyList(), Mockito.eq("100"), Mockito.anyInt())).thenReturn(needPushList2);
        Mockito.when(idGenerator.snowFlakeIdStr()).thenReturn("id");
        count = pushStdModelDataService.pushStdModelData(customerNameList, preDays);
        Assert.assertEquals(0, count);

        preDays = 100;
        PushStdModelDataDTO pushStdModelDataDTO = new PushStdModelDataDTO();
        pushStdModelDataDTO.setTaskNo("taskNo1");
        pushStdModelDataDTO.setLastUpdatedDate(new Date());
        pushStdModelDataDTO.setCurrProcess("10");
        pushStdModelDataDTO.setPushFailCount(0);
        needPushList.add(pushStdModelDataDTO);
        List<PsTaskExtendedDTO> psTaskExtendedList = new ArrayList<>();
        Mockito.when(psTaskExtendedService.queryByTaskNos(anyList())).thenReturn(psTaskExtendedList);
        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any())).thenReturn(sysLookupValues);
        sysLookupValues.setLookupMeaning("123");
        count = pushStdModelDataService.pushStdModelData(customerNameList, preDays);
        Assert.assertEquals(0, count);

        for (int i = 0; i <= 100; i++) {
            PushStdModelDataDTO dto = new PushStdModelDataDTO();
            dto.setTaskNo(i + "");
            dto.setCurrProcess("10");
            dto.setPushFailCount(0);
            needPushList.add(dto);
        }
        count = pushStdModelDataService.pushStdModelData(customerNameList, preDays);
        Assert.assertEquals(0, count);

        needPushList.clear();
        needPushList.add(pushStdModelDataDTO);
        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setTaskNo("taskNo1");
        psTaskExtendedList.add(psTaskExtendedDTO);
        count = pushStdModelDataService.pushStdModelData(customerNameList, preDays);
        Assert.assertEquals(0, count);

        psTaskExtendedDTO.setBusinessScene(BusinessSceneEnum.MANUFACTURE.getCode());
        Mockito.when(pdmRemoteService.getCategory(Mockito.anyString())).thenReturn("");
        Map<String, Date> startAndEndTimeMap = new HashMap<>();
        Mockito.when(centerFactoryCallSiteService.queryScheduleStartAndEndTime(Mockito.anyString(), Mockito.anyString())).thenReturn(null);
        List<FixBomDetailDTO> fixBomDetailList = new ArrayList<>();
        Mockito.when(fixBomCommonService.queryFixBomDetailByFixBomId(Mockito.anyString())).thenReturn(fixBomDetailList);
        Map<Integer, String> factoryIdToNameMap = new HashMap<>();
        factoryIdToNameMap.put(58, "南京工厂ZTE101");
        ReflectionTestUtils.setField(pushStdModelDataService, "factoryIdToNameMap", factoryIdToNameMap);
        count = pushStdModelDataService.pushStdModelData(customerNameList, preDays);
        Assert.assertEquals(0, count);

        psTaskExtendedDTO.setBusinessScene(BusinessSceneEnum.MODIFIED.getCode());
        Mockito.when(pdmRemoteService.getCategory(Mockito.anyString())).thenReturn("customPartType");
        Mockito.when(centerFactoryCallSiteService.queryScheduleStartAndEndTime(Mockito.anyString(), Mockito.anyString())).thenReturn(startAndEndTimeMap);
        count = pushStdModelDataService.pushStdModelData(customerNameList, preDays);
        Assert.assertEquals(0, count);

        psTaskExtendedDTO.setBusinessScene(BusinessSceneEnum.RETURN.getCode());
        psTaskExtendedDTO.setCustomerPartType("customPartType");
        count = pushStdModelDataService.pushStdModelData(customerNameList, preDays);
        Assert.assertEquals(0, count);

        psTaskExtendedDTO.setBusinessScene(BusinessSceneEnum.DISASSEMBLY.getCode());
        startAndEndTimeMap.put("scheduleStartDate", new Date());
        startAndEndTimeMap.put("scheduleEndDate", new Date());
        psTaskExtendedDTO.setFixBomId("fixBomId");
        psTaskExtendedDTO.setBillNo("billNo");
        psTaskExtendedDTO.setTaskType("1");
        pushStdModelDataDTO.setTaskQty(10);
        psTaskExtendedDTO.setSelfSupplyPrepareDate(new Date());
        psTaskExtendedDTO.setFullPrepareDate(new Date());
        psTaskExtendedDTO.setCustomerItemName("111.222.333");
        // 设置无效数据
        FixBomDetailDTO trash1 = new FixBomDetailDTO();
        fixBomDetailList.add(trash1);
        FixBomDetailDTO trash2 = new FixBomDetailDTO();
        trash2.setItemSeq("1");
        fixBomDetailList.add(trash2);
        FixBomDetailDTO trash3 = new FixBomDetailDTO();
        trash3.setItemLevel("0");
        fixBomDetailList.add(trash3);
        FixBomDetailDTO trash4 = new FixBomDetailDTO();
        trash4.setItemLevel("1");
        trash4.setItemSeq("1-9");
        fixBomDetailList.add(trash4);

        FixBomDetailDTO fixBomDetailDTO = new FixBomDetailDTO();
        fixBomDetailDTO.setItemLevel("0");
        fixBomDetailDTO.setItemSeq("1");
        fixBomDetailDTO.setFixBomRequired("Y");
        fixBomDetailDTO.setItemSupplierNo("F3CN.34.C0V1P0U1");
        fixBomDetailDTO.setItemType("成品料");
        fixBomDetailDTO.setCustomerComponentType("server.configmodel");
        fixBomDetailList.add(fixBomDetailDTO);
        FixBomDetailDTO fixBomDetailTop = new FixBomDetailDTO();
        fixBomDetailTop.setItemLevel("0");
        fixBomDetailTop.setItemSeq("2");
        fixBomDetailTop.setItemSupplierNo("供应商料号");
        fixBomDetailTop.setItemType("成品料");
        fixBomDetailTop.setCustomerComponentType("SSD");
        fixBomDetailList.add(fixBomDetailTop);
        FixBomDetailDTO fixBomDetailDTO1 = new FixBomDetailDTO();
        fixBomDetailDTO1.setItemLevel("1");
        fixBomDetailDTO1.setItemSeq("1-1");
        fixBomDetailDTO1.setFixBomRequired("Y");
        fixBomDetailDTO1.setItemType("成品料");
        fixBomDetailDTO1.setItemSupplierNo("供应商料号");
        fixBomDetailDTO1.setCustomerComponentType("SSD");
        fixBomDetailList.add(fixBomDetailDTO1);
        FixBomDetailDTO fixBomDetailDTO2 = new FixBomDetailDTO();
        fixBomDetailDTO2.setItemLevel("2");
        fixBomDetailDTO2.setItemSeq("1-1-1");
        fixBomDetailDTO2.setFixBomRequired("Y");
        fixBomDetailDTO2.setItemSupplierNo("供应商料号");
        fixBomDetailDTO2.setCustomerComponentType("SSD");
        fixBomDetailList.add(fixBomDetailDTO2);
        FixBomDetailDTO fixBomDetailDTO3 = new FixBomDetailDTO();
        fixBomDetailDTO3.setItemLevel("2");
        fixBomDetailDTO3.setItemSeq("1-1-2");
        fixBomDetailDTO3.setFixBomRequired("N");
        fixBomDetailDTO3.setItemSupplierNo("供应商料号");
        fixBomDetailDTO3.setCustomerComponentType("SSD");
        fixBomDetailList.add(fixBomDetailDTO3);
        FixBomDetailDTO fixBomDetailDTO4 = new FixBomDetailDTO();
        fixBomDetailDTO4.setItemLevel("2");
        fixBomDetailDTO4.setItemSeq("1-1-3");
        fixBomDetailDTO4.setFixBomRequired("Y");
        fixBomDetailDTO4.setItemSupplierNo("供应商料号");
        fixBomDetailDTO4.setCustomerComponentType("L6包");
        fixBomDetailDTO4.setItemType("成品料");
        fixBomDetailList.add(fixBomDetailDTO4);
        FixBomDetailDTO fixBomDetailDTO5 = new FixBomDetailDTO();
        fixBomDetailDTO5.setItemLevel("3");
        fixBomDetailDTO5.setItemSeq("1-1-3-1");
        fixBomDetailDTO5.setFixBomRequired("Y");
        fixBomDetailDTO5.setItemSupplierNo("供应商料号");
        fixBomDetailDTO5.setCustomerComponentType("CPU");
        fixBomDetailList.add(fixBomDetailDTO5);
        FixBomDetailDTO fixBomDetailDTO6 = new FixBomDetailDTO();
        fixBomDetailDTO6.setItemLevel("2");
        fixBomDetailDTO6.setItemSeq("3-1-3");
        fixBomDetailDTO6.setFixBomRequired("Y");
        fixBomDetailDTO6.setItemSupplierNo("供应商料号");
        fixBomDetailDTO6.setCustomerComponentType("CPU");
        fixBomDetailDTO6.setItemType("成品料");
        fixBomDetailList.add(fixBomDetailDTO6);
        FixBomDetailDTO fixBomDetailDTO7 = new FixBomDetailDTO();
        fixBomDetailDTO7.setItemLevel("2");
        fixBomDetailDTO7.setItemSeq("1-1-4");
        fixBomDetailDTO7.setFixBomRequired("Y");
        fixBomDetailDTO7.setItemSupplierNo("MOC");
        fixBomDetailDTO7.setCustomerComponentType("server.configmodel.moc");
        fixBomDetailDTO7.setItemType("MOC");
        fixBomDetailList.add(fixBomDetailDTO7);
        FixBomDetailDTO fixBomDetailDTO8 = new FixBomDetailDTO();
        fixBomDetailDTO8.setItemLevel("1");
        fixBomDetailDTO8.setItemSeq("1-2");
        fixBomDetailDTO8.setFixBomRequired("Y");
        fixBomDetailDTO8.setItemSupplierNo("F3C.32.C0V1P0UP");
        fixBomDetailDTO8.setCustomerComponentType("server.configmodel");
        fixBomDetailDTO8.setItemType("成品料");
        fixBomDetailList.add(fixBomDetailDTO8);
        FixBomDetailDTO fixBomDetailDTO9 = new FixBomDetailDTO();
        fixBomDetailDTO9.setItemLevel("2");
        fixBomDetailDTO9.setItemSeq("1-2-4");
        fixBomDetailDTO9.setFixBomRequired("Y");
        fixBomDetailDTO9.setItemSupplierNo("MOC");
        fixBomDetailDTO9.setCustomerComponentType("server.configmodel.moc");
        fixBomDetailDTO9.setItemType("MOC");
        fixBomDetailList.add(fixBomDetailDTO9);
        ReflectionTestUtils.setField(pushStdModelDataService,"specificItemTypeList", Arrays.asList("成品料"));
        ReflectionTestUtils.setField(pushStdModelDataService,"specificMaterialCategoryList", Arrays.asList("L6包"));
        ReflectionTestUtils.setField(pushStdModelDataService,"specificCustomerComponentType", Arrays.asList("server.configmodel.moc"));
        count = pushStdModelDataService.pushStdModelData(customerNameList, preDays);
        Assert.assertEquals(0, count);

        psTaskExtendedDTO.setBusinessScene(BusinessSceneEnum.REPAIR.getCode());
        count = pushStdModelDataService.pushStdModelData(customerNameList, preDays);
        Assert.assertEquals(0, count);

        psTaskExtendedDTO.setBusinessScene(BusinessSceneEnum.BUFFER.getCode());
        count = pushStdModelDataService.pushStdModelData(customerNameList, preDays);
        Assert.assertEquals(0, count);

        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        PowerMockito.when(redisOpsValue.get(any())).thenReturn(null);
        PowerMockito.when(redisOpsValue.setIfAbsent(Mockito.anyString(), Mockito.any(), Mockito.anyLong(),
                Mockito.anyObject())).thenReturn(false);
        Assert.assertEquals(0, count);

        PowerMockito.when(redisOpsValue.setIfAbsent(Mockito.anyString(), Mockito.any(), Mockito.anyLong(),
                Mockito.anyObject())).thenReturn(true);
        psTaskExtendedDTO.setEntityClass(EntityClassEnum.REWORK_2.getCode());
        List<PickListResultDTO> pickListResultDTOList = new ArrayList<>();
        PowerMockito.mockStatic(DatawbRemoteService.class);
        Mockito.when(DatawbRemoteService.queryMaterialOrderNoByTaskNo(Mockito.anyString())).thenReturn(pickListResultDTOList);
        count = pushStdModelDataService.pushStdModelData(customerNameList, preDays);
        Assert.assertEquals(1, count);

        PowerMockito.when(redisOpsValue.get(any())).thenReturn("taskNo");
        psTaskExtendedDTO.setEntityClass(EntityClassEnum.FG_DISAS_2.getCode());
        pickListResultDTOList.add(new PickListResultDTO());
        count = pushStdModelDataService.pushStdModelData(customerNameList, preDays);
        Assert.assertEquals(1, count);

        psTaskExtendedDTO.setEntityClass(EntityClassEnum.STD_UNIT_2.getCode());
        count = pushStdModelDataService.pushStdModelData(customerNameList, preDays);
        Assert.assertEquals(1, count);

        pushStdModelDataDTO.setCurrProcess("20");
        count = pushStdModelDataService.pushStdModelData(customerNameList, preDays);
        Assert.assertEquals(1, count);
    }
    /* Ended by AICoder, pid:x260fw60fca8209145500b2030a7bb6065713dd3 */

    /* Started by AICoder, pid:b7687o1bc1ud2771492e08620075715ad508c015 */
    @Test
    public void setControlLevel() throws Exception {
        ReflectionTestUtils.setField(pushStdModelDataService,"specificItemTypeList", Arrays.asList("成品料"));
        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        FixBomDetailDTO fixBomDetailDTO = new FixBomDetailDTO();
        MaterialBillDTO materialBillDTO = new MaterialBillDTO();
        List<String> aliControlItemNos = new ArrayList<>();
        fixBomDetailDTO.setItemType("成品料");
        psTaskExtendedDTO.setBusinessScene("MANUFACTUR");
        Whitebox.invokeMethod(pushStdModelDataService, "setControlLevel", psTaskExtendedDTO, fixBomDetailDTO, materialBillDTO, aliControlItemNos);
        Assert.assertNull(materialBillDTO.getControlLevel());

        fixBomDetailDTO.setItemType("成品料");
        psTaskExtendedDTO.setBusinessScene("BUFFER");
        Whitebox.invokeMethod(pushStdModelDataService, "setControlLevel", psTaskExtendedDTO, fixBomDetailDTO, materialBillDTO, aliControlItemNos);
        Assert.assertNull(materialBillDTO.getControlLevel());

        fixBomDetailDTO.setItemType("非成品料");
        psTaskExtendedDTO.setBusinessScene("MANUFACTUR");
        Whitebox.invokeMethod(pushStdModelDataService, "setControlLevel", psTaskExtendedDTO, fixBomDetailDTO, materialBillDTO, aliControlItemNos);
        Assert.assertNull(materialBillDTO.getControlLevel());

        fixBomDetailDTO.setItemType("非成品料");
        psTaskExtendedDTO.setBusinessScene("BUFFER");
        Whitebox.invokeMethod(pushStdModelDataService, "setControlLevel", psTaskExtendedDTO, fixBomDetailDTO, materialBillDTO, aliControlItemNos);

        aliControlItemNos.add("123456789123");
        fixBomDetailDTO.setZteCode("123456789123");
        Whitebox.invokeMethod(pushStdModelDataService, "setControlLevel", psTaskExtendedDTO, fixBomDetailDTO, materialBillDTO, aliControlItemNos);
        Assert.assertNull(materialBillDTO.getControlLevel());
    }
    /* Ended by AICoder, pid:b7687o1bc1ud2771492e08620075715ad508c015 */

    @Test
    public void testUpdateIfChange_whenValidInput_updatesTasks() {

// 准备更多测试数据
        PushStdModelDataDTO dto3 = new PushStdModelDataDTO();
        PushStdModelDataDTO dto4 = new PushStdModelDataDTO();
        List<PushStdModelDataDTO> largeList = Arrays.asList(dto3, dto4);

        // 假设BATCH_SIZE=2
        int batchSize = 2;

        // 执行方法
        pushStdModelDataService.batchSaveOrUpdate(largeList);
        Assert.assertEquals(2,batchSize);

    }
    @Test
    public void ticketClosedCallBack() throws Exception {
        PowerMockito.mockStatic(JSON.class);
        B2bCallBackNewDTO dto1 = new B2bCallBackNewDTO();
        dto1.setCode("0000");
        dto1.setSuccess(true);
        dto1.setKeywords("task123");
        dto1.setData("{\"messageId\":\"task123\", \"success\":true}");

        WorkOrderWriteCallBackDTO workOrderWriteCallBackDTO = new WorkOrderWriteCallBackDTO();
        workOrderWriteCallBackDTO.setData("{\"success\": true, \"result\": {\"success\": true, \"msg\": \"Success\"}}");
        WorkOrderWriteCallBackDataDTO workOrderWriteCallBackDataDTO = new WorkOrderWriteCallBackDataDTO();
        workOrderWriteCallBackDataDTO.setSuccess(true);
        workOrderWriteCallBackDataDTO.setResult("{\"success\": true, \"msg\": \"Success\"}");
        WorkOrderWriteCallBackResultDTO workOrderWriteCallBackResultDTO = new WorkOrderWriteCallBackResultDTO();
        workOrderWriteCallBackResultDTO.setSuccess(true);

        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDTO.class))).thenReturn(workOrderWriteCallBackDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDataDTO.class))).thenReturn(workOrderWriteCallBackDataDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackResultDTO.class))).thenReturn(workOrderWriteCallBackResultDTO);
        pushStdModelDataService.ticketClosedCallBack(dto1);
        dto1.setKeywords("");
        pushStdModelDataService.ticketClosedCallBack(dto1);
        PowerMockito.mockStatic(JSON.class);
        dto1.setCode("0000");
        dto1.setSuccess(false);
        dto1.setKeywords("task123");
        dto1.setData("{\"messageId\":\"task123\", \"success\":true}");

        workOrderWriteCallBackDTO.setData("{\"success\": true, \"result\": {\"success\": true, \"msg\": \"Success\"}}");
        workOrderWriteCallBackDataDTO.setSuccess(true);
        workOrderWriteCallBackDataDTO.setResult("{\"success\": true, \"msg\": \"Success\"}");
        workOrderWriteCallBackResultDTO.setSuccess(true);

        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDTO.class))).thenReturn(workOrderWriteCallBackDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDataDTO.class))).thenReturn(workOrderWriteCallBackDataDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackResultDTO.class))).thenReturn(workOrderWriteCallBackResultDTO);
        pushStdModelDataService.ticketClosedCallBack(dto1);
        dto1.setSuccess(true);
        assertTrue(dto1.isSuccess());
    }
    @Test
    public void pushFaildData() throws Exception {
        PushStdModelDataDTO dto = new PushStdModelDataDTO();
        pushStdModelDataService.pushFaildData(dto);
        PowerMockito.when(pushStdModelDataRepository.getPushSnNumByTaskNos(anyString()))
                .thenReturn(null);
        dto.setTaskNo("123");
        dto.setTaskQty(12);
        pushStdModelDataService.pushFaildData(dto);
        PushStdDataTaskAndSnNumDTO pushStdDataTaskAndSnNumDTO = new PushStdDataTaskAndSnNumDTO();
        pushStdDataTaskAndSnNumDTO.setSnNum(11);
        PowerMockito.when(pushStdModelDataRepository.getPushSnNumByTaskNos(anyString()))
                .thenReturn(pushStdDataTaskAndSnNumDTO);
        pushStdModelDataService.pushFaildData(dto);
        pushStdDataTaskAndSnNumDTO.setSnNum(12);
        PowerMockito.when(pushStdModelDataRepository.getPushSnNumByTaskNos(anyString()))
                .thenReturn(pushStdDataTaskAndSnNumDTO);
        PowerMockito.mockStatic(DatawbRemoteService.class);
        Mockito.when(DatawbRemoteService.getTaskNoStatusByErp(Mockito.anyList())).thenReturn(null);
        dto.setOrgId(BigDecimal.ONE);
        pushStdModelDataService.pushFaildData(dto);
        assertNotNull(dto);

    }
    @Test
    public void testTicketClosed() throws Exception {
        WipEntityInfoDTO wip = new WipEntityInfoDTO();
        wip.setStatusType("11");
        PowerMockito.mockStatic(DatawbRemoteService.class);
        Mockito.when(DatawbRemoteService.getTaskNoStatusByErp(Mockito.anyList())).thenReturn(null);
        // 模拟数据
        PushStdModelDataDTO dto = new PushStdModelDataDTO();
        dto.setTaskNo("task1");
        dto.setTaskQty(1);
        dto.setOrgId(BigDecimal.ONE);
        PushStdModelDataDTO dto1 = new PushStdModelDataDTO();
        dto1.setTaskNo("");
        dto1.setTaskQty(10);
        PushStdDataTaskAndSnNumDTO snNumDTO = new PushStdDataTaskAndSnNumDTO();
        snNumDTO.setTaskNo("task1");
        snNumDTO.setSnNum(15);
        PushStdDataTaskAndSnNumDTO pushStdDataTaskAndSnNumDTO = new PushStdDataTaskAndSnNumDTO();
        pushStdDataTaskAndSnNumDTO.setTaskNo("123");
        pushStdDataTaskAndSnNumDTO.setCustomerPartType("123");
        pushStdDataTaskAndSnNumDTO.setSnNum(1);
        PushStdDataTaskAndSnNumDTO pushStdDataTaskAndSnNumDTO2 = new PushStdDataTaskAndSnNumDTO();
        pushStdDataTaskAndSnNumDTO2.setTaskNo("123");
        pushStdDataTaskAndSnNumDTO2.setCustomerPartType("123");
        pushStdDataTaskAndSnNumDTO2.setSnNum(0);
        PushStdDataTaskAndSnNumDTO pushStdDataTaskAndSnNumDTO1 = new PushStdDataTaskAndSnNumDTO();
        pushStdDataTaskAndSnNumDTO1.setTaskNo("");
        pushStdDataTaskAndSnNumDTO1.setCustomerPartType("123");
        pushStdDataTaskAndSnNumDTO1.setSnNum(1);
        PowerMockito.when(pushStdModelDataRepository.selectPushFailData(anyString(), any()))
                .thenReturn(null)
                .thenReturn(Collections.singletonList(pushStdDataTaskAndSnNumDTO))
                .thenReturn(Collections.singletonList(pushStdDataTaskAndSnNumDTO1))
        ;
        SysLookupValues sysLookupTypesDTO = new SysLookupValues();
        sysLookupTypesDTO.setLookupMeaning("123");
        when(sysLookupValuesService.findByLookupType(Constant.LOOK_UP_TYPE_ALIBABA)).thenReturn(Collections.singletonList(sysLookupTypesDTO));
        pushStdModelDataService.ticketClosed();
        PowerMockito.when(pushStdModelDataRepository.selectDataByTaskNoLimit(anyString(),anyList(),any()))
                .thenReturn(Collections.singletonList(dto))
                .thenReturn(Collections.singletonList(dto1))
        ;
        PowerMockito.when(pushStdModelDataRepository.selectPushFailData(anyString(), any()))
                .thenReturn(Collections.singletonList(pushStdDataTaskAndSnNumDTO))
                .thenReturn(Collections.singletonList(pushStdDataTaskAndSnNumDTO1))
        ;
        WipEntityInfoDTO wip1 = new WipEntityInfoDTO();
        wip1.setStatusType("12");
        Mockito.when(DatawbRemoteService.getTaskNoStatusByErp(Mockito.anyList())).thenReturn(Collections.singletonList(wip))
                .thenReturn(Collections.singletonList(wip1))
        ;
        pushStdModelDataService.ticketClosed();
        PowerMockito.when(pushStdModelDataRepository.getPushSnNumByTaskNos(anyString()))
                .thenReturn(pushStdDataTaskAndSnNumDTO1);

        pushStdModelDataService.ticketClosed();
        PowerMockito.when(pushStdModelDataRepository.selectDataByTaskNoLimit(any(),anyList(),any()))
                .thenReturn(Collections.singletonList(dto))
                .thenReturn(Collections.singletonList(dto))
                .thenReturn(Collections.singletonList(dto))
                .thenReturn(Collections.singletonList(dto))
                .thenReturn(Collections.singletonList(dto1))
                .thenReturn(Collections.singletonList(dto))
                .thenReturn(null)
        ;
        PowerMockito.when(pushStdModelDataRepository.getPushSnNumByTaskNos(anyString()))
                .thenReturn(pushStdDataTaskAndSnNumDTO)
                .thenReturn(pushStdDataTaskAndSnNumDTO1)
                .thenReturn(null)
                .thenReturn(pushStdDataTaskAndSnNumDTO2)
        ;

        pushStdModelDataService.ticketClosed();
        Mockito.when(DatawbRemoteService.getTaskNoStatusByErp(Mockito.anyList())).thenReturn(Collections.singletonList(wip))
                .thenReturn(Collections.singletonList(wip1))
        ;
        pushStdModelDataService.ticketClosed();
        PowerMockito.when(pushStdModelDataRepository.update(any()))
                .thenThrow(new RuntimeException("131"))
                .thenReturn(1)
                .thenThrow(new RuntimeException("131"))
                .thenReturn(1)
        ;
        pushStdDataTaskAndSnNumDTO.setPushFailCount(0);
        PowerMockito.when(pushStdModelDataRepository.selectPushFailData(anyString(), any()))
                .thenReturn(Collections.singletonList(pushStdDataTaskAndSnNumDTO))
                .thenReturn(Collections.singletonList(pushStdDataTaskAndSnNumDTO1))
        ;
        PowerMockito.when(pushStdModelDataRepository.getPushSnNumByTaskNos(anyString()))
                .thenReturn(null)
        ;
        pushStdModelDataService.ticketClosed();
        pushStdDataTaskAndSnNumDTO.setPushFailCount(-2);
        PowerMockito.when(pushStdModelDataRepository.selectPushFailData(anyString(), any()))
                .thenReturn(Collections.singletonList(pushStdDataTaskAndSnNumDTO))
        ;
        PowerMockito.when(pushStdModelDataRepository.selectPushFailData(anyString(), any()))
                .thenReturn(Collections.singletonList(pushStdDataTaskAndSnNumDTO1))
        ;
        PowerMockito.when(pushStdModelDataRepository.getPushSnNumByTaskNos(anyString()))
                .thenThrow(new RuntimeException("12"))
        ;

        PowerMockito.when(pushStdModelDataRepository.selectDataByTaskNoLimit(anyString(),anyList(),any()))
                .thenReturn(Collections.singletonList(dto));
        Assert.assertThrows(RuntimeException.class,()->pushStdModelDataService.ticketClosed());
    }
    /* Started by AICoder, pid:ee56208f49eda0214be6085820687b3469327886 */
    @Test
    public void testDispatchManufactureOrder_Success() throws Exception {
        pushStdModelDataDTO = new PushStdModelDataDTO();
        pushStdModelDataDTO.setTaskNo("task123");
        pushStdModelDataDTO.setCustomerName("CustomerA");
        pushStdModelDataDTO.setFactoryId(1);

        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setCustomerPartType("TypeA");

        taskExtendedMap = Collections.singletonMap("task123", psTaskExtendedDTO);

        Date scheduleStartDate = new Date();
        Mockito.when(centerFactoryCallSiteService.queryScheduleStartAndEndTime(anyString(), anyString()))
                .thenReturn(Collections.singletonMap("scheduleStartDate", scheduleStartDate));
        PowerMockito.doNothing().when(tradeDataLogService).pushDataOfExceptionRollback(Mockito.anyList());

        Method method = pushStdModelDataService.getClass().getDeclaredMethod("dispatchManufactureOrder",
                        PushStdModelDataDTO.class,  Map.class);
        method.setAccessible(true);

        // 3. 执行并断言
        boolean result = (boolean) method.invoke(pushStdModelDataService,  pushStdModelDataDTO, taskExtendedMap);

        Assert.assertEquals(true, result);
    }

    @Test
    public void testDispatchManufactureOrder_TaskNotFound() throws Exception {
        pushStdModelDataDTO = new PushStdModelDataDTO();
        pushStdModelDataDTO.setTaskNo("task123");
        pushStdModelDataDTO.setCustomerName("CustomerA");
        pushStdModelDataDTO.setFactoryId(1);

        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setCustomerPartType("TypeA");

        taskExtendedMap = Collections.singletonMap("task123", psTaskExtendedDTO);

        Map<String, PsTaskExtendedDTO> emptyTaskMap = Collections.emptyMap();

        Method method = pushStdModelDataService.getClass().getDeclaredMethod("dispatchManufactureOrder",
                PushStdModelDataDTO.class,  Map.class);
        method.setAccessible(true);

        // 3. 执行并断言
        boolean result = (boolean) method.invoke(pushStdModelDataService,  pushStdModelDataDTO, emptyTaskMap);

        Assert.assertEquals(false, result);
    }

    @Test
    public void testDispatchManufactureOrder_ScheduleStartAndEndTimeNull() throws Exception {
        pushStdModelDataDTO = new PushStdModelDataDTO();
        pushStdModelDataDTO.setTaskNo("task123");
        pushStdModelDataDTO.setCustomerName("CustomerA");
        pushStdModelDataDTO.setFactoryId(1);

        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setCustomerPartType("TypeA");

        taskExtendedMap = Collections.singletonMap("task123", psTaskExtendedDTO);

        Mockito.when(centerFactoryCallSiteService.queryScheduleStartAndEndTime(anyString(), anyString()))
                .thenReturn(Collections.singletonMap("scheduleStartDate", null));
        PowerMockito.doNothing().when(tradeDataLogService).pushDataOfExceptionRollback(Mockito.anyList());

        Method method = pushStdModelDataService.getClass().getDeclaredMethod("dispatchManufactureOrder",
                PushStdModelDataDTO.class,  Map.class);
        method.setAccessible(true);

        // 3. 执行并断言
        boolean result = (boolean) method.invoke(pushStdModelDataService,  pushStdModelDataDTO, taskExtendedMap);

        Assert.assertEquals(true, result);
    }

    @Test
    public void testDispatchManufactureOrder_Exception() throws Exception {
        pushStdModelDataDTO = new PushStdModelDataDTO();
        pushStdModelDataDTO.setTaskNo("task123");
        pushStdModelDataDTO.setCustomerName("CustomerA");
        pushStdModelDataDTO.setFactoryId(1);

        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setCustomerPartType("TypeA");

        taskExtendedMap = Collections.singletonMap("task123", psTaskExtendedDTO);

        Mockito.when(centerFactoryCallSiteService.queryScheduleStartAndEndTime(anyString(), anyString()))
                .thenThrow(new RuntimeException("Test Exception"));
        doNothing().when(noticeCenterService).sendEmail(anyString(), anyString(), anyMap());

        Method method = pushStdModelDataService.getClass().getDeclaredMethod("dispatchManufactureOrder",
                PushStdModelDataDTO.class,  Map.class);
        method.setAccessible(true);

        // 3. 执行并断言
        try {
            method.invoke(pushStdModelDataService,  pushStdModelDataDTO, taskExtendedMap);
        } catch (Exception e) {
        }
        assertTrue(true);
    }
    /* Ended by AICoder, pid:ee56208f49eda0214be6085820687b3469327886 */

    /* Started by AICoder, pid:g1ffb05091s967d14f1c09e5e083f568f290ce98 */
    @Test
    public void testDispatchManufactureOrderCallBack_Success() {
        PowerMockito.mockStatic(JSON.class);
        B2bCallBackNewDTO dto1 = new B2bCallBackNewDTO();
        dto1.setCode("0000");
        dto1.setSuccess(true);
        dto1.setKeywords("task123");
        dto1.setData("{\"messageId\":\"task123\", \"success\":true}");

        WorkOrderWriteCallBackDTO workOrderWriteCallBackDTO = new WorkOrderWriteCallBackDTO();
        workOrderWriteCallBackDTO.setData("{\"success\": true, \"result\": {\"success\": true, \"msg\": \"Success\"}}");
        WorkOrderWriteCallBackDataDTO workOrderWriteCallBackDataDTO = new WorkOrderWriteCallBackDataDTO();
        workOrderWriteCallBackDataDTO.setSuccess(true);
        workOrderWriteCallBackDataDTO.setResult("{\"success\": true, \"msg\": \"Success\"}");
        WorkOrderWriteCallBackResultDTO workOrderWriteCallBackResultDTO = new WorkOrderWriteCallBackResultDTO();
        workOrderWriteCallBackResultDTO.setSuccess(true);

        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDTO.class))).thenReturn(workOrderWriteCallBackDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDataDTO.class))).thenReturn(workOrderWriteCallBackDataDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackResultDTO.class))).thenReturn(workOrderWriteCallBackResultDTO);

        pushStdModelDataService.dispatchManufactureOrderCallBack(dto1);

        verify(pushStdModelDataRepository, times(1)).update(any(PushStdModelDataDTO.class));
    }

    @Test
    public void testDispatchManufactureOrderCallBack_Failure() {
        B2bCallBackNewDTO dto1 = new B2bCallBackNewDTO();
        dto1.setCode("0000");
        dto1.setSuccess(true);
        dto1.setData("{\"messageId\":\"task123\", \"success\":true}");

        B2bCallBackNewDTO dto2 = new B2bCallBackNewDTO();
        dto2.setKeywords("task456");
        dto2.setCode("0001");
        dto1.setSuccess(false);
        dto2.setData("{\"messageId\":\"task456\", \"success\":false}");
        dataList = Arrays.asList(dto1, dto2);

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("1");
        when(sysLookupValuesService.findByLookupCode(Constant.LOOK_UP_VALUE_7599002)).thenReturn(sysLookupValues);

        dataList.forEach(d -> pushStdModelDataService.dispatchManufactureOrderCallBack(d));

        verify(pushStdModelDataRepository, times(1)).update(any(PushStdModelDataDTO.class));
    }

    @Test
    public void testDispatchManufactureOrderCallBack_NoCustomerDataLog() {
        B2bCallBackNewDTO dto1 = new B2bCallBackNewDTO();
        dto1.setCode("0000");
        dto1.setSuccess(true);
        dto1.setData("{\"messageId\":\"task123\", \"success\":true}");

        pushStdModelDataService.dispatchManufactureOrderCallBack(dto1);

        verify(pushStdModelDataRepository, never()).update(any(PushStdModelDataDTO.class));
    }

    @Test
    public void testDispatchManufactureOrderCallBack_DataFalse() {
        PowerMockito.mockStatic(JSON.class);
        B2bCallBackNewDTO dto1 = new B2bCallBackNewDTO();
        dto1.setKeywords("task123");
        dto1.setCode("0001");
        dto1.setSuccess(true);
        dto1.setData("{\"messageId\":\"task123\", \"success\":true}");

        when(sysLookupValuesService.findByLookupCode(Constant.LOOK_UP_VALUE_7599002)).thenReturn(null);

        WorkOrderWriteCallBackDTO workOrderWriteCallBackDTO = new WorkOrderWriteCallBackDTO();
        workOrderWriteCallBackDTO.setData("{\"success\": true, \"result\": {\"success\": true, \"msg\": \"Success\"}}");
        WorkOrderWriteCallBackDataDTO workOrderWriteCallBackDataDTO = new WorkOrderWriteCallBackDataDTO();
        workOrderWriteCallBackDataDTO.setSuccess(false);

        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDTO.class))).thenReturn(workOrderWriteCallBackDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDataDTO.class))).thenReturn(workOrderWriteCallBackDataDTO);

        Exception exception = assertThrows(MesBusinessException.class, () -> {
            pushStdModelDataService.dispatchManufactureOrderCallBack(dto1);
        });

        assertEquals(RetCode.BUSINESSERROR_CODE, ((MesBusinessException) exception).getExCode());
    }

    @Test
    public void testDispatchManufactureOrderCallBack_ResultFalse() {
        PowerMockito.mockStatic(JSON.class);
        B2bCallBackNewDTO dto1 = new B2bCallBackNewDTO();
        dto1.setKeywords("task123");
        dto1.setCode("0001");
        dto1.setSuccess(true);
        dto1.setData("{\"messageId\":\"task123\", \"success\":true}");

        when(sysLookupValuesService.findByLookupCode(Constant.LOOK_UP_VALUE_7599002)).thenReturn(new SysLookupValues());
        WorkOrderWriteCallBackDTO workOrderWriteCallBackDTO = new WorkOrderWriteCallBackDTO();
        workOrderWriteCallBackDTO.setData("{\"success\": true, \"result\": {\"success\": true, \"msg\": \"Success\"}}");
        WorkOrderWriteCallBackDataDTO workOrderWriteCallBackDataDTO = new WorkOrderWriteCallBackDataDTO();
        workOrderWriteCallBackDataDTO.setSuccess(true);
        workOrderWriteCallBackDataDTO.setResult("{\"success\": true, \"msg\": \"Success\"}");
        WorkOrderWriteCallBackResultDTO workOrderWriteCallBackResultDTO = new WorkOrderWriteCallBackResultDTO();
        workOrderWriteCallBackResultDTO.setSuccess(false);

        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDTO.class))).thenReturn(workOrderWriteCallBackDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackDataDTO.class))).thenReturn(workOrderWriteCallBackDataDTO);
        when(JSON.parseObject(anyString(), eq(WorkOrderWriteCallBackResultDTO.class))).thenReturn(workOrderWriteCallBackResultDTO);

        Exception exception = assertThrows(MesBusinessException.class, () -> {
            pushStdModelDataService.dispatchManufactureOrderCallBack(dto1);
        });

        assertEquals(RetCode.BUSINESSERROR_CODE, ((MesBusinessException) exception).getExCode());
    }
    /* Ended by AICoder, pid:g1ffb05091s967d14f1c09e5e083f568f290ce98 */

    /* Started by AICoder, pid:f40b0b69ebvc738140e009bab1d2ec001cc82d3c */
    @Test
    public void testPushSchedulingInfo_ConfirmationBranch_Success() throws Exception {
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PushStdModelDataServiceImpl spyService = PowerMockito.spy(pushStdModelDataService);
        // 1. 模拟静态工具类
        PowerMockito.when(JacksonJsonConverUtil.beanToJson(any(ManufactureOrderDTO.class))).thenReturn("{\"status\":\"SUCCESS\"}");

        List<PushStdModelConfirmationDTO> list = Lists.newArrayList();
        PushStdModelConfirmationDTO confirmationDTO1 = new PushStdModelConfirmationDTO();
        confirmationDTO1.setTaskNo("TASK001");
        confirmationDTO1.setItemNo("ITEM001");
        confirmationDTO1.setFactoryId(1);
        confirmationDTO1.setPushFailCount(0);
        confirmationDTO1.setConfirmationType(2);
        confirmationDTO1.setTaskQty(2);

        PushStdModelConfirmationDTO confirmationDTO2 = new PushStdModelConfirmationDTO();
        confirmationDTO2.setTaskNo("TASK002");
        confirmationDTO2.setItemNo("ITEM001");
        confirmationDTO2.setFactoryId(1);
        confirmationDTO2.setPushFailCount(0);
        confirmationDTO2.setConfirmationType(1);
        confirmationDTO2.setTaskQty(2);
        list.add(confirmationDTO1);
        list.add(confirmationDTO2);

        PsTaskExtendedDTO taskExtended = new PsTaskExtendedDTO();
        taskExtended.setCustomerNo("CUST_001");
        taskExtended.setTaskNo("taskNo");

        taskExtendedMap = new HashMap<>();
        taskExtendedMap.put("TASK001", taskExtended);
        taskExtendedMap.put("TASK002", taskExtended);

        ReflectionTestUtils.setField(spyService, "factoryIdToNameMap", new HashMap<>());

        // 2. 设置 handleConfirmationStdModel 返回 true
        PowerMockito.doReturn(true).when(spyService, "validAndPackPushData", any(PushStdModelDTO.class),
                any(PsTaskExtendedDTO.class),
                isNull(SceneCodeEnum.class),
                any(ManufactureOrderDTO.class));

        PushStdModelDataDTO pushStdModelDataDTO = new PushStdModelDataDTO();
        pushStdModelDataDTO.setCurrProcess("20");
        when(pushStdModelDataRepository.getDataByTaskNo("TASK001")).thenReturn(pushStdModelDataDTO);

        PushStdModelSnDataDTO pushStdModelSnDataDTO1 = new PushStdModelSnDataDTO();
        pushStdModelSnDataDTO1.setCurrProcess("50");
        PushStdModelSnDataDTO pushStdModelSnDataDTO2 = new PushStdModelSnDataDTO();
        pushStdModelSnDataDTO2.setCurrProcess("40");
        pushStdModelSnDataDTO2.setPushStatus(2);
        when(pushStdModelSnDataRepository.selectByQuery(any())).thenReturn(Arrays.asList(pushStdModelSnDataDTO1, pushStdModelSnDataDTO2));
        when(pushStdModelDataRepository.getDataByTaskNo(any())).thenReturn(pushStdModelDataDTO);
        ReflectionTestUtils.setField(spyService, "alarmTitle", "%s:任务对应厂商自供料预计齐套日期或全部物料预计齐套日期在APS不存在");
        boolean result = false;

        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        PowerMockito.when(redisOpsValue.get(any())).thenReturn(null);
        PowerMockito.when(redisOpsValue.setIfAbsent(Mockito.anyString(), Mockito.any(), Mockito.anyLong(),
                        Mockito.anyObject())).thenReturn(true);
        // 3. 执行测试方法
        for (PushStdModelConfirmationDTO confirmationDTO : list) {
            result = spyService.pushSchedulingInfo(confirmationDTO, taskExtendedMap);
        }

        // 4. 验证结果
        Assert.assertFalse(result);
    }

    @Test
    public void testPushSchedulingInfo_ConfirmationBranch_NoData() throws Exception {
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PushStdModelDataServiceImpl spyService = PowerMockito.spy(pushStdModelDataService);
        // 1. 模拟静态工具类
        PowerMockito.when(JacksonJsonConverUtil.beanToJson(any(ManufactureOrderDTO.class))).thenReturn("{\"status\":\"SUCCESS\"}");
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        PowerMockito.when(redisOpsValue.get(any())).thenReturn(null);
        PowerMockito.when(redisOpsValue.setIfAbsent(Mockito.anyString(), Mockito.any(), Mockito.anyLong(),
                Mockito.anyObject())).thenReturn(true);
        List<PushStdModelConfirmationDTO> list = Lists.newArrayList();
        PushStdModelConfirmationDTO confirmationDTO1 = new PushStdModelConfirmationDTO();
        confirmationDTO1.setTaskNo("TASK001");
        confirmationDTO1.setItemNo("ITEM001");
        confirmationDTO1.setFactoryId(1);
        confirmationDTO1.setPushFailCount(0);
        confirmationDTO1.setConfirmationType(2);
        confirmationDTO1.setTaskQty(2);

        PushStdModelConfirmationDTO confirmationDTO2 = new PushStdModelConfirmationDTO();
        confirmationDTO2.setTaskNo("TASK002");
        confirmationDTO2.setItemNo("ITEM001");
        confirmationDTO2.setFactoryId(1);
        confirmationDTO2.setPushFailCount(0);
        confirmationDTO2.setConfirmationType(1);
        confirmationDTO2.setTaskQty(2);
        list.add(confirmationDTO1);
        list.add(confirmationDTO2);

        PsTaskExtendedDTO taskExtended = new PsTaskExtendedDTO();
        taskExtended.setCustomerNo("CUST_001");

        taskExtendedMap = new HashMap<>();
        taskExtendedMap.put("TASK001", taskExtended);
        taskExtendedMap.put("TASK002", taskExtended);

        PushStdModelSnDataDTO pushStdModelSnDataDTO1 = new PushStdModelSnDataDTO();
        pushStdModelSnDataDTO1.setCurrProcess("20");
        PushStdModelSnDataDTO pushStdModelSnDataDTO2 = new PushStdModelSnDataDTO();
        pushStdModelSnDataDTO2.setCurrProcess("40");
        pushStdModelSnDataDTO2.setPushStatus(9);
        when(pushStdModelSnDataRepository.selectByQuery(any())).thenReturn(Arrays.asList(pushStdModelSnDataDTO1, pushStdModelSnDataDTO2));

        boolean result = false;
        // 3. 执行测试方法
        for (PushStdModelConfirmationDTO confirmationDTO : list) {
            result = spyService.pushSchedulingInfo(confirmationDTO, taskExtendedMap);
        }

        // 4. 验证结果
        Assert.assertFalse(result);
    }

    @Test
    public void testPushSchedulingInfo_ConfirmationBranch_NoPush() throws Exception {
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        PowerMockito.when(redisOpsValue.get(any())).thenReturn(null);
        PowerMockito.when(redisOpsValue.setIfAbsent(Mockito.anyString(), Mockito.any(), Mockito.anyLong(),
                Mockito.anyObject())).thenReturn(true);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PushStdModelDataServiceImpl spyService = PowerMockito.spy(pushStdModelDataService);
        // 1. 模拟静态工具类
        PowerMockito.when(JacksonJsonConverUtil.beanToJson(any(ManufactureOrderDTO.class))).thenReturn("{\"status\":\"SUCCESS\"}");

        List<PushStdModelConfirmationDTO> list = Lists.newArrayList();
        PushStdModelConfirmationDTO confirmationDTO1 = new PushStdModelConfirmationDTO();
        confirmationDTO1.setTaskNo("TASK001");
        confirmationDTO1.setItemNo("ITEM001");
        confirmationDTO1.setFactoryId(1);
        confirmationDTO1.setPushFailCount(0);
        confirmationDTO1.setConfirmationType(2);
        confirmationDTO1.setTaskQty(1);

        PushStdModelConfirmationDTO confirmationDTO2 = new PushStdModelConfirmationDTO();
        confirmationDTO2.setTaskNo("TASK002");
        confirmationDTO2.setItemNo("ITEM001");
        confirmationDTO2.setFactoryId(1);
        confirmationDTO2.setPushFailCount(0);
        confirmationDTO2.setConfirmationType(1);
        confirmationDTO2.setTaskQty(1);
        list.add(confirmationDTO1);
        list.add(confirmationDTO2);

        PsTaskExtendedDTO taskExtended = new PsTaskExtendedDTO();
        taskExtended.setCustomerNo("CUST_001");

        taskExtendedMap = new HashMap<>();
        taskExtendedMap.put("TASK001", taskExtended);
        taskExtendedMap.put("TASK002", taskExtended);

        PushStdModelDataDTO pushStdModelDataDTO = new PushStdModelDataDTO();
        pushStdModelDataDTO.setCurrProcess("10");
        when(pushStdModelDataRepository.getDataByTaskNo("TASK001")).thenReturn(pushStdModelDataDTO);
        boolean result = false;
        // 3. 执行测试方法
        for (PushStdModelConfirmationDTO confirmationDTO : list) {
            result = spyService.pushSchedulingInfo(confirmationDTO, taskExtendedMap);
        }

        // 4. 验证结果
        Assert.assertFalse(result);
    }

    @Test
    public void setPreparedTime() throws Exception{
        ManufactureOrderDTO manufactureOrderDTO = new ManufactureOrderDTO();
        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setTaskNo("taskNo");
        Whitebox.invokeMethod(pushStdModelDataService, "setPreparedTime", manufactureOrderDTO, psTaskExtendedDTO);

        Page<PsTaskExtendedDTO> page = new Page<>();
        PowerMockito.when(apsInOneClient.customerTaskQuery(Mockito.any(TasksQueryDTO.class))).thenReturn(page);
        Whitebox.invokeMethod(pushStdModelDataService, "setPreparedTime", manufactureOrderDTO, psTaskExtendedDTO);

        PsTaskExtendedDTO psTaskExt = new PsTaskExtendedDTO();
        page.setRows(Arrays.asList(psTaskExt));
        Whitebox.invokeMethod(pushStdModelDataService, "setPreparedTime", manufactureOrderDTO, psTaskExtendedDTO);

        psTaskExt.setSelfSupplyPrepareDate(new Date());
        psTaskExt.setFullPrepareDate(null);
        Whitebox.invokeMethod(pushStdModelDataService, "setPreparedTime", manufactureOrderDTO, psTaskExtendedDTO);

        psTaskExt.setSelfSupplyPrepareDate(null);
        psTaskExt.setFullPrepareDate(new Date());
        Whitebox.invokeMethod(pushStdModelDataService, "setPreparedTime", manufactureOrderDTO, psTaskExtendedDTO);

        psTaskExt.setSelfSupplyPrepareDate(new Date());
        psTaskExt.setFullPrepareDate(new Date());
        Whitebox.invokeMethod(pushStdModelDataService, "setPreparedTime", manufactureOrderDTO, psTaskExtendedDTO);

        psTaskExtendedDTO.setSelfSupplyPrepareDate(new Date());
        psTaskExtendedDTO.setFullPrepareDate(null);
        Whitebox.invokeMethod(pushStdModelDataService, "setPreparedTime", manufactureOrderDTO, psTaskExtendedDTO);

        psTaskExtendedDTO.setSelfSupplyPrepareDate(null);
        psTaskExtendedDTO.setFullPrepareDate(new Date());
        Whitebox.invokeMethod(pushStdModelDataService, "setPreparedTime", manufactureOrderDTO, psTaskExtendedDTO);

        psTaskExtendedDTO.setSelfSupplyPrepareDate(new Date());
        psTaskExtendedDTO.setFullPrepareDate(new Date());
        Whitebox.invokeMethod(pushStdModelDataService, "setPreparedTime", manufactureOrderDTO, psTaskExtendedDTO);
        assertTrue(true);
    }

    @Test
    public void testValidAndPackPushData_ConfirmationTypeNot2_ValidationPass() throws Exception {
        PushStdModelConfirmationDTO confirmationDTO = new PushStdModelConfirmationDTO();
        confirmationDTO.setTaskNo("TASK001");
        confirmationDTO.setItemNo("ITEM001");
        confirmationDTO.setFactoryId(1);
        confirmationDTO.setTaskQty(100);
        confirmationDTO.setPushFailCount(0);
        confirmationDTO.setConfirmationType(2);

        PsTaskExtendedDTO taskExtendedDTO = new PsTaskExtendedDTO();
        taskExtendedDTO.setTaskType("1");
        taskExtendedDTO.setFixBomId("FIX_BOM_001");
        taskExtendedDTO.setSelfSupplyPrepareDate(new Date());
        taskExtendedDTO.setFullPrepareDate(new Date());
        taskExtendedDTO.setEntityClass("STD_UNIT_2");

        ManufactureOrderDTO manufactureOrderDTO = new ManufactureOrderDTO();

        ReflectionTestUtils.setField(pushStdModelDataService, "factoryIdToNameMap", new HashMap<>());

        ValidatorFactory mockValidatorFactory = mock(ValidatorFactory.class);
        when(mockValidatorFactory.getValidator()).thenReturn(mockValidator);
        PowerMockito.mockStatic(Validation.class);
        when(Validation.buildDefaultValidatorFactory()).thenReturn(mockValidatorFactory);

        // 2. 模拟参数校验通过
        Set<ConstraintViolation<ManufactureOrderDTO>> emptyErrors = Collections.emptySet();
        when(mockValidatorFactory.getValidator().validate(eq(manufactureOrderDTO), any(Class.class))).thenReturn(emptyErrors);

        // 3. 调用私有方法
        boolean result = Whitebox.invokeMethod(pushStdModelDataService, "validAndPackPushData",
                confirmationDTO, taskExtendedDTO, SceneCodeEnum.BUFFER_TOTALLY_BIND, manufactureOrderDTO);

        // 4. 验证结果
        Assert.assertFalse(result);
    }

    @Test
    public void setMaterialBillList_shouldHandleEmptyFixBomDetailList() throws Exception {
        // Arrange
        ManufactureOrderDTO orderDTO = new ManufactureOrderDTO();
        PsTaskExtendedDTO taskDTO = new PsTaskExtendedDTO();
        taskDTO.setFixBomId("bom123");
        taskDTO.setFixBomHeadId("bom123");

        when(fixBomCommonService.queryFixBomDetailByHeadId("bom123"))
                .thenReturn(Collections.emptyList());

        // Act
        Whitebox.invokeMethod(pushStdModelDataService, "setMaterialBillList", orderDTO, taskDTO);


        // Assert
        assertTrue(orderDTO.getMaterialBillList().isEmpty());
        verify(fixBomCommonService).queryFixBomDetailByHeadId("bom123");
        verifyNoInteractions(iscpRemoteService);
    }

    @Test
    public void setMaterialBillList_shouldFilterOutItemsWithBlankItemSeqOrNonNumericLevel() throws Exception {
        // Arrange
        ManufactureOrderDTO orderDTO = new ManufactureOrderDTO();
        PsTaskExtendedDTO taskDTO = new PsTaskExtendedDTO();
        taskDTO.setFixBomId("bom123");
        taskDTO.setFixBomHeadId("bom123");

        List<FixBomDetailDTO> mockDetails = Arrays.asList(
                createFixBomDetail("1", "1", "Y", "item1"), // valid
                createFixBomDetail("", "1", "Y", "item2"),  // invalid - blank seq
                createFixBomDetail("3", "A", "Y", "item3"), // invalid - non-numeric level
                createFixBomDetail("4", "2", "N", "item4")  // valid but may be filtered later
        );

        when(fixBomCommonService.queryFixBomDetailByHeadId("bom123"))
                .thenReturn(mockDetails);

        // Act
                Whitebox.invokeMethod(pushStdModelDataService, "setMaterialBillList", orderDTO, taskDTO);


        // Assert
        // The method should proceed beyond the empty check since we have at least one valid item
        verify(iscpRemoteService).queryAliControlInfoList(anyList());
    }

    @Test
    public void setMaterialBillList_shouldFilterTopLevelAndRequiredItems() throws Exception {
        // Arrange
        ManufactureOrderDTO orderDTO = new ManufactureOrderDTO();
        PsTaskExtendedDTO taskDTO = new PsTaskExtendedDTO();
        taskDTO.setFixBomId("bom123");

        List<FixBomDetailDTO> mockDetails = Arrays.asList(
                createFixBomDetail("1", "1", "Y", "item1"), // top level - should be included
                createFixBomDetail("2", "2", "Y", "item2"),  // required - should be included
                createFixBomDetail("3", "2", "N", "item3"),  // not required, not top - should be excluded
                createFixBomDetail("4", "1", "N", "item4")  // top level - should be included
        );

        when(fixBomCommonService.queryFixBomDetailByFixBomId("bom123"))
                .thenReturn(mockDetails);

        // Mock control info to return empty list (no controlled items)
        when(iscpRemoteService.queryAliControlInfoList(anyList()))
                .thenReturn(Collections.emptyList());

        // Act
                Whitebox.invokeMethod(pushStdModelDataService, "setMaterialBillList", orderDTO, taskDTO);


        // Assert
        // Verify that the method processes only the filtered items (3 should be excluded)
        // Additional assertions would depend on your packAndValidData implementation
        assertNotNull(orderDTO.getMaterialBillList());
    }

    @Test
    public void setMaterialBillList_shouldHandleAliControlItems() throws Exception {
        // Arrange
        ManufactureOrderDTO orderDTO = new ManufactureOrderDTO();
        PsTaskExtendedDTO taskDTO = new PsTaskExtendedDTO();
        taskDTO.setFixBomId("bom123");

        List<FixBomDetailDTO> mockDetails = Arrays.asList(
                createFixBomDetail("1", "1", "Y", "controlledItem"), // controlled item
                createFixBomDetail("2", "1", "Y", "normalItem")      // normal item
        );
        CivControlInfoDTO civControlInfoDTO = new CivControlInfoDTO();
        civControlInfoDTO.setItemNo("controlledItem");
        civControlInfoDTO.setControl( 1);
        List<CivControlInfoDTO> controlInfo = Arrays.asList(
                civControlInfoDTO
        );

        when(fixBomCommonService.queryFixBomDetailByFixBomId("bom123"))
                .thenReturn(mockDetails);
        when(iscpRemoteService.queryAliControlInfoList(anyList()))
                .thenReturn(controlInfo);

        // Act
                Whitebox.invokeMethod(pushStdModelDataService, "setMaterialBillList", orderDTO, taskDTO);


        // Assert
        // Verify controlled items are properly handled in packAndValidData
        // Would need to verify the behavior based on your implementation
        assertNotNull(orderDTO.getMaterialBillList());
    }

    @Test
    public void setMaterialBillList_shouldHandlePackAndValidDataFailures() throws Exception {
        // Arrange
        ManufactureOrderDTO orderDTO = new ManufactureOrderDTO();
        PsTaskExtendedDTO taskDTO = new PsTaskExtendedDTO();
        taskDTO.setFixBomId("bom123");

        List<FixBomDetailDTO> mockDetails = Arrays.asList(
                createFixBomDetail("1", "1", "Y", "item1") // valid item
        );

        when(fixBomCommonService.queryFixBomDetailByFixBomId("bom123"))
                .thenReturn(mockDetails);
        when(iscpRemoteService.queryAliControlInfoList(anyList()))
                .thenReturn(Collections.emptyList());

        // Mock packAndValidData to not add to the list (simulating validation failure)

        // Act
                Whitebox.invokeMethod(pushStdModelDataService, "setMaterialBillList", orderDTO, taskDTO);


        // Assert
        assertTrue(orderDTO.getMaterialBillList().isEmpty());
    }

    @Test
    public void setMaterialBillList_shouldBuildMaterialBillHierarchy() throws Exception {
        // Arrange
        ManufactureOrderDTO orderDTO = new ManufactureOrderDTO();
        PsTaskExtendedDTO taskDTO = new PsTaskExtendedDTO();
        taskDTO.setFixBomId("bom123");

        List<FixBomDetailDTO> mockDetails = Arrays.asList(
                createFixBomDetail("1", "1", "Y", "item1"), // top level
                createFixBomDetail("2", "2", "Y", "item2")  // child level
        );

        when(fixBomCommonService.queryFixBomDetailByFixBomId("bom123"))
                .thenReturn(mockDetails);
        when(iscpRemoteService.queryAliControlInfoList(anyList()))
                .thenReturn(Collections.emptyList());

        // Act
                Whitebox.invokeMethod(pushStdModelDataService, "setMaterialBillList", orderDTO, taskDTO);


        // Assert
        // Verify the hierarchy is built correctly
        // This would depend on your getMaterialBillList implementation
        assertNotNull(orderDTO.getMaterialBillList());
    }

    public  FixBomDetailDTO createFixBomDetail(String itemSeq, String itemLevel, String fixBomRequired, String zteCode) {
        FixBomDetailDTO dto = new FixBomDetailDTO();
        dto.setItemSeq(itemSeq);
        dto.setItemLevel(itemLevel);
        dto.setFixBomRequired(fixBomRequired);
        dto.setZteCode(zteCode);
        return dto;
    }
    /* Ended by AICoder, pid:f40b0b69ebvc738140e009bab1d2ec001cc82d3c */

    /* Started by AICoder, pid:e117270751e5ca21494008a22092e85dd1b02a38 */
    @Test
    public void testPushSchedulingInfo_whenNullInput_returnsFalse() throws Exception {
        // Arrange
        // Act
        boolean result = pushStdModelDataService.pushSchedulingInfo(null, null, null);

        // Assert
        assertFalse(result);
    }

    @Test
    public void testPushSchedulingInfo_whenValidInput_returnsTrue() throws Exception {
        // Arrange
        PushStdModelDTO pushStdModelDTO = new PushStdModelDTO();
        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        SceneCodeEnum sceneCodeEnum = SceneCodeEnum.FORMAL_SCEDULE;
        pushStdModelDataService = PowerMockito.spy(pushStdModelDataService);
        PowerMockito.doReturn(true).when(pushStdModelDataService, "validAndPackPushData", any(), any(), any(), any());

        // Act
        boolean result = pushStdModelDataService.pushSchedulingInfo(pushStdModelDTO, psTaskExtendedDTO, sceneCodeEnum);

        // Assert
        assertTrue(result);
    }

    @Test
    public void testPushSchedulingInfo_whenValidInputButInvalidData_returnsFalse() throws Exception {
        // Arrange
        PushStdModelDTO pushStdModelDTO = new PushStdModelDTO();
        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        SceneCodeEnum sceneCodeEnum = SceneCodeEnum.FORMAL_SCEDULE;
        pushStdModelDataService = PowerMockito.spy(pushStdModelDataService);
        PowerMockito.doReturn(false).when(pushStdModelDataService, "validAndPackPushData", any(), any(), any(), any());

        // Act
        boolean result = pushStdModelDataService.pushSchedulingInfo(pushStdModelDTO, psTaskExtendedDTO, sceneCodeEnum);

        // Assert
        assertFalse(result);
    }
    /* Ended by AICoder, pid:e117270751e5ca21494008a22092e85dd1b02a38 */
}

