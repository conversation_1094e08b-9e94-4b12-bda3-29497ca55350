package com.zte.application.impl;

import com.zte.application.CustomerDataLogService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.PushDataToB2B;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.itp.msa.util.encrypt.secure.SecureEncryptorUtils;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertNotNull;

/**
 * ClassName: PushDataToB2BTest
 * Description:
 *
 * <AUTHOR>
 * @date 2023/5/12 上午9:35
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class,SecureEncryptorUtils.class})
public class PushDataToB2BTest extends BaseTestCase {
    @InjectMocks
    private PushDataToB2B pushDataToB2B;

    @Mock
    CustomerDataLogService customerDataLogService;
    @Test
    public void init() throws Exception {
        PowerMockito.mockStatic(SecureEncryptorUtils.class);
        assertNotNull(pushDataToB2B);
    }
    @Test
    public void getUrlTest() {
        Assert.assertNull(pushDataToB2B.getUrl());
    }

    @Test
    public void getHeadParamsTest() throws Exception {
        Assert.assertNotNull(pushDataToB2B.getHeadParams());
    }

    @Test
    public void getRequestBodyTest() throws Exception {
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        Assert.assertNotNull(pushDataToB2B.getRequestBody(dto));
    }

    @Test
    public void handleExceptionTest() throws Exception {
        Throwable e = new Exception();
        Object data = new CustomerDataLogDTO();
        pushDataToB2B.handleException(e, null);
        pushDataToB2B.handleException(e, data);
        Throwable e1 = new MesBusinessException(RetCode.VALIDATIONERROR_CODE, MessageId.PARAM_IS_NULL);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.anyString())).thenReturn("test");
        pushDataToB2B.handleException(e1, data);
        Assert.assertNotNull(e);
        Assert.assertNotNull(data);
    }

    @Test
    public void beforePushDataTest() throws Exception {
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        pushDataToB2B.beforePushData(dto);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void beforePushAllDataTest() throws Exception {
        CustomerDataLogDTO dto = new CustomerDataLogDTO();
        List<Object> dataList = new ArrayList<>();
        dataList.add(dto);
        pushDataToB2B.beforePushAllData(dataList);
        Assert.assertNotNull(dataList);
    }

    @Test
    public void handleResponseTest() throws Exception {
        try {
            pushDataToB2B.handleResponse(null);
        } catch (MesBusinessException e) {
            Assert.assertTrue(com.zte.domain.model.MessageId.CUSTOMIZE_MSG.equals(e.getExMsgId()));
        }

    }
}
