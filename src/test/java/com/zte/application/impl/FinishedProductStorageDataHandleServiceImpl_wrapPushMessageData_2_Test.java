/*Started by AICoder, pid:kfa910bffbx367b140a309d951152356c967dc2d*/
package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.application.PushStdModelSnDataService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.infrastructure.feign.ApsInOneClient;
import com.zte.interfaces.dto.ApsCustInstructBomDTO;
import com.zte.interfaces.dto.ApsCustomerTaskDTO;
import com.zte.interfaces.dto.FinishedProductStorageCargoTransportDTO;
import com.zte.interfaces.dto.FinishedProductStorageDTO;
import com.zte.interfaces.dto.FinishedProductStorageItemDTO;
import com.zte.interfaces.dto.PushStdModelSnDataExtDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.IdGenerator;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class FinishedProductStorageDataHandleServiceImpl_wrapPushMessageData_2_Test {

    @Mock
    private ApsInOneClient apsInOneClient;

    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private IdGenerator idGenerator;

    @InjectMocks
    private FinishedProductStorageDataHandleServiceImpl service;

    @Mock
    PushStdModelSnDataService pushStdModelSnDataService;

    @Before
    public void setUp() throws Exception {
        Mockito.reset(apsInOneClient, sysLookupValuesRepository);
        PowerMockito.field(FinishedProductStorageDataHandleServiceImpl.class, "needCustomerComponentTypeList").set(service,
                Arrays.asList("server.ConfigModel"));
        PowerMockito.when(pushStdModelSnDataService.getFinishedProductStorageCargoTransportDTO(anyList(),anyList(),any(),any())).thenReturn(new FinishedProductStorageCargoTransportDTO());
    }

    @Test
    public void testWrapPushMessageData() {
        // Given
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        pushStdModelSnDataExt.setTaskNo("task1");
        pushStdModelSnDataExt.setStockOrgId(123);
        pushStdModelSnDataExt.setSn("sn1");
        pushStdModelSnDataExt.setTaskBillNo("bill1");
        pushStdModelSnDataExt.setTaskCustomerPartType("partType1");
        pushStdModelSnDataExt.setTaskCustomerItemName("itemName1");

        List<WipExtendIdentificationDTO> wipExtendIdentifications = Collections.emptyList();
        List<FixBomDetailDTO> fixBomDetails = Collections.singletonList(new FixBomDetailDTO());

        ApsCustInstructBomDTO apsCustInstructBom = new ApsCustInstructBomDTO();
        apsCustInstructBom.setMaterialBillType("NEW_INSTRUCT_BOM");
        apsCustInstructBom.setMaterialSign("materialSign1");

        ApsCustomerTaskDTO apsCustomerTaskDTO = new ApsCustomerTaskDTO();
        Page<ApsCustomerTaskDTO> page = new Page<>();
        page.setRows(Arrays.asList(apsCustomerTaskDTO));
        apsCustomerTaskDTO.setBomList(Collections.singletonList(apsCustInstructBom));
        when(apsInOneClient.customerTaskQueryWithDetail(any())).thenReturn(page);

        // When
        FinishedProductStorageDTO result = service.wrapPushMessageData(pushStdModelSnDataExt, wipExtendIdentifications, fixBomDetails);

        // Then
        assertNotNull(result);
        assertEquals("task1", result.getManufactureOrderNo());
        assertEquals("partType1", result.getCategory());
        assertEquals(1, result.getQuantityCompleted());
        assertEquals(1, result.getMaterialBillList().size());
        assertEquals(null, result.getMaterialBillList().get(0).getSnNo());
        assertEquals("materialSign1", result.getMaterialBillList().get(0).getMaterialSign());
    }

    @Test
    public void testWrapPushMessageDataWithSubStock() {
        // Given
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        pushStdModelSnDataExt.setTaskNo("task1");
        pushStdModelSnDataExt.setStockOrgId(123);
        pushStdModelSnDataExt.setSn("sn1");
        pushStdModelSnDataExt.setTaskBillNo("bill1");
        pushStdModelSnDataExt.setTaskCustomerPartType("partType1");
        pushStdModelSnDataExt.setTaskCustomerItemName("itemName1");
        pushStdModelSnDataExt.setStockName("subStock1");

        List<WipExtendIdentificationDTO> wipExtendIdentifications = Collections.emptyList();
        List<FixBomDetailDTO> fixBomDetails = Collections.singletonList(new FixBomDetailDTO());

        ApsCustInstructBomDTO apsCustInstructBom = new ApsCustInstructBomDTO();
        apsCustInstructBom.setMaterialSign("materialSign1");
        apsCustInstructBom.setMaterialBillType("NEW_INSTRUCT_BOM");

        ApsCustomerTaskDTO apsCustomerTaskDTO = new ApsCustomerTaskDTO();
        Page<ApsCustomerTaskDTO> page = new Page<>();
        page.setRows(Arrays.asList(apsCustomerTaskDTO));
        apsCustomerTaskDTO.setBomList(Collections.singletonList(apsCustInstructBom));
        when(apsInOneClient.customerTaskQueryWithDetail(any())).thenReturn(page);

        SysLookupValues stockConfig = new SysLookupValues();
        stockConfig.setAttribute2("subStock");
        stockConfig.setLookupCode(new BigDecimal(Constant.LOOKUP_CODE_BAD_WAREHOUSE));
        stockConfig.setRemark(Constant.STR_DEFECTIVE_PRODUCT_WAREHOUSE);
        SysLookupValues stockConfig1 = new SysLookupValues();
        stockConfig1.setAttribute2("subStock1");
        stockConfig1.setLookupCode(new BigDecimal(Constant.LOOKUP_CODE_BAD_WAREHOUSE));
        stockConfig1.setRemark(Constant.STR_DEFECTIVE_PRODUCT_WAREHOUSE);
        SysLookupValues stockConfig2 = new SysLookupValues();
        stockConfig2.setAttribute2(null);
        stockConfig2.setLookupCode(new BigDecimal(Constant.LOOKUP_CODE_BAD_WAREHOUSE));
        stockConfig2.setRemark("TEST");

        when(sysLookupValuesRepository.selectValuesByType(anyInt())).thenReturn(Lists.newArrayList(stockConfig, stockConfig2, stockConfig1));

        // When
        FinishedProductStorageDTO result = service.wrapPushMessageData(pushStdModelSnDataExt, wipExtendIdentifications, fixBomDetails);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getMaterialQuality());
    }

    @Test
    public void testWrapPushMessageDataWithSubStock1() {
        // Given
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        pushStdModelSnDataExt.setTaskNo("task1");
        pushStdModelSnDataExt.setStockOrgId(123);
        pushStdModelSnDataExt.setSn("sn1");
        pushStdModelSnDataExt.setTaskBillNo("bill1");
        pushStdModelSnDataExt.setTaskCustomerPartType("partType1");
        pushStdModelSnDataExt.setTaskCustomerItemName("itemName1");
        pushStdModelSnDataExt.setStockName("subStock1");

        WipExtendIdentificationDTO wipExtendIdentification = new WipExtendIdentificationDTO();
        wipExtendIdentification.setItemNo("child11ItemNo");
        wipExtendIdentification.setSn("child11Sn");
        WipExtendIdentificationDTO wipExtendIdentification1 = new WipExtendIdentificationDTO();
        wipExtendIdentification1.setItemNo("child11ItemNo");
        wipExtendIdentification1.setSn("child11Sn_1");
        WipExtendIdentificationDTO wipExtendIdentification2 = new WipExtendIdentificationDTO();
        wipExtendIdentification2.setSn("child11Sn_1");
        List<WipExtendIdentificationDTO> wipExtendIdentifications = Lists.newArrayList(wipExtendIdentification, wipExtendIdentification1);
        FixBomDetailDTO topFixBomDetail = new FixBomDetailDTO();
        List<FixBomDetailDTO> fixBomDetails = Collections.singletonList(topFixBomDetail);

        FixBomDetailDTO child1FixBomDetail = new FixBomDetailDTO();
        child1FixBomDetail.setItemSupplierNo("child1ItemSupplierNo");

        FixBomDetailDTO child2FixBomDetail = new FixBomDetailDTO();
        topFixBomDetail.setChildNodes(Lists.newArrayList(child1FixBomDetail, child2FixBomDetail));

        FixBomDetailDTO child11FixBomDetail = new FixBomDetailDTO();
        child11FixBomDetail.setItemSupplierNo("child11ItemSupplierNo");
        child11FixBomDetail.setZteCode("child11ItemNo");
        child1FixBomDetail.setChildNodes(Lists.newArrayList(child11FixBomDetail));

        ApsCustInstructBomDTO topApsCustInstructBom = new ApsCustInstructBomDTO();
        topApsCustInstructBom.setMaterialSign("materialSign");
        topApsCustInstructBom.setMaterialBillType("NEW_INSTRUCT_BOM");

        ApsCustInstructBomDTO apsCustInstructBom1 = new ApsCustInstructBomDTO();
        apsCustInstructBom1.setMaterialSign("materialSign1");
        apsCustInstructBom1.setCustomerMaterialName("child11ItemSupplierNo");

        topApsCustInstructBom.setBomList(Lists.newArrayList(apsCustInstructBom1));

        ApsCustomerTaskDTO apsCustomerTaskDTO = new ApsCustomerTaskDTO();
        Page<ApsCustomerTaskDTO> page = new Page<>();
        page.setRows(Arrays.asList(apsCustomerTaskDTO));
        apsCustomerTaskDTO.setBomList(Lists.newArrayList(topApsCustInstructBom));
        when(apsInOneClient.customerTaskQueryWithDetail(any())).thenReturn(page);

        SysLookupValues prodStorageMaterialSignLookupValues = new SysLookupValues();
        prodStorageMaterialSignLookupValues.setLookupCode(new BigDecimal(2));
        prodStorageMaterialSignLookupValues.setLookupMeaning("child11ItemSupplierNo");

        SysLookupValues stockConfig = new SysLookupValues();
        stockConfig.setAttribute2("subStock");
        stockConfig.setLookupCode(new BigDecimal(Constant.LOOKUP_CODE_BAD_WAREHOUSE));
        stockConfig.setRemark(Constant.STR_DEFECTIVE_PRODUCT_WAREHOUSE);

        when(sysLookupValuesRepository.selectValuesByType(anyInt()))
                .thenReturn(Lists.newArrayList(stockConfig))
                .thenReturn(Lists.newArrayList(prodStorageMaterialSignLookupValues));

        // When
        FinishedProductStorageDTO result = service.wrapPushMessageData(pushStdModelSnDataExt, wipExtendIdentifications, fixBomDetails);

        // Then
        assertNotNull(result);
        assertEquals(1, result.getMaterialQuality());
        List<FixBomDetailDTO> childNodes = topFixBomDetail.getChildNodes();
        for (int i = 0; i < 15; i++) {
            FixBomDetailDTO a1 = new FixBomDetailDTO();
            a1.setCustomerComponentType("server.ConfigModel");
            if(i==1){
                a1.setItemType(Constant.COMPONENTS_OF_FINISHED_MATERIAL);
            }
            if(i==2){
                a1.setItemType(Constant.COMPONENTS_OF_FINISHED_MATERIAL);
                a1.setItemMaterialType(Constant.ITEM_TYPE_L6_PKG);
            }
            if(i==3){
                a1.setItemType(Constant.COMPONENTS_OF_FINISHED_MATERIAL);
                a1.setBoxBomRequired(Constant.FLAG_Y);
            }
            if(i==4){
                a1.setItemType(Constant.COMPONENTS_OF_FINISHED_MATERIAL);
                a1.setBoxBomRequired(Constant.FLAG_Y);
                a1.setItemNumber("4");
            }
            childNodes.add(a1);
        }
        service.wrapPushMessageData(pushStdModelSnDataExt, wipExtendIdentifications, fixBomDetails);
    }

    @Test
    public void testWrapPushMessageDataWithoutSubStock() {
        // Given
        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        pushStdModelSnDataExt.setTaskNo("task1");
        pushStdModelSnDataExt.setStockOrgId(123);
        pushStdModelSnDataExt.setSn("sn1");
        pushStdModelSnDataExt.setTaskBillNo("bill1");
        pushStdModelSnDataExt.setTaskCustomerPartType("partType1");
        pushStdModelSnDataExt.setTaskCustomerItemName("itemName1");

        List<WipExtendIdentificationDTO> wipExtendIdentifications = Collections.emptyList();
        List<FixBomDetailDTO> fixBomDetails = Collections.singletonList(new FixBomDetailDTO());

        ApsCustInstructBomDTO apsCustInstructBom = new ApsCustInstructBomDTO();
        apsCustInstructBom.setMaterialSign("materialSign1");
        apsCustInstructBom.setMaterialBillType("NEW_INSTRUCT_BOM");

        ApsCustomerTaskDTO apsCustomerTaskDTO = new ApsCustomerTaskDTO();
        Page<ApsCustomerTaskDTO> page = new Page<>();
        page.setRows(Arrays.asList(apsCustomerTaskDTO));
        apsCustomerTaskDTO.setBomList(Collections.singletonList(apsCustInstructBom));
        when(apsInOneClient.customerTaskQueryWithDetail(any())).thenReturn(page);

        // When
        FinishedProductStorageDTO result = service.wrapPushMessageData(pushStdModelSnDataExt, wipExtendIdentifications, fixBomDetails);

        // Then
        assertNotNull(result);
        assertEquals(2, result.getMaterialQuality());
    }

    @Test
    public void setMaterialBillList()throws Exception {
        FinishedProductStorageCargoTransportDTO finishedProductStorageCargoTransportDTO = new FinishedProductStorageCargoTransportDTO();
        List<FinishedProductStorageItemDTO > materialBillList = Lists.newArrayList();
        materialBillList.add(new FinishedProductStorageItemDTO());
        finishedProductStorageCargoTransportDTO.setMaterialBillList(Lists.newArrayList(new FinishedProductStorageCargoTransportDTO()));
        Whitebox.invokeMethod(service,"setMaterialBillList",finishedProductStorageCargoTransportDTO,materialBillList);

        // Then
        assertNotNull(materialBillList);
        assertEquals(2, materialBillList.size());
    }
}
/*Ended by AICoder, pid:kfa910bffbx367b140a309d951152356c967dc2d*/