package com.zte.application.impl;

import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.infrastructure.remote.AiPlatformRemoteService;
import com.zte.infrastructure.remote.MessagePlatformRemoteService;
import com.zte.interfaces.dto.ChatbotDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @Date 2024/9/5 下午3:21
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ChatBotServiceImpl.class, ServiceDataBuilderUtil.class, HttpRemoteUtil.class, CommonUtils.class, HttpRemoteUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class})
public class ChatBotServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ChatBotServiceImpl service;
    @Mock
    MessagePlatformRemoteService messagePlatformRemoteService;
    @Mock
    private AiPlatformRemoteService aiPlatformRemoteService;

    @Before
    public void before() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        FieldUtils.writeField(aiPlatformRemoteService, "chatUrl", "https://rdcloud.zte.com.cn/zte-studio-ai-platform/info", true);
        FieldUtils.writeField(aiPlatformRemoteService, "datasetSearchUrl", "https://rdcloud.zte.com.cn/zte-studio-ai-platform/info", true);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        FieldUtils.writeField(messagePlatformRemoteService, "chatBotUrl", "https://moaportal.test.zte.com.cn:15061/services/sendExtmsg/users/{senduri}", true);
    }

    /* Started by AICoder, pid:teef49d092qd9f514080081a406ef227f28512ca */
    @Test
    public void questionAndAnswerOfcChatBot() throws Exception {
        // 准备匹配成功的测试数据
        ChatbotDTO chatbotDTO = new ChatbotDTO();
        ChatbotDTO.Data data = new ChatbotDTO.Data();
        data.setMsgBody("你好 @鲁班 问题内容");
        data.setRobotCName("鲁班");
        chatbotDTO.setData(data);
        // 模拟 aiPlatformRemoteService 的行为
        String aiChatResult = "AI 回答";
        PowerMockito.when(aiPlatformRemoteService.getAiChatResult(anyString(), anyString(), anyString())).thenReturn(aiChatResult);
        // 模拟 messagePlatformRemoteService 的行为
        PowerMockito.when(messagePlatformRemoteService.sentToTheMessagingPlatform(chatbotDTO, aiChatResult)).thenReturn("success");
        // 调用被测试方法
        String result = service.questionAndAnswerOfcChatBot(chatbotDTO);
        // 断言结果
        assertNull(result);
        // 测试没有匹配到关键词的情况
        data.setMsgBody("问题内容");
        service.questionAndAnswerOfcChatBot(chatbotDTO);
    }
    /* Ended by AICoder, pid:teef49d092qd9f514080081a406ef227f28512ca */
}