package com.zte.application.impl;

import com.zte.application.sncabind.impl.PsTaskServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.ProductionmgmtRemoteService;
import com.zte.interfaces.dto.PartsbarScanInfoDTO;
import com.zte.interfaces.dto.ScanPartstoboardDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.WarehouseEntryInfoHistoryDTO;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.EmailUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisHelper.class, DatawbRemoteService.class, ProductionmgmtRemoteService.class})
public class WipExtendIdentificationServiceImplTest {
    @InjectMocks
    private WipExtendIdentificationServiceImpl service;

    @Mock
    private SysLookupValuesServiceImpl lookupValuesService;
    @Mock
    private SysLookupTypesRepository sysLookupTypesRepository;
    @Mock
    private PsTaskServiceImpl psTaskService;
    @Mock
    private EmailUtils emailUtils;

    @Test
    public void sendWipExtSemiBySpm() throws Exception{
        PowerMockito.mockStatic(RedisHelper.class, DatawbRemoteService.class, ProductionmgmtRemoteService.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        String batch = "";
        String empNo = "10313234";
        try {
            service.sendWipExtSemiBySpm("123", batch, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SEND_SEMI_WIP_EXT_BY_SPM_LOCK,e.getMessage());
        }
        try {
            service.sentMaterialWipExtBySpm("123", empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SEND_MATERIAL_WIP_EXT_BY_SPM_LOCK,e.getMessage());
        }
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        PowerMockito.when(lookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(null);
        try {
            service.sendWipExtSemiBySpm("123", batch, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SEND_SEMI_WIP_EXT_BY_SPM_LOCK,e.getMessage());
        }
        try {
            service.sentMaterialWipExtBySpm("123", empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FIND_WIP_EXT_PROD_PLAN, e.getMessage());
        }
        SysLookupValues lookupValues = new SysLookupValues();
        lookupValues.setLookupMeaning("2023-02-10 00:00:00");
        PowerMockito.when(lookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(lookupValues);
        try {
            service.sendWipExtSemiBySpm("123", batch, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FIND_WIP_EXT_PROD_PLAN, e.getMessage());
        }
        try {
            service.sentMaterialWipExtBySpm("123", empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FIND_WIP_EXT_PROD_PLAN, e.getMessage());
        }
        PowerMockito.when(DatawbRemoteService.getSemiWipExtFromSpm(Mockito.any(), Mockito.anyBoolean())).thenReturn(new ArrayList<>());
        PowerMockito.when(DatawbRemoteService.getMaterialWipExtFromSpm(Mockito.any())).thenReturn(new ArrayList<>());
        try {
            service.sendWipExtSemiBySpm("123", batch, empNo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
        try {
            service.sentMaterialWipExtBySpm("123", empNo);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
        List<String> orgList = new ArrayList<>();
        orgList.add("4437");
        orgList.add("855");
        orgList.add("2437");
        orgList.add("3417");
        orgList.add("3577");
        orgList.add("395");
        int prod = 7000000;
        List<ScanPartstoboardDTO> dtoList = new ArrayList<>();
        List<PartsbarScanInfoDTO> partsList = new ArrayList<>();
        List<PsTask> psTasks = new ArrayList<>();
        for (int i = 1; i <= orgList.size() + 3; i++) {
            ScanPartstoboardDTO dto = new ScanPartstoboardDTO();
            PartsbarScanInfoDTO infoDTO = new PartsbarScanInfoDTO();
            dto.setPartsPlanid(prod + i);
            dto.setBoardPlanid(prod + i);
            dto.setBoardSn(i);
            dto.setPartsSn(i + 1);
            dto.setCreateDate(new Date());
            dto.setProdplanId(prod + i + "");
            dtoList.add(dto);
            infoDTO.setProdplanId(prod + i + "");
            infoDTO.setScanDate(new Date());
            partsList.add(infoDTO);
            PsTask psTask = new PsTask();
            psTask.setProdplanId(prod + i + "");
            psTask.setFactoryId(new BigDecimal(51));
            if (i < orgList.size()) {
                psTask.setFactoryId(new BigDecimal(51));
                psTask.setOrgId(new BigDecimal(orgList.get(i - 1)));
            } else if (i == orgList.size()) {
                psTask.setFactoryId(new BigDecimal(53));
            } else {
                psTask.setFactoryId(new BigDecimal(52));
            }
            psTasks.add(psTask);
        }
        PowerMockito.when(DatawbRemoteService.getSemiWipExtFromSpm(Mockito.any(),Mockito.anyBoolean())).thenReturn(dtoList);
        PowerMockito.when(DatawbRemoteService.getMaterialWipExtFromSpm(Mockito.any())).thenReturn(partsList);
        PowerMockito.when(psTaskService.getFactoryIdByProdId(Mockito.any())).thenReturn(psTasks);
        PowerMockito.when(ProductionmgmtRemoteService.pushWipExtSemiToFactory(Mockito.any(), Mockito.any(), Mockito.anyString())).thenReturn(1);
        List<SysLookupTypesDTO> valuesList = new ArrayList<>();
        SysLookupTypesDTO typesDTO = new SysLookupTypesDTO();
        SysLookupTypesDTO typesDTO1 = new SysLookupTypesDTO();
        SysLookupTypesDTO typesDTO2 = new SysLookupTypesDTO();
        typesDTO.setLookupMeaning("53");
        typesDTO1.setLookupMeaning("52");
        typesDTO2.setLookupMeaning("55");
        valuesList.add(typesDTO);
        valuesList.add(typesDTO1);
        valuesList.add(typesDTO2);
        PowerMockito.when(sysLookupTypesRepository.getList(Mockito.any())).thenReturn(valuesList);
        try {
            service.sendWipExtSemiBySpm("123", batch, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FIND_WIP_EXT_PROD_PLAN,e.getMessage());
        }
        try {
            service.sentMaterialWipExtBySpm("123", empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FIND_WIP_EXT_PROD_PLAN, e.getMessage());
        }
    }
}