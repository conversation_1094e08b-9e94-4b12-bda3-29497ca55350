package com.zte.application.impl;


import com.zte.domain.model.PmRepairInfoRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.PmRepairInfoStatDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest(DatawbRemoteService.class)
public class PmRepairInfoServiceImplTest extends BaseTestCase {

    @InjectMocks
    private PmRepairInfoServiceImpl pmRepairInfoServiceImpl;

    @Mock
    private PmRepairInfoRepository pmRepairInfoRepository;

    @Before
    public void init() {
    }

    /* Started by AICoder, pid:53658ob44a127ad140e309de21eb8311d921a2b8 */
    @Test
    public void statByStype() {
        List<PmRepairInfoStatDTO> result = pmRepairInfoServiceImpl.statByStype("", "");
        Assert.assertTrue(result.isEmpty());

        result = pmRepairInfoServiceImpl.statByStype("1", "");
        Assert.assertTrue(result.isEmpty());

        result = pmRepairInfoServiceImpl.statByStype("1", "error");
        Assert.assertTrue(result.isEmpty());

        PowerMockito.mockStatic(DatawbRemoteService.class);
        List<PmRepairInfoStatDTO> statDataOfMes = new ArrayList<>();
        PowerMockito.when(DatawbRemoteService.statByTroubleSmallCode(Mockito.anyString(), Mockito.anyString())).thenReturn(statDataOfMes);
        result = pmRepairInfoServiceImpl.statByStype("127215751001AKB", "error");
        Assert.assertTrue(result.isEmpty());

        List<PmRepairInfoStatDTO> statData = new ArrayList<>();
        PmRepairInfoStatDTO statDTO = new PmRepairInfoStatDTO();
        statDTO.setRepairProductType("维修大类1");
        statDTO.setRepairProductStype("维修小类1");
        statDTO.setCount(5);
        statData.add(statDTO);
        PowerMockito.when(pmRepairInfoRepository.statByStype(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(statData);

        PmRepairInfoStatDTO mesStatDTO = new PmRepairInfoStatDTO();
        mesStatDTO.setRepairProductType("维修大类1");
        mesStatDTO.setRepairProductStype("维修小类1");
        mesStatDTO.setCount(7);
        statDataOfMes.add(mesStatDTO);

        mesStatDTO = new PmRepairInfoStatDTO();
        mesStatDTO.setRepairProductType("维修大类2");
        mesStatDTO.setRepairProductStype("维修小类2");
        mesStatDTO.setCount(20);
        statDataOfMes.add(mesStatDTO);

        result = pmRepairInfoServiceImpl.statByStype("127215751001AKB", "error");
        Assert.assertTrue(result.size() == 2);
        Assert.assertTrue(result.get(0).getCount() == 12);
    }

    @Test
    public void statByStypeAndLoc() {
        List<PmRepairInfoStatDTO> result = pmRepairInfoServiceImpl.statByStypeAndLoc("", "");
        Assert.assertTrue(result.isEmpty());

        result = pmRepairInfoServiceImpl.statByStypeAndLoc("1", "");
        Assert.assertTrue(result.isEmpty());

        result = pmRepairInfoServiceImpl.statByStypeAndLoc("1", "error");
        Assert.assertTrue(result.isEmpty());

        PowerMockito.mockStatic(DatawbRemoteService.class);
        List<PmRepairInfoStatDTO> statDataOfMes = new ArrayList<>();
        PowerMockito.when(DatawbRemoteService.statByTroubleSmallCodeAndSiteNo(Mockito.anyString(), Mockito.anyString())).thenReturn(statDataOfMes);
        result = pmRepairInfoServiceImpl.statByStypeAndLoc("127215751001AKB", "error");
        Assert.assertTrue(result.isEmpty());

        List<PmRepairInfoStatDTO> statData = new ArrayList<>();
        PmRepairInfoStatDTO statDTO = new PmRepairInfoStatDTO();
        statDTO.setRepairProductType("维修大类1");
        statDTO.setRepairProductStype("维修小类1");
        statDTO.setLocationNo("A1");
        statDTO.setCount(5);
        statData.add(statDTO);
        PowerMockito.when(pmRepairInfoRepository.statByStypeAndLoc(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(statData);

        PmRepairInfoStatDTO mesStatDTO = new PmRepairInfoStatDTO();
        mesStatDTO.setRepairProductType("维修大类1");
        mesStatDTO.setRepairProductStype("维修小类1");
        mesStatDTO.setLocationNo("A1");
        mesStatDTO.setCount(7);
        statDataOfMes.add(mesStatDTO);

        mesStatDTO = new PmRepairInfoStatDTO();
        mesStatDTO.setRepairProductType("维修大类2");
        mesStatDTO.setRepairProductStype("维修小类2");
        mesStatDTO.setLocationNo("A2");
        mesStatDTO.setCount(20);
        statDataOfMes.add(mesStatDTO);

        result = pmRepairInfoServiceImpl.statByStypeAndLoc("127215751001AKB", "error");
        Assert.assertTrue(result.size() == 2);
        Assert.assertTrue(result.get(0).getCount() == 12);
    }
    /* Ended by AICoder, pid:53658ob44a127ad140e309de21eb8311d921a2b8 */

}
