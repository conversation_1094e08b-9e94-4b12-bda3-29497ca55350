package com.zte.application.impl;

import cn.hutool.core.lang.copier.Copier;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.listener.ReadListener;
import com.zte.application.ResourceInfoService;
import com.zte.common.CommonUtils;
import com.zte.common.ExcelUtils;
import com.zte.common.model.MessageId;
import com.zte.common.model.ResultData;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ResourceWarningCheckHandler;
import com.zte.common.utils.constant.SnCaConstant;
import com.zte.domain.model.*;
import com.zte.interfaces.dto.ResourceInfoEntityDTO;
import com.zte.interfaces.dto.ResourceWarningConfigDTO;
import com.zte.interfaces.dto.ResourceWarningResultDTO;
import com.zte.interfaces.dto.ResourceWarningConfigImportDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.EasyExcelUtils;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.MESHttpHelper;
import org.apache.kafka.common.utils.ByteUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.BeanUtils;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>  魏振东
 * @description: TODO
 * @date 2023/9/7 下午4:34
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({BeanUtils.class, MESHttpHelper.class, UUID.class, CommonUtils.class, ExcelUtils.class, SnCaConstant.class,EasyExcelUtils.class})
public class ResourceWarningConfigServiceImplTest {

    @InjectMocks
    private ResourceWarningConfigServiceImpl resourceWarningConfigService;

    @Mock
    private ResourceInfoRepository resourceInfoRepository;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private ResourceWarningRecordRepository resourceWarningRecordRepository;
    @Mock
    private ResourceWarningConfigRepository repository;
    @Mock
    private ResourceInfoServiceImpl infoService;


    @Test
    public void insertTest() {
        PowerMockito.mockStatic(BeanUtils.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(UUID.class);
        ResourceWarningConfigDTO dto = new ResourceWarningConfigDTO();
        Assert.assertThrows(MesBusinessException.class, () -> resourceWarningConfigService.insert(dto));
        dto.setWarningType("1");
        List<ResourceWarningConfig> list = new ArrayList<>();
        PowerMockito.when(repository.selectCountByResourceNo(any())).thenReturn(1L);
        PowerMockito.when(repository.insertEntity(any())).thenReturn(1L);
        dto.setLowWaterLevel(10);
        Assert.assertThrows(MesBusinessException.class, () -> resourceWarningConfigService.insert(dto));
        dto.setDeviceType("1");
        resourceWarningConfigService.insert(dto);
        dto.setLowWaterLevel(null);
        dto.setExpiryDateBefore(10);
        resourceWarningConfigService.insert(dto);
        list.add(new ResourceWarningConfig());
        PowerMockito.when(repository.selectCountByResourceNo(any())).thenReturn(1L);
        PowerMockito.when(repository.selectCountByDeviceType(any(), any())).thenReturn(1L);
        Assert.assertThrows(MesBusinessException.class, () -> resourceWarningConfigService.insert(dto));


        dto.setWarningType("0");
        Assert.assertThrows(MesBusinessException.class, () -> resourceWarningConfigService.insert(dto));
        dto.setResourceNo("123");
        Assert.assertThrows(MesBusinessException.class, () -> resourceWarningConfigService.insert(dto));
        dto.setDeviceType(null);
        Assert.assertThrows(MesBusinessException.class, () -> resourceWarningConfigService.insert(dto));
        dto.setDeviceType("123");
        PowerMockito.when(repository.selectCountByResourceNo(any())).thenReturn(0L);
        ResourceInfoEntityDTO infoDto = new ResourceInfoEntityDTO();
        infoDto.setDeviceType("123");
        PowerMockito.when(infoService.findDeviceByResourceNo(any())).thenReturn(infoDto);
        PowerMockito.when(repository.selectCountByDeviceType(any(),any())).thenReturn(0L);
        resourceWarningConfigService.insert(dto);
        PowerMockito.when(repository.selectCountByDeviceType(any(),any())).thenReturn(1L);
        Assert.assertThrows(MesBusinessException.class, () -> resourceWarningConfigService.insert(dto));
    }


    @Test
    public void updateTest() {
        PowerMockito.mockStatic(BeanUtils.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(UUID.class);
        ResourceWarningConfigDTO dto = new ResourceWarningConfigDTO();
        Assert.assertThrows(MesBusinessException.class, () -> resourceWarningConfigService.update(dto));
        dto.setWarningType("1");
        PowerMockito.when(repository.updateEntity(any())).thenReturn(1L);
        dto.setLowWaterLevel(10);
        dto.setDeviceType("1");
        resourceWarningConfigService.update(dto);
    }

    @Test
    public void deleteTest() {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(repository.deleteEntity(any(), any())).thenReturn(1L);
        resourceWarningConfigService.delete("123");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }


    @Test
    public void pageTest() {
        PowerMockito.mockStatic(MESHttpHelper.class);
        ResourceWarningConfigDTO dto = new ResourceWarningConfigDTO();
        List<ResourceWarningConfigDTO> list = new ArrayList<>();
        PowerMockito.when(repository.selectEntityListPage(any())).thenReturn(list);
        Page<ResourceWarningConfigDTO> page = resourceWarningConfigService.page(dto);
        Assert.assertTrue(page.getRows().size() >= 0);
    }

    @Test
    public void waterLevelWarning() {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("sss");
        List<ResourceWarningConfig> list = new ArrayList<>();
        PowerMockito.when(repository.selectListByWaterLevel(any())).thenReturn(list);
        resourceWarningConfigService.waterLevelWarning();

        ResourceWarningConfig resourceWarningConfig1 = new ResourceWarningConfig();
        resourceWarningConfig1.setResourceNo("111");
        resourceWarningConfig1.setWarningType("0");
        resourceWarningConfig1.setLowWaterLevel(1);
        ResourceWarningConfig resourceWarningConfig2 = new ResourceWarningConfig();
        resourceWarningConfig2.setResourceNo("222");
        resourceWarningConfig2.setWarningType("0");
        resourceWarningConfig2.setLowWaterLevel(22);
        ResourceWarningConfig resourceWarningConfig3 = new ResourceWarningConfig();
        resourceWarningConfig3.setDeviceType("333");
        resourceWarningConfig3.setWarningType("1");
        resourceWarningConfig3.setLowWaterLevel(1);
        ResourceWarningConfig resourceWarningConfig4 = new ResourceWarningConfig();
        resourceWarningConfig4.setDeviceType("444");
        resourceWarningConfig4.setWarningType("1");
        resourceWarningConfig4.setLowWaterLevel(444);
        list.add(resourceWarningConfig1);
        list.add(resourceWarningConfig2);
        list.add(resourceWarningConfig3);
        list.add(resourceWarningConfig4);
        PowerMockito.when(repository.selectListByWaterLevel(any())).thenReturn(list);

        List<ResourceInfoEntityDTO> resourceInfoList = new ArrayList<>();
        resourceInfoList.add(new ResourceInfoEntityDTO());
        resourceInfoList.add(new ResourceInfoEntityDTO());
        resourceInfoList.add(new ResourceInfoEntityDTO());
        resourceInfoList.add(new ResourceInfoEntityDTO());
        resourceInfoList.get(0).setResourceNo("111");
        resourceInfoList.get(0).setDeviceType("333");
        resourceInfoList.get(0).setAvailableQuantity(BigInteger.valueOf(111));
        resourceInfoList.get(1).setResourceNo("222");
        resourceInfoList.get(1).setDeviceType("444");
        resourceInfoList.get(1).setAvailableQuantity(BigInteger.valueOf(2));
        resourceInfoList.get(2).setResourceNo("111");
        resourceInfoList.get(2).setDeviceType("333");
        resourceInfoList.get(2).setAvailableQuantity(BigInteger.valueOf(2));
        resourceInfoList.get(3).setResourceNo("222");
        resourceInfoList.get(3).setDeviceType("444");
        resourceInfoList.get(3).setAvailableQuantity(BigInteger.valueOf(2));

        PowerMockito.when(resourceInfoRepository.getListByResource(any())).thenReturn(resourceInfoList);
        PowerMockito.when(resourceInfoRepository.getListByDeviceType(any())).thenReturn(resourceInfoList);
        PowerMockito.when(resourceInfoRepository.getListByGroupDeviceType(any())).thenReturn(resourceInfoList);
        resourceWarningConfigService.waterLevelWarning();

        List<SysLookupValues> sysLookupValues = new ArrayList<>();
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues.add(sysLookupValues1);
        PowerMockito.when(sysLookupValuesRepository.getList(any())).thenReturn(sysLookupValues);
        resourceWarningConfigService.waterLevelWarning();
        sysLookupValues1.setLookupMeaning("asd");
        PowerMockito.when(sysLookupValuesRepository.getList(any())).thenReturn(sysLookupValues);
        resourceWarningConfigService.waterLevelWarning();
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void expiryDateWarning() {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("sss");
        List<ResourceWarningConfig> list = new ArrayList<>();
        Page pageInfo = new Page<>(1, 500);
        pageInfo.setTotalPage(2);
        PowerMockito.when(repository.selectListByExpiryDate(pageInfo)).thenReturn(list);
        resourceWarningConfigService.expiryDateWarning();

        ResourceWarningConfig resourceWarningConfig1 = new ResourceWarningConfig();
        resourceWarningConfig1.setResourceNo("111");
        resourceWarningConfig1.setWarningType("0");
        resourceWarningConfig1.setExpiryDateBefore(1);
        ResourceWarningConfig resourceWarningConfig2 = new ResourceWarningConfig();
        resourceWarningConfig2.setResourceNo("222");
        resourceWarningConfig2.setWarningType("0");
        resourceWarningConfig2.setExpiryDateBefore(22);
        ResourceWarningConfig resourceWarningConfig3 = new ResourceWarningConfig();
        resourceWarningConfig3.setDeviceType("333");
        resourceWarningConfig3.setWarningType("1");
        resourceWarningConfig3.setExpiryDateBefore(1);
        ResourceWarningConfig resourceWarningConfig4 = new ResourceWarningConfig();
        resourceWarningConfig4.setDeviceType("444");
        resourceWarningConfig4.setWarningType("1");
        resourceWarningConfig4.setExpiryDateBefore(444);
        list.add(resourceWarningConfig1);
        list.add(resourceWarningConfig2);
        list.add(resourceWarningConfig3);
        list.add(resourceWarningConfig4);
        PowerMockito.when(repository.selectListByExpiryDate(any())).thenReturn(list);

        List<ResourceInfoEntityDTO> resourceInfoList = new ArrayList<>();
        resourceInfoList.add(new ResourceInfoEntityDTO());
        resourceInfoList.add(new ResourceInfoEntityDTO());
        resourceInfoList.add(new ResourceInfoEntityDTO());
        resourceInfoList.add(new ResourceInfoEntityDTO());
        resourceInfoList.get(0).setResourceNo("111");
        resourceInfoList.get(0).setDeviceType("333");
        resourceInfoList.get(0).setExpiryDate(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000 * 3));
        resourceInfoList.get(1).setResourceNo("222");
        resourceInfoList.get(1).setDeviceType("444");
        resourceInfoList.get(1).setExpiryDate(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000 * 3));
        resourceInfoList.get(2).setResourceNo("111");
        resourceInfoList.get(2).setDeviceType("333");
        resourceInfoList.get(2).setExpiryDate(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000 * 3));
        resourceInfoList.get(3).setResourceNo("222");
        resourceInfoList.get(3).setDeviceType("444");
        resourceInfoList.get(3).setExpiryDate(new Date(System.currentTimeMillis() + 24 * 60 * 1000 * 3));

        PowerMockito.when(resourceInfoRepository.getListByResource(any())).thenReturn(resourceInfoList);
        PowerMockito.when(resourceInfoRepository.getListByDeviceType(any())).thenReturn(resourceInfoList);
        PowerMockito.when(resourceInfoRepository.getListByGroupDeviceType(any())).thenReturn(resourceInfoList);
        resourceWarningConfigService.expiryDateWarning();

        List<SysLookupValues> sysLookupValues = new ArrayList<>();
        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setLookupMeaning("asd");
        sysLookupValues.add(sysLookupValues1);
        PowerMockito.when(sysLookupValuesRepository.getList(any())).thenReturn(sysLookupValues);
        resourceWarningConfigService.expiryDateWarning();
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void insertRecord() {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("sss");

        List<ResourceWarningResultDTO> resourceInfoList = new ArrayList<>();

        ResourceWarningResultDTO resourceWarningResultDTO1 = new ResourceWarningResultDTO();
        resourceWarningResultDTO1.setWarningType("0");
        resourceWarningResultDTO1.setResourceNo("0");
        resourceWarningResultDTO1.setDeviceType("0");
        resourceInfoList.add(resourceWarningResultDTO1);
        resourceWarningConfigService.insertRecord(resourceInfoList);

        ResourceWarningResultDTO resourceWarningResultDTO2 = new ResourceWarningResultDTO();
        resourceWarningResultDTO2.setWarningType("0");
        resourceWarningResultDTO2.setResourceNo("1");
        resourceWarningResultDTO2.setDeviceType("0");
        ResourceWarningResultDTO resourceWarningResultDTO3 = new ResourceWarningResultDTO();
        resourceWarningResultDTO3.setWarningType("1");
        resourceWarningResultDTO3.setResourceNo("2");
        resourceWarningResultDTO3.setDeviceType("1");
        ResourceWarningResultDTO resourceWarningResultDTO4 = new ResourceWarningResultDTO();
        resourceWarningResultDTO4.setWarningType("0");
        resourceWarningResultDTO4.setResourceNo("3");
        resourceWarningResultDTO4.setDeviceType("1");
        resourceInfoList.add(resourceWarningResultDTO2);
        resourceInfoList.add(resourceWarningResultDTO3);
        resourceInfoList.add(resourceWarningResultDTO4);
        resourceWarningConfigService.insertRecord(resourceInfoList);

        List<ResourceWarningRecord> recordList = new ArrayList<>();
        ResourceWarningRecord resourceWarningRecord = new ResourceWarningRecord();
        resourceWarningRecord.setResourceNo("0");
        resourceWarningRecord.setLowWaterWarningFlag("Y");
        ResourceWarningRecord resourceWarningRecord1 = new ResourceWarningRecord();
        resourceWarningRecord1.setResourceNo("1");
        resourceWarningRecord1.setLowWaterWarningFlag("N");
        recordList.add(resourceWarningRecord);
        recordList.add(resourceWarningRecord1);

        PowerMockito.when(resourceWarningRecordRepository.queryByResource(any())).thenReturn(recordList);
        resourceWarningConfigService.insertRecord(resourceInfoList);

        resourceInfoList.get(0).setExpiryDate(new Date());
        recordList.get(0).setExpiryWarningFlag("Y");
        recordList.get(1).setLowWaterWarningFlag("N");
        PowerMockito.when(resourceWarningRecordRepository.queryByResource(any())).thenReturn(recordList);
        resourceWarningConfigService.insertRecord(resourceInfoList);
        Assert.assertNotNull(resourceInfoList);
    }

    @Test
    public void cancelWarning() {
        resourceWarningConfigService.cancelWarning();

        List<ResourceWarningRecord> recordList = new ArrayList<>();
        ResourceWarningRecord resourceWarningRecord = new ResourceWarningRecord();
        resourceWarningRecord.setResourceNo("111");
        resourceWarningRecord.setConfigId("0");
        resourceWarningRecord.setLowWaterWarningFlag("Y");
        resourceWarningRecord.setExpiryWarningFlag("Y");
        ResourceWarningRecord resourceWarningRecord1 = new ResourceWarningRecord();
        resourceWarningRecord1.setResourceNo("1");
        resourceWarningRecord1.setConfigId("1");
        resourceWarningRecord1.setLowWaterWarningFlag("Y");
        resourceWarningRecord1.setExpiryWarningFlag("Y");
        recordList.add(resourceWarningRecord);
        recordList.add(resourceWarningRecord1);

        PowerMockito.when(resourceWarningRecordRepository.selectWarningRecord(any())).thenReturn(recordList);

        List<ResourceWarningConfig> configList = new ArrayList<>();
        ResourceWarningConfig resourceWarningConfig1 = new ResourceWarningConfig();
        resourceWarningConfig1.setResourceNo("111");
        resourceWarningConfig1.setId("0");
        resourceWarningConfig1.setWarningType("0");
        resourceWarningConfig1.setExpiryDateBefore(1);
        ResourceWarningConfig resourceWarningConfig2 = new ResourceWarningConfig();
        resourceWarningConfig2.setResourceNo("222");
        resourceWarningConfig2.setWarningType("0");
        resourceWarningConfig2.setLowWaterLevel(2);
        resourceWarningConfig2.setId("1");
        resourceWarningConfig2.setExpiryDateBefore(22);
        ResourceWarningConfig resourceWarningConfig3 = new ResourceWarningConfig();
        resourceWarningConfig3.setDeviceType("333");
        resourceWarningConfig3.setWarningType("1");
        resourceWarningConfig3.setExpiryDateBefore(1);
        ResourceWarningConfig resourceWarningConfig4 = new ResourceWarningConfig();
        resourceWarningConfig4.setDeviceType("444");
        resourceWarningConfig4.setWarningType("1");
        resourceWarningConfig4.setExpiryDateBefore(444);
        configList.add(resourceWarningConfig1);
        configList.add(resourceWarningConfig2);
        configList.add(resourceWarningConfig3);
        configList.add(resourceWarningConfig4);
        PowerMockito.when(repository.selectListByIds(any())).thenReturn(configList);

        List<ResourceInfoEntityDTO> resourceInfoList = new ArrayList<>();
        resourceInfoList.add(new ResourceInfoEntityDTO());
        resourceInfoList.add(new ResourceInfoEntityDTO());
        resourceInfoList.add(new ResourceInfoEntityDTO());
        resourceInfoList.add(new ResourceInfoEntityDTO());
        resourceInfoList.get(0).setResourceNo("111");
        resourceInfoList.get(0).setDeviceType("333");
        resourceInfoList.get(0).setExpiryDate(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000 * 3));
        resourceInfoList.get(1).setResourceNo("222");
        resourceInfoList.get(1).setAvailableQuantity(new BigInteger("1"));
        resourceInfoList.get(1).setDeviceType("444");
        resourceInfoList.get(1).setExpiryDate(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000 * 3));
        resourceInfoList.get(2).setResourceNo("111");
        resourceInfoList.get(2).setDeviceType("333");
        resourceInfoList.get(2).setExpiryDate(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000 * 3));
        resourceInfoList.get(3).setResourceNo("222");
        resourceInfoList.get(3).setDeviceType("444");
        resourceInfoList.get(3).setExpiryDate(new Date(System.currentTimeMillis() + 24 * 60 * 1000 * 3));

        PowerMockito.when(resourceInfoRepository.getListByResource(any())).thenReturn(resourceInfoList);


        resourceWarningConfigService.cancelWarning();
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }


    @Test
    public void checkAndReturnExcelList() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("sss");
        List<ResourceWarningConfigImportDTO> list = new ArrayList<>();
        Assert.assertNotNull(resourceWarningConfigService.checkAndReturnExcelList(list));
        ResourceWarningConfigImportDTO dto = new ResourceWarningConfigImportDTO();
        dto.setWarningType("0");
        dto.setResourceNo("123");
        dto.setDeviceType("123");
        dto.setLowWaterLevel("10");
        list.add(dto);
        Mockito.when(repository.selectByNosOrTypes(any(),any())).thenReturn(list);
        Mockito.when(infoService.findDeviceByResourceNos(any())).thenReturn(new ArrayList<>());
        resourceWarningConfigService.checkAndReturnExcelList(list);


    }


    @Test
    public void batchInsertTest() {
        List<ResourceWarningConfigImportDTO> list = new ArrayList<>();
        ResourceWarningConfigImportDTO dto = new ResourceWarningConfigImportDTO();
        dto.setDeviceType("123");
        dto.setResourceNo("123");
        dto.setWarningType("1");
        dto.setExpiryDateBefore("10");
        list.add(dto);
        Assert.assertTrue(resourceWarningConfigService.batchInsert(list));
    }


    @Test
    public void checkImportDataTest() {
        List<ResourceWarningConfigImportDTO> list = new ArrayList<>();
        ResourceWarningConfigImportDTO dto = new ResourceWarningConfigImportDTO();
        dto.setDeviceType("123");
        dto.setResourceNo("123");
        dto.setWarningType("1");
        dto.setExpiryDateBefore("10");
        list.add(dto);
        resourceWarningConfigService.checkImportData(list, true);
        for (int i = 0; i < 1000; i++) {
            ResourceWarningConfigImportDTO dto1 = new ResourceWarningConfigImportDTO();
            dto1.setDeviceType("123");
            list.add(dto1);
        }
        Assert.assertThrows(MesBusinessException.class,()->resourceWarningConfigService.checkImportData(list, true));
    }


    @Test
    public void resourceWarningHandler() {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("sss");
        Map<String, List<ResourceWarningConfigImportDTO>> resource2DtoMap = new HashMap<>();
        Map<String, List<ResourceWarningConfigImportDTO>> device2DtoMap = new HashMap<>();
        Map<String, String> resource2Device = new HashMap<>();
        ResourceWarningCheckHandler handler = new ResourceWarningCheckHandler(resource2DtoMap,device2DtoMap,resource2Device);
        List<ResourceWarningConfigImportDTO>  list = new ArrayList<>();
        ResourceWarningConfigImportDTO dto = new ResourceWarningConfigImportDTO();
        dto.setWarningType("1");
        list.add(dto);
        handler.checkFillMsgList(list);
        dto.setExpiryDateBefore("-123");
        handler.checkFillMsgList(list);
        dto.setDeviceType("123");
        dto.setExpiryDateBefore("12");
        dto.setLowWaterLevel("123");
        handler.checkFillMsgList(list);
        ResourceWarningConfigImportDTO dto1 = new ResourceWarningConfigImportDTO();
        dto1.setWarningType("0");
        dto1.setResourceNo("123");
        dto1.setDeviceType("12");
        dto1.setLowWaterLevel("123");
        list.add(dto1);
        handler.checkFillMsgList(list);
        resource2Device.put("123","000");
        handler.checkFillMsgList(list);
        handler.setInsertFlag(true);
        Assert.assertThrows(MesBusinessException.class,()-> handler.checkFillMsgList(list));
    }
}
