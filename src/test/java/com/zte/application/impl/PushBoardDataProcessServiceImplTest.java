package com.zte.application.impl;
/* Started by AICoder, pid:2cdb8af4593648e08481f775236498e0 */

import com.zte.application.TradeDataLogService;
import com.zte.domain.model.PushBoardDataDetailRepository;
import com.zte.domain.model.PushBoardDataProcessRepository;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.PushBoardDataDetailDTO;
import com.zte.interfaces.dto.PushBoardDataProcessDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.BaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;

import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
@PrepareForTest({RequestHeadValidationUtil.class})
public class PushBoardDataProcessServiceImplTest extends BaseTestCase {
    @InjectMocks
    private PushBoardDataProcessServiceImpl service;
    @Mock
    private PushBoardDataProcessRepository pushBoardDataProcessRepository;
    @Mock
    private IdGenerator idGenerator;
    @Mock
    private TradeDataLogService tradeDataLogService;

    @Mock
    private PushBoardDataDetailRepository pushBoardDataDetailRepository;

    @Before
    public void init(){
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);
    }
    @Test
    public void getAndInitNeedPushData() {
        List<String> snList = new ArrayList<>();
        String businessType = "";
        List<PushBoardDataProcessDTO> result = service.getAndInitNeedPushData(snList, businessType);
        Assert.assertTrue(result.isEmpty());

        snList.add("777766600001");
        businessType = "AOI";
        List<String> existList = new ArrayList<>();
        Mockito.when(pushBoardDataProcessRepository.getExistSn(Mockito.anyList(), Mockito.anyString())).thenReturn(existList);
        result = service.getAndInitNeedPushData(snList, businessType);
        Assert.assertTrue(result.isEmpty());

        existList.add("777766600001");
        result = service.getAndInitNeedPushData(snList, businessType);
        Assert.assertTrue(result.isEmpty());
    }

    /* Started by AICoder, pid:a7f47uf0403c9a4147310819c023c29e60e4c7a2 */
    @Test
    public void syncProcessPushStatus() {
        PushBoardDataProcessDTO param = new PushBoardDataProcessDTO();
        param.setBeforeDay(0);
        List<PushBoardDataProcessDTO> processList = new ArrayList<>();
        List<PushBoardDataProcessDTO> processList2 = new ArrayList<>();
        Mockito.when(pushBoardDataProcessRepository.getNotCallBackList(Mockito.any(), Mockito.eq(""),
                Mockito.anyInt(),Mockito.any())).thenReturn(processList);
        Mockito.when(pushBoardDataProcessRepository.getNotCallBackList(Mockito.any(), Mockito.eq("200"),
                Mockito.anyInt(),Mockito.any())).thenReturn(processList2);
        service.syncProcessPushStatus(param);

        PushBoardDataProcessDTO pushBoardDataProcessDTO = new PushBoardDataProcessDTO();
        pushBoardDataProcessDTO.setPushStatus(1);
        pushBoardDataProcessDTO.setSn("777766600001");
        pushBoardDataProcessDTO.setLastUpdatedDate(new Date());
        pushBoardDataProcessDTO.setBusinessType("AOI");
        pushBoardDataProcessDTO.setPushNumber(1);
        processList.add(pushBoardDataProcessDTO);
        List<CustomerDataLogDTO> tradeDataList = new ArrayList<>();
        Mockito.when(tradeDataLogService.getDataBySn(Mockito.anyList(), Mockito.anyList())).thenReturn(tradeDataList);
        param.setBeforeDay(7);
        service.syncProcessPushStatus(param);

        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setSn("777766600002");
        customerDataLogDTO.setProjectPhase("AOI");
        customerDataLogDTO.setStatus("CY");
        tradeDataList.add(customerDataLogDTO);
        service.syncProcessPushStatus(param);

        customerDataLogDTO.setSn("777766600001");
        CustomerDataLogDTO customerDataLogDTO1 = new CustomerDataLogDTO();
        customerDataLogDTO1.setSn("777766600001");
        customerDataLogDTO1.setProjectPhase("AOI");
        customerDataLogDTO1.setStatus("PY");
        tradeDataList.add(customerDataLogDTO1);
        service.syncProcessPushStatus(param);

        customerDataLogDTO1.setStatus("CN");
        service.syncProcessPushStatus(param);

        customerDataLogDTO1.setStatus("CY");
        service.syncProcessPushStatus(param);

        for (int i = 0; i <= 200; i++) {
            PushBoardDataProcessDTO dto = new PushBoardDataProcessDTO();
            dto.setPushStatus(1);
            dto.setSn(i + "");
            dto.setLastUpdatedDate(new Date());
            dto.setBusinessType("AOI");
            dto.setPushNumber(i%3);
            processList.add(dto);

            CustomerDataLogDTO cutom = new CustomerDataLogDTO();
            cutom.setSn(i+"");
            cutom.setProjectPhase("AOI");
            cutom.setStatus("PY");
            if(i==2){
                cutom.setStatus("CN");
                dto.setPushNumber(1);
            }
            if(i==3){
                cutom.setStatus("CY");
                dto.setPushNumber(2);
                CustomerDataLogDTO temp = new CustomerDataLogDTO();
                BeanUtils.copyProperties(cutom, temp);
                tradeDataList.add(temp);
            }
            if(i==4){
                cutom.setStatus("CN");
                dto.setPushNumber(3);
            }
            tradeDataList.add(cutom);
        }
        service.syncProcessPushStatus(param);
        Assert.assertTrue(true);
    }
    /* Ended by AICoder, pid:a7f47uf0403c9a4147310819c023c29e60e4c7a2 */

    /* Started by AICoder, pid:67519hace7j5a4314f9c097040fece2a87a3810e */
    @Test
    public void testInsertBoardProcessDataBatch() {
        // Initialize list with ArrayList for better performance
        List<PushBoardDataProcessDTO> list = new ArrayList<>();

        // Reuse the same instance of PushBoardDataProcessDTO if possible
        PushBoardDataProcessDTO dto = new PushBoardDataProcessDTO();

        for (int i = 0; i < 45; i++) {
            list.add(dto);
        }

        // Ensure service is not null before calling methods on it
        int result = service.insertBoardProcessDataBatch(null);
        Assert.assertEquals(0, result); // Assuming 0 indicates no records inserted

        result = service.insertBoardProcessDataBatch(list);
        Assert.assertEquals(45, result); // Assuming the method returns the number of inserted records
    }

    /* Ended by AICoder, pid:67519hace7j5a4314f9c097040fece2a87a3810e */


    /* Started by AICoder, pid:6f68bq28edxe17b14b9c08ac61bdbc3b3a1420fe */
    @Test
    public void testScheduledInsertPushProcessTask_InvalidCustomerName() {
        PushBoardDataProcessDTO dto = new PushBoardDataProcessDTO();
        dto.setCustomerName("");
        dto.setBusinessTypeList(Arrays.asList("type1", "type2"));

        assertThrows(MesBusinessException.class, () -> {
            service.scheduledInsertPushProcessTask(dto);
        });
    }

    @Test
    public void testScheduledInsertPushProcessTask_EmptyBusinessTypeList() {
        PushBoardDataProcessDTO dto = new PushBoardDataProcessDTO();
        dto.setCustomerName("customer1");
        dto.setBusinessTypeList(Collections.emptyList());

        assertThrows(MesBusinessException.class, () -> {
            service.scheduledInsertPushProcessTask(dto);
        });
    }

    @Test
    public void testScheduledInsertPushProcessTask_NoDataToProcess() {
        PushBoardDataProcessDTO dto = new PushBoardDataProcessDTO();
        dto.setCustomerName("customer1");
        dto.setBusinessTypeList(Arrays.asList("type1", "type2"));

        when(pushBoardDataDetailRepository.queryNeedInsertProcessList(any(PushBoardDataProcessDTO.class)))
                .thenReturn(Collections.emptyList());

        service.scheduledInsertPushProcessTask(dto);

        verify(pushBoardDataDetailRepository, times(1)).queryNeedInsertProcessList(any(PushBoardDataProcessDTO.class));
        verify(pushBoardDataProcessRepository, never()).batchInsertIfNotExists(anyList());
    }

    @Test
    public void testScheduledInsertPushProcessTask_ProcessData() {
        PushBoardDataProcessDTO dto = new PushBoardDataProcessDTO();
        dto.setCustomerName("customer1");
        dto.setBusinessTypeList(Arrays.asList("type1", "type2"));

        PushBoardDataDetailDTO detailDTO = new PushBoardDataDetailDTO();
        detailDTO.setProdplanId("prodplan1");
        detailDTO.setSn("sn1");

        List<PushBoardDataDetailDTO> detailList = Arrays.asList(detailDTO);

        when(pushBoardDataDetailRepository.queryNeedInsertProcessList(any(PushBoardDataProcessDTO.class)))
                .thenReturn(detailList);

        when(idGenerator.snowFlakeIdStr()).thenReturn("id1", "id2");

        service.scheduledInsertPushProcessTask(dto);
        assertTrue(Objects.nonNull(service));

        dto.setRows(1);
        service.scheduledInsertPushProcessTask(dto);
    }
    /*Ended by AICoder, pid:6f68bq28edxe17b14b9c08ac61bdbc3b3a1420fe*/

    /* Started by AICoder, pid:29f57196c5k41c9148c0087d6066d1190f837465 */
    @Test
    public void updateBoardDataProcessBatchTest(){
        service.updateBoardDataProcessBatch(null);
        List<PushBoardDataProcessDTO> updateList = new LinkedList<>();
        for (int i = 0; i < 300; i++) {
            PushBoardDataProcessDTO a1 = new PushBoardDataProcessDTO();
            updateList.add(a1);
        }
        service.updateBoardDataProcessBatch(updateList);
        Assert.assertEquals(updateList.size(),300);
        PowerMockito.when(RequestHeadValidationUtil.validaFactoryId()).thenReturn("43");
        PushBoardDataProcessDTO pto = new PushBoardDataProcessDTO();
        service.queryPushDataList(pto);
    }
    /* Ended by AICoder, pid:29f57196c5k41c9148c0087d6066d1190f837465 */

    /* Started by AICoder, pid:qceeba5859o0ef314a990a4f80d256377395d693 */
    @Test
    public void queryPushDataListTest(){
        PowerMockito.when(RequestHeadValidationUtil.validaFactoryId()).thenReturn("55");
        PushBoardDataProcessDTO processDTO = new PushBoardDataProcessDTO();
        List<PushBoardDataProcessDTO> list = service.queryPushDataList(processDTO);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }
    /* Ended by AICoder, pid:qceeba5859o0ef314a990a4f80d256377395d693 */

    @Test
    public void testGetDataBySnAndBusinessType() {
        // Case 1: Both lists empty
        List<PushBoardDataProcessDTO> result = service.getDataBySnAndBusinessType(new ArrayList<>(), new ArrayList<>());
        Assert.assertTrue(result.isEmpty());

        // Case 2: Normal input, repository returns data
        List<String> snList = Arrays.asList("SN1", "SN2");
        List<String> businessTypeList = Arrays.asList("TYPE1", "TYPE2");
        List<PushBoardDataProcessDTO> mockResult = new ArrayList<>();
        PushBoardDataProcessDTO dto = new PushBoardDataProcessDTO();
        dto.setSn("SN1");
        dto.setBusinessType("TYPE1");
        mockResult.add(dto);
        Mockito.when(pushBoardDataProcessRepository.getDataBySnAndBusinessType(snList, businessTypeList)).thenReturn(mockResult);

        result = service.getDataBySnAndBusinessType(snList, businessTypeList);
        Assert.assertEquals(1, result.size());
        Assert.assertEquals("SN1", result.get(0).getSn());
        Assert.assertEquals("TYPE1", result.get(0).getBusinessType());

        // Case 3: snList not empty, businessTypeList empty
        result = service.getDataBySnAndBusinessType(snList, new ArrayList<>());
        Assert.assertTrue(result.isEmpty());

        // Case 4: snList empty, businessTypeList not empty
        result = service.getDataBySnAndBusinessType(new ArrayList<>(), businessTypeList);
        Assert.assertTrue(result.isEmpty());
    }

}
