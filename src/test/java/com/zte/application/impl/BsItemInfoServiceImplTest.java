package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.domain.model.BsItemInfoRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.MtlSystemItemsDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.model.RetCode;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;


@RunWith(PowerMockRunner.class)
@PrepareForTest({DatawbRemoteService.class, SpringContextUtil.class})
public class BsItemInfoServiceImplTest {

    @InjectMocks
    BsItemInfoServiceImpl service;

    @Mock
    private BsItemInfoRepository bsItemInfoRepository;

    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;

    @Test
    public void getItemNameAndPCBVersion() {
        try{
            service.getItemNameAndPCBVersion(null);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
        try{
            service.getItemNameAndPCBVersion(new ArrayList(1001));
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
        service.getItemNameAndPCBVersion(Lists.newArrayList("1"));
    }

    /*Started by AICoder, pid:6ea19v96fbk50ea146a8093ab0816370d9076cd1*/
    @Test
    public void testSyncIncrementalItem() throws Exception {
        PowerMockito.mockStatic(DatawbRemoteService.class, SpringContextUtil.class);
        PowerMockito.when(SpringContextUtil.getBean("bsItemInfoServiceImpl", BsItemInfoServiceImpl.class)).thenReturn(service);
        MtlSystemItemsDTO dto = new MtlSystemItemsDTO();
        dto.setEndSyncDate(new Date());
        List<MtlSystemItemsDTO> items = Arrays.asList(new MtlSystemItemsDTO(), new MtlSystemItemsDTO());
        // PowerMockito.when(bsItemInfoRepository.getIncrementalItem(anyBoolean(), any(MtlSystemItemsDTO.class))).thenReturn(items);
        try {
            service.syncIncrementalItem(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FOUND_ITEM_INFO, e.getMessage());
        }
        dto.setBeginSyncDate(new Date());
        dto.setEndSyncDate(null);
        try {
            service.syncIncrementalItem(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FOUND_ITEM_INFO, e.getMessage());
        }
        dto.setEndSyncDate(new Date());
        try {
            service.syncIncrementalItem(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FOUND_ITEM_INFO, e.getMessage());
        }
        dto.setBeginSyncDate(null);
        dto.setEndSyncDate(null);
        service.syncIncrementalItem(dto);
        dto.setSegment1("qqq");
        try {
            service.syncIncrementalItem(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FOUND_ITEM_INFO, e.getMessage());
        }
        dto.setBeginSyncDate(null);
        dto.setEndSyncDate(null);
        List<String> list = new ArrayList<>();
        list.add("eee");
        dto.setSegment1List(list);
        try {
            service.syncIncrementalItem(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FOUND_ITEM_INFO, e.getMessage());
        }
        dto.setBeginSyncDate(new Date());
        dto.setEndSyncDate(new Date());
        try {
            service.syncIncrementalItem(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NOT_FOUND_ITEM_INFO, e.getMessage());
        }
        dto.setBeginSyncDate(null);
        dto.setEndSyncDate(null);
        dto.setSegment1(null);
        dto.setSegment1List(new ArrayList<>());
        service.syncIncrementalItem(dto);
        List<SysLookupValues> values = new ArrayList<>();
        SysLookupValues value = new SysLookupValues();
        value.setLookupMeaning("2024-01-01 00:00:00");
        values.add(value);
        dto.setBeginSyncDate(null);
        dto.setEndSyncDate(null);
        dto.setSegment1(null);
        dto.setSegment1List(new ArrayList<>());
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.anyMap())).thenReturn(values);
        service.syncIncrementalItem(dto);
        value.setLookupMeaning(null);
        dto.setBeginSyncDate(null);
        dto.setEndSyncDate(null);
        dto.setSegment1(null);
        dto.setSegment1List(new ArrayList<>());
        service.syncIncrementalItem(dto);
        List<MtlSystemItemsDTO> dtoList = new ArrayList<>();
        MtlSystemItemsDTO dto1 = new MtlSystemItemsDTO();
        dto1.setSegment1("Qqq");
        dto1.setEndSyncDate(new Date());
        dtoList.add(dto1);
        PowerMockito.when(DatawbRemoteService.getIncrementalItem(Mockito.any())).thenReturn(dtoList);
        dto.setBeginSyncDate(null);
        dto.setEndSyncDate(null);
        service.syncIncrementalItem(dto);
        service.syncIncrementalItem(dto);
    }
    /*Ended by AICoder, pid:6ea19v96fbk50ea146a8093ab0816370d9076cd1*/

    @Test
    public void checkItemNoIsValid() throws Exception {
        String itemNo = "111";
        PowerMockito.when(bsItemInfoRepository.checkItemCodeIsExist(Mockito.any())).thenReturn(null);
        Assert.assertNotNull(itemNo);
        service.checkItemNoIsValid(itemNo);
        PowerMockito.when(bsItemInfoRepository.checkItemCodeIsExist(Mockito.any())).thenReturn(1);
        service.checkItemNoIsValid(itemNo);

    }
}