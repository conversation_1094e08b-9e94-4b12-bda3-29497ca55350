package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.ResourceInfoDetailRepository;
import com.zte.interfaces.dto.ResourceInfoDetailDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.FileUtils;
import junit.framework.TestCase;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisHelper.class, RedisCacheUtils.class, SpringContextUtil.class, FileUtils.class, DateUtils.class,ResourceInfoServiceImpl.class})
public class ResourceGetAndExportDetailServiceImplTest extends TestCase {

    @InjectMocks
    private ResourceInfoDetailExportServiceImpl service;
    @Mock
    ResourceInfoDetailRepository repository;
    @Mock
    ResourceInfoDetailServiceImpl resourceInfoDetailService;

    @Test
    public void countExportTotal1() {
        ResourceInfoDetailDTO info = new ResourceInfoDetailDTO();
        info.setResourceType(MpConstant.NAL);
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_DRTAIL_RESOURCE_NO_ERROR, e.getMessage());
        }
        info.setResourceNo("123");
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }
        info.setResourceStr("123");
        info.setStartTime("2023-12-19 00:00:00");
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }
        info.setStartTime(null);
        info.setEndTime("2024-01-19 23:59:59");
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }
        info.setStartTime("2023-12-19 00:00:00");
        info.setEndTime("2025-01-19 23:59:59");
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_DRTAIL_TIME_INTERVAL_ERROR, e.getMessage());
        }
        info.setEndTime("2023-12-20 23:59:59");
        Page<ResourceInfoDetailDTO> page = new Page<>(info.getPage(), info.getRows());
        page.setParams(info);

        PowerMockito.when(repository.page(Mockito.any())).thenReturn(null);
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.EXPORT_DRTAIL_RESOURCE_NO_ERROR, e.getMessage());
        }
        service.countExportTotal(info);

        info.setStatus("123");
        service.countExportTotal(info);
    }

    @Test
    public void queryExportData() {
        ResourceInfoDetailDTO info = new ResourceInfoDetailDTO();
        info.setResourceNo("123");
        info.setStartTime("2023-12-19 00:00:00");
        info.setEndTime("2023-12-20 23:59:59");
        service.queryExportData(info, 1, 10);

        List<ResourceInfoDetailDTO> entityDTOList = new ArrayList<>();
        entityDTOList.add(info);
        PowerMockito.when(repository.page(Mockito.any())).thenReturn(entityDTOList);
        service.queryExportData(info, 1, 10);

        PowerMockito.when(resourceInfoDetailService.getLookUpVaulesList(Mockito.anyMap(),Mockito.any())).thenReturn(null);
        try {
            service.countExportTotal(info);
        }catch (Exception e){
            Assert.assertEquals(MessageId.QUERY_EXPORT_ERROR, e.getMessage());
        }
    }

}