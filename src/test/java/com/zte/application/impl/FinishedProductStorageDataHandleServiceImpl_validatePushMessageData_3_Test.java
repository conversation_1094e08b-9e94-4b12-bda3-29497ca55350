/*Started by AICoder, pid:ra2b3ua0af5cccf1430f0b1dc2929c19b9c368f8*/
package com.zte.application.impl;
import com.google.common.collect.Lists;
import com.zte.interfaces.dto.FinishedProductStorageDTO;
import com.zte.interfaces.dto.FinishedProductStorageItemDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
public class FinishedProductStorageDataHandleServiceImpl_validatePushMessageData_3_Test {

    @InjectMocks
    private FinishedProductStorageDataHandleServiceImpl service;

    @Mock
    private FinishedProductStorageDTO finishedProductStorage;

    @Mock
    private FinishedProductStorageItemDTO storageItemDTO;

    @Before
    public void setUp() {
        // Setup code if needed
    }

    @Test
    public void testValidatePushMessageData_NotInstanceOfFinishedProductStorageDTO() {
        String currProcess = "testProcess";
        Object pushMessageData = new Object();

        boolean result = service.validatePushMessageData(currProcess, pushMessageData);

        assertFalse(result);
    }

    @Test
    public void testValidatePushMessageData_CategoryIsBlank() {
        when(finishedProductStorage.getCategory()).thenReturn("");

        boolean result = service.validatePushMessageData("testProcess", finishedProductStorage);

        assertFalse(result);
    }

    @Test
    public void testValidatePushMessageData_ManufactureOrderNoIsBlank() {
        when(finishedProductStorage.getCategory()).thenReturn("category");
        when(finishedProductStorage.getManufactureOrderNo()).thenReturn("");

        boolean result = service.validatePushMessageData("testProcess", finishedProductStorage);

        assertFalse(result);
    }

    @Test
    public void testValidatePushMessageData_InvalidMaterialQuality() {
        when(finishedProductStorage.getCategory()).thenReturn("category");
        when(finishedProductStorage.getManufactureOrderNo()).thenReturn("orderNo");
        when(finishedProductStorage.getMaterialQuality()).thenReturn(3);

        boolean result = service.validatePushMessageData("testProcess", finishedProductStorage);

        assertFalse(result);
    }

    @Test
    public void testValidatePushMessageData_QuantityCompletedIsNull() {
        when(finishedProductStorage.getCategory()).thenReturn("category");
        when(finishedProductStorage.getManufactureOrderNo()).thenReturn("orderNo");
        when(finishedProductStorage.getMaterialQuality()).thenReturn(1);
        when(finishedProductStorage.getQuantityCompleted()).thenReturn(null);

        boolean result = service.validatePushMessageData("testProcess", finishedProductStorage);

        assertFalse(result);
    }

    @Test
    public void testValidatePushMessageData_MaterialBillListIsEmpty() {
        when(finishedProductStorage.getCategory()).thenReturn("category");
        when(finishedProductStorage.getManufactureOrderNo()).thenReturn("orderNo");
        when(finishedProductStorage.getMaterialQuality()).thenReturn(1);
        when(finishedProductStorage.getQuantityCompleted()).thenReturn(100);
        when(finishedProductStorage.getMaterialBillList()).thenReturn(Collections.emptyList());

        boolean result = service.validatePushMessageData("testProcess", finishedProductStorage);

        assertTrue(result);
    }

    @Test
    public void testValidatePushMessageData_ItemMaterialCategoryIsBlank() {
        List<FinishedProductStorageItemDTO> materialBillList = Arrays.asList(storageItemDTO);
        when(finishedProductStorage.getCategory()).thenReturn("category");
        when(finishedProductStorage.getManufactureOrderNo()).thenReturn("orderNo");
        when(finishedProductStorage.getMaterialQuality()).thenReturn(1);
        when(finishedProductStorage.getQuantityCompleted()).thenReturn(100);
        when(finishedProductStorage.getMaterialBillList()).thenReturn(materialBillList);
        when(storageItemDTO.getMaterialCategory()).thenReturn("");
        when(storageItemDTO.getMaterialSign()).thenReturn("sign");
        when(storageItemDTO.getMaterialBom()).thenReturn("bom");
        when(storageItemDTO.getMaterialName()).thenReturn("name");

        boolean result = service.validatePushMessageData("testProcess", finishedProductStorage);

        assertTrue(result);
    }

    @Test
    public void testValidatePushMessageData_ItemMaterialBomIsBlank() {
        List<FinishedProductStorageItemDTO> materialBillList = Arrays.asList(storageItemDTO);
        when(finishedProductStorage.getCategory()).thenReturn("category");
        when(finishedProductStorage.getManufactureOrderNo()).thenReturn("orderNo");
        when(finishedProductStorage.getMaterialQuality()).thenReturn(1);
        when(finishedProductStorage.getQuantityCompleted()).thenReturn(100);
        when(finishedProductStorage.getMaterialBillList()).thenReturn(materialBillList);
        when(storageItemDTO.getMaterialCategory()).thenReturn("category");
        when(storageItemDTO.getMaterialSign()).thenReturn("sign");
        when(storageItemDTO.getMaterialBom()).thenReturn("");
        when(storageItemDTO.getMaterialName()).thenReturn("name");

        boolean result = service.validatePushMessageData("testProcess", finishedProductStorage);

        assertTrue(result);
    }

    @Test
    public void testValidatePushMessageData_ItemMaterialNameIsBlank() {
        List<FinishedProductStorageItemDTO> materialBillList = Arrays.asList(storageItemDTO);
        when(finishedProductStorage.getCategory()).thenReturn("category");
        when(finishedProductStorage.getManufactureOrderNo()).thenReturn("orderNo");
        when(finishedProductStorage.getMaterialQuality()).thenReturn(1);
        when(finishedProductStorage.getQuantityCompleted()).thenReturn(100);
        when(finishedProductStorage.getMaterialBillList()).thenReturn(materialBillList);
        when(storageItemDTO.getMaterialCategory()).thenReturn("category");
        when(storageItemDTO.getMaterialSign()).thenReturn("sign");
        when(storageItemDTO.getMaterialBom()).thenReturn("bom");
        when(storageItemDTO.getMaterialName()).thenReturn("");

        boolean result = service.validatePushMessageData("testProcess", finishedProductStorage);

        assertTrue(result);
    }

    @Test
    public void testValidatePushMessageData_ValidData() {
        List<FinishedProductStorageItemDTO> materialBillList = Arrays.asList(storageItemDTO);
        when(finishedProductStorage.getCategory()).thenReturn("category");
        when(finishedProductStorage.getManufactureOrderNo()).thenReturn("orderNo");
        when(finishedProductStorage.getMaterialQuality()).thenReturn(1);
        when(finishedProductStorage.getQuantityCompleted()).thenReturn(100);
        when(finishedProductStorage.getMaterialBillList()).thenReturn(materialBillList);
        when(storageItemDTO.getMaterialCategory()).thenReturn("category");
        when(storageItemDTO.getMaterialSign()).thenReturn("sign");
        when(storageItemDTO.getMaterialBom()).thenReturn("bom");
        when(storageItemDTO.getMaterialName()).thenReturn("name");

        boolean result = service.validatePushMessageData("testProcess", finishedProductStorage);

        assertTrue(result);
    }

    @Test
    public void testValidatePushMessageData_ChildNotEmpty_True() {
        List<FinishedProductStorageItemDTO> materialBillList = Arrays.asList(storageItemDTO);
        when(finishedProductStorage.getCategory()).thenReturn("category");
        when(finishedProductStorage.getManufactureOrderNo()).thenReturn("orderNo");
        when(finishedProductStorage.getMaterialQuality()).thenReturn(1);
        when(finishedProductStorage.getQuantityCompleted()).thenReturn(100);
        when(finishedProductStorage.getMaterialBillList()).thenReturn(materialBillList);
        when(storageItemDTO.getMaterialCategory()).thenReturn("category");
        when(storageItemDTO.getMaterialSign()).thenReturn("sign");
        when(storageItemDTO.getMaterialBom()).thenReturn("bom");
        when(storageItemDTO.getMaterialName()).thenReturn("name");
        when(storageItemDTO.getMaterialBillList()).thenReturn(Lists.newArrayList(storageItemDTO)).thenReturn(Lists.newArrayList());

        boolean result = service.validatePushMessageData("testProcess", finishedProductStorage);

        assertTrue(result);
    }

    @Test
    public void testValidatePushMessageData_ChildNotEmpty_False() {
        List<FinishedProductStorageItemDTO> materialBillList = Arrays.asList(storageItemDTO);
        when(finishedProductStorage.getCategory()).thenReturn("category");
        when(finishedProductStorage.getManufactureOrderNo()).thenReturn("orderNo");
        when(finishedProductStorage.getMaterialQuality()).thenReturn(1);
        when(finishedProductStorage.getQuantityCompleted()).thenReturn(100);
        when(finishedProductStorage.getMaterialBillList()).thenReturn(materialBillList);
        when(storageItemDTO.getMaterialCategory()).thenReturn("category");
        when(storageItemDTO.getMaterialSign()).thenReturn("sign");
        when(storageItemDTO.getMaterialBom()).thenReturn("bom");
        when(storageItemDTO.getMaterialName()).thenReturn("name").thenReturn(null);
        when(storageItemDTO.getMaterialBillList()).thenReturn(Lists.newArrayList(storageItemDTO)).thenReturn(Lists.newArrayList());

        boolean result = service.validatePushMessageData("testProcess", finishedProductStorage);

        assertTrue(result);
    }
}
/*Ended by AICoder, pid:ra2b3ua0af5cccf1430f0b1dc2929c19b9c368f8*/