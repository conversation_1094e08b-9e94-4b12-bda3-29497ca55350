package com.zte.application.impl;

import com.zte.common.model.MessageId;
import com.zte.domain.model.AbnormalMaterialBillDetail;
import com.zte.domain.model.AbnormalMaterialBillDetailRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.interfaces.dto.AbnormalMaterialBillDetailDTO;
import com.zte.interfaces.dto.AbnormalMaterialBillDetailPageQueryDTO;
import com.zte.interfaces.dto.AbnormalMaterialBillHeadPageQueryDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.doThrow;

/**
 * 异常物料单据详表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-28 17:33:41
 */
@RunWith(PowerMockRunner.class)
public class AbnormalMaterialBillDetailServiceImplTest extends BaseTestCase {

    @InjectMocks
    private AbnormalMaterialBillDetailServiceImpl service;
    @Mock
    private AbnormalMaterialBillDetailRepository repository;
    @Mock
    private CloudDiskHelper cloudDiskHelper;
    @Mock
    private HrmUserCenterServiceImpl hrmUserCenterService;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;

    @Test
    public void testQueryPage() throws Exception {
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(new ArrayList<AbnormalMaterialBillDetailPageQueryDTO>());
        Assert.assertNotNull(service.queryPage(new AbnormalMaterialBillDetailPageQueryDTO()));
    }

    @Test
    public void queryAbnormalBillDetail() throws Exception {
        AbnormalMaterialBillDetailPageQueryDTO query = new AbnormalMaterialBillDetailPageQueryDTO();
        query.setRows(501);
        try {
            service.queryAbnormalBillDetail(query);
        }catch(Exception e){
            Assert.assertEquals(MessageId.SINGLE_PAGE_QUERY_CANNOT_EXCEED_500_ENTRIES, e.getMessage());
        }
        query.setRows(50);
        query.setLastUpdatedDateEnd("2024-04-10 08:30:00");
        try {
            service.queryAbnormalBillDetail(query);
        }catch(Exception e){
            Assert.assertEquals(MessageId.QUERY_LAST_TIME_AND_BILL_NO_IS_NULL, e.getMessage());
        }
        query.setLastUpdatedDateEnd("");
        query.setLastUpdatedDateStart("2024-04-10 08:30:00");
        try {
            service.queryAbnormalBillDetail(query);
        }catch(Exception e){
            Assert.assertEquals(MessageId.QUERY_LAST_TIME_AND_BILL_NO_IS_NULL, e.getMessage());
        }
        query.setBillNo("TEST");
        PowerMockito.when(repository.getAbnormalBillDetail(Mockito.any())).thenReturn(new ArrayList<AbnormalMaterialBillDetailPageQueryDTO>());
        Assert.assertNotNull(service.queryAbnormalBillDetail(query));
        query.setLastUpdatedDateEnd("2024-04-10 08:30:00");
        query.setLastUpdatedDateStart("2023-04-10 08:30:00");
        try {
            service.queryAbnormalBillDetail(query);
        }catch(Exception e){
            Assert.assertEquals(MessageId.LAST_TIME_INTERVAL_MORE_THAN_HALF_YEAR, e.getMessage());
        }
        query.setLastUpdatedDateEnd("2024-04-10 08:30:00");
        query.setLastUpdatedDateStart("2024-04-09 08:30:00");
        Assert.assertNotNull(service.queryAbnormalBillDetail(query));
    }

    @Test
    public void queryAbnormalBillDetailTwo() throws Exception {
        AbnormalMaterialBillDetailPageQueryDTO query = new AbnormalMaterialBillDetailPageQueryDTO();
        query.setRows(1);
        query.setPage(1);
        query.setLastUpdatedDateStart("2024-04-01 08:30:00");
        query.setBillNo("test");
        query.setLastUpdatedDateEnd("2024-04-09 08:30:00");

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("00286569");
        hrmPersonInfoDTOMap.put("00286569", hrmPersonInfoDTO);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);

        List<AbnormalMaterialBillDetailPageQueryDTO> list = new ArrayList<>();
        AbnormalMaterialBillDetailPageQueryDTO abnormalMaterialBillHead = new AbnormalMaterialBillDetailPageQueryDTO();
        abnormalMaterialBillHead.setBillNo("test");
        abnormalMaterialBillHead.setBillStatus(1);
        abnormalMaterialBillHead.setHandlerEmp("00286569");
        abnormalMaterialBillHead.setCreateBy("00286569");
        list.add(abnormalMaterialBillHead);
        List<SysLookupValues> statusList = new ArrayList<>();
        PowerMockito.when(sysLookupValuesRepository.selectValuesByType(Mockito.any())).thenReturn(statusList);
        PowerMockito.when(repository.getAbnormalBillDetail(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(service.queryAbnormalBillDetail(query));
    }

    @Test
    public void queryAbnormalBillDetailThree() throws Exception {
        AbnormalMaterialBillDetailPageQueryDTO query = new AbnormalMaterialBillDetailPageQueryDTO();
        query.setRows(1);
        query.setPage(1);
        query.setCreateDateEnd("2024-04-10 08:30:00");
        query.setBillNo("test");
        query.setCreateDateStart("2024-04-09 08:30:00");
        List<AbnormalMaterialBillDetailPageQueryDTO> list = new ArrayList<>();
        AbnormalMaterialBillDetailPageQueryDTO abnormalMaterialBillHead = new AbnormalMaterialBillDetailPageQueryDTO();
        abnormalMaterialBillHead.setBillNo("test");
        abnormalMaterialBillHead.setBillStatus(1);
        list.add(abnormalMaterialBillHead);
        PowerMockito.when(repository.getAbnormalBillDetail(Mockito.any())).thenReturn(list);

        List<SysLookupValues> statusList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("1");
        sysLookupValues.setDescriptionChin("拟制中");
        statusList.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesRepository.selectValuesByType(Mockito.any())).thenReturn(statusList);
        Assert.assertNotNull(service.queryAbnormalBillDetail(query));
    }

    @Test
    public void viewFile() throws Exception {
        PowerMockito.when(cloudDiskHelper.getFileDownloadUrl(anyString(), anyString(), anyString()))
                .thenReturn("111");
        try {
            Assert.assertNotNull(service.viewFile("111","111","00286569"));
        } catch (Exception e) {
            Assert.assertEquals(null,e.getMessage());
        }
    }

    @Test
    public void downloadFile() throws Exception {
        try {
            Assert.assertNull(service.downloadFile("", "", ""));
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }

    @Test
    public void uploadFile() throws Exception {
        String empNo = "1111";
        MultipartFile file = new MultipartFile() {
            @Override
            public String getName() {
                return "ceshi.xlsx";
            }

            @Override
            public String getOriginalFilename() {
                return "ceshi.xlsx";
            }

            @Override
            public String getContentType() {
                return null;
            }

            @Override
            public boolean isEmpty() {
                return false;
            }

            @Override
            public long getSize() {
                return 0;
            }

            @Override
            public byte[] getBytes() throws IOException {
                return new byte[0];
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return null;
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {

            }
        };

        String fileId = "test";
        PowerMockito.when(cloudDiskHelper.fileUpload(file, empNo)).thenReturn(fileId);
        service.uploadFile(".xls",empNo, file);
        doThrow(new Exception()).when(cloudDiskHelper).fileUpload(file, empNo);
        try {
            service.uploadFile(".xls",empNo, file);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.FAIL_TO_UPLOAD_FILE));
        }
    }

    @Test
    public void checkFileType() throws Exception {
        MultipartFile file = new MultipartFile() {
            @Override
            public String getName() {
                return null;
            }

            @Override
            public String getOriginalFilename() {
                return "123.cc";
            }

            @Override
            public String getContentType() {
                return null;
            }

            @Override
            public boolean isEmpty() {
                return false;
            }

            @Override
            public long getSize() {
                return 0;
            }

            @Override
            public byte[] getBytes() throws IOException {
                return new byte[0];
            }

            @Override
            public InputStream getInputStream() throws IOException {
                return null;
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {

            }
        };
        try {
            service.checkFileType(file);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.FILE_TYPE_ILLEGAL));
        }
    }

    @Test
    public void checkFileTypeTwo() throws Exception {
        String fileType = "test";
        service.checkFileType(null);
        Assert.assertNotNull(fileType);
    }
    @Test
    public void deleteFile() throws Exception {
        AbnormalMaterialBillDetailDTO abnormalMaterialBillDetailDTO = new AbnormalMaterialBillDetailDTO();
        Assert.assertNotNull(service.deleteFile(abnormalMaterialBillDetailDTO));
    }

    @Test
    public void testCountExportTotal() {
        PowerMockito.when(repository.selectExportCount(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(service.countExportTotal(new AbnormalMaterialBillDetailPageQueryDTO()));
    }

    @Test
    public void queryExportTotal() {
        PowerMockito.when(repository.selectExportCount(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(service.queryExportTotal(new AbnormalMaterialBillDetailPageQueryDTO()));
    }

    @Test
    public void setUserName() throws Exception {
        List<AbnormalMaterialBillDetailPageQueryDTO> list = new ArrayList<>();
        AbnormalMaterialBillDetailPageQueryDTO dto = new AbnormalMaterialBillDetailPageQueryDTO();
        dto.setFinderEmp("00286569");
        list.add(dto);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("00286569");
        hrmPersonInfoDTOMap.put("00286569", hrmPersonInfoDTO);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);

        Assert.assertNull(Whitebox.invokeMethod(service, "setUserName", list));
    }

    @Test
    public void testQueryExportData() {
        AbnormalMaterialBillDetailPageQueryDTO query = new AbnormalMaterialBillDetailPageQueryDTO();
        try{
            service.queryExportData(query,1,10);
        }catch(Exception e){
            Assert.assertEquals(MessageId.QUERY_TIME_AND_BILL_NO_IS_NULL, e.getMessage());
        }
        query.setCreateDateEnd("2024-04-10 08:30:00");
        try{
            service.queryExportData(query,1,10);
        }catch(Exception e){
            Assert.assertEquals(MessageId.QUERY_TIME_AND_BILL_NO_IS_NULL, e.getMessage());
        }
        query.setCreateDateEnd("");
        query.setCreateDateStart("2024-04-10 08:30:00");
        query.setBillNo("test");
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(new ArrayList<AbnormalMaterialBillDetailPageQueryDTO>());
        Assert.assertNotNull(service.queryExportData(query,1,10));
        query.setCreateDateEnd("2024-04-10 08:30:00");
        query.setCreateDateStart("2023-04-10 08:30:00");
        try{
            service.queryExportData(query,1,10);
        }catch(Exception e){
            Assert.assertEquals(MessageId.TIME_INTERVAL_MORE_THAN_HALF_YEAR, e.getMessage());
        }
    }
}

