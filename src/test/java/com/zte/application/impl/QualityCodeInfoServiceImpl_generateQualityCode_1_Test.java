/*Started by AICoder, pid:62f982b1e267cb2142ec09d751d6c61698a89850*/
package com.zte.application.impl;
import com.zte.application.TradeDataLogService;
import com.zte.domain.model.QualityCodeInfoRepository;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.QualityCodeInfoDTO;
import com.zte.interfaces.dto.SaveQualityCodeDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class QualityCodeInfoServiceImpl_generateQualityCode_1_Test {

    @Mock
    private TradeDataLogService tradeDataLogService;

    @Mock
    private QualityCodeInfoRepository qualityCodeInfoRepository;

    @InjectMocks
    private QualityCodeInfoServiceImpl qualityCodeInfoService;

    private SaveQualityCodeDTO dto;

    @Before
    public void setUp() {
        dto = new SaveQualityCodeDTO();
    }

    @Test
    public void testGenerateQualityCode_NoData() throws Exception {
        when(qualityCodeInfoRepository.getQualityCodeNotGenerated(any(SaveQualityCodeDTO.class)))
                .thenReturn(Collections.emptyList());

        qualityCodeInfoService.generateQualityCode(dto);

        verify(qualityCodeInfoRepository, times(1)).getQualityCodeNotGenerated(any(SaveQualityCodeDTO.class));
        verify(tradeDataLogService, never()).pushDataOfExceptionRollback(anyList());
    }

    @Test
    public void testGenerateQualityCode_WithData() throws Exception {
        List<QualityCodeInfoDTO> qualityCodeInfoDTOList = new ArrayList<>();
        QualityCodeInfoDTO qualityCodeInfoDTO = new QualityCodeInfoDTO();
        qualityCodeInfoDTO.setId("1");
        qualityCodeInfoDTO.setTaskNo("task1");
        qualityCodeInfoDTO.setNodeSn("sn1");
        // Ensure the date is of type Date
        qualityCodeInfoDTO.setLastUpdatedDate(new Date());
        qualityCodeInfoDTOList.add(qualityCodeInfoDTO);

        when(qualityCodeInfoRepository.getQualityCodeNotGenerated(any(SaveQualityCodeDTO.class)))
                .thenReturn(qualityCodeInfoDTOList)
                .thenReturn(Collections.emptyList());

        doAnswer((Answer<Void>) invocation -> {
            List<CustomerDataLogDTO> dataList = invocation.getArgument(0);
            assertEquals(1, dataList.size());
            CustomerDataLogDTO customerDataLogDTO = dataList.get(0);
            assertEquals("task1", customerDataLogDTO.getTaskNo());
            assertEquals("sn1", customerDataLogDTO.getSn());
            return null;
        }).when(tradeDataLogService).pushDataOfExceptionRollback(anyList());

        qualityCodeInfoService.generateQualityCode(dto);

        verify(qualityCodeInfoRepository, times(2)).getQualityCodeNotGenerated(any(SaveQualityCodeDTO.class));
        verify(tradeDataLogService, times(1)).pushDataOfExceptionRollback(anyList());
    }
}
/*Ended by AICoder, pid:62f982b1e267cb2142ec09d751d6c61698a89850*/