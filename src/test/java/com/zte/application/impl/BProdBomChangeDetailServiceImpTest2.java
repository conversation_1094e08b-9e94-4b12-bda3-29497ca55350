/*Started by AICoder, pid:3b51c2d33aw7e351435f09d291a4531ff738496d*/
package com.zte.application.impl;
import com.zte.application.BProdBomDetailService;
import com.zte.domain.model.BProdBomChangeDetailRepository;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.interfaces.dto.BProdBomDetailDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class BProdBomChangeDetailServiceImpTest2 {

    @InjectMocks
    private BProdBomChangeDetailServiceImpl bProdBomChangeDetailService;

    @Mock
    private BProdBomDetailService bProdBomDetailService;

    @Mock
    private BProdBomChangeDetailRepository bProdBomChangeDetailRepository;

    private BProdBomChangeDetailDTO params;

    @Before
    public void setUp() {
        params = new BProdBomChangeDetailDTO();
        params.setProdplanId("123");
        params.setItemCode("ABC");
    }

    @Test(expected = MesBusinessException.class)
    public void testSelectMBomOriginalWithEmptyProdplanId() {
        params.setProdplanId(null);
        bProdBomChangeDetailService.selectMBomOriginal(params);
    }

    @Test(expected = MesBusinessException.class)
    public void testSelectMBomOriginalWithEmptyItemCode() {
        params.setItemCode(null);
        bProdBomChangeDetailService.selectMBomOriginal(params);
    }

    @Test
    public void testSelectMBomOriginalWithNoChangesAndNoOriginal() {
        when(bProdBomChangeDetailRepository.queryBProdBomDetailChangeByProdplan(any(BProdBomChangeDetailDTO.class)))
                .thenReturn(Collections.emptyList());
        when(bProdBomDetailService.getBProdBomDetailItemList(any(BProdBomDetailDTO.class)))
                .thenReturn(Collections.emptyList());

        List<BProdBomChangeDetailDTO> result = bProdBomChangeDetailService.selectMBomOriginal(params);

        assertEquals(0, result.size());
    }

    @Test
    public void testSelectMBomOriginalWithChangesAndNoOriginal() {
        BProdBomChangeDetailDTO changeDto = new BProdBomChangeDetailDTO();
        when(bProdBomChangeDetailRepository.queryBProdBomDetailChangeByProdplan(any(BProdBomChangeDetailDTO.class)))
                .thenReturn(Collections.singletonList(changeDto));
        when(bProdBomDetailService.getBProdBomDetailItemList(any(BProdBomDetailDTO.class)))
                .thenReturn(Collections.emptyList());

        List<BProdBomChangeDetailDTO> result = bProdBomChangeDetailService.selectMBomOriginal(params);

        assertEquals(1, result.size());
        assertEquals(changeDto, result.get(0));
    }

    @Test
    public void testSelectMBomOriginalWithChangesAndOriginal() {
        BProdBomChangeDetailDTO changeDto = new BProdBomChangeDetailDTO();
        BProdBomDetailDTO detailDto = new BProdBomDetailDTO();
        detailDto.setOriginalProductCode("OPC");
        detailDto.setItemCode("IC");

        when(bProdBomChangeDetailRepository.queryBProdBomDetailChangeByProdplan(any(BProdBomChangeDetailDTO.class)))
                .thenReturn(Collections.singletonList(changeDto));
        when(bProdBomDetailService.getBProdBomDetailItemList(any(BProdBomDetailDTO.class)))
                .thenReturn(Collections.singletonList(detailDto));

        List<BProdBomChangeDetailDTO> result = bProdBomChangeDetailService.selectMBomOriginal(params);

        assertEquals(2, result.size());
        assertEquals(changeDto, result.get(0));
        assertEquals("OPC", result.get(1).getOriginalProductCode());
        assertEquals("IC", result.get(1).getOriginalItemCode());
    }
}
/*Ended by AICoder, pid:3b51c2d33aw7e351435f09d291a4531ff738496d*/