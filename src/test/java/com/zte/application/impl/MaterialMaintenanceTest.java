package com.zte.application.impl;


import com.zte.common.utils.Constant;
import com.zte.domain.model.MaterialMaintenanceRepository;
import com.zte.interfaces.dto.MaterialMaintenanceDTO;
import com.zte.interfaces.dto.SolderInfo;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.util.web.HttpHeaderUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;

@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class, HttpHeaderUtil.class})
public class MaterialMaintenanceTest extends BaseTestCase {

    @InjectMocks
    private MaterialMaintenanceServiceImpl materialService;

    @Mock
    private MaterialMaintenanceRepository materialRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.mockStatic(HttpHeaderUtil.class);

    }

    @Test
    public void queryMaterial() throws Exception {
        MaterialMaintenanceDTO page = new MaterialMaintenanceDTO();
        materialService.queryMaterial(page);
        page.setStartDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2021-07-09 15:30:40"));
        materialService.queryMaterial(page);
        page.setEndDate(new Date());
        page.setStartDate(new Date());
        materialService.queryMaterial(page);
        Assert.assertNotNull(page);
    }

    @Test
    public void updateMaterial() {
        MaterialMaintenanceDTO page = new MaterialMaintenanceDTO();
        Assert.assertThrows(Exception.class, () -> materialService.updateMaterial(page));
        page.setMaintenanceType(Constant.GLUE_ITEM);
        Assert.assertThrows(Exception.class, () -> materialService.updateMaterial(page));
        page.setStorageMethod(Constant.GLUE_FREEZING);
        Assert.assertThrows(Exception.class, () -> materialService.updateMaterial(page));
        page.setThawingDuration(1);
        Assert.assertThrows(Exception.class, () -> materialService.updateMaterial(page));
        page.setStorageMethod(Constant.GLUE_NORMAL);
        Assert.assertThrows(Exception.class, () -> materialService.updateMaterial(page));
        page.setItemCode(Constant.GLUE_NORMAL);
        materialService.updateMaterial(page);
        materialService.addMaterial(page);
        page.setMaintenanceType(Constant.GLUE_BARCODE);
        Assert.assertThrows(Exception.class, () -> materialService.updateMaterial(page));
        page.setBarcode(Constant.GLUE_BARCODE);
        materialService.updateMaterial(page);
        page.setMaintenanceType(Constant.GLUE_ITEM_SUP);
        page.setItemCode(null);
        Assert.assertThrows(Exception.class, () -> materialService.updateMaterial(page));
        page.setSupplier(Constant.GLUE_ITEM_SUP);
        Assert.assertThrows(Exception.class, () -> materialService.updateMaterial(page));
        page.setItemCode(Constant.GLUE_NORMAL);
        materialService.updateMaterial(page);
        PowerMockito.when(materialRepository.checkExist(page)).thenReturn(1);
        Assert.assertThrows(Exception.class, () -> materialService.updateMaterial(page));
    }

    @Test
    public void delMaterial() {
        PowerMockito.when(materialRepository.delMaterial(any())).thenReturn(1);
        Assert.assertEquals(1, materialService.delMaterial(new MaterialMaintenanceDTO()));
    }

    @Test
    public void queryMaterialByItemNo_NoResults() {
        String itemNo = "ITEM456";
        String barcode = "barcode1";

        PowerMockito.when(materialRepository.queryMaterialByItemNoOrBarcode("", barcode)).thenReturn(new ArrayList<>());
        PowerMockito.when(materialRepository.queryMaterialByItemNoOrBarcode(itemNo, "")).thenReturn(new ArrayList<>());

        // 执行方法
        List<MaterialMaintenanceDTO> result2 = materialService.queryMaterialByItemNoOrBarcode(itemNo, barcode);

        List<MaterialMaintenanceDTO> materialMaintenanceDTOS = new ArrayList<>();
        MaterialMaintenanceDTO materialMaintenanceDTO = new MaterialMaintenanceDTO();
        materialMaintenanceDTOS.add(materialMaintenanceDTO);
        PowerMockito.when(materialRepository.queryMaterialByItemNoOrBarcode("", barcode)).thenReturn(materialMaintenanceDTOS);
        List<MaterialMaintenanceDTO> result = materialService.queryMaterialByItemNoOrBarcode(itemNo, barcode);

        // 验证结果
        Assert.assertNotNull(result);
    }

    @Test
    public void testGetMaterialsBySolderInfoList() {
        // Test case 1: Empty solderInfoList
        List<SolderInfo> solderInfoList = new ArrayList<>();
        List<MaterialMaintenanceDTO> result = materialService.getMaterialsBySolderInfoList(solderInfoList);
        Assert.assertEquals(0, result.size());

        // Test case 2: Only barcodes are present
        SolderInfo solderInfo1 = new SolderInfo();
        solderInfo1.setBarcode("barcode1");
        solderInfoList.add(solderInfo1);

        MaterialMaintenanceDTO materialMaintenanceDTO1 = new MaterialMaintenanceDTO();
        List<MaterialMaintenanceDTO> materialMaintenanceDTOS1 = new ArrayList<>();
        materialMaintenanceDTOS1.add(materialMaintenanceDTO1);

        PowerMockito.when(materialRepository.queryMaterialsByBarcodes(anyList())).thenReturn(materialMaintenanceDTOS1);

        result = materialService.getMaterialsBySolderInfoList(solderInfoList);

        // Test case 3: Only itemNos are present
        solderInfoList.clear(); // Clear previous data
        SolderInfo solderInfo2 = new SolderInfo();
        solderInfo2.setItemNo("ITEM123");
        solderInfoList.add(solderInfo2);

        MaterialMaintenanceDTO materialMaintenanceDTO2 = new MaterialMaintenanceDTO();
        List<MaterialMaintenanceDTO> materialMaintenanceDTOS2 = new ArrayList<>();
        materialMaintenanceDTOS2.add(materialMaintenanceDTO2);

        PowerMockito.when(materialRepository.queryMaterialsByItemNos(anyList())).thenReturn(materialMaintenanceDTOS2);

        result = materialService.getMaterialsBySolderInfoList(solderInfoList);

        // Test case 4: Both barcodes and itemNos are present
        solderInfoList.clear(); // Clear previous data
        solderInfoList.add(solderInfo1); // Add barcode
        solderInfoList.add(solderInfo2); // Add itemNo

        // Mock both queries
        PowerMockito.when(materialRepository.queryMaterialsByBarcodes(anyList())).thenReturn(materialMaintenanceDTOS1);
        PowerMockito.when(materialRepository.queryMaterialsByItemNos(anyList())).thenReturn(materialMaintenanceDTOS2);

        result = materialService.getMaterialsBySolderInfoList(solderInfoList);

        // Test case 5: No valid materials returned
        solderInfoList.clear(); // Clear previous data
        solderInfo1.setBarcode("barcode2");
        solderInfo2.setItemNo("ITEM456");
        solderInfoList.add(solderInfo1);
        solderInfoList.add(solderInfo2);

        PowerMockito.when(materialRepository.queryMaterialsByBarcodes(anyList())).thenReturn(new ArrayList<>());
        PowerMockito.when(materialRepository.queryMaterialsByItemNos(anyList())).thenReturn(new ArrayList<>());

        result = materialService.getMaterialsBySolderInfoList(solderInfoList);
    }

}
