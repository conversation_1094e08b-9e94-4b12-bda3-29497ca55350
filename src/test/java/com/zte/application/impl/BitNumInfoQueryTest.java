package com.zte.application.impl;

import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BBomHeader;
import com.zte.domain.model.BBomHeaderRepository;
import com.zte.domain.model.BPcbLocationDetail;
import com.zte.domain.model.BPcbLocationDetailRepository;
import com.zte.interfaces.dto.BPcbLocationDetailDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.CommonRedisUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.when;

@RunWith(PowerMockRunner.class)
public class BitNumInfoQueryTest extends BaseTestCase {
    @InjectMocks
    private BPcbLocationDetailServiceImpl service;

    @Mock
    private BPcbLocationDetailRepository bPcbLocationDetailRepository;

    @Mock
    private BBomHeaderRepository bBomHeaderRepository;

    @Mock
    private CommonRedisUtil commonRedisUtil;

    @Mock
    private BPcbLocationBomServiceImpl bPcbLocationBomServiceImpl;

    @Test
    public void bitNumInfoQueryTest() throws Exception {
        // Prepare test data
        List<BPcbLocationDetailDTO> inputList = new ArrayList<>();

        // Case 1: Input list is empty
        try {
            service.bitNumInfoQuery(new ArrayList<>(), "emp001");
            fail("Expected MesBusinessException for empty input list");
        } catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }

        // Case 2: Input list size exceeds 10
        for (int i = 0; i < 11; i++) {
            inputList.add(new BPcbLocationDetailDTO());
        }
        try {
            service.bitNumInfoQuery(inputList, "emp001");
            fail("Expected MesBusinessException for exceeding list size");
        } catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }

        // Case 3: Product code and pointLoc both are blank
        BPcbLocationDetailDTO dto1 = new BPcbLocationDetailDTO();
        dto1.setProductCode("");
        dto1.setPointLoc("");
        inputList.clear();
        inputList.add(dto1);
        try {
            service.bitNumInfoQuery(inputList, "emp001");
            fail("Expected MesBusinessException for empty product code and pointLoc");
        } catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }

        BPcbLocationDetailDTO dto3 = new BPcbLocationDetailDTO();
        dto3.setProductCode("");
        dto3.setPointLoc("123");
        inputList.clear();
        inputList.add(dto3);
        try {
            service.bitNumInfoQuery(inputList, "emp001");
            fail("Expected MesBusinessException for empty product code and pointLoc");
        } catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }

        // Case 4: Normal case with valid data
        BPcbLocationDetailDTO dto2 = new BPcbLocationDetailDTO();
        dto2.setProductCode("product1");
        dto2.setPointLoc("loc1,loc2");
        inputList.clear();
        inputList.add(dto2);

        // Mock the repository
        List<BPcbLocationDetail> mockDetails = new ArrayList<>();
        BPcbLocationDetail detail1 = new BPcbLocationDetail();
        detail1.setProductCode("product1");
        detail1.setPointLoc("loc1");
        mockDetails.add(detail1);
        when(bPcbLocationDetailRepository.batchSelectBPcbLocationDetail(anyList())).thenReturn(mockDetails);

        // Invoke the method
        List<BPcbLocationDetailDTO> result = service.bitNumInfoQuery(inputList, "emp001");

        BPcbLocationDetailDTO dto4 = new BPcbLocationDetailDTO();
        dto4.setProductCode("product1");
        dto4.setPointLoc("loc1,loc2");
        inputList.clear();
        inputList.add(dto4);

        // Mock the repository
        List<BPcbLocationDetail> mockDetails1 = new ArrayList<>();
        BPcbLocationDetail detail2 = new BPcbLocationDetail();
        detail2.setProductCode("product2");
        detail2.setPointLoc("loc1,loc2,loc3");
        mockDetails1.add(detail2);
        when(bPcbLocationDetailRepository.batchSelectBPcbLocationDetail(anyList())).thenReturn(mockDetails1);

        // Invoke the method
        List<BPcbLocationDetailDTO> result2 = service.bitNumInfoQuery(inputList, "emp001");
        // Assert results
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testBitNumInfoQueryEdgeCases() throws Exception {
        List<BPcbLocationDetailDTO> inputList = new ArrayList<>();
        // Case 1: Product code is blank, pointLoc is not blank
        BPcbLocationDetailDTO dto1 = new BPcbLocationDetailDTO();
        dto1.setProductCode("");
        dto1.setPointLoc("loc1");
        inputList.clear();
        inputList.add(dto1);
        try {
            service.bitNumInfoQuery(inputList, "emp001");
            fail("Expected MesBusinessException for empty product code and non-empty pointLoc");
        } catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }

        // Case 2: Product code is not blank, pointLoc is blank
        BPcbLocationDetailDTO dto2 = new BPcbLocationDetailDTO();
        dto2.setProductCode("product1");
        dto2.setPointLoc("");
        inputList.clear();
        inputList.add(dto2);
        try {
            service.bitNumInfoQuery(inputList, "emp001");
            fail("Expected MesBusinessException for non-empty product code and empty pointLoc");
        } catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }

        // Case 3: Product code and pointLoc both are blank
        BPcbLocationDetailDTO dto3 = new BPcbLocationDetailDTO();
        dto3.setProductCode("");
        dto3.setPointLoc("");
        inputList.clear();
        inputList.add(dto3);
        try {
            service.bitNumInfoQuery(inputList, "emp001");
            fail("Expected MesBusinessException for empty product code and pointLoc");
        } catch (MesBusinessException e) {
            assertEquals(RetCode.BUSINESSERROR_CODE, e.getExCode());
        }
    }

    @Test
    public void batchAnalyzeBomTest() throws Exception {
        // Case 1: needAnalyze is empty
        service.batchAnalyzeBom(new ArrayList<>(), "emp001");

        // Case 2: bBomHeaderList is empty
        List<String> needAnalyze = Arrays.asList("product1", "product2");
        when(bBomHeaderRepository.selectBBomHeaderByProductCodes(needAnalyze)).thenReturn(new ArrayList<>());

        service.batchAnalyzeBom(needAnalyze, "emp001");

        // Case 3: Lock fails or no data returned from batchInsertPcbLocation
        BBomHeader bBomHeader = new BBomHeader();
        when(bBomHeaderRepository.selectBBomHeaderByProductCodes(needAnalyze)).thenReturn(Collections.singletonList(bBomHeader));
        String uuid = "lock-uuid";
        when(commonRedisUtil.batchLockRedisKey(needAnalyze, Constant.ONE_MINUTES)).thenReturn(uuid);
        when(bPcbLocationBomServiceImpl.batchInsertPcbLocation(anyList(), anyString())).thenReturn(Collections.emptyList());

        service.batchAnalyzeBom(needAnalyze, "emp001");

        // Case 4: Some product codes are not parsed
        when(bPcbLocationBomServiceImpl.batchInsertPcbLocation(anyList(), anyString())).thenReturn(Arrays.asList("product2"));

        try {
            service.batchAnalyzeBom(needAnalyze, "emp001");
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

}