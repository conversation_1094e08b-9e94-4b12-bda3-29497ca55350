package com.zte.application.impl;

import com.zte.common.ConstantInterface;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.ApprovalRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.HrmUserInfoService;
import com.zte.infrastructure.remote.ICenterRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.iss.approval.sdk.bean.TaskVo;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.iss.approval.sdk.client.ApprovalTaskClient;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.util.BaseTestCase;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.doNothing;

/**
 * 异常物料单据头表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-03-27 10:11:49
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ApprovalTaskClient.class, ApprovalFlowClient.class, RedisCacheUtils.class,ExcelCommonUtils.class})
public class AbnormalMaterialBillHeadServiceImplTest extends BaseTestCase {

    @InjectMocks
    private AbnormalMaterialBillHeadServiceImpl service;
    @Mock
    private AbnormalMaterialBillHeadRepository repository;
    @Mock
    private AbnormalMaterialBillDetailRepository abnormalMaterialBillDetailRepository;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private SXSSFSheet sheet;
    @Mock
    private RedisLock redisLock;
    @Mock
    private ConstantInterface constantInterface;
    @Mock
    private CloudDiskHelper cloudDiskHelper;
    @Mock
    private HrmUserCenterServiceImpl hrmUserCenterService;
    @Mock
    private HrmUserInfoService hrmUserInfoService;
    @Mock
    private ApprovalRemoteService approvalRemoteService;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private ApprovalProcessInfoRepository approvalProcessInforepository;
    @Mock
    ICenterRemoteService iCenterRemoteService;

    @Test
    public void testQueryPage() throws Exception {
        AbnormalMaterialBillHeadPageQueryDTO query = new AbnormalMaterialBillHeadPageQueryDTO();
        query.setRows(1);
        query.setPage(1);
        try{
            service.queryPage(query);
        }catch(Exception e){
            Assert.assertEquals(MessageId.QUERY_TIME_AND_BILL_NO_IS_NULL, e.getMessage());
        }
        query.setCreateDateEnd("2024-04-10 08:30:00");
        try{
            service.queryPage(query);
        }catch(Exception e){
            Assert.assertEquals(MessageId.QUERY_TIME_AND_BILL_NO_IS_NULL, e.getMessage());
        }
        query.setCreateDateStart("2023-04-09 08:30:00");
        try{
            service.queryPage(query);
        }catch(Exception e){
            Assert.assertEquals(MessageId.TIME_INTERVAL_MORE_THAN_HALF_YEAR, e.getMessage());
        }
        query.setBillNo("test");
        query.setCreateDateStart("2024-04-09 08:30:00");

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("00286569");
        hrmPersonInfoDTOMap.put("00286569", hrmPersonInfoDTO);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);

        List<AbnormalMaterialBillHeadPageQueryDTO> list = new ArrayList<>();
        AbnormalMaterialBillHeadPageQueryDTO abnormalMaterialBillHead = new AbnormalMaterialBillHeadPageQueryDTO();
        abnormalMaterialBillHead.setBillNo("test");
        abnormalMaterialBillHead.setBillStatus(1);
        abnormalMaterialBillHead.setHandlerEmp("00286569");
        abnormalMaterialBillHead.setCreateBy("00286569");
        list.add(abnormalMaterialBillHead);
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(list);
        List<SysLookupValues> statusList = new ArrayList<>();
        PowerMockito.when(sysLookupValuesRepository.selectValuesByType(Mockito.any())).thenReturn(statusList);

        Assert.assertNotNull(service.queryPage(query));
    }

    @Test
    public void testQueryPageTwo() throws Exception {
        AbnormalMaterialBillHeadPageQueryDTO query = new AbnormalMaterialBillHeadPageQueryDTO();
        query.setRows(1);
        query.setPage(1);
        query.setCreateDateEnd("2024-04-10 08:30:00");
        query.setBillNo("test");
        query.setCreateDateStart("2024-04-09 08:30:00");
        List<AbnormalMaterialBillHeadPageQueryDTO> list = new ArrayList<>();
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(service.queryPage(query));
    }

    @Test
    public void noticeInformPerson() throws Exception {
        AbnormalMaterialBillHeadDTO dto = new AbnormalMaterialBillHeadDTO();
        dto.setInformPerson("10275508,");
        List<AbnormalMaterialBillDetailDTO> detailDTOList = new ArrayList<>();
        AbnormalMaterialBillDetailDTO abnormalMaterialBillDetailDTO = new AbnormalMaterialBillDetailDTO();
        abnormalMaterialBillDetailDTO.setDocId("test");
        detailDTOList.add(abnormalMaterialBillDetailDTO);
        dto.setDetailDTOList(detailDTOList);
        PowerMockito.when(emailUtils.sendMail(Mockito.any(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(true);
        doNothing().when(iCenterRemoteService).sendMessage(any(), any(), any());
        Assert.assertNull(Whitebox.invokeMethod(service, "noticeInformPerson", dto));
    }

    @Test
    public void sendEmailAfterApproval() throws Exception {
        AbnormalMaterialBillHeadDTO dto = new AbnormalMaterialBillHeadDTO();
        dto.setBillNo("test");
        dto.setBillStatus(3);
        List<AbnormalMaterialBillDetailDTO> details = new ArrayList<>();
        AbnormalMaterialBillDetailDTO abnormalMaterialBillDetailDTO = new AbnormalMaterialBillDetailDTO();
        abnormalMaterialBillDetailDTO.setBillNo("test");
        abnormalMaterialBillDetailDTO.setAbnormalType("test");
        abnormalMaterialBillDetailDTO.setAbnormalDescription("test");
        abnormalMaterialBillDetailDTO.setSn("test");
        details.add(abnormalMaterialBillDetailDTO);
        PowerMockito.when(abnormalMaterialBillDetailRepository.selectByBillNo(Mockito.any())).thenReturn(details);

        AbnormalMaterialBillHead billHead = new AbnormalMaterialBillHead();
        billHead.setBillNo("test");
        billHead.setProductionBase("test");
        billHead.setDepartment("test");
        billHead.setCreateBy("00286569");
        PowerMockito.when(repository.selectById(Mockito.any())).thenReturn(billHead);
        Assert.assertNull(Whitebox.invokeMethod(service, "sendEmailAfterApproval", dto));
        dto.setBillStatus(4);
        Assert.assertNull(Whitebox.invokeMethod(service, "sendEmailAfterApproval", dto));
    }

    @Test
    public void testQueryPageThree() throws Exception {
        AbnormalMaterialBillHeadPageQueryDTO query = new AbnormalMaterialBillHeadPageQueryDTO();
        query.setRows(1);
        query.setPage(1);
        query.setCreateDateEnd("2024-04-10 08:30:00");
        query.setBillNo("test");
        query.setCreateDateStart("2024-04-09 08:30:00");
        List<AbnormalMaterialBillHeadPageQueryDTO> list = new ArrayList<>();
        AbnormalMaterialBillHeadPageQueryDTO abnormalMaterialBillHead = new AbnormalMaterialBillHeadPageQueryDTO();
        abnormalMaterialBillHead.setBillNo("test");
        abnormalMaterialBillHead.setBillStatus(1);
        list.add(abnormalMaterialBillHead);
        PowerMockito.when(repository.selectPage(Mockito.any())).thenReturn(list);

        List<SysLookupValues> statusList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("1");
        sysLookupValues.setDescriptionChin("拟制中");
        statusList.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesRepository.selectValuesByType(Mockito.any())).thenReturn(statusList);
        Assert.assertNotNull(service.queryPage(query));
    }

    @Test
    public void checkQueryParam() throws Exception {
        AbnormalMaterialBillHeadPageQueryDTO query = new AbnormalMaterialBillHeadPageQueryDTO();
        query.setRows(1);
        query.setPage(1);
        query.setCreateDateEnd("2024-04-10 08:30:00");
        query.setCreateDateStart("2024-04-09 08:30:00");
        Whitebox.invokeMethod(service, "checkQueryParam", query);
        Assert.assertNotNull(query);
    }

    @Test
    public void checkQueryParamTwo() throws Exception {
        AbnormalMaterialBillHeadPageQueryDTO query = new AbnormalMaterialBillHeadPageQueryDTO();
        query.setRows(1);
        query.setPage(1);
        query.setBillNo("test");
        Whitebox.invokeMethod(service, "checkQueryParam", query);
        Assert.assertNotNull(query);
    }

    @Test
    public void transFinderName() throws Exception {
        List<AbnormalMaterialBillDetailDTO> detailDTOList = new ArrayList<>();
        Assert.assertNull(Whitebox.invokeMethod(service, "transFinderName", detailDTOList));
        AbnormalMaterialBillDetailDTO dto = new AbnormalMaterialBillDetailDTO();
        dto.setFinderEmp("00286569");
        detailDTOList.add(dto);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("00286569");
        hrmPersonInfoDTOMap.put("00286569", hrmPersonInfoDTO);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);

        Assert.assertNull(Whitebox.invokeMethod(service, "transFinderName", detailDTOList));
    }

    @Test
    public void approvalOperation() throws Exception {
        PowerMockito.mockStatic(ApprovalTaskClient.class, ApprovalFlowClient.class);
        AbnormalMaterialBillHead abnormalMaterialBillHead = new AbnormalMaterialBillHead();
        abnormalMaterialBillHead.setOperateType("2");
        abnormalMaterialBillHead.setBillNo("test");
        abnormalMaterialBillHead.setLastUpdatedBy("00286569");
        try{
            service.approvalOperation(abnormalMaterialBillHead);
        }catch(Exception e){
            Assert.assertEquals(MessageId.THE_PERSON_TO_BE_HANDED_OVER_TO_CAN_NOT_BE_NULL, e.getMessage());
        }
        abnormalMaterialBillHead.setOperateType("1");
        try{
            service.approvalOperation(abnormalMaterialBillHead);
        }catch(Exception e){
            Assert.assertEquals(MessageId.APPROVAL_COMMENTS_CANNOT_BE_EMPTY_WHEN_REJECTING, e.getMessage());
        }
        abnormalMaterialBillHead.setApprovalComment("拒绝");
        PowerMockito.when(repository.updateSelectiveById(Mockito.any())).thenReturn(1l);
        PageRows<TaskVo> taskVoPageRows = new PageRows<>();
        TaskVo taskVo = new TaskVo();
        taskVo.setTaskId("test");
        List<TaskVo> taskVoList = new ArrayList<>();
        taskVoList.add(taskVo);
        taskVoPageRows.setTotal(1);
        taskVoPageRows.setRows(taskVoList);
        PowerMockito.when(ApprovalTaskClient.queryTask(any())).thenReturn(taskVoPageRows);

        service.approvalOperation(abnormalMaterialBillHead);
        Assert.assertNotNull(taskVo);
        abnormalMaterialBillHead.setOperateType("0");
        service.approvalOperation(abnormalMaterialBillHead);
        Assert.assertNotNull(taskVo);
        abnormalMaterialBillHead.setOperateType("4");
        try{
            service.approvalOperation(abnormalMaterialBillHead);
        }catch(Exception e){
            Assert.assertEquals(MessageId.THE_APPROVAL_TYPE_IS_INCORRECT, e.getMessage());
        }
        abnormalMaterialBillHead.setOperateType("2");
        abnormalMaterialBillHead.setHandlerEmp("00286569");
        PowerMockito.when(ApprovalTaskClient.reassign(any())).thenReturn("");
        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("00286569");
        hrmPersonInfoDTOMap.put("00286569", hrmPersonInfoDTO);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);
        PowerMockito.when(approvalProcessInforepository.updateApproverInfo(any())).thenReturn(1);

        AbnormalMaterialBillHead billHead = new AbnormalMaterialBillHead();
        billHead.setBillNo("test");
        billHead.setProductionBase("test");
        billHead.setDepartment("test");
        PowerMockito.when(repository.selectById(Mockito.any())).thenReturn(billHead);

        List<AbnormalMaterialBillDetailDTO> details = new ArrayList<>();
        AbnormalMaterialBillDetailDTO abnormalMaterialBillDetailDTO = new AbnormalMaterialBillDetailDTO();
        abnormalMaterialBillDetailDTO.setBillNo("test");
        abnormalMaterialBillDetailDTO.setAbnormalType("test");
        abnormalMaterialBillDetailDTO.setAbnormalDescription("test");
        abnormalMaterialBillDetailDTO.setSn("test");
        details.add(abnormalMaterialBillDetailDTO);
        PowerMockito.when(abnormalMaterialBillDetailRepository.selectByBillNo(Mockito.any())).thenReturn(details);
        PowerMockito.when(emailUtils.sendMail(Mockito.any(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(true);
        service.approvalOperation(abnormalMaterialBillHead);
        Assert.assertNotNull(taskVo);
    }

    @Test
    public void getTaskVoForCommon() throws Exception {
        PowerMockito.mockStatic(ApprovalTaskClient.class, ApprovalFlowClient.class);
        AbnormalMaterialBillHead abnormalMaterialBillHead = new AbnormalMaterialBillHead();
        abnormalMaterialBillHead.setLastUpdatedBy("00286569");
        abnormalMaterialBillHead.setBillNo("test");
        try{
            PageRows<TaskVo> taskVoPageRows = new PageRows<>();
            TaskVo taskVo = new TaskVo();
            List<TaskVo> taskVoList = new ArrayList<>();
            taskVoList.add(taskVo);
            taskVoPageRows.setTotal(1);
            taskVoPageRows.setRows(taskVoList);
            PowerMockito.when(ApprovalTaskClient.queryTask(any())).thenReturn(taskVoPageRows);
            Whitebox.invokeMethod(service, "getTaskVoForCommon", abnormalMaterialBillHead);
        } catch (Exception e){
            Assert.assertEquals(MessageId.NO_CORRESPONDING_DOCUMENTS_OBTAINED_IN_THE_APPROVAL_CENTER, e.getMessage());
        }
        try{
            PageRows<TaskVo> taskVoPageRows = new PageRows<>();
            List<TaskVo> taskVoList = new ArrayList<>();
            taskVoPageRows.setTotal(1);
            taskVoPageRows.setRows(taskVoList);
            PowerMockito.when(ApprovalTaskClient.queryTask(any())).thenReturn(taskVoPageRows);
            Whitebox.invokeMethod(service, "getTaskVoForCommon", abnormalMaterialBillHead);
        } catch (Exception e){
            Assert.assertEquals(MessageId.NO_CORRESPONDING_DOCUMENTS_OBTAINED_IN_THE_APPROVAL_CENTER, e.getMessage());
        }
        try{
            PowerMockito.when(ApprovalTaskClient.queryTask(any())).thenReturn(null);
            Whitebox.invokeMethod(service, "getTaskVoForCommon", abnormalMaterialBillHead);
        } catch (Exception e){
            Assert.assertEquals(MessageId.NO_CORRESPONDING_DOCUMENTS_OBTAINED_IN_THE_APPROVAL_CENTER, e.getMessage());
        }
    }


    @Test
    public void getDepartmentOptions() throws Exception {
        List<String> list = new ArrayList<>();
        list.add("test");
        PowerMockito.when(repository.getDepartmentOptions()).thenReturn(list);
        Assert.assertNotNull(service.getDepartmentOptions());
    }

    @Test
    public void generateBillNo() throws Exception {
        PowerMockito.mockStatic(RedisCacheUtils.class);
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);
        try{
            service.generateBillNo();
        }catch(Exception e){
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void getRedisId() throws Exception {
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);
        try{
            Whitebox.invokeMethod(service, "getRedisId", "test");
        }catch(Exception e){
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void findMaxCount() throws Exception {
        PowerMockito.mockStatic(RedisCacheUtils.class);
        Assert.assertNotNull(service.findMaxCount("111"));
        PowerMockito.when(RedisCacheUtils.get(Mockito.anyString(), Mockito.any())).thenReturn(9);
        Assert.assertNotNull(service.findMaxCount("111"));
    }

    @Test
    public void saveBillInfo() throws Exception {
        AbnormalMaterialBillHeadDTO dto = new AbnormalMaterialBillHeadDTO();
        try{
            service.saveBillInfo(dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.BILL_INFO_INPUT_PARAM_EMPTY, e.getMessage());
        }
        dto.setBillNo("test");
        try{
            service.saveBillInfo(dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.BILL_INFO_INPUT_PARAM_EMPTY, e.getMessage());
        }
        dto.setDepartment("test");
        try{
            service.saveBillInfo(dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.BILL_INFO_INPUT_PARAM_EMPTY, e.getMessage());
        }
        dto.setProductionBase("test");
        try{
            service.saveBillInfo(dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.BILL_INFO_INPUT_PARAM_EMPTY, e.getMessage());
        }
        dto.setHandlerEmp("00286569");
        List<AbnormalMaterialBillDetailDTO> detailDTOList = new ArrayList<>();
        dto.setDetailDTOList(detailDTOList);
        try{
            service.saveBillInfo(dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.PLEASE_MAINTAIN_AT_LEAST_ONE, e.getMessage());
        }
        AbnormalMaterialBillDetailDTO abnormalMaterialBillDetailDTO = new AbnormalMaterialBillDetailDTO();
        abnormalMaterialBillDetailDTO.setDocId("test");
        detailDTOList.add(abnormalMaterialBillDetailDTO);
        dto.setDetailDTOList(detailDTOList);
        try{
            service.saveBillInfo(dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.INPUT_SN_IS_NULL_OR_DUPLICATE, e.getMessage());
        }
        AbnormalMaterialBillDetailDTO abnormalMaterialBillDetailDTO1 = new AbnormalMaterialBillDetailDTO();
        abnormalMaterialBillDetailDTO1.setSn("test");
        detailDTOList.add(abnormalMaterialBillDetailDTO1);
        dto.setDetailDTOList(detailDTOList);
        try{
            service.saveBillInfo(dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.INPUT_SN_IS_NULL_OR_DUPLICATE, e.getMessage());
        }
        List<AbnormalMaterialBillDetailDTO> detailDTOList2 = new ArrayList<>();
        detailDTOList2.add(abnormalMaterialBillDetailDTO1);
        dto.setDetailDTOList(detailDTOList2);
        try{
            service.saveBillInfo(dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.CALL_BARCODE_CENTER_EXPANDQUERY_BARCODE_FALIED, e.getMessage());
        }
        abnormalMaterialBillDetailDTO1.setItemCode("test");
        List<AbnormalMaterialBillDetailDTO> detailDTOList3 = new ArrayList<>();
        detailDTOList3.add(abnormalMaterialBillDetailDTO1);
        dto.setDetailDTOList(detailDTOList3);
        try{
            service.saveBillInfo(dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.TYPE_OR_DEC_OF_SN_IS_NULL, e.getMessage());
        }
        abnormalMaterialBillDetailDTO1.setAbnormalType("test");
        List<AbnormalMaterialBillDetailDTO> detailDTOList4 = new ArrayList<>();
        detailDTOList4.add(abnormalMaterialBillDetailDTO1);
        dto.setDetailDTOList(detailDTOList4);
        try{
            service.saveBillInfo(dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.TYPE_OR_DEC_OF_SN_IS_NULL, e.getMessage());
        }
        abnormalMaterialBillDetailDTO1.setAbnormalDescription("test");
        List<AbnormalMaterialBillDetailDTO> detailDTOList5 = new ArrayList<>();
        detailDTOList5.add(abnormalMaterialBillDetailDTO1);
        dto.setDetailDTOList(detailDTOList5);
        try{
            service.saveBillInfo(dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.QTY_OF_BARCODE_CAN_NOT_BE_NULL, e.getMessage());
        }
        abnormalMaterialBillDetailDTO1.setQty(0);
        List<AbnormalMaterialBillDetailDTO> detailDTOList6 = new ArrayList<>();
        detailDTOList6.add(abnormalMaterialBillDetailDTO1);
        dto.setDetailDTOList(detailDTOList6);
        try{
            service.saveBillInfo(dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.QTY_OF_BARCODE_CAN_NOT_BE_NULL, e.getMessage());
        }
        abnormalMaterialBillDetailDTO1.setQty(1);
        try{
            service.saveBillInfo(dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.EXTERNAL_TYPE_IS_NULL, e.getMessage());
        }
        abnormalMaterialBillDetailDTO1.setExternalType("TEST");
        try{
            service.saveBillInfo(dto);
        }catch(Exception e){
            Assert.assertEquals(MessageId.STYLE_IS_NULL, e.getMessage());
        }
        abnormalMaterialBillDetailDTO1.setStyle("TEST");
        List<AbnormalMaterialBillDetailDTO> detailDTOList7 = new ArrayList<>();
        detailDTOList7.add(abnormalMaterialBillDetailDTO1);
        service.saveBillInfo(dto);
        Assert.assertNotNull(abnormalMaterialBillDetailDTO1);
    }

    @Test
    public void saveBillInfoTwo() throws Exception {
        AbnormalMaterialBillHeadDTO dto = new AbnormalMaterialBillHeadDTO();
        dto.setBillNo("test");
        dto.setDepartment("test");
        dto.setProductionBase("test");
        dto.setHandlerEmp("00286569");
        AbnormalMaterialBillDetailDTO abnormalMaterialBillDetailDTO = new AbnormalMaterialBillDetailDTO();
        abnormalMaterialBillDetailDTO.setSn("test");
        abnormalMaterialBillDetailDTO.setItemCode("test");
        abnormalMaterialBillDetailDTO.setQty(1);
        abnormalMaterialBillDetailDTO.setAbnormalType("测试不通过");
        abnormalMaterialBillDetailDTO.setAbnormalDescription("测试不通过");
        abnormalMaterialBillDetailDTO.setExternalType("测试不通过");
        abnormalMaterialBillDetailDTO.setStyle("测试不通过");
        List<AbnormalMaterialBillDetailDTO> detailDTOList = new ArrayList<>();
        detailDTOList.add(abnormalMaterialBillDetailDTO);
        dto.setDetailDTOList(detailDTOList);
        dto.setSaveAction("0");
        PowerMockito.when(repository.selectCount(Mockito.any())).thenReturn(0);
        PowerMockito.when(repository.insert(Mockito.any())).thenReturn(1l);
        PowerMockito.when(abnormalMaterialBillDetailRepository.batchInsert(Mockito.anyList())).thenReturn(1);
        service.saveBillInfo(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void saveBillInfoThree() throws Exception {
        PowerMockito.mockStatic(ExcelCommonUtils.class);
        AbnormalMaterialBillHeadDTO dto = new AbnormalMaterialBillHeadDTO();
        dto.setBillNo("test");
        dto.setDepartment("test");
        dto.setProductionBase("test");
        dto.setHandlerEmp("00286569");
        AbnormalMaterialBillDetailDTO abnormalMaterialBillDetailDTO = new AbnormalMaterialBillDetailDTO();
        abnormalMaterialBillDetailDTO.setSn("test");
        abnormalMaterialBillDetailDTO.setItemCode("test");
        abnormalMaterialBillDetailDTO.setQty(1);
        abnormalMaterialBillDetailDTO.setAbnormalType("测试不通过");
        abnormalMaterialBillDetailDTO.setAbnormalDescription("测试不通过");
        abnormalMaterialBillDetailDTO.setExternalType("测试不通过");
        abnormalMaterialBillDetailDTO.setStyle("测试不通过");
        List<AbnormalMaterialBillDetailDTO> detailDTOList = new ArrayList<>();
        detailDTOList.add(abnormalMaterialBillDetailDTO);
        dto.setDetailDTOList(detailDTOList);
        dto.setSaveAction("1");
        PowerMockito.when(repository.selectCount(Mockito.any())).thenReturn(1);
        PowerMockito.when(repository.updateSelectiveById(Mockito.any())).thenReturn(1l);
        PowerMockito.when(abnormalMaterialBillDetailRepository.updateSelectiveById(Mockito.any())).thenReturn(1l);
        PowerMockito.when(abnormalMaterialBillDetailRepository.batchInsert(Mockito.anyList())).thenReturn(1);

        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://zte-mes-manufactureshare-externalserv/file/download");
        PowerMockito.when(ExcelCommonUtils.createSheet(Mockito.any(),Mockito.any())).thenReturn(sheet);
        PowerMockito.when(cloudDiskHelper.fileUpload(anyString(), anyString(), anyInt()))
                .thenReturn("111");
        PowerMockito.when(cloudDiskHelper.getFileDownloadUrl(anyString(), anyString(), anyString()))
                .thenReturn("111");

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("00286569");
        hrmPersonInfoDTOMap.put("00286569", hrmPersonInfoDTO);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);

        PowerMockito.when(abnormalMaterialBillDetailRepository.batchInsert(Mockito.anyList())).thenReturn(1);
        PowerMockito.mockStatic(ApprovalFlowClient.class);
        PowerMockito.when(approvalRemoteService.start(Mockito.any())).thenReturn("");
        service.saveBillInfo(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void updateById() throws Exception {
        AbnormalMaterialBillHead dto = new AbnormalMaterialBillHead();
        dto.setBillNo("test");
        PowerMockito.when(repository.updateById(Mockito.any())).thenReturn(1l);
        service.updateById(dto);
        Assert.assertNotNull(dto);
    }
    @Test
    public void getUserInfo() throws Exception {
        BsPubHrvOrgId bsPubHrvOrgId = new BsPubHrvOrgId();
        bsPubHrvOrgId.setOfficeName("供应链");
        bsPubHrvOrgId.setUserId("00286569");
        List<BsPubHrvOrgId> bsPubHrvOrgIdList = new ArrayList<>();
        bsPubHrvOrgIdList.add(bsPubHrvOrgId);
        PowerMockito.when(hrmUserInfoService.getBsPubHrvOrgIdInfo(anyList())).thenReturn(bsPubHrvOrgIdList);
        Assert.assertNotNull(service.getUserInfo("00286569"));
        Assert.assertNotNull(service.getUserInfo(null));
    }
}

