package com.zte.application.impl;

import com.zte.application.BsItemInfoService;
import com.zte.application.BsPremanuBomInfoService;
import com.zte.application.BsPremanuItemInfoService;
import com.zte.application.IMESLogService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.domain.model.*;
import com.zte.domain.model.craftTech.CtRouteHeadRepository;
import com.zte.infrastructure.remote.MdsRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.util.EmailUtils;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class BPcbLocationDetailServiceImplTest extends TestCase {

    @InjectMocks
    private BPcbLocationDetailServiceImpl bPcbLocationDetailService;
    @Mock
    private IMESLogService imesLogService;
    @Mock
    private CtRouteHeadRepository ctRouteHeadRepository;
    @Mock
    private BsPremanuBomInfoRepository bsPremanuBomInfoRepository;
    @Mock
    private BPcbLocationDetailRepository bPcbLocationDetailRepository;
    @Mock
    private BsItemInfoRepository bsItemInfoRepository;
    @Mock
    private CadUploadRecordRepository cadUploadRecordRepository;

    @Mock
    private BsPremanuBomInfoService bsPremanuBomInfoService;
    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private MdsRemoteService mdsRemoteService;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private BsPremanuItemInfoService bsPremanuItemInfoService;
    @Mock
    private BBomHeaderRepository bBomHeaderRepository;
    @Mock
    private BsItemInfoService itemInfoService;
    @Mock
    private BProdBomChangeDetailRepository bProdBomChangeDetailRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(CommonUtils.class);
    }

    @Test
    public void testPreManuUpdateProgrammingFromMDS() throws Exception {
        PowerMockito.when(emailUtils.sendMail(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(true);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(bsPremanuItemInfoService).asyncCycleSaveSubLevelPremanuInfo(any());
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(bsPremanuBomInfoService).pushPreBomToMes(any());
        PowerMockito.when(cadUploadRecordRepository.insertSelective(Mockito.any())).thenReturn(1);
        BPcbLocationDetailDTO dto = new BPcbLocationDetailDTO();
        bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        List<PreManuMDSProgrammingDTO> programmingDTOList = new ArrayList<>();
        dto.setProgrammingList(programmingDTOList);
        PreManuMDSProgrammingDTO preManuMDSProgrammingDTO = new PreManuMDSProgrammingDTO();
        programmingDTOList.add(preManuMDSProgrammingDTO);
        preManuMDSProgrammingDTO.setLocation("D1");
        preManuMDSProgrammingDTO.setPcbVersion("AKB123");
        dto.setProductCode("123AKB");
        dto.setFactoryId(51);
        try {
            bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODUCT_CODE_NOT_SAME_ERROR, e.getMessage());
        }
        preManuMDSProgrammingDTO.setPcbMaterial("123AKB");
        try {
            bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODUCT_CODE_LENGTH_ERROR, e.getMessage());
        }
        // 12位方法单元测试
        dto.setProductCode("123456789012");
        preManuMDSProgrammingDTO.setPcbMaterial("123456789012");
        bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        PowerMockito.when(itemInfoService.getItemNameAndPCBVersion(any())).thenReturn(new ArrayList<>());
        List<CtRouteHeadDTO> ctRouteHeadDTOTen = new ArrayList<>();
        for (int i = 1; i <= 11; i++) {
            CtRouteHeadDTO ctRouteHeadDTO = new CtRouteHeadDTO();
            ctRouteHeadDTO.setItemNo(String.valueOf(i));
            ctRouteHeadDTOTen.add(ctRouteHeadDTO);
        }
        PowerMockito.when(ctRouteHeadRepository.getList(any())).thenReturn(ctRouteHeadDTOTen);
        bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        List<BsItemInfo> items = new ArrayList<>();
        for (int i = 1; i <= 11; i++) {
            BsItemInfo bsItemInfo = new BsItemInfo();
            bsItemInfo.setItemNo(String.valueOf(i));
            bsItemInfo.setItemName("AKB123");
            bsItemInfo.setPcbVersion("AKB123");
            items.add(bsItemInfo);
        }
        items.get(0).setItemName(null);
        PowerMockito.when(itemInfoService.getItemNameAndPCBVersion(any())).thenReturn(items);
        bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);

        items.get(0).setPcbVersion(null);
        List<CtRouteHeadDTO> ctRouteHeadDTOs = new ArrayList<>();
        PowerMockito.when(ctRouteHeadRepository.getList(any())).thenReturn(ctRouteHeadDTOs);
        CtRouteHeadDTO ctRouteHeadDTO = new CtRouteHeadDTO();
        ctRouteHeadDTO.setItemNo("123456789012ABC");
        ctRouteHeadDTOs.add(ctRouteHeadDTO);
        try {
            bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }

        List<CtRouteHeadDTO> ctRouteHeadDTOS = new LinkedList<>();
        for (int i = 0; i < 15; i++) {
            CtRouteHeadDTO temp = new CtRouteHeadDTO();
            temp.setItemNo("123"+i);
            ctRouteHeadDTOS.add(temp);
        }
        PowerMockito.when(ctRouteHeadRepository.getList(Mockito.anyMap())).thenReturn(ctRouteHeadDTOS);
        bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);

        PowerMockito.when(ctRouteHeadRepository.getList(any())).thenReturn(ctRouteHeadDTOs);

        List<BPcbLocationDetail> bPcbLocationDetails = new ArrayList<>();
        BPcbLocationDetail bPcbLocationDetail = new BPcbLocationDetail();
        bPcbLocationDetails.add(bPcbLocationDetail);
        bPcbLocationDetail.setPointLoc("D1");
        PowerMockito.when(bPcbLocationDetailRepository.selectBPcbLocationDetailForMDSProgramming(any(), any())).thenReturn(bPcbLocationDetails);
        try {
            bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MDS_IN_PROGRAM_ERROR, e.getMessage());
        }
        bPcbLocationDetail.setItemCode("0040491232");
        bPcbLocationDetails.add(bPcbLocationDetail);
        try {
            bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MDS_IN_PROGRAM_ERROR, e.getMessage());
        }
        bPcbLocationDetails.remove(1);
        bPcbLocationDetail.setCraftSection("SMT-A");
        bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        List<BsItemInfo> bsItemInfoList = new ArrayList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemNo("123456789012ABC");
        bsItemInfoList.add(bsItemInfo);
        PowerMockito.when(bsItemInfoRepository.getItemNameAndPCBVersion(any())).thenReturn(bsItemInfoList);
        bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        bsItemInfo.setItemNo("0040491232");
        preManuMDSProgrammingDTO.setMaterialCode("0040491232");
        bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        // 15位方法单元测试
        dto.setProductCode("123456789012ABC");
        preManuMDSProgrammingDTO.setPcbMaterial("123456789012ABC");
        bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        List<BsPremanuBomInfo> bsPreManuBomInfoList = new ArrayList<>();
        PowerMockito.when(bsPremanuBomInfoRepository.selectPreBomInfoForMDSProgramming(any())).thenReturn(bsPreManuBomInfoList);
        BsPremanuBomInfo bsPremanuBomInfo = new BsPremanuBomInfo();
        bsPremanuBomInfo.setTagNum("D1");
        bsPremanuBomInfo.setTypeCode("XP");
        bsPremanuBomInfo.setBomCode("123456789012ABC");
        bsPremanuBomInfo.setItemNo("0040491232");
        bsPreManuBomInfoList.add(bsPremanuBomInfo);
        bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        bsPremanuBomInfo.setTypeCode("HK");
        bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        // 多位号处理
        preManuMDSProgrammingDTO.setLocation("D1,D2");
        BPcbLocationDetail bPcbLocationDetail2 = new BPcbLocationDetail();
        bPcbLocationDetails.add(bPcbLocationDetail2);
        bPcbLocationDetail2.setPointLoc("D2");
        bPcbLocationDetail2.setItemCode("0040491232");
        bPcbLocationDetail2.setCraftSection("SMT-A");
        bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        bsPremanuBomInfo.setTypeCode("XP");
        bsPremanuBomInfo.setDeliveryProcess("SMT配送");
        try {
            bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MDS_IN_PROGRAM_ERROR, e.getMessage());
        }
        bPcbLocationDetail2.setItemCode("0040491231");
        try {
            bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MDS_IN_PROGRAM_ERROR, e.getMessage());
        }
        bPcbLocationDetail2.setItemCode(null);
        try {
            bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MDS_IN_PROGRAM_ERROR, e.getMessage());
        }
        dto.setCadUploader("0040491231");
        try {
            bPcbLocationDetailService.preManuUpdateProgrammingFromMDS(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.XP_AUTO_SYNC_FAILED_MSG, e.getMessage());
        }

    }

    @Test
    public void preManuMDSProgrammingSync() throws Exception {
        BsAsyncDataDTO bsAsyncDataDTO = new BsAsyncDataDTO();
        bsAsyncDataDTO.setServiceKey("778899001122ABB");
        List<SysLookupValues> sysList = new ArrayList<>();
        List<BBomHeader> bBomHeaderList = new ArrayList<>();
        PowerMockito.when(sysLookupValuesService.selectValuesByType(any())).thenReturn(sysList);
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(bBomHeaderRepository.getList(any())).thenReturn(bBomHeaderList);
        SysLookupValues sysLookupValue = new SysLookupValues();
        sysLookupValue.setLookupMeaning("发布版本");
        sysList.add(sysLookupValue);
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOM_DATA_OF_BOM_NO_NOT_EXIST, e.getMessage());
        }
        BBomHeader bomHeader = new BBomHeader();
        bomHeader.setProductCode("test123");
        bomHeader.setChiDesc("test123");
        bomHeader.setVerNo("test123");
        bBomHeaderList.add(bomHeader);
        PowerMockito.when(emailUtils.sendMail(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(true);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(bsPremanuItemInfoService).asyncCycleSaveSubLevelPremanuInfo(any());
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(bsPremanuBomInfoService).pushPreBomToMes(any());
        PowerMockito.when(cadUploadRecordRepository.insertSelective(Mockito.any())).thenReturn(1);
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.any())).thenReturn( new ArrayList<>()).thenReturn(sysList);
        List<BsPremanuBomInfo> bsPreManuBomInfoList = new ArrayList<>();
        PowerMockito.when(bsPremanuBomInfoRepository.selectPreBomInfoForMDSProgramming(any())).thenReturn(bsPreManuBomInfoList);
        List<PreManuMDSProgrammingDTO> fullBomCodePreManuList = new ArrayList<>();
        PreManuMDSProgrammingDTO preManuMDSProgrammingDTO = new PreManuMDSProgrammingDTO();
        preManuMDSProgrammingDTO.setLocation("D1");
        List<PreManuMDSProgrammingDTO> partBomCodePreManuList = new ArrayList<>();
        PowerMockito.when(mdsRemoteService.getPreManuMDSProgrammingList(Mockito.any())).thenReturn(fullBomCodePreManuList).thenReturn(partBomCodePreManuList);
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        partBomCodePreManuList.add(preManuMDSProgrammingDTO);
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        fullBomCodePreManuList.add(preManuMDSProgrammingDTO);
        PowerMockito.when(mdsRemoteService.getPreManuMDSProgrammingList(Mockito.any())).thenReturn(fullBomCodePreManuList);
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<BsPremanuBomInfo> bsPremanuBomInfoList = new ArrayList<>();
        BsPremanuBomInfo bsPremanuBomInfo = new BsPremanuBomInfo();
        bsPremanuBomInfoList.add(bsPremanuBomInfo);
        PowerMockito.when(bsPremanuBomInfoRepository.getBsPremanuBomInfoList(Mockito.any())).thenReturn(bsPremanuBomInfoList);
        PowerMockito.when(mdsRemoteService.getPreManuMDSProgrammingList(Mockito.any())).thenReturn(new ArrayList<>());
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(mdsRemoteService.getPreManuMDSProgrammingList(Mockito.any())).thenReturn(new ArrayList<>()).thenReturn(partBomCodePreManuList);
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<BPcbLocationDetail> bPcbLocationDetails = new ArrayList<>();
        BPcbLocationDetail bPcbLocationDetail = new BPcbLocationDetail();
        bPcbLocationDetail.setPointLoc("D1");
        bPcbLocationDetails.add(bPcbLocationDetail);
        PowerMockito.when(mdsRemoteService.getPreManuMDSProgrammingList(Mockito.any())).thenReturn(new ArrayList<>()).thenReturn(partBomCodePreManuList);
        PowerMockito.when(bPcbLocationDetailRepository.selectBPcbLocationDetailByProductCode(Mockito.any())).thenReturn(bPcbLocationDetails);
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        bPcbLocationDetail.setPointLoc("D2");
        PowerMockito.when(mdsRemoteService.getPreManuMDSProgrammingList(Mockito.any())).thenReturn(new ArrayList<>()).thenReturn(partBomCodePreManuList);
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
    @Test
    public void syncXPFromMds() throws Exception {
        List<SysLookupValues> sysList = null;
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.any())).thenReturn(sysList);
        try{
            Whitebox.invokeMethod(bPcbLocationDetailService,"syncXPFromMds",new BsAsyncDataDTO(), true);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG,e.getMessage());
        }
    }

    @Test
    public void syncXPFromMds1() throws Exception {
        BsAsyncDataDTO bsAsyncDataDTO = new BsAsyncDataDTO();
        bsAsyncDataDTO.setServiceKey("778899001122ABB");
        boolean isSuccess = true;
        List<SysLookupValues> sysList = new ArrayList<>();
        SysLookupValues sysLookupValue = new SysLookupValues();
        sysLookupValue.setLookupType(new BigDecimal("652307"));
        sysLookupValue.setLookupCode(new BigDecimal("652307001"));
        sysLookupValue.setLookupMeaning("Y");
        sysList.add(sysLookupValue);
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.any())).thenReturn(sysList);
        List<BBomHeader> bBomHeaderList = new ArrayList<>();
        PowerMockito.when(sysLookupValuesService.selectValuesByType(any())).thenReturn(sysList);
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOM_DATA_OF_BOM_NO_NOT_EXIST, e.getMessage());
        }
        PowerMockito.when(bBomHeaderRepository.getList(any())).thenReturn(bBomHeaderList);
        sysLookupValue.setLookupMeaning("发布版本");
        sysList.add(sysLookupValue);
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOM_DATA_OF_BOM_NO_NOT_EXIST, e.getMessage());
        }
        BBomHeader bomHeader = new BBomHeader();
        bomHeader.setProductCode("test123");
        bomHeader.setChiDesc("test123");
        bomHeader.setVerNo("test123");
        bBomHeaderList.add(bomHeader);
        PowerMockito.when(emailUtils.sendMail(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(true);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(bsPremanuItemInfoService).asyncCycleSaveSubLevelPremanuInfo(any());
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(bsPremanuBomInfoService).pushPreBomToMes(any());
        PowerMockito.when(cadUploadRecordRepository.insertSelective(Mockito.any())).thenReturn(1);
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.any())).thenReturn( new ArrayList<>()).thenReturn(sysList);
        List<BsPremanuBomInfo> bsPreManuBomInfoList = new ArrayList<>();
        PowerMockito.when(bsPremanuBomInfoRepository.selectPreBomInfoForMDSProgramming(any())).thenReturn(bsPreManuBomInfoList);
        List<PreManuMDSProgrammingDTO> fullBomCodePreManuList = new ArrayList<>();
        PreManuMDSProgrammingDTO preManuMDSProgrammingDTO = new PreManuMDSProgrammingDTO();
        preManuMDSProgrammingDTO.setLocation("D1");
        List<PreManuMDSProgrammingDTO> partBomCodePreManuList = new ArrayList<>();
        PowerMockito.when(mdsRemoteService.getPreManuMDSProgrammingList(Mockito.any())).thenReturn(fullBomCodePreManuList).thenReturn(partBomCodePreManuList);
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        partBomCodePreManuList.add(preManuMDSProgrammingDTO);
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        fullBomCodePreManuList.add(preManuMDSProgrammingDTO);
        PowerMockito.when(mdsRemoteService.getPreManuMDSProgrammingList(Mockito.any())).thenReturn(fullBomCodePreManuList);
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<BsPremanuBomInfo> bsPremanuBomInfoList = new ArrayList<>();
        BsPremanuBomInfo bsPremanuBomInfo = new BsPremanuBomInfo();
        bsPremanuBomInfoList.add(bsPremanuBomInfo);
        PowerMockito.when(bsPremanuBomInfoRepository.getBsPremanuBomInfoList(Mockito.any())).thenReturn(bsPremanuBomInfoList);
        PowerMockito.when(mdsRemoteService.getPreManuMDSProgrammingList(Mockito.any())).thenReturn(new ArrayList<>());
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(mdsRemoteService.getPreManuMDSProgrammingList(Mockito.any())).thenReturn(new ArrayList<>()).thenReturn(partBomCodePreManuList);
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        List<BPcbLocationDetail> bPcbLocationDetails = new ArrayList<>();
        BPcbLocationDetail bPcbLocationDetail = new BPcbLocationDetail();
        bPcbLocationDetail.setPointLoc("D1");
        bPcbLocationDetails.add(bPcbLocationDetail);
        PowerMockito.when(mdsRemoteService.getPreManuMDSProgrammingList(Mockito.any())).thenReturn(new ArrayList<>()).thenReturn(partBomCodePreManuList);
        PowerMockito.when(bPcbLocationDetailRepository.selectBPcbLocationDetailByProductCode(Mockito.any())).thenReturn(bPcbLocationDetails);
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        bPcbLocationDetail.setPointLoc("D2");
        PowerMockito.when(mdsRemoteService.getPreManuMDSProgrammingList(Mockito.any())).thenReturn(new ArrayList<>()).thenReturn(partBomCodePreManuList);
        try {
            bPcbLocationDetailService.preManuMDSProgrammingSync(bsAsyncDataDTO);
        } catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        try{
            Whitebox.invokeMethod(bPcbLocationDetailService,"syncXPFromMds",bsAsyncDataDTO, isSuccess);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG,e.getMessage());
        }
    }

    /* Started by AICoder, pid:2c3dfo9f12o3033144cf0897607df755de474f8e */
    @Test
    public void testGetList() throws Exception {
        Map map = new HashMap();
        map.put("pointLoc", "R55");
        map.put("productCode", "139571751152ZTA");
        String prodplanId = "7011619";
        List<BPcbLocationDetail> bPcbLocationDetailList = new ArrayList<>();
        BPcbLocationDetail bPcbLocationDetail = new BPcbLocationDetail();
        bPcbLocationDetail.setProductCode("139571751152ZTA");
        bPcbLocationDetail.setLocationCode("R55");
        bPcbLocationDetail.setItemCode("118020100878");
        bPcbLocationDetailList.add(bPcbLocationDetail);
        BPcbLocationDetail bPcbLocationDetail1 = new BPcbLocationDetail();
        bPcbLocationDetail1.setProductCode("139571751152ZTA");
        bPcbLocationDetail1.setLocationCode("R55");
        bPcbLocationDetail1.setItemCode("008020100878");
        bPcbLocationDetailList.add(bPcbLocationDetail1);
        PowerMockito.when(bPcbLocationDetailRepository.getPage(map)).thenReturn(bPcbLocationDetailList);
        List<BProdBomChangeDetailDTO> bProdBomChangeDetailDTOS = new ArrayList<>();
        BProdBomChangeDetailDTO bProdBomChangeDetailDTO = new BProdBomChangeDetailDTO();
        bProdBomChangeDetailDTO.setOriginalProductCode("139571751152ZTB");
        bProdBomChangeDetailDTO.setProductCode("139571751152ZTA_1");
        bProdBomChangeDetailDTO.setOriginalItemCode("008020100878");
        bProdBomChangeDetailDTO.setItemCode("118020100878");
        bProdBomChangeDetailDTO.setChiDesc("贴片绿色发光二极管");
        bProdBomChangeDetailDTOS.add(bProdBomChangeDetailDTO);
        PowerMockito.when(bProdBomChangeDetailRepository.queryBProdBomDetailChangeList(Mockito.any())).thenReturn(null);
        bPcbLocationDetailService.getList(map, null);
        bPcbLocationDetailService.getList(map, prodplanId);
        PowerMockito.when(bProdBomChangeDetailRepository.queryBProdBomDetailChangeList(Mockito.any())).thenReturn(bProdBomChangeDetailDTOS);
        bPcbLocationDetailService.getList(map, prodplanId);
        bProdBomChangeDetailDTO.setOriginalProductCode("139571751152ZTA");
        List<BPcbLocationDetail> list = bPcbLocationDetailService.getList(map, prodplanId);
        Assert.assertEquals(list.get(0).getItemCode(), "118020100878");
    }
    /* Ended by AICoder, pid:2c3dfo9f12o3033144cf0897607df755de474f8e */

}