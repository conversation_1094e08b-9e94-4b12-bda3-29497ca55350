package com.zte.application.impl.craftTech;

import com.google.common.collect.Lists;
import com.zte.application.craftTech.CtRouteHeadService;
import com.zte.application.kafka.producer.CraftInfoSynchronizationProducer;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BoardInfo;
import com.zte.domain.model.BoardRepository;
import com.zte.domain.model.craftTech.*;
import com.zte.interfaces.dto.CtBasicDTO;
import com.zte.interfaces.dto.CtBasicVersionUpdateDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.CtRouteInfoDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
public class CtBasicServiceImplTest {

    @InjectMocks
    private CtBasicServiceImpl service;
    @Mock
    private CtRouteHeadService ctRouteHeadService;

    @Mock
    private CtBasicRepository repository;

    @Mock
    private CraftInfoSynchronizationProducer craftInfoSynchronizationProducer;

    @Mock
    private CtRouteHeadRepository ctRouteHeadRepository;

    @Mock
    private CtRouteDetailRepository ctRouteDetailRepository;

	@Mock
	private BoardRepository boardRepository;

    @Test
    public void testSelectCtBasicByItemOrTaskWithCraftStatus() throws Exception {
        List<CtBasicDTO> list = new ArrayList<>();
        List<CtBasicDTO> list2 = new ArrayList<>();
        CtBasic ctBasic = new CtBasic();
        ctBasic.setItemOrTask("1234567890abcde");
        CtBasicDTO dto1 = new CtBasicDTO();
        CtBasicDTO dto2 = new CtBasicDTO();
        CtBasicDTO dto3 = new CtBasicDTO();
        dto1.setCraftStatus(Constant.WE_STATUS);
        dto2.setCraftStatus(Constant.STATUS_FICTION);
        dto3.setCraftStatus("test");
        list.add(dto1);
        list.add(dto3);
        list.add(dto2);
        PowerMockito.when(repository.selectCtBasicByItemOrTaskWithCraftStatus(any())).thenReturn(list);
        service.selectCtBasicByItemOrTaskWithCraftStatus(ctBasic);
        PowerMockito.when(repository.selectCtBasicByItemOrTaskWithCraftStatus(any())).thenReturn(list2);
        Assert.assertNotNull(service.selectCtBasicByItemOrTaskWithCraftStatus(ctBasic));
    }

    @Test
    public void testSelectCtBasicByItemOrTaskWithCraftStatusWhereStatusIsScrapped() throws Exception {
        List<CtBasicDTO> list = new ArrayList<>();
        List<CtBasicDTO> list2 = new ArrayList<>();
        CtBasic ctBasic = new CtBasic();
        ctBasic.setItemOrTask("1234567890abcde");
        CtBasicDTO dto1 = new CtBasicDTO();
        CtBasicDTO dto2 = new CtBasicDTO();
        CtBasicDTO dto3 = new CtBasicDTO();
        CtBasicDTO dto4 = new CtBasicDTO();
        dto1.setCraftVersion("V.A");
        dto2.setCraftVersion("V.C");
        dto3.setCraftVersion("V.B");
        list.add(dto1);
        list.add(dto2);
        list.add(dto3);
        list.add(dto4);
        PowerMockito.when(repository.selectCtBasicByItemOrTaskWithCraftStatus(any())).thenReturn(list2);
        service.selectCtBasicByItemOrTaskWithCraftStatus(ctBasic);
        PowerMockito.when(repository.selectScrappedCtBasicWithCraftStatus(any())).thenReturn(list);
        Assert.assertNotNull(service.selectCtBasicByItemOrTaskWithCraftStatus(ctBasic));
    }

    @Test
    public void versionUpdate() throws Exception {

        CtBasicVersionUpdateDTO dto = new CtBasicVersionUpdateDTO();
        dto.setCraftId("craftId");

        CtBasicDTO ctBasicDTO = new CtBasicDTO();
        ctBasicDTO.setCraftVersion("V.AB");
        when(repository.selectCtBasicById(anyObject())).thenReturn(ctBasicDTO);

        List<CtRouteInfoDTO> ctRouteInfo = Lists.newArrayList();
        for (int i = 0; i < 2; i++) {
            CtRouteInfoDTO ctRouteInfoDTO = new CtRouteInfoDTO();
            ctRouteInfoDTO.setRouteId(String.valueOf(i));

            List<CtRouteDetailDTO> listDetail = Lists.newArrayList();
            listDetail.add(new CtRouteDetailDTO());
            ctRouteInfoDTO.setListDetail(listDetail);
            ctRouteInfo.add(ctRouteInfoDTO);
        }
        when(ctRouteHeadService.getBatchRouteHeadAndDetail(anyMap())).thenReturn(ctRouteInfo);
        service.versionUpdate(dto);
        verify(repository, times(1)).insertCtBasicSelective(anyObject());
        verify(ctRouteHeadService, times(2)).insertCtRouteHeadSelective(anyObject());
        verify(ctRouteDetailRepository, times(2)).batchInsertRouteDetail(anyList());

        PowerMockito.when(repository.selectCtBasicById(any())).thenReturn(null);
        Assert.assertThrows(MesBusinessException.class, () -> service.versionUpdate(dto));
    }

    @Test(expected = Exception.class)
    public void testGetListByItemNoOpenApi() throws Exception {
        List<CtBasicOpenApi> list1 = new ArrayList();
        List<CtBasicOpenApi> list2 = new ArrayList();
        String routeDetail = "test->test1";
        CtBasicOpenApi ctBasicOpenApi = new CtBasicOpenApi();
        ctBasicOpenApi.setRouteDetail(routeDetail);
        list2.add(ctBasicOpenApi);
        CtBasicDTO ctBasicDTO = new CtBasicDTO();
        String itemNos = "129571751001ZTQ,122098451106AIB";
        String itemNos300 = "1";
        StringBuilder itemNo = new StringBuilder(itemNos300);
        for (int i = 0; i <= 301; i++) {
            itemNo.insert(0, "123,");
        }
        ctBasicDTO.setItemNos(itemNos);
        CtBasicDTO ctBasicDTO2 = new CtBasicDTO();
        CtBasicDTO ctBasicDTO3 = new CtBasicDTO();
        ctBasicDTO3.setItemNos(itemNo.toString());
        PowerMockito.when(repository.getListByItemNoOpenApi(any(), any())).thenReturn(list1);
        service.getListByItemNoOpenApi(ctBasicDTO);
        PowerMockito.when(repository.getListByItemNoOpenApi(any(), any())).thenReturn(list2);
        service.getListByItemNoOpenApi(ctBasicDTO);
        service.getListByItemNoOpenApi(ctBasicDTO2);
        service.getListByItemNoOpenApi(ctBasicDTO3);
    }

    @Test
    public void itemCodeRPAD() {
        service.itemCodeRPAD();
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void pushAfterRPAD() {
        service.pushAfterRPAD(null, null);
        service.pushAfterRPAD(null, 10000);
        service.pushAfterRPAD(10000, null);
        service.pushAfterRPAD(10000, 10000);
        service.pushAfterRPAD(1, 1);
        PowerMockito.when(repository.getRPADCraft(anyInt(), anyInt()))
                .thenReturn(Lists.newArrayList(new CtBasicDTO() {{
                    setCraftId("1");
                }}));
        service.pushAfterRPAD(1, 1);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

	@Test
	public void updateSync() throws Exception {
		List<String> list = new ArrayList<>();
		list.add("test");
		PowerMockito.when(repository.getSyncPage(any())).thenReturn(list);
		List<BoardInfo> infoList =  new ArrayList<>();
		BoardInfo boardInfo = new BoardInfo();
		boardInfo.setCraftUpdatedBy("刘子荷00286569");
		boardInfo.setBomNo("test");
		infoList.add(boardInfo);
		PowerMockito.when(boardRepository.getUpdatedBy(anyList())).thenReturn(infoList);
		service.updateSync();
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
	}

	@Test
	public void getUpdatedBy() throws Exception {
		List<String> list = new ArrayList<>();
		list.add("test");
		List<BoardInfo> infoList =  new ArrayList<>();
		BoardInfo boardInfo = new BoardInfo();
		boardInfo.setCraftUpdatedBy("刘子荷00286569");
		boardInfo.setBomNo("test");
		infoList.add(boardInfo);
		PowerMockito.when(boardRepository.getUpdatedBy(anyList())).thenReturn(infoList);
        Assert.assertNotNull(service.getUpdatedBy(list));
	}
}
