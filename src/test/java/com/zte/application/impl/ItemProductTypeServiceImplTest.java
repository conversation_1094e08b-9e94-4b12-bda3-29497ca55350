package com.zte.application.impl;

import com.zte.application.SysLookupValuesService;
import com.zte.domain.model.ItemProductType;
import com.zte.domain.model.ItemProductTypeRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.ItemProductTypePageDTO;
import com.zte.springbootframe.common.model.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.slf4j.Logger;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023/3/22 17:38
 */
public class ItemProductTypeServiceImplTest {
    @Mock
    Logger logger;
    @Mock
    ItemProductTypeRepository itemProductTypeRepository;
    @Mock
    SysLookupValuesService sysLookupValuesService;
    @InjectMocks
    ItemProductTypeServiceImpl itemProductTypeServiceImpl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testDeleteItemProductTypeById() throws Exception {
        when(itemProductTypeRepository.deleteItemProductTypeById(anyString())).thenReturn(0);

        Assert.assertNotNull(itemProductTypeServiceImpl.deleteItemProductTypeById("id"));
    }

    @Test
    public void testInsertItemProductType() throws Exception {
        when(itemProductTypeRepository.insertItemProductType(any())).thenReturn(0);

        Assert.assertNotNull(itemProductTypeServiceImpl.insertItemProductType(new ItemProductType()));
    }

    @Test
    public void testSelectItemProductTypeById() throws Exception {
        when(itemProductTypeRepository.selectItemProductTypeById(anyString())).thenReturn(new ItemProductType());

        Assert.assertNotNull(itemProductTypeServiceImpl.selectItemProductTypeById("id"));
    }

    @Test
    public void testUpdateItemProductTypeByIdSelective() throws Exception {
        when(itemProductTypeRepository.updateItemProductTypeByIdSelective(any())).thenReturn(0);

        Assert.assertNotNull(itemProductTypeServiceImpl.updateItemProductTypeByIdSelective(new ItemProductType()));
    }

    @Test
    public void testSelectByItemPrefix() throws Exception {
        when(itemProductTypeRepository.selectByItemPrefix(any())).thenReturn(Arrays.<ItemProductType>asList(new ItemProductType()));

        Assert.assertNotNull(itemProductTypeServiceImpl.selectByItemPrefix("items"));
    }

    @Test
    public void testSelectByCondition() throws Exception {
        when(itemProductTypeRepository.selectByCondition(any())).thenReturn(Arrays.<ItemProductType>asList(new ItemProductType()));

        Assert.assertNotNull(itemProductTypeServiceImpl.selectByCondition(new ItemProductType()));
    }

    @Test
    public void testBatchInsert() throws Exception {
        when(itemProductTypeRepository.batchInsert(any())).thenReturn(0);

        Assert.assertNotNull(itemProductTypeServiceImpl.batchInsert(Arrays.<ItemProductType>asList(new ItemProductType())));
    }

    @Test
    public void testFindList() throws Exception {
        when(itemProductTypeRepository.selectListByPage(any())).thenReturn(Arrays.<ItemProductType>asList(new ItemProductType()));

        Page<ItemProductType> result = itemProductTypeServiceImpl.findList(new ItemProductTypePageDTO());
        Assert.assertTrue(result.getRows().size() >= 0);
    }

    @Test
    public void testExportModel() throws Exception {
        try{
            itemProductTypeServiceImpl.exportModel(null);
        } catch (Exception e) {
            Assert.assertEquals("getHSSFWorkbook:", e.getMessage());
        }
    }

    @Test
    public void testMergeItemProductType() throws Exception {
        when(itemProductTypeRepository.mergeItemProductType(any())).thenReturn(0);

        Assert.assertNotNull(itemProductTypeServiceImpl.mergeItemProductType(Arrays.<ItemProductType>asList(new ItemProductType())));
    }

    @Test
    public void testHomeTerminal() throws Exception {
        when(itemProductTypeRepository.selectByItemPrefix(any())).thenReturn(Arrays.<ItemProductType>asList(new ItemProductType()));
        when(sysLookupValuesService.findByLookupType(anyString())).thenReturn(Arrays.<SysLookupValues>asList(new SysLookupValues()));

        Assert.assertFalse(itemProductTypeServiceImpl.homeTerminal("item", "factoryId"));
    }

    @Test
    public void testHomeTerminal2() throws Exception {
        when(itemProductTypeRepository.selectByItemPrefix(any())).thenReturn(Arrays.<ItemProductType>asList());
        when(sysLookupValuesService.findByLookupType(anyString())).thenReturn(Arrays.<SysLookupValues>asList(new SysLookupValues(){{setLookupMeaning("factoryId");}}));

        Assert.assertFalse(itemProductTypeServiceImpl.homeTerminal("item", "factoryId"));
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme