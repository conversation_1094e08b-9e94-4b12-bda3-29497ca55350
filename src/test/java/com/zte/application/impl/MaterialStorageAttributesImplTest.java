package com.zte.application.impl;

import com.zte.domain.model.MaterialStorageAttributesRepository;
import com.zte.interfaces.dto.MaterialStorageAttributesDTO;
import com.zte.interfaces.dto.MaterialStorageAttributesLevelDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @description: TODO
 * @date 2023/9/19 10:34
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest()
public class MaterialStorageAttributesImplTest {

    @InjectMocks
    private MaterialStorageAttributesServiceImpl materialStorageAttributesService;
    @Mock
    private MaterialStorageAttributesRepository materialStorageAttributesRepository;

    @Test
    public void levelQuery() {
        MaterialStorageAttributesDTO query = new MaterialStorageAttributesDTO();
        materialStorageAttributesService.levelQuery(query);
        List<MaterialStorageAttributesDTO> list = new ArrayList<>();
        MaterialStorageAttributesDTO result = new MaterialStorageAttributesDTO();
        result.setBarcode("111");
        list.add(result);
        PowerMockito.when(materialStorageAttributesRepository.selectEntityListPage(any())).thenReturn(list);
        query.setBarcode("1");
        materialStorageAttributesService.levelQuery(query);
        PowerMockito.when(materialStorageAttributesRepository.selectEntityListPage(any())).thenReturn(null);
        materialStorageAttributesService.levelQuery(query);
        PowerMockito.when(materialStorageAttributesRepository.selectEntityListPage(any())).thenReturn(list);
        query.setBarcode(null);
        query.setItemCode("111");
        query.setSupplier("null");
        materialStorageAttributesService.levelQuery(query);
        PowerMockito.when(materialStorageAttributesRepository.selectEntityListPage(any())).thenReturn(null);
        materialStorageAttributesService.levelQuery(query);
        PowerMockito.when(materialStorageAttributesRepository.selectEntityListPage(any())).thenReturn(list);
        query.setSupplier(null);
        MaterialStorageAttributesDTO materialStorageAttributesDTO = materialStorageAttributesService.levelQuery(query);
        Assert.assertEquals("111", materialStorageAttributesDTO.getBarcode());
        PowerMockito.when(materialStorageAttributesRepository.selectEntityListPage(any())).thenReturn(null);
        materialStorageAttributesService.levelQuery(query);
    }

    @Test
    public void batchQuery() {
        List<MaterialStorageAttributesDTO> list = new ArrayList<>();
        for (int i = 0; i < 501; i++) {
            list.add(new MaterialStorageAttributesDTO());
        }
        Assert.assertThrows(MesBusinessException.class,()->materialStorageAttributesService.batchQuery(list));
        list.remove(0);
        Assert.assertThrows(MesBusinessException.class,()->materialStorageAttributesService.batchQuery(new ArrayList<>()));

        materialStorageAttributesService.batchQuery(list);
        list.clear();
        MaterialStorageAttributesDTO dto = new MaterialStorageAttributesDTO();
        dto.setSupplier("111");
        dto.setBarcode("111");
        dto.setItemCode("111");
        list.add(dto);
        MaterialStorageAttributesLevelDTO result = materialStorageAttributesService.batchQuery(list);
        Assert.assertEquals(0, result.getBarCodeList().size());
    }
    @Test
    public void levelQueryBatchTest() {
        List<MaterialStorageAttributesDTO> params = new ArrayList<>();
        materialStorageAttributesService.levelQueryFreezeBatch(params);
        MaterialStorageAttributesDTO materialStorageAttributesDTO = new MaterialStorageAttributesDTO();
        params.add(materialStorageAttributesDTO);
        materialStorageAttributesService.levelQueryFreezeBatch(params);
        PowerMockito.when(materialStorageAttributesRepository.levelQueryBatch(any())).thenReturn(params);
        materialStorageAttributesService.levelQueryFreezeBatch(params);
        materialStorageAttributesDTO.setMaintenanceType("条码");
        materialStorageAttributesDTO.setBarcode("1234");
        Assert.assertEquals("1234", materialStorageAttributesService.levelQueryFreezeBatch(params).get(0));
    }

}
