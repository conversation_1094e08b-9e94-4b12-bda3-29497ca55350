package com.zte.application.impl;

import com.google.common.collect.Lists;
import com.zte.application.PublicHolidaySettingService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.CapacityCalendar;
import com.zte.domain.model.CapacityCalendarRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.util.BaseTestCase;
import org.checkerframework.checker.units.qual.A;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.mock.web.MockHttpServletResponse;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;

import static org.mockito.ArgumentMatchers.any;

@RunWith(PowerMockRunner.class)
public class CapacityCalendarServiceImplTest extends BaseTestCase {

    @InjectMocks
    CapacityCalendarServiceImpl service;

    @Mock
    private CapacityCalendarRepository repository;

    @Mock
    private PublicHolidaySettingService holidayService;

    @Mock
    private SysLookupValuesService lookupValuesService;

    @Test
    public void saveCalendarData() {
        CapacityCalendarListSaveDTO params = new CapacityCalendarListSaveDTO();
        try {
            service.saveCalendarData(params, "1");
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setSetDimension(Constant.PRODUCTION_UNIT);
        try {
            service.saveCalendarData(params, "1");
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setProductionUnits(Lists.newArrayList("1"));
        try {
            service.saveCalendarData(params, "1");
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setCapacityDays(Lists.newArrayList(new CapacityCalendarDTO(){{
            setCapacityDate(new Date());
            setCapacityUtilizationRate(100);
        }}));
        service.saveCalendarData(params, "1");
        params.setSetDimension("1");
        service.saveCalendarData(params, "1");
        params.setSetDimension(Constant.WORK_ORDER_CATEGORY);
        try {
            service.saveCalendarData(params, "1");
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setWorkOrderCategorys(Lists.newArrayList("1"));
        service.saveCalendarData(params, "1");
        params.setWorkOrderCategorys(null);
        params.setSetDimension(Constant.MODEL);
        try {
            service.saveCalendarData(params, "1");
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setWorkOrderCategorys(Lists.newArrayList("1"));
        try {
            service.saveCalendarData(params, "1");
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setPlanGroupIds(Lists.newArrayList("1"));
        try {
            service.saveCalendarData(params, "1");
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setModelNames(Lists.newArrayList("1"));
        service.saveCalendarData(params, "1");
        params.setModelNames(Lists.newArrayList("1","1","1","1","1","1","1","1","1","1","1","1","1"));
        try {
            service.saveCalendarData(params, "1");
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setPlanGroupIds(Lists.newArrayList("1","1","1","1","1","1","1","1","1","1","1","1","1"));
        try {
            service.saveCalendarData(params, "1");
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
    }

    @Test
    public void export() throws IOException {
        CapacityCalendarExportDTO params = new CapacityCalendarExportDTO();
        Date date = new Date();
        params.setStartDate(date);
        params.setEndDate(date);
        service.export(params, new MockHttpServletResponse());
        Assert.assertNotNull(params);
    }

    @Test
    public void queryCapacityCalendar() throws IOException {
        CapacityCalendarExportDTO params = new CapacityCalendarExportDTO();
        Date date = new Date();
        params.setStartDate(date);
        params.setEndDate(date);
        service.queryCapacityCalendar(params);
        PowerMockito.when(repository.selectForExport(any())).thenReturn(Lists.newArrayList(
                new HashMap() {{
                    put(DateUtil.convertDateToString(date, DateUtil.DATE_FORMATE_DAY), new BigDecimal(2));
                }}
        ));
        Assert.assertNotNull(service.queryCapacityCalendar(params));
    }

    @Test
    public void selectPage() {
        CapacityCalendarQueryDTO params = new CapacityCalendarQueryDTO();
        Date date = new Date();
        params.setStartDate(date);
        params.setEndDate(date);
        Page<CapacityCalendarHeaderDTO> page = service.selectPage(params);
        Assert.assertTrue(page.getRows().size() >= 0);
    }

    @Test
    public void getList() {
        CapacityCalendarListQueryDTO params = new CapacityCalendarListQueryDTO();
        try {
            service.getList(params);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setStartDate(new Date());
        try {
            service.getList(params);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setEndDate(new Date(System.currentTimeMillis() - 10000));
        try {
            service.getList(params);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setEndDate(new Date());
        service.getList(params);
        PowerMockito.when(repository.selectList(any())).thenReturn(Lists.newArrayList(
                new CapacityCalendar() {{
                    setCapacityDate(new Date());
                    setDayOfWeek(1);
                }}
        ));
        service.getList(params);
    }

    @Test
    public void getCalendarData() {
        CapacityCalendarListQueryDTO params = new CapacityCalendarListQueryDTO();
        try {
            service.getCalendarData(params);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setStartDate(new Date());
        try {
            service.getCalendarData(params);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setSetDimension(Constant.PRODUCTION_UNIT);
        try {
            service.getCalendarData(params);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setProductionUnit("1");
        try {
            service.getCalendarData(params);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        service.getCalendarData(params);
        params.setSetDimension(Constant.WORK_ORDER_CATEGORY);
        try {
            service.getCalendarData(params);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setWorkOrderCategory("1");
        service.getCalendarData(params);
        params.setWorkOrderCategory(null);
        params.setSetDimension(Constant.MODEL);
        try {
            service.getCalendarData(params);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setWorkOrderCategory("1");
        try {
            service.getCalendarData(params);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setPlanGroupId("1");
        try {
            service.getCalendarData(params);
        } catch (MesBusinessException e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
        params.setModelName("1");
        PowerMockito.when(repository.selectList(any())).thenReturn(Lists.newArrayList(
                new CapacityCalendar() {{
                    setCapacityDate(new Date());
                }},
                new CapacityCalendar() {{
                    setCapacityDate(new Date());
                    setCapacityUtilizationRate(100);
                }}
        ));
        PowerMockito.when(holidayService.queryHolidayForCalendar(any())).thenReturn(
                Lists.newArrayList(new PublicHolidaySettingDTO() {{
                    setStartDate(new Date());
                }})
        );
        PowerMockito.when(lookupValuesService.selectValuesByType(Constant.TYPE_8884)).thenReturn(
                Lists.newArrayList(new SysLookupValues() {{
                    setLookupMeaning("1");
                }})
        );
        service.getCalendarData(params);
    }
}