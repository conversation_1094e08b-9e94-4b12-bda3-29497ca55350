package com.zte.application.impl;

import com.alibaba.excel.context.AnalysisContextImpl;
import com.alibaba.excel.read.metadata.holder.ReadSheetHolder;
import com.zte.application.SysLookupValuesService;
import com.zte.common.CommonUtils;
import com.zte.common.authority.HttpConstant;
import com.zte.common.excel.ResourceUseAnalysisEventListener;
import com.zte.common.model.MessageId;
import com.zte.domain.model.ResourceInfoDetailRepository;
import com.zte.domain.model.ResourceInfoRepository;
import com.zte.domain.model.ResourceOptLogRepository;
import com.zte.domain.model.ResourceUseInfoRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.ResourceInfoDetailDTO;
import com.zte.interfaces.dto.ResourceOptLogDTO;
import com.zte.interfaces.dto.ResourceUseInfoDTO;
import com.zte.interfaces.dto.ResourceUseInfoImportDTO;
import com.zte.interfaces.dto.ResourceUseInfoImportReqDTO;
import com.zte.interfaces.dto.ResourceUseInfoImportResDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>  魏振东
 * @description: TODO
 * @date 2023/10/9 上午11:03
 */
@PrepareForTest({BeanUtils.class, MESHttpHelper.class, RedisHelper.class, UUID.class, JacksonJsonConverUtil.class, HttpClientUtil.class, MESHttpHelper.class, CommonUtils.class, RedisHelper.class, HttpClientUtil.class, CommonUtils.class})
public class ResourceUseInfoServiceImplTest extends BaseTestCase {

    @InjectMocks
    ResourceUseInfoServiceImpl resourceUseInfoService;
    @InjectMocks
    ResourceOptLogServiceImpl resourceOptLogService;
    @Value("${license.use.export.max}")
    private Integer maxExport;
    @Mock
    ResourceInfoRepository resourceInfoRepository;
    @Mock
    ResourceUseInfoRepository repository;
    @Mock
    ResourceInfoDetailServiceImpl resourceInfoDetailService;
    @Mock
    ResourceInfoDetailRepository resourceInfoDetailRepository;

    @Mock
    SysLookupValuesService sysLookupValuesService;
    @Mock
    IdGenerator idGenerator;
    @Mock
    EmailUtils emailUtils;
    @Mock
    ResourceOptLogRepository resourceOptLogRepository;
    @Mock
    RedisTemplate redisTemplate;
    @Mock
    private HttpServletResponse response;
    @Mock
    ServletOutputStream outputStream;
    @Mock
    ValueOperations<String, String> valueOperations;

    @Mock
    CloudDiskHelper cloudDiskHelper;

    @Mock
    AnalysisContextImpl analysisContext;

    @Mock
    ReadSheetHolder readSheetHolder;

    @Mock
    MultipartFile file;


    @Test
    public void batchInsert() throws Exception {
        resourceUseInfoService.batchInsert(null);
        List<ResourceUseInfoDTO> resourceUseInfoDTOList = new ArrayList<>();
        resourceUseInfoDTOList.add(new ResourceUseInfoDTO());
        ResourceUseInfoDTO a1 = new ResourceUseInfoDTO();
        a1.setReportStatus("0");
        a1.setRegistStatus("0");
        resourceUseInfoDTOList.add(a1);
        resourceUseInfoService.batchInsert(resourceUseInfoDTOList);
        Assert.assertNotNull(resourceUseInfoDTOList);
    }

    @Test
    public void unBind() throws Exception {
        try {
            resourceUseInfoService.unBind("", "");
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.PARAMS_ERROR));
        }
        try {
            resourceUseInfoService.unBind("sn", "");
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.NO_CURRENT_BARCODE_BINDING_RECORD_FOUND));
        }
        List<ResourceUseInfoDTO> resourceUseInfoDTOList = new ArrayList<>();
        resourceUseInfoDTOList.add(new ResourceUseInfoDTO());
        PowerMockito.when(repository.queryByBarcode(any())).thenReturn(resourceUseInfoDTOList);
        resourceUseInfoService.unBind("sn", "");
    }

    @Test
    public void pageTest() throws Exception {
        ResourceUseInfoDTO dto = new ResourceUseInfoDTO();
        dto.setResourceNo("123");
        dto.setReportStatus("0");
        dto.setRegistStatus("0");
        dto.setIsRework("0");
        ResourceUseInfoDTO dto1 = new ResourceUseInfoDTO();
        dto1.setResourceNo("123");
        dto1.setReportStatus("0");
        dto1.setRegistStatus("0");
        dto1.setIsRework("1");
        List<ResourceUseInfoDTO> dtoList = new ArrayList<>();
        dtoList.add(dto);
        dtoList.add(dto1);
        PowerMockito.when(repository.page(any())).thenReturn(dtoList);
        PowerMockito.when(sysLookupValuesService.selectValuesByType(any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(resourceUseInfoService.page(dto));
        dto.setResourceNum("123");
        Assert.assertNotNull(resourceUseInfoService.page(dto));
        dto.setResourceNum("123\n123");
        Assert.assertNotNull(resourceUseInfoService.page(dto));
        StringBuilder num = new StringBuilder("123,");
        for(int i=0; i<= 100; i++) {
            num.append("123,");
        }
        dto.setResourceNum(num.toString());
        try {
            resourceUseInfoService.page(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.RESOURCE_NUM_MAX_ONE_HUNDRED, e.getMessage());
        }
    }

    @Test
    public void groupQueryUse() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("sss");
        ResourceUseInfoDTO dto = new ResourceUseInfoDTO();
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.groupQueryUse(dto));
        dto.setCreateEndDate(null);
        dto.setCreateStartDate("2023-09-10 00:00:00");
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.groupQueryUse(dto));
        dto.setCreateStartDate("2023-09-10 00:00:00");
        dto.setCreateEndDate("2023-10-12 00:00:00");
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.groupQueryUse(dto));
        dto.setCreateEndDate("2023-10-10 00:00:00");

        List<ResourceUseInfoDTO> dtoList = new ArrayList<>();
        dtoList.add(dto);
        PowerMockito.when(repository.groupQueryUseByResourceNo(any())).thenReturn(dtoList);
        resourceUseInfoService.groupQueryUse(dto);
    }

    @Test
    public void exportUpload() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class, MESHttpHelper.class);
        Map<String, String> header = new HashMap<>();
        header.put("x-factory-id", "51");
        header.put("X-Emp-No", "10296137");
        header.put(HttpConstant.X_AUTH_VALUE, "token");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);

        ReflectionTestUtils.setField(resourceUseInfoService, "maxExport", 3);
        ReflectionTestUtils.setField(resourceUseInfoService, "lockExpire", 360);

        List<ResourceUseInfoDTO> resourceUseInfoDTOList = new ArrayList<>();
        for (int i = 0; i < 101; i++) {
            ResourceUseInfoDTO resourceUseInfoDTO = new ResourceUseInfoDTO();
            resourceUseInfoDTO.setResourceNo(i + "resourceNo");
            resourceUseInfoDTO.setIsRework("1");
            resourceUseInfoDTO.setUseQty(i);
            resourceUseInfoDTOList.add(resourceUseInfoDTO);
        }
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.exportUpload(resourceUseInfoDTOList));
        resourceUseInfoDTOList.get(0).setUseQty(5000001);
        resourceUseInfoDTOList.remove(100);
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.exportUpload(resourceUseInfoDTOList));
        resourceUseInfoDTOList.get(0).setUseQty(1);
        resourceUseInfoDTOList.get(0).setCreateStartDate("2023-09-10 00:00:00");
        resourceUseInfoDTOList.get(0).setCreateEndDate("2023-10-12 00:00:00");
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.exportUpload(resourceUseInfoDTOList));
        resourceUseInfoDTOList.get(0).setCreateEndDate("2023-10-01 00:00:00");

        List<ResourceUseInfoDTO> dtoList = new ArrayList<>();
        ResourceUseInfoDTO dto = new ResourceUseInfoDTO();
        dto.setIsRework("0");
        dto.setBarcode("111");
        dto.setResourceNum("222");
        dtoList.add(dto);
        PowerMockito.when(repository.pageQueryUseInfo(any())).thenReturn(dtoList);

        PowerMockito.when(cloudDiskHelper.fileUpload("1", "1")).thenReturn("111");
        PowerMockito.when(cloudDiskHelper.getFileDownloadUrl(any(), any(), any())).thenReturn("111");
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        PowerMockito.when(valueOperations.increment(any())).thenReturn(4L);
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.exportUpload(resourceUseInfoDTOList));
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        PowerMockito.when(valueOperations.increment(any())).thenReturn(1L);
        resourceUseInfoService.exportUpload(resourceUseInfoDTOList);
    }

    @Test
    public void queryExportDataTest() {
        ResourceUseInfoDTO dto = new ResourceUseInfoDTO();
        dto.setResourceNo("123");
        dto.setReportStatus("0");
        dto.setRegistStatus("0");
        dto.setIsRework("0");
        List<ResourceUseInfoDTO> dtoList = new ArrayList<>();
        dtoList.add(dto);
        PowerMockito.when(repository.page(any())).thenReturn(dtoList);
        PowerMockito.when(sysLookupValuesService.selectValuesByType(any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(resourceUseInfoService.queryExportData(dto, 1, 10));
    }


    @Test
    public void countExportTotalTest() {
        ResourceUseInfoDTO dto = new ResourceUseInfoDTO();
        ReflectionTestUtils.setField(resourceUseInfoService, "maxExportCount", 100000);
        PowerMockito.when(repository.queryCount(any())).thenReturn(200000);
        try {
            resourceUseInfoService.countExportTotal(dto);
            assert 1 == 2;
        } catch (MesBusinessException e) {
            assert MessageId.EXCEED_MAX_EXPORT_COUNT.equals(e.getExMsgId());
        }

        PowerMockito.when(repository.queryCount(any())).thenReturn(50000);
        int count = resourceUseInfoService.countExportTotal(dto);
        Assert.assertEquals(50000, count);
    }

    @Test
    public void validRequestTest() throws Exception {
        ResourceUseInfoDTO dto = new ResourceUseInfoDTO();
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.validRequest(null));
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.validRequest(dto));
        dto.setDistributeStartDate("2023-01-10 00:00:00");
        dto.setDistributeEndDate("2023-05-10 00:00:00");
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.validRequest(dto));
        dto.setDistributeEndDate("2023-02-10 00:00:00");
        resourceUseInfoService.validRequest(dto);
        dto.setReportStartDate("2023-01-10 00:00:00");
        dto.setReportEndDate("2023-05-10 00:00:00");
        resourceUseInfoService.validRequest(dto);
        dto.setReportEndDate("2023-02-10 00:00:00");
        resourceUseInfoService.validRequest(dto);
        dto.setCreateStartDate("2023-01-10 00:00:00");
        dto.setCreateEndDate("2023-05-10 00:00:00");
        dto.setReportStartDate(null);
        dto.setReportEndDate(null);
        dto.setDistributeStartDate(null);
        dto.setDistributeEndDate(null);
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.validRequest(dto));
        dto.setCreateEndDate("2023-02-10 00:00:00");
        resourceUseInfoService.validRequest(dto);
        dto.setBarcode("123");
        resourceUseInfoService.validRequest(dto);
        dto.setResourceNum("123");
        resourceUseInfoService.validRequest(dto);
        dto.setResourceNo("123");
        resourceUseInfoService.validRequest(dto);
        ResourceUseInfoDTO dto1 = new ResourceUseInfoDTO();
        dto1.setReportStartDate("2023-01-10 00:00:00");
        dto1.setReportEndDate(null);
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.validRequest(dto1));
        dto1.setReportStartDate(null);
        dto1.setReportEndDate("2023-01-10 00:00:00");
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.validRequest(dto1));

    }

    @Test
    public void optionAndReturnErrDTO() {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("sss");
        Map<String, String> map = new HashMap<>();
        Assert.assertNotNull(resourceUseInfoService.optionAndReturnErrDTO(new ArrayList<>(), false, map));
        List<ResourceUseInfoImportDTO> list = new ArrayList<>();
        ResourceUseInfoImportDTO dto = new ResourceUseInfoImportDTO();
        dto.setResourceNum("12345678");
        dto.setResourceNo("12345678");
        dto.setBarcode("123");
        list.add(dto);
        list.add(dto);
        ResourceUseInfoImportDTO dto3 = new ResourceUseInfoImportDTO();
        dto3.setResourceNum("12345678");
        dto3.setResourceNo("12345678");
        dto3.setBarcode("1234");
        list.add(dto3);
        ResourceUseInfoImportDTO dto1 = new ResourceUseInfoImportDTO();
        dto1.setResourceNum("12345");
        dto1.setResourceNo("12345678");
        list.add(dto1);
        ResourceUseInfoImportDTO dto2 = new ResourceUseInfoImportDTO();
        dto2.setResourceNum("123456789");
        dto2.setResourceNo("12345678");
        dto2.setRepeatFlag(true);
        list.add(dto2);
        ResourceUseInfoImportDTO dto4 = new ResourceUseInfoImportDTO();
        dto4.setResourceNum("123451");
        dto4.setResourceNo("12345678");
        dto4.setRepeatFlag(true);
        list.add(dto4);
        ResourceUseInfoImportDTO dto5 = new ResourceUseInfoImportDTO();
        dto5.setResourceNum("12345678");
        dto5.setResourceNo("1234567811");
        dto5.setRepeatFlag(true);
        list.add(dto5);
        List<ResourceInfoDetailDTO> detailList = new ArrayList<>();
        ResourceInfoDetailDTO detailDTO = new ResourceInfoDetailDTO();
        detailDTO.setResourceNum("12345678");
        detailDTO.setResourceNo("12345678");
        detailDTO.setStatus("1");
        detailList.add(detailDTO);
        ResourceInfoDetailDTO detailDTO1 = new ResourceInfoDetailDTO();
        detailDTO1.setResourceNum("12345a");
        detailDTO1.setResourceNo("1234567");
        detailDTO1.setStatus("1");
        detailList.add(detailDTO1);
        ResourceInfoDetailDTO detailDTO2 = new ResourceInfoDetailDTO();
        detailDTO2.setResourceNum("123456789");
        detailDTO2.setResourceNo("12345678");
        detailDTO2.setStatus("2");
        detailList.add(detailDTO2);
        PowerMockito.when(resourceInfoDetailRepository.queryByResourceNumList(any())).thenReturn(detailList);
        PowerMockito.when(repository.queryByResourceNumList(any())).thenReturn(new ArrayList<>());
        PowerMockito.when(repository.queryByBarcodeList(any(), any(), any())).thenReturn(new ArrayList<>());
        PowerMockito.when(repository.batchInsert(any())).thenReturn(1);
        PowerMockito.when(repository.batchUpdateResourceById(any())).thenReturn(1);
        PowerMockito.when(resourceInfoDetailRepository.batchBinding(any())).thenReturn(1);
        PowerMockito.when(idGenerator.snowFlakeId()).thenReturn(123L);
        Assert.assertNotNull(resourceUseInfoService.optionAndReturnErrDTO(list, false, map));
        List<String> numList = new ArrayList<>();
        numList.add("12345678");
        PowerMockito.when(repository.queryByResourceNumList(any())).thenReturn(numList);
        Assert.assertNotNull(resourceUseInfoService.optionAndReturnErrDTO(list, false, map));
        List<ResourceUseInfoDTO> useInfoDTOList = new ArrayList<>();
        ResourceUseInfoDTO resourceUseInfoDTO = new ResourceUseInfoDTO();
        resourceUseInfoDTO.setResourceNum("12345678");
        resourceUseInfoDTO.setBarcode("123");
        ResourceUseInfoDTO resourceUseInfoDTO1 = new ResourceUseInfoDTO();
        resourceUseInfoDTO1.setResourceNum("123451");
        resourceUseInfoDTO1.setBarcode("123");
        useInfoDTOList.add(resourceUseInfoDTO);
        useInfoDTOList.add(resourceUseInfoDTO1);
        PowerMockito.when(repository.queryByResourceNumList(any())).thenReturn(new ArrayList<>());
        PowerMockito.when(repository.queryByBarcodeList(any(), any(), any())).thenReturn(useInfoDTOList);
        Assert.assertNotNull(resourceUseInfoService.optionAndReturnErrDTO(list, false, map));
        Assert.assertNotNull(resourceUseInfoService.optionAndReturnErrDTO(list, true, map));
        Assert.assertNotNull(resourceUseInfoService.optionAndReturnErrDTO(list, true, map));
    }


    @Test
    public void getImportSuccessKeyTest() {
        ResourceUseInfoImportResDTO dto = new ResourceUseInfoImportResDTO();
        dto.setCode("0000");
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        PowerMockito.when(valueOperations.increment(Mockito.any())).thenReturn(100L);
        Assert.assertNotNull(resourceUseInfoService.getImportSuccessKey("123"));

        PowerMockito.when(valueOperations.get(Mockito.any())).thenReturn("{code: 0000}");
        resourceUseInfoService.getImportSuccessKey("123");
    }


    @Test
    public void importTest() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("sss");
        ResourceUseInfoImportReqDTO reqDTO = new ResourceUseInfoImportReqDTO();
        reqDTO.setBarcodeType("SN");
        reqDTO.setResourceNo("123");
        ResourceUseAnalysisEventListener<ResourceUseInfoImportDTO> listener = new ResourceUseAnalysisEventListener<ResourceUseInfoImportDTO>(resourceUseInfoService, redisTemplate, cloudDiskHelper, reqDTO, "123");
        ResourceUseInfoImportDTO importDTO = new ResourceUseInfoImportDTO();
        importDTO.setBarcodeType("SN");
        importDTO.setResourceNo("123");
        PowerMockito.when(analysisContext.readSheetHolder()).thenReturn(readSheetHolder);
        PowerMockito.when(readSheetHolder.getApproximateTotalRowNumber()).thenReturn(100);
        listener.invoke(importDTO, analysisContext);
        listener.invoke(importDTO, analysisContext);
        reqDTO.setBarcodeType("0");
        listener.invoke(importDTO, analysisContext);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        PowerMockito.when(valueOperations.setIfAbsent(Mockito.any(), Mockito.any(), Mockito.anyLong(),
                Mockito.any())).thenReturn(true);
        for (int i = 0; i < 500; i++) {
            listener.invoke(importDTO, analysisContext);
        }
        listener.doAfterAllAnalysed(analysisContext);
        ResourceUseAnalysisEventListener<ResourceUseInfoImportDTO> listener1 = new ResourceUseAnalysisEventListener<ResourceUseInfoImportDTO>(resourceUseInfoService, redisTemplate, cloudDiskHelper, reqDTO, "123");
        PowerMockito.when(readSheetHolder.getApproximateTotalRowNumber()).thenReturn(300002);
        Assert.assertThrows(MesBusinessException.class, () -> listener1.invoke(importDTO, analysisContext));

        PowerMockito.when(readSheetHolder.getApproximateTotalRowNumber()).thenReturn(100);
        BusiException exception = new BusiException();
        Assert.assertThrows(BusiException.class, () -> listener.onException(exception, analysisContext));
        Assert.assertThrows(BusiException.class, () -> listener1.onException(exception, analysisContext));
        listener1.doAfterAllAnalysed(analysisContext);

    }

    @Test
    public void delTest() {
        resourceUseInfoService.deleteUseUseInfo(new ArrayList<>());
        PowerMockito.when(repository.deleteByIds(any())).thenReturn(1);
        PowerMockito.when(repository.batchInsert(any())).thenReturn(1);
        PowerMockito.when(resourceInfoDetailRepository.batchBinding(any())).thenReturn(1);
        List<ResourceUseInfoImportDTO> list = new ArrayList<>();
        ResourceUseInfoImportDTO dto = new ResourceUseInfoImportDTO();
        dto.setId(1234L);
        dto.setResourceNo("1234567");
        list.add(dto);
        resourceUseInfoService.deleteUseUseInfo(list);
        resourceUseInfoService.addUseInfo(new ArrayList<>(), true);
        resourceUseInfoService.addUseInfo(list, false);
        dto.setResourceNo("123");
        resourceUseInfoService.addUseInfo(list, false);
        ResourceUseInfoImportDTO dto1 = new ResourceUseInfoImportDTO();
        dto1.setId(12345L);
        dto1.setResourceNo("1234567");
        dto1.setRepeatFlag(true);
        list.add(dto1);
        resourceUseInfoService.addUseInfo(list, true);
        resourceUseInfoService.addUseInfo(list, false);
        Assert.assertNotNull(list);
    }

    @Test
    public void batchLogInsert() throws Exception {
        resourceOptLogService.batchInsert(null);
        List<ResourceOptLogDTO> resourceOptLogDTOS = new ArrayList<>();
        resourceOptLogDTOS.add(new ResourceOptLogDTO());

        PowerMockito.when(resourceOptLogRepository.batchInsert(any())).thenReturn(100);
        resourceOptLogService.batchInsert(resourceOptLogDTOS);
        Assert.assertNotNull(resourceOptLogDTOS);
    }

    @Test
    public void pageLog() throws Exception {
        // 触发单元测试
        Page<ResourceOptLogDTO> page = resourceOptLogService.page(new ResourceOptLogDTO());
        Assert.assertTrue(page.getRows().size() >= 0);
    }

    @Test
    public void testCheckImportReq() {
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.checkUseImportReq(null));
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.checkUseImportReq(new ResourceUseInfoImportReqDTO()));
        ResourceUseInfoImportReqDTO reqDTO = new ResourceUseInfoImportReqDTO();
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.checkUseImportReq(reqDTO));
        reqDTO.setBarcodeType("0");
        reqDTO.setResourceNo("123");
        PowerMockito.when(resourceInfoRepository.selectValidityByResourceNo(any())).thenReturn(0);
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.checkUseImportReq(reqDTO));
        PowerMockito.when(resourceInfoRepository.selectValidityByResourceNo(any())).thenReturn(1);
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.checkUseImportReq(reqDTO));
        reqDTO.setFile(file);
        resourceUseInfoService.checkUseImportReq(reqDTO);
    }


    @Test
    public void testGetDictionaryKeyValue() {
        List<SysLookupValues> sysLookupValues = new ArrayList<>();
        SysLookupValues lookupValues = new SysLookupValues();
        lookupValues.setLookupMeaning("123");
        lookupValues.setDescriptionChin("123");
        sysLookupValues.add(lookupValues);
        PowerMockito.when(sysLookupValuesService.selectValuesByType(any())).thenReturn(sysLookupValues);
        Assert.assertNotNull(resourceUseInfoService.getDictionaryKeyValue(123));
    }


    @Test
    public void testImportFile() {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        ResourceUseInfoImportReqDTO reqDTO = new ResourceUseInfoImportReqDTO();
        reqDTO.setFile(file);
        reqDTO.setBarcodeType("1");
        PowerMockito.when(resourceInfoRepository.selectValidityByResourceNo(any())).thenReturn(1);
        Assert.assertThrows(MesBusinessException.class, () -> resourceUseInfoService.importFile(reqDTO));
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        resourceUseInfoService.importFile(reqDTO);
    }

    @Test
    public void testCheckExcelData() {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("sss");
        List<ResourceUseInfoImportDTO> importList = new ArrayList<>();
        List<ResourceUseInfoImportDTO> errorList = new ArrayList<>();
        ResourceUseInfoImportDTO dto = new ResourceUseInfoImportDTO();
        dto.setResourceNum("2222222");
        dto.setBarcode("B8SO0TLmN5Bqy01");
        importList.add(dto);
        ResourceUseInfoImportDTO dto1 = new ResourceUseInfoImportDTO();
        dto1.setResourceNum("2222221");
        dto1.setBarcode("8BFyhaBvqmJutDV");
        importList.add(dto1);
        ResourceUseInfoImportDTO dto2 = new ResourceUseInfoImportDTO();
        dto2.setResourceNum("2222222");
        dto2.setBarcode("8BFyhaBvqmJutDV");
        importList.add(dto2);
        ResourceUseInfoImportDTO dto3 = new ResourceUseInfoImportDTO();
        dto3.setResourceNum("2222222");
        dto3.setBarcode("7qQYy8etPvn9O9G");
        importList.add(dto3);
        ResourceUseInfoImportDTO dto4 = new ResourceUseInfoImportDTO();
        dto4.setResourceNum("2222221");
        dto4.setBarcode("6ZRzSnxAkS2cNg5");
        importList.add(dto4);
        ResourceUseInfoImportDTO dto5 = new ResourceUseInfoImportDTO();
        dto5.setResourceNum("22222224");
        dto5.setBarcode("7qQYy8etPvn9O9G");
        importList.add(dto5);
        ResourceUseInfoImportDTO dto6 = new ResourceUseInfoImportDTO();
        dto6.setResourceNum("2222222");
        dto6.setBarcode("6ZRzSnxAkS2cNg5");
        importList.add(dto6);
        ResourceUseInfoImportDTO dto7 = new ResourceUseInfoImportDTO();
        dto7.setResourceNum("22222223");
        dto7.setBarcode("B8SO0TLmN5Bqy01");
        importList.add(dto7);
        ResourceUseInfoImportDTO dto8 = new ResourceUseInfoImportDTO();
        dto8.setResourceNum("22222224");
        dto8.setBarcode("8BFyhaBvqmJutDV");
        importList.add(dto8);
        ResourceUseInfoImportDTO dto9 = new ResourceUseInfoImportDTO();
        dto9.setResourceNum("22222225");
        dto9.setBarcode("8BFyhaBvqmJutDV");
        importList.add(dto9);
        ResourceUseInfoImportDTO dto11 = new ResourceUseInfoImportDTO();
        dto11.setResourceNum("22222224");
        dto11.setBarcode("7xbCeL4ycJREqdN");
        importList.add(dto11);
        ResourceUseInfoImportDTO dto12 = new ResourceUseInfoImportDTO();
        dto12.setResourceNum("2222222");
        dto12.setBarcode("7KqtvxTdCC1HBzl");
        importList.add(dto12);
        Assert.assertNotNull(resourceUseInfoService.checkExcelData(importList, errorList, true));
        Assert.assertNotNull(resourceUseInfoService.checkExcelData(importList, errorList, false));
    }


    @Test
    public void countExportTotal1() {
        ResourceOptLogDTO info = new ResourceOptLogDTO();
        try {
            resourceOptLogService.countExportTotal(info);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }
        info.setCreateStartDate("2023-12-19 00:00:00");
        try {
            resourceOptLogService.countExportTotal(info);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }
        info.setCreateStartDate(null);
        info.setCreateEndDate("2024-01-19 23:59:59");
        try {
            resourceOptLogService.countExportTotal(info);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.EXPORT_TIME_CAN_NOT_NULL, e.getMessage());
        }
        info.setCreateStartDate("2023-12-19 00:00:00");
        info.setCreateEndDate("2025-01-19 23:59:59");
        try {
            resourceOptLogService.countExportTotal(info);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.EXPORT_DRTAIL_TIME_INTERVAL_ERROR, e.getMessage());
        }
        info.setCreateEndDate("2023-12-20 23:59:59");
        Page<ResourceOptLogDTO> page = new Page<>(info.getPage(), info.getRows());
        page.setParams(info);

        PowerMockito.when(repository.page(Mockito.any())).thenReturn(null);
        try {
            resourceOptLogService.countExportTotal(info);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.EXPORT_DRTAIL_RESOURCE_NO_ERROR, e.getMessage());
        }
        resourceOptLogService.countExportTotal(info);

        info.setStatus("123");
        resourceOptLogService.countExportTotal(info);
    }

    @Test
    public void queryExportData() {
        ResourceOptLogDTO info = new ResourceOptLogDTO();
        info.setResourceNo("123");
        info.setCreateStartDate("2023-12-19 00:00:00");
        info.setCreateEndDate("2023-12-20 23:59:59");
        resourceOptLogService.queryExportData(info, 1, 10);

        List<ResourceOptLogDTO> entityDTOList = new ArrayList<>();
        entityDTOList.add(info);
        PowerMockito.when(resourceOptLogRepository.page(Mockito.any())).thenReturn(entityDTOList);
        resourceOptLogService.queryExportData(info, 1, 10);

        PowerMockito.when(resourceInfoDetailService.getLookUpVaulesList(Mockito.anyMap(), Mockito.any())).thenReturn(null);
        try {
            resourceOptLogService.countExportTotal(info);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.QUERY_EXPORT_ERROR, e.getMessage());
        }
    }

    @Test
    public void testDownloadTemplate() throws Exception {
        resourceUseInfoService.downloadTemplate(response);
        PowerMockito.when(response.getOutputStream()).thenReturn(outputStream);
        resourceUseInfoService.downloadTemplate(response);
        PowerMockito.doThrow(new IOException()).when(outputStream).close();
        resourceUseInfoService.downloadTemplate(response);
        assertNotNull(outputStream);
    }
}
