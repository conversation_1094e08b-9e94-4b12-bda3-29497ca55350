package com.zte.application.impl;
import com.zte.application.PsTaskExtendedService;
import com.zte.application.PushStdModelDataService;
import com.zte.application.sncabind.PsTaskService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PushStdModelConfirmationRepository;
import com.zte.domain.model.WarehouseEntryInfo;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.PushStdModelConfirmationDTO;
import com.zte.springbootframe.common.model.Page;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static com.zte.common.utils.NumConstant.*;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class PushStdModelConfirmationServiceImplTest {

    @Mock
    private PsTaskExtendedService psTaskExtendedService;

    @Mock
    private PushStdModelDataService pushStdModelDataService;
    @Mock
    private PushStdModelConfirmationRepository pushStdModelConfirmationRepository;
    @Mock
    private PsTaskService psTaskService;

    @InjectMocks
    private PushStdModelConfirmationServiceImpl service;

    private List<PushStdModelConfirmationDTO> confirmationDTOList;
    private PsTask psTask;

    @Before
    public void setUp() {
        confirmationDTOList = new ArrayList<>();
        psTask = new PsTask();
        psTask.setTaskNo("task1");
        psTask.setConfirmationStatus(STRING_THREE);
    }

    @Test
    public void testPageList() throws Exception {
        // Given
        PushStdModelConfirmationDTO record = new PushStdModelConfirmationDTO();
        record.setPage(1);
        record.setRows(10);

        Page<PushStdModelConfirmationDTO> result = service.pageList(record);

        assertEquals(0, result.getRows().size());
    }

    /* Started by AICoder, pid:75ac156c6cd06611483d0bc3106df81e71956642 */
    @Test
    public void testSave() throws Exception {
        PushStdModelConfirmationDTO record = new PushStdModelConfirmationDTO();

        when(pushStdModelConfirmationRepository.batchInsert(anyList())).thenReturn(1);

        int result = service.save(record);

        assertEquals(1, result);
        verify(pushStdModelConfirmationRepository).batchInsert(anyList());
    }
    /* Ended by AICoder, pid:75ac156c6cd06611483d0bc3106df81e71956642 */

    @Test
    public void testUpdate() throws Exception {
        PushStdModelConfirmationDTO record = new PushStdModelConfirmationDTO();

        when(pushStdModelConfirmationRepository.batchUpdate(anyList())).thenReturn(1);

        int result = service.update(record);

        assertEquals(1, result);
        verify(pushStdModelConfirmationRepository).batchUpdate(anyList());
    }

    @Test
    public void testSaveConfirmation() {
        PsTask record = new PsTask();
        record.setFactoryId(new BigDecimal(1));

        when(pushStdModelConfirmationRepository.batchInsert(anyList())).thenReturn(1);

        service.saveAndPushConfirmation(record);

        verify(pushStdModelConfirmationRepository).batchInsert(anyList());
    }

    /*Started by AICoder, pid:se19ed5f2fze23214b33099c310fd52603c1c690*/
    @Test
    public void testPushTaskToB2B_Success() throws Exception {
        // Given
        when(psTaskExtendedService.queryByTaskNos(anyList())).thenReturn(Collections.singletonList(new PsTaskExtendedDTO()));
        when(pushStdModelDataService.pushSchedulingInfo(any(PushStdModelConfirmationDTO.class), anyMap())).thenReturn(true);
        PushStdModelConfirmationDTO dto1 = new PushStdModelConfirmationDTO();
        dto1.setTaskNo("task1");
        dto1.setConfirmationType(NUM_TWO);
        PushStdModelConfirmationDTO dto2 = new PushStdModelConfirmationDTO();
        dto2.setTaskNo("task2");
        dto2.setConfirmationType(NUM_ONE);
        PushStdModelConfirmationDTO dto3 = new PushStdModelConfirmationDTO();
        dto3.setTaskNo("task3");
        dto3.setConfirmationType(NUM_ONE);
        PushStdModelConfirmationDTO dto4 = new PushStdModelConfirmationDTO();
        dto4.setTaskNo("task4");
        dto4.setConfirmationType(NUM_ONE);
        PushStdModelConfirmationDTO dto5 = new PushStdModelConfirmationDTO();
        dto5.setTaskNo("task5");
        dto5.setConfirmationType(NUM_TWO);

        PsTask psTask1 = new PsTask();
        psTask1.setTaskNo("task1");
        psTask1.setConfirmationStatus(STRING_THREE);
        PsTask psTask2 = new PsTask();
        psTask2.setTaskNo("task2");
        psTask2.setConfirmationStatus(STRING_THREE);
        PsTask psTask4 = new PsTask();
        psTask4.setTaskNo("task4");
        psTask4.setConfirmationStatus(STRING_ZERO);
        PsTask psTask5 = new PsTask();
        psTask5.setTaskNo("task5");
        psTask5.setConfirmationStatus(STRING_ZERO);

        // When
        service.pushTaskToB2B(Arrays.asList(dto1, dto2, dto3, dto4, dto5), Arrays.asList(psTask1, psTask2, psTask4, psTask5));

        // Then
        verify(psTaskExtendedService, times(1)).queryByTaskNos(anyList());
    }

    @Test
    public void testPushTaskToB2B_TaskNotFound() throws Exception {
        // Given
        when(psTaskExtendedService.queryByTaskNos(anyList())).thenReturn(Collections.singletonList(new PsTaskExtendedDTO()));
//        when(pushStdModelDataService.pushSchedulingInfo(any(PushStdModelConfirmationDTO.class), anyMap())).thenReturn(false);

        PsTask psTask = new PsTask();
        psTask.setTaskNo("task1");
        psTask.setConfirmationStatus(STRING_THREE);

        PushStdModelConfirmationDTO dto = new PushStdModelConfirmationDTO();
        dto.setTaskNo("task2");
        confirmationDTOList.add(dto);

        // When
        service.pushTaskToB2B(confirmationDTOList, Arrays.asList(psTask));

        // Then
        verify(psTaskExtendedService, times(1)).queryByTaskNos(anyList());
        verify(pushStdModelDataService, never()).pushSchedulingInfo(any(PushStdModelConfirmationDTO.class), anyMap());
    }

    @Test
    public void testPushTaskToB2B_ExceptionHandling() throws Exception {
        // Given
        when(psTaskExtendedService.queryByTaskNos(anyList())).thenReturn(Collections.singletonList(new PsTaskExtendedDTO()));
        doThrow(new RuntimeException("Test Exception")).when(pushStdModelDataService).pushSchedulingInfo(any(PushStdModelConfirmationDTO.class), anyMap());

        PushStdModelConfirmationDTO dto = new PushStdModelConfirmationDTO();
        dto.setTaskNo("task1");
        dto.setConfirmationType(NUM_ONE);
        dto.setPushFailCount(NUM_ZERO);
        confirmationDTOList.add(dto);

        // When
        service.pushTaskToB2B(confirmationDTOList, Collections.singletonList(psTask));

        // Then
        verify(psTaskExtendedService, times(1)).queryByTaskNos(anyList());
        verify(pushStdModelDataService, times(1)).pushSchedulingInfo(any(PushStdModelConfirmationDTO.class), anyMap());
    }
    /*Ended by AICoder, pid:se19ed5f2fze23214b33099c310fd52603c1c690*/

    /* Started by AICoder, pid:yf3aed330dca436140840a4b7088873a9768e465 */
    @Test
    public void testSavePushStdModelConfirmation_NoDataToSave() {
        WarehouseEntryInfo warehouseEntryInfo1 = new WarehouseEntryInfo();
        warehouseEntryInfo1.setTaskNo("task1");
        warehouseEntryInfo1.setItemNo("item1");
        warehouseEntryInfo1.setFactoryId(new BigDecimal(1));
        List<WarehouseEntryInfo> data = Arrays.asList(warehouseEntryInfo1);

        PushStdModelConfirmationDTO existingDTO = new PushStdModelConfirmationDTO();
        existingDTO.setTaskNo("task1");
        existingDTO.setConfirmationType(NUM_TWO);

        PushStdModelConfirmationDTO existingDTO1 = new PushStdModelConfirmationDTO();
        existingDTO1.setTaskNo("task2");
        existingDTO1.setConfirmationType(NUM_ONE);

        when(pushStdModelConfirmationRepository.getList(any(PushStdModelConfirmationDTO.class))).thenReturn(Arrays.asList(existingDTO, existingDTO1));

        int result = service.savePushStdModelConfirmation(data);

        Assert.assertEquals(0, result);
    }

    @Test
    public void testSavePushStdModelConfirmation_DataToSave() {
        WarehouseEntryInfo warehouseEntryInfo1 = new WarehouseEntryInfo();
        warehouseEntryInfo1.setTaskNo("task1");
        warehouseEntryInfo1.setItemNo("item1");
        warehouseEntryInfo1.setFactoryId(new BigDecimal(1));
        WarehouseEntryInfo warehouseEntryInfo2 = new WarehouseEntryInfo();
        warehouseEntryInfo2.setTaskNo("task2");
        warehouseEntryInfo2.setItemNo("item2");
        warehouseEntryInfo2.setFactoryId(new BigDecimal(2));
        List<WarehouseEntryInfo> data = Arrays.asList(warehouseEntryInfo1, warehouseEntryInfo2);

        when(pushStdModelConfirmationRepository.getList(any(PushStdModelConfirmationDTO.class)))
                .thenReturn(Collections.emptyList());

        when(pushStdModelConfirmationRepository.batchInsert(anyList()))
                .thenReturn(2);

        int result = service.savePushStdModelConfirmation(data);

        Assert.assertEquals(2, result);
    }
    /* Ended by AICoder, pid:yf3aed330dca436140840a4b7088873a9768e465 */

    /* Started by AICoder, pid:o1610xa9e7v6c441442408f9c0b51e56df591631 */
    @Test
    public void testPushConfirmationScheduleToB2B_NoData() {
        PushStdModelConfirmationDTO confirmationDTO = new PushStdModelConfirmationDTO();
        confirmationDTO.setPushStatus(Constant.PUSH_STATUS.NOT_PUSHED);

        when(pushStdModelConfirmationRepository.getList(any(PushStdModelConfirmationDTO.class)))
                .thenReturn(Collections.emptyList());

        int result = service.pushConfirmationScheduleToB2B();

        assertEquals(0, result);
    }

    @Test
    public void testPushConfirmationScheduleToB2B_WithData() throws Exception {
        PushStdModelConfirmationDTO confirmationDTO = new PushStdModelConfirmationDTO();
        confirmationDTO.setPushStatus(Constant.PUSH_STATUS.NOT_PUSHED);

        PushStdModelConfirmationDTO dto1 = new PushStdModelConfirmationDTO();
        dto1.setTaskNo("task1");
        dto1.setPushFailCount(1);
        dto1.setConfirmationType(NUM_THREE);

        PushStdModelConfirmationDTO dto2 = new PushStdModelConfirmationDTO();
        dto2.setTaskNo("task2");
        dto2.setPushFailCount(2);
        dto2.setConfirmationType(NUM_THREE);

        List<PushStdModelConfirmationDTO> confirmationDTOList = Arrays.asList(dto1, dto2);

        PsTaskExtendedDTO extendedDTO1 = new PsTaskExtendedDTO();
        extendedDTO1.setTaskNo("task1");
        extendedDTO1.setConfirmationStatus(STRING_THREE);

        PsTaskExtendedDTO extendedDTO2 = new PsTaskExtendedDTO();
        extendedDTO2.setTaskNo("task2");
        extendedDTO2.setConfirmationStatus(STRING_THREE);

        List<PsTaskExtendedDTO> psTaskExtendedList = Arrays.asList(extendedDTO1, extendedDTO2);

        PsTask task1 = new PsTask();
        task1.setTaskNo("task1");

        PsTask task2 = new PsTask();
        task2.setTaskNo("task2");

        List<PsTask> psTaskList = Arrays.asList(task1, task2);

        when(pushStdModelDataService.pushSchedulingInfo(any(PushStdModelConfirmationDTO.class), anyMap())).thenReturn(true);
        when(pushStdModelConfirmationRepository.getList(any(PushStdModelConfirmationDTO.class)))
                .thenReturn(confirmationDTOList);

        when(psTaskExtendedService.queryByTaskNos(anyList())).thenReturn(psTaskExtendedList);

        when(psTaskService.getPsTask(anyList())).thenReturn(psTaskList);

        int result = service.pushConfirmationScheduleToB2B();

        assertEquals(2, result); // 假设 pushConfirmationInfo 返回任务数量
    }
    /* Ended by AICoder, pid:o1610xa9e7v6c441442408f9c0b51e56df591631 */

    @Test
    public void testSaveAndPushConfirmation_WhenPushToB2BFails_ShouldLogError() {
        // 准备测试数据
        PsTask psTask = new PsTask();
        psTask.setTaskNo("TASK-001");
        psTask.setItemNo("ITEM-001");
        psTask.setFactoryId(new BigDecimal(1));

        // 模拟 batchInsert 抛出异常
        doThrow(new RuntimeException("B2B API error"))
                .when(pushStdModelConfirmationRepository)
                .batchInsert(anyList());

        // 执行测试方法（应捕获异常，不向外抛出）
        service.saveAndPushConfirmation(psTask);

        // 验证 repository 方法被调用
        verify(pushStdModelConfirmationRepository, times(1)).batchInsert(anyList());
    }
}