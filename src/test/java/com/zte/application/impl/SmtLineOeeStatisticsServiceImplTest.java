package com.zte.application.impl;

import com.zte.common.utils.Constant;
import com.zte.domain.model.SmtLineOee;
import com.zte.domain.model.SmtLineOeeRepository;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.OpenApiRemoteService;
import com.zte.interfaces.dto.SmtLineOeeStatisticsDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.MESHttpHelper;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;


@RunWith(PowerMockRunner.class)
@PrepareForTest({BasicsettingRemoteService.class, MESHttpHelper.class})
public class SmtLineOeeStatisticsServiceImplTest extends TestCase {

    @InjectMocks
    private SmtLineOeeStatisticsServiceImpl service;

    @Mock
    private SmtLineOeeRepository smtLineOeeRepository;
    @Mock
    private SysLookupTypesRepository sysLookupTypesRepository;
    @Mock
    private OpenApiRemoteService openApiRemoteService;

    @Test
    public void testCountExportTotal() {
        Integer count = 0;
        Assert.assertEquals(count, service.countExportTotal(new SmtLineOee()));
    }

    @Test
    public void testQueryExportData() {
        SmtLineOee param = new SmtLineOee();
        Assert.assertEquals(new ArrayList<>(), service.queryExportData(param, 0, 0));
        SmtLineOeeStatisticsDTO smtLineOeeStatisticsDTO = new SmtLineOeeStatisticsDTO();
        List<SmtLineOeeStatisticsDTO> list = new ArrayList<>();
        list.add(smtLineOeeStatisticsDTO);
        PowerMockito.when(smtLineOeeRepository.pageListAndStatistics(any())).thenReturn(list);
        smtLineOeeStatisticsDTO.setTimeMobility(new BigDecimal(1));
        smtLineOeeStatisticsDTO.setProductionEfficiency(new BigDecimal(1));
        smtLineOeeStatisticsDTO.setOee(new BigDecimal(1));
        service.queryExportData(param, 0, 0);
    }

    @Test
    public void testPageListAndStatistics() {
        SmtLineOee param = new SmtLineOee();
        Page<SmtLineOeeStatisticsDTO> page = new Page<>(param.getPage(), param.getRows());
        page.setParams(param);
        page.setRows(null);
        Assert.assertEquals(page, service.pageListAndStatistics(param));
        List<SmtLineOeeStatisticsDTO> list = new ArrayList<>();
        SmtLineOeeStatisticsDTO smtLineOeeStatisticsDTO = new SmtLineOeeStatisticsDTO();
        list.add(smtLineOeeStatisticsDTO);
        PowerMockito.when(smtLineOeeRepository.pageListAndStatistics(any())).thenReturn(list);
        smtLineOeeStatisticsDTO.setTimeMobility(new BigDecimal(1));
        smtLineOeeStatisticsDTO.setProductionEfficiency(new BigDecimal(1));
        smtLineOeeStatisticsDTO.setOee(new BigDecimal(1));
        service.pageListAndStatistics(param);
        List<SysLookupTypesDTO> valuesList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        valuesList.add(sysLookupTypesDTO);
        PowerMockito.when(sysLookupTypesRepository.getList(any())).thenReturn(valuesList);
        sysLookupTypesDTO.setLookupMeaning("55");
        sysLookupTypesDTO.setDescriptionChinV("heyuan");
        SysLookupTypesDTO sysLookupTypesDTO1 = new SysLookupTypesDTO();
        valuesList.add(sysLookupTypesDTO1);
        sysLookupTypesDTO1.setLookupMeaning("52");
        smtLineOeeStatisticsDTO.setStatisticsParam("55");

        service.pageListAndStatistics(param);
        param.setCurrentEndDate(new Date());
        param.setCurrentStartDate(new Date(Constant.DAY_TIME * 365));
        service.pageListAndStatistics(param);
    }

    @Test
    public void testGetBasicInfoForOee() {
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SmtLineOee param = new SmtLineOee();
        Assert.assertEquals(new ArrayList<>(), service.getBasicInfoForOee(param));
        List<String> factoryList = new ArrayList<>();
        factoryList.add("55");
        param.setFactoryIdList(factoryList);
        Assert.assertEquals(new ArrayList<>(), service.getBasicInfoForOee(param));
        Page<SmtLineOee> page = new Page<>(param.getPage(), param.getRows());
        PowerMockito.when(openApiRemoteService.getBasicInfoForOee(any(), any())).thenReturn(page);
        service.getBasicInfoForOee(param);
        List<SmtLineOee> list = new ArrayList<>();
        list.add(param);
        page.setRows(list);
        service.getBasicInfoForOee(param);

    }
}