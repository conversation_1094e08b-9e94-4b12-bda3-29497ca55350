/*Started by AICoder, pid:sfe1e63905c6765148ee0ab5411ee96e1a405a95*/
package com.zte.application.impl;

import com.zte.application.TradeDataLogService;
import com.zte.application.cbom.FixBomCommonService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PushStdModelSnDataRepository;
import com.zte.domain.model.PushStdModelSnDataSubRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.PushStdModelSnDataDTO;
import com.zte.interfaces.dto.PushStdModelSnDataExtDTO;
import com.zte.interfaces.dto.PushStdModelSnDataHandleDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import com.zte.interfaces.dto.mbom.FixBomDetailDTO;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.util.BaseTestCase;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.LinkedList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@PrepareForTest({DatawbRemoteService.class})
public class BasePushStdModelSnDataHandleService_handlePushStdModelSnData_0_Test extends BaseTestCase {

    @Mock
    private PushStdModelSnDataRepository pushStdModelSnDataRepository;
    @Mock
    private FixBomCommonService fixBomCommonService;
    @Mock
    protected IdGenerator idGenerator;
    @Mock
    TradeDataLogService tradeDataLogService;
    @Mock
    private PushStdModelSnDataSubRepository pushStdModelSnDataSubRepository;
    @Mock
    private
    BarcodeCenterRemoteService barcodeCenterRemoteService;

    @InjectMocks
    private BasePushStdModelSnDataHandleService basePushStdModelSnDataHandleService = new BasePushStdModelSnDataHandleServiceImpl();

    @Before
    public void setUp() {
        // Reset mocks before each test
        Mockito.reset(pushStdModelSnDataRepository);
        ReflectionTestUtils.setField(basePushStdModelSnDataHandleService, "pushFailThreshold", 3);
    }

    @Test
    public void testHandlePushStdModelSnData_WhenPushStdModelSnDataExtIsNull() {
        PushStdModelSnDataHandleDTO pushStdModelSnDataHandle = new PushStdModelSnDataHandleDTO();
        pushStdModelSnDataHandle.setId("1L");

        when(pushStdModelSnDataRepository.selectExtByPrimaryKey(eq("1L"))).thenReturn(null);

        boolean result = basePushStdModelSnDataHandleService.handlePushStdModelSnData(pushStdModelSnDataHandle);

        assertFalse(result);
        verify(pushStdModelSnDataRepository, never()).update(any(PushStdModelSnDataDTO.class));
    }

    @Test
    public void testHandlePushStdModelSnData_WhenPushStdModelSnDataExtPushed() {
        PushStdModelSnDataHandleDTO pushStdModelSnDataHandle = new PushStdModelSnDataHandleDTO();
        pushStdModelSnDataHandle.setId("1L");

        when(pushStdModelSnDataRepository.selectExtByPrimaryKey(eq("1L"))).thenReturn(new PushStdModelSnDataExtDTO() {{ setPushStatus(1); }});

        boolean result = basePushStdModelSnDataHandleService.handlePushStdModelSnData(pushStdModelSnDataHandle);

        assertFalse(result);
        verify(pushStdModelSnDataRepository, never()).update(any(PushStdModelSnDataDTO.class));
    }

    @Test
    public void testHandlePushStdModelSnData_WhenDoHandleReturnsTrue() throws Exception {
        PushStdModelSnDataHandleDTO pushStdModelSnDataHandle = new PushStdModelSnDataHandleDTO();
        pushStdModelSnDataHandle.setId("2L");
        pushStdModelSnDataHandle.setStockName("testStock");
        pushStdModelSnDataHandle.setCurrProcess("testProcess1");

        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        pushStdModelSnDataExt.setId("2L");
        pushStdModelSnDataExt.setPushStatus(0);
        pushStdModelSnDataExt.setPushFailCount(0);

        when(pushStdModelSnDataRepository.selectExtByPrimaryKey(eq("2L"))).thenReturn(pushStdModelSnDataExt);
        PowerMockito.field(BasePushStdModelSnDataService.class,"productSnReportCurrProcess").set(basePushStdModelSnDataHandleService,"30");
        PowerMockito.mockStatic(DatawbRemoteService.class);
        boolean result = basePushStdModelSnDataHandleService.handlePushStdModelSnData(pushStdModelSnDataHandle);
        assertTrue(result);
        verify(pushStdModelSnDataRepository).update(any(PushStdModelSnDataDTO.class));

        pushStdModelSnDataHandle.setCurrProcess("30");
        try {
            basePushStdModelSnDataHandleService.handlePushStdModelSnData(pushStdModelSnDataHandle);
        } catch (Exception e) {
        }

        List<WipExtendIdentificationDTO> extendIdentificationDTOS = new LinkedList<>();
        List<FixBomDetailDTO> list =new LinkedList<>();
        for (int i = 0; i < 20; i++) {
            WipExtendIdentificationDTO b1 = new WipExtendIdentificationDTO();
            FixBomDetailDTO a1 = new FixBomDetailDTO();
            String s = String.valueOf(i);
            if(i%2==0){
                a1.setBoxBomRequired("Y");
                a1.setZteCode(s);
                a1.setBoxPriority(i);
            }
            list.add(a1);
            b1.setItemNo(s);
            extendIdentificationDTOS.add(b1);
        }
        PowerMockito.when(fixBomCommonService.queryFixBomDetailByHeadId(Mockito.any()))
                .thenReturn(list);
        basePushStdModelSnDataHandleService.handlePushStdModelSnData(pushStdModelSnDataHandle);

        pushStdModelSnDataHandle.setWipExtendIdentifications(extendIdentificationDTOS);
        basePushStdModelSnDataHandleService.handlePushStdModelSnData(pushStdModelSnDataHandle);

        PowerMockito.when(pushStdModelSnDataSubRepository.selectBySnPriority(Mockito.any(), Mockito.anyInt(), Mockito.any()))
                .thenReturn(1);
        basePushStdModelSnDataHandleService.handlePushStdModelSnData(pushStdModelSnDataHandle);
    }

    @Test
    public void testHandlePushStdModelSnData_WhenDoHandleReturnsFalse() {
        PushStdModelSnDataHandleDTO pushStdModelSnDataHandle = new PushStdModelSnDataHandleDTO();
        pushStdModelSnDataHandle.setId("1L");
        pushStdModelSnDataHandle.setStockName("testStock");
        pushStdModelSnDataHandle.setCurrProcess("testProcess0");

        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        pushStdModelSnDataExt.setId("1L");
        pushStdModelSnDataExt.setPushStatus(0);
        pushStdModelSnDataExt.setPushFailCount(0);

        when(pushStdModelSnDataRepository.selectExtByPrimaryKey(eq("1L"))).thenReturn(pushStdModelSnDataExt);

        boolean result = basePushStdModelSnDataHandleService.handlePushStdModelSnData(pushStdModelSnDataHandle);

        assertFalse(result);
        verify(pushStdModelSnDataRepository).update(any(PushStdModelSnDataDTO.class));
    }

    @Test
    public void testHandlePushStdModelSnData_WhenExceptionOccurs() {
        PushStdModelSnDataHandleDTO pushStdModelSnDataHandle = new PushStdModelSnDataHandleDTO();
        pushStdModelSnDataHandle.setId("1L");
        pushStdModelSnDataHandle.setStockName("testStock");
        pushStdModelSnDataHandle.setCurrProcess("testProcess");

        PushStdModelSnDataExtDTO pushStdModelSnDataExt = new PushStdModelSnDataExtDTO();
        pushStdModelSnDataExt.setId("1L");
        pushStdModelSnDataExt.setPushFailCount(0);
        pushStdModelSnDataExt.setPushStatus(0);
        pushStdModelSnDataExt.setTaskFixBomId("test");

        when(pushStdModelSnDataRepository.selectExtByPrimaryKey(eq("1L"))).thenReturn(pushStdModelSnDataExt);
        doThrow(new RuntimeException("Test Exception")).when(fixBomCommonService).queryTreeNodeByFixBomId(anyString());

        boolean result = basePushStdModelSnDataHandleService.handlePushStdModelSnData(pushStdModelSnDataHandle);

        assertFalse(result);
        verify(pushStdModelSnDataRepository).update(any(PushStdModelSnDataDTO.class));
    }

    @Test
    public void testWrapFailCountAndPushStatus_WhenFailCountBelowThreshold() throws Exception {
        PushStdModelSnDataDTO pushStdModelSnData = new PushStdModelSnDataDTO();
        pushStdModelSnData.setPushFailCount(2);

        Whitebox.invokeMethod(basePushStdModelSnDataHandleService, "wrapFailCountAndPushStatus", pushStdModelSnData);

        assertEquals(3, pushStdModelSnData.getPushFailCount());
        assertEquals(Constant.PUSH_STATUS.DATA_CHECK_OR_PUSH_FAIL, pushStdModelSnData.getPushStatus());
    }

    @Test
    public void testWrapFailCountAndPushStatus_WhenFailCountAtThreshold() throws Exception {
        PushStdModelSnDataDTO pushStdModelSnData = new PushStdModelSnDataDTO();
        pushStdModelSnData.setPushFailCount(3);

        Whitebox.invokeMethod(basePushStdModelSnDataHandleService, "wrapFailCountAndPushStatus", pushStdModelSnData);

        assertEquals(4, pushStdModelSnData.getPushFailCount());
        assertEquals(Constant.PUSH_STATUS.DATA_CHECK_OR_PUSH_FAIL, pushStdModelSnData.getPushStatus());
    }
}

class BasePushStdModelSnDataHandleServiceImpl extends BasePushStdModelSnDataHandleService<Object> {
    @Override
    public boolean match(String currProcess) {
        return false;
    }

    @Override
    public boolean handlePushStdModelSnDatas(List<PushStdModelSnDataHandleDTO> pushStdModelSnDataHandles) {
        return false;
    }

    @Override
    public <R> boolean validatePushMessageData(String currProcess, R pushMessageData) {
        return pushMessageData != null;
    }

    @Override
    protected Object wrapPushMessageData(PushStdModelSnDataExtDTO pushStdModelSnDataExt, List<WipExtendIdentificationDTO> wipExtendIdentifications, List<FixBomDetailDTO> fixBomDetails) {
        return "1L".equals(pushStdModelSnDataExt.getId()) ? null : "test123";
    }

    @Override
    protected String getPushStdModelSnDataMessageType() {
        return null;
    }
}
/*Ended by AICoder, pid:sfe1e63905c6765148ee0ab5411ee96e1a405a95*/