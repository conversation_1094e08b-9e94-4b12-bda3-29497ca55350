package com.zte.application.impl;

import com.zte.application.VMesBoxNoCopyService;
import com.zte.domain.model.VMesBoxNoCopyRepository;
import com.zte.interfaces.dto.VMesBoxNoCopyDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
@RunWith(PowerMockRunner.class)
public class VMesBoxNoCopyServiceImplTest {
    @InjectMocks
    private VMesBoxNoCopyServiceImpl vMesBoxNoCopyService;
    @Mock
    private VMesBoxNoCopyRepository vMesBoxNoCopyRepository;
    @Test
    public void insertCopyInfoBatch() {
        List<VMesBoxNoCopyDTO> list = new ArrayList<>();
        VMesBoxNoCopyDTO vMesBoxNoCopyDTO = new VMesBoxNoCopyDTO();
        vMesBoxNoCopyDTO.setExternalOrderkey2("bill1");
        vMesBoxNoCopyDTO.setFormId("box1");
        List<VMesBoxNoCopyDTO> listParam = new ArrayList<>();
        listParam.add(vMesBoxNoCopyDTO);
        PowerMockito.when(vMesBoxNoCopyRepository.getCopyInfoByBillNo(Mockito.any())).thenReturn(list);
        PowerMockito.when(vMesBoxNoCopyRepository.insertCopyInfoBatch(Mockito.any())).thenReturn(1);
        Assert.assertTrue(vMesBoxNoCopyService.insertCopyInfoBatch(listParam) >= 0);
        list.add(vMesBoxNoCopyDTO);
        Assert.assertTrue(vMesBoxNoCopyService.insertCopyInfoBatch(listParam) >= 0);
        VMesBoxNoCopyDTO vMesBoxNoCopyDTO1= new VMesBoxNoCopyDTO();
        vMesBoxNoCopyDTO1.setExternalOrderkey2("bill2");
        vMesBoxNoCopyDTO1.setFormId("box2");
        listParam.add(vMesBoxNoCopyDTO1);
        Assert.assertTrue(vMesBoxNoCopyService.insertCopyInfoBatch(listParam) >= 0);
    }

    @Test
    public void deleteCopyInfoBatch() {
        VMesBoxNoCopyDTO vMesBoxNoCopyDTO = new VMesBoxNoCopyDTO();
        vMesBoxNoCopyDTO.setExternalOrderkey2("bill1");
        vMesBoxNoCopyDTO.setFormId("box1");
        List<VMesBoxNoCopyDTO> listParam = new ArrayList<>();
        listParam.add(vMesBoxNoCopyDTO);
        PowerMockito.when(vMesBoxNoCopyRepository.deleteCopyInfoBatch(Mockito.any())).thenReturn(1);
        Assert.assertTrue(vMesBoxNoCopyService.deleteCopyInfoBatch(listParam) >= 0);
    }

    @Test
    public void getCopyInfoList() {
        VMesBoxNoCopyDTO vMesBoxNoCopyDTO = new VMesBoxNoCopyDTO();
        vMesBoxNoCopyDTO.setExternalOrderkey2("bill1");
        vMesBoxNoCopyDTO.setFormId("box1");
        List<VMesBoxNoCopyDTO> listParam = new ArrayList<>();
        listParam.add(vMesBoxNoCopyDTO);
        PowerMockito.when(vMesBoxNoCopyRepository.getCopyInfoList(Mockito.any())).thenReturn(listParam);
        Assert.assertTrue(vMesBoxNoCopyService.getCopyInfoList(vMesBoxNoCopyDTO).size() >= 0);
    }
}