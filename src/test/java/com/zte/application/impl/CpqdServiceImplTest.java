/*Started by AICoder, pid:c85f068b446649f1404b0a7f01a26c0272a11b0a*/
package com.zte.application.impl;

import com.zte.infrastructure.remote.CpqdRemoteService;
import com.zte.interfaces.dto.mbom.CpqdGbomDTO;
import com.zte.interfaces.dto.mbom.CpqdQueryDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * CpqdServiceImpl 单元测试
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2025/4/29
 */
@RunWith(MockitoJUnitRunner.class)
public class CpqdServiceImplTest {

    @Mock
    private CpqdRemoteService cpqdRemoteService;

    @InjectMocks
    private CpqdServiceImpl cpqdService;

    private CpqdQueryDTO cpqdQueryDTO;
    private List<CpqdGbomDTO> mockGbomList;

    @Before
    public void setUp() {
        // 初始化测试数据
        cpqdQueryDTO = new CpqdQueryDTO();
        cpqdQueryDTO.setInstanceNo(Arrays.asList("ITEM001", "ITEM002", "ITEM003"));

        mockGbomList = new ArrayList<>();
        
        // 创建测试用的CpqdGbomDTO对象
        CpqdGbomDTO gbom1 = new CpqdGbomDTO();
        gbom1.setInstanceNo("ITEM001");
        gbom1.setMaterialSign("General Server");
        
        CpqdGbomDTO gbom2 = new CpqdGbomDTO();
        gbom2.setInstanceNo("ITEM002");
        gbom2.setMaterialSign("other server");
        
        CpqdGbomDTO gbom3 = new CpqdGbomDTO();
        gbom3.setInstanceNo("ITEM003");
        gbom3.setMaterialSign(null);
        
        mockGbomList.add(gbom1);
        mockGbomList.add(gbom2);
        mockGbomList.add(gbom3);
    }

    @Test
    public void testQueryGbomList_Success() throws Exception {
        // 准备测试数据
        when(cpqdRemoteService.queryGbomList(any(CpqdQueryDTO.class))).thenReturn(mockGbomList);

        // 执行测试
        List<CpqdGbomDTO> result = cpqdService.queryGbomList(cpqdQueryDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size());
        
        // 验证第一个对象 - 单节点机型
        assertEquals("ITEM001", result.get(0).getInstanceNo());
        assertEquals("General Server", result.get(0).getMaterialSign());
        assertEquals("单节点机型", result.get(0).getMaterialSignName());
        
        // 验证第二个对象 - 复合机型
        assertEquals("ITEM002", result.get(1).getInstanceNo());
        assertEquals("other server", result.get(1).getMaterialSign());
        assertEquals("复合机型", result.get(1).getMaterialSignName());
        
        // 验证第三个对象 - materialSign为null
        assertEquals("ITEM003", result.get(2).getInstanceNo());
        assertEquals(null, result.get(2).getMaterialSign());
        assertEquals("", result.get(2).getMaterialSignName());
    }

    @Test
    public void testQueryGbomList_EmptyList() throws Exception {
        // 准备测试数据 - 返回空列表
        when(cpqdRemoteService.queryGbomList(any(CpqdQueryDTO.class))).thenReturn(new ArrayList<>());

        // 执行测试
        List<CpqdGbomDTO> result = cpqdService.queryGbomList(cpqdQueryDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryGbomList_NullList() throws Exception {
        // 准备测试数据 - 返回null
        when(cpqdRemoteService.queryGbomList(any(CpqdQueryDTO.class))).thenReturn(null);

        // 执行测试
        List<CpqdGbomDTO> result = cpqdService.queryGbomList(cpqdQueryDTO);

        // 验证结果
        assertEquals(null, result);
    }

    @Test
    public void testGetMaterialSignNameByMaterialSign_GeneralServer() throws Exception {
        // 测试单节点机型
        String result = Whitebox.invokeMethod(cpqdService, "getMaterialSignNameByMaterialSign", "General Server");
        assertEquals("单节点机型", result);
    }

    @Test
    public void testGetMaterialSignNameByMaterialSign_GeneralServerUpperCase() throws Exception {
        // 测试单节点机型 - 大写
        String result = Whitebox.invokeMethod(cpqdService, "getMaterialSignNameByMaterialSign", "General Server");
        assertEquals("单节点机型", result);
    }

    @Test
    public void testGetMaterialSignNameByMaterialSign_OtherServer() throws Exception {
        // 测试复合机型
        String result = Whitebox.invokeMethod(cpqdService, "getMaterialSignNameByMaterialSign", "other server");
        assertEquals("复合机型", result);
    }

    @Test
    public void testGetMaterialSignNameByMaterialSign_Null() throws Exception {
        // 测试null值
        String result = Whitebox.invokeMethod(cpqdService, "getMaterialSignNameByMaterialSign", (String) null);
        assertEquals("", result);
    }

    @Test
    public void testGetMaterialSignNameByMaterialSign_Empty() throws Exception {
        // 测试空字符串
        String result = Whitebox.invokeMethod(cpqdService, "getMaterialSignNameByMaterialSign", "");
        assertEquals("", result);
    }

    @Test
    public void testGetMaterialSignNameByMaterialSign_Blank() throws Exception {
        // 测试空白字符串
        String result = Whitebox.invokeMethod(cpqdService, "getMaterialSignNameByMaterialSign", "   ");
        assertEquals("", result);
    }

    @Test
    public void testQueryGbomList_WithEmptyMaterialSign() throws Exception {
        // 准备测试数据 - 包含空materialSign的对象
        List<CpqdGbomDTO> testList = new ArrayList<>();
        CpqdGbomDTO gbom = new CpqdGbomDTO();
        gbom.setInstanceNo("ITEM004");
        gbom.setMaterialSign("");
        testList.add(gbom);
        
        when(cpqdRemoteService.queryGbomList(any(CpqdQueryDTO.class))).thenReturn(testList);

        // 执行测试
        List<CpqdGbomDTO> result = cpqdService.queryGbomList(cpqdQueryDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("ITEM004", result.get(0).getInstanceNo());
        assertEquals("", result.get(0).getMaterialSign());
        assertEquals("", result.get(0).getMaterialSignName());
    }

    @Test
    public void testQueryGbomList_WithBlankMaterialSign() throws Exception {
        // 准备测试数据 - 包含空白materialSign的对象
        List<CpqdGbomDTO> testList = new ArrayList<>();
        CpqdGbomDTO gbom = new CpqdGbomDTO();
        gbom.setInstanceNo("ITEM005");
        gbom.setMaterialSign("   ");
        testList.add(gbom);
        
        when(cpqdRemoteService.queryGbomList(any(CpqdQueryDTO.class))).thenReturn(testList);

        // 执行测试
        List<CpqdGbomDTO> result = cpqdService.queryGbomList(cpqdQueryDTO);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("ITEM005", result.get(0).getInstanceNo());
        assertEquals("   ", result.get(0).getMaterialSign());
        assertEquals("", result.get(0).getMaterialSignName());
    }
}
/*Ended by AICoder, pid:c85f068b446649f1404b0a7f01a26c0272a11b0a*/ 