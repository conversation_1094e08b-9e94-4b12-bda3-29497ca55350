package com.zte.application.impl;

import com.zte.application.sncabind.CaRecordService;
import com.zte.application.sncabind.PsTaskService;
import com.zte.application.sncabind.SnCaBindingActualService;
import com.zte.application.sncabind.impl.SnCaBindingServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.domain.model.sncabind.SnCaBindingRepository;
import com.zte.infrastructure.remote.OpenApiRemoteService;
import com.zte.interfaces.dto.CACertificateImportResultDTO;
import com.zte.interfaces.sncabind.dto.CaOfCompleteBarcodeDTO;
import com.zte.interfaces.sncabind.dto.SnCaBindingDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.powermock.api.mockito.PowerMockito.doReturn;
import static org.powermock.api.mockito.PowerMockito.doThrow;

/**
 * ClassName: SnCaBindingServiceImplTest
 * Description:
 *
 * <AUTHOR>
 * @date 2023/6/20 下午3:26
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class, SpringContextUtil.class})
public class SnCaBindingServiceImplTest extends BaseTestCase {
    @InjectMocks
    private SnCaBindingServiceImpl sevice;
    
    @Mock
    private SnCaBindingRepository snCaBindingRepository;

    @Mock
    private OpenApiRemoteService openApiRemoteService;

    @Mock
    private LocaleMessageSourceBean lmb;

    @Mock
    private CaRecordService caRecordService;

    @Mock
    private SnCaBindingActualService snCaBindingActualService;

    @Mock
    private PsTaskService psTaskService;
    
    
    @Test
    public void importCACertificateStandardBarcodeTest() throws Exception {
        List<String> snList = new ArrayList<>();
        try {
            sevice.importCACertificateStandardBarcode(snList);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SN_LIST_OF_CA_CERTIFICATE_NULL.equals(e.getExMsgId()));
        }
        for (int i = 0; i < 300; i++) {
            snList.add("test");
        }
        try {
            sevice.importCACertificateStandardBarcode(snList);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SN_LIST_OF_CA_CERTIFICATE_MORE_200.equals(e.getExMsgId()));
        }
        snList = new ArrayList<>();
        snList.add("sn1");
        snList.add("sn2");
        snList.add("sn3");
        snList.add("sn4");
        snList.add("sn5");
        List<String> snCaBindingList = new ArrayList<>();
        snCaBindingList.add("sn1");
        PowerMockito.when(snCaBindingRepository.listSnBySns(Mockito.anyList())).thenReturn(snCaBindingList);
        PowerMockito.when(openApiRemoteService.checkCACertificateStandardBarcode(Mockito.anyList())).thenReturn(new ArrayList<>());

        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("test");
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), any(String[].class))).thenReturn("snCheckError");
        sevice.importCACertificateStandardBarcode(snList);



        List<CACertificateImportResultDTO> checkList = new ArrayList<>();
        CACertificateImportResultDTO openapiDTO3 = new CACertificateImportResultDTO();
        checkList.add(openapiDTO3);
        openapiDTO3.setSn("sn4");
        openapiDTO3.setErrorMsg("");
        openapiDTO3.setTaskNo("taskNo4");
        openapiDTO3.setItemNo("itemNo4");
        openapiDTO3.setItemName("itemName4");
        CACertificateImportResultDTO openapiDTO4 = new CACertificateImportResultDTO();
        checkList.add(openapiDTO4);
        openapiDTO4.setSn("sn5");
        openapiDTO4.setErrorMsg("");
        openapiDTO4.setTaskNo("taskNo5");
        openapiDTO4.setItemNo("itemNo5");
        openapiDTO4.setItemName("itemName5");
        PowerMockito.when(openApiRemoteService.checkCACertificateStandardBarcode(Mockito.anyList())).thenReturn(checkList);
        
        sevice.importCACertificateStandardBarcode(snList);


        CACertificateImportResultDTO openapiDTO1 = new CACertificateImportResultDTO();
        checkList.add(openapiDTO1);
        openapiDTO1.setSn("sn2");
        openapiDTO1.setErrorMsg("不存在");
        CACertificateImportResultDTO openapiDTO2 = new CACertificateImportResultDTO();
        checkList.add(openapiDTO2);
        openapiDTO2.setSn("sn3");
        openapiDTO2.setErrorMsg("非标模");
        PowerMockito.when(openApiRemoteService.checkCACertificateStandardBarcode(Mockito.anyList())).thenReturn(checkList);
        sevice.importCACertificateStandardBarcode(snList);
    }

    @Test
    public void queryCACertificateByStandardBarcodes() {
        try {
            sevice.queryCACertificateByStandardBarcodes(null);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.PARAM_IS_NULL.equals(e.getExMsgId()));
        }
        CaOfCompleteBarcodeDTO caOfCompleteBarcodeDTO = new CaOfCompleteBarcodeDTO();
        try {
            sevice.queryCACertificateByStandardBarcodes(caOfCompleteBarcodeDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.PAGE_PARAMS_OF_CA_QUERY_NULL.equals(e.getExMsgId()));
        }
        caOfCompleteBarcodeDTO.setPage(1);
        try {
            sevice.queryCACertificateByStandardBarcodes(caOfCompleteBarcodeDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.PAGE_PARAMS_OF_CA_QUERY_NULL.equals(e.getExMsgId()));
        }
        caOfCompleteBarcodeDTO.setRows(10);
        try {
            sevice.queryCACertificateByStandardBarcodes(caOfCompleteBarcodeDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SN_LIST_OF_CA_QUERY_NULL.equals(e.getExMsgId()));
        }
        List<String> snList = new ArrayList<>();
        snList.add("test");
        caOfCompleteBarcodeDTO.setSnList(snList);
        PowerMockito.when(snCaBindingRepository.countListInfoBySns(Mockito.anyList())).thenReturn(1);
        sevice.queryCACertificateByStandardBarcodes(caOfCompleteBarcodeDTO);
    }

    @Test
    public void generateSnByPsTaskTest() throws Exception {
        int limitNum = 1;
        SnCaBindingDTO entity = new SnCaBindingDTO();
        entity.setCreateBy("sas");
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(anyString())).thenReturn(lmb);
        List<PsTask> psTaskList = new ArrayList<>();

        PowerMockito.when(psTaskService.getPsTaskList(Mockito.anyMap(), anyString(),
                anyString(),Mockito.anyLong(), Mockito.anyLong())).thenReturn(psTaskList);
        sevice.generateSnByPsTask(limitNum, entity);
        psTaskList.add(new PsTask());
        doThrow(new Exception("213123")).when(snCaBindingActualService).insertFromOne(any(), anyString());
        Assert.assertNotNull(sevice.generateSnByPsTask(limitNum, entity));
    }

    @Test
    public void generateSnByPsTaskManualTest() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        SnCaBindingDTO entity = new SnCaBindingDTO();
        entity.setCreateBy("sas");
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(anyString())).thenReturn(lmb);
        List<PsTask> psTaskList = new ArrayList<>();

        PowerMockito.when(psTaskService.getPsTaskList(Mockito.anyMap(), anyString(),
                anyString(),Mockito.anyLong(), Mockito.anyLong())).thenReturn(psTaskList);
        sevice.generateSnByPsTaskManual(entity);
        psTaskList.add(new PsTask());
        doThrow(new Exception("213123")).when(snCaBindingActualService).insertFromOne(any(), anyString());

        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("test");
        Assert.assertNotNull(sevice.generateSnByPsTaskManual(entity));

    }

    @Test
    public void snCaBindTest() throws Exception {
        int limitNum = 1;

        PowerMockito.mockStatic(CommonUtils.class);
        SnCaBindingDTO entity = new SnCaBindingDTO();
        entity.setCreateBy("sas");
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(anyString())).thenReturn(lmb);
        List<SnCaBindingDTO> list = new ArrayList<>();
        PowerMockito.when(snCaBindingRepository.getSnCaBindingList(Mockito.anyMap())).thenReturn(list);
        sevice.snCaBind(limitNum,entity);
        list.add(new SnCaBindingDTO());
        doThrow(new Exception("213123")).when(snCaBindingActualService).bindOneSn(any(), anyString());

        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("test");
        sevice.snCaBind(limitNum,entity);

        doReturn("ERROR").when(snCaBindingActualService).bindOneSn(any(), anyString());
        try {
            sevice.snCaBind(limitNum,entity);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.CA_API_CALL_ERROR.equals(e.getExMsgId()));
        }

        doReturn(BusinessConstant.EMPTY_STRING).when(snCaBindingActualService).bindOneSn(any(), anyString());
        sevice.snCaBind(limitNum,entity);
    }

    @Test
    public void snCaBindManualTest() throws Exception {

        PowerMockito.mockStatic(CommonUtils.class);
        SnCaBindingDTO entity = new SnCaBindingDTO();
        entity.setSn("sdasd");
        entity.setCreateBy("sas");
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(anyString())).thenReturn(lmb);
        List<SnCaBindingDTO> list = new ArrayList<>();
        PowerMockito.when(snCaBindingRepository.getSnCaBindingList(Mockito.anyMap())).thenReturn(list);
        sevice.snCaBindManual(entity);
        list.add(new SnCaBindingDTO());
        doThrow(new Exception("213123")).when(snCaBindingActualService).bindOneSn(any(), anyString());

        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("test");
        sevice.snCaBindManual(entity);

        doReturn(BusinessConstant.EMPTY_STRING).when(snCaBindingActualService).bindOneSn(any(), anyString());
        sevice.snCaBindManual(entity);

        doReturn("ERROR").when(snCaBindingActualService).bindOneSn(any(), anyString());
        try {
            sevice.snCaBindManual(entity);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.CA_API_CALL_ERROR.equals(e.getExMsgId()));
        }
    }
}
