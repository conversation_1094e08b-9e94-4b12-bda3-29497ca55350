/*Started by AICoder, pid:of2eck7da5k3be91479f0a99619e074bf6618a4c*/
package com.zte.application.impl;

import com.zte.application.IMESLogService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.AoiFile;
import com.zte.domain.model.AoiFileRepository;
import com.zte.interfaces.dto.LocRangeDTO;
import com.zte.interfaces.dto.SpcDTO;
import com.zte.km.udm.common.util.MD5Util;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.FileUtils;
import com.zte.util.BaseTestCase;
import org.apache.commons.codec.digest.DigestUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.ArrayList;
import java.util.List;


/* Started by AICoder, pid:98cd031f33811a5144360b9fe027a40b27874224 */
@RunWith(PowerMockRunner.class)
public class AoiFileServiceImplTest extends BaseTestCase {

    @Mock
    private CloudDiskHelper cloudDiskHelper;
    @Mock
    private AoiFileRepository repository;
    @Mock
    private IMESLogService imesLogService;
    @Mock
    private MultipartFile file;
    @Mock
    private File file1;
    @InjectMocks
    private AoiFileServiceImpl aoiFileService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    @PrepareForTest({DigestUtils.class})
    public void uploadAoiFile() throws Exception {
        PowerMockito.mockStatic(DigestUtils.class);
        try {
            aoiFileService.uploadAoiFile("", "type", "empNo", file);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOM_CODE_CAN_NOT_BE_EMPTY, e.getMessage());
        }
        try {
            aoiFileService.uploadAoiFile("bomNo", "", "empNo", file);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMER_TYPE_NULL, e.getMessage());
        }
        PowerMockito.when(file.getOriginalFilename()).thenReturn("test.text");
        try {
            aoiFileService.uploadAoiFile("bomNo", "type", "empNo", file);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FILE_FORMAT, e.getMessage());
        }
        try {
            aoiFileService.uploadAoiFile("bomNo", "SPC-A", "empNo", file);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FILE_FORMAT, e.getMessage());
        }
        PowerMockito.when(file.getOriginalFilename()).thenReturn("test.csv");
        try {
            aoiFileService.uploadAoiFile("bomNo", "SPC-A", "empNo", file);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BOM_CODE_CAN_NOT_BE_EMPTY, e.getMessage());
        }
        PowerMockito.when(MD5Util.getHexMd5ByBytes(Mockito.any())).thenReturn("123321");
        aoiFileService.uploadAoiFile("bomNo", "SPC-A", "empNo", file);
        AoiFile aoiFile = new AoiFile();
        aoiFile.setMd5Code("123321");
        PowerMockito.when(repository.getAoiFileInfoByBomAndType(Mockito.anyString(), Mockito.anyString())).thenReturn(aoiFile);
        aoiFileService.uploadAoiFile("bomNo", "SPC-A", "empNo", file);
        aoiFile.setMd5Code("123322");
        PowerMockito.when(repository.getAoiFileInfoByBomAndType(Mockito.anyString(), Mockito.anyString())).thenReturn(aoiFile);
        PowerMockito.when(cloudDiskHelper.deleteFile(Mockito.anyString(), Mockito.anyString())).thenReturn(true);
        aoiFileService.uploadAoiFile("bomNo", "SPC-A", "empNo", file);
        PowerMockito.when(cloudDiskHelper.deleteFile(Mockito.anyString(), Mockito.anyString())).thenThrow(new MesBusinessException("0005", "FAILED"));
        aoiFileService.uploadAoiFile("bomNo", "SPC-A", "empNo", file);
    }

    @Test
    @PrepareForTest({FileUtils.class})
    public void getTagCoordinates() throws Exception {
        PowerMockito.mockStatic(FileUtils.class);
        List<String> refNameList = new ArrayList<>();
        Assert.assertNull(aoiFileService.getTagCoordinates("123", "123", refNameList).getBoardSizeX());
        refNameList.add("123");
        Assert.assertNull(aoiFileService.getTagCoordinates("123", "123", refNameList).getBoardSizeX());
        AoiFile aoiFile = new AoiFile();
        aoiFile.setMd5Code("123321");
        PowerMockito.when(repository.getAoiFileInfoByBomAndType(Mockito.anyString(), Mockito.anyString())).thenReturn(aoiFile);
        Assert.assertNull(aoiFileService.getTagCoordinates("123", "123", refNameList).getBoardSizeX());
        aoiFile.setType("SPC-A");
        aoiFile.setCreateBy("123321");
        aoiFile.setFileName("123321");
        aoiFile.setDocId("123321");
        PowerMockito.when(FileUtils.createFilePathAndCheck(Mockito.anyString())).thenReturn("/usr/local/tomcat/1.jpg");
        PowerMockito.whenNew(File.class).withAnyArguments().thenReturn(file1);
        PowerMockito.when(file1.getName()).thenReturn("123321");
        try {
            aoiFileService.getTagCoordinates("123", "SPC-A", refNameList);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void transFile() throws Exception {
        try {
            Whitebox.invokeMethod(aoiFileService, "transFile", file);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    @Test
    public void setHeadData() throws Exception {
        List<String[]> strings = new ArrayList<>();
        try {
            Whitebox.invokeMethod(aoiFileService, "setHeadData", new SpcDTO(), strings);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FILE_PARSING_ERROR, e.getMessage());
        }
        String[] a = {"1", "2"};
        String[] b = {"BoardSizeX", "2"};
        String[] c = {"123", "BoardSizeY"};
        String[] d = {"123", "321"};
        strings.add(a);
        strings.add(b);
        strings.add(c);
        strings.add(d);
        SpcDTO spcDTO = new SpcDTO();
        Whitebox.invokeMethod(aoiFileService, "setHeadData", spcDTO, strings);
        Assert.assertNotNull(spcDTO.getBoardSizeX());
    }
    @Test
    public void setDetailIndex() throws Exception {
        List<String[]> strings = new ArrayList<>();
        try {
            Whitebox.invokeMethod(aoiFileService, "setDetailIndex", new LocRangeDTO(), strings);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FILE_PARSING_ERROR, e.getMessage());
        }
        String[] a = {"1"};
        String[] b = {"RefName"};
        String[] c = {"StepCenterX"};
        String[] d = {"StepCenterY"};
        String[] e = {"StepSizeX"};
        String[] f = {"StepSizeY"};
        strings.add(a);
        strings.add(b);
        strings.add(c);
        strings.add(d);
        strings.add(e);
        strings.add(f);
        LocRangeDTO loc = new LocRangeDTO();
        Whitebox.invokeMethod(aoiFileService, "setDetailIndex", loc, strings);
        Assert.assertNotNull(loc.getRefNameIdx());
    }
    @Test
    public void getDetailData() throws Exception {
        LocRangeDTO dto = new LocRangeDTO();
        dto.setStartRowNum(0);
        dto.setStepSizeXIdx(0);
        dto.setStepSizeYIdx(0);
        dto.setStepCenterXIdx(0);
        dto.setStepCenterYIdx(0);
        dto.setRefNameIdx(1);
        List<String> refNameList = new ArrayList<String>();
        refNameList.add("qqq");
        List<String[]> strings = new ArrayList<>();
        String[] a = {"1", "RefName"};
        String[] b = {"1", "RefName"};
        strings.add(b);
        strings.add(a);
        Assert.assertNotNull(Whitebox.invokeMethod(aoiFileService, "getDetailData", dto, strings, refNameList));
        String[] c = {"1", "qqq"};
        strings.add(c);
        Assert.assertNotNull(Whitebox.invokeMethod(aoiFileService, "getDetailData", dto, strings, refNameList));
    }

}
/*Ended by AICoder, pid:of2eck7da5k3be91479f0a99619e074bf6618a4c*/