package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.SysLookupValuesService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.WorkshopResourceCapacity;
import com.zte.domain.model.WorkshopResourceCapacityRepository;
import com.zte.infrastructure.remote.ApsRemoteService;
import com.zte.interfaces.dto.APSDataDTO;
import com.zte.interfaces.dto.WorkshopResourceCapacityQueryDTO;
import com.zte.interfaces.dto.WorkshopResourceImportResultDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.ums.nfv.eco.hsif.msb.common.RouteException;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

/**
 * ClassName: WorkshopResourceCapacityServiceImplTest
 * Description:
 *
 * <AUTHOR>
 * @date 2023/7/26 上午10:20
 */
@PrepareForTest({MicroServiceRestUtil.class})
public class WorkshopResourceCapacityServiceImplTest extends BaseTestCase {
    @InjectMocks
    private WorkshopResourceCapacityServiceImpl service;

    @Mock
    private WorkshopResourceCapacityRepository repository;

    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Mock
    private SysLookupValuesService lookupValuesService;


    @Mock
    private ApsRemoteService apsRemoteService;

    @Test
    public void deleteByPrimaryKey() {
        Assert.assertNotNull(service.insertSelective(null));
    }

    @Test
    public void insert() {
        Assert.assertNotNull(service.insertSelective(null));
    }

    @Test
    public void insertBatch() throws RouteException, IOException {
        service.insertBatch(null, "1");
        service.insertBatch(Lists.newArrayList(new WorkshopResourceCapacity()), "1");
        PowerMockito.when(repository.checkExist(any())).thenReturn(Lists.newArrayList("1"));
        try {
            service.insertBatch(Lists.newArrayList(new WorkshopResourceCapacity()), "1");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKSHOP_CAPACITY_ALREADY_EXISTS, e.getMessage());
        }
    }

    @Test
    public void insertSelective() {
        Assert.assertNotNull(service.insertSelective(null));
    }

    @Test
    public void selectByPrimaryKey() {
        Assert.assertNull(service.selectByPrimaryKey("1"));
    }

    @Test
    public void updateByPrimaryKeySelective() {
        service.updateByPrimaryKeySelective(null);
        PowerMockito.when(repository.checkExistSingle(any())).thenReturn("1");
        try {
            service.updateByPrimaryKeySelective(null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.WORKSHOP_CAPACITY_ALREADY_EXISTS,e.getMessage());
        }
    }

    @Test
    public void updateByPrimaryKey() {
        Assert.assertNotNull(service.updateByPrimaryKey(null));
    }

    @Test
    public void selectList() {
        Assert.assertNotNull(service.selectList(null));
    }

    @Test
    public void selectPage() {
        Page<WorkshopResourceCapacity> page = service.selectPage(new WorkshopResourceCapacityQueryDTO() {{
            setCurrent(1);
            setPageSize(1);
        }});
        Assert.assertTrue(page.getRows().size() >= 0);
    }

    @Test
    public void getModelNames() {
        Assert.assertNotNull(service.getModelNames("1"));
    }

    @Test
    public void export() throws IOException {
        HttpServletResponse response = new MockHttpServletResponse();
        service.export(new WorkshopResourceCapacityQueryDTO(), response);
        PowerMockito.when(repository.selectPage(any())).thenReturn(Lists.newArrayList(
                new WorkshopResourceCapacity() {{
                    setProcessInformation(JSON.toJSONString(Lists.newArrayList()));
                }},
                new WorkshopResourceCapacity()
        ));
        service.export(new WorkshopResourceCapacityQueryDTO(), response);
        try {
            PowerMockito.when(repository.selectPage(any())).thenReturn(Lists.newArrayList(
                    new WorkshopResourceCapacity() {{
                        setProcessInformation("1");
                    }}
            ));
            service.export(new WorkshopResourceCapacityQueryDTO(), response);
        } catch (Exception e) {
            Assert.assertEquals(null,e.getMessage());
        }
    }

    @Test
    public void delByIdList() {
        WorkshopResourceCapacity dto = new WorkshopResourceCapacity();
        try {
            service.delByIdList(dto);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.DO_NOT_PERFORM_DELETION_OPERATION_WITHOUT_INCOMING_DATA,e.getMessage());
        }
        List<String> list = new ArrayList<>();
        list.add("123");
        dto.setWkspRcIdList(list);
        service.delByIdList(dto);

    }

    @Test
    public void downloadTemplate() throws IOException {
        service.downloadTemplate(new MockHttpServletResponse());
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void importExcel() {
        try {
            service.importExcel(null, "1");
        } catch (Exception e) {
            Assert.assertEquals(null,e.getMessage());
        }
        MultipartFile file = new MockMultipartFile("1", "1", Constant.APPLICATION_EXCEL, "".getBytes());
        try {
            service.importExcel(file, "1");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.EXCEL_READ_FAILED,e.getMessage());
        }
    }

    @Test
    public void checkExcelData() throws Exception {
        service.checkExcelData(Lists.newArrayList(new WorkshopResourceImportResultDTO()), "1");
        PowerMockito.when(lookupValuesService.selectValuesByType(any())).thenReturn(Lists.newArrayList(new SysLookupValues() {{
            setLookupMeaning("1");
        }}, new SysLookupValues() {{
            setLookupMeaning("主设备");
        }}));
        PowerMockito.when(repository.checkExist(any())).thenReturn(Lists.newArrayList("2211"));
        service.checkExcelData(Lists.newArrayList(
                new WorkshopResourceImportResultDTO() {{
                    setProductionUnit("2");
                    setWorkOrderCategory("2");
                    setPlanGroupName("1");
                    setModelName("1");
                    setSummaryModel("1");
                    setProcessNode("2");
                    setKeyProcesses("2");
                    setModelRatedCapacity("1");
                    setModelDesignProductionCapacity("2");
                    setProcessCapacity("1");
                    setProcessCycle("1");
                    setFrontBiasTime("1");
                    setPostBiasTime("1");
                }},
                new WorkshopResourceImportResultDTO() {{
                    setProductionUnit("1");
                    setWorkOrderCategory("1");
                    setPlanGroupName("planGroupName1");
                    setModelName("modelName1");
                    setProcessNode("1");
                    setKeyProcesses("1");
                    setModelRatedCapacity("-1");
                    setSummaryModelLineCapacity("-1");
                    setProcessCapacity("-1");
                    setProcessCycle("-1");
                    setFrontBiasTime("-1");
                    setPostBiasTime("q");
                }},
                new WorkshopResourceImportResultDTO() {{
                    setProductionUnit("1");
                    setWorkOrderCategory("主设备");
                    setPlanGroupName("planGroupName1");
                    setModelName("1");
                }},
                new WorkshopResourceImportResultDTO() {{
                    setProductionUnit("1");
                    setWorkOrderCategory("主设备");
                    setPlanGroupName("planGroupName1");
                    setModelName("modelName1");
                }}
        ), "1");
        Assert.assertNull(service.checkExcelData(Lists.newArrayList(
                new WorkshopResourceImportResultDTO() {{
                    setProductionUnit("1");
                    setWorkOrderCategory("主设备");
                    setPlanGroupName("planGroupName1");
                    setModelName("modelName1");
                    setProcessNode("1");
                    setKeyProcesses("1");
                    setModelRatedCapacity("1");
                    setProcessCapacity("1");
                    setProcessCycle("1");
                    setFrontBiasTime("1");
                    setPostBiasTime("1");
                }},
                new WorkshopResourceImportResultDTO() {{
                    setProductionUnit("1");
                    setWorkOrderCategory("1");
                    setPlanGroupName("planGroupName1");
                    setModelName("modelName1");
                    setSummaryModel("1");
                    setProcessNode("1");
                    setKeyProcesses("1");
                    setModelRatedCapacity("1");
                    setSummaryModelLineCapacity("1");
                    setProcessCapacity("1");
                    setProcessCycle("1");
                    setFrontBiasTime("1");
                    setPostBiasTime("1");
                }}
        ), "1"));
    }

    @Test
    public void tranToSaveList() {
        Assert.assertNotNull(service.tranToSaveList(Lists.newArrayList(

                new WorkshopResourceImportResultDTO() {{
                    setProductionUnit("1");
                    setWorkOrderCategory("1");
                    setPlanGroupName("planGroupName1");
                    setModelName("modelName1");
                    setSummaryModel("1");
                    setProcessNode("1");
                    setKeyProcesses("1");
                    setModelRatedCapacity("1");
                    setSummaryModelLineCapacity("1");
                    setProcessCapacity("1");
                    setProcessCycle("1");
                    setFrontBiasTime("1");
                    setPostBiasTime("1");
                }},
                new WorkshopResourceImportResultDTO() {{
                    setProductionUnit("1");
                    setWorkOrderCategory("1");
                    setPlanGroupName("planGroupName1");
                    setModelName("modelName1");
                    setSummaryModel("1");
                    setProcessNode("1");
                    setKeyProcesses("1");
                    setModelRatedCapacity("1");
                    setSummaryModelLineCapacity("1");
                    setModelDesignProductionCapacity("2");
                    setProcessCapacity("1");
                    setProcessCycle("1");
                    setFrontBiasTime("1");
                    setPostBiasTime("1");
                }},
                new WorkshopResourceImportResultDTO() {{
                    setProductionUnit("11");
                    setWorkOrderCategory("1");
                    setPlanGroupName("planGroupName11");
                    setModelName("modelName11");
                    setSummaryModel("1");
                    setProcessNode("1");
                    setKeyProcesses("1");
                    setModelRatedCapacity("1");
                    setSummaryModelLineCapacity("1");
                    setModelDesignProductionCapacity("2");
                    setProcessCapacity("1");
                    setProcessCycle("1");
                    setFrontBiasTime("1");
                    setPostBiasTime("1");
                }}
        )));
    }

    @Test
    public void downloadImportResult() throws Exception {
        HttpServletResponse response = new MockHttpServletResponse();
        service.downloadImportResult(response, "", "", "");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void getAPSData() {
        Assert.assertNotNull(service.getAPSData());
    }

    @Test
    public void scheduledRefreshPlanAndModel() {
        ReflectionTestUtils.setField(service, "maxNum", 2);
        service.scheduledRefreshPlanAndModel("");
        List<APSDataDTO> apsDataDTOList = new ArrayList<>();
        apsDataDTOList.add(new APSDataDTO(){{setPlanGroupId("1");setModelNo("1");}});
        apsDataDTOList.add(new APSDataDTO(){{setPlanGroupId("1");setModelNo("1");}});
        apsDataDTOList.add(new APSDataDTO(){{setPlanGroupId("1");setModelNo("1");}});
        PowerMockito.when(apsRemoteService.getAPSDataAll()).thenReturn(apsDataDTOList);
        try {
            service.scheduledRefreshPlanAndModel("");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.MORE_THAN_MAX_NUM, e.getMessage());
        }
        apsDataDTOList.clear();
        apsDataDTOList.add(new APSDataDTO(){{setPlanGroupId("1");setModelNo("1");}});
        service.scheduledRefreshPlanAndModel("");

        service.batchLoseEfficacyByIdList("",null);
        List<String> updateList = new ArrayList<>();
        updateList.add("2");
        service.batchLoseEfficacyByIdList("",updateList);
        Map<String, APSDataDTO> apsDataDTOMap = new HashMap<>();
        apsDataDTOMap.put("2_1",new APSDataDTO());
        List<WorkshopResourceCapacity> workshopResourceCapacityList = new ArrayList<>();
        workshopResourceCapacityList.add(new WorkshopResourceCapacity(){{setPlanGroupId("1");setModelNo("2");}});
        workshopResourceCapacityList.add(new WorkshopResourceCapacity(){{setPlanGroupId("2");setModelNo("2");}});
        service.checkExist(apsDataDTOMap,updateList,workshopResourceCapacityList);
        apsDataDTOList.clear();
        apsDataDTOList.add(new APSDataDTO(){{setPlanGroupId("1");setModelNo("1");setExist("Y");}});
        service.batchInsert("", apsDataDTOList);
    }
}
