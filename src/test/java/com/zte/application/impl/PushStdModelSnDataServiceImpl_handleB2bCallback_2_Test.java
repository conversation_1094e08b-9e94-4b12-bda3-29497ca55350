/*Started by AICoder, pid:p857ax349bh9f0d14874094be1508b2426a78bfb*/
package com.zte.application.impl;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PushStdModelSnData;
import com.zte.domain.model.PushStdModelSnDataRepository;
import com.zte.domain.model.PushStdModelSnDataSub;
import com.zte.domain.model.PushStdModelSnDataSubRepository;
import com.zte.domain.model.TradeDataLogRepository;
import com.zte.gei.common.utils.HttpClientUtil;
import com.zte.interfaces.dto.B2bCallBackNewDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.FinishedProductStorageDTO;
import com.zte.interfaces.dto.FinishedProductStorageItemDTO;
import com.zte.interfaces.dto.PushStdModelSnDataDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.util.Pair;

import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@PrepareForTest({RequestHeadValidationUtil.class,ServiceDataBuilderUtil.class,
        JacksonJsonConverUtil.class,HttpClientUtil.class,MESHttpHelper.class})
public class PushStdModelSnDataServiceImpl_handleB2bCallback_2_Test extends BaseTestCase {

    @Mock
    private PushStdModelSnDataRepository pushStdModelSnDataRepository;

    @InjectMocks
    private PushStdModelSnDataServiceImpl pushStdModelSnDataService;
    @Mock
    private Map<String,String> requestDefaultHeadMap;
    @Mock
    private Map<String, String> sameMessageTypeCallBackMap;
    @Mock
    private TradeDataLogRepository tradeDataLogRepository;
    @Mock
    private PushStdModelSnDataSubRepository pushStdModelSnDataSubRepository;

    @Before
    public void setUp() throws Exception{
        // Setup code if needed
        PowerMockito.mockStatic(RequestHeadValidationUtil.class, MESHttpHelper.class,
                JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class, HttpClientUtil.class);
        PowerMockito.field(PushStdModelSnDataServiceImpl.class, "requestDefaultHeadMap")
                .set(pushStdModelSnDataService, new HashMap<>());
        PowerMockito.field(PushStdModelSnDataServiceImpl.class, "sameMessageTypeCallBackMap")
                .set(pushStdModelSnDataService, new HashMap<>());
        PowerMockito.when(RequestHeadValidationUtil.validaFactoryId()).thenReturn("55");
        PowerMockito.when(RequestHeadValidationUtil.validaFactoryIdAndEmpno()).thenReturn(Pair.of("43","44"));
    }

    /* Ended by AICoder, pid:aeb864a918wa7cc1478d09f37059684140419e92 */
    @Test
    public void handleB2bCallback(){
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setKeywords("233" + Constant.CALL_BACK_SN_REPLACEMENT);
        boolean b = pushStdModelSnDataService.handleB2bCallback(b2bCallBackNewDTO);
        assertTrue(b);

        CustomerDataLogDTO tradeDataLogById = new CustomerDataLogDTO();
        PowerMockito.when(tradeDataLogRepository.getTradeDataLogById(Mockito.any()))
                .thenReturn(tradeDataLogById);
        pushStdModelSnDataService.handleB2bCallback(b2bCallBackNewDTO);

        tradeDataLogById.setFactoryId(2);
        pushStdModelSnDataService.handleB2bCallback(b2bCallBackNewDTO);

        b2bCallBackNewDTO.setKeywords("233" + Constant.CALL_BACK_DATA_SUB);
        pushStdModelSnDataService.handleB2bCallback(b2bCallBackNewDTO);

        PushStdModelSnDataSub dataSub = new PushStdModelSnDataSub();
        dataSub.setPushFailCount(1);
        PowerMockito.when(pushStdModelSnDataSubRepository.selectById(Mockito.any()))
                .thenReturn(dataSub);
        pushStdModelSnDataService.handleB2bCallback(b2bCallBackNewDTO);

        String msg = "{\"code\":\"0000\",\"data\":\"{\\\"data\\\":{\\\"code\\\":\\\"200\\\"," +
                "\\\"result\\\":\\\"{\\\\\\\"msg\\\\\\\":\\\\\\\"成功\\\\\\\",\\\\\\\"trace_id\\\\\\\":null," +
                "\\\\\\\"total\\\\\\\":0,\\\\\\\"code\\\\\\\":\\\\\\\"00000000\\\\\\\"," +
                "\\\\\\\"data\\\\\\\":{\\\\\\\"check_state\\\\\\\":5,\\\\\\\"check_error_msg\\\\\\\":\\\\\\\"\\\\\\\"},\\\\\\\"success\\\\\\\":true,\\\\\\\"pending\\\\\\\":false}\\\",\\\"success\\\":true},\\\"request_id\\\":\\\"eg59t39cdfp6\\\"}\",\"keywords\":\"1928366460053090304#dataSub\",\"messageId\":\"1928374409732820992\",\"messageType\":\"ZTEiMES-Alibaba-ProductSnReport\",\"requestId\":\"eg59t39cdfp6\",\"source\":\"Alibaba\",\"success\":true}";
        b2bCallBackNewDTO = JSON.parseObject(msg, B2bCallBackNewDTO.class);
        pushStdModelSnDataService.handleB2bCallback(b2bCallBackNewDTO);
    }
    /* Ended by AICoder, pid:aeb864a918wa7cc1478d09f37059684140419e92 */

    @Test
    public void testHandleB2bCallback_WithoutKeywords() {
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setKeywords(null);

        boolean result = pushStdModelSnDataService.handleB2bCallback(b2bCallBackNewDTO);

        assertFalse(result);
    }

    @Test
    public void testHandleB2bCallback_Success() {
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setKeywords("testKey");
        b2bCallBackNewDTO.setSuccess(true);
        b2bCallBackNewDTO.setData("{\"data\": {\"success\": true, \"result\": {\"success\": true}}}");

        when(pushStdModelSnDataRepository.batchUpdate(any())).thenReturn(1);

        boolean result = pushStdModelSnDataService.handleB2bCallback(b2bCallBackNewDTO);

        assertTrue(result);
        verify(pushStdModelSnDataRepository).batchUpdate(any());
    }

    @Test
    public void testHandleB2bCallback_b2bCallBackNewFailure() {
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setKeywords("testKey");
        b2bCallBackNewDTO.setSuccess(false);
        b2bCallBackNewDTO.setData("error data");

        when(pushStdModelSnDataRepository.batchUpdate(any())).thenReturn(1);

        boolean result = pushStdModelSnDataService.handleB2bCallback(b2bCallBackNewDTO);

        assertTrue(result);
        verify(pushStdModelSnDataRepository).batchUpdate(any());
    }

    @Test
    public void testHandleB2bCallback_FinishedProdCallbackNull() {
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setKeywords("testKey");
        b2bCallBackNewDTO.setSuccess(true);
        b2bCallBackNewDTO.setData(null);
        b2bCallBackNewDTO.setMessageType(Constant.MESSAGE_TYPE_FINISHED_PRODUCT_STORAGE);

        when(tradeDataLogRepository.getTradeDataLogById(any())).thenReturn(null);

        boolean result = pushStdModelSnDataService.handleB2bCallback(b2bCallBackNewDTO);

        assertTrue(result);
        verify(pushStdModelSnDataRepository, never()).batchUpdate(any());
    }

    @Test
    public void testHandleB2bCallback_OrderWriteCallBackNull() {
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setKeywords("testKey");
        b2bCallBackNewDTO.setSuccess(true);
        b2bCallBackNewDTO.setData(null);

        boolean result = pushStdModelSnDataService.handleB2bCallback(b2bCallBackNewDTO);

        assertTrue(result);
        verify(pushStdModelSnDataRepository).batchUpdate(any());
    }

    @Test
    public void testHandleB2bCallback_OrderWriteCallBackDataFalse() {
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setKeywords("testKey");
        b2bCallBackNewDTO.setSuccess(true);
        b2bCallBackNewDTO.setData("{\"data\": {\"success\": false, \"result\": {\"success\": false}}}");
        b2bCallBackNewDTO.setMessageType(Constant.MESSAGE_TYPE_FINISHED_PRODUCT_STORAGE);

        when(tradeDataLogRepository.getTradeDataLogById(any())).thenReturn(new CustomerDataLogDTO());
        when(tradeDataLogRepository.getPushData(any())).thenReturn("test");
        when(pushStdModelSnDataRepository.update(any(PushStdModelSnDataDTO.class))).thenReturn(1);
        PowerMockito.when(JacksonJsonConverUtil.jsonToBean("test", FinishedProductStorageDTO.class))
                .thenReturn(new FinishedProductStorageDTO() {{ setMaterialBillList(Lists.newArrayList(new FinishedProductStorageItemDTO()));}});
        when(pushStdModelSnDataRepository.selectExists(any(), any())).thenReturn(Lists.newArrayList(new PushStdModelSnDataDTO()));

        boolean result = pushStdModelSnDataService.handleB2bCallback(b2bCallBackNewDTO);

        assertTrue(result);
        verify(pushStdModelSnDataRepository).batchUpdate(any());
    }

    @Test
    public void testHandleB2bCallback_OrderWriteCallBackDataNull() {
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setKeywords("testKey");
        b2bCallBackNewDTO.setSuccess(true);
        b2bCallBackNewDTO.setData("{\"data\": null}");

        when(pushStdModelSnDataRepository.batchUpdate(any())).thenReturn(1);

        boolean result = pushStdModelSnDataService.handleB2bCallback(b2bCallBackNewDTO);

        assertTrue(result);
        verify(pushStdModelSnDataRepository).batchUpdate(any());
    }

    @Test
    public void testHandleB2bCallback_OrderWriteCallBackResultNull() {
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setKeywords("testKey");
        b2bCallBackNewDTO.setSuccess(true);
        b2bCallBackNewDTO.setData("{\"data\": {\"success\": true, \"result\": null}}");

        when(pushStdModelSnDataRepository.batchUpdate(any())).thenReturn(1);

        boolean result = pushStdModelSnDataService.handleB2bCallback(b2bCallBackNewDTO);

        assertTrue(result);
        verify(pushStdModelSnDataRepository).batchUpdate(any());
    }

    @Test
    public void testHandleB2bCallback_OrderWriteCallBackResultFalse() {
        B2bCallBackNewDTO b2bCallBackNewDTO = new B2bCallBackNewDTO();
        b2bCallBackNewDTO.setKeywords("testKey");
        b2bCallBackNewDTO.setSuccess(true);
        b2bCallBackNewDTO.setData("{\"data\": {\"success\": true, \"result\": {\"success\": false}}}");

        when(pushStdModelSnDataRepository.batchUpdate(any())).thenReturn(1);

        boolean result = pushStdModelSnDataService.handleB2bCallback(b2bCallBackNewDTO);

        assertTrue(result);
        verify(pushStdModelSnDataRepository).batchUpdate(any());
    }

    @Test
    public void testHandleB2bCallback_OrderWriteCallBackResultDataError() {
        String msg = "{\"code\":\"0000\",\"data\":\"{\\\"data\\\":{\\\"code\\\":\\\"200\\\",\\\"result\\\":\\\"{\\\\\\\"msg\\\\\\\":\\\\\\\"成功\\\\\\\",\\\\\\\"trace_id\\\\\\\":null,\\\\\\\"total\\\\\\\":0,\\\\\\\"code\\\\\\\":\\\\\\\"00000000\\\\\\\",\\\\\\\"data\\\\\\\":{\\\\\\\"check_state\\\\\\\":5,\\\\\\\"check_error_msg\\\\\\\":\\\\\\\"[];机型: G83G4.J2.C0V1P0U1 传入的BOM中缺少物料类目:ais.PCBAZHUBAN;机型: G83G4.J2.C0V1P0U1 传入的BOM中缺少物料类目:server.fan;机型: G83G4.J2.C0V1P0U1 传入的BOM中缺少物料类目:server.gpu;机型: G83G4.J2.C0V1P0U1 传入的BOM中缺少物料类目:server.psu\\\\\\\"},\\\\\\\"success\\\\\\\":true,\\\\\\\"pending\\\\\\\":false}\\\",\\\"success\\\":true},\\\"request_id\\\":\\\"eg59t39cdfp6\\\"}\",\"keywords\":\"1928366460053090304\",\"messageId\":\"1928374409732820992\",\"messageType\":\"ZTEiMES-Alibaba-ProductSnReport\",\"requestId\":\"eg59t39cdfp6\",\"source\":\"Alibaba\",\"success\":true}";
        B2bCallBackNewDTO b2bCallBackNewDTO = JSON.parseObject(msg, B2bCallBackNewDTO.class);
        boolean result = pushStdModelSnDataService.handleB2bCallback(b2bCallBackNewDTO);
        assertTrue(result);
        verify(pushStdModelSnDataRepository).batchUpdate(any());
    }

    /* Started by AICoder, pid:m9e11lc32d19895142e40b926012ff389c0076cd */
    @Test
    public void queryPushStdModelSn(){
        PushStdModelSnData pushStdModelSnData = new PushStdModelSnData();
        try {
            pushStdModelSnDataService.replaceSnStdModelData(pushStdModelSnData);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_PUSH_STD_MODEL_LOST, e.getMessage());
        }

        PushStdModelSnDataDTO pushStdModelSn = new PushStdModelSnDataDTO();
        PowerMockito.when(pushStdModelSnDataRepository.queryPushStdModelSn(Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(pushStdModelSn);

    }
    /* Ended by AICoder, pid:m9e11lc32d19895142e40b926012ff389c0076cd */

    /*Started by AICoder, pid:r7c337d4144079a14a900862411ccc96c445721b*/
    @Test
    public void testQueryPushStdModelSnList_SingleBatchWithResults() {
        List<PushStdModelSnData> queryList = createSampleList(50);
        List<PushStdModelSnDataDTO> mockResult = createSampleDtoList(50);

        when(pushStdModelSnDataRepository.queryPushStdModelSnList(anyList()))
                .thenReturn(mockResult);

        List<PushStdModelSnDataDTO> result = pushStdModelSnDataService.queryPushStdModelSnList(queryList);

        assertEquals(50, result.size());
        verify(pushStdModelSnDataRepository).queryPushStdModelSnList(queryList);
    }

    @Test
    public void testQueryPushStdModelSnList_MultipleBatches() {
        List<PushStdModelSnData> queryList = createSampleList(150);
        List<PushStdModelSnDataDTO> batch1Result = createSampleDtoList(100);
        List<PushStdModelSnDataDTO> batch2Result = createSampleDtoList(50);

        when(pushStdModelSnDataRepository.queryPushStdModelSnList(argThat(list -> list.size() == 100)))
                .thenReturn(batch1Result);

        List<PushStdModelSnDataDTO> result = pushStdModelSnDataService.queryPushStdModelSnList(queryList);

        assertEquals(100, result.size());
    }

    @Test
    public void testQueryPushStdModelSnList_EmptyBatchResults() {
        List<PushStdModelSnData> queryList = createSampleList(100);

        when(pushStdModelSnDataRepository.queryPushStdModelSnList(anyList()))
                .thenReturn(Collections.emptyList());

        List<PushStdModelSnDataDTO> result = pushStdModelSnDataService.queryPushStdModelSnList(queryList);

        Assert.assertTrue(result.isEmpty());
        verify(pushStdModelSnDataRepository).queryPushStdModelSnList(anyList());
    }

    @Test
    public void testQueryPushStdModelSnList_PartialEmptyBatches() {
        pushStdModelSnDataService.queryPushStdModelSnList(null);
        List<PushStdModelSnData> queryList = createSampleList(150);
        List<PushStdModelSnDataDTO> batch1Result = createSampleDtoList(100);

        when(pushStdModelSnDataRepository.queryPushStdModelSnList(argThat(list -> list.size() == 100)))
                .thenReturn(batch1Result);

        List<PushStdModelSnDataDTO> result = pushStdModelSnDataService.queryPushStdModelSnList(queryList);

        assertEquals(100, result.size());
        verify(pushStdModelSnDataRepository, times(2)).queryPushStdModelSnList(anyList());
    }

    private List<PushStdModelSnData> createSampleList(int size) {
        List<PushStdModelSnData> list = new java.util.ArrayList<>();
        for (int i = 0; i < size; i++) {
            list.add(new PushStdModelSnData());
        }
        return list;
    }

    private List<PushStdModelSnDataDTO> createSampleDtoList(int size) {
        List<PushStdModelSnDataDTO> list = new java.util.ArrayList<>();
        for (int i = 0; i < size; i++) {
            list.add(new PushStdModelSnDataDTO());
        }
        return list;
    }
    /*Ended by AICoder, pid:r7c337d4144079a14a900862411ccc96c445721b*/

    /* Started by AICoder, pid:aa8f5r56d6e19cc1437708ac80e27b1e3e817bb1 */
    @Test
    public void updateVirtualSnBatch() {
        List<PushStdModelSnData> snDataList = new LinkedList<>();
        for (int i = 0; i < 50; i++) {
            snDataList.add(new PushStdModelSnData());
        }
        pushStdModelSnDataService.updateVirtualSnBatch(snDataList);
        assertTrue(CollectionUtils.isNotEmpty(snDataList));
    }

    /* Ended by AICoder, pid:aa8f5r56d6e19cc1437708ac80e27b1e3e817bb1 */
}
/*Ended by AICoder, pid:p857ax349bh9f0d14874094be1508b2426a78bfb*/