package com.zte.application.impl;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.zte.application.BsItemInfoService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.BBomDetailRepository;
import com.zte.domain.model.BPcbLocationDetail;
import com.zte.domain.model.BPcbLocationDetailRepository;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.BsPremanuBomInfo;
import com.zte.domain.model.BsPremanuBomInfoRepository;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BsPremanuBomInfoDTO;
import com.zte.interfaces.dto.BsPremanuItemInfoDTO;
import com.zte.interfaces.dto.datawb.ProdSmtWriteDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ImesExcelUtil.class, EasyExcelFactory.class})
public class BsPremanuBomInfoServiceImplTest extends BaseTestCase {
    @InjectMocks
    private BsPremanuBomInfoServiceImpl bsPremanuBomInfoService;
    @Mock
    private BsPremanuBomInfoRepository bsPremanuBomInfoRepository;
    @Mock
    private BBomDetailRepository bBomDetailRepository;
    @Mock
    private BPcbLocationDetailRepository bPcbLocationDetailRepository;
    @Mock
    private HttpServletResponse response;
    @Mock
    private WriteSheet build;
    @Mock
    private ExcelWriter excelWriter;
    @Mock
    private ExcelWriterSheetBuilder excelWriterSheetBuilder;
    @Mock
    private ExcelWriterBuilder write;
    @Mock
    private ExcelWriterBuilder excelWriterBuilder;
    @Mock
    private BsItemInfoService bsItemInfoService;

    @InjectMocks
    private BsPremanuItemInfoServiceImpl bsPremanuItemInfoService;

    @Test
    public void insertBsPremanuBomInfoSelective() {
        BsPremanuBomInfo record = new BsPremanuBomInfo();
        record.setRecordId("111");
        PowerMockito.when(bsPremanuBomInfoRepository.insertBsPremanuBomInfoSelective(Mockito.any())).thenReturn(1);
        bsPremanuBomInfoService.insertBsPremanuBomInfoSelective(record);
        Assert.assertNotNull(record);
    }
    @Test
    public void deleteBsPremanuBomInfoById() {
        BsPremanuBomInfo record = new BsPremanuBomInfo();
        record.setRecordId("111");
        PowerMockito.when(bsPremanuBomInfoRepository.deleteBsPremanuBomInfoById(Mockito.any())).thenReturn(1);
        bsPremanuBomInfoService.deleteBsPremanuBomInfoById(record);
        Assert.assertNotNull(record);
    }

    @Test
    public void updateBsPremanuBomInfoById() {
        BsPremanuBomInfo record = new BsPremanuBomInfo();
        record.setRecordId("111");
        PowerMockito.when(bsPremanuBomInfoRepository.updateBsPremanuBomInfoById(Mockito.any())).thenReturn(1);
        bsPremanuBomInfoService.updateBsPremanuBomInfoById(record);
        Assert.assertNotNull(record);
    }

    @Test
    public void selectBsPremanuBomInfoById() {
        BsPremanuBomInfo record = new BsPremanuBomInfo();
        record.setRecordId("111");
        PowerMockito.when(bsPremanuBomInfoRepository.selectBsPremanuBomInfoById(Mockito.any())).thenReturn(record);
        bsPremanuBomInfoService.selectBsPremanuBomInfoById(record);
        Assert.assertNotNull(record);
    }

    @Test
    public void getBomPreInfo() {
        BsPremanuBomInfoDTO record = new BsPremanuBomInfoDTO();
        record.setPage(1);
        record.setRows(10);
        Page<BsPremanuBomInfoDTO> pageInfo = new Page<>();
        pageInfo.setParams(record);
        List<BsPremanuBomInfo> preInfoList = new ArrayList<>();
        PowerMockito.when(bsPremanuBomInfoRepository.getBomPreInfo(Mockito.any())).thenReturn(preInfoList);
        pageInfo.setRows(new ArrayList<>());
        bsPremanuBomInfoService.getBomPreInfo(record);
        Assert.assertNotNull(record);
    }

    @Test
    public void mergeIntoPreBom() {
        BsPremanuBomInfo record = new BsPremanuBomInfo();
        record.setRecordId("111");
        PowerMockito.when(bsPremanuBomInfoRepository.mergeIntoPreBom(Mockito.any())).thenReturn(1);
        bsPremanuBomInfoService.mergeIntoPreBom(record);
        Assert.assertNotNull(record);
    }

    @Test
    public void deleteBsPremanuByItemAndBom() {
        BsPremanuBomInfo record = new BsPremanuBomInfo();
        record.setRecordId("111");
        PowerMockito.when(bsPremanuBomInfoRepository.deleteBsPremanuByItemAndBom(Mockito.any())).thenReturn(1);
        bsPremanuBomInfoService.deleteBsPremanuByItemAndBom(record);
        Assert.assertNotNull(record);
    }

    @Test
    public void selectBsPremanuBomInfoSelective() throws Exception{
        BsPremanuBomInfoDTO record = new BsPremanuBomInfoDTO();
        record.setPage(1);
        record.setRows(10);
        Page<BsPremanuBomInfoDTO> pageInfo = new Page<>();
        pageInfo.setParams(record);
        List<BsPremanuBomInfo> preInfoList = new ArrayList<>();
        PowerMockito.when(bsPremanuBomInfoRepository.selectBsPremanuBomInfoSelective(Mockito.any())).thenReturn(preInfoList);
        record.setItemNo("111");
        StringBuilder itemBuilder = new StringBuilder();
        itemBuilder.append("'").append("111").append("'").append(",");
        BsItemInfo queryItem = new BsItemInfo();
        queryItem.setInItemNo("111");
        List<BsItemInfo> itemList = new ArrayList<>();
        bsItemInfoService.getInfoList(queryItem);
        pageInfo.setRows(new ArrayList<>());
        bsPremanuBomInfoService.selectBsPremanuBomInfoSelective(record);
        Assert.assertNotNull(record);
    }

    @Test
    public void selectBsPremanuBomInfoSelective1() throws Exception{
        BsPremanuBomInfoDTO record = new BsPremanuBomInfoDTO();
        record.setPage(1);
        record.setRows(10);
        Page<BsPremanuBomInfoDTO> pageInfo = new Page<>();
        pageInfo.setParams(record);
        List<BsPremanuBomInfo> preInfoList = null;
        PowerMockito.when(bsPremanuBomInfoRepository.selectBsPremanuBomInfoSelective(Mockito.any())).thenReturn(preInfoList);
        pageInfo.setRows(new ArrayList<>());
        bsPremanuBomInfoService.selectBsPremanuBomInfoSelective(record);
        Assert.assertNotNull(record);
    }

    @Test
    public void getBsPremanuBomInfoCount() {
        BsPremanuBomInfoDTO record = new BsPremanuBomInfoDTO();
        record.setRecordId("111");
        PowerMockito.when(bsPremanuBomInfoRepository.getBsPremanuBomInfoCount(Mockito.any())).thenReturn(1L);
        bsPremanuBomInfoService.getBsPremanuBomInfoCount(record);
        Assert.assertNotNull(record);
    }

    @Test
    public void getBsPremanuBomInfoList() throws Exception {
        List<BsPremanuBomInfo> resultList = new ArrayList<>();
        BsPremanuBomInfoDTO bsPremanuBomInfoDTO = new BsPremanuBomInfoDTO();
        bsPremanuBomInfoDTO.setBomCode("PG-TEST");
        bsPremanuBomInfoDTO.setItemNo("PG-TEST");
        BsPremanuBomInfo record1 = new BsPremanuBomInfo();
        record1.setBomCode("PG-TEST");
        record1.setItemNo("PG-TEST");
        record1.setItemName("PG-TEST");
        record1.setTypeCode("PG-TEST");
        record1.setTypeName("PG-TEST");
        record1.setTraceCode("PG-TEST");
        record1.setTraceName("PG-TEST");
        record1.setDeliveryProcess("PG-TEST");
        record1.setIsPreManu("Y");
        record1.setStyle("test");
        record1.setVerNo("test");
        resultList.add(record1);
        PowerMockito.when(bsPremanuBomInfoRepository.getBsPremanuBomInfoList(any())).thenReturn(resultList);
        List<BsPremanuBomInfoDTO> result = bsPremanuBomInfoService.getBsPremanuBomInfoList(bsPremanuBomInfoDTO);
        Assert.assertTrue(result.size() >= 0);
    }

    @Test
    public void getBsPremanuBomInfoPage() {
        BsPremanuBomInfoDTO record = new BsPremanuBomInfoDTO();
        Page<BsPremanuBomInfoDTO> queryPage = bsPremanuBomInfoService.getBsPremanuBomInfoPage(record);
        Assert.assertTrue(queryPage.getRows().size() >= 0);
    }

    @Test
    public void countExportTotal() throws Exception {
        BsPremanuBomInfoDTO queryDTO = new BsPremanuBomInfoDTO();
        PowerMockito.when(bsPremanuBomInfoRepository.getBsPremanuBomInfoCount(any())).thenReturn(1L);
        Integer res = bsPremanuBomInfoService.countExportTotal(queryDTO);
        Assert.assertEquals(1, res.intValue());
    }

    @Test
    public void queryExportData() throws Exception {
        BsPremanuBomInfoDTO queryDTO = new BsPremanuBomInfoDTO();
        List<BsPremanuBomInfo> rows = new ArrayList<>();
        BsPremanuBomInfo bsPremanuBomInfo = new BsPremanuBomInfo();
        rows.add(bsPremanuBomInfo);
        PowerMockito.when(bsPremanuBomInfoRepository.getBsPremanuBomInfoPage(any())).thenReturn(rows);
        List<BsPremanuBomInfoDTO> res = bsPremanuBomInfoService.queryExportData(queryDTO, 1, 5000);
        Assert.assertEquals(1, res.size());
    }

    @Test
    public void updateTagNum() throws Exception {
        BsPremanuBomInfoDTO dto = new BsPremanuBomInfoDTO();
        dto.setTypeName("写片");
        dto.setTypeName("00286569");
        List<BsPremanuBomInfo> preManuBomDetail = new ArrayList<>();
        BsPremanuBomInfo dto1 = new BsPremanuBomInfo();
        dto1.setBomCode("130000186138ACB");
        dto1.setItemNo("045020400012");
        preManuBomDetail.add(dto1);
        PowerMockito.when(bsPremanuBomInfoRepository.getBsPremanuBomInfoListByType(any())).thenReturn(preManuBomDetail);

        List<BBomDetailDTO> bomPartList = new ArrayList<>();
        BBomDetailDTO dto2 = new BBomDetailDTO();
        dto2.setItemCode("");
        dto2.setProductCode("");
        dto2.setUsageCount(new BigDecimal(1));
        bomPartList.add(dto2);
        PowerMockito.when(bBomDetailRepository.getUsageCountByCond(Mockito.anyList())).thenReturn(bomPartList);

        List<BPcbLocationDetail> locationPartList = new ArrayList<>();
        BPcbLocationDetail dto3 = new BPcbLocationDetail();
        dto3.setItemCode("");
        dto3.setProductCode("");
        dto3.setUsageSum(new BigDecimal(1));
        locationPartList.add(dto3);
        PowerMockito.when(bPcbLocationDetailRepository.getCountByCond(Mockito.anyList())).thenReturn(locationPartList);

        PowerMockito.when(bsPremanuBomInfoRepository.updateTagNumBatch(Mockito.anyList())).thenReturn(1);
        bsPremanuBomInfoService.updateTagNum(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void checkParams() throws Exception {
        BsPremanuItemInfoDTO record = new BsPremanuItemInfoDTO();
        record.setTypeName("2222");
        record.setTypeCode("2222");
        record.setTraceName("2222");
        try {
            bsPremanuItemInfoService.updatePreManuItem(record);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.TYPE_CODE_NAME_TRACE_CODE_NAME_CAN_NOT_BE_EMPTY_ALONE,e.getMessage());
            e.printStackTrace();
        }
        Assert.assertNotNull(record);
    }

    @Test
    public void selectPreBomInfoByBomList() throws Exception {
        bsPremanuBomInfoService.selectPreBomInfoByBomList(new ArrayList<>());
        List<String> bomList = new ArrayList<>();
        bomList.add("123");
        bsPremanuBomInfoService.selectPreBomInfoByBomList(bomList);
        List<BsPremanuBomInfo> qryList = new ArrayList<>();
        qryList.add(new BsPremanuBomInfo());
        PowerMockito.when(bsPremanuBomInfoRepository.selectPreBomInfoByBomList(any())).thenReturn(qryList);
        Assert.assertNotNull(bsPremanuBomInfoService.selectPreBomInfoByBomList(bomList));
    }
    @Test
    public void buildData() throws Exception {
        List<BsPremanuBomInfo> details = new ArrayList<>();
        Map<String, List<BsPremanuBomInfo>> collectMap = new HashMap<>();
        collectMap.put("test123",details);
        List<ProdSmtWriteDTO> sendMesList = new LinkedList<>();
        List<ProdSmtWriteDTO> list = new ArrayList<>();
        sendMesList.addAll(list);
        Map<String, List<BsPremanuBomInfo>> bomPreMap = new HashMap<>();
        String tagNum ="test123";
        bomPreMap.put("test123",details);
        BsPremanuBomInfo bsPremanuBomInfo = new BsPremanuBomInfo();
        bsPremanuBomInfo.setBomCode("test123");
        bsPremanuBomInfo.setItemNo("test123");
        bsPremanuBomInfo.setLastUpdatedBy("10309565");
        bsPremanuBomInfo.setTagNum(tagNum);
        bsPremanuBomInfo.setTypeCode("XP");
        details.add(bsPremanuBomInfo);
        try {
            Whitebox.invokeMethod(bsPremanuBomInfoService, "buildData", details);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    /* Started by AICoder, pid:195aao5242d72f7149cc0b76301b941bc5441f97 */
    @Test
    public void convertParamsAfterInTest() throws Exception {
        BsPremanuBomInfoDTO dto = new BsPremanuBomInfoDTO();
        Whitebox.invokeMethod(bsPremanuBomInfoService, "convertParamsAfterIn", null);
        Assert.assertTrue(true);
        Whitebox.invokeMethod(bsPremanuBomInfoService, "convertParamsAfterIn", dto);
        Assert.assertTrue(dto.getItemNoList() == null);
        dto.setInItemNo("'1','2'");
        Whitebox.invokeMethod(bsPremanuBomInfoService, "convertParamsAfterIn", dto);
        Assert.assertTrue(dto.getItemNoList().size() == 2);
    }
    /* Ended by AICoder, pid:195aao5242d72f7149cc0b76301b941bc5441f97 */
}