package com.zte.application.authentication.impl;

import com.zte.common.model.MessageId;
import com.zte.domain.model.ZteMbcFactoryInfo;
import com.zte.domain.model.ZteMbcFactoryInfoRepository;
import com.zte.domain.model.authentication.SalCSupProduce;
import com.zte.domain.model.authentication.SalCSupProduceRepository;
import com.zte.domain.model.authentication.SnRidSupProduce;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.interfaces.dto.BarcodeExpandVO;
import com.zte.util.BaseTestCase;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;

@RunWith(PowerMockRunner.class)
public class SnRidSupProduceServiceImplTest extends BaseTestCase {

    @InjectMocks
    private SnRidSupProduceServiceImpl snRidSupProduceService;
    @Mock
    private ZteMbcFactoryInfoRepository zteMbcFactoryInfoRepository;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;
    @Mock
    private SalCSupProduceRepository salCSupProduceRepository;

    @Test
    public void insertDataForUseZTEReelId() throws Exception {

        List<List<SnRidSupProduce>> listOfListForUseZTEReelId = new ArrayList<>();
        snRidSupProduceService.insertDataForUseZTEReelId(listOfListForUseZTEReelId);
        PowerMockito.when(zteMbcFactoryInfoRepository.checkFactoryIsUseZTEReelId(any())).thenReturn(null);
        snRidSupProduceService.insertDataForUseZTEReelId(listOfListForUseZTEReelId);
        ZteMbcFactoryInfo test = new ZteMbcFactoryInfo();
        test.setIsUseZTEreelid("N");
        PowerMockito.when(zteMbcFactoryInfoRepository.checkFactoryIsUseZTEReelId(any())).thenReturn(test);
        snRidSupProduceService.insertDataForUseZTEReelId(listOfListForUseZTEReelId);

        List<SnRidSupProduce> snRidSupProduceList = new ArrayList<>();
        List<SalCSupProduce> salCSupProduceList = new ArrayList<>();
        List<BarcodeExpandVO> barcodeExpandVOList = new ArrayList<>();
        SnRidSupProduce snRidSupProduce1 = new SnRidSupProduce();
        SnRidSupProduce snRidSupProduce2 = new SnRidSupProduce();
        SnRidSupProduce snRidSupProduce3 = new SnRidSupProduce();
        snRidSupProduce1.setOutReelId("123415");
        snRidSupProduce1.setInFactoryId(BigDecimal.valueOf(12345));
        snRidSupProduce2.setOutReelId("412455");
        snRidSupProduce3.setOutReelId("634113");
        snRidSupProduceList.add(snRidSupProduce1);
        snRidSupProduceList.add(snRidSupProduce2);
        snRidSupProduceList.add(snRidSupProduce3);
        listOfListForUseZTEReelId.add(snRidSupProduceList);
        ZteMbcFactoryInfo zteMbcFactoryInfo = new ZteMbcFactoryInfo();
        zteMbcFactoryInfo.setIsUseZTEreelid("Y");
        PowerMockito.when(zteMbcFactoryInfoRepository.checkFactoryIsUseZTEReelId(any())).thenReturn(zteMbcFactoryInfo);
        PowerMockito.when(salCSupProduceRepository.checkReelIdInSalC(any())).thenReturn(salCSupProduceList);
        PowerMockito.when(barcodeCenterRemoteService.barcodeQuery(any(), any())).thenReturn(barcodeExpandVOList);
        try {
            snRidSupProduceService.insertDataForUseZTEReelId(listOfListForUseZTEReelId);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BARCODE_NOT_REGISTERED, e.getMessage());
        }

        SalCSupProduce salCSupProduce = new SalCSupProduce();
        salCSupProduce.setOutReelId("123415");
        salCSupProduceList.add(salCSupProduce);

        BarcodeExpandVO barcodeExpandVO = new BarcodeExpandVO();
        barcodeExpandVO.setBarcode("412455");
        barcodeExpandVOList.add(barcodeExpandVO);

        PowerMockito.when(salCSupProduceRepository.checkReelIdInSalC(any())).thenReturn(salCSupProduceList);
        PowerMockito.when(barcodeCenterRemoteService.barcodeQuery(any(), any())).thenReturn(barcodeExpandVOList);
        try {
            snRidSupProduceService.insertDataForUseZTEReelId(listOfListForUseZTEReelId);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.BARCODE_NOT_REGISTERED, e.getMessage());
        }
        BarcodeExpandVO barcodeExpandVO1 = new BarcodeExpandVO();
        barcodeExpandVO1.setBarcode("634113");
        barcodeExpandVOList.add(barcodeExpandVO1);
        snRidSupProduceService.insertDataForUseZTEReelId(listOfListForUseZTEReelId);
        SalCSupProduce salCSupProduce1 = new SalCSupProduce();
        salCSupProduce1.setOutReelId("634113");
        SalCSupProduce salCSupProduce2 = new SalCSupProduce();
        salCSupProduce2.setOutReelId("412455");
        salCSupProduceList.add(salCSupProduce1);
        salCSupProduceList.add(salCSupProduce2);
        PowerMockito.when(salCSupProduceRepository.checkReelIdInSalC(any())).thenReturn(salCSupProduceList);
        snRidSupProduceService.insertDataForUseZTEReelId(listOfListForUseZTEReelId);

    }
}