package com.zte.application.kafka.consumer;
/* Started by AICoder, pid:71fdfrc1ad1e65b140be0a9183d6be1fd45102a1 */

import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.PsTaskExtendedService;
import com.zte.application.PushStdModelConfirmationService;
import com.zte.application.kafka.component.KafkaProdListener;
import com.zte.application.sncabind.PsTaskService;
import com.zte.application.sncabind.TaskConfirmationService;
import com.zte.common.enums.ConfirmationStatusEnum;
import com.zte.common.utils.Constant;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.interfaces.dto.aps.EntityBindResultDTO;
import com.zte.itp.msa.message.SpringKafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RecordMetadata.class})
public class EntityBindMsgConsumerTest {

    @InjectMocks
    private EntityBindMsgConsumer consumer;

    @Mock
    private PsTaskService psTaskService;

    @Mock
    private SpringKafkaProducer springKafkaProducer;

    @Mock
    private TaskConfirmationService taskConfirmationService;

    @Mock
    private PsTaskExtendedService psTaskExtendedService;

    @Mock
    private TransactionTemplate transactionTemplate;
    @Mock
    private PushStdModelConfirmationService pushStdModelConfirmationService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(consumer, "excludedTypes", Collections.singletonList("FG_DISAS_2"));
    }

    @Test
    public void testProcess_whenValidInput_processesCorrectly() throws InterruptedException {
        // Arrange
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("stockOrgId", "1");
        jsonObject.put("entityNo", "task1");
        jsonObject.put("billNo", "bill1");
        jsonObject.put("messageType", Constant.OPERATION_CONFIRMATION);

        JSONArray jsonArray = new JSONArray();
        jsonArray.add(jsonObject);

        PsTask psTask = new PsTask();
        psTask.setTaskNo("task1");
        psTask.setOrgId(new BigDecimal(1));
        psTask.setType("SOME_TYPE");

        when(psTaskService.getPsTask(anyList())).thenReturn(Collections.singletonList(psTask));

        // Act
        consumer.process(jsonArray);

        // Assert
        verify(psTaskService).getPsTask(anyList());
        verify(taskConfirmationService).processInboundFlag(anyCollection());
    }

    @Test
    public void testHandleConfirmation_whenPsTaskIsNull_doesNothing() {
        // Arrange
        EntityBindResultDTO entityBindResultDTO = new EntityBindResultDTO();
        PsTask psTask = null;

        // Act
        ReflectionTestUtils.invokeMethod(consumer, "handleConfirmation", entityBindResultDTO, psTask);

        // Assert
        assertNull(entityBindResultDTO.getSuccess());
        assertNull(entityBindResultDTO.getResult());
    }

    @Test
    public void testHandleConfirmation_whenExcludedType_doesNothing() {
        // Arrange
        EntityBindResultDTO entityBindResultDTO = new EntityBindResultDTO();
        PsTask psTask = new PsTask();
        psTask.setType("FG_DISAS_2");

        // Act
        ReflectionTestUtils.invokeMethod(consumer, "handleConfirmation", entityBindResultDTO, psTask);

        // Assert
        assertNull(entityBindResultDTO.getSuccess());
        assertNull(entityBindResultDTO.getResult());
    }

    @Test
    public void testHandleConfirmation_whenPreCheckTrue_doesNothing() {
        // Arrange
        EntityBindResultDTO entityBindResultDTO = new EntityBindResultDTO();
        PsTask psTask = new PsTask();
        psTask.setType("SOME_TYPE");
        psTask.setConfirmationStatus(ConfirmationStatusEnum.CONFIRMING.getCode());

        // Act
        ReflectionTestUtils.invokeMethod(consumer, "handleConfirmation", entityBindResultDTO, psTask);

        // Assert
        assertNull(entityBindResultDTO.getSuccess());
        assertNull(entityBindResultDTO.getResult());
    }

    @Test
    public void testHandleConfirmation_whenExistInboundBarcode_updatesStatus() {
        // Arrange
        EntityBindResultDTO entityBindResultDTO = new EntityBindResultDTO();
        PsTask psTask = new PsTask();
        psTask.setType("SOME_TYPE");
        psTask.setConfirmationStatus(ConfirmationStatusEnum.PENDING_CONFIRMATION.getCode());
        psTask.setExistInboundBarcode(true);

        // Act
        ReflectionTestUtils.invokeMethod(consumer, "handleConfirmation", entityBindResultDTO, psTask);

        // Assert
        assertEquals(ConfirmationStatusEnum.PENDING_CONFIRMATION.getCode(), psTask.getConfirmationStatus());
        assertEquals(Constant.Y_STATUS, entityBindResultDTO.getSuccess());
        assertEquals(ConfirmationStatusEnum.PENDING_CONFIRMATION.getDesc(), entityBindResultDTO.getResult());
    }

    @Test
    public void testHandleConfirmation_whenNotExistsInboundBarcode_updatesStatus() {
        // Arrange
        EntityBindResultDTO entityBindResultDTO = new EntityBindResultDTO();
        PsTask psTask = new PsTask();
        psTask.setType("SOME_TYPE");
        psTask.setConfirmationStatus(ConfirmationStatusEnum.PENDING_CONFIRMATION.getCode());
        psTask.setExistInboundBarcode(false);

        // Act
        ReflectionTestUtils.invokeMethod(consumer, "handleConfirmation", entityBindResultDTO, psTask);

        // Assert
        assertEquals(ConfirmationStatusEnum.CONFIRMED.getCode(), psTask.getConfirmationStatus());
        assertEquals(Constant.Y_STATUS, entityBindResultDTO.getSuccess());
        assertEquals(ConfirmationStatusEnum.CONFIRMED.getDesc(), entityBindResultDTO.getResult());
    }

    @Test
    public void testHandleCancellation_whenPsTaskIsNull_updatesEntityResult() {
        // Arrange
        EntityBindResultDTO entityBindResultDTO = new EntityBindResultDTO();
        PsTask psTask = null;

        // Act
        ReflectionTestUtils.invokeMethod(consumer, "handleCancellation", entityBindResultDTO, psTask);

        // Assert
        assertEquals(Constant.Y_STATUS, entityBindResultDTO.getSuccess());
        assertEquals(Constant.TIP_APS_NO_TASK, entityBindResultDTO.getResult());
    }

    @Test
    public void testHandleCancellation_whenPreCheckTrue_updatesEntityResult() {
        // Arrange
        EntityBindResultDTO entityBindResultDTO = new EntityBindResultDTO();
        PsTask psTask = new PsTask();
        psTask.setTaskNo("task1");
        psTask.setConfirmationStatus(ConfirmationStatusEnum.CONFIRMING.getCode());

        // Act
        ReflectionTestUtils.invokeMethod(consumer, "handleCancellation", entityBindResultDTO, psTask);

        // Assert
        assertEquals(Constant.N_STATUS, entityBindResultDTO.getSuccess());
        assertEquals(String.format(Constant.TIP_APS_CANCELLATION, "task1"), entityBindResultDTO.getResult());
    }

    @Test
    public void testHandleCancellation_whenValidUpdatesStatus() {
        // Arrange
        EntityBindResultDTO entityBindResultDTO = new EntityBindResultDTO();
        PsTask psTask = new PsTask();
        psTask.setTaskNo("task1");
        psTask.setConfirmationStatus(ConfirmationStatusEnum.PENDING_CONFIRMATION.getCode());

        // Act
        ReflectionTestUtils.invokeMethod(consumer, "handleCancellation", entityBindResultDTO, psTask);

        // Assert
        assertEquals(ConfirmationStatusEnum.CANCELLED.getCode(), psTask.getConfirmationStatus());
        assertEquals(Constant.Y_STATUS, entityBindResultDTO.getSuccess());
        assertEquals(ConfirmationStatusEnum.CANCELLED.getDesc(), entityBindResultDTO.getResult());
    }

    @Test
    public void testUpdateEntityAndPsTask_updatesStatus() {
        // Arrange
        EntityBindResultDTO entityBindResultDTO = new EntityBindResultDTO();
        PsTask psTask = new PsTask();
        ConfirmationStatusEnum status = ConfirmationStatusEnum.CONFIRMED;

        // Act
        ReflectionTestUtils.invokeMethod(consumer, "updateEntityAndPsTask", entityBindResultDTO, psTask, status);

        // Assert
        assertEquals(status.getCode(), psTask.getConfirmationStatus());
        assertEquals(Constant.Y_STATUS, entityBindResultDTO.getSuccess());
        assertEquals(status.getDesc(), entityBindResultDTO.getResult());
    }

    @Test
    public void testUpdateEntityResult_updatesEntityResult() {
        // Arrange
        EntityBindResultDTO entityBindResultDTO = new EntityBindResultDTO();
        String success = Constant.Y_STATUS;
        String result = "some result";

        // Act
        ReflectionTestUtils.invokeMethod(consumer, "updateEntityResult", entityBindResultDTO, success, result);

        // Assert
        assertEquals(success, entityBindResultDTO.getSuccess());
        assertEquals(result, entityBindResultDTO.getResult());
    }

    @Test
    public void testPreCheck_returnsCorrectResult() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setConfirmationStatus(ConfirmationStatusEnum.CONFIRMING.getCode());

        // Act
        boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(consumer, "preCheck", psTask));

        // Assert
        assertTrue(result);
    }

    @Test
    public void testGetPsTaskMap_returnsExpectedMap() {
        // Arrange
        EntityBindResultDTO dto1 = new EntityBindResultDTO();
        dto1.setTaskNo("task1");

        List<EntityBindResultDTO> entityBindResultDTOList = Collections.singletonList(dto1);

        PsTask psTask1 = new PsTask();
        psTask1.setTaskId("1");
        psTask1.setTaskNo("task1");
        psTask1.setOrgId(new BigDecimal(1));
        psTask1.setFactoryId(new BigDecimal(1));
        psTask1.setConfirmationStatus(ConfirmationStatusEnum.PENDING_CONFIRMATION.getCode());

        when(psTaskService.getPsTask(anyList())).thenReturn(Collections.singletonList(psTask1));

        // Act
        Map<String, PsTask> result = ReflectionTestUtils.invokeMethod(consumer, "getPsTaskMap", entityBindResultDTOList);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("task1"));
        assertEquals("1", Convert.toStr(result.get("task1").getOrgId()));
    }

    @Test
    public void testConvert_returnsExpectedList() {
        // Arrange
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("stockOrgId", "1");
        jsonObject.put("entityNo", "task1");
        jsonObject.put("billNo", "bill1");
        jsonObject.put("messageType", Constant.OPERATION_CONFIRMATION);

        JSONArray jsonArray = new JSONArray();
        jsonArray.add(jsonObject);

        // Act
        List<EntityBindResultDTO> result = ReflectionTestUtils.invokeMethod(consumer, "convert", jsonArray);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("task1", result.get(0).getTaskNo());
        assertEquals("1", Convert.toStr(result.get(0).getOrgId()));
        assertEquals("bill1", result.get(0).getBillNo());
        assertEquals(Constant.OPERATION_CONFIRMATION, result.get(0).getOperation());
    }

    /* Started by AICoder, pid:80e81721b0g4714145350a5c20c83f47ce80ec14 */
    @Test
    public void testGetProducerListener_onSuccess() {
        // Arrange
        CompletableFuture<Void> sendFuture = new CompletableFuture<>();
        ProducerRecord<String, String> producerRecord = new ProducerRecord<>("topic", "key", "value");
        RecordMetadata recordMetadata = PowerMockito.mock(RecordMetadata.class);

        KafkaProdListener listener = ReflectionTestUtils.invokeMethod(consumer, "getProducerListener", sendFuture);

        // Act
        Objects.requireNonNull(listener).onSuccess(producerRecord, recordMetadata);

        // Assert
        assertTrue(sendFuture.isDone());
        assertNull(sendFuture.join());
    }

    @Test
    public void testGetProducerListener_onError() {
        // Arrange
        CompletableFuture<Void> sendFuture = new CompletableFuture<>();
        ProducerRecord<String, String> producerRecord = new ProducerRecord<>("topic", "key", "value");
        RecordMetadata recordMetadata = PowerMockito.mock(RecordMetadata.class);
        Exception exception = new RuntimeException("Kafka error");

        KafkaProdListener listener = ReflectionTestUtils.invokeMethod(consumer, "getProducerListener", sendFuture);

        // Act
        Objects.requireNonNull(listener).onError(producerRecord, recordMetadata, exception);

        // Assert
        assertTrue(sendFuture.isCompletedExceptionally());
        try {
            sendFuture.get();
            fail("Expected exception to be thrown");
        } catch (Exception e) {
            assertEquals(exception.getClass(), e.getCause().getClass());
            assertEquals(exception.getMessage(), e.getCause().getMessage());
        }
    }
    /* Ended by AICoder, pid:80e81721b0g4714145350a5c20c83f47ce80ec14 */

    @Test
    public void testUpdateIfChange_whenValidInput_updatesCorrectly() {
        // Arrange
        EntityBindResultDTO dto1 = new EntityBindResultDTO();
        dto1.setTaskNo("task1");
        dto1.setSuccess(Constant.FLAG_Y);
        dto1.setBillNo("bill1");

        EntityBindResultDTO dto2 = new EntityBindResultDTO();
        dto2.setTaskNo("task2");
        dto2.setSuccess(Constant.FLAG_N);

        List<EntityBindResultDTO> entityBindResultDTOList = Arrays.asList(dto1, dto2);

        PsTask psTask1 = new PsTask();
        psTask1.setTaskNo("task1");
        psTask1.setConfirmationStatus(ConfirmationStatusEnum.CONFIRMED.getCode());

        Map<String, PsTask> psTaskMap = MapUtil.of("task1", psTask1);

        // Act
        ReflectionTestUtils.invokeMethod(consumer, "updateIfChange", entityBindResultDTOList, psTaskMap);

        // Assert
        verify(psTaskExtendedService).batchUpdateByTaskNo(any());
        verify(pushStdModelConfirmationService).saveAndPushConfirmation(eq(psTask1));
    }

    @Test
    public void testUpdateIfChange_whenEmptyList_doesNothing() {
        // Arrange
        List<EntityBindResultDTO> entityBindResultDTOList = Collections.emptyList();
        Map<String, PsTask> psTaskMap = Collections.emptyMap();

        // Act
        ReflectionTestUtils.invokeMethod(consumer, "updateIfChange", entityBindResultDTOList, psTaskMap);

        // Assert
        verify(psTaskExtendedService).batchUpdateByTaskNo(any());
        verify(pushStdModelConfirmationService, never()).saveAndPushConfirmation(any());
    }

    @Test
    public void testSaveAndPushConfirmation_whenValidInput_savesAndPushes() {
        // Arrange
        PsTask psTask1 = new PsTask();
        psTask1.setTaskNo("task1");
        psTask1.setConfirmationStatus(ConfirmationStatusEnum.CONFIRMED.getCode());

        PsTask psTask2 = new PsTask();
        psTask2.setTaskNo("task2");
        psTask2.setConfirmationStatus(ConfirmationStatusEnum.PENDING_CONFIRMATION.getCode());

        List<PsTask> psTaskList = Arrays.asList(psTask1, psTask2);

        // Act
        ReflectionTestUtils.invokeMethod(consumer, "saveAndPushConfirmation", psTaskList);

        // Assert
        verify(pushStdModelConfirmationService).saveAndPushConfirmation(eq(psTask1));
        verify(pushStdModelConfirmationService, never()).saveAndPushConfirmation(eq(psTask2));
    }

    @Test
    public void testUpdatePsTaskExtended_whenValidInput_updatesExtended() {
        // Arrange
        EntityBindResultDTO dto1 = new EntityBindResultDTO();
        dto1.setTaskNo("task1");
        dto1.setBillNo("bill1");

        EntityBindResultDTO dto2 = new EntityBindResultDTO();
        dto2.setTaskNo("task2");
        dto2.setBillNo("bill2");

        List<EntityBindResultDTO> entityBindResultDTOList = Arrays.asList(dto1, dto2);

        PsTask psTask1 = new PsTask();
        psTask1.setTaskNo("task1");

        PsTask psTask2 = new PsTask();
        psTask2.setTaskNo("task2");

        List<PsTask> psTaskList = Arrays.asList(psTask1, psTask2);


        // Act
        ReflectionTestUtils.invokeMethod(consumer, "updatePsTaskExtended", entityBindResultDTOList, psTaskList);

        // Assert
        verify(psTaskExtendedService).batchUpdateByTaskNo(any());
    }
}

/* Ended by AICoder, pid:71fdfrc1ad1e65b140be0a9183d6be1fd45102a1 */