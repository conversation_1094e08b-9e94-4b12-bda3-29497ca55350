package com.zte.application.kafka.consumer;

import com.alibaba.fastjson2.JSON;
import com.zte.domain.model.RequestSnapshotInfoRepository;
import com.zte.interfaces.dto.RequestSnapshotInfo;
import com.zte.springbootframe.common.model.MsgData;
import com.zte.springbootframe.util.JsonConvertUtil;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * ClassName: SnapshotLogConsumerTest
 * Description:
 *
 * <AUTHOR>
 * @date 2024/3/25 上午9:12
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({JsonConvertUtil.class})
public class SnapshotLogConsumerTest {
    @InjectMocks
    private SnapshotLogConsumer consumer;
    @Mock
    private RequestSnapshotInfoRepository repository;
    @Test
    public void consumeTest() {
        MsgData<List<RequestSnapshotInfo>> msgData = new MsgData<>();
        consumer.consume(null);
        Assert.assertTrue(1==1);
        consumer.consume(JSON.toJSONString(msgData));
        Assert.assertTrue(1==1);
        List<RequestSnapshotInfo> insertList = new ArrayList<>();
        msgData.setData(insertList);
        RequestSnapshotInfo info = new RequestSnapshotInfo();
        info.setServletPath("test");
        insertList.add(info);
        consumer.consume(JSON.toJSONString(msgData));
        Assert.assertTrue(1==1);
    }
}
