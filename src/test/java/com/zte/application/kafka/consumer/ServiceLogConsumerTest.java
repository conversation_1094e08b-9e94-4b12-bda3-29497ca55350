package com.zte.application.kafka.consumer;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.ServiceLogService;
import com.zte.domain.model.ServiceLog;
import com.zte.springbootframe.common.model.MsgData;
import com.zte.springbootframe.util.JsonConvertUtil;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({JsonConvertUtil.class})
public class ServiceLogConsumerTest extends TestCase {
    @InjectMocks
    private ServiceLogConsumer serviceLogConsumer;
    @Mock
    private ServiceLogService serviceLogService;

    @Test
    public void consume () throws Exception {
        MsgData<ServiceLog> msgData = new MsgData<>();
        ServiceLog serviceLog = new ServiceLog();
        String factoryId = "52";
        serviceLog.setFactoryId(factoryId);
        serviceLog.setServiceName("centerfactory");
        serviceLog.setRequestUrl("http://aaa.com");
        serviceLog.setReqDate(new Date());
        msgData.setData(serviceLog);
        msgData.setFactoryId(factoryId);
        msgData.setOperationMode(1);

        List<String> records = new ArrayList<>();
        records.add(JSONObject.toJSONString(msgData));
        serviceLogConsumer.consume(records);
        Assert.assertNotNull(records);
    }
}
