package com.zte.application.kafka.consumer;
/* Started by AICoder, pid:r2408c18cdm65a314e800bde51a88a3ea9054efb */

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zte.aiop.dtems.AlarmHelper;
import com.zte.aiop.dtems.cache.impl.AlarmObjectCache;
import com.zte.aiop.dtems.service.impl.AlarmServiceImpl;
import com.zte.aiop.dtems.utils.InitUtils;
import com.zte.application.CustomerItemsService;
import com.zte.application.SysLookupValuesService;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.NoticeCenterService;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.NoticeCenter.IcenterInfo;
import com.zte.interfaces.dto.NoticeCenter.NoticeCenterMsgDto;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({AlarmHelper.class, AlarmServiceImpl.class, InitUtils.class, AlarmObjectCache.class})
public class CustomerItemMsgConsumerTest {

    @Mock
    private CustomerItemsService customerItemsService;
    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private NoticeCenterService noticeCenterService;

    @InjectMocks
    private CustomerItemMsgConsumer consumer;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(consumer, "alarmMetric", "imes_req_monitor_alarm");
        ReflectionTestUtils.setField(consumer, "alarmKey", "1001");
        ReflectionTestUtils.setField(consumer, "iCenterTemplateId", "W2025041815310150458");
    }

    @Test
    public void testConsume() throws Exception {
        String msg = "[{\"itemNo\":\"1\",\"itemName\":\"name1\",\"customerType\":\"type1\",\"customerCode\":\"code1\",\"customerItemNo\":\"citem1\",\"isValid\":\"Y\"}]";
        consumer.consume(msg);

        verify(customerItemsService, atLeastOnce()).queryListByZteCodes(anyList());
        verify(customerItemsService, atLeastOnce()).batchInsert(anyList());
    }

    @Test
    public void testPerformSave() throws Exception {
        List<CustomerItemsDTO> saveList = new ArrayList<>();
        saveList.add(new CustomerItemsDTO());

        ReflectionTestUtils.invokeMethod(consumer, "performSave", saveList);

        verify(customerItemsService, times(1)).batchInsert(saveList);
//        verify(noticeCenterService, atLeastOnce()).sendMsg(any());
    }

    @Test
    public void testGetSourceList() throws Exception {
        JSONArray jsonArray = new JSONArray();
        JSONObject item = new JSONObject();
        jsonArray.add(item);

        List<CustomerItemsDTO> result = ReflectionTestUtils.invokeMethod(consumer, "getSourceList", jsonArray);

        assert result.size() == 1;
    }

    @Test
    public void testPerformRemove() throws Exception {
        JSONArray jsonArray = new JSONArray();
        JSONObject customerItemMsg = new JSONObject();
        customerItemMsg.put("isValid", "N");
        jsonArray.add(customerItemMsg);

        ReflectionTestUtils.invokeMethod(consumer, "performRemove", jsonArray);

        verify(customerItemsService, times(1)).deleteCustomerItemsByZteCodes(anyList());
    }

    @Test
    public void testPerformUpdate() throws Exception {
        List<CustomerItemsDTO> sourceList = new ArrayList<>();
        CustomerItemsDTO source = new CustomerItemsDTO();
        source.setZteCode("1");
        sourceList.add(source);

        List<CustomerItemsDTO> targetList = new ArrayList<>();
        CustomerItemsDTO target = new CustomerItemsDTO();
        target.setZteCode("1");
        targetList.add(target);

        when(customerItemsService.queryListByZteCodes(anyList())).thenReturn(targetList);

        ReflectionTestUtils.invokeMethod(consumer, "performUpdate", sourceList);

        verify(customerItemsService, times(1)).batchUpdateByZteCode(anyList());
    }

    @Test
    public void testUpdate() throws Exception {
        List<CustomerItemsDTO> updateList = new ArrayList<>();
        updateList.add(new CustomerItemsDTO());

        ReflectionTestUtils.invokeMethod(consumer, "update", updateList);

        verify(customerItemsService, times(1)).batchUpdateByZteCode(updateList);
    }

    @Test
    public void testConvert() throws Exception {
        JSONObject customerItemMsg = new JSONObject();
        CustomerItemsDTO result = (CustomerItemsDTO) ReflectionTestUtils.invokeMethod(consumer, "convert", customerItemMsg);

        assert result != null;
    }

    @Test
    public void testKeyFieldConvert() throws Exception {
        JSONObject customerItemMsg = new JSONObject();
        customerItemMsg.put("customerType", "value");

        Map<String, Object> result = ReflectionTestUtils.invokeMethod(consumer, "keyFieldConvert", customerItemMsg);

        assert result != null;
    }

    @Test
    public void testAlarm() throws Exception {
        ReflectionTestUtils.invokeMethod(consumer, "alarm", "title", "info");
        assert consumer != null;
    }

    @Test
    public void testSendNotice() throws Exception {
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        customerItemsDTOList.add(new CustomerItemsDTO());
        String[] noticeTos = {"user1"};
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("xx");
        when(sysLookupValuesService.selectValuesByType(anyInt())).thenReturn(Collections.singletonList(sysLookupValues));
        when(sysLookupValuesService.getValueMappingByType(anyInt(), any())).thenReturn(Collections.singletonMap("key", "value"));

//        ReflectionTestUtils.setField(consumer, "noticeToSupplier", () -> noticeTos);

        ReflectionTestUtils.invokeMethod(consumer, "sendNotice", customerItemsDTOList);

        verify(noticeCenterService, times(1)).sendMsg(any());
    }

    @Test
    public void testCreateNoticeCenterMsgDto() throws Exception {
        List<CustomerItemsDTO> customerItemsDTOList = new ArrayList<>();
        customerItemsDTOList.add(new CustomerItemsDTO());
        String[] noticeTos = {"user1"};

        NoticeCenterMsgDto<IcenterInfo> result = ReflectionTestUtils.invokeMethod(consumer, "createNoticeCenterMsgDto", customerItemsDTOList, noticeTos);

        assert result != null;
    }

    @Test
    public void testSave() throws Exception {
        List<CustomerItemsDTO> saveList = new ArrayList<>();
        saveList.add(new CustomerItemsDTO());

        ReflectionTestUtils.invokeMethod(consumer, "save", saveList);

        verify(customerItemsService, times(1)).batchInsert(saveList);
    }

    @Test
    public void testRemove() throws Exception {
        List<String> removeList = new ArrayList<>();
        removeList.add("1");

        ReflectionTestUtils.invokeMethod(consumer, "remove", removeList);

        verify(customerItemsService, times(1)).deleteCustomerItemsByZteCodes(removeList);
    }

    @Test
    public void testShouldRemove() throws Exception {
        JSONObject customerItemMsg = new JSONObject();
        customerItemMsg.put("isValid", "N");

        boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(consumer, "shouldRemove", customerItemMsg));

        assert result;
    }

    @Test
    public void testParse() throws Exception {
        String msg = "[{\"itemNo\":\"1\"}]";
        JSONArray result = (JSONArray) ReflectionTestUtils.invokeMethod(consumer, "parse", msg);

        assert result.size() == 1;
    }

    @Test
    public void testGetNoticeTos() throws Exception {
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("xx");
        when(sysLookupValuesService.selectValuesByType(anyInt())).thenReturn(Collections.singletonList(sysLookupValues));

        String[] result = (String[]) ReflectionTestUtils.invokeMethod(consumer, "getNoticeTos", 1);

        assert result != null;
    }

}

/* Ended by AICoder, pid:r2408c18cdm65a314e800bde51a88a3ea9054efb */