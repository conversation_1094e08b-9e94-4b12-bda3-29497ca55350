package com.zte.application.kafka.consumer;
/* Started by AICoder, pid:tb3f1oa7370d2f5141eb097d21c5c662da782776 */

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zte.aiop.dtems.AlarmHelper;
import com.zte.application.PsTaskExtendedService;
import com.zte.application.PushStdModelConfirmationService;
import com.zte.application.PushStdModelDataService;
import com.zte.application.sncabind.PsTaskService;
import com.zte.common.enums.SceneCodeEnum;
import com.zte.common.utils.Constant;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.infrastructure.remote.PlanScheduleRemoteService;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.PushStdModelConfirmationDTO;
import com.zte.interfaces.dto.PushStdModelDataDTO;
import com.zte.interfaces.dto.aps.EntityQueryDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiFunction;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({EntityModifyMsgConsumer.class, AlarmHelper.class})
public class EntityModifyMsgConsumerTest {

    @InjectMocks
    private EntityModifyMsgConsumer consumer;

    @Mock
    private PsTaskService psTaskService;

    @Mock
    private PlanScheduleRemoteService planScheduleRemoteService;

    @Mock
    private PushStdModelDataService pushStdModelDataService;

    @Mock
    private TransactionTemplate transactionTemplate;

    @Mock
    private PsTaskExtendedService psTaskExtendedService;

    @Mock
    private PushStdModelConfirmationService pushStdModelConfirmationService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testProcess_whenValidInput_updatesAndExecutesTransaction() throws Exception {
        // Arrange
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("entityNo", "task1");
        jsonObject.put("stockOrgId", "1");
        jsonObject.put("qty", 100);
        jsonArray.add(jsonObject);

        EntityQueryDTO dto = new EntityQueryDTO();
        dto.setEntityNo("task1");
        dto.setStockOrgId(1);
        dto.setQty(100L);

        PsTask psTask = new PsTask();
        psTask.setTaskNo("task1");
        psTask.setOrgId(new BigDecimal(1));

        when(psTaskService.getPsTask(anyList())).thenReturn(Collections.singletonList(psTask));

        // Act
        consumer.process(jsonArray);

        // Assert
        verify(transactionTemplate).execute(any());
    }

    @Test
    public void testUpdateIfChange_whenValidInput_updatesTasks() {
        // Arrange
        PsTask psTask1 = new PsTask();
        psTask1.setTaskNo("task1");
        psTask1.setTaskQty(new BigDecimal(100L));
        psTask1.setFactoryId(new BigDecimal(1));

        PsTask psTask2 = new PsTask();
        psTask2.setTaskNo("task2");
        psTask2.setTaskQty(null);
        psTask2.setFactoryId(new BigDecimal(Constant.FACTORY_ID_CENTER));

        Collection<PsTask> psTasks = Arrays.asList(psTask1, psTask2);

        // Act
        ReflectionTestUtils.invokeMethod(consumer, "updateIfChange", psTasks);

        // Assert
        verify(psTaskService).batchUpdateByPK(anyList());
        verify(planScheduleRemoteService).updateQtyByTaskNo(anyList());
        verify(pushStdModelDataService).batchUpdate(anyList());
    }

    @Test
    public void testGetPsTaskMap_returnsExpectedMap() {
        // Arrange
        EntityQueryDTO dto = new EntityQueryDTO();
        dto.setEntityNo("task1");
        dto.setStockOrgId(1);

        List<EntityQueryDTO> entityQueryDTOList = Collections.singletonList(dto);

        PsTask psTask = new PsTask();
        psTask.setTaskNo("task1");
        psTask.setOrgId(new BigDecimal(1));

        when(psTaskService.getPsTask(anyList())).thenReturn(Collections.singletonList(psTask));

        // Act
        Map<String, PsTask> result = ReflectionTestUtils.invokeMethod(consumer, "getPsTaskMap", entityQueryDTOList);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey("task1"));
        assertEquals("1", Convert.toStr(result.get("task1").getOrgId()));
    }

    @Test
    public void testConvert_returnsExpectedList() {
        // Arrange
        JSONArray jsonArray = new JSONArray();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("entityNo", "task1");
        jsonObject.put("stockOrgId", "1");
        jsonObject.put("qty", 100);
        jsonArray.add(jsonObject);

        // Act
        List<EntityQueryDTO> result = ReflectionTestUtils.invokeMethod(consumer, "convert", jsonArray);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("task1", result.get(0).getEntityNo());
        assertEquals("1", Convert.toStr(result.get(0).getStockOrgId()));
        assertEquals(100L, result.get(0).getQty().longValue());
    }

    @Test
    public void testUpdateIfExistAlibabaTask_withValidTasks_callsAllMethods() throws Exception {
        PsTask psTask1 = new PsTask();
        psTask1.setItemNo("ITEM1");
        PsTask psTask2 = new PsTask();
        psTask2.setItemNo("ITEM2");
        List<PsTask> tasks = Arrays.asList(psTask1, psTask2);
        Map<String, Boolean> alibabaMap = new HashMap<>();
        alibabaMap.put("ITEM1", true);
        alibabaMap.put("ITEM2", false);

        when(psTaskService.judgeAlibabaTask(any())).thenReturn(alibabaMap);

        PushStdModelDataDTO dto = new PushStdModelDataDTO();
        dto.setTaskNo("TASK1");
        List<PushStdModelDataDTO> dtoList = Collections.singletonList(dto);
        consumer = PowerMockito.spy(consumer);
        PowerMockito.doReturn(dtoList).when(consumer, "getPushStdModelDataDTOList", any());

        PsTaskExtendedDTO extendedDTO = new PsTaskExtendedDTO();
        extendedDTO.setTaskNo("TASK1");
        when(psTaskExtendedService.listByTaskNos(any())).thenReturn(Collections.singletonList(extendedDTO));

        ReflectionTestUtils.invokeMethod(consumer, "updateIfExistAlibabaTask", tasks);

        verify(psTaskService).judgeAlibabaTask(any());
        verify(pushStdModelDataService).batchUpdate(any());
    }

    // ----------------------------
    // 私有方法测试：pushSchedulingInfo
    // ----------------------------

    @Test
    public void testPushSchedulingInfo_withNullExtendedDTO_logsWarning() throws Exception {
        PushStdModelDataDTO dto = new PushStdModelDataDTO();
        dto.setTaskNo("TASK1");
        List<PushStdModelDataDTO> dataDTOs = Collections.singletonList(dto);
        Map<String, PsTaskExtendedDTO> emptyMap = new HashMap<>();

        ReflectionTestUtils.invokeMethod(consumer, "pushSchedulingInfo", dataDTOs, emptyMap);

        assertTrue(CollUtil.isNotEmpty(dataDTOs));
    }

    @Test
    public void testPushSchedulingInfo_withNullExtendedDTO_logsWarning2() throws Exception {
        PushStdModelDataDTO dto = new PushStdModelDataDTO();
        dto.setTaskNo("TASK1");
        List<PushStdModelDataDTO> dataDTOs = Collections.singletonList(dto);
        PsTaskExtendedDTO extendedDTO = new PsTaskExtendedDTO();
        extendedDTO.setTaskNo("TASK1");

        Map<String, PsTaskExtendedDTO> map = Collections.singletonMap("TASK1", extendedDTO);

        BiFunction<PushStdModelDataDTO, PsTaskExtendedDTO, Boolean> mockFunction = mock(BiFunction.class);
        when(mockFunction.apply(any(), any())).thenReturn(true);
        consumer = PowerMockito.spy(consumer);
        PowerMockito.doReturn(mockFunction).when(consumer, "getPushFunction", any());
        ReflectionTestUtils.invokeMethod(consumer, "pushSchedulingInfo", dataDTOs, map);

        assertTrue(CollUtil.isNotEmpty(dataDTOs));
    }

    @Test
    public void testPushSchedulingInfo_whenPushFunctionThrowsException_logsErrorAndTriggersAlarm() throws Exception {
        PushStdModelDataDTO dto = new PushStdModelDataDTO();
        dto.setTaskNo("TASK1");
        dto.setErrorMsg("网络错误");

        PsTaskExtendedDTO extendedDTO = new PsTaskExtendedDTO();
        extendedDTO.setTaskNo("TASK1");

        Map<String, PsTaskExtendedDTO> map = Collections.singletonMap("TASK1", extendedDTO);

        BiFunction<PushStdModelDataDTO, PsTaskExtendedDTO, Boolean> mockFunction = mock(BiFunction.class);
        when(mockFunction.apply(any(), any())).thenThrow(new RuntimeException("推送失败"));
        consumer = PowerMockito.spy(consumer);
        PowerMockito.doReturn(mockFunction).when(consumer, "getPushFunction", any());

        PowerMockito.mockStatic(AlarmHelper.class);
        PowerMockito.doNothing().when(AlarmHelper.class, "alarm", anyString(), anyString(), any(), anyString(), anyString());

        try {
            ReflectionTestUtils.invokeMethod(consumer, "pushSchedulingInfo", Collections.singletonList(dto), map);
//            fail("Expected exception not thrown");
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof RuntimeException);
        }

        PowerMockito.verifyStatic(AlarmHelper.class, times(1));
        AlarmHelper.alarm(anyString(), anyString(), any(), anyString(), anyString());
    }

    // ----------------------------
    // 私有方法测试：getPushFunction
    // ----------------------------

    @Test
    public void testGetPushFunction_withBufferTaskAndConfirmations_returnsFormalPushFunction() throws Exception {
        PsTaskExtendedDTO dto = new PsTaskExtendedDTO();
        dto.setTaskNo("TASK1");
        dto.setTaskType("BUFFER_TASK");

        PushStdModelConfirmationDTO confirm = new PushStdModelConfirmationDTO();
        confirm.setPushStatus(Constant.PUSH_STATUS.PUSHED_NOT_CALLBACK);

        when(pushStdModelConfirmationService.getList(any())).thenReturn(Collections.singletonList(confirm));

        BiFunction<PushStdModelDataDTO, PsTaskExtendedDTO, Boolean> result = ReflectionTestUtils.invokeMethod(consumer, "getPushFunction", dto);

        assertNotNull(result);
        PowerMockito.doReturn(true).when(pushStdModelDataService, "pushSchedulingInfo", any(), any(), any(SceneCodeEnum.class));
        PushStdModelDataDTO dataDTO = new PushStdModelDataDTO();
        dataDTO.setTaskNo("TASK1");
        Boolean success = result.apply(dataDTO, dto);
        assertFalse(success);
    }

    @Test
    public void testGetPushFunction_withNonBufferTask_returnsDefaultPushFunction() throws Exception {
        PsTaskExtendedDTO dto = new PsTaskExtendedDTO();
        dto.setTaskNo("TASK1");
        dto.setTaskType("NORMAL_TASK");

        BiFunction<PushStdModelDataDTO, PsTaskExtendedDTO, Boolean> result = ReflectionTestUtils.invokeMethod(consumer, "getPushFunction", dto);

        assertNotNull(result);
        PowerMockito.doReturn(true).when(pushStdModelDataService, "pushSchedulingInfo", any(), any(Map.class));
        PushStdModelDataDTO dataDTO = new PushStdModelDataDTO();
        dataDTO.setTaskNo("TASK1");
        Boolean success = result.apply(dataDTO, dto);
        assertTrue(success);
    }

    @Test
    public void testGetPushFunction_withNonBufferTask_returnsDefaultPushFunction2() throws Exception {
        PsTaskExtendedDTO dto = new PsTaskExtendedDTO();
        dto.setTaskNo("TASK1");
        dto.setTaskType("2");

        BiFunction<PushStdModelDataDTO, PsTaskExtendedDTO, Boolean> result = ReflectionTestUtils.invokeMethod(consumer, "getPushFunction", dto);

        assertNotNull(result);
        PowerMockito.doReturn(true).when(pushStdModelDataService, "pushSchedulingInfo", any(), any(Map.class));
        PushStdModelDataDTO dataDTO = new PushStdModelDataDTO();
        dataDTO.setTaskNo("TASK1");
        Boolean success = result.apply(dataDTO, dto);
        assertTrue(success);
    }

    @Test
    public void testGetPushFunction_withNonBufferTask_returnsDefaultPushFunction3() throws Exception {
        PsTaskExtendedDTO dto = new PsTaskExtendedDTO();
        dto.setTaskNo("TASK1");
        dto.setTaskType("2");
        PushStdModelConfirmationDTO confirmationDTO = new PushStdModelConfirmationDTO();
        confirmationDTO.setPushStatus(Constant.PUSH_STATUS.PUSHED_NOT_CALLBACK);
        when(pushStdModelConfirmationService.getList(any(PushStdModelConfirmationDTO.class))).thenReturn(Collections.singletonList(confirmationDTO));
        BiFunction<PushStdModelDataDTO, PsTaskExtendedDTO, Boolean> result = ReflectionTestUtils.invokeMethod(consumer, "getPushFunction", dto);

        assertNotNull(result);
        PowerMockito.doReturn(true).when(pushStdModelDataService, "pushSchedulingInfo", any(), any(Map.class));
        PushStdModelDataDTO dataDTO = new PushStdModelDataDTO();
        dataDTO.setTaskNo("TASK1");
        Boolean success = result.apply(dataDTO, dto);
        assertFalse(success);
    }

    // ----------------------------
    // 异常处理测试
    // ----------------------------

    @Test
    public void testGetPushFunction_whenPushFails_throwsRuntimeException() throws Exception {
        PsTaskExtendedDTO dto = new PsTaskExtendedDTO();
        dto.setTaskNo("TASK1");
        dto.setTaskType("NORMAL_TASK");

        BiFunction<PushStdModelDataDTO, PsTaskExtendedDTO, Boolean> result = ReflectionTestUtils.invokeMethod(consumer, "getPushFunction", dto);

        PowerMockito.doThrow(new Exception("推送失败")).when(pushStdModelDataService, "pushSchedulingInfo", any(), any(Map.class));

        PushStdModelDataDTO dataDTO = new PushStdModelDataDTO();
        dataDTO.setTaskNo("TASK1");

        try {
            Objects.requireNonNull(result).apply(dataDTO, dto);
            fail("Expected RuntimeException was not thrown");
        } catch (Exception e) {
            assertTrue(e instanceof RuntimeException);
            assertEquals("推送失败", e.getCause().getMessage());
        }
    }

    // ----------------------------
    // 边界值测试
    // ----------------------------

    @Test
    public void testUpdateIfExistAlibabaTask_withEmptyList_doesNotCallAnyService() throws Exception {
        ReflectionTestUtils.invokeMethod(consumer, "updateIfExistAlibabaTask", Collections.emptyList());
        verify(psTaskService).judgeAlibabaTask(anyCollection());
        verify(pushStdModelDataService).batchUpdate(anyList());
    }
}

/* Ended by AICoder, pid:tb3f1oa7370d2f5141eb097d21c5c662da782776 */