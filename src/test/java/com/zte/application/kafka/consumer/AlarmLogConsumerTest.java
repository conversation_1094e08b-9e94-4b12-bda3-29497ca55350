package com.zte.application.kafka.consumer;

import com.zte.application.AlarmLogService;
import com.zte.infrastructure.remote.ICenterRemoteService;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;

import static org.mockito.Matchers.any;
import static org.mockito.Mockito.doNothing;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2023/1/31 19:18
 * @Description
 */
public class AlarmLogConsumerTest {
    @Mock
    AlarmLogService alarmLogService;
    @Mock
    ICenterRemoteService iCenterRemoteService;
    @InjectMocks
    AlarmLogConsumer alarmLogConsumer;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        FieldUtils.writeField(alarmLogConsumer, "alarmEmpNos", "10328274", true);
        FieldUtils.writeField(alarmLogConsumer, "alarmType", "sql", true);
    }

    @Test
    public void testBatchConsume() throws Exception {
        doNothing().when(alarmLogService).addBatch(any());
        doNothing().when(iCenterRemoteService).sendMessage(any(), any(), any());
        try{
            alarmLogConsumer.batchConsume(Arrays.<String>asList("{\"operationMode\":1,\"data\":{\"serviceIp\":\"************\",\"headers\":\"{\\\"x-emp-no\\\":\\\"10328274\\\",\\\"X-Link-Id\\\":\\\"e297316e-2ffc-4543-a5e7-17471af1f129\\\",\\\"x-factory-id\\\":\\\"51\\\",\\\"x-auth-value\\\":\\\"XXX\\\"}\",\"factoryId\":51,\"appearDate\":1675164260368,\"empNo\":\"10328274\",\"id\":\"6d7e1ce0-6bef-4c32-b6e9-088be82a5ff1\",\"serviceName\":\"zte-mes-manufactureshare-centerfactory\",\"title\":\"SQL查询返回数据超过1W条\",\"type\":\"sql\",\"content\":\"size:10;d\",\"levels\":\"重要\"},\"factoryId\":\"51\"}"));
        } catch(Exception e) {
            Assert.assertEquals("保存告警信息失败",e.getMessage());
        }
    }
}

//Generated with love by TestMe :) Please report issues and submit feature requests at: http://weirddev.com/forum#!/testme