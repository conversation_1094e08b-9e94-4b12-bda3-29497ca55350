package com.zte.application.sncabind;
/* Started by AICoder, pid:c4fa5u310fj0976143110b4c212b6f8661b4989b */

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.zte.application.SysLookupValuesService;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.domain.model.sncabind.PsTaskRepository;
import com.zte.infrastructure.remote.PdmRemoteService;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({AbstractMesPsTaskService.class, PageHelper.class})
public class AbstractMesPsTaskServiceTest {

    @InjectMocks
    private AbstractMesPsTaskService service = new MesPsTaskServiceImpl();

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Mock
    private PsTaskRepository psTaskRepository;

    @Mock
    private PdmRemoteService pdmRemoteService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(PageHelper.class);
    }

    @Test
    public void testProcessMesPsTask_whenNoTasks() {
        // Arrange
        when(psTaskRepository.pageBySourceSysAndTaskStatus(anyString(), anyString(), any(Date.class))).thenReturn(new Page<>(0, 200, false));

        // Act
        service.processMesPsTask("alibaba");

        // Assert
        verify(psTaskRepository).pageBySourceSysAndTaskStatus(anyString(), anyString(), any(Date.class));
    }

    @Test
    public void testProcessMesPsTask_whenTasks() {
        Page<PsTask> page = new Page<PsTask>(){
            PsTask psTask1 = new PsTask();
            {
                psTask1.setItemNo("item1");
            }
            @Override
            public List getResult() {
                if (psTask1 == null){
                    return Collections.emptyList();
                }
                PsTask psTask = psTask1;
                psTask1 = null;
                return Collections.singletonList(psTask);
            }
        };
        // Arrange
        when(psTaskRepository.pageBySourceSysAndTaskStatus(anyString(), anyString(), any(Date.class))).thenReturn(page);
        Map<String, String> customerToLookupTypeMap = new HashMap<>();
        customerToLookupTypeMap.put("alibaba", "1004115");
        ReflectionTestUtils.setField(service, "customerToLookupTypeMap", customerToLookupTypeMap);

        // Act
        service.processMesPsTask("alibaba");

        // Assert
        verify(psTaskRepository, times(2)).pageBySourceSysAndTaskStatus(anyString(), anyString(), any(Date.class));
    }

    @Test
    public void testFilter_whenCustomerNotFound() {
        // Arrange
        PsTask psTask1 = new PsTask();
        psTask1.setItemNo("item1");
        PsTask psTask2 = new PsTask();
        psTask2.setItemNo("item2");
        List<PsTask> tasks = Arrays.asList(psTask1, psTask2);
        Map<String, String> customerToLookupTypeMap = new HashMap<>();
        ReflectionTestUtils.setField(service, "customerToLookupTypeMap", customerToLookupTypeMap);

        // Act & Assert
        try {
            ReflectionTestUtils.invokeMethod(service, "filter", tasks, "unknown");
            fail("Expected MesBusinessException to be thrown");
        } catch (MesBusinessException e) {
            assertEquals(RetCode.VALIDATIONERROR_CODE, e.getExCode());
        }
    }

    @Test
    public void testFilter_whenSpecificCustomerNumbersEmpty() {
        // Arrange
        PsTask psTask1 = new PsTask();
        psTask1.setItemNo("item1");
        PsTask psTask2 = new PsTask();
        psTask2.setItemNo("item2");
        List<PsTask> tasks = Arrays.asList(psTask1, psTask2);
        Map<String, String> customerToLookupTypeMap = new HashMap<>();
        customerToLookupTypeMap.put("alibaba", "1004115");
        ReflectionTestUtils.setField(service, "customerToLookupTypeMap", customerToLookupTypeMap);
        when(sysLookupValuesService.getMeaningsByType("1004115")).thenReturn(Collections.emptyList());

        // Act

        List<PsTask> result = ReflectionTestUtils.invokeMethod(service, "filter", tasks, "alibaba");

        // Assert
        assertTrue(Objects.requireNonNull(result).isEmpty());
    }

    @Test
    public void testFilter_whenItemNoToCustomerNumberMapEmpty() {
        // Arrange
        PsTask psTask1 = new PsTask();
        psTask1.setItemNo("item1");
        PsTask psTask2 = new PsTask();
        psTask2.setItemNo("item2");
        List<PsTask> tasks = Arrays.asList(psTask1, psTask2);
        Map<String, String> customerToLookupTypeMap = new HashMap<>();
        customerToLookupTypeMap.put("alibaba", "1004115");
        ReflectionTestUtils.setField(service, "customerToLookupTypeMap", customerToLookupTypeMap);
        when(sysLookupValuesService.getMeaningsByType("1004115")).thenReturn(Arrays.asList("cust1", "cust2"));
        when(pdmRemoteService.getItemNoToCustomerNumberMap(any())).thenReturn(Collections.emptyMap());

        // Act
        List<PsTask> result = ReflectionTestUtils.invokeMethod(service, "filter", tasks, "alibaba");

        // Assert
        assertTrue(Objects.requireNonNull(result).isEmpty());
    }

    @Test
    public void testFilter_whenValid() {
        // Arrange
        PsTask psTask1 = new PsTask();
        psTask1.setItemNo("item1");
        PsTask psTask2 = new PsTask();
        psTask2.setItemNo("item2");
        List<PsTask> tasks = Arrays.asList(psTask1, psTask2);
        Map<String, String> customerToLookupTypeMap = new HashMap<>();
        customerToLookupTypeMap.put("alibaba", "1004115");
        ReflectionTestUtils.setField(service, "customerToLookupTypeMap", customerToLookupTypeMap);
        when(sysLookupValuesService.getMeaningsByType("1004115")).thenReturn(Arrays.asList("cust1", "cust2"));
        Map<String, String> itemNoToCustomerNumberMap = new HashMap<>();
        itemNoToCustomerNumberMap.put("item1", "cust1");
        itemNoToCustomerNumberMap.put("item2", "cust2");
        when(pdmRemoteService.getItemNoToCustomerNumberMap(any())).thenReturn(itemNoToCustomerNumberMap);

        // Act
        List<PsTask> result = ReflectionTestUtils.invokeMethod(service, "filter", tasks, "alibaba");

        // Assert
        assertEquals(2, Objects.requireNonNull(result).size());
    }

    @Test
    public void testPageBySourceSysAndTaskStatus() {
        // Arrange
        when(psTaskRepository.pageBySourceSysAndTaskStatus(anyString(), anyString(), any(Date.class))).thenReturn(new Page<>(0, 200, false));

        // Act
        Page<PsTask> page = ReflectionTestUtils.invokeMethod(service, "pageBySourceSysAndTaskStatus", 1, 200, false);

        // Assert
        assertNotNull(page);
        assertEquals(0, page.getTotal());
    }

    // Implementing the abstract method for testing purposes
    private static class MesPsTaskServiceImpl extends AbstractMesPsTaskService {
        @Override
        protected List<PsTask> filter(List<PsTask> rows) {
            return rows; // Return all rows for testing
        }

        @Override
        protected void process(List<PsTask> filteredRows, List<PsTask> allRows) {
            // Implementation for testing
        }
    }
}

/* Ended by AICoder, pid:c4fa5u310fj0976143110b4c212b6f8661b4989b */