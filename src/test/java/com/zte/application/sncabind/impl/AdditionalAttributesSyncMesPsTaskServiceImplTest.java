package com.zte.application.sncabind.impl;

import cn.hutool.core.map.MapUtil;
import com.zte.aiop.dtems.AlarmHelper;
import com.zte.application.CustomerItemsService;
import com.zte.application.PsTaskExtendedService;
import com.zte.application.SysLookupValuesService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.infrastructure.feign.ApsInOneClient;
import com.zte.infrastructure.remote.CpqdRemoteService;
import com.zte.infrastructure.remote.PdmRemoteService;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.mbom.CpqdGbomDTO;
import com.zte.interfaces.dto.mbom.PdmProdInstanceResDTO;
import com.zte.springbootframe.common.model.Page;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({AdditionalAttributesSyncMesPsTaskServiceImpl.class, AlarmHelper.class})
public class AdditionalAttributesSyncMesPsTaskServiceImplTest {

    @InjectMocks
    private AdditionalAttributesSyncMesPsTaskServiceImpl service;

    @Mock
    private ApsInOneClient apsInOneClient;

    @Mock
    private PsTaskExtendedService psTaskExtendedService;

    @Mock
    private SysLookupValuesService sysLookupValuesService;
    
    @Mock
    private PdmRemoteService pdmRemoteService;
    
    @Mock
    private CustomerItemsService customerItemsService;
    
    @Mock
    private CpqdRemoteService cpqdRemoteService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(AlarmHelper.class);
        
        // 设置配置值
        ReflectionTestUtils.setField(service, "taskTypeType", 2025050601);
        ReflectionTestUtils.setField(service, "entityClassType", "2025051511");
        // 删除这行
        // ReflectionTestUtils.setField(service, "materialControlType", 2025051601);
    }

    @Test
    public void testFilter_whenNoTasks() {
        // Arrange
        List<PsTask> rows = Collections.emptyList();

        // Act
        List<PsTask> result = service.filter(rows);

        // Assert
        assertTrue(result.isEmpty());
    }

    @Test
    public void testFilter_whenTasksExist() {
        // Arrange
        PsTask task1 = new PsTask();
        task1.setTaskNo("task1");
        PsTask task2 = new PsTask();
        task2.setTaskNo("task2");
        List<PsTask> rows = Arrays.asList(task1, task2);
        PsTaskExtendedDTO dto1 = new PsTaskExtendedDTO();
        dto1.setTaskNo("task1");
        when(psTaskExtendedService.listByTaskNos(any())).thenReturn(Collections.singletonList(dto1));

        // Act
        List<PsTask> result = service.filter(rows);

        // Assert
        assertEquals(1, result.size());
        assertEquals("task2", result.get(0).getTaskNo());
    }

    @Test
    public void testProcess_whenFilteredRowsEmpty() {
        // Arrange
        List<PsTask> filteredRows = Collections.emptyList();
        List<PsTask> allRows = Collections.emptyList();

        // Act
        service.process(filteredRows, allRows);

        // Assert
        verify(psTaskExtendedService, never()).batchSave(any());
    }

    @Test
    public void testProcess_whenPsTaskExtendedDTOListEmpty() {
        // Arrange
        PsTask task1 = new PsTask();
        task1.setTaskNo("task1");
        List<PsTask> filteredRows = Collections.singletonList(task1);
        List<PsTask> allRows = Collections.singletonList(task1);
        
        Page<PsTaskExtendedDTO> page = new Page<>();
        page.setRows(Collections.emptyList());
        when(apsInOneClient.customerTaskQuery(any())).thenReturn(page);

        // Act
        service.process(filteredRows, allRows);

        // Assert
        verify(psTaskExtendedService, never()).batchSave(any());
    }

    @Test
    public void testProcess_whenValid() {
        // Arrange
        PsTask task1 = new PsTask();
        task1.setTaskNo("task1");
        task1.setOrgId(BigDecimal.valueOf(795));
        List<PsTask> filteredRows = Collections.singletonList(task1);
        List<PsTask> allRows = Collections.singletonList(task1);
        
        PsTaskExtendedDTO dto1 = new PsTaskExtendedDTO();
        dto1.setTaskNo("task1");
        dto1.setEntityClass("xx");
        dto1.setBusinessScene("businessScene1");
        dto1.setBillNo("bill1");
        dto1.setMaterialControl("1"); // 保持原始值
        
        Page<PsTaskExtendedDTO> page = new Page<>();
        page.setRows(Collections.singletonList(dto1));
        when(apsInOneClient.customerTaskQuery(any())).thenReturn(page);
        
        // Mock 任务类型映射
        SysLookupValues taskTypeLookup = new SysLookupValues();
        taskTypeLookup.setLookupMeaning("businessScene1");
        taskTypeLookup.setAttribute1("taskType1");
        when(sysLookupValuesService.selectValuesByType(2025050601)).thenReturn(Collections.singletonList(taskTypeLookup));
        
        // Mock 实体分类
        when(sysLookupValuesService.getMeaningsByType("2025051511")).thenReturn(Collections.singletonList("xx"));
        
        // Mock 云类型查询
        CpqdGbomDTO gbomDTO = new CpqdGbomDTO();
        gbomDTO.setBillNo("bill1");
        gbomDTO.setCloudType("cloud1");
        when(cpqdRemoteService.queryGbomList(any())).thenReturn(Collections.singletonList(gbomDTO));

        // Act
        service.process(filteredRows, allRows);

        // Assert
        verify(psTaskExtendedService).batchSave(any());
    }

    @Test
    public void testComplement() throws Exception {
        // Arrange
        PsTaskExtendedDTO dto1 = new PsTaskExtendedDTO();
        List<PsTaskExtendedDTO> dtos = Collections.singletonList(dto1);

        // Act
        ReflectionTestUtils.invokeMethod(service, "complement", dtos);

        // Assert
        assertEquals(Constant.SYSTEM, dto1.getLastUpdatedBy());
        assertEquals(Constant.SYSTEM, dto1.getCreateBy());
    }

    @Test
    public void testSave() throws Exception {
        // Arrange
        PsTaskExtendedDTO dto1 = new PsTaskExtendedDTO();
        List<PsTaskExtendedDTO> dtos = Collections.singletonList(dto1);

        // Act
        ReflectionTestUtils.invokeMethod(service, "save", dtos);

        // Assert
        verify(psTaskExtendedService).batchSave(dtos);
    }

    @Test
    public void testQueryPsTaskExtendedDTOList_whenBusinessSceneMapped() {
        // Arrange
        PsTask task1 = new PsTask();
        task1.setTaskNo("task1");
        task1.setOrgId(BigDecimal.valueOf(795));
        List<PsTask> rows = Collections.singletonList(task1);
        
        PsTaskExtendedDTO dto1 = new PsTaskExtendedDTO();
        dto1.setTaskNo("task1");
        dto1.setBusinessScene("businessScene1");
        dto1.setEntityClass("xx");
        dto1.setBillNo("bill1");
        dto1.setMaterialControl("1"); // 保持原始值
        
        Page<PsTaskExtendedDTO> page = new Page<>();
        page.setRows(Collections.singletonList(dto1));
        when(apsInOneClient.customerTaskQuery(any())).thenReturn(page);
        
        // Mock 任务类型映射
        SysLookupValues taskTypeLookup = new SysLookupValues();
        taskTypeLookup.setLookupMeaning("businessScene1");
        taskTypeLookup.setAttribute1("taskType1");
        when(sysLookupValuesService.selectValuesByType(2025050601)).thenReturn(Collections.singletonList(taskTypeLookup));
        
        // Mock 实体分类
        when(sysLookupValuesService.getMeaningsByType("2025051511")).thenReturn(Collections.singletonList("xx"));
        
        // Mock 云类型查询
        CpqdGbomDTO gbomDTO = new CpqdGbomDTO();
        gbomDTO.setBillNo("bill1");
        gbomDTO.setCloudType("cloud1");
        when(cpqdRemoteService.queryGbomList(any())).thenReturn(Collections.singletonList(gbomDTO));

        // Act
        List<PsTaskExtendedDTO> result = ReflectionTestUtils.invokeMethod(service, "queryPsTaskExtendedDTOList", rows);

        // Assert
        assertEquals(1, Objects.requireNonNull(result).size());
        assertEquals("taskType1", result.get(0).getTaskType());
        assertEquals("cloud1", result.get(0).getCloudType()); // 使用从接口获取的原始值
        assertEquals("1", result.get(0).getMaterialControl()); // 保持原始值
    }

    @Test
    public void testQueryPsTaskExtendedDTOList_whenBusinessSceneNotMapped() {
        // Arrange
        PsTask task1 = new PsTask();
        task1.setTaskNo("task1");
        task1.setOrgId(BigDecimal.valueOf(795));
        List<PsTask> rows = Collections.singletonList(task1);
        
        PsTaskExtendedDTO dto1 = new PsTaskExtendedDTO();
        dto1.setTaskNo("task1");
        dto1.setBusinessScene("businessScene1");
        dto1.setEntityClass("xx");
        
        Page<PsTaskExtendedDTO> page = new Page<>();
        page.setRows(Collections.singletonList(dto1));
        when(apsInOneClient.customerTaskQuery(any())).thenReturn(page);
        
        when(sysLookupValuesService.selectValuesByType(2025050601)).thenReturn(Collections.emptyList());

        // Act
        List<PsTaskExtendedDTO> result = ReflectionTestUtils.invokeMethod(service, "queryPsTaskExtendedDTOList", rows);

        // Assert
        assertEquals(0, Objects.requireNonNull(result).size());
        // 只验证业务逻辑，不验证静态方法调用
    }

    @Test
    public void testQueryPsTaskExtendedDTOList_whenEntityClassNotInRange() {
        // Arrange
        PsTask task1 = new PsTask();
        task1.setTaskNo("task1");
        task1.setOrgId(BigDecimal.valueOf(795));
        List<PsTask> rows = Collections.singletonList(task1);
        
        PsTaskExtendedDTO dto1 = new PsTaskExtendedDTO();
        dto1.setTaskNo("task1");
        dto1.setBusinessScene("businessScene1");
        dto1.setEntityClass("invalidClass");
        
        Page<PsTaskExtendedDTO> page = new Page<>();
        page.setRows(Collections.singletonList(dto1));
        when(apsInOneClient.customerTaskQuery(any())).thenReturn(page);
        
        // Mock 任务类型映射
        SysLookupValues taskTypeLookup = new SysLookupValues();
        taskTypeLookup.setLookupMeaning("businessScene1");
        taskTypeLookup.setAttribute1("taskType1");
        when(sysLookupValuesService.selectValuesByType(2025050601)).thenReturn(Collections.singletonList(taskTypeLookup));
        
        // Mock 实体分类 - 不包含 invalidClass
        when(sysLookupValuesService.getMeaningsByType("2025051511")).thenReturn(Collections.singletonList("validClass"));

        // Act
        List<PsTaskExtendedDTO> result = ReflectionTestUtils.invokeMethod(service, "queryPsTaskExtendedDTOList", rows);

        // Assert
        assertEquals(0, Objects.requireNonNull(result).size());
        // 只验证业务逻辑，不验证静态方法调用
    }

    @Test
    public void testQueryPsTaskExtendedDTOList_whenBillNoIsBlank() {
        // Arrange
        PsTask task1 = new PsTask();
        task1.setTaskNo("task1");
        task1.setOrgId(BigDecimal.valueOf(795));
        List<PsTask> rows = Collections.singletonList(task1);
        
        PsTaskExtendedDTO dto1 = new PsTaskExtendedDTO();
        dto1.setTaskNo("task1");
        dto1.setBusinessScene("businessScene1");
        dto1.setEntityClass("xx");
        dto1.setBillNo(""); // 空字符串
        dto1.setMaterialControl("1"); // 保持原始值
        // 不要在这里设置 cloudType，让它由 setCloudType 方法设置
        
        Page<PsTaskExtendedDTO> page = new Page<>();
        page.setRows(Collections.singletonList(dto1));
        when(apsInOneClient.customerTaskQuery(any())).thenReturn(page);
        
        // Mock 任务类型映射
        SysLookupValues taskTypeLookup = new SysLookupValues();
        taskTypeLookup.setLookupMeaning("businessScene1");
        taskTypeLookup.setAttribute1("taskType1");
        when(sysLookupValuesService.selectValuesByType(2025050601)).thenReturn(Collections.singletonList(taskTypeLookup));
        
        // Mock 实体分类
        when(sysLookupValuesService.getMeaningsByType("2025051511")).thenReturn(Collections.singletonList("xx"));
        
        // Mock 云类型查询 - 返回空列表，因为 billNo 为空
        when(cpqdRemoteService.queryGbomList(any())).thenReturn(Collections.emptyList());

        // Act
        List<PsTaskExtendedDTO> result = ReflectionTestUtils.invokeMethod(service, "queryPsTaskExtendedDTOList", rows);

        // Assert
        assertEquals(1, Objects.requireNonNull(result).size());
        assertNull(result.get(0).getCloudType()); // 云类型应该为null，因为billNo为空
        assertEquals("1", result.get(0).getMaterialControl()); // 保持原始值
    }

    @Test
    public void testQueryPsTaskExtendedDTOList_whenPageRowsIsNull() {
        // Arrange
        PsTask task1 = new PsTask();
        task1.setTaskNo("task1");
        task1.setOrgId(BigDecimal.valueOf(795));
        List<PsTask> rows = Collections.singletonList(task1);
        
        Page<PsTaskExtendedDTO> page = new Page<>();
        page.setRows(null); // 设置为null
        when(apsInOneClient.customerTaskQuery(any())).thenReturn(page);

        // Act
        List<PsTaskExtendedDTO> result = ReflectionTestUtils.invokeMethod(service, "queryPsTaskExtendedDTOList", rows);

        // Assert
        assertEquals(0, Objects.requireNonNull(result).size());
    }

    @Test
    public void testQueryPsTaskExtendedDTOList_whenPageRowsIsEmpty() {
        // Arrange
        PsTask task1 = new PsTask();
        task1.setTaskNo("task1");
        task1.setOrgId(BigDecimal.valueOf(795));
        List<PsTask> rows = Collections.singletonList(task1);
        
        Page<PsTaskExtendedDTO> page = new Page<>();
        page.setRows(Collections.emptyList()); // 设置为空列表
        when(apsInOneClient.customerTaskQuery(any())).thenReturn(page);

        // Act
        List<PsTaskExtendedDTO> result = ReflectionTestUtils.invokeMethod(service, "queryPsTaskExtendedDTOList", rows);

        // Assert
        assertEquals(0, Objects.requireNonNull(result).size());
    }

    @Test
    public void testUpdateCustomItemInfo_whenValidInput_updatesCorrectly() {
        // Arrange
        PsTask task1 = new PsTask();
        task1.setItemNo("item1");

        PsTask task2 = new PsTask();
        task2.setItemNo("item2");

        List<PsTask> psTasks = Arrays.asList(task1, task2);

        List<PdmProdInstanceResDTO> pdmProdInstanceResDTOList = getPdmProdInstanceResDTOS();

        CustomerItemsDTO customerItemsDTO1 = new CustomerItemsDTO();
        customerItemsDTO1.setZteCode("item1");
        customerItemsDTO1.setId("1");

        List<CustomerItemsDTO> customerItemsDTOS = Collections.singletonList(customerItemsDTO1);

        Map<String, String> customerCodeToNameMap = MapUtil.ofEntries(MapUtil.entry("custom1", "Customer1"), MapUtil.entry("custom2", "Customer2"));

        when(pdmRemoteService.getPdmProdInstanceResDTOList(anyList())).thenReturn(pdmProdInstanceResDTOList);
        when(customerItemsService.queryListByZteCodes(anyList())).thenReturn(customerItemsDTOS);
        when(sysLookupValuesService.getValueMappingByType(anyInt(), any())).thenReturn(customerCodeToNameMap);

        // Act
        ReflectionTestUtils.invokeMethod(service, "updateCustomItemInfo", psTasks);

        // Assert
        verify(customerItemsService).batchUpdateByZteCode(anyList());
        verify(customerItemsService).batchInsert(anyList());
    }

    @Test
    public void testUpdateCustomItemInfo_whenInvalidCustomNo_throwsException() {
        // Arrange
        PsTask task1 = new PsTask();
        task1.setItemNo("item1");

        List<PsTask> psTasks = Collections.singletonList(task1);

        PdmProdInstanceResDTO pdmProdInstanceResDTO1 = new PdmProdInstanceResDTO();
        pdmProdInstanceResDTO1.setInstanceNo("item1");
        pdmProdInstanceResDTO1.setCustomNo("invalidCustomNo");

        List<PdmProdInstanceResDTO> pdmProdInstanceResDTOList = Collections.singletonList(pdmProdInstanceResDTO1);

        when(pdmRemoteService.getPdmProdInstanceResDTOList(anyList())).thenReturn(pdmProdInstanceResDTOList);
        when(customerItemsService.queryListByZteCodes(anyList())).thenReturn(Collections.emptyList());
        when(sysLookupValuesService.getValueMappingByType(anyInt(), any())).thenReturn(Collections.emptyMap());

        // Act & Assert
        try {
            ReflectionTestUtils.invokeMethod(service, "updateCustomItemInfo", psTasks);
            fail("Expected IllegalArgumentException to be thrown");
        } catch (IllegalArgumentException e) {
            assertEquals("客户编码invalidCustomNo对应的客户名称不存在", e.getMessage());
        }
    }

    private List<PdmProdInstanceResDTO> getPdmProdInstanceResDTOS() {
        PdmProdInstanceResDTO pdmProdInstanceResDTO1 = new PdmProdInstanceResDTO();
        pdmProdInstanceResDTO1.setInstanceNo("item1");
        pdmProdInstanceResDTO1.setCustomNo("custom1");
        pdmProdInstanceResDTO1.setCustomPartType("type1");

        PdmProdInstanceResDTO pdmProdInstanceResDTO2 = new PdmProdInstanceResDTO();
        pdmProdInstanceResDTO2.setInstanceNo("item2");
        pdmProdInstanceResDTO2.setCustomNo("custom2");
        pdmProdInstanceResDTO2.setCustomPartType("type2");

        return Arrays.asList(pdmProdInstanceResDTO1, pdmProdInstanceResDTO2);
    }
}