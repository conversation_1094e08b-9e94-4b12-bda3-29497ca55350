package com.zte.application.sncabind.impl;

import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Lists;
import com.zte.application.IMESLogService;
import com.zte.application.PsTaskExtendedService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.CpqdService;
import com.zte.application.sncabind.TaskConfirmationService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.domain.model.sncabind.PsTaskRepository;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.WarehouseEntryInfoDTO;
import com.zte.interfaces.dto.mbom.CpqdGbomDTO;
import com.zte.interfaces.dto.mbom.CpqdQueryDTO;
import com.zte.interfaces.dto.aps.ApsResponseDTO;
import com.zte.interfaces.dto.aps.EntityQueryDTO;
import com.zte.interfaces.dto.aps.TaskCancelableDTO;
import com.zte.interfaces.sncabind.dto.AllocationQueryDTO;
import com.zte.interfaces.sncabind.dto.PsTaskDTO;
import com.zte.interfaces.sncabind.dto.TaskBrandChangeStatusDTO;
import com.zte.springbootframe.common.Constants;
import com.zte.springbootframe.common.exception.MesBusinessException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.*;

import static com.zte.common.utils.Constant.STR_0;
import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
public class PsTaskServiceImplTest {

    @InjectMocks
    PsTaskServiceImpl service;

    @Mock
    PsTaskRepository psTaskRepository;
    @Mock
    private ApsRemoteService apsRemoteService;
    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private PlanScheduleRemoteService planScheduleRemoteService;

    @Mock
    private DatawbRemoteService datawbRemoteService;

    @Mock
    SysLookupValuesRepository sysLookupValuesRepository;

    @Mock
    IMESLogService imesLogService;
    @Mock
    private PdmRemoteService pdmRemoteService;
    @Mock
    private PsTaskExtendedService psTaskExtendedService;
    @Mock
    private TaskConfirmationService taskConfirmationService;
    @Mock
    private ProductionmgmtRemoteService productionmgmtRemoteService;

    @Mock
    private CpqdService cpqdService;

    @Test
    public void getBrandChangeStatusByTaskNo() throws Exception {
        PowerMockito.when(psTaskRepository.selectBrandChangeStatusByTaskNo(any()))
                .thenReturn(null);
        try {
            service.getBrandChangeStatusByTaskNo("1");
        } catch (MesBusinessException e) {
            assertEquals(e.getExMsgId(), MessageId.TASK_NO_IS_NOT_EXISTS);
        }
        try {
            PowerMockito.when(psTaskRepository.selectBrandChangeStatusByTaskNo(any()))
                    .thenReturn(new TaskBrandChangeStatusDTO());
            service.getBrandChangeStatusByTaskNo("1");
        } catch (MesBusinessException e) {
            assertEquals(e.getExMsgId(), MessageId.TASK_NO_IS_NOT_EXISTS);
        }
        PowerMockito.when(psTaskRepository.selectBrandChangeStatusByTaskNo(any()))
                .thenReturn(new TaskBrandChangeStatusDTO(){{setProdplanId("1");setModifiable(STR_0);}});

        PowerMockito.when(psTaskRepository.selectBrandChangeStatusByTaskNo(any()))
                .thenReturn(new TaskBrandChangeStatusDTO(){{setProdplanId("1");}});
        PowerMockito.when(datawbRemoteService.validateSubmitStatus(any()))
                .thenReturn(false);
        Assert.assertNotNull(service.getBrandChangeStatusByTaskNo("1"));
        PowerMockito.when(datawbRemoteService.validateSubmitStatus(any()))
                .thenReturn(true);
        Assert.assertNotNull(service.getBrandChangeStatusByTaskNo("1"));
    }

    @Test
    public void getBrandChangeStatusByTaskNo2() throws Exception {
        PowerMockito.when(psTaskRepository.selectBrandChangeStatusByTaskNo(any()))
                .thenReturn(new TaskBrandChangeStatusDTO(){{setProdplanId("1");}});
        List<SysLookupValues> list = new ArrayList<>();
        PowerMockito.when(sysLookupValuesRepository.getList(any()))
                .thenReturn(list);
        Assert.assertNotNull(service.getBrandChangeStatusByTaskNo("1"));
        list.add(new SysLookupValues(){{setLookupMeaning("2");}});
        list.add(new SysLookupValues(){{setLookupMeaning("3");}});
        PowerMockito.when(sysLookupValuesRepository.getList(any()))
                .thenReturn(list);
        Assert.assertNotNull(service.getBrandChangeStatusByTaskNo("1"));
        list.add(new SysLookupValues(){{setLookupMeaning("1");}});
        PowerMockito.when(sysLookupValuesRepository.getList(any()))
                .thenReturn(list);
        Assert.assertNotNull(service.getBrandChangeStatusByTaskNo("1"));

    }

    @Test
    public void batchUpdateInforExe() throws Exception {
        service.batchUpdateInforExe(new AllocationQueryDTO());
        Assert.assertNotNull(service.batchUpdateInforExe(new AllocationQueryDTO(){{setSourceTaskList(Lists.newArrayList("1"));}}));
    }

    @Test
    public void dealHisDataWithNoFactoryId() throws Exception {
        PsTaskDTO dto = new PsTaskDTO();
        dto.setCreateDateStart("2024-01-01 00:00:00");
        dto.setCreateDateEnd("2024-12-01 00:00:00");
        List<PsTask> psTasks = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanNo("test123");
        psTask.setProdplanId("test123");
        PowerMockito.when(psTaskRepository.getPsTaskListByCondition(any())).thenReturn(psTasks);
        Assert.assertTrue(service.dealHisDataWithNoFactoryId(dto)==0);
        psTasks.add(psTask);
        List<SysLookupValues> sysLookValues = new ArrayList<>();
        SysLookupValues sysLookupValue = new SysLookupValues();
        sysLookupValue.setLookupMeaning("58");
        sysLookupValue.setRemark("4437,395");
        sysLookupValue.setAttribute2("666");
        sysLookValues.add(sysLookupValue);
        SysLookupValues sysLookupValue1 = new SysLookupValues();
        sysLookupValue1.setLookupMeaning("55");
        sysLookupValue1.setRemark("666");
        sysLookupValue1.setAttribute2("666");
        sysLookValues.add(sysLookupValue1);
        List<ApsResponseDTO> apsTempList = new ArrayList<>();
        PowerMockito.when(sysLookupValuesService.findByLookupType(any())).thenReturn(sysLookValues);
        PowerMockito.when(apsRemoteService.getBatchProdPlanSendBomHead(any())).thenReturn(apsTempList);
        PowerMockito.when(planScheduleRemoteService.dealPsTaskToLocalFactoryUpdate(any(),any(),any(),any())).thenReturn(1);
        Assert.assertTrue(service.dealHisDataWithNoFactoryId(dto)>=0);
        ApsResponseDTO apsResponseDTO = new ApsResponseDTO();
        apsResponseDTO.setProdplanNo("test123");
        apsTempList.add(apsResponseDTO);
        ApsResponseDTO apsResponseDTO1 = new ApsResponseDTO();
        apsResponseDTO1.setProdplanNo("test124");
        apsResponseDTO1.setQty(new BigDecimal("100"));
        apsTempList.add(apsResponseDTO1);
        ApsResponseDTO apsResponseDTO2 = new ApsResponseDTO();
        apsResponseDTO2.setProdplanNo("test125");
        apsResponseDTO2.setQty(new BigDecimal("0"));
        apsTempList.add(apsResponseDTO2);
        ApsResponseDTO apsResponseDTO3 = new ApsResponseDTO();
        apsResponseDTO3.setProdplanNo("test126");
        apsResponseDTO3.setQty(new BigDecimal("1000"));
        apsTempList.add(apsResponseDTO3);
        PowerMockito.when(planScheduleRemoteService.getPsTaskByProdPlanIdList(any(),any())).thenReturn(psTasks);
        Assert.assertTrue(service.dealHisDataWithNoFactoryId(dto)>=0);
        PsTask psTask1 = new PsTask();
        psTask1.setProdplanNo("test124");
        psTask1.setProdplanId("test124");
        psTask1.setFactoryId(new BigDecimal("58"));
        PsTask psTask2 = new PsTask();
        psTask2.setProdplanNo("test125");
        psTask2.setProdplanId("test125");
        psTask2.setOrgId(new BigDecimal("4437"));
        PsTask psTask3 = new PsTask();
        psTask3.setProdplanNo("test126");
        psTask3.setProdplanId("test126");
        psTask3.setOrgId(new BigDecimal("4437"));
        psTask3.setFactoryId(new BigDecimal("55"));
        psTasks.add(psTask1);
        psTasks.add(psTask2);
        psTasks.add(psTask3);
        PowerMockito.when(planScheduleRemoteService.getPsTaskByProdPlanIdList(any(),any())).thenReturn(psTasks);
        Assert.assertTrue(service.dealHisDataWithNoFactoryId(dto)>=0);
    }
    @Test
    public void dealHisDataWithNoFactoryIdTextOne() throws Exception {
        PsTaskDTO dto = new PsTaskDTO();
        dto.setCreateDateStart("2024-01-01 00:00:00");
        dto.setCreateDateEnd("2024-12-01 00:00:00");
        List<PsTask> psTasks = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanNo("test123");
        psTask.setProdplanId("test123");
        PowerMockito.when(psTaskRepository.getPsTaskListByCondition(any())).thenReturn(psTasks);
        Assert.assertTrue(service.dealHisDataWithNoFactoryId(dto)==0);
        psTasks.add(psTask);

        List<SysLookupValues> sysLookValues = new ArrayList<>();
        SysLookupValues sysLookupValue = new SysLookupValues();
        sysLookupValue.setLookupMeaning("58");
        sysLookupValue.setRemark("4437,395");
        sysLookupValue.setAttribute2("666");
        sysLookValues.add(sysLookupValue);
        SysLookupValues sysLookupValue1 = new SysLookupValues();
        sysLookupValue1.setLookupMeaning("55");
        sysLookupValue1.setRemark("666");
        sysLookupValue1.setAttribute2("666");
        sysLookValues.add(sysLookupValue1);
        List<ApsResponseDTO> apsTempList = new ArrayList<>();
        PowerMockito.when(sysLookupValuesService.findByLookupType(any())).thenReturn(sysLookValues);
        PowerMockito.when(apsRemoteService.getBatchProdPlanSendBomHead(any())).thenReturn(apsTempList);
        PowerMockito.when(planScheduleRemoteService.dealPsTaskToLocalFactoryUpdate(any(),any(),any(),any())).thenReturn(1);
        PowerMockito.when(planScheduleRemoteService.getPsTaskByProdPlanIdList(any(),any())).thenReturn(new ArrayList<>());
        Assert.assertTrue(service.dealHisDataWithNoFactoryId(dto)>=0);
        PsTask psTask1 = new PsTask();
        psTask1.setProdplanNo("test124");
        psTask1.setProdplanId("test124");
        psTask1.setFactoryId(new BigDecimal("58"));
        PsTask psTask2 = new PsTask();
        psTask2.setProdplanNo("test125");
        psTask2.setProdplanId("test125");
        psTask2.setOrgId(new BigDecimal("4437"));
        PsTask psTask3 = new PsTask();
        psTask3.setProdplanNo("test126");
        psTask3.setProdplanId("test126");
        psTask3.setOrgId(new BigDecimal("4437"));
        psTask3.setFactoryId(new BigDecimal("55"));
        psTasks.add(psTask1);
        psTasks.add(psTask2);
        psTasks.add(psTask3);
        PowerMockito.when(planScheduleRemoteService.getPsTaskByProdPlanIdList(any(),any())).thenReturn(new ArrayList<>());
        Assert.assertTrue(service.dealHisDataWithNoFactoryId(dto)>=0);
    }

    /* Started by AICoder, pid:sc957597cey1bb6140e208c900c2531e6623a290 */
    @Test
    public void handlerInParamsTest() throws Exception {
        Map<String, Object> map = new HashMap<>();
        Whitebox.invokeMethod(service, "handlerInParams", map);
        Assert.assertTrue(map.get("statusList") == null);
        map.put("inStatus", "'3','4'");
        Whitebox.invokeMethod(service, "handlerInParams", map);
        Object statusList = map.get("statusList");
        List<String> r = (List<String>)statusList;
        Assert.assertTrue(r.size() == 2);
    }
    /* Ended by AICoder, pid:sc957597cey1bb6140e208c900c2531e6623a290 */

    /*Started by AICoder, pid:vf61fyd1eeqca801449a0a5011cfbd67a4b4ca00*/

    @Mock private PsTask psTaskModel;
    @Mock private SysLookupTypesDTO sysLookupTypesDTO;
    private PsTaskServiceImpl psTaskService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        psTaskService = new PsTaskServiceImpl();
    }

    @Test
    public void testSetFactoryIdWithEmptyList() {
        psTaskService.setFactoryId(psTaskModel, null);
        verify(psTaskModel, never()).setFactoryId(any());
    }

    @Test
    public void testSetFactoryIdWithNonEmptyListAndValidLookupMeaning() {
        when(sysLookupTypesDTO.getLookupMeaning()).thenReturn("123");
        List<SysLookupTypesDTO> matchSysLookupTypesList = new ArrayList<>();
        matchSysLookupTypesList.add(sysLookupTypesDTO);

        psTaskService.setFactoryId(psTaskModel, matchSysLookupTypesList);
        verify(psTaskModel).setFactoryId(new BigDecimal("123"));
    }

    @Test(expected = MesBusinessException.class)
    public void testSetFactoryIdWithNonEmptyListAndEmptyLookupMeaning() {
        when(sysLookupTypesDTO.getLookupMeaning()).thenReturn("");
        List<SysLookupTypesDTO> matchSysLookupTypesList = new ArrayList<>();
        matchSysLookupTypesList.add(sysLookupTypesDTO);

        psTaskService.setFactoryId(psTaskModel, matchSysLookupTypesList);
    }
    /*Ended by AICoder, pid:vf61fyd1eeqca801449a0a5011cfbd67a4b4ca00*/

    /* Started by AICoder, pid:o5410k4ea6od9551473f0bd6a01bcf9d0977cfcd */
    /**
     * 测试 preQueryForCancelConfirmation 方法，输入为空的情况。
     */
    @Test
    public void testPreQueryForCancelConfirmation_EmptyInput() {
        List<TaskCancelableDTO> taskCancelableDTOList = Collections.emptyList();

        List<TaskCancelableDTO> result = taskConfirmationService.preQueryForCancelConfirmation(taskCancelableDTOList);

        assertTrue(result.isEmpty());
    }


    /* Ended by AICoder, pid:o5410k4ea6od9551473f0bd6a01bcf9d0977cfcd */

    /* Started by AICoder, pid:d2be7y9ec3ufc56145d109673021a63a013437d2 */
    @Test
    public void updateConfirmationStatus() {
        service.updateConfirmationStatus("", "");
        service.updateConfirmationStatus("", "taskNo1");
        service.updateConfirmationStatus("3", "");
        service.updateConfirmationStatus("3", "taskNo1");
        Assert.assertTrue(true);
    }
    /* Ended by AICoder, pid:d2be7y9ec3ufc56145d109673021a63a013437d2 */

    @Test
    public void testValidateForAlibabaTask_whenNotToGrantAndIsMes_noException() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setTaskStatus("NOT_TO_GRANT");
        boolean isMes = true;

        // Act & Assert
        assertDoesNotThrow(() -> ReflectionTestUtils.invokeMethod(service, "validateForAlibabaTask", psTask, isMes));
    }

    @Test
    public void testValidateForAlibabaTask_whenToGrantAndNotMes_noException() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setTaskStatus(Constant.TASK_STATUS_TOGRANT);
        boolean isMes = false;

        // Act & Assert
        assertDoesNotThrow(() -> ReflectionTestUtils.invokeMethod(service, "validateForAlibabaTask", psTask, isMes));
    }

    @Test
    public void testValidateForAlibabaTask_whenToGrantAndIsMesAndNotAlibaba_noException() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setTaskStatus(Constant.TASK_STATUS_TOGRANT);
        psTask.setItemNo("item1");
        boolean isMes = true;

        when(pdmRemoteService.getItemNoToCustomerNumberMap(any())).thenReturn(Collections.emptyMap());
        when(sysLookupValuesService.getMeaningsByType(any())).thenReturn(Collections.emptyList());

        // Act & Assert
        assertDoesNotThrow(() -> ReflectionTestUtils.invokeMethod(service, "validateForAlibabaTask", psTask, isMes));
    }

    @Test
    public void testValidateForAlibabaTask_whenToGrantAndIsMesAndAlibaba_noException() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setTaskStatus(Constant.TASK_STATUS_TOGRANT);
        psTask.setItemNo("item1");
        boolean isMes = true;
        Map<String, String> itemNoToCustomerNumberMap = new HashMap<>();
        itemNoToCustomerNumberMap.put("item1", "cust1");
        ReflectionTestUtils.setField(service, "pdmApiOpen", true);
        when(pdmRemoteService.getItemNoToCustomerNumberMap(any())).thenReturn(itemNoToCustomerNumberMap);
        when(sysLookupValuesService.getMeaningsByType(any())).thenReturn(Collections.singletonList("cust1"));
        PsTaskExtendedDTO psTaskExtendedDTO = new PsTaskExtendedDTO();
        psTaskExtendedDTO.setFixBomComplete("Y");
        when(psTaskExtendedService.listByTaskNos(any())).thenReturn(Collections.singletonList(psTaskExtendedDTO));

        // Act & Assert
        assertDoesNotThrow(() -> ReflectionTestUtils.invokeMethod(service, "validateForAlibabaTask", psTask, isMes));
    }

    @Test
    public void testValidateForAlibabaTask_whenToGrantAndIsMesAndAlibabaButEmptyExtendedList_throwsException() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setTaskStatus(Constant.TASK_STATUS_TOGRANT);
        psTask.setItemNo("item1");
        psTask.setTaskNo("task1");
        boolean isMes = true;

        Map<String, String> itemNoToCustomerNumberMap = new HashMap<>();
        itemNoToCustomerNumberMap.put("item1", "cust1");
        ReflectionTestUtils.setField(service, "pdmApiOpen", true);
        when(pdmRemoteService.getItemNoToCustomerNumberMap(any())).thenReturn(itemNoToCustomerNumberMap);
        when(sysLookupValuesService.getMeaningsByType(any())).thenReturn(Collections.singletonList("cust1"));
        when(psTaskExtendedService.listByTaskNos(any())).thenReturn(Collections.emptyList());

        // Act & Assert
        Exception exception = assertThrows(MesBusinessException.class, () -> ReflectionTestUtils.invokeMethod(service, "validateForAlibabaTask", psTask, isMes));
        assertEquals("fix.bom.incomplete", exception.getMessage().split(",")[0]);
    }

    @Test
    public void testValidateForAlibabaTask_whenToGrantAndIsMesAndAlibabaAndValidExtendedList_noException() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setTaskStatus(Constant.TASK_STATUS_TOGRANT);
        psTask.setItemNo("item1");
        psTask.setTaskNo("task1");
        boolean isMes = true;

        Map<String, String> itemNoToCustomerNumberMap = new HashMap<>();
        itemNoToCustomerNumberMap.put("item1", "cust1");

        when(pdmRemoteService.getItemNoToCustomerNumberMap(any())).thenReturn(itemNoToCustomerNumberMap);
        when(sysLookupValuesService.getMeaningsByType(any())).thenReturn(Arrays.asList("cust1"));

        PsTaskExtendedDTO extendedDTO = new PsTaskExtendedDTO();
        extendedDTO.setFixBomComplete(Constants.FLAG_Y);
        when(psTaskExtendedService.listByTaskNos(any())).thenReturn(Collections.singletonList(extendedDTO));

        // Act & Assert
        assertDoesNotThrow(() -> ReflectionTestUtils.invokeMethod(service, "validateForAlibabaTask", psTask, isMes));
    }

    @Test
    public void testIsAlibabaTask_whenPdmApiOpenAndItemNoMappedToAlibaba_returnsTrue() {
        // Arrange
        String itemNo = "item1";
        Map<String, String> itemNoToCustomerNumberMap = new HashMap<>();
        itemNoToCustomerNumberMap.put(itemNo, "cust1");

        when(pdmRemoteService.getItemNoToCustomerNumberMap(any())).thenReturn(itemNoToCustomerNumberMap);
        when(sysLookupValuesService.getMeaningsByType(any())).thenReturn(Arrays.asList("cust1"));

        ReflectionTestUtils.setField(service, "pdmApiOpen", true);

        // Act
        boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(service, "isAlibabaTask", itemNo));

        // Assert
        assertTrue(result);
    }

    @Test
    public void testIsAlibabaTask_whenPdmApiClosed_returnsFalse() {
        // Arrange
        String itemNo = "item1";

        ReflectionTestUtils.setField(service, "pdmApiOpen", false);

        // Act
        boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(service, "isAlibabaTask", itemNo));

        // Assert
        assertFalse(result);
    }

    @Test
    public void testIsAlibabaTask_whenItemNoNotMappedToAlibaba_returnsFalse() {
        // Arrange
        String itemNo = "item1";
        Map<String, String> itemNoToCustomerNumberMap = new HashMap<>();
        itemNoToCustomerNumberMap.put(itemNo, "cust2");

        when(pdmRemoteService.getItemNoToCustomerNumberMap(any())).thenReturn(itemNoToCustomerNumberMap);
        when(sysLookupValuesService.getMeaningsByType(any())).thenReturn(Arrays.asList("cust1"));

        ReflectionTestUtils.setField(psTaskService, "pdmApiOpen", true);

        // Act
        boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(service, "isAlibabaTask", itemNo));

        // Assert
        assertFalse(result);
    }

    @Test
    public void testValidateForAlibabaTask_whenNotMes_noException() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setItemNo("item1");
        psTask.setTaskNo("task1");
        boolean isMes = false;

        // Act & Assert
        assertDoesNotThrow(() -> ReflectionTestUtils.invokeMethod(service, "validateSplitForAlibabaTask", psTask, isMes));
    }

    @Test
    public void testValidateForAlibabaTask_whenIsMesAndNotAlibaba_noException() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setItemNo("item1");
        psTask.setTaskNo("task1");
        boolean isMes = true;

        when(pdmRemoteService.getItemNoToCustomerNumberMap(any())).thenReturn(Collections.emptyMap());
        when(sysLookupValuesService.getMeaningsByType(any())).thenReturn(Collections.emptyList());

        // Act & Assert
        assertDoesNotThrow(() -> ReflectionTestUtils.invokeMethod(service, "validateSplitForAlibabaTask", psTask, isMes));
    }

    @Test
    public void testValidateForAlibabaTask_whenIsMesAndIsAlibaba_throwsException() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setItemNo("item1");
        psTask.setTaskNo("task1");
        boolean isMes = true;

        Map<String, String> itemNoToCustomerNumberMap = new HashMap<>();
        itemNoToCustomerNumberMap.put("item1", "cust1");
        ReflectionTestUtils.setField(service, "pdmApiOpen", true);
        when(pdmRemoteService.getItemNoToCustomerNumberMap(any())).thenReturn(itemNoToCustomerNumberMap);
        when(sysLookupValuesService.getMeaningsByType(any())).thenReturn(Collections.singletonList("cust1"));

        // Act & Assert
        Exception exception = assertThrows(MesBusinessException.class, () -> ReflectionTestUtils.invokeMethod(service, "validateSplitForAlibabaTask", psTask, isMes));
        assertEquals("split.not.allowed", exception.getMessage().split(",")[0]);
    }

    @Test
    public void testIsAlibabaTask_whenPdmApiOpenAndItemNoMappedToAlibaba_returnsTrue2() {
        // Arrange
        String itemNo = "item1";
        Map<String, String> itemNoToCustomerNumberMap = new HashMap<>();
        itemNoToCustomerNumberMap.put(itemNo, "cust1");

        when(pdmRemoteService.getItemNoToCustomerNumberMap(any())).thenReturn(itemNoToCustomerNumberMap);
        when(sysLookupValuesService.getMeaningsByType(any())).thenReturn(Collections.singletonList("cust1"));

        ReflectionTestUtils.setField(service, "pdmApiOpen", true);

        // Act
        boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(service, "isAlibabaTask", itemNo));

        // Assert
        assertTrue(result);
    }

    @Test
    public void testIsAlibabaTask_whenPdmApiClosed_returnsFalse2() {
        // Arrange
        String itemNo = "item1";

        ReflectionTestUtils.setField(service, "pdmApiOpen", false);

        // Act
        boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(service, "isAlibabaTask", itemNo));

        // Assert
        assertFalse(result);
    }

    @Test
    public void testIsAlibabaTask_whenItemNoNotMappedToAlibaba_returnsFalse2() {
        // Arrange
        String itemNo = "item1";
        Map<String, String> itemNoToCustomerNumberMap = new HashMap<>();
        itemNoToCustomerNumberMap.put(itemNo, "cust2");

        when(pdmRemoteService.getItemNoToCustomerNumberMap(any())).thenReturn(itemNoToCustomerNumberMap);
        when(sysLookupValuesService.getMeaningsByType(any())).thenReturn(Collections.singletonList("cust1"));

        ReflectionTestUtils.setField(service, "pdmApiOpen", true);

        // Act
        boolean result = Boolean.TRUE.equals(ReflectionTestUtils.invokeMethod(service, "isAlibabaTask", itemNo));

        // Assert
        assertFalse(result);
    }

    @Test
    public void testTaskStockQuery_whenValidInput_returnsExpectedResult() throws Exception {
        // Arrange
        EntityQueryDTO dto1 = new EntityQueryDTO();
        dto1.setEntityNo("task1");
        dto1.setStockOrgId(1);

        EntityQueryDTO dto2 = new EntityQueryDTO();
        dto2.setEntityNo("task2");
        dto2.setStockOrgId(2);

        List<EntityQueryDTO> entityQueryDTOS = Arrays.asList(dto1, dto2);

        PsTask psTask1 = new PsTask();
        psTask1.setTaskNo("task1");
        psTask1.setOrgId(new BigDecimal(1));
        psTask1.setFactoryId(new BigDecimal(1));

        PsTask psTask2 = new PsTask();
        psTask2.setTaskNo("task2");
        psTask2.setOrgId(new BigDecimal(2));
        psTask2.setFactoryId(null);

        List<PsTask> psTaskList = Arrays.asList(psTask1, psTask2);

        WarehouseEntryInfoDTO warehouseEntryInfoDTO1 = new WarehouseEntryInfoDTO();
        warehouseEntryInfoDTO1.setTaskNo("task1");
        warehouseEntryInfoDTO1.setStockQty(100L);


        when(productionmgmtRemoteService.taskStockQuery(any(), anyString())).thenReturn(Collections.singletonList(warehouseEntryInfoDTO1));

        // Act
        List<EntityQueryDTO> result = service.taskStockQuery(entityQueryDTOS);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(0L, result.get(1).getStockQty().longValue());
    }

    @Test
    public void testGetWarehouseEntryInfoDTOS_whenValidInput_returnsExpectedResult() {
        // Arrange
        Map<String, Set<String>> factoryIdGroup = MapUtil.of(
                "factory1", new HashSet<>(Arrays.asList("task1", "task2"))
        );

        WarehouseEntryInfoDTO warehouseEntryInfoDTO1 = new WarehouseEntryInfoDTO();
        warehouseEntryInfoDTO1.setTaskNo("task1");
        warehouseEntryInfoDTO1.setStockQty(100L);

        when(productionmgmtRemoteService.taskStockQuery(any(), anyString())).thenReturn(Collections.singletonList(warehouseEntryInfoDTO1));

        // Act
        Collection<WarehouseEntryInfoDTO> result = ReflectionTestUtils.invokeMethod(service, "getWarehouseEntryInfoDTOS", factoryIdGroup, Arrays.asList("0", "2"));

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("task1", result.iterator().next().getTaskNo());
        assertEquals(100L, result.iterator().next().getStockQty().longValue());

        verify(productionmgmtRemoteService).taskStockQuery(any(), anyString());
    }

    @Test
    public void testQueryPlannedTaskQty_whenValidInput_returnsExpectedResult() {
        // Arrange
        EntityQueryDTO input1 = new EntityQueryDTO();
        input1.setEntityNo("entity1");

        EntityQueryDTO input2 = new EntityQueryDTO();
        input2.setEntityNo("entity2");

        List<EntityQueryDTO> entityQueryDTOS = Arrays.asList(input1, input2);

        EntityQueryDTO result1 = new EntityQueryDTO();
        result1.setEntityNo("entity1");
        result1.setQty(100L);

        List<EntityQueryDTO> queryResults = Collections.singletonList(result1);

        when(psTaskRepository.queryTaskQty(anyList(), anyInt(), any())).thenReturn(queryResults);

        // Act
        List<EntityQueryDTO> result = service.queryPlannedTaskQty(entityQueryDTOS);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());

        assertTrue(result.get(0).isPlanned());
        assertEquals(Long.valueOf(100), result.get(0).getQty());

        assertFalse(result.get(1).isPlanned());
        assertNull(result.get(1).getQty());

        verify(psTaskRepository).queryTaskQty(anyList(), anyInt(), any());
    }

    @Test
    public void testQueryPlannedTaskQty_whenEmptyInput_returnsEmptyList() {
        // Arrange
        List<EntityQueryDTO> entityQueryDTOS = Collections.emptyList();

        when(psTaskRepository.queryTaskQty(anyList(), anyInt(), any())).thenReturn(Collections.emptyList());

        // Act
        List<EntityQueryDTO> result = service.queryPlannedTaskQty(entityQueryDTOS);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(psTaskRepository).queryTaskQty(anyList(), anyInt(), any());
    }

    // ==================== setMaterialSignInfo 方法测试 ====================

    @Test
    public void testSetMaterialSignInfo_Success() throws Exception {
        // Arrange
        List<PsTask> psTaskList = new ArrayList<>();
        
        PsTask task1 = new PsTask();
        task1.setItemNo("ITEM001");
        task1.setTaskNo("TASK001");
        
        PsTask task2 = new PsTask();
        task2.setItemNo("ITEM002");
        task2.setTaskNo("TASK002");
        
        PsTask task3 = new PsTask();
        task3.setItemNo("ITEM003");
        task3.setTaskNo("TASK003");
        
        psTaskList.add(task1);
        psTaskList.add(task2);
        psTaskList.add(task3);

        List<CpqdGbomDTO> cpqdGbomList = new ArrayList<>();
        
        CpqdGbomDTO gbom1 = new CpqdGbomDTO();
        gbom1.setInstanceNo("ITEM001");
        gbom1.setMaterialSign("gerneral server");
        gbom1.setMaterialSignName("单节点机型");
        
        CpqdGbomDTO gbom2 = new CpqdGbomDTO();
        gbom2.setInstanceNo("ITEM002");
        gbom2.setMaterialSign("other server");
        gbom2.setMaterialSignName("复合机型");
        
        CpqdGbomDTO gbom3 = new CpqdGbomDTO();
        gbom3.setInstanceNo("ITEM003");
        gbom3.setMaterialSign(null);
        gbom3.setMaterialSignName("");
        
        cpqdGbomList.add(gbom1);
        cpqdGbomList.add(gbom2);
        cpqdGbomList.add(gbom3);

        when(cpqdService.queryGbomList(any(CpqdQueryDTO.class))).thenReturn(cpqdGbomList);

        // Act
        Whitebox.invokeMethod(service, "setMaterialSignInfo", psTaskList);

        // Assert
        assertEquals("gerneral server", psTaskList.get(0).getMaterialSign());
        assertEquals("单节点机型", psTaskList.get(0).getMaterialSignName());
        
        assertEquals("other server", psTaskList.get(1).getMaterialSign());
        assertEquals("复合机型", psTaskList.get(1).getMaterialSignName());
        
        assertEquals(null, psTaskList.get(2).getMaterialSign());
        assertEquals("", psTaskList.get(2).getMaterialSignName());
    }

    @Test
    public void testSetMaterialSignInfo_EmptyList() throws Exception {
        // Arrange
        List<PsTask> emptyList = new ArrayList<>();

        // Act
        Whitebox.invokeMethod(service, "setMaterialSignInfo", emptyList);

        // Assert - 不应该调用cpqdService
        verify(cpqdService, never()).queryGbomList(any(CpqdQueryDTO.class));
    }

    @Test
    public void testSetMaterialSignInfo_NullList() throws Exception {
        // Arrange
        List<PsTask> nullList = null;

        // Act
        Whitebox.invokeMethod(service, "setMaterialSignInfo", nullList);

        // Assert - 不应该调用cpqdService
        verify(cpqdService, never()).queryGbomList(any(CpqdQueryDTO.class));
    }

    @Test
    public void testSetMaterialSignInfo_AllEmptyItemNos() throws Exception {
        // Arrange
        List<PsTask> emptyItemNoList = new ArrayList<>();
        
        PsTask task1 = new PsTask();
        task1.setItemNo("");
        task1.setTaskNo("TASK001");
        
        PsTask task2 = new PsTask();
        task2.setItemNo(null);
        task2.setTaskNo("TASK002");
        
        emptyItemNoList.add(task1);
        emptyItemNoList.add(task2);

        // Act
        Whitebox.invokeMethod(service, "setMaterialSignInfo", emptyItemNoList);

        // Assert - 不应该调用cpqdService，因为过滤后没有有效的物料编码
        verify(cpqdService, never()).queryGbomList(any(CpqdQueryDTO.class));
    }

    @Test
    public void testSetMaterialSignInfo_CpqdServiceReturnsEmptyList() throws Exception {
        // Arrange
        List<PsTask> psTaskList = new ArrayList<>();
        
        PsTask task1 = new PsTask();
        task1.setItemNo("ITEM001");
        task1.setTaskNo("TASK001");
        
        psTaskList.add(task1);

        when(cpqdService.queryGbomList(any(CpqdQueryDTO.class))).thenReturn(new ArrayList<>());

        // Act
        Whitebox.invokeMethod(service, "setMaterialSignInfo", psTaskList);

        // Assert - 所有任务的materialSign应该为null
        assertNull(psTaskList.get(0).getMaterialSign());
        assertNull(psTaskList.get(0).getMaterialSignName());
    }

    @Test
    public void testSetMaterialSignInfo_CpqdServiceReturnsNull() throws Exception {
        // Arrange
        List<PsTask> psTaskList = new ArrayList<>();
        
        PsTask task1 = new PsTask();
        task1.setItemNo("ITEM001");
        task1.setTaskNo("TASK001");
        
        psTaskList.add(task1);

        when(cpqdService.queryGbomList(any(CpqdQueryDTO.class))).thenReturn(null);

        // Act
        Whitebox.invokeMethod(service, "setMaterialSignInfo", psTaskList);

        // Assert - 所有任务的materialSign应该为null
        assertNull(psTaskList.get(0).getMaterialSign());
        assertNull(psTaskList.get(0).getMaterialSignName());
    }

    @Test
    public void testSetMaterialSignInfo_CpqdServiceThrowsException() throws Exception {
        // Arrange
        List<PsTask> psTaskList = new ArrayList<>();
        
        PsTask task1 = new PsTask();
        task1.setItemNo("ITEM001");
        task1.setTaskNo("TASK001");
        
        psTaskList.add(task1);

        when(cpqdService.queryGbomList(any(CpqdQueryDTO.class))).thenThrow(new RuntimeException("CPQD服务异常"));

        // Act - 应该捕获异常并记录日志，不影响正常流程
        Whitebox.invokeMethod(service, "setMaterialSignInfo", psTaskList);

        // Assert - 所有任务的materialSign应该为null（因为异常被捕获）
        assertNull(psTaskList.get(0).getMaterialSign());
        assertNull(psTaskList.get(0).getMaterialSignName());
    }

    @Test
    public void testSetMaterialSignInfo_DuplicateItemNos() throws Exception {
        // Arrange
        List<PsTask> duplicateList = new ArrayList<>();
        
        PsTask task1 = new PsTask();
        task1.setItemNo("ITEM001");
        task1.setTaskNo("TASK001");
        
        PsTask task2 = new PsTask();
        task2.setItemNo("ITEM001"); // 重复的物料编码
        task2.setTaskNo("TASK002");
        
        PsTask task3 = new PsTask();
        task3.setItemNo("ITEM002");
        task3.setTaskNo("TASK003");
        
        duplicateList.add(task1);
        duplicateList.add(task2);
        duplicateList.add(task3);

        List<CpqdGbomDTO> duplicateGbomList = new ArrayList<>();
        
        CpqdGbomDTO gbom1 = new CpqdGbomDTO();
        gbom1.setInstanceNo("ITEM001");
        gbom1.setMaterialSign("gerneral server");
        gbom1.setMaterialSignName("单节点机型");
        
        CpqdGbomDTO gbom2 = new CpqdGbomDTO();
        gbom2.setInstanceNo("ITEM002");
        gbom2.setMaterialSign("other server");
        gbom2.setMaterialSignName("复合机型");
        
        duplicateGbomList.add(gbom1);
        duplicateGbomList.add(gbom2);

        when(cpqdService.queryGbomList(any(CpqdQueryDTO.class))).thenReturn(duplicateGbomList);

        // Act
        Whitebox.invokeMethod(service, "setMaterialSignInfo", duplicateList);

        // Assert - 重复的物料编码应该都设置相同的值
        assertEquals("gerneral server", duplicateList.get(0).getMaterialSign());
        assertEquals("单节点机型", duplicateList.get(0).getMaterialSignName());
        
        assertEquals("gerneral server", duplicateList.get(1).getMaterialSign());
        assertEquals("单节点机型", duplicateList.get(1).getMaterialSignName());
        
        assertEquals("other server", duplicateList.get(2).getMaterialSign());
        assertEquals("复合机型", duplicateList.get(2).getMaterialSignName());
    }

    @Test
    public void testSetMaterialSignInfo_ItemNoNotInCpqdResult() throws Exception {
        // Arrange
        List<PsTask> notFoundList = new ArrayList<>();
        
        PsTask task1 = new PsTask();
        task1.setItemNo("ITEM001");
        task1.setTaskNo("TASK001");
        
        PsTask task2 = new PsTask();
        task2.setItemNo("ITEM999"); // 不存在的物料编码
        task2.setTaskNo("TASK002");
        
        notFoundList.add(task1);
        notFoundList.add(task2);

        List<CpqdGbomDTO> partialGbomList = new ArrayList<>();
        
        CpqdGbomDTO gbom1 = new CpqdGbomDTO();
        gbom1.setInstanceNo("ITEM001");
        gbom1.setMaterialSign("gerneral server");
        gbom1.setMaterialSignName("单节点机型");
        
        partialGbomList.add(gbom1);

        when(cpqdService.queryGbomList(any(CpqdQueryDTO.class))).thenReturn(partialGbomList);

        // Act
        Whitebox.invokeMethod(service, "setMaterialSignInfo", notFoundList);

        // Assert
        assertEquals("gerneral server", notFoundList.get(0).getMaterialSign());
        assertEquals("单节点机型", notFoundList.get(0).getMaterialSignName());
        
        // 不存在的物料编码应该没有设置materialSign
        assertNull(notFoundList.get(1).getMaterialSign());
        assertNull(notFoundList.get(1).getMaterialSignName());
    }

    @Test
    public void testSetMaterialSignInfo_EmptyInstanceNo() throws Exception {
        // Arrange
        List<PsTask> emptyInstanceList = new ArrayList<>();
        
        PsTask task1 = new PsTask();
        task1.setItemNo("ITEM001");
        task1.setTaskNo("TASK001");
        
        emptyInstanceList.add(task1);

        List<CpqdGbomDTO> emptyInstanceGbomList = new ArrayList<>();
        
        CpqdGbomDTO gbom1 = new CpqdGbomDTO();
        gbom1.setInstanceNo(""); // 空的instanceNo
        gbom1.setMaterialSign("gerneral server");
        gbom1.setMaterialSignName("单节点机型");
        
        emptyInstanceGbomList.add(gbom1);

        when(cpqdService.queryGbomList(any(CpqdQueryDTO.class))).thenReturn(emptyInstanceGbomList);

        // Act
        Whitebox.invokeMethod(service, "setMaterialSignInfo", emptyInstanceList);

        // Assert - 空的instanceNo应该被过滤掉，不会设置materialSign
        assertNull(emptyInstanceList.get(0).getMaterialSign());
        assertNull(emptyInstanceList.get(0).getMaterialSignName());
    }

    @Test
    public void testSetMaterialSignInfo_NullInstanceNo() throws Exception {
        // Arrange
        List<PsTask> nullInstanceList = new ArrayList<>();
        
        PsTask task1 = new PsTask();
        task1.setItemNo("ITEM001");
        task1.setTaskNo("TASK001");
        
        nullInstanceList.add(task1);

        List<CpqdGbomDTO> nullInstanceGbomList = new ArrayList<>();
        
        CpqdGbomDTO gbom1 = new CpqdGbomDTO();
        gbom1.setInstanceNo(null); // null的instanceNo
        gbom1.setMaterialSign("gerneral server");
        gbom1.setMaterialSignName("单节点机型");
        
        nullInstanceGbomList.add(gbom1);

        when(cpqdService.queryGbomList(any(CpqdQueryDTO.class))).thenReturn(nullInstanceGbomList);

        // Act
        Whitebox.invokeMethod(service, "setMaterialSignInfo", nullInstanceList);

        // Assert - null的instanceNo应该被过滤掉，不会设置materialSign
        assertNull(nullInstanceList.get(0).getMaterialSign());
        assertNull(nullInstanceList.get(0).getMaterialSignName());
    }

    @Test
    public void testSetMaterialSignInfo_MixedValidAndInvalidData() throws Exception {
        // Arrange
        List<PsTask> mixedList = new ArrayList<>();
        
        PsTask task1 = new PsTask();
        task1.setItemNo("ITEM001");
        task1.setTaskNo("TASK001");
        
        PsTask task2 = new PsTask();
        task2.setItemNo(""); // 空物料编码
        task2.setTaskNo("TASK002");
        
        PsTask task3 = new PsTask();
        task3.setItemNo("ITEM002");
        task3.setTaskNo("TASK003");
        
        PsTask task4 = new PsTask();
        task4.setItemNo(null); // null物料编码
        task4.setTaskNo("TASK004");
        
        mixedList.add(task1);
        mixedList.add(task2);
        mixedList.add(task3);
        mixedList.add(task4);

        List<CpqdGbomDTO> mixedGbomList = new ArrayList<>();
        
        CpqdGbomDTO gbom1 = new CpqdGbomDTO();
        gbom1.setInstanceNo("ITEM001");
        gbom1.setMaterialSign("gerneral server");
        gbom1.setMaterialSignName("单节点机型");
        
        CpqdGbomDTO gbom2 = new CpqdGbomDTO();
        gbom2.setInstanceNo("ITEM002");
        gbom2.setMaterialSign("other server");
        gbom2.setMaterialSignName("复合机型");
        
        mixedGbomList.add(gbom1);
        mixedGbomList.add(gbom2);

        when(cpqdService.queryGbomList(any(CpqdQueryDTO.class))).thenReturn(mixedGbomList);

        // Act
        Whitebox.invokeMethod(service, "setMaterialSignInfo", mixedList);

        // Assert
        // 有效的物料编码应该设置materialSign
        assertEquals("gerneral server", mixedList.get(0).getMaterialSign());
        assertEquals("单节点机型", mixedList.get(0).getMaterialSignName());
        
        // 空的物料编码不应该设置materialSign
        assertNull(mixedList.get(1).getMaterialSign());
        assertNull(mixedList.get(1).getMaterialSignName());
        
        // 有效的物料编码应该设置materialSign
        assertEquals("other server", mixedList.get(2).getMaterialSign());
        assertEquals("复合机型", mixedList.get(2).getMaterialSignName());
        
        // null的物料编码不应该设置materialSign
        assertNull(mixedList.get(3).getMaterialSign());
        assertNull(mixedList.get(3).getMaterialSignName());
    }
}