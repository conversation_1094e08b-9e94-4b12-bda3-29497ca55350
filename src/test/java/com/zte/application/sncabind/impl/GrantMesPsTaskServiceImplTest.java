package com.zte.application.sncabind.impl;
/* Started by AICoder, pid:ze1a0p5243df037146150ba292dc951ecca07fe2 */

import com.google.common.collect.Lists;
import com.zte.application.PsTaskExtendedService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.sncabind.PsTaskService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.infrastructure.remote.PlanScheduleRemoteService;
import com.zte.interfaces.dto.PsTaskExtendedDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
public class GrantMesPsTaskServiceImplTest {

    @InjectMocks
    private GrantMesPsTaskServiceImpl service;

    @Mock
    private PsTaskExtendedService psTaskExtendedService;

    @Mock
    private PsTaskService psTaskService;

    @Mock
    private PlanScheduleRemoteService planScheduleRemoteService;

    @Mock
    private TransactionTemplate transactionTemplate;

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testFilter_whenValidInput_returnsSameList() {
        // Arrange
        List<PsTask> rows = Arrays.asList(new PsTask(), new PsTask());

        // Act
        List<PsTask> result = service.filter(rows);

        // Assert
        assertNotNull(result);
        assertEquals(rows, result);
    }

    @Test
    public void testProcess_whenValidInput_processesCorrectly() {
        // Arrange
        PsTask task1 = new PsTask();
        task1.setTaskNo("task1");
        task1.setOrgId(new BigDecimal(1));

        PsTask task2 = new PsTask();
        task2.setTaskNo("task2");
        task2.setOrgId(new BigDecimal(2));

        List<PsTask> filteredRows = Lists.newArrayList(task1);
        List<PsTask> allRows = Lists.newArrayList(task1, task2);

        PsTaskExtendedDTO extendedDTO = new PsTaskExtendedDTO();
        extendedDTO.setTaskNo("task1");
        extendedDTO.setFixBomComplete(Constant.Y_STATUS);

        when(psTaskExtendedService.listByTaskNos(any())).thenReturn(Collections.singletonList(extendedDTO));

        // Act
        service.process(filteredRows, allRows);

        // Assert
        verify(psTaskExtendedService).listByTaskNos(anyCollection());
    }

    @Test
    public void testGrant_whenEmptyPsTasks_logsWarning() {
        // Arrange
        Collection<PsTask> psTasks = Collections.emptyList();

        // Act
        ReflectionTestUtils.invokeMethod(service, "grant", psTasks);

        // Assert
        verify(psTaskService, never()).batchUpdateByPK(anyList());
        verify(planScheduleRemoteService, never()).syncPsTaskToSys(anyList());
    }

    @Test
    public void testGrant_whenValidPsTasks_updatesAndSyncs() {
        // Arrange
        PsTask task1 = new PsTask();
        task1.setTaskNo("task1");
        task1.setOrgId(new BigDecimal(1));

        PsTask task2 = new PsTask();
        task2.setTaskNo("task2");
        task2.setOrgId(new BigDecimal(2));

        Collection<PsTask> psTasks = Arrays.asList(task1, task2);

        SysLookupValues lookupValue1 = new SysLookupValues();
        lookupValue1.setRemark("1");
        lookupValue1.setLookupMeaning("1");

        SysLookupValues lookupValue2 = new SysLookupValues();
        lookupValue2.setRemark("2");
        lookupValue2.setLookupMeaning("2");

        List<SysLookupValues> sysLookupValues = Arrays.asList(lookupValue1, lookupValue2);

        when(sysLookupValuesService.findByLookupType(any())).thenReturn(sysLookupValues);
        // Act
        ReflectionTestUtils.invokeMethod(service, "grant", psTasks);

        // Assert
        verify(transactionTemplate).execute(any());
    }

    @Test
    public void testFilterRows_whenNoFixBomInfo_returnsEmptyList() {
        // Arrange
        PsTask task1 = new PsTask();
        task1.setTaskNo("task1");

        List<PsTask> filteredRows = Collections.singletonList(task1);

        when(psTaskExtendedService.listByTaskNos(any())).thenReturn(Collections.emptyList());

        // Act
        List<PsTask> result = ReflectionTestUtils.invokeMethod(service, "filterRows", filteredRows);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testFilterRows_whenValidFixBomInfo_filtersCorrectly() {
        // Arrange
        PsTask task1 = new PsTask();
        task1.setTaskNo("task1");

        PsTask task2 = new PsTask();
        task2.setTaskNo("task2");

        List<PsTask> filteredRows = Arrays.asList(task1, task2);

        PsTaskExtendedDTO extendedDTO1 = new PsTaskExtendedDTO();
        extendedDTO1.setTaskNo("task1");
        extendedDTO1.setFixBomComplete(Constant.Y_STATUS);

        PsTaskExtendedDTO extendedDTO2 = new PsTaskExtendedDTO();
        extendedDTO2.setTaskNo("task2");
        extendedDTO2.setFixBomComplete(Constant.N_STATUS);

        when(psTaskExtendedService.listByTaskNos(any())).thenReturn(Arrays.asList(extendedDTO1, extendedDTO2));

        // Act
        List<PsTask> result = ReflectionTestUtils.invokeMethod(service, "filterRows", filteredRows);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("task1", result.get(0).getTaskNo());
    }

    @Test
    public void testGetOrgIdToFactoryIdMap_returnsExpectedMap() {
        // Arrange
        SysLookupValues lookupValue1 = new SysLookupValues();
        lookupValue1.setRemark("org1");
        lookupValue1.setLookupMeaning("factory1");

        SysLookupValues lookupValue2 = new SysLookupValues();
        lookupValue2.setRemark("org2");
        lookupValue2.setLookupMeaning("factory2");

        List<SysLookupValues> sysLookupValues = Arrays.asList(lookupValue1, lookupValue2);

        when(sysLookupValuesService.findByLookupType(any())).thenReturn(sysLookupValues);

        // Act
        Map<String, String> result = ReflectionTestUtils.invokeMethod(service, "getOrgIdToFactoryIdMap");

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("factory1", result.get("org1"));
        assertEquals("factory2", result.get("org2"));
    }
}

/* Ended by AICoder, pid:ze1a0p5243df037146150ba292dc951ecca07fe2 */