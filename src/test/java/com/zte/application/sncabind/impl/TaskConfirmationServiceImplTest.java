package com.zte.application.sncabind.impl;
/* Started by AICoder, pid:2e32bn7b5bad6cc148800967e166e797f8454cc7 */

import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import com.zte.application.SysLookupValuesService;
import com.zte.common.enums.ConfirmationStatusEnum;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.domain.model.sncabind.PsTaskRepository;
import com.zte.infrastructure.remote.ProductionmgmtRemoteService;
import com.zte.interfaces.dto.WarehouseEntryInfoDTO;
import com.zte.interfaces.dto.aps.TaskCancelableDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({Convert.class})
public class TaskConfirmationServiceImplTest {

    @InjectMocks
    private TaskConfirmationServiceImpl service;

    @Mock
    private PsTaskRepository psTaskRepository;

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Mock
    private ProductionmgmtRemoteService productionmgmtRemoteService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(service, "focusLookupValues", Arrays.asList("7502003", "7502001"));
    }

    @Test
    public void testPreQueryForCancelConfirmation_whenValidInput_returnsExpectedResult() {
        // Arrange
        List<TaskCancelableDTO> taskCancelableDTOList = Collections.singletonList(new TaskCancelableDTO().setTaskNo("task1"));
        PsTask psTask = new PsTask();
        psTask.setTaskNo("task1");
        psTask.setType("TYPE1");
        psTask.setFactoryId(BigDecimal.ONE);
        psTask.setConfirmationStatus(ConfirmationStatusEnum.CONFIRMING.getCode());
        List<PsTask> psTaskList = Collections.singletonList(psTask);

        when(psTaskRepository.queryTaskList(anyString(), anyList())).thenReturn(psTaskList);

        // Act
        List<TaskCancelableDTO> result = service.preQueryForCancelConfirmation(taskCancelableDTOList);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("task1", result.get(0).getTaskNo());
    }

    @Test
    public void testIsCancelable_whenExcludedType_returnsFalse() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setType("FG_DISAS_2");
        psTask.setFactoryId(new BigDecimal("1"));

        // Act
        Boolean result = ReflectionTestUtils.invokeMethod(service, "isCancelable", psTask);

        // Assert
        assertEquals(Boolean.TRUE, result);
    }

    @Test
    public void testIsCancelable_whenNotExcludedTypeAndConfirming_returnsFalse() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setType("TYPE1");
        psTask.setConfirmationStatus(ConfirmationStatusEnum.CONFIRMING.getCode());
        psTask.setFactoryId(new BigDecimal("1"));

        // Act
        Boolean result = ReflectionTestUtils.invokeMethod(service, "isCancelable", psTask);

        // Assert
        assertNotEquals(Boolean.TRUE, result);
    }

    @Test
    public void testIsCancelable_whenNotExcludedTypeAndConfirmed_returnsFalse() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setType("TYPE1");
        psTask.setConfirmationStatus(ConfirmationStatusEnum.CONFIRMED.getCode());
        psTask.setFactoryId(new BigDecimal("1"));

        // Act
        Boolean result = ReflectionTestUtils.invokeMethod(service, "isCancelable", psTask);

        // Assert
        assertNotEquals(Boolean.TRUE, result);
    }

    @Test
    public void testIsCancelable_whenNotExcludedTypeAndOtherStatus_returnsTrue() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setType("TYPE1");
        psTask.setConfirmationStatus(ConfirmationStatusEnum.PENDING_CONFIRMATION.getCode());
        psTask.setFactoryId(new BigDecimal("1"));

        // Act
        Boolean result = ReflectionTestUtils.invokeMethod(service, "isCancelable", psTask);

        // Assert
        assertEquals(Boolean.TRUE, result);
    }

    @Test
    public void testProcessInboundFlag_whenEmptyWarehouseEntryInfo_doesNothing() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setTaskNo("task1");
        Collection<PsTask> psTasks = Collections.singletonList(psTask);
        psTask.setFactoryId(new BigDecimal("1"));

        // Act
        service.processInboundFlag(psTasks);

        // Assert
        verify(psTaskRepository, never()).queryTaskList(anyString(), anyList());
    }

    @Test
    public void testProcessInboundFlag_whenNonEmptyWarehouseEntryInfo_updatesPsTasks() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setTaskNo("task1");
        psTask.setFactoryId(new BigDecimal("1"));
        Collection<PsTask> psTasks = Collections.singletonList(psTask);
        WarehouseEntryInfoDTO warehouseEntryInfoDTO = new WarehouseEntryInfoDTO();
        warehouseEntryInfoDTO.setTaskNo("task1");
        warehouseEntryInfoDTO.setStatus(Constant.STR_0);
        warehouseEntryInfoDTO.setWarehouseCode("warehouse1");

        when(productionmgmtRemoteService.getWarehouseEntryInfoDTOListByTaskNos(anyString(), anySet()))
                .thenReturn(Collections.singletonList(warehouseEntryInfoDTO));
        PowerMockito.mockStatic(Convert.class);
        when(Convert.toStr(any())).thenReturn("1");

        // Act
        service.processInboundFlag(psTasks);

        // Assert
        assertTrue(psTask.isExistInboundBarcode());
    }

    @Test
    public void testProcessInboundFlag_whenNonEmptyWarehouseEntryInfo_updatesPsTask2() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setTaskNo("task1");
        psTask.setFactoryId(new BigDecimal("1"));
        Collection<PsTask> psTasks = Collections.singletonList(psTask);
        WarehouseEntryInfoDTO warehouseEntryInfoDTO = new WarehouseEntryInfoDTO();
        warehouseEntryInfoDTO.setTaskNo("task2");
        warehouseEntryInfoDTO.setStatus(Constant.STR_0);
        warehouseEntryInfoDTO.setWarehouseCode("warehouse1");

        when(productionmgmtRemoteService.getWarehouseEntryInfoDTOListByTaskNos(anyString(), anySet()))
                .thenReturn(Collections.singletonList(warehouseEntryInfoDTO));
        PowerMockito.mockStatic(Convert.class);
        when(Convert.toStr(any())).thenReturn("1");

        // Act
        service.processInboundFlag(psTasks);

        // Assert
        assertFalse(psTask.isExistInboundBarcode());
    }

    @Test
    public void testProcessInboundFlag_whenNonEmptyWarehouseEntryInfo_updatesPsTasks3() {
        // Arrange
        PsTask psTask = new PsTask();
        psTask.setTaskNo("task1");
        psTask.setFactoryId(new BigDecimal("1"));
        Collection<PsTask> psTasks = Collections.singletonList(psTask);
        WarehouseEntryInfoDTO warehouseEntryInfoDTO = new WarehouseEntryInfoDTO();
        warehouseEntryInfoDTO.setTaskNo("task1");
        warehouseEntryInfoDTO.setStatus(Constant.STR_0);
        warehouseEntryInfoDTO.setWarehouseCode("warehouse1");

        SysLookupValues sysLookupValue1 = new SysLookupValues();
        sysLookupValue1.setLookupCode(new BigDecimal("7502003"));
        sysLookupValue1.setAttribute2("warehouse1,warehouse2");
        SysLookupValues sysLookupValue2 = new SysLookupValues();
        sysLookupValue2.setLookupCode(new BigDecimal("7502001"));
        sysLookupValue2.setAttribute2("warehouse3");
        List<SysLookupValues> sysLookupValuesList = Arrays.asList(sysLookupValue1, sysLookupValue2);

        when(sysLookupValuesService.findByLookupType(Constant.LOOKUP_TYPE_7502)).thenReturn(sysLookupValuesList);
        when(productionmgmtRemoteService.getWarehouseEntryInfoDTOListByTaskNos(anyString(), anySet()))
                .thenReturn(Collections.singletonList(warehouseEntryInfoDTO));
        PowerMockito.mockStatic(Convert.class);
        when(Convert.toStr(any())).thenReturn("7502003");

        // Act
        service.processInboundFlag(psTasks);

        // Assert
        assertTrue(psTask.isExistInboundBarcode());
        assertEquals(ConfirmationStatusEnum.CONFIRMING.getCode(), psTask.getConfirmationStatus());
    }

    @Test
    public void testGetWarehouseCodeColl_returnsExpectedCodes() {
        // Arrange
        SysLookupValues sysLookupValue1 = new SysLookupValues();
        sysLookupValue1.setLookupCode(new BigDecimal("7502003"));
        sysLookupValue1.setAttribute2("warehouse1,warehouse2");
        SysLookupValues sysLookupValue2 = new SysLookupValues();
        sysLookupValue2.setLookupCode(new BigDecimal("7502001"));
        sysLookupValue2.setAttribute2("warehouse3");
        List<SysLookupValues> sysLookupValuesList = Arrays.asList(sysLookupValue1, sysLookupValue2);

        when(sysLookupValuesService.findByLookupType(Constant.LOOKUP_TYPE_7502)).thenReturn(sysLookupValuesList);
        PowerMockito.mockStatic(Convert.class);
        when(Convert.toStr(any())).thenReturn("7502003", "7502001");

        // Act
        List<String> result = ReflectionTestUtils.invokeMethod(service, "getWarehouseCodeColl");

        // Assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.contains("warehouse1"));
        assertTrue(result.contains("warehouse2"));
        assertTrue(result.contains("warehouse3"));
    }

    @Test
    public void testGetWarehouseEntryInfoDTOList_returnsExpectedList() {
        // Arrange
        Map<String, Set<String>> factoryIdGroup = MapUtil.of("factory1", new HashSet<>(Arrays.asList("task1", "task2")));
        WarehouseEntryInfoDTO warehouseEntryInfoDTO1 = new WarehouseEntryInfoDTO();
        warehouseEntryInfoDTO1.setTaskNo("task1");
        WarehouseEntryInfoDTO warehouseEntryInfoDTO2 = new WarehouseEntryInfoDTO();
        warehouseEntryInfoDTO2.setTaskNo("task2");

        when(productionmgmtRemoteService.getWarehouseEntryInfoDTOListByTaskNos(anyString(), anySet()))
                .thenReturn(Arrays.asList(warehouseEntryInfoDTO1, warehouseEntryInfoDTO2));

        // Act
        List<WarehouseEntryInfoDTO> result = ReflectionTestUtils.invokeMethod(service, "getWarehouseEntryInfoDTOList", factoryIdGroup);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(warehouseEntryInfoDTO1));
        assertTrue(result.contains(warehouseEntryInfoDTO2));
    }
}

/* Ended by AICoder, pid:2e32bn7b5bad6cc148800967e166e797f8454cc7 */