/*Started by AICoder, pid:j7beer58cfc493a144300b5c118a9b4058f862f2*/
package com.zte.application.sncabind.impl;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.domain.model.sncabind.PsTaskRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class PsTaskServiceImplTest2 {

    @Mock
    private PsTaskRepository psTaskRepository;

    @InjectMocks
    private PsTaskServiceImpl psTaskService;

    @Before
    public void setUp() {
        // Setup code if needed
    }

    @Test
    public void testSelectProdPlanIDBySpare_EmptyList() {
        List<String> input = Collections.emptyList();
        List<PsTask> expected = Collections.emptyList();

        List<PsTask> result = psTaskService.selectProdPlanIDBySpare(input,Collections.emptyList());

        assertEquals(expected, result);
    }

    @Test
    public void testSelectProdPlanIDBySpare_SingleElement() {
        List<String> input = Arrays.asList("1");
        List<PsTask> mockResult = Arrays.asList(new PsTask(), new PsTask());

        when(psTaskRepository.selectProdPlanIDWitnSpare(anyList(),anyList())).thenReturn(mockResult);

        List<PsTask> result = psTaskService.selectProdPlanIDBySpare(input,Collections.emptyList());

        assertEquals(mockResult, result);
        verify(psTaskRepository, times(1)).selectProdPlanIDWitnSpare(anyList(),anyList());
    }

    @Test
    public void testSelectProdPlanIDBySpare_MultipleElements() {
        List<String> input = Arrays.asList("1", "2", "3", "4", "5", "6", "7", "8", "9", "10");
        List<PsTask> mockResult1 = Arrays.asList(new PsTask(), new PsTask());

        when(psTaskRepository.selectProdPlanIDWitnSpare(anyList(),anyList()))
            .thenReturn(mockResult1);

        List<PsTask> result = psTaskService.selectProdPlanIDBySpare(input,Collections.emptyList());

        assertEquals(2, result.size());
    }
}
/*Ended by AICoder, pid:j7beer58cfc493a144300b5c118a9b4058f862f2*/