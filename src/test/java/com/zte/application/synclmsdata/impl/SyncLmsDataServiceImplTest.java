package com.zte.application.synclmsdata.impl;

import com.zte.application.impl.SysLookupValuesServiceImpl;
import com.zte.application.kafka.producer.SyncLmsBillStatusProducer;
import com.zte.application.kafka.producer.SyncLmsSnImuProducer;
import com.zte.application.sncabind.impl.PsTaskServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.datawb.BarSubmitDTO;
import com.zte.domain.model.datawb.BoardOnline;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.sncabind.dto.PsTaskDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.HttpRemoteUtil;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;

@RunWith(PowerMockRunner.class)
@PrepareForTest({DatawbRemoteService.class, MicroServiceRestUtil.class,Constant.class,RedisHelper.class,
        ConstantInterface.class, HttpRemoteUtil.class, CommonUtils.class, NumConstant.class})
public class SyncLmsDataServiceImplTest  extends TestCase {
    @InjectMocks
    SyncLmsDataServiceImpl service;

    @Mock
    private SysLookupValuesServiceImpl sysLookupValuesService;
    @Mock
    private PsTaskServiceImpl psTaskService;
    @Mock
    private SyncLmsBillStatusProducer syncLmsBillStatusProducer;
    @Mock
    private SyncLmsSnImuProducer syncLmsSnImuProducer;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Before
    public void init(){
        PowerMockito.mockStatic(RedisHelper.class);
    }

    @Test
    public void syncBillStatus() throws Exception {
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("2022-11-23 15:00:00");
        List<BarSubmitDTO> barSubmitList =new ArrayList<>();
        BarSubmitDTO barSubmitDTO = new BarSubmitDTO();
        barSubmitDTO.setBillNo("test123");
        barSubmitDTO.setProdplanId(new Long("8888777"));
        barSubmitDTO.setVerifyDate(new Date());
        barSubmitList.add(barSubmitDTO);
        List<PsTaskDTO> psTaskDTOs =new ArrayList<>();
        PsTaskDTO psTaskDTO= new PsTaskDTO();
        psTaskDTO.setProdplanId("8888777");
        psTaskDTO.setFactoryId(new BigDecimal("52"));
        psTaskDTOs.add(psTaskDTO);
        List<String> strings =new ArrayList<>();
        strings.add("8888777");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(sysLookupValues);
        PowerMockito.when(datawbRemoteService.getBarSubmitBatch(Mockito.anyMap())).thenReturn(barSubmitList);
        PowerMockito.when(psTaskService.getFactoryIdByProdplanId(Mockito.anyList())).thenReturn(psTaskDTOs);
        PowerMockito.when(sysLookupValuesService.updateSysLookupValuesById(Mockito.any())).thenReturn(1);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);

        service.syncBillStatus("00286523","52");
        PowerMockito.when(datawbRemoteService.getBarSubmitBatch(Mockito.anyMap())).thenReturn(null);
        service.syncBillStatus("00286523","52");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(null);
        service.syncBillStatus("00286523","52");
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        Assert.assertNotNull(service.syncBillStatus("00286523","52"));
    }

    @Test
    public void syncSnImu() throws Exception {
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("2022-11-23 15:00:00");
        List<SysLookupValues> imuDTOs = new ArrayList<>();
        SysLookupValues sysLookup = new SysLookupValues();
        sysLookup.setLookupMeaning("42");
        sysLookup.setDescriptionChin("42");
        imuDTOs.add(sysLookup);
        List<BoardOnline> boardOnlines =new ArrayList<>();
        BoardOnline boardOnline = new BoardOnline();
        boardOnline.setImuId(new BigDecimal("42"));
        boardOnline.setBoardSn(new BigDecimal("1"));
        boardOnline.setProdplanId(new BigDecimal("8888777"));
        boardOnline.setScanDate(new Date());
        boardOnlines.add(boardOnline);
        Page<BoardOnline> resultPage = new Page<BoardOnline>();
        resultPage.setRows(boardOnlines);
        resultPage.setTotalPage(1);
        List<PsTaskDTO> psTaskDTOs =new ArrayList<>();
        PsTaskDTO psTaskDTO= new PsTaskDTO();
        psTaskDTO.setProdplanId("8888777");
        psTaskDTO.setFactoryId(new BigDecimal("52"));
        psTaskDTOs.add(psTaskDTO);
        List<String> strings =new ArrayList<>();
        strings.add("8888777");

        PowerMockito.when(sysLookupValuesService.getList(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(imuDTOs);
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(sysLookupValues);
        PowerMockito.when(datawbRemoteService.getBoardOnlineBatch(Mockito.any())).thenReturn(resultPage);
        PowerMockito.when(psTaskService.getFactoryIdByProdplanId(Mockito.anyList())).thenReturn(psTaskDTOs);
        PowerMockito.when(sysLookupValuesService.updateSysLookupValuesById(Mockito.any())).thenReturn(1);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);

        service.syncSnImu("00286523");

        PowerMockito.when(sysLookupValuesService.getList(Mockito.any(),Mockito.anyString(),Mockito.anyString())).thenReturn(null);
        try{
            service.syncSnImu("00286523");
        }catch (Exception e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        PowerMockito.when(sysLookupValuesService.getList(Mockito.any(),Mockito.anyString(),Mockito.anyString())).thenReturn(imuDTOs);
        PowerMockito.when(datawbRemoteService.getBoardOnlineBatch(Mockito.any())).thenReturn(null);
        service.syncSnImu("00286523");

        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(null);
        service.syncSnImu("00286523");
        PowerMockito.when(psTaskService.getFactoryIdByProdplanId(Mockito.anyList())).thenReturn(null);
        service.syncSnImu("00286523");
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        service.syncSnImu("00286523");
    }
}