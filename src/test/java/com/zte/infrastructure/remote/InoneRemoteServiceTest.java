package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.SysLookupValuesService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.EdiSoSDTO;
import com.zte.interfaces.dto.datawb.PickListQueryDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.ArgumentMatchers.any;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, HttpClientUtil.class,
        MicroServiceRestUtil.class, InterfaceEnum.class, CommonUtils.class, ServiceDataBuilderUtil.class,
        JacksonJsonConverUtil.class})
public class InoneRemoteServiceTest extends TestCase {
    @InjectMocks
    private InoneRemoteService service;
    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Before
    public void init() {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
    }

    @Test
    public void testGetSendMaterials() {
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("1");
        sysLookupValues.setAttribute1("1");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(any())).thenReturn(sysLookupValues);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString())).thenReturn(JSON.toJSONString(new ServiceData() {{
        }}));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(JSONObject.toJSONString(""));
        Assert.assertNull(service.getPicklistMain(new PickListQueryDTO()));

    }
}