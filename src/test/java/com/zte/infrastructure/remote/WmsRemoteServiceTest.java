package com.zte.infrastructure.remote;
/* Started by AICoder, pid:941e30fc4awa93a14b4509c2001cc4988fb5279b */

import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.application.IMESLogService;
import com.zte.application.SysLookupValuesService;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.wms.WmsQueryItemQtyDTO;
import com.zte.interfaces.dto.wms.WmsQueryItemQtyResultDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

@PrepareForTest({WmsRemoteService.class, MESHttpHelper.class,HttpRemoteUtil.class,ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class})
public class WmsRemoteServiceTest extends BaseTestCase {

    @InjectMocks
    private WmsRemoteService wmsRemoteService;

    @Mock
    private IMESLogService imesLogService;

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Before
    public void setUp() {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        Map<String, String> header = new HashMap<>();
        header.put("appCode", "testAppCode");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
    }

    @Test
    public void testSelectWarehouseToWms_Success() throws Exception {
        // Mocking the required methods and objects
        WmsQueryItemQtyDTO reqDTO = new WmsQueryItemQtyDTO();
        String jsonReq = JSON.toJSONString(reqDTO);

        Map<String, String> header = new HashMap<>();
        header.put("appCode", "testAppCode");

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("http://testUrl");
        Mockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);

        String responseJson = "[{\"item\":\"item1\",\"qty\":10}]";
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.eq(jsonReq), Mockito.eq(header), Mockito.anyString(), Mockito.any()))
                .thenReturn(responseJson);

        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn(responseJson);

        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        List<WmsQueryItemQtyResultDTO> expectedList = null;
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.eq(responseJson), Mockito.any(TypeReference.class)))
                .thenReturn(expectedList);

        // Call the method to be tested
        List<WmsQueryItemQtyResultDTO> result = wmsRemoteService.selectWarehouseToWms(reqDTO);

        // Assert the results
//        assertNotNull(result);
        assertNull(result);
    }

    @Test(expected = MesBusinessException.class)
    public void testSelectWarehouseToWms_ThrowExceptionWhenGetSysLookupValuesDTOReturnNull() throws Exception {
        WmsQueryItemQtyDTO reqDTO = new WmsQueryItemQtyDTO();

        Mockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(null);

        wmsRemoteService.selectWarehouseToWms(reqDTO);
    }

    @Test(expected = MesBusinessException.class)
    public void testSelectWarehouseToWms_ThrowExceptionWhenGetRequestUriReturnNull() throws Exception {
        WmsQueryItemQtyDTO reqDTO = new WmsQueryItemQtyDTO();

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("");
        Mockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);

        wmsRemoteService.selectWarehouseToWms(reqDTO);
    }
}

/* Ended by AICoder, pid:941e30fc4awa93a14b4509c2001cc4988fb5279b */