package com.zte.infrastructure.remote;

import com.zte.common.model.MessageId;
import com.zte.itp.msa.util.encrypt.secure.SecureEncryptorUtils;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import static org.junit.Assert.assertNotNull;

@PrepareForTest({HttpRemoteUtil.class,SecureEncryptorUtils.class})
public class FilePreViewRemoteServiceTest extends BaseTestCase {
    @InjectMocks
    private FilePreViewRemoteService filePreViewRemoteService;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> valueOps;
    @Test
    public void init() throws Exception {
        PowerMockito.mockStatic(SecureEncryptorUtils.class);
        assertNotNull(filePreViewRemoteService);
    }
    @Test
    public void queryCrossInfo() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn("");
        PowerMockito.field(FilePreViewRemoteService.class, "accessKey").set(filePreViewRemoteService, "imes300_cloududm");
        PowerMockito.field(FilePreViewRemoteService.class, "accessSecret").set(filePreViewRemoteService, "e78e82fd8fa34bbe1d26359bcf9862de0191ab71e02de5d68d145e20a58405f5");
        PowerMockito.field(FilePreViewRemoteService.class, "baseUrl").set(filePreViewRemoteService, "http://test.idrive.zte.com.cn/zte-km-cloududm-docview/");
        PowerMockito.field(FilePreViewRemoteService.class, "viewUri").set(filePreViewRemoteService, "/api/docview/getPreviewUrl/v2");
        PowerMockito.field(FilePreViewRemoteService.class, "environment").set(filePreViewRemoteService, "DEV");
        try {
            filePreViewRemoteService.getPreViewUrl("", "", "", "");
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.FAILED_TO_GENERATE_PREVIEW_HEADER.equals(e.getExMsgId()));
        }
    }
        /*Started by AICoder, pid:hceeft4afai98c514af80b3490fb9c4f10c97734*/


        @Test(timeout = 8000, expected = MesBusinessException.class)
        public void testGetPreViewUrlWithEmptyFileDownloadUrl() throws MesBusinessException {
            String fileDownloadUrl = "";
            String fileId = "123";
            String fileName = "test.pdf";
            String empNo = "emp123";
            filePreViewRemoteService.getPreViewUrl(fileDownloadUrl, fileId, fileName, empNo);
        }

        @Test(timeout = 8000, expected = MesBusinessException.class)
        public void testGetPreViewUrlWithEmptyFileId() throws MesBusinessException {
            String fileDownloadUrl = "http://example.com/test.pdf";
            String fileId = "";
            String fileName = "test.pdf";
            String empNo = "emp123";
            filePreViewRemoteService.getPreViewUrl(fileDownloadUrl, fileId, fileName, empNo);
        }

        @Test(timeout = 8000, expected = MesBusinessException.class)
        public void testGetPreViewUrlWithEmptyFileName() throws MesBusinessException {
            String fileDownloadUrl = "http://example.com/test.pdf";
            String fileId = "123";
            String fileName = "";
            String empNo = "emp123";
            filePreViewRemoteService.getPreViewUrl(fileDownloadUrl, fileId, fileName, empNo);
        }

        @Test(timeout = 8000, expected = MesBusinessException.class)
        public void testGetPreViewUrlWithEmptyEmpNo() throws MesBusinessException {
            String fileDownloadUrl = "http://example.com/test.pdf";
            String fileId = "123";
            String fileName = "test.pdf";
            String empNo = "";
            filePreViewRemoteService.getPreViewUrl(fileDownloadUrl, fileId, fileName, empNo);
        }
        /*Ended by AICoder, pid:hceeft4afai98c514af80b3490fb9c4f10c97734*/
}

