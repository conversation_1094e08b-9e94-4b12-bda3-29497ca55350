package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.interfaces.dto.AiChatDTO;
import com.zte.interfaces.dto.AiChatResultDTO;
import com.zte.interfaces.dto.AiExportDTO;
import com.zte.interfaces.dto.ChatbotDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.constant.SysGlobalConst;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.BigExcelProcesser;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.After;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.whenNew;

/**
 * AiPlatformRemoteService Tester.
 *
 * <AUTHOR>
 * @version 1.0
 * @email <EMAIL>
 * @since <pre>12/07/2023</pre>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpClientUtil.class, ServiceDataBuilderUtil.class, HttpRemoteUtil.class, CommonUtils.class, HttpRemoteUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class})
public class AiPlatformRemoteServiceTest {

    @InjectMocks
    private AiPlatformRemoteService service;
    @Mock
    private HttpServletResponse httpServletResponse;
    @Mock
    private PrintWriter printWriter;
    @Mock
    BigExcelProcesser bigExcelProcesser;
    @Mock
    CloudDiskHelper cloudDiskHelper;

    @Mock
    MessagePlatformRemoteService messagePlatformRemoteService;

    @Before
    public void before() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        FieldUtils.writeField(service, "chatUrl", "https://rdcloud.zte.com.cn/zte-studio-ai-platform/info", true);
        FieldUtils.writeField(service, "datasetSearchUrl", "https://rdcloud.zte.com.cn/zte-studio-ai-platform/info", true);
        FieldUtils.writeField(service, "similarScore", 0F, true);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
    }

    @After
    public void after() throws Exception {
    }

    @Test(expected = Exception.class)
    public void testPostChat() throws Exception {
        datasetSearchCommon();
        PowerMockito.when(httpServletResponse.getWriter()).thenReturn(printWriter);
        String res = service.postChat(new AiChatDTO(), httpServletResponse);
        System.out.println(res);
        Assert.assertNotNull(res);
    }

    @Test(expected = Exception.class)
    public void testPostChat2() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("11");

        ServiceData<JSONArray> serviceData = new ServiceData<>();
        JSONObject data = new JSONObject();
        data.put("score", -1);
        JSONArray array = new JSONArray();
        array.add(data);
        serviceData.setBo(array);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyMap(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));

        PowerMockito.when(httpServletResponse.getWriter()).thenReturn(printWriter);
        String res = service.postChat(new AiChatDTO() {{
            setStream(false);
        }}, httpServletResponse);
        System.out.println(res);
        Assert.assertNotNull(res);
    }

    @Test(expected = Exception.class)
    public void testPostChat3() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString())).thenReturn("11");

        ServiceData<JSONArray> serviceData = new ServiceData<>();
        JSONObject data = new JSONObject();
        data.put("score", -1);
        JSONArray array = new JSONArray();
        array.add(data);
        serviceData.setBo(array);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyMap(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));

        PowerMockito.when(httpServletResponse.getWriter()).thenReturn(printWriter);
        String res = service.postChat(new AiChatDTO() {{
            setStream(true);
        }}, httpServletResponse);
        System.out.println(res);
        Assert.assertNotNull(res);
    }

    @Test(expected = MesBusinessException.class)
    public void testPostChat4() throws Exception {
        PowerMockito.when(httpServletResponse.getWriter()).thenThrow(IOException.class);
        String res = service.postChat(new AiChatDTO(), httpServletResponse);
        System.out.println(res);
        Assert.assertNotNull(res);
    }

    @Test(expected = MesBusinessException.class)
    public void postEventStream() throws Exception {
        PowerMockito.when(httpServletResponse.getWriter()).thenThrow(IOException.class);
        String res = service.postEventStream(null, null, null, httpServletResponse);
        System.out.println(res);
        Assert.assertNotNull(res);
    }

    private void datasetSearchCommon() {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn("");

        ServiceData<JSONArray> serviceData = new ServiceData<>();
        JSONObject data = new JSONObject();
        data.put("score", 1);
        JSONArray array = new JSONArray();
        array.add(data);
        serviceData.setBo(array);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyMap(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));
    }

    @Test()
    public void datasetSearch() throws Exception {
        datasetSearchCommon();
        boolean res = service.datasetSearch(new AiChatDTO(), httpServletResponse);
        Assert.assertEquals(res, true);
        ServiceData<JSONArray> serviceData = new ServiceData<>();
        JSONObject data = new JSONObject();
        data.put("score1", 1);
        JSONArray array = new JSONArray();
        array.add(data);
        serviceData.setBo(array);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyMap(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));
        try{
            service.datasetSearch(new AiChatDTO(), httpServletResponse);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        data.put("score", null);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyMap(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));
        try{
            service.datasetSearch(new AiChatDTO(), httpServletResponse);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void testGeneralExportAI() throws Exception {
        Assert.assertEquals("", service.generalExportAI(new AiExportDTO()));
        AiExportDTO aiExportDTO = new AiExportDTO();
        aiExportDTO.setTable("1");
        Assert.assertEquals("", service.generalExportAI(aiExportDTO));
        aiExportDTO.setTable("123\n123\n123\n");
        service.generalExportAI(aiExportDTO);
    }

    /* Started by AICoder, pid:o85bapf7a3b02c614e400ae29010913d6841d034 */
    @Test
    public void getAiChatResultTest() throws Exception {
        // Mock HttpRemoteUtil.remoteExeFoExternal的返回值
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), anyMap(), anyString(), any())).thenReturn("AI回复");
        // Mock HttpServletRequest的返回值
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("AI回复");
        AiChatResultDTO aiChatResultDTO = new AiChatResultDTO();
        aiChatResultDTO.setResult("AI回复");
        PowerMockito.when(JacksonJsonConverUtil.jsonToBean("AI回复", AiChatResultDTO.class)).thenReturn(aiChatResultDTO);
        service.getAiChatResult("appId", "apiKey", "AI回复");
        PowerMockito.when(JacksonJsonConverUtil.jsonToBean("AI回复", AiChatResultDTO.class)).thenReturn(new AiChatResultDTO());
        service.getAiChatResult("appId", "apiKey", "AI回复");
        // 测试 AI 回复为空的情况
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn(" ");
        try {
            service.getAiChatResult("appId", "apiKey", "AI回复");
            fail("预期会抛出 MesBusinessException，但未抛出");
        } catch (MesBusinessException e) {
            assertEquals(MessageId.AI_QUESTION_NOT_FOUND, e.getExMsgId());
        }
        // 测试 HttpRemoteUtil 抛出异常的情况
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), anyMap(), anyString(), any())).thenThrow(new RuntimeException("模拟异常"));
        try {
            service.getAiChatResult("appId", "apiKey", "AI回复");
            fail("预期会抛出 Exception，但未抛出");
        } catch (Exception e) {
            assertTrue(e.getMessage().contains("模拟异常"));
        }
    }
    /* Ended by AICoder, pid:o85bapf7a3b02c614e400ae29010913d6841d034 */

}
