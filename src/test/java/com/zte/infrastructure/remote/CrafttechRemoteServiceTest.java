package com.zte.infrastructure.remote;

import com.zte.common.utils.MpConstant;
import com.zte.interfaces.dto.BSProcess;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import junit.framework.TestCase;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpClientUtil.class, JacksonJsonConverUtil.class, MpConstant.class})
public class CrafttechRemoteServiceTest extends TestCase {
    @InjectMocks
    private CrafttechRemoteService crafttechRemoteService;

    public void testGetProcessInfo() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(MpConstant.class);

        List<BSProcess> bsProcessList=new ArrayList<>();
        BSProcess bsProcess=new BSProcess();
        bsProcess.setProcessCode("S1015");
        bsProcess.setProcessName("出库");
        bsProcessList.add(bsProcess);
        String basicUrl="http://10.5.129.208";
        String result="{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"processId\":\"9e93813f-56f4-4b94-b757-184a3cea29a3\",\"processCode\":\"P0004\",\"processType\":\"手工测试\",\"xType\":\"子工序\",\"processName\":\"维修完毕\",\"toolType\":null,\"processControlGroup\":null,\"isPrintTempLabel\":null,\"isDictionaryConfigProcess\":null,\"isDeliverProcess\":null,\"isFailScan\":null,\"remark\":null,\"createBy\":null,\"processSeq\":null,\"currProcess\":null,\"createDate\":\"2018-01-16 15:41:18\",\"lastUpdatedBy\":\"10207212\",\"lastUpdatedDate\":\"2017-11-02 19:21:22\",\"orgId\":null,\"entityId\":2,\"factoryId\":52,\"enabledFlag\":\"Y\",\"attribute1\":null,\"attribute2\":null,\"attribute3\":null,\"attribute4\":null,\"attribute5\":null,\"craftSection\":\"维修\",\"processControlGroupName\":null,\"jigBinding\":null,\"jigDownline\":null,\"wareHousing\":null,\"sourceSys\":null,\"bimuId\":null,\"processControl\":null,\"controlAddress\":null,\"syncAddress\":null,\"qtyControl\":\"1\",\"scanByStation\":null}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.BSProcessController@getList\",\"code\":\"0000\",\"costTime\":\"520ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Thu May 05 08:47:01 CST 2022\",\"tag\":\"查询工序信息\",\"serviceName\":\"zte-mes-manufactureshare-crafttechsys\",\"userId\":\"10260525\"}}";
        PowerMockito.when(HttpClientUtil.httpGet(Mockito.any(),Mockito.anyMap(),Mockito.any())).thenReturn(result);
        PowerMockito.spy(JacksonJsonConverUtil.class);
        CrafttechRemoteService.getProcessInfo(basicUrl,new HashMap<>(),"");
    }
}