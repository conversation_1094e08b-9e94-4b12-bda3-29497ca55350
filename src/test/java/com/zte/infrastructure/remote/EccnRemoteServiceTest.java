package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.SysLookupValuesService;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.EccnControlInfoDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
@PrepareForTest({MESHttpHelper.class, HttpRemoteUtil.class})
public class EccnRemoteServiceTest extends TestCase {

    @InjectMocks
    private EccnRemoteService eccnRemoteService;

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Before
    public void init(){
        PowerMockito.mockStatic(MESHttpHelper.class, HttpRemoteUtil.class);
    }

    @Test
    public void getFirstOrderBatch() throws Exception {
        String itemNo = "046050500166";
        List<String> itemNos = new ArrayList<>();
        List<String> result = eccnRemoteService.getEccnItemNo(itemNos);
        Assert.assertEquals(true, result.isEmpty());

        itemNos.add(itemNo);
        List<SysLookupValues> sysLookupValues = new ArrayList<>();
        PowerMockito.when(sysLookupValuesService.selectValuesByType(Mockito.eq(Constant.ECCN_LOOK_UP)))
                .thenReturn(sysLookupValues);
        result = eccnRemoteService.getEccnItemNo(itemNos);
        Assert.assertEquals(true, result.isEmpty());

        SysLookupValues sysLookupValue = new SysLookupValues();
        sysLookupValue.setLookupCode(new BigDecimal(Constant.LUP_6100005));
        sysLookupValue.setLookupMeaning(Constant.FLAG_Y);
        sysLookupValues.add(sysLookupValue);
        result = eccnRemoteService.getEccnItemNo(itemNos);
        Assert.assertEquals(true, result.isEmpty());

        sysLookupValue = new SysLookupValues();
        sysLookupValue.setLookupCode(new BigDecimal(Constant.LUP_6100004));
        sysLookupValue.setLookupMeaning("http://eccn.zte.com.cn/zte-grc-eccn-eccnsearch/eccnApi/itemEccn");
        sysLookupValues.add(sysLookupValue);
        result = eccnRemoteService.getEccnItemNo(itemNos);
        Assert.assertEquals(true, result.isEmpty());

        sysLookupValue = new SysLookupValues();
        sysLookupValue.setLookupCode(new BigDecimal(Constant.LUP_6100001));
        sysLookupValue.setDescriptionChin("X-Auth-Value");
        sysLookupValue.setLookupMeaning("t-o-k-e-n");
        sysLookupValues.add(sysLookupValue);

        ServiceData serviceData = new ServiceData();
        JSONObject jsonObject = new JSONObject();
        List<EccnControlInfoDTO> list = new ArrayList<>();
        jsonObject.put(Constant.JSON_ROWS, list);
        serviceData.setBo(jsonObject);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        result = eccnRemoteService.getEccnItemNo(itemNos);
        Assert.assertEquals(false, result.isEmpty());

        EccnControlInfoDTO eccnControlInfoDTO = new EccnControlInfoDTO();
        eccnControlInfoDTO.setItemNo(itemNo);
        eccnControlInfoDTO.setComplianceFlg("YES");
        list.add(eccnControlInfoDTO);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        result = eccnRemoteService.getEccnItemNo(itemNos);
        Assert.assertEquals(true, result.isEmpty());

        eccnControlInfoDTO.setComplianceFlg(Constant.NO);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        result = eccnRemoteService.getEccnItemNo(itemNos);
        Assert.assertEquals(false, result.isEmpty());
    }
}
