package com.zte.infrastructure.remote;

import com.zte.interfaces.dto.mbom.MbomQueryDTO;
import com.zte.interfaces.dto.mbom.MbomResDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.km.udm.common.util.JacksonJsonConverUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.LinkedList;
import java.util.List;

/**
 * packageName com.zte.infrastructure.remote
 * <AUTHOR>
 * @version JDK 8
 * @date 2025/5/1
 */
@PrepareForTest({ServiceDataBuilderUtil.class,HttpClientUtil.class,JacksonJsonConverUtil.class, MESHttpHelper.class})
public class IccRemoteServiceTest extends BaseTestCase {
    @InjectMocks
    private IccRemoteService iccRemoteService;

    @Before
    public void init(){
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class,HttpClientUtil.class,JacksonJsonConverUtil.class, MESHttpHelper.class);
    }

    /* Started by AICoder, pid:982e2w21e9md9cc1481d094730b0d35694c7cbd3 */
    @Test
    public void queryMbomDetail(){
        MbomQueryDTO param = new MbomQueryDTO();
        List<MbomResDTO> mbomResDTOS = iccRemoteService.queryMbomDetail(param);
        Assert.assertTrue(CollectionUtils.isEmpty(mbomResDTOS));

        List<String> productNameList = new LinkedList<>();
        productNameList.add("123");
        param.setProductNameList(productNameList);
        iccRemoteService.queryMbomDetail(param);
    }
    /* Ended by AICoder, pid:982e2w21e9md9cc1481d094730b0d35694c7cbd3 */
}
