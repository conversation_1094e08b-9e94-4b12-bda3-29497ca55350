package com.zte.infrastructure.remote;
import com.zte.interfaces.dto.mbom.CpqdGbomDTO;
import com.zte.interfaces.dto.mbom.CpqdQueryDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.LinkedList;
import java.util.List;

/**
 * packageName com.zte.infrastructure.remote
 * <AUTHOR>
 * @version JDK 8
 * @date 2025/5/1
 */
@PrepareForTest({ServiceDataBuilderUtil.class, HttpClientUtil.class, JacksonJsonConverUtil.class, MESHttpHelper.class})
public class CpqdRemoteServiceTest extends BaseTestCase {
    @InjectMocks
    private CpqdRemoteService cpqdRemoteService;

    @Before
    public void init(){
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class,HttpClientUtil.class,JacksonJsonConverUtil.class, MESHttpHelper.class);
    }

    /* Started by AICoder, pid:neb31t5970fe9fc146530b0e307224228fa182c2 */
    @Test
    public void queryGbomList(){
        CpqdQueryDTO cpqdQueryDTO = new CpqdQueryDTO();
        List<CpqdGbomDTO> cpqdGbomDTOS = cpqdRemoteService.queryGbomList(cpqdQueryDTO);
        Assert.assertTrue(CollectionUtils.isEmpty(cpqdGbomDTOS));
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(new LinkedList<>());
        cpqdRemoteService.queryGbomList(cpqdQueryDTO);
    }

    /* Ended by AICoder, pid:neb31t5970fe9fc146530b0e307224228fa182c2 */
}
