package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.zte.application.SysLookupValuesService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.SupplierInfoDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2023-06-28 13:58
 */
@PrepareForTest({HttpRemoteUtil.class})
public class SupplierRemoteServiceTest extends BaseTestCase {
    @InjectMocks
    private SupplierRemoteService supplierRemoteService;

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Before
    public void init() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
    }

    @Test
    public void getSuppliersInfo() {
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(null);
        SupplierInfoDTO dto = new SupplierInfoDTO();
        try {
            supplierRemoteService.getSuppliersInfo(dto);
            Assert.fail();
        } catch (MesBusinessException e){
            assert MessageId.SYS_LOOK_NOT_CONFIG.equals(e.getExMsgId());
        }

        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(sysLookupValues);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData()));

        ServiceData ret = supplierRemoteService.getSuppliersInfo(dto);
        assert RetCode.SUCCESS_CODE.equals(ret.getCode().getCode());

        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(sysLookupValues);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn("");

        ret = supplierRemoteService.getSuppliersInfo(dto);
        assert RetCode.SUCCESS_CODE.equals(ret.getCode().getCode());
    }
    /*Started by AICoder, pid:34f52y940dk4d621470508e3809ff55b8608c0cc*/
    @Test(timeout = 8000, expected = MesBusinessException.class)
    public void testGetSuppliersInfo_SysLookupValuesIsNull() {
        SupplierInfoDTO dto = new SupplierInfoDTO();
        supplierRemoteService.getSuppliersInfo(dto);
    }

    @Test(timeout = 8000)
    public void testGetSuppliersInfo_HttpRemoteUtilReturnsEmptyString() {
        SysLookupValues record = new SysLookupValues();
        record.setLookupCode(new BigDecimal("2000000005001"));
        record.setLookupMeaning("http://test.com");
        when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(record);

        SupplierInfoDTO dto = new SupplierInfoDTO();
        ServiceData ret = supplierRemoteService.getSuppliersInfo(dto);
        assertNotNull(ret);
    }
    /*Ended by AICoder, pid:34f52y940dk4d621470508e3809ff55b8608c0cc*/
}
