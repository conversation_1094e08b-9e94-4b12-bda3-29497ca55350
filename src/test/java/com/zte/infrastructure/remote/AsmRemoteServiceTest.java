package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.interfaces.dto.bytedance.BoardRepairQueryDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * <AUTHOR>
 * @date 2023-06-28 13:58
 */
@PrepareForTest({JacksonJsonConverUtil.class, HttpRemoteUtil.class, JSON.class, ServiceDataBuilderUtil.class})
public class AsmRemoteServiceTest extends BaseTestCase {
    @InjectMocks
    private AsmRemoteService asmRemoteService;
    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private  JsonNode jsonNode;
    @Before
    public void init() throws Exception{
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(jsonNode);
        ReflectionTestUtils.setField(asmRemoteService, "asmStationLogUrl", "N");


    }

    @Test
    public void queryBoardStationMsg() {
        BoardRepairQueryDTO boardRepairQueryDTO = new BoardRepairQueryDTO();
        asmRemoteService.queryBoardStationMsg(boardRepairQueryDTO);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn("OK");
        asmRemoteService.queryBoardStationMsg(boardRepairQueryDTO);
        PowerMockito.when(jsonNode.get(Mockito.anyString())).thenReturn(jsonNode);
        Assert.assertNotNull(asmRemoteService.queryBoardStationMsg(boardRepairQueryDTO));
    }
}
