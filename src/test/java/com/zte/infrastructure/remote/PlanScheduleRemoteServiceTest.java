package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.SysLookupValuesService;
import com.zte.domain.model.MessageId;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.interfaces.dto.SynchronizeSpmDateDTO;
import com.zte.interfaces.dto.aps.ApsResponseDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.MESHttpHelper;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertThrows;
import static org.mockito.Matchers.any;

/**
 * <AUTHOR>
 * @date 2021/9/17
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({PlanScheduleRemoteService.class, HttpClientUtil.class, HttpRemoteService.class, BasicsettingRemoteService.class,
        MESHttpHelper.class, JacksonJsonConverUtil.class})
public class PlanScheduleRemoteServiceTest extends TestCase {
    @Mock
    SysLookupValuesService sysLookupValuesService;

    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;

    @InjectMocks
    private PlanScheduleRemoteService planScheduleRemoteService;

    @Test
    public void getPsTaskInfo() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpGet(any(), any(), any())).thenReturn(JSON.toJSONString(new ServiceData() {{
            setBo(new PsTask());
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
            }});
        }}));
        Assert.assertNotNull(PlanScheduleRemoteService.getPsTaskInfo("", "", new HashMap<>()));
    }

    @Test
    public void getPsTaskByProdPlanIdList() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any())).thenReturn(JSON.toJSONString(new ServiceData() {{
            setBo("");
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
            }});
        }}));
        List<String> prodplanIdList = new ArrayList<>();
        prodplanIdList.add("1");
        List<SysLookupValues> sysLookValues = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("55");
        sysLookupValues.setAttribute2("testCS");
        sysLookValues.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.anyString())).thenReturn(sysLookValues);
        PowerMockito.when(sysLookupValuesService.getAddressByFactoryId(Mockito.anyString())).thenReturn("2323");
        Assert.assertNull(planScheduleRemoteService.getPsTaskByProdPlanIdList("55", prodplanIdList));
    }

    @Test
    public void getPsTaskInfoByProdplanIdTest() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        BigDecimal factoryId = new BigDecimal(52);
        String prodplanId = "21323213";
        Map<String, Map<String, String>> map = new HashMap<>();
        Map<String, String> m = new HashMap<>();
        m.put("lookupMeaning", "52");
        m.put("attribute2", "213123");
        map.put("213", m);
        PowerMockito.when(BasicsettingRemoteService.getsysLookupTypeList(Mockito.anyString())).thenReturn(map);
        List<SysLookupValues> sysLookValues = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("52");
        sysLookupValues.setAttribute2("testCS");
        sysLookValues.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.anyString())).thenReturn(sysLookValues);
        ServiceData res = new ServiceData();
        res.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        PowerMockito.when(HttpRemoteService.remoteExe(Mockito.anyObject(), Mockito.anyMap(), Mockito.anyString(), Mockito.anyString())).thenReturn(JSON.toJSONString(res));
        PowerMockito.when(sysLookupValuesService.getAddressByFactoryId(Mockito.anyString())).thenReturn("2323");
        Assert.assertNull(planScheduleRemoteService.getPsTaskInfoByProdplanId(factoryId, prodplanId));
    }

    @Test
    public void delegateToLocalFactoryUpdateTest() throws Exception {
        BigDecimal factoryId = new BigDecimal("52");
        String address = "testUrl";
        String emp = "test";
        List<ApsResponseDTO> localFactoryTaskNos = new ArrayList<>();
        // 请求头获取，mock
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        ServiceData<List<ApsResponseDTO>> rt = new ServiceData<>();
        List<ApsResponseDTO> apsList = new ArrayList<>();
        ApsResponseDTO dto7 = new ApsResponseDTO();
        dto7.setProdplanNo("tasKNoNJ");
        dto7.setProdplanModifyNo("prodplanModifyNo");
        ApsResponseDTO dto8 = new ApsResponseDTO();
        dto8.setProdplanNo("tasKNoXA");
        dto8.setProdplanModifyNo("prodplanModifyNo");
        ApsResponseDTO dto9 = new ApsResponseDTO();
        dto9.setProdplanNo("tasKNoNotExit");
        dto9.setProdplanModifyNo("prodplanModifyNo");
        apsList.add(dto7);
        apsList.add(dto8);
        apsList.add(dto9);
        rt.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        rt.setBo(apsList);
        String msg = JSONObject.toJSONString(rt);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(msg);

        planScheduleRemoteService.delegateToLocalFactoryUpdate(factoryId, emp, address, localFactoryTaskNos);
        Assert.assertNotNull(planScheduleRemoteService.delegateToLocalFactoryValidate(factoryId, emp, address, localFactoryTaskNos));
    }

    @Test
    public void synchronizeSpmData() throws Exception {
        PowerMockito.mockStatic(MESHttpHelper.class, HttpClientUtil.class, JacksonJsonConverUtil.class);
        // 请求头获取，mock
        BigDecimal factoryId = new BigDecimal("52");
        String emp = "00286569";
        String address = "2";
        int optType = 1;
        List<SynchronizeSpmDateDTO> localFactorySpmDataList = new ArrayList<>();
        List<SysLookupValues> sysLookValues = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("52");
        sysLookupValues.setAttribute2("testCS");
        sysLookValues.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesService.getAddressByFactoryId(Mockito.anyString())).thenReturn("2323");
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.anyString())).thenReturn(sysLookValues);
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        try {
            planScheduleRemoteService.synchronizeSpmData(factoryId, emp, address, localFactorySpmDataList, optType);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());

        }
        optType = 2;
        try {
            planScheduleRemoteService.synchronizeSpmData(factoryId, emp, address, localFactorySpmDataList, optType);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());

        }
        optType = 3;
        try {
            planScheduleRemoteService.synchronizeSpmData(factoryId, emp, address, localFactorySpmDataList, optType);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());

        }
        optType = 4;
        try {
            planScheduleRemoteService.synchronizeSpmData(factoryId, emp, address, localFactorySpmDataList, optType);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());

        }
        optType = 5;
        try {
            planScheduleRemoteService.synchronizeSpmData(factoryId, emp, address, localFactorySpmDataList, optType);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());

        }

        optType = 6;
        try {
            planScheduleRemoteService.synchronizeSpmData(factoryId, emp, address, localFactorySpmDataList, optType);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());

        }
    }

    @Test
    public void dealPsTaskToLocalFactoryUpdate() throws Exception {
        try {
            planScheduleRemoteService.dealPsTaskToLocalFactoryUpdate(new BigDecimal("58"), "00286523", "",new ArrayList<>());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CUSTOMIZE_MSG, e.getMessage());
        }
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        ServiceData<List<ApsResponseDTO>> rt = new ServiceData<>();
        List<ApsResponseDTO> apsList = new ArrayList<>();
        ApsResponseDTO dto7 = new ApsResponseDTO();
        dto7.setProdplanNo("tasKNoNJ");
        dto7.setProdplanModifyNo("prodplanModifyNo");
        ApsResponseDTO dto8 = new ApsResponseDTO();
        dto8.setProdplanNo("tasKNoXA");
        dto8.setProdplanModifyNo("prodplanModifyNo");
        ApsResponseDTO dto9 = new ApsResponseDTO();
        dto9.setProdplanNo("tasKNoNotExit");
        dto9.setProdplanModifyNo("prodplanModifyNo");
        apsList.add(dto7);
        apsList.add(dto8);
        apsList.add(dto9);
        rt.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        rt.setBo(apsList);
        String msg = JSONObject.toJSONString(rt);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.any())).thenReturn(msg);

        Assert.assertNotNull(planScheduleRemoteService.dealPsTaskToLocalFactoryUpdate(new BigDecimal("58"), "00286523", "address",new ArrayList<>()));
    }

    /* Started by AICoder, pid:y2d3a46168u6ba4144ad0875a0d0f421ecf280dc */
    @Test
    public void testGetScheduleStartAndEndTime_Success() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        String basicUrl = "http://example.com";
        String taskNo = "task123";
        Map<String, String> headerParamsMap = new HashMap<>();
        headerParamsMap.put("headerKey", "headerValue");
        String resultJson = "{\"scheduleStartDate\":\"2023-10-01 12:00:00\",\"scheduleEndDate\":\"2023-10-02 12:00:00\"}";

        PowerMockito.when(HttpClientUtil.httpGet(any(), any(), any())).thenReturn(JSON.toJSONString(new ServiceData() {{
            setBo(resultJson);
            setCode(new RetCode() {{
                setCode(RetCode.SUCCESS_CODE);
            }});
        }}));

        Exception exception = assertThrows(Exception.class, () -> {
            PlanScheduleRemoteService.getScheduleStartAndEndTime(basicUrl, taskNo, headerParamsMap);
        });

        assertNotNull(exception);
    }
    /* Ended by AICoder, pid:y2d3a46168u6ba4144ad0875a0d0f421ecf280dc */
}
