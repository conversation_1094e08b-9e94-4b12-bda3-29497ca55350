package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSONObject;
import com.zte.common.utils.Constant;
import com.zte.domain.model.MdsResponse;
import org.apache.http.HttpEntity;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;

import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.doNothing;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpClients.class})
public class MdsRemoteService_uploadFmParamMesFileInfo_Test {

    @InjectMocks
    private MdsRemoteService mdsRemoteService;
    @Mock
    private CloseableHttpClient httpClient;
    @Mock
    private CloseableHttpResponse httpResponse;
    @Mock
    private StatusLine statusLine;
    @Mock
    private HttpEntity httpEntity;


    /* Started by AICoder, pid:of61eje530n68fb1484a08afb0cec87ed296cb35 */
    @Test
    public void testUploadFmParamMesFileInfo_NormalCase1() throws Exception {
        PowerMockito.mockStatic(HttpClients.class);
        when(HttpClients.createDefault()).thenReturn(httpClient);
        doNothing().when(httpResponse).close();
        PowerMockito.field(MdsRemoteService.class, "uploadFmParamMesFileInfoUrl")
                .set(mdsRemoteService, "uploadFmParamMesFileInfoUrl");
        MultipartFile multipartFile = new MockMultipartFile("test", "test", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "test".getBytes());
        // 模拟HTTP响应
        StatusLine statusLine = Mockito.mock(StatusLine.class);
        when(httpResponse.getStatusLine()).thenReturn(statusLine);
        when(statusLine.getStatusCode()).thenReturn(200);
        // 模拟HTTP响应
        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpResponse.getEntity()).thenReturn(httpEntity);
        // 模拟成功的JSON响应
        MdsResponse mdsResponse = new MdsResponse();
        MdsResponse.Code code = new MdsResponse.Code();
        code.setCode(Constant.SUCCESS);
        mdsResponse.setCode(code);
        mdsResponse.setBo("successResult");
        String jsonResponse = JSONObject.toJSONString(mdsResponse);
        when(httpEntity.getContent()).thenReturn(new ByteArrayInputStream(jsonResponse.getBytes()));
        String result = mdsRemoteService.uploadFmParamMesFileInfo(multipartFile, "type1", "task123");
        Assert.assertNotNull(result);
        Assert.assertEquals(result, "successResult");

    }

    @Test
    public void testUploadFmParamMesFileInfo_FailCase1() throws Exception {
        PowerMockito.mockStatic(HttpClients.class);
        when(HttpClients.createDefault()).thenReturn(httpClient);
        doNothing().when(httpResponse).close();
        PowerMockito.field(MdsRemoteService.class, "uploadFmParamMesFileInfoUrl")
                .set(mdsRemoteService, "uploadFmParamMesFileInfoUrl");
        MultipartFile multipartFile = new MockMultipartFile("test", "test", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "test".getBytes());
        // 模拟HTTP响应
        StatusLine statusLine = Mockito.mock(StatusLine.class);
        when(httpResponse.getStatusLine()).thenReturn(statusLine);
        when(statusLine.getStatusCode()).thenReturn(400);
        // 模拟HTTP响应
        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpResponse.getEntity()).thenReturn(httpEntity);
        // 模拟成功的JSON响应
        MdsResponse mdsResponse = new MdsResponse();
        MdsResponse.Code code = new MdsResponse.Code();
        code.setCode(Constant.SUCCESS);
        mdsResponse.setCode(code);
        mdsResponse.setBo("successResult");
        String jsonResponse = JSONObject.toJSONString(mdsResponse);
        when(httpEntity.getContent()).thenReturn(new ByteArrayInputStream(jsonResponse.getBytes()));
        assertThrows(RuntimeException.class, () -> {
            mdsRemoteService.uploadFmParamMesFileInfo(multipartFile, "type1", "task123");
        });

    }

    /* Ended by AICoder, pid:of61eje530n68fb1484a08afb0cec87ed296cb35 */
}