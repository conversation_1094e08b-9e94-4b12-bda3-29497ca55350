package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.SysLookupValuesService;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.CivControlInfoDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;

import static com.zte.common.utils.MpConstant.DEFAULT_EXPORT_CONTROL_METHOD;
import com.zte.interfaces.dto.ItemSplitInfoDTO;

@RunWith(PowerMockRunner.class)
@PrepareForTest({MESHttpHelper.class, HttpRemoteUtil.class})
public class IscpRemoteServiceTest extends TestCase {

    @InjectMocks
    private IscpRemoteService service;

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Before
    public void init(){
        PowerMockito.mockStatic(MESHttpHelper.class, HttpRemoteUtil.class);
    }

    /* Started by AICoder, pid:gb4594b1ccl9eef141a709d820f86115d0c89f18 */
    @Test
    public void checkCivItem() throws Exception {
        CivControlInfoDTO civControlInfoDTO = new CivControlInfoDTO();

        // 测试默认情况
        Whitebox.invokeMethod(service, "checkCivItem", civControlInfoDTO);
        Assert.assertNotNull(civControlInfoDTO);

        // 测试设置导出控制方法
        civControlInfoDTO.setExportcontrolmethod(MpConstant.DEFAULT_EXPORT_CONTROL_METHOD);
        Whitebox.invokeMethod(service, "checkCivItem", civControlInfoDTO);
        Assert.assertNotNull(civControlInfoDTO);

        // 测试设置导出控制方法名称
        civControlInfoDTO.setExportcontrolmethodName(MpConstant.DEFAULT_EXPORT_CONTROL_METHOD);
        Whitebox.invokeMethod(service, "checkCivItem", civControlInfoDTO);
        Assert.assertNotNull(civControlInfoDTO);
    }

    /* Ended by AICoder, pid:gb4594b1ccl9eef141a709d820f86115d0c89f18 */
    @Test
    public void getCivControlInfo() throws Exception {
        List<String> itemNos = new ArrayList<>();
        List<CivControlInfoDTO> result = service.getCivControlInfo(itemNos);
        Assert.assertEquals(true, result.isEmpty());

        itemNos.add("046050500166");
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("http://test.iscp.zte.com.cn/zte-scm-iscp-bff-service");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any()))
                .thenReturn(sysLookupValues);

        List<CivControlInfoDTO> infoDTOList = new ArrayList<>();
        ServiceData serviceData = new ServiceData();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(Constant.JSON_LIST, infoDTOList);
        serviceData.setBo(jsonObject);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSONObject.toJSONString(serviceData));

        result = service.getCivControlInfo(itemNos);
        Assert.assertEquals(true, result.isEmpty());

        CivControlInfoDTO dto = new CivControlInfoDTO();
        dto.setExportcontrolmethodName(DEFAULT_EXPORT_CONTROL_METHOD);
        infoDTOList.add(dto);
        sysLookupValues.setAttribute1("/external/item/ItemSplitInfo");
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        result = service.getCivControlInfo(itemNos);
        Assert.assertEquals(false, result.isEmpty());
    }


    /* Ended by AICoder, pid:gb4594b1ccl9eef141a709d820f86115d0c89f18 */
    @Test
    public void queryCivControlInfoList() throws Exception {
        List<String> itemNos = new ArrayList<>();
        List<CivControlInfoDTO> result = service.queryCivControlInfoList(itemNos);
        Assert.assertEquals(true, result.isEmpty());

        itemNos.add("046050500166");
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("http://test.iscp.zte.com.cn/zte-scm-iscp-bff-service");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any()))
                .thenReturn(sysLookupValues);

        List<CivControlInfoDTO> infoDTOList = new ArrayList<>();
        ServiceData serviceData = new ServiceData();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(Constant.JSON_LIST, infoDTOList);
        serviceData.setBo(jsonObject);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSONObject.toJSONString(serviceData));

        result = service.queryCivControlInfoList(itemNos);
        Assert.assertEquals(true, result.isEmpty());

        CivControlInfoDTO dto = new CivControlInfoDTO();
        dto.setExportcontrolmethodName(DEFAULT_EXPORT_CONTROL_METHOD);
        infoDTOList.add(dto);
        sysLookupValues.setAttribute1("/external/item/ItemSplitInfo");
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        result = service.queryCivControlInfoList(itemNos);
        Assert.assertEquals(false, result.isEmpty());
    }

    @Test
    public void queryAliControlInfoList() throws Exception {
        List<String> itemNos = new ArrayList<>();
        List<CivControlInfoDTO> result = service.queryAliControlInfoList(itemNos);
        Assert.assertEquals(true, result.isEmpty());

        itemNos.add("046050500166");
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("http://test.iscp.zte.com.cn/zte-scm-iscp-bff-service");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any()))
                .thenReturn(sysLookupValues);

        List<CivControlInfoDTO> infoDTOList = new ArrayList<>();
        ServiceData serviceData = new ServiceData();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(Constant.JSON_LIST, infoDTOList);
        serviceData.setBo(jsonObject);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSONObject.toJSONString(serviceData));

        result = service.queryAliControlInfoList(itemNos);
        Assert.assertEquals(true, result.isEmpty());

        CivControlInfoDTO dto = new CivControlInfoDTO();
        dto.setExportcontrolmethodName(DEFAULT_EXPORT_CONTROL_METHOD);
        infoDTOList.add(dto);
        sysLookupValues.setAttribute1("/external/item/ItemSplitInfo");
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        result = service.queryAliControlInfoList(itemNos);
        Assert.assertEquals(false, result.isEmpty());
    }

    @Test
    public void getItemSplitInfoByUuid() throws Exception {
        // 测试空列表情况
        List<String> itemUuidList = new ArrayList<>();
        List<ItemSplitInfoDTO> result = service.getItemSplitInfoByUuid(itemUuidList);
        Assert.assertEquals(true, result.isEmpty());

        // 测试正常情况
        itemUuidList.add("uuid1");
        itemUuidList.add("uuid2");
        
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("http://test.iscp.zte.com.cn/zte-scm-iscp-bff-service");
        sysLookupValues.setAttribute1("/external/item/split");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any()))
                .thenReturn(sysLookupValues);

        List<ItemSplitInfoDTO> infoDTOList = new ArrayList<>();
        ItemSplitInfoDTO dto1 = new ItemSplitInfoDTO();
        dto1.setItemNo("046050500166");
        dto1.setItemUuid("uuid1");
        dto1.setPackType("TAPE");
        dto1.setSupplierNo("SUP001");
        dto1.setSupplierName("供应商A");
        dto1.setBraidDirection("UP");
        dto1.setStatus("ACTIVE");
        dto1.setBrandNo("BRAND001");
        dto1.setBrandName("品牌A");
        dto1.setBrandStyle("STYLE001");
        infoDTOList.add(dto1);

        ItemSplitInfoDTO dto2 = new ItemSplitInfoDTO();
        dto2.setItemNo("046050500167");
        dto2.setItemUuid("uuid2");
        dto2.setPackType("REEL");
        dto2.setSupplierNo("SUP002");
        dto2.setSupplierName("供应商B");
        dto2.setBraidDirection("DOWN");
        dto2.setStatus("INACTIVE");
        dto2.setBrandNo("BRAND002");
        dto2.setBrandName("品牌B");
        dto2.setBrandStyle("STYLE002");
        infoDTOList.add(dto2);

        ServiceData serviceData = new ServiceData();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(Constant.JSON_LIST, infoDTOList);
        serviceData.setBo(jsonObject);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSONObject.toJSONString(serviceData));

        result = service.getItemSplitInfoByUuid(itemUuidList);
        Assert.assertEquals(false, result.isEmpty());
        Assert.assertEquals(2, result.size());
        Assert.assertEquals("046050500166", result.get(0).getItemNo());
        Assert.assertEquals("uuid1", result.get(0).getItemUuid());
        Assert.assertEquals("TAPE", result.get(0).getPackType());
        Assert.assertEquals("SUP001", result.get(0).getSupplierNo());
        Assert.assertEquals("供应商A", result.get(0).getSupplierName());
        Assert.assertEquals("UP", result.get(0).getBraidDirection());
        Assert.assertEquals("ACTIVE", result.get(0).getStatus());
        Assert.assertEquals("BRAND001", result.get(0).getBrandNo());
        Assert.assertEquals("品牌A", result.get(0).getBrandName());
        Assert.assertEquals("STYLE001", result.get(0).getBrandStyle());

        Assert.assertEquals("046050500167", result.get(1).getItemNo());
        Assert.assertEquals("uuid2", result.get(1).getItemUuid());
        Assert.assertEquals("REEL", result.get(1).getPackType());
        Assert.assertEquals("SUP002", result.get(1).getSupplierNo());
        Assert.assertEquals("供应商B", result.get(1).getSupplierName());
        Assert.assertEquals("DOWN", result.get(1).getBraidDirection());
        Assert.assertEquals("INACTIVE", result.get(1).getStatus());
        Assert.assertEquals("BRAND002", result.get(1).getBrandNo());
        Assert.assertEquals("品牌B", result.get(1).getBrandName());
        Assert.assertEquals("STYLE002", result.get(1).getBrandStyle());

        // 测试返回空列表的情况
        infoDTOList.clear();
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        result = service.getItemSplitInfoByUuid(itemUuidList);
        Assert.assertEquals(true, result.isEmpty());
    }

}
