package com.zte.infrastructure.remote;

import com.zte.itp.authorityclient.client.AuthorityClient;
import com.zte.itp.authorityclient.entity.output.ServiceData;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import static org.junit.Assert.*;

/**
 * <AUTHOR>
 * @Date 2024/11/12 上午11:21
 */
@PrepareForTest({AuthorityClient.class})
public class UppRemoteServiceTest extends BaseTestCase {

    @InjectMocks
    private UppRemoteService uppRemoteService;


    /* Started by AICoder, pid:q412dyf4fe78105144d30a1a40d1ac1d58b3587c */
    @Test
    public void getUserDataAuthByResourceId() {
        String empNo = "10307329";
        String token = "74407153473ecc84c00869360505c934";
        Long moduleId = 100241L;
        Long resourceId = 12126L;
        ServiceData userResources = new ServiceData<>();
        PowerMockito.mockStatic(AuthorityClient.class);
        PowerMockito.when(AuthorityClient.userConstraint(Mockito.any())).thenReturn(userResources);
        Assert.assertNotNull(uppRemoteService.getUserDataAuthByResourceId(empNo, token, moduleId, resourceId));
    }
    /* Ended by AICoder, pid:q412dyf4fe78105144d30a1a40d1ac1d58b3587c */
}