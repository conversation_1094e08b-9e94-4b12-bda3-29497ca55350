package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.interfaces.dto.CACertificateImportResultDTO;
import com.zte.interfaces.dto.EmSmtStencil;
import com.zte.interfaces.dto.FixtureInfoDetailDTO;
import com.zte.interfaces.dto.RemoteServiceRequestDTO;
import com.zte.interfaces.dto.SolderInfoDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.consts.SysConst;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * ClassName: OpenApiRemoteServiceTest
 * Description:
 *
 * <AUTHOR>
 * @date 2023/6/20 下午4:13
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({PlanScheduleRemoteService.class, HttpClientUtil.class, HttpRemoteService.class, BasicsettingRemoteService.class,
        MESHttpHelper.class, JacksonJsonConverUtil.class,JSON.class,MicroServiceRestUtil.class,ServiceDataBuilderUtil.class,
		HttpRemoteUtil.class, ConstantInterface.class,})
public class OpenApiRemoteServiceTest {
    @InjectMocks
    private OpenApiRemoteService service;

    @Mock
    private ConstantInterface constantInterface;
	@Before
	public void init(){
		MockitoAnnotations.initMocks(this);
		PowerMockito.mockStatic(HttpRemoteUtil.class);
	}
    @Test
    public void checkCACertificateStandardBarcodeTest() throws Exception {
		List<CACertificateImportResultDTO> caCertImpList = new ArrayList<>();
        service.checkCACertificateStandardBarcode(caCertImpList);
        caCertImpList.add(new CACertificateImportResultDTO());
        // 请求头获取，mock
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("x-emp-no", "dasdas");
        headerMap.put("x-factory-id", "52");
        PowerMockito.mockStatic(MESHttpHelper.class, HttpClientUtil.class);
        when(MESHttpHelper.getHttpRequestHeader()).thenReturn(headerMap);
        when(constantInterface.getUrl(Mockito.any())).thenReturn("url");
		assertNotNull(caCertImpList);
    }

	@Test
	public void testDeleteSolderOriginalInfo() throws Exception {
		SolderInfoDTO solderInfoDTO = new SolderInfoDTO();
		solderInfoDTO.setBarcode("test");
		solderInfoDTO.setToFactoryId("55");
		PowerMockito.mockStatic(JSON.class);
		PowerMockito.mockStatic(MESHttpHelper.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class);
		PowerMockito.mockStatic(JacksonJsonConverUtil.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		when(constantInterface.getUrl(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-openapi/SparePartsStorage/deleteSolderOriginalInfo");
		when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
		service.deleteSolderOriginalInfo(solderInfoDTO);
		assertNotNull(solderInfoDTO);
	}

	@Test
	public void testDeleteFixtureOriginalInfo() throws Exception {
		FixtureInfoDetailDTO fixtureInfoDetailDTO = new FixtureInfoDetailDTO();
		fixtureInfoDetailDTO.setBarcode("test");
		fixtureInfoDetailDTO.setToFactoryId("55");
		PowerMockito.mockStatic(JSON.class);
		PowerMockito.mockStatic(MESHttpHelper.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class);
		PowerMockito.mockStatic(JacksonJsonConverUtil.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		when(constantInterface.getUrl(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-openapi/SparePartsStorage/deleteFixtureOriginalInfo");
		when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
		service.deleteFixtureOriginalInfo(fixtureInfoDetailDTO);
		assertNotNull(fixtureInfoDetailDTO);
	}

	@Test
	public void testDeleteStencilsOriginalInfo() throws Exception {
		EmSmtStencil emSmtStencil = new EmSmtStencil();
		emSmtStencil.setStencilsCode("test");
		emSmtStencil.setToFactoryId("55");
		PowerMockito.mockStatic(JSON.class);
		PowerMockito.mockStatic(MESHttpHelper.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class);
		PowerMockito.mockStatic(JacksonJsonConverUtil.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		when(constantInterface.getUrl(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-openapi/SparePartsStorage/deleteStencilsOriginalInfo");
		when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
		service.deleteStencilsOriginalInfo(emSmtStencil);
		assertNotNull(emSmtStencil);
	}

	@Test
	public void queryRelPcbInfo() throws Exception {
		EmSmtStencil emSmtStencil = new EmSmtStencil();
		emSmtStencil.setStencilsCode("test");
		emSmtStencil.setToFactoryId("55");
		PowerMockito.mockStatic(JSON.class);
		PowerMockito.mockStatic(MESHttpHelper.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class);
		PowerMockito.mockStatic(JacksonJsonConverUtil.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		when(constantInterface.getUrl(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-openapi/SparePartsStorage/deleteStencilsOriginalInfo");
		when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
		assertNotNull(emSmtStencil);
	}

	@Test
	public void queryTaskInfo() throws Exception {
		EmSmtStencil emSmtStencil = new EmSmtStencil();
		emSmtStencil.setStencilsCode("test");
		emSmtStencil.setToFactoryId("55");
		PowerMockito.mockStatic(HttpRemoteUtil.class);
		PowerMockito.mockStatic(MESHttpHelper.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		when(constantInterface.getUrl(any())).thenReturn("http://10.5.209.128/zte-mes-manufactureshare-openapi/SparePartsStorage/deleteStencilsOriginalInfo");
		when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
		service.queryTaskInfo(new HashMap<>());
		assertNotNull(emSmtStencil);
		List<PsTask> taskList = new ArrayList<>();
		when(HttpRemoteUtil.remoteExe(anyString(),anyMap(),anyString(),anyString())).thenReturn(JSON.toJSONString(taskList));
		when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn(JSON.toJSONString(taskList));
		service.queryTaskInfo(new HashMap<>());
		assertNotNull(emSmtStencil);
		PsTask psTask = new PsTask();
		taskList.add(psTask);
		psTask.setItemNo("12545544444444444");
		psTask.setExternalType("externalType");
		taskList.add(psTask);
		when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn(JSON.toJSONString(taskList));
		service.queryTaskInfo(new HashMap<>());
		assertNotNull(emSmtStencil);
	}

	@Test
	public void updateLocalFactoryQty() throws Exception {
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		PsTask psTask = new PsTask();
		service.updateLocalFactoryQty(new PsTask());
		assertNotNull(psTask);
	}

	@Test
	public void getBasicInfoForOee() throws Exception {
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		PowerMockito.mockStatic(MESHttpHelper.class);
		PowerMockito.mockStatic(ConstantInterface.class);
		PowerMockito.mockStatic(HttpClientUtil.class);
		PowerMockito.mockStatic(JSON.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class);

		assertNotNull(service.getBasicInfoForOee("55", ""));
		assertNotNull(service.getBasicInfoForOee("55", "WORKSHOP"));
		when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any())).thenReturn("123");
		service.getBasicInfoForOee("55", "LINE");
	}
	/* Started by AICoder, pid:47e73a6bd0p79d11421b082db0e75319145865ae */

	@Test
	public void taskBomChangeableQueryDelegateTest() {
		String taskNo = "test";
		String factoryId = "52";
		String address = "add";
		PowerMockito.mockStatic(MESHttpHelper.class, HttpClientUtil.class);
		when(constantInterface.getUrl(Mockito.any())).thenReturn("url");
		when(HttpRemoteUtil.remoteExe(Mockito.anyString(), Mockito.any(), Mockito.any(),Mockito.any())).thenReturn(
				JSON.toJSONString(new ServiceData(){{
					setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
					setBo("result");
				}})
		);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		when(ServiceDataBuilderUtil.checkResponseSimpleBo(any())).thenReturn("result");
		String s = service.taskBomChangeableQueryDelegate(taskNo, factoryId, address);
		assertTrue("result".equals(s));
	}

	/* Ended by AICoder, pid:47e73a6bd0p79d11421b082db0e75319145865ae */

	/* Started by AICoder, pid:3ac73ub8acdf9e41464c0b2820d96b2b63d02b7c */
	@Test
	public void queryInfoTransferOrder() throws Exception {
		// 使用Set确保每个类只被模拟一次，避免重复调用
		Set<Class<?>> classesToMock = new HashSet<>(Arrays.asList(
				ServiceDataBuilderUtil.class,
				MESHttpHelper.class,
				ConstantInterface.class,
				HttpClientUtil.class,
				JSON.class,
				MicroServiceRestUtil.class,
				HttpRemoteUtil.class
		));

		for (Class<?> clazz : classesToMock) {
			PowerMockito.mockStatic(clazz);
		}

		// 调用实际方法并进行断言
		assertNotNull(service.queryInfoTransferOrder(new HashMap<>()));
	}

	/* Ended by AICoder, pid:3ac73ub8acdf9e41464c0b2820d96b2b63d02b7c */
	@Test
	public void testInvokeInternalServiceReturnColl_whenValidInput_returnsExpectedResult() throws Exception {
		// Arrange
		String factoryId = "factory1";
		Object params = new Object();
		TypeReference<List<String>> typeRef = new TypeReference<List<String>>() {};
		String jsonResult = "[\"item1\", \"item2\"]";

		PowerMockito.mockStatic(JacksonJsonConverUtil.class);
		when(JacksonJsonConverUtil.beanToJson(params)).thenReturn("{}");
		when(JacksonJsonConverUtil.jsonToListBean(jsonResult, typeRef)).thenReturn(Collections.singletonList("item1"));
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any())).thenReturn("[\"item1\", \"item2\"]");

		// Act
		Collection<String> result = service.invokeInternalServiceReturnColl(InterfaceEnum.getWarehouseEntryInfoDTOListByTaskNos, factoryId, params, typeRef);

		// Assert
		assertNotNull(result);
	}

	@Test
	public void testInvokeInternalServiceReturnColl_whenEmptyResponse_returnsEmptyList() throws Exception {
		// Arrange
		String factoryId = "factory1";
		Object params = new Object();
		TypeReference<List<String>> typeRef = new TypeReference<List<String>>() {};

		PowerMockito.mockStatic(JacksonJsonConverUtil.class);
		when(JacksonJsonConverUtil.beanToJson(params)).thenReturn("{}");
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);

		// Act
		Collection<String> result = service.invokeInternalServiceReturnColl(InterfaceEnum.getWarehouseEntryInfoDTOListByTaskNos, factoryId, params, typeRef);

		// Assert
		assertNotNull(result);
		assertTrue(result.isEmpty());
	}

	@Test
	public void testInvokeInternalServiceReturnBean_whenValidInput_returnsExpectedResult() throws Exception {
		// Arrange
		String factoryId = "factory1";
		Object params = new Object();
		Class<String> returnType = String.class;
		String jsonResult = "result";

		PowerMockito.mockStatic(JacksonJsonConverUtil.class);
		when(JacksonJsonConverUtil.beanToJson(params)).thenReturn("{}");
		when(JacksonJsonConverUtil.jsonToBean(jsonResult, returnType)).thenReturn("result");
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any())).thenReturn("result");

		// Act
		String result = service.invokeInternalServiceReturnBean(InterfaceEnum.getWarehouseEntryInfoDTOListByTaskNos, factoryId, params, returnType);

		// Assert
		assertNotNull(result);
	}

	@Test
	public void testInvokeInternalServiceReturnBean_whenEmptyResponse_returnsNull() throws Exception {
		// Arrange
		String factoryId = "factory1";
		Object params = new Object();
		Class<String> returnType = String.class;

		PowerMockito.mockStatic(JacksonJsonConverUtil.class);
		when(JacksonJsonConverUtil.beanToJson(params)).thenReturn("{}");
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		// Act
		String result = service.invokeInternalServiceReturnBean(InterfaceEnum.getWarehouseEntryInfoDTOListByTaskNos, factoryId, params, returnType);

		// Assert
		assertNull(result);
	}

	@Test
	public void testInvokeInternalService_whenValidInput_callsForwardingRequest() throws Exception {
		// Arrange
		String factoryId = "factory1";
		String params = "{}";
		InterfaceEnum interfaceEnum = InterfaceEnum.updateQtyByTaskNo;
		PowerMockito.mockStatic(MESHttpHelper.class);
		Map<String, String> expectedHeaders = MESHttpHelper.getHttpRequestHeader();
		expectedHeaders.put(SysConst.HTTP_HEADER_X_FACTORY_ID, factoryId);
		expectedHeaders.put("X-Mes-Bff", "iMes-bff-authorization");

		RemoteServiceRequestDTO serviceRequestDTO = new RemoteServiceRequestDTO();
		serviceRequestDTO.setRequestUrl(interfaceEnum.getUrl());
		serviceRequestDTO.setFactoryId(factoryId);
		serviceRequestDTO.setSendType(interfaceEnum.getReqType());
		serviceRequestDTO.setServiceName(interfaceEnum.getServiceName());
		serviceRequestDTO.setParams(params);

		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);

		// Act
		String result = service.invokeInternalService(interfaceEnum, factoryId, params);

		// Assert
		assertNull(result);
	}


	/*Started by AICoder, pid:g225aqe6c5nab48142730b8bc08a8e932b064154*/
	@Test
	public void testGetWipExtendInfoByFormSnList() {
		PowerMockito.mockStatic(MESHttpHelper.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		// Given
		List<String> snList = Arrays.asList("SN1", "SN2", "SN3");
		String factoryId = "F1";

		when(constantInterface.getUrl(InterfaceEnum.forwardingRequest)).thenReturn("http://mock-url");

		RemoteServiceRequestDTO requestDTO = new RemoteServiceRequestDTO();
		requestDTO.setRequestUrl("PM/getWipExtendInfoByFormSnList");
		requestDTO.setFactoryId(factoryId);
		requestDTO.setSendType(com.zte.microservice.name.MicroServiceNameEum.SENDTYPEPOST);
		requestDTO.setServiceName(com.zte.microservice.name.MicroServiceNameEum.PRODUCTIONMGMT);

		WipExtendIdentificationDTO dto = new WipExtendIdentificationDTO();
		dto.setFormType("2");
		dto.setFormSnList(snList.subList(0, 3));

		String jsonRequest = JSON.toJSONString(requestDTO);
		String jsonResponse = "[{\"formType\":\"2\", \"formSnList\":[\"SN1\", \"SN2\", \"SN3\"]}]";
		String bo = "[{\"formType\":\"2\", \"formSnList\":[\"SN1\", \"SN2\", \"SN3\"]}]";

		when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), eq(com.zte.microservice.name.MicroServiceNameEum.SENDTYPEPOST)))
				.thenReturn(jsonResponse);
		when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(jsonResponse)).thenReturn(bo);
		// When
		List<WipExtendIdentificationDTO> result = service.getWipExtendInfoByFormSnList(snList, factoryId);
		assertNotNull(result);


		when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(jsonResponse)).thenReturn("");
		result = service.getWipExtendInfoByFormSnList(snList, factoryId);
		assertNotNull(result);

		List<WipExtendIdentificationDTO> wipExtendIdentificationDTOList = JSON.parseArray(bo, WipExtendIdentificationDTO.class);
		when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(jsonResponse)).thenReturn(JSON.toJSONString(wipExtendIdentificationDTOList));

		// Then
		assertNotNull(result);
		assertEquals(0, result.size());

	}
	/*Ended by AICoder, pid:g225aqe6c5nab48142730b8bc08a8e932b064154*/
}
