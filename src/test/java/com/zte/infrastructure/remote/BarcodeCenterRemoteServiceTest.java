package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.zte.application.SysLookupValuesService;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.BarcodeExpandResponse;
import com.zte.interfaces.dto.BarcodeExpandVO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.powermock.api.mockito.PowerMockito.when;

@PrepareForTest({HttpRemoteUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class,MicroServiceRestUtil.class})
public class BarcodeCenterRemoteServiceTest extends BaseTestCase {

    @InjectMocks
    private BarcodeCenterRemoteService service;

    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private ObjectMapper objectMapper;
    @Mock
    private JsonNode jsonNode;
    @Mock
    private JsonNode codeNode;
    @Mock
    private JsonNode boNode;

    @Mock
    private JsonNode rowsNode;

    @Before
    public void init() {
        PowerMockito.mockStatic(HttpRemoteUtil.class,ServiceDataBuilderUtil.class);
        List<SysLookupValues> list = getSysLookupValues();
        when(sysLookupValuesService.selectValuesByType(any())).thenReturn(list);
    }

    @Test
    public void queryRegisterNumber(){
        BarcodeExpandResponse response = service.queryRegisterNumber("12", "11");
        Assert.assertTrue(Objects.nonNull(response));

        service.queryRegisterNumber("12", "11");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("{}");
        service.queryRegisterNumber("12", "11");

    }

    private List<SysLookupValues> getSysLookupValues() {
        return Lists.newArrayList(new SysLookupValues () {{
            setLookupCode(new BigDecimal(1004052001)); setLookupMeaning("http://dev.ibarcode.zte.com.cn/zte-iss-barcodecenter-barcode");
        }}, new SysLookupValues () {{
            setLookupCode(new BigDecimal(1004052002)); setLookupMeaning("10001");
        }}, new SysLookupValues () {{
            setLookupCode(new BigDecimal(1004052003)); setLookupMeaning("A0509777019518562304");
        }}, new SysLookupValues () {{
            setLookupCode(new BigDecimal(1004052004)); setLookupMeaning("9648B76C92DBA3BDAE028233271D80E0A6B10F416714E40C2FBCFF5FF48BF678");
        }}, new SysLookupValues () {{
            setLookupCode(new BigDecimal(1004052005)); setLookupMeaning("/barcode/update");
        }}, new SysLookupValues () {{
            setLookupCode(new BigDecimal(1004052006)); setLookupMeaning("/barcode/expandQuery");
        }}, new SysLookupValues () {{
            setLookupCode(new BigDecimal(1004052007)); setLookupMeaning("/barcode/barcodeQuery");
        }}, new SysLookupValues () {{
            setLookupCode(new BigDecimal(1004052008)); setLookupMeaning("/barcode/register");
        }});
    }

    @Test
    public void testBarcodeQuery() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class, ServiceDataBuilderUtil.class);
        List<String> barcodes = new ArrayList<>();
        barcodes.add("1234");
        service.barcodeQuery(null, barcodes);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn("1234");
        Assert.assertNull(service.barcodeQuery(null, barcodes));

    }

    @Test
    public void testQueryByTaskNos() throws JsonProcessingException {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class,HttpRemoteUtil.class);
        ServiceData rt = new ServiceData();
        rt.setBo("");
        try {
            service.getSnByBarCode("1", "1");
        }catch (MesBusinessException e){
            Assert.assertNotNull(e.getMessage());
        }
        rt.setBo("1");
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(rt));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("{}");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), any())).thenReturn(Collections.emptyList());
        try {
            service.getSnByBarCode("1","1");
        }catch (Exception e){
            Assert.assertTrue(true);
        }

    }

    /**
     * 测试getSnByBarCodeAndRcvNo方法 - 正常返回
     */
    @Test
    public void testGetSnByBarCodeAndRcvNo_success() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class);

        // Mock HttpRemoteUtil返回成功响应
        String mockResponse = "{\"code\":\"200\",\"bo\":\"{\\\"rows\\\":[{\\\"barcode\\\":\\\"SN001\\\"},{\\\"barcode\\\":\\\"SN002\\\"}]}\"}";
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn(mockResponse);

        // Mock ServiceDataBuilderUtil返回业务对象
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn("{\"rows\":[{\"barcode\":\"SN001\"},{\"barcode\":\"SN002\"}]}");

        // Mock JacksonJsonConverUtil返回BarcodeExpandResponse对象
        BarcodeExpandResponse barcodeExpandResponse = new BarcodeExpandResponse();
        List<BarcodeExpandVO> rows = new ArrayList<>();
        BarcodeExpandVO vo1 = new BarcodeExpandVO();
        vo1.setBarcode("SN001");
        BarcodeExpandVO vo2 = new BarcodeExpandVO();
        vo2.setBarcode("SN002");
        rows.add(vo1);
        rows.add(vo2);
        barcodeExpandResponse.setRows(rows);

        PowerMockito.when(JacksonJsonConverUtil.jsonToBean(any(), eq(BarcodeExpandResponse.class)))
                .thenReturn(barcodeExpandResponse);

        // 执行测试
        List<BarcodeExpandVO> result = service.getSnByBarCodeAndRcvNo("CARTON001", "RCV001");

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());
        Assert.assertEquals("SN001", result.get(0).getBarcode());
        Assert.assertEquals("SN002", result.get(1).getBarcode());
    }

    /**
     * 测试getSnByBarCodeAndRcvNo方法 - 返回空结果
     */
    @Test
    public void testGetSnByBarCodeAndRcvNo_emptyResult() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class);

        // Mock HttpRemoteUtil返回成功响应
        String mockResponse = "{\"code\":\"200\",\"bo\":\"{}\"}";
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn(mockResponse);

        // Mock ServiceDataBuilderUtil返回空业务对象
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString()))
                .thenReturn("");

        // 执行测试
        List<BarcodeExpandVO> result = service.getSnByBarCodeAndRcvNo("CARTON001", "RCV001");

        // 验证结果
        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testBarcodeQueryBatch_success() {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class);

        List<String> barcodes = Lists.newArrayList("BC001", "BC002");
        // mock ServiceDataBuilderUtil 返回 bo 字符串
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn("[{\"barcode\":\"BC001\"},{\"barcode\":\"BC002\"}]");

        // mock JacksonJsonConverUtil 返回对象列表
        BarcodeExpandVO vo1 = new BarcodeExpandVO();
        vo1.setBarcode("BC001");
        BarcodeExpandVO vo2 = new BarcodeExpandVO();
        vo2.setBarcode("BC002");
        List<BarcodeExpandVO> voList = Lists.newArrayList(vo1, vo2);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any(TypeReference.class)))
                .thenReturn(voList);

        // 由于service.barcodeQueryBatch内部会调用HttpRemoteUtil.remoteExeFoExternal，这里需要mock该静态方法，防止NPE
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn("{\"code\":\"200\",\"bo\":\"[{\\\"barcode\\\":\\\"BC001\\\"},{\\\"barcode\\\":\\\"BC002\\\"}]\"}");

        List<BarcodeExpandVO> result = service.barcodeQueryBatch(barcodes);

        Assert.assertNotNull(result);
        Assert.assertEquals(2, result.size());
        Assert.assertEquals("BC001", result.get(0).getBarcode());
        Assert.assertEquals("BC002", result.get(1).getBarcode());
    }

    @Test
    public void testBarcodeQueryBatch_paramEmpty() {
        List<BarcodeExpandVO> result = service.barcodeQueryBatch(new ArrayList<>());
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testBarcodeQueryBatch_boIsBlank() {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);

        List<String> barcodes = Lists.newArrayList("BC001", "BC002");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn("");

        // 由于service.barcodeQueryBatch内部会调用HttpRemoteUtil.remoteExeFoExternal，这里需要mock该静态方法，防止NPE
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn("{\"code\":\"200\",\"bo\":\"\"}");

        List<BarcodeExpandVO> result = service.barcodeQueryBatch(barcodes);

        Assert.assertNotNull(result);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void testBarcodeQueryBatch_boNotBlankButParseNull() {
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class, HttpRemoteUtil.class);

        List<String> barcodes = Lists.newArrayList("BC001", "BC002");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(any()))
                .thenReturn("[{\"barcode\":\"BC001\"}]");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(), any(TypeReference.class)))
                .thenReturn(null);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn("{\"code\":\"200\",\"bo\":\"\"}");
        List<BarcodeExpandVO> result = service.barcodeQueryBatch(barcodes);

        Assert.assertNull(result);
    }
}
