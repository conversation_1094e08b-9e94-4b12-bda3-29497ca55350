package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.BaBomHead;
import com.zte.domain.model.datawb.BaBomHeadDTO;
import com.zte.domain.model.datawb.BarSubmitDTO;
import com.zte.domain.model.datawb.BoardOnline;
import com.zte.interfaces.dto.BarcodeNetSignDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.EdiSoSDTO;
import com.zte.interfaces.dto.PartsbarScanInfoDTO;
import com.zte.interfaces.dto.PickListResultDTO;
import com.zte.interfaces.dto.ScanPartstoboardDTO;
import com.zte.interfaces.dto.SpSpecialityNalDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.interfaces.dto.datawb.EdiOrderStatusViewDTO;
import com.zte.interfaces.dto.datawb.PickListQueryDTO;
import com.zte.interfaces.dto.datawb.ProcPicklistDetail;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import junit.framework.TestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Matchers.any;
import static org.mockito.Mockito.mock;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class,HttpClientUtil.class,
        MicroServiceRestUtil.class, InterfaceEnum.class, CommonUtils.class,ServiceDataBuilderUtil.class,
        JacksonJsonConverUtil.class})
public class DatawbRemoteServiceTest extends TestCase {
    @InjectMocks
    private DatawbRemoteService service;
    @Mock
    private ConstantInterface constantInterface;

    /*Started by AICoder, pid:j9d93m874c9421b1406b0a8d416d198f0100ada5*/

    @Before
    public void  init(){
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
    }

    @Test
    public void queryProcPickDetailBatch() {
        List<ProcPicklistDetail> list = DatawbRemoteService.queryProcPickDetailBatch(null);
        Assert.assertTrue(CollectionUtils.isEmpty(list));
    }

    @Test
    public void queryTotalNotComplete() {
        PowerMockito.when(ServiceDataBuilderUtil.checkResponseSimpleBo(Mockito.any()))
                .thenReturn("1");
        List<EdiSoSDTO> ediList = DatawbRemoteService.queryTotalNotComplete(null);
        Assert.assertTrue(CollectionUtils.isEmpty(ediList));
    }

    @Test
    public void testSelectByPageReelHis_WithValidPage() {
        // Given
        Page<?> page = mock(Page.class);
        String msgResult = "{\"data\": [{\"id\": 1, \"name\": \"test\"}]}";

        // When
        ;

        // Then
        assertNull(service.selectByPageReelHis(page));
    }

    @Test
    public void testSelectByPageReelHis_WithEmptyPage() {
        // Given
        Page<?> page = mock(Page.class);
        String msgResult = "[]";

        // When

        // Then
        assertNull(service.selectByPageReelHis(page));
    }

    @Test
    public void testSelectByPageReelHis_WithNullPage() {
        // Given
        Page<?> page = null;
        String msgResult = "{\"data\": [{\"id\": 1, \"name\": \"test\"}]}";

        // When

        // Then
        assertNull(service.selectByPageReelHis(page));
    }
    /*Ended by AICoder, pid:j9d93m874c9421b1406b0a8d416d198f0100ada5*/

    @Test(timeout = 8000)
    public void testRepushCustormerLogDataFromMES_EmptyList() {
        List<CustomerDataLogDTO> groupList = new ArrayList<>();
        String url = "http://test.com";
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        service.repushCustormerLogDataFromMES(groupList, url);
        assertNotNull(url);
    }

    @Test(timeout = 8000)
    public void testRepushCustormerLogDataFromMES_NonEmptyList() {
        String url = "http://test.com";
        List<CustomerDataLogDTO> groupList = new LinkedList<>();
        CustomerDataLogDTO a1 = new CustomerDataLogDTO();
        groupList.add(a1);
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        service.repushCustormerLogDataFromMES(groupList, url);
        assertNotNull(url);
    }
    /*Ended by AICoder, pid:eab31m6862oc680148000b5e5171785b9a62ac70*/

    @Test
    public void getBoardOnlineBatch() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        BoardOnline paramsMap = new BoardOnline();
        paramsMap.setImuId(new BigDecimal("42"));
        List<BoardOnline> boardOnlines = new ArrayList<>();
        BoardOnline boardOnline = new BoardOnline();
        boardOnline.setImuId(new BigDecimal("42"));
        boardOnlines.add(boardOnline);
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":[]," +
                "\"other\":null}";
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://zte-mes-manufactureshare-datawbsys/kxStepBoardOnlineCtrl/selectBoardOnlineWithParam");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), Mockito.anyMap(), anyString(), anyString())).thenReturn(result);
        try {
            service.getBoardOnlineBatch(paramsMap);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.GET_BOARD_ONLINE_INFO_FAILED, e.getMessage());
        }
    }

    @Test
    public void getBarSubmitBatch() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("status", "1");
        List<BarSubmitDTO> barSubmitDTOS = new ArrayList<>();
        BarSubmitDTO barSubmitDTO = new BarSubmitDTO();
        barSubmitDTO.setStatus(new Long("1"));
        barSubmitDTOS.add(barSubmitDTO);
        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":[]," +
                "\"other\":null}";
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://zte-mes-manufactureshare-datawbsys/kxStepBarSubmitCtrl/selectBarSubmitWithParam");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), Mockito.anyMap(), anyString(), anyString())).thenReturn(result);
        try {
            service.getBarSubmitBatch(paramsMap);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.GET_BAR_SUBMIT_INFO_FAILED.equals(e.getMessage()));
        }
    }

    @Test
    public void getBomInfoByBomNoList() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class, CommonUtils.class,ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        List<String> list = new ArrayList<>();
        list.add("name");

        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":[]," +
                "\"other\":null}";
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://zte-mes-manufactureshare-datawbsys/baBomHeadCtrl/getBomInfoByBomNoList");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(null);
        service.getBomInfoByBomNoList(list);

        ServiceData<List<BaBomHeadDTO>> serviceData = new ServiceData<>();
        List<BaBomHeadDTO> boList = new ArrayList<>();
        BaBomHeadDTO dto = new BaBomHeadDTO();
        boList.add(dto);
        serviceData.setBo(boList);

        result = JSONObject.toJSONString(serviceData);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(JSONObject.toJSONString(boList));
        Assert.assertNotNull(service.getBomInfoByBomNoList(list));
    }

    @Test
    public void getBomInfoByBomNoListTwo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        List<String> list = new ArrayList<>();

        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":[]," +
                "\"other\":null}";
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://zte-mes-manufactureshare-datawbsys/baBomHeadCtrl/getBomInfoByBomNoList");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), Mockito.anyMap(), anyString(), anyString())).thenReturn(result);
        Assert.assertNotNull(service.getBomInfoByBomNoList(list));
    }

    @Test
    public void getItemNoByProdplanId() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class, HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        String result = "";
        Set<BigDecimal> prodSet = new HashSet<>();
        prodSet.add(new BigDecimal("6666"));
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://zte-mes-manufactureshare-datawbsys/model/getItemNoByProdplanId");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), Mockito.anyMap(), anyString(), anyString())).thenReturn(result);
        try {
            service.getItemNoByProdplanId(prodSet);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.QUERY_BOM_NO_NULL.equals(e.getMessage()));
        }
        result = "{\"code\":{\"code\":\"0005\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":[]," +
                "\"other\":null}";
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), Mockito.anyMap(), anyString(), anyString())).thenReturn(result);
        try {
            service.getItemNoByProdplanId(prodSet);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.QUERY_BOM_NO_NULL.equals(e.getMessage()));
        }
    }

    @Test
    public void getSemiWipExtFromSpm() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);

        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":[\"{\"prodplanId\":\"123123\"}\"]," +
                "\"other\":null}";

        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(anyString(),
                anyString(), anyString(), anyString(), Mockito.anyObject())).thenReturn(null);

        ScanPartstoboardDTO dto = new ScanPartstoboardDTO();
        PartsbarScanInfoDTO infoDTO = new PartsbarScanInfoDTO();
        service.getSemiWipExtFromSpm(dto, true);
        service.getMaterialWipExtFromSpm(infoDTO);
        ServiceData serviceData = new ServiceData();
        List<ScanPartstoboardDTO> dtoList = new ArrayList<>();
        serviceData.setBo(dtoList);
        serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        service.getSemiWipExtFromSpm(dto, false);
        List<PartsbarScanInfoDTO> dtoList1 = new ArrayList<>();
        infoDTO.setProdplanId("123");
        dtoList1.add(infoDTO);
        ServiceData serviceData1 = new ServiceData();
        serviceData1.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        serviceData1.setBo(dtoList1);
        service.getMaterialWipExtFromSpm(infoDTO);
        serviceData1.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.SUCCESS_MSGID));
        serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.SUCCESS_MSGID));
        service.getSemiWipExtFromSpm(dto, false);
        Assert.assertNull(service.getMaterialWipExtFromSpm(infoDTO));
    }
    @Test
    public void testRepushCustormerLogDataFromMES() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(),any(),any(),any(),any(),any())).thenReturn("123");
        String url = "/TEST/TEST";
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setTaskNo("1234");
        customerDataLogDTO.setSn("1234");
        List<CustomerDataLogDTO> customerDataLogDTOList = new ArrayList<>();
        customerDataLogDTOList.add(customerDataLogDTO);
        service.repushCustormerLogDataFromMES(customerDataLogDTOList, url);
        Assert.assertNotNull(customerDataLogDTOList);
    }
    @Test
    public void uploadResourceInfoToMes() {
        List<SpSpecialityNalDTO> resourceInfoDetailDTOList = new ArrayList<>();
        resourceInfoDetailDTOList.add(new SpSpecialityNalDTO(){{setScrambleCode("2");}});
        PowerMockito.mockStatic(HttpClientUtil.class,ServiceDataBuilderUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), Mockito.anyMap()))
                .thenReturn("{\n" +
                        "  \"code\": {\n" +
                        "    \"code\": \"0000\",\n" +
                        "    \"msgId\": \"RetCode.Success\",\n" +
                        "    \"msg\": \"操作成功\"\n" +
                        "  },\n" +
                        "  \"bo\": {\"list\":[]}\n" +
                        "}");
        service.uploadResourceInfoToMes(resourceInfoDetailDTOList,new HashMap<>());
        Assert.assertNotNull(resourceInfoDetailDTOList);
    }


    @Test
    public void getMacByResourceNumber() {
        List<SpSpecialityNalDTO> resourceInfoDetailDTOList = new ArrayList<>();
        resourceInfoDetailDTOList.add(new SpSpecialityNalDTO(){{setScrambleCode("2");}});
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{}}));
        service.getMacByResourceNumber(new ArrayList<>());
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap()))
                .thenReturn(JSON.toJSONString(new ServiceData(){{setBo(resourceInfoDetailDTOList);}}));
        Assert.assertNotNull(service.getMacByResourceNumber(new ArrayList<>()));
    }

    @Test
    public void updateCSRStatusFromMES(){
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{}}));
        service.updateCSRStatusFromMES(new CustomerDataLogDTO());
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        Assert.assertNotNull(customerDataLogDTO);
    }

    @Test
    public void updateOverAllUnitMeiTuanFromMES(){
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{}}));
        service.updateOverAllUnitMeiTuanFromMES(new CustomerDataLogDTO());
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        Assert.assertNotNull(customerDataLogDTO);
    }

    @Test
    public void updateFileLogMeiTuanFromMES(){
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{}}));
        service.updateFileLogMeiTuanFromMES(new CustomerDataLogDTO());
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        Assert.assertNotNull(customerDataLogDTO);
    }

    /*Started by AICoder, pid:p0dc70632e0676314c320823c0ef426a630837f4*/
    @Test
    public void testSelectBarAccSignForSchTaskNullBarcodeNetSignDTO() {
        BarcodeNetSignDTO barcodeNetSignDTO = null;
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{}}));
        List<BarcodeNetSignDTO> result = service.selectBarAccSignForSchTask(barcodeNetSignDTO);
        assertNull(result);

        List<BarcodeNetSignDTO> resultList = new ArrayList<>();
        resultList.add(new BarcodeNetSignDTO());
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{setBo(resultList);}}));
        List<BarcodeNetSignDTO> result1 = service.selectBarAccSignForSchTask(barcodeNetSignDTO);
        assertNull(result1);
    }
    /*Ended by AICoder, pid:p0dc70632e0676314c320823c0ef426a630837f4*/

    /* Started by AICoder, pid:y229b4d3f9cfe8914c0d085be09564284cf79ef3 */
    @Test
    public void updateMesInfoUploadLog(){
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{}}));
        service.updateMesInfoUploadLog(new CustomerDataLogDTO());
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        Assert.assertNotNull(customerDataLogDTO);
    }
    /* Ended by AICoder, pid:y229b4d3f9cfe8914c0d085be09564284cf79ef3 */

    @Test
    public void updateMesInfoUploadFailedLog(){
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{}}));
        service.updateMesInfoUploadFailedLog(new CustomerDataLogDTO());
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        Assert.assertNotNull(customerDataLogDTO);
    }

    /* Started by AICoder, pid:40733zce9fud81d147bf0bfd30835e74fbf8565b */
    @Test
    public void getSysLookupValuesList() {
        SysLookupValuesDTO sysLookupValuesDTO = null;
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{}}));
        List<SysLookupValuesDTO> result = service.getSysLookupValuesList(sysLookupValuesDTO);
        assertNull(result);

        List<SysLookupValuesDTO> resultList = new ArrayList<>();
        resultList.add(new SysLookupValuesDTO());
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{setBo(resultList);}}));
        List<SysLookupValuesDTO> result1 = service.getSysLookupValuesList(sysLookupValuesDTO);
        assertNull(result1);
    }
    /* Ended by AICoder, pid:40733zce9fud81d147bf0bfd30835e74fbf8565b */


    @Test
    public void getSubmitStatusBatch() throws Exception {
        List<String> prodPlanIdList = new LinkedList<>();
        prodPlanIdList.add("8899512");
        Assert.assertNull(service.getSubmitStatusBatch(prodPlanIdList));
    }

    @Test
    public void queryBomInfoByBomNoList() throws Exception {
        List<String> bomNoList = new LinkedList<>();
        Assert.assertNotNull(service.queryBomInfoByBomNoList(bomNoList));
        bomNoList.add("123");
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://zte-mes-manufactureshare-datawbsys/baBomHeadCtrl/queryBomInfoByBomNoList");
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{}}));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(JSONObject.toJSONString(""));
        List<BaBomHeadDTO> baBomHeadDTOS = service.queryBomInfoByBomNoList(bomNoList);
        Assert.assertNull(baBomHeadDTOS);

    }

    @Test
    public void getBomInfoByBomNoListAndCodeType() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, CommonUtils.class);
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        PowerMockito.mock(JacksonJsonConverUtil.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(anyString(),
                anyString(), anyString(), anyString(), Mockito.anyObject())).thenReturn(null);

        BaBomHead baBomHead = new BaBomHead();

        try {
            service.getBomInfoByBomNoListAndCodeType(baBomHead);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INTERFACE_CALL_ERROR,e.getMessage());
        }

        String result = "{\"code\":{\"code\":\"0000\",\"msgId\":\"test\",\"msg\":\"test\"},\"bo\":[\"{\"prodplanId\":\"123123\"}\"]," +
                "\"other\":null}";

        JsonNode jsonNode = Mockito.mock(JsonNode.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelectiveByObj(anyString(),
                anyString(), anyString(), anyString(), Mockito.anyObject()))
                .thenReturn(jsonNode);
        try {
            service.getBomInfoByBomNoListAndCodeType(baBomHead);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INTERFACE_CALL_ERROR,e.getMessage());
        }
        PowerMockito.when(jsonNode.get(MpConstant.JSON_BO)).thenReturn(jsonNode);
        try {
            service.getBomInfoByBomNoListAndCodeType(baBomHead);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INTERFACE_CALL_ERROR,e.getMessage());
        }
    }

    @Test
    public void testQueryPickListByTaskNos() {
        PickListQueryDTO queryDTO = new PickListQueryDTO();
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://zte-mes-manufactureshare-datawbsys/baBomHeadCtrl/queryBomInfoByBomNoList");
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{}}));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(JSONObject.toJSONString(""));
        Assert.assertNull(service.queryPickListByTaskNos(queryDTO));

    }

    @Test
    public void testGetMaterialsWarehouses() {
        List<String> billNos = new ArrayList<>();
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://zte-mes-manufactureshare-datawbsys/baBomHeadCtrl/queryBomInfoByBomNoList");
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{}}));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(JSONObject.toJSONString(""));
        Assert.assertNull(service.getMaterialsWarehouses(billNos));

    }

    @Test
    public void testQueryListByCondition() {
        EdiOrderStatusViewDTO record = new EdiOrderStatusViewDTO();
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://zte-mes-manufactureshare-datawbsys/baBomHeadCtrl/queryBomInfoByBomNoList");
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{}}));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(JSONObject.toJSONString(""));
        Assert.assertNull(service.queryStatusByCondition(record));

    }

    @Test
    public void testGetSendMaterials() throws Exception {
        EdiSoSDTO ediSoSDTO = new EdiSoSDTO();
        PowerMockito.when(constantInterface.getUrl(any())).thenReturn("http://zte-mes-manufactureshare-datawbsys/baBomHeadCtrl/queryBomInfoByBomNoList");
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), anyString(), Mockito.anyMap())).thenReturn(JSON.toJSONString(new ServiceData(){{}}));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn(JSONObject.toJSONString(""));
        Assert.assertNull(service.getSendMaterials(ediSoSDTO));

    }

    /* Started by AICoder, pid:b24d9ic3f0320e01408a0b03a09242207f908e8c */
    @Test
    public void testQueryMaterialOrderNoByTaskNo_Success() {
        String taskNo = "task123";
        String urlStatic = "http://example.com/api";
        String responseJson = "[{\"taskNo\":\"order123\"}]";

        PowerMockito.when(ConstantInterface.getUrlStatic(InterfaceEnum.queryMaterialOrderNoByTaskNo)).thenReturn(urlStatic);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseJson);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(responseJson)).thenReturn(responseJson);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(eq(responseJson), any(TypeReference.class))).thenReturn(Collections.singletonList(new PickListResultDTO()));
        List<PickListResultDTO> result = service.queryMaterialOrderNoByTaskNo(taskNo);

        assertEquals(1, result.size());
    }

    @Test
    public void testGetMainItemNo_Success() {
        List<String> itemNoList = Arrays.asList("ITEM001", "ITEM002", "ITEM003");
        String urlStatic = "http://example.com/api/getMainItemNo";
        String responseJson = "[\"MAIN001\",\"MAIN002\",\"MAIN003\"]";
        List<String> expectedResult = Arrays.asList("MAIN001", "MAIN002", "MAIN003");

        PowerMockito.when(ConstantInterface.getUrlStatic(InterfaceEnum.getMainItemNo)).thenReturn(urlStatic);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(), anyString(), any())).thenReturn(responseJson);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(responseJson)).thenReturn(responseJson);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(eq(responseJson), any(TypeReference.class))).thenReturn(expectedResult);
        
        List<String> result = DatawbRemoteService.getMainItemNo(itemNoList);

        Assert.assertNotNull(result);
        Assert.assertEquals(3, result.size());
        Assert.assertEquals("MAIN001", result.get(0));
        Assert.assertEquals("MAIN002", result.get(1));
        Assert.assertEquals("MAIN003", result.get(2));
    }
    /* Ended by AICoder, pid:b24d9ic3f0320e01408a0b03a09242207f908e8c */
}