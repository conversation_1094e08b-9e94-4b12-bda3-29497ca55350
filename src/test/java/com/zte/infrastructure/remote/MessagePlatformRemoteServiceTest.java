package com.zte.infrastructure.remote;

import com.zte.common.CommonUtils;
import com.zte.common.utils.Constant;
import com.zte.interfaces.dto.ChatbotDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.whenNew;

/**
 * <AUTHOR>
 * @Date 2024/9/5 下午4:58
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpClientUtil.class, ServiceDataBuilderUtil.class, HttpRemoteUtil.class, CommonUtils.class, HttpRemoteUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class})
public class MessagePlatformRemoteServiceTest {
    @InjectMocks
    private MessagePlatformRemoteService service;

    @Before
    public void before() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        FieldUtils.writeField(service, "chatBotUrl", "https://moaportal.test.zte.com.cn:15061/services/sendExtmsg/users/{senduri}", true);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
    }
    @Test
    public void sentToTheMessagingPlatformTest() throws Exception {
        ChatbotDTO chatbotDTO = new ChatbotDTO();
        ChatbotDTO.Data data = new ChatbotDTO.Data();
        data.setMsgBody("@机器人 问题内容");
        data.setRobotCName("机器人");
        data.setGroupID("123");
        data.setSenderCName("谢耀");
        data.setSender("10307329");
        data.setGroupID("123");
        chatbotDTO.setData(data);
        // Mock HttpRemoteUtil.remoteExeFoExternal的返回值
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), anyMap(), anyString(), any())).thenReturn("AI回复");
        service.sentToTheMessagingPlatform(chatbotDTO, "AI回复");
        // 异常情况测试
        whenNew(MessageDigest.class).withParameterTypes(String.class).withArguments(Constant.SHA_256).thenThrow(new NoSuchAlgorithmException());
        String resultInException = service.calculateVerifyCode("uri", "type", 123456789L, 100);
        assertNotNull(resultInException);
        service.sentToTheMessagingPlatform(chatbotDTO, "AI回复");
        data.setMsgBody("@机器人 春日阳光明媚，\u200C万物复苏，\u200C绿意盎然。\u200C漫步林间小道，\u200C鸟语花香，\u200C心情格外舒畅。\u200C远望群山连绵，\u200C近观溪水潺潺，\u200C自然之美令人陶醉。\u200C此情此景，\u200C不禁让人感叹大自然的鬼斧神工，\u200C心生敬畏。\u200C愿时光能缓，\u200C故人不散，\u200C共赏这良辰美景，\u200C留下美好回忆。\u200C岁月如歌，\u200C愿我们都能珍惜眼前人，\u200C把握当下，\u200C不负韶华。\u200C生活虽忙碌，\u200C但也要懂得适时放慢脚步，\u200C感受身边的美好，\u200C让心灵得到真正的放松和滋养。\u200C如此，\u200C方能以更加饱满的热情和精力，\u200C迎接每一个新的挑战和机遇。\u200C");
        String result1 = service.sentToTheMessagingPlatform(chatbotDTO, "AI回复");
        assertNotNull(result1);  // 断言结果不为空
        // 测试 HttpRemoteUtil 抛出异常的情况
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), anyMap(), anyString(), any())).thenThrow(new RuntimeException("模拟异常"));
        try {
            service.sentToTheMessagingPlatform(chatbotDTO, "AI回复");
            fail("预期会抛出 Exception，但未抛出");
        } catch (Exception e) {
            assertTrue(e.getMessage().contains("模拟异常"));
        }
    }
}