package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.application.SysLookupValuesService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.MdsResponse;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.*;
import com.zte.interfaces.dto.mds.MdsAccessTokenDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ReflectUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import junit.framework.TestCase;
import org.apache.http.HttpEntity;
import org.apache.http.StatusLine;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.doNothing;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, JacksonJsonConverUtil.class,
        MicroServiceRestUtil.class, InterfaceEnum.class, CommonUtils.class, ServiceDataBuilderUtil.class, MESHttpHelper.class, HttpClients.class})
public class MdsRemoteServiceTest extends TestCase {
    @InjectMocks
    private MdsRemoteService mdsRemoteService;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> valueOps;
    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private ObjectMapper mapperInstance;
    @Mock
    private JsonNode json;
    @Mock
    private CloseableHttpClient httpClient;
    @Mock
    private CloseableHttpResponse httpResponse;
    @Mock
    private StatusLine statusLine;
    @Mock
    private HttpEntity httpEntity;


    @Test
    public void getPreManuMDSProgrammingList() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MESHttpHelper.class,JacksonJsonConverUtil.class);
        SysLookupValues sysLookupValues =  new SysLookupValues();
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOps);
        PowerMockito.when( sysLookupValuesService.findByLookupCode(Mockito.any())).thenReturn(null);

        PowerMockito.when(valueOps.get(Mockito.anyString())).thenReturn("test123");
        try {
            mdsRemoteService.getPreManuMDSProgrammingList(new PreManuMDSProgrammingDTO());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when( sysLookupValuesService.findByLookupCode(Mockito.any())).thenReturn(sysLookupValues);
        try {
            mdsRemoteService.getPreManuMDSProgrammingList(new PreManuMDSProgrammingDTO());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupValues.setLookupMeaning("test123");
        List<PreManuMDSProgrammingDTO> reList = new ArrayList<>();
        String str = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":2}";
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any() , Mockito.any())).thenReturn(str);
        try {
            mdsRemoteService.getPreManuMDSProgrammingList(new PreManuMDSProgrammingDTO());
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
        PowerMockito.when(JacksonJsonConverUtil.getMapperInstance()).thenReturn(mapperInstance);
        PowerMockito.when(mapperInstance.readTree(Mockito.anyString())).thenReturn(json);
        PowerMockito.when(json.get(Mockito.any())).thenReturn(json);
        PowerMockito.when(json.asText()).thenReturn("0001");
        try {
            mdsRemoteService.getPreManuMDSProgrammingList(new PreManuMDSProgrammingDTO());
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
        PowerMockito.when(json.get(Mockito.any())).thenReturn(null);
        try {
            mdsRemoteService.getPreManuMDSProgrammingList(new PreManuMDSProgrammingDTO());
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
        PowerMockito.when(json.asText()).thenReturn(null);
        try {
            mdsRemoteService.getPreManuMDSProgrammingList(new PreManuMDSProgrammingDTO());
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }

    @Test
    public void getAoiTestData()  {
        String sn = "";
        List<MdsAoiTestDataDTO> result = mdsRemoteService.getAoiTestData(sn);
        Assert.assertTrue(result.isEmpty());

        sn = "777766600001";
        try {
            mdsRemoteService.getAoiTestData(sn);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }

        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any())).thenReturn(sysLookupValues);
        try {
            mdsRemoteService.getAoiTestData(sn);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }

        sysLookupValues.setLookupMeaning("getAoiDataUrl");
        PowerMockito.mockStatic(HttpRemoteUtil.class, ServiceDataBuilderUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("[]");
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOps);
        PowerMockito.when(valueOps.get(Mockito.anyString())).thenReturn("test123");
        result = mdsRemoteService.getAoiTestData(sn);
        Assert.assertTrue(result.isEmpty());

        result = mdsRemoteService.getAoiTestData(sn);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void getRepairInfoBySn()  {
        String sn = "";
        List<MdsRepairInfoResponseDTO> result = mdsRemoteService.getRepairInfoBySn(sn);
        Assert.assertTrue(result.isEmpty());

        sn = "777766600001";
        try {
            mdsRemoteService.getRepairInfoBySn(sn);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }

        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.any())).thenReturn(sysLookupValues);
        try {
            mdsRemoteService.getRepairInfoBySn(sn);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }

        sysLookupValues.setLookupMeaning("getAoiDataUrl");
        PowerMockito.mockStatic(HttpRemoteUtil.class, ServiceDataBuilderUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("[]");
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOps);
        PowerMockito.when(valueOps.get(Mockito.anyString())).thenReturn("test123");
        result = mdsRemoteService.getRepairInfoBySn(sn);
        Assert.assertTrue(result.isEmpty());

        result = mdsRemoteService.getRepairInfoBySn(sn);
        Assert.assertTrue(result.isEmpty());
    }

    @Test
    public void getAccessToken() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(MESHttpHelper.class,JacksonJsonConverUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOps);

        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        PowerMockito.when(sysLookupValuesService.selectValuesByType(Mockito.any())).thenReturn(null);
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupValues dto1 = new SysLookupValues();
        dto1.setLookupCode(new BigDecimal("6732001"));
        sysLookupValuesList.add(dto1);

        PowerMockito.when(valueOps.get(Mockito.anyString())).thenReturn("");
        PowerMockito.when(sysLookupValuesService.selectValuesByType(Mockito.any())).thenReturn(sysLookupValuesList);
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        dto1.setLookupMeaning("1");
        PowerMockito.when(sysLookupValuesService.selectValuesByType(Mockito.any())).thenReturn(sysLookupValuesList);
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupValues dto2 = new SysLookupValues();
        dto2.setLookupCode(new BigDecimal("6732003"));
        sysLookupValuesList.add(dto2);
        PowerMockito.when(sysLookupValuesService.selectValuesByType(Mockito.any())).thenReturn(sysLookupValuesList);
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        dto2.setLookupMeaning("1");
        PowerMockito.when(sysLookupValuesService.selectValuesByType(Mockito.any())).thenReturn(sysLookupValuesList);
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupValues dto3 = new SysLookupValues();
        dto3.setLookupCode(new BigDecimal("6732004"));
        sysLookupValuesList.add(dto3);
        PowerMockito.when(sysLookupValuesService.selectValuesByType(Mockito.any())).thenReturn(sysLookupValuesList);
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        dto3.setLookupMeaning("1");
        PowerMockito.when(sysLookupValuesService.selectValuesByType(Mockito.any())).thenReturn(sysLookupValuesList);
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        SysLookupValues dto4 = new SysLookupValues();
        dto4.setLookupCode(new BigDecimal("6732002"));
        sysLookupValuesList.add(dto4);
        PowerMockito.when(sysLookupValuesService.selectValuesByType(Mockito.any())).thenReturn(sysLookupValuesList);
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        dto4.setLookupMeaning("1");
        PowerMockito.when(sysLookupValuesService.selectValuesByType(Mockito.any())).thenReturn(sysLookupValuesList);
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DQAS_ERROR, e.getMessage());
        }
        ServiceData serviceData = new ServiceData<>();
        MdsAccessTokenDTO mdsAccessTokenDTO = new MdsAccessTokenDTO();
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any() , Mockito.any())).thenReturn(JSONObject.toJSONString(null));
        PowerMockito.when(JacksonJsonConverUtil.jsonToBean("null", MdsAccessTokenDTO.class))
                .thenReturn(null);
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DQAS_ERROR, e.getMessage());
        }
        PowerMockito.when(JacksonJsonConverUtil.jsonToBean("null", MdsAccessTokenDTO.class))
                .thenReturn(mdsAccessTokenDTO);
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DQAS_ERROR, e.getMessage());
        }
        mdsAccessTokenDTO.setExpiresIn(new Long("1000"));
        mdsAccessTokenDTO.setAssessToken("token");
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DQAS_ERROR, e.getMessage());
        }
        mdsAccessTokenDTO.setExpiresIn(new Long("6000"));
        try {
            mdsRemoteService.getAccessToken();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DQAS_ERROR, e.getMessage());
        }
    }

    /*Started by AICoder, pid:k7855j1c8aae2a714cf5080980ae6f888461ab82*/
    @Test(expected = MesBusinessException.class)
    public void testGetRepairInformation_ExceptionThrown() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class,ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
        List<String> barCodeList = Arrays.asList("SN1", "SN2", "SN3");

        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), anyMap(), anyString(), any())).thenReturn("{}");
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString())).thenReturn("");
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOps);
        PowerMockito.when(valueOps.get(Mockito.anyString())).thenReturn("token");
        mdsRemoteService.getRepairInformation(barCodeList);
    }

    @Test(expected = MesBusinessException.class)
    public void testGetRepairInformation_NonEmptyList() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class,ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOps);
        PowerMockito.when(valueOps.get(Mockito.anyString())).thenReturn("token");
        List<String> barCodeList = Arrays.asList("SN1", "SN2", "SN3");

        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), anyMap(), anyString(), any())).thenReturn("{\"data\":[{\"id\":\"1\"},{\"id\":\"2\"}]}");
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString())).thenReturn("{\"data\":[{\"id\":\"1\"},{\"id\":\"2\"}]}");
        when(JacksonJsonConverUtil.jsonToListBean(anyString(), any(TypeReference.class))).thenReturn(Arrays.asList(new MdsRepairInformationDTO(), new MdsRepairInformationDTO()));

        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        SysLookupValues dto1 = new SysLookupValues();
        dto1.setLookupCode(new BigDecimal("6732001"));
        sysLookupValuesList.add(dto1);

        PowerMockito.when(valueOps.get(Mockito.anyString())).thenReturn("");
        PowerMockito.when(sysLookupValuesService.selectValuesByType(Mockito.any())).thenReturn(sysLookupValuesList);
        List<MdsRepairInformationDTO> result = mdsRemoteService.getRepairInformation(barCodeList);

        assertEquals(2, result.size());
    }

    @Test
    public void testGetRepairInformation_EmptyList() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class,ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
        List<String> barCodeList = Collections.emptyList();
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOps);
        PowerMockito.when(valueOps.get(Mockito.anyString())).thenReturn("token");
        List<MdsRepairInformationDTO> result = mdsRemoteService.getRepairInformation(barCodeList);

        assertTrue(result.isEmpty());
    }

    /*Ended by AICoder, pid:k7855j1c8aae2a714cf5080980ae6f888461ab82*/


    @Test
    public void getRepairInformation() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class,ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
        List<String> barCodeList = Arrays.asList("SN1", "SN2", "SN3");
        String str = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":2}";
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any() , Mockito.any())).thenReturn(str);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOps);
        PowerMockito.when(valueOps.get(Mockito.anyString())).thenReturn("token");
        PowerMockito.field(MdsRemoteService.class, "queryRepairInfoUrl")
                .set(mdsRemoteService, "url");

        List<MdsRepairInformationDTO> list = new ArrayList<>();
        list.add(new MdsRepairInformationDTO());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn(JSON.toJSONString(list));
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(),any())).thenReturn(new ArrayList<>());
        mdsRemoteService.getRepairInformation(barCodeList);
        Assert.assertNotNull(list);

        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(),any())).thenReturn(list);
        mdsRemoteService.getRepairInformation(barCodeList);
        Assert.assertNotNull(list);
    }

    @Test(expected = MesBusinessException.class)
    public void querySspTaskInfoForAli() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class,ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
        List<String> barCodeList = Arrays.asList("SN1", "SN2", "SN3");
        String str = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":2}";
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any() , Mockito.any())).thenReturn(str);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOps);
        PowerMockito.when(valueOps.get(Mockito.anyString())).thenReturn("token");
        PowerMockito.field(MdsRemoteService.class, "queryRepairInfoUrl")
                .set(mdsRemoteService, "url");

        List<SspTaskInfoDTO> list = new ArrayList<>();
        list.add(new SspTaskInfoDTO());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn(JSON.toJSONString(list));
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(),any())).thenReturn(new ArrayList<>());
        ReflectUtil.setFieldValue(mdsRemoteService,"querySspTaskInfoForAliUrl","url");
        mdsRemoteService.querySspTaskInfoForAli(barCodeList);
        Assert.assertNotNull(list);

        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(),any())).thenReturn(list);
        mdsRemoteService.querySspTaskInfoForAli(barCodeList);
        Assert.assertNotNull(list);

        mdsRemoteService.querySspTaskInfoForAli(new ArrayList<>());
        Assert.assertNotNull(list);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
        mdsRemoteService.querySspTaskInfoForAli(new ArrayList<>());
        Assert.assertNotNull(list);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
        mdsRemoteService.querySspTaskInfoForAli(barCodeList);
        Assert.assertNotNull(list);
    }

    @Test
    public void feedbackOfProductionStationTestingInformation() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class,ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
        List<String> barCodeList = Arrays.asList("SN1", "SN2", "SN3");
        String str = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":2}";
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any() , Mockito.any())).thenReturn(str);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOps);
        PowerMockito.when(valueOps.get(Mockito.anyString())).thenReturn("token");
        PowerMockito.field(MdsRemoteService.class, "queryRepairInfoUrl")
                .set(mdsRemoteService, "url");

        List<MdsRepairInformationDTO> list = new ArrayList<>();
        list.add(new MdsRepairInformationDTO());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn(JSON.toJSONString(list));
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(),any())).thenReturn(new ArrayList<>());
        mdsRemoteService.feedbackOfProductionStationTestingInformation(barCodeList);
        Assert.assertNotNull(list);

        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(),any())).thenReturn(list);
        mdsRemoteService.feedbackOfProductionStationTestingInformation(barCodeList);
        Assert.assertNotNull(list);

        mdsRemoteService.feedbackOfProductionStationTestingInformation(new ArrayList<>());
        Assert.assertNotNull(list);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
        mdsRemoteService.feedbackOfProductionStationTestingInformation(new ArrayList<>());
        Assert.assertNotNull(list);
    }

    @Test
    public void feedbackCompleteMachineTestingGenerateFile() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class,ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class);
        List<String> barCodeList = Arrays.asList("SN1", "SN2", "SN3");
        String str = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":2}";
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any() , Mockito.any())).thenReturn(str);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOps);
        PowerMockito.when(valueOps.get(Mockito.anyString())).thenReturn("token");
        PowerMockito.field(MdsRemoteService.class, "queryRepairInfoUrl")
                .set(mdsRemoteService, "url");

        List<MdsRepairInformationDTO> list = new ArrayList<>();
        list.add(new MdsRepairInformationDTO());
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn(JSON.toJSONString(list));
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(),any())).thenReturn(new ArrayList<>());
        mdsRemoteService.feedbackCompleteMachineTestingGenerateFile("",new ArrayList<>(),false);
        Assert.assertNotNull(list);

        mdsRemoteService.feedbackCompleteMachineTestingGenerateFile("",barCodeList,false);
        Assert.assertNotNull(list);

        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(anyString(),any())).thenReturn(list);
        mdsRemoteService.feedbackCompleteMachineTestingGenerateFile("2",barCodeList,false);
        Assert.assertNotNull(list);

        mdsRemoteService.feedbackCompleteMachineTestingGenerateFile("2",barCodeList,false);
        Assert.assertNotNull(list);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
        mdsRemoteService.feedbackCompleteMachineTestingGenerateFile("2",barCodeList,false);
        Assert.assertNotNull(list);
    }


    /* Started by AICoder, pid:of61eje530n68fb1484a08afb0cec87ed296cb35 */
    @Test
    public void testUploadFmParamMesFileInfo_NormalCase1() throws Exception {
        PowerMockito.mockStatic(HttpClients.class);
        when(HttpClients.createDefault()).thenReturn(httpClient);
        doNothing().when(httpResponse).close();
        PowerMockito.field(MdsRemoteService.class, "uploadFmParamMesFileInfoUrl")
                .set(mdsRemoteService, "uploadFmParamMesFileInfoUrl");
        MultipartFile multipartFile = new MockMultipartFile("test", "test", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "test".getBytes());
        // 模拟HTTP响应
        StatusLine statusLine = Mockito.mock(StatusLine.class);
        when(httpResponse.getStatusLine()).thenReturn(statusLine);
        when(statusLine.getStatusCode()).thenReturn(200);
        // 模拟HTTP响应
        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpResponse.getEntity()).thenReturn(httpEntity);
        // 模拟成功的JSON响应
        MdsResponse mdsResponse = new MdsResponse();
        MdsResponse.Code code = new MdsResponse.Code();
        code.setCode(Constant.SUCCESS);
        mdsResponse.setCode(code);
        mdsResponse.setBo("successResult");
        String jsonResponse = JSONObject.toJSONString(mdsResponse);
        when(httpEntity.getContent()).thenReturn(new ByteArrayInputStream(jsonResponse.getBytes()));
        String result = mdsRemoteService.uploadFmParamMesFileInfo(multipartFile, "type1", "task123");
        Assert.assertNotNull(result);
        Assert.assertEquals(result, "successResult");

    }

    @Test
    public void testUploadFmParamMesFileInfo_FailCase1() throws Exception {
        PowerMockito.mockStatic(HttpClients.class);
        when(HttpClients.createDefault()).thenReturn(httpClient);
        doNothing().when(httpResponse).close();
        PowerMockito.field(MdsRemoteService.class, "uploadFmParamMesFileInfoUrl")
                .set(mdsRemoteService, "uploadFmParamMesFileInfoUrl");
        MultipartFile multipartFile = new MockMultipartFile("test", "test", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "test".getBytes());
        // 模拟HTTP响应
        StatusLine statusLine = Mockito.mock(StatusLine.class);
        when(httpResponse.getStatusLine()).thenReturn(statusLine);
        when(statusLine.getStatusCode()).thenReturn(400);
        // 模拟HTTP响应
        when(httpClient.execute(any(HttpPost.class))).thenReturn(httpResponse);
        when(httpResponse.getEntity()).thenReturn(httpEntity);
        // 模拟成功的JSON响应
        MdsResponse mdsResponse = new MdsResponse();
        MdsResponse.Code code = new MdsResponse.Code();
        code.setCode(Constant.SUCCESS);
        mdsResponse.setCode(code);
        mdsResponse.setBo("successResult");
        String jsonResponse = JSONObject.toJSONString(mdsResponse);
        when(httpEntity.getContent()).thenReturn(new ByteArrayInputStream(jsonResponse.getBytes()));
        assertThrows(RuntimeException.class, () -> {
            mdsRemoteService.uploadFmParamMesFileInfo(multipartFile, "type1", "task123");
        });

    }

    /* Ended by AICoder, pid:of61eje530n68fb1484a08afb0cec87ed296cb35 */

}