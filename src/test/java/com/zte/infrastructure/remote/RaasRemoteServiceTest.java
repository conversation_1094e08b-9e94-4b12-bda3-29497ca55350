package com.zte.infrastructure.remote;

import com.zte.interfaces.dto.RaasQueryDTO;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @date 2023-06-28 13:58
 */
@PrepareForTest({HttpRemoteUtil.class})
public class RaasRemoteServiceTest extends BaseTestCase {
    @InjectMocks
    private RaasRemoteService raasRemoteService;

    @Before
    public void init() {
    }

    /* Started by AICoder, pid:j0fe3ec97dj5e561416f0859f05d8514fa07952e */
    @Test
    public void queryRepairMethod() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn("123");

        RaasQueryDTO raasQueryDTO = new RaasQueryDTO();
        String result = raasRemoteService.queryRepairMethod(raasQueryDTO);
        Assert.assertTrue("123".equals(result));
    }
    /* Ended by AICoder, pid:j0fe3ec97dj5e561416f0859f05d8514fa07952e */

    /* Started by AICoder, pid:yb767ubbdag24721483a09c3e088cd1e65c8a23a */
    @Test
    public void queryModuleInfo() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
                .thenReturn("123");

        RaasQueryDTO raasQueryDTO = new RaasQueryDTO();
        String result = raasRemoteService.queryModuleInfo(raasQueryDTO);
        Assert.assertTrue("123".equals(result));
    }
    /* Ended by AICoder, pid:yb767ubbdag24721483a09c3e088cd1e65c8a23a */
}
