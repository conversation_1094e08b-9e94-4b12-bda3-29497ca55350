package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.zte.application.IMESLogService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.interfaces.dto.ApprovalCenterCallbackDealDTO;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.SplitReelIdDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.WipExtendIdentificationDTO;
import com.zte.interfaces.sncabind.dto.TestInfoDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class, CommonUtils.class, ServiceDataBuilderUtil.class})
public class ProductionmgmtRemoteServiceTest extends BaseTestCase {
    @InjectMocks
    private ProductionmgmtRemoteService service;
    @Mock
    private IMESLogService imesLogService;
    @Mock
    private SysLookupTypesRepository sysLookupTypesRepository;

    @Test
    public void callTheLocalFactoryToWriteTestRecords() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class);
        List<TestInfoDTO> testInfoDTOList = new ArrayList<>();
        TestInfoDTO testInfoDTO = new TestInfoDTO();
        testInfoDTO.setSn("777888900001");
        testInfoDTO.setFactoryId("55");
        testInfoDTOList.add(testInfoDTO);
        ServiceData<Integer> serviceData = new ServiceData<>();
        serviceData.setBo(1);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), any())).thenReturn(JSON.toJSONString(serviceData));
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://ip/zte-mes-manufactureshare-productionmgmtsys/testInfoBatchctrl/testDataOpeSrv");

        PowerMockito.when(HttpRemoteService.remoteExe(any(), anyMap(), anyMap(), anyString())).thenReturn(JSON.toJSONString(serviceData));
        List<SysLookupTypesDTO> valuesList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("55");
        valuesList.add(sysLookupTypesDTO);
        PowerMockito.when(sysLookupTypesRepository.getList(anyMap())).thenReturn(valuesList);
        service.callTheLocalFactoryToWriteTestRecords(testInfoDTOList, "");
        Assert.assertNotNull(testInfoDTOList);
    }
    @Test
    public void callTheLocalFactoryToWriteTestRecords2() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class);
        List<TestInfoDTO> testInfoDTOList = new ArrayList<>();
        TestInfoDTO testInfoDTO = new TestInfoDTO();
        testInfoDTO.setSn("777888900001");
        testInfoDTO.setFactoryId("55");
        testInfoDTOList.add(testInfoDTO);
        ServiceData<Integer> serviceData = new ServiceData<>();
        serviceData.setBo(1);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), any())).thenThrow(new RuntimeException("test"));
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://ip/zte-mes-manufactureshare-productionmgmtsys/testInfoBatchctrl/testDataOpeSrv");

        List<SysLookupTypesDTO> valuesList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("55");
        valuesList.add(sysLookupTypesDTO);
        PowerMockito.when(sysLookupTypesRepository.getList(anyMap())).thenReturn(valuesList);

        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("2");
        PowerMockito.doNothing().when(imesLogService).log(any(), any());
        service.callTheLocalFactoryToWriteTestRecords(testInfoDTOList, "");
        Assert.assertNotNull(testInfoDTOList);
    }

    @Test(expected = MesBusinessException.class)
    public void callTheLocalFactoryToWriteTestRecords3() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class);
        List<TestInfoDTO> testInfoDTOList = new ArrayList<>();
        TestInfoDTO testInfoDTO = new TestInfoDTO();
        testInfoDTO.setSn("777888900001");
        testInfoDTO.setFactoryId("55");
        testInfoDTOList.add(testInfoDTO);
        ServiceData<Integer> serviceData = new ServiceData<>();
        serviceData.setBo(1);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), any())).thenThrow(new RuntimeException("test"));
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://ip/zte-mes-manufactureshare-productionmgmtsys/testInfoBatchctrl/testDataOpeSrv");

        List<SysLookupTypesDTO> valuesList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("55");
        valuesList.add(sysLookupTypesDTO);
        PowerMockito.when(sysLookupTypesRepository.getList(anyMap())).thenReturn(valuesList);

        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("2");
        PowerMockito.doNothing().when(imesLogService).log(any(), any());
        Whitebox.setInternalState(service, "writeTestRecordExceptionEnabled", true);
        service.callTheLocalFactoryToWriteTestRecords(testInfoDTOList, "");
    }


    @Test
    public void updateOrInsertByPkcode() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        PkCodeInfo record = new PkCodeInfo();
        record.setPkCode("test");
        record.setFactoryId(new BigDecimal("52"));
        ServiceData<Integer> serviceData = new ServiceData<>();
        serviceData.setBo(1);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), any())).thenReturn(JSON.toJSONString(serviceData));
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://ip/zte-mes-manufactureshare-productionmgmtsys/pkCodeInfoCtrl/rdOwnMaterialsRegisterToLocal");

        PowerMockito.when(HttpRemoteService.remoteExe(any(), anyMap(), anyMap(), anyString())).thenReturn(JSON.toJSONString(serviceData));
        Assert.assertNotNull(service.updateOrInsertByPkcode(record, InterfaceEnum.updateOrInsertPkcodeInfo));
    }

    @Test
    public void splitReelId() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, InterfaceEnum.class);
        SplitReelIdDTO dto = new SplitReelIdDTO();

        ServiceData<Integer> serviceData = new ServiceData<>();
        serviceData.setBo(1);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), any())).thenReturn(JSON.toJSONString(serviceData));
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://ip/zte-mes-manufactureshare-productionmgmtsys/pkCodeInfoCtrl/rdOwnMaterialsRegisterToLocal");
        PowerMockito.when(HttpRemoteService.remoteExe(any(), anyMap(), anyMap(), anyString())).thenReturn(JSON.toJSONString(serviceData));
        Assert.assertNotNull(service.splitReelId(dto, InterfaceEnum.updateOrInsertPkcodeInfo));
    }

    @Test
    public void updateBillStatus() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class);
        PkCodeInfo record = new PkCodeInfo();
        record.setPkCode("test");
        ServiceData<Integer> serviceData = new ServiceData<>();
        serviceData.setBo(1);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), any())).thenReturn(JSON.toJSONString(serviceData));
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://ip/zte-mes-manufactureshare-productionmgmtsys/pkCodeInfoCtrl/rdOwnMaterialsRegisterToLocal");

        PowerMockito.when(HttpRemoteService.remoteExe(any(), anyMap(), anyMap(), anyString())).thenReturn(JSON.toJSONString(serviceData));
        List<SysLookupTypesDTO> valuesList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("55");
        valuesList.add(sysLookupTypesDTO);
        PowerMockito.when(sysLookupTypesRepository.getList(anyMap())).thenReturn(valuesList);
        ApprovalCenterCallbackDealDTO approvalCenterCallbackDealDTO = new ApprovalCenterCallbackDealDTO();
        approvalCenterCallbackDealDTO.setApprover("approver");
        approvalCenterCallbackDealDTO.setBillNo("businessId");
        approvalCenterCallbackDealDTO.setApprovalStatus("approvalStatus");
        approvalCenterCallbackDealDTO.setApprovalOpinion("op");
        service.updateBillStatus("55", approvalCenterCallbackDealDTO, "url");
        Assert.assertNotNull(approvalCenterCallbackDealDTO);
    }

    @Test
    public void pushWipExtSemiToFactory() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class);
        List<WipExtendIdentificationDTO> wipList = new ArrayList<>();
        WipExtendIdentificationDTO dto = new WipExtendIdentificationDTO();
        dto.setSn("700000100001");
        wipList.add(dto);
        ServiceData<Integer> serviceData = new ServiceData<>();
        serviceData.setBo(1);
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("55");
        String empNo = "10313234";
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), any())).thenReturn(null);
        try {
            service.pushWipExtSemiToFactory(wipList, sysLookupTypesDTO, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PUSH_WIP_EXT_TO_FACTORY_FAILED, e.getMessage());
        }
        serviceData.setCode(null);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), any())).thenReturn(JSON.toJSONString(serviceData));
        try {
            service.pushWipExtSemiToFactory(wipList, sysLookupTypesDTO, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PUSH_WIP_EXT_TO_FACTORY_FAILED, e.getMessage());
        }
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://ip/zte-mes-manufactureshare-productionmgmtsys/PM/pushWipExtSemiToFactory");
        serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), any())).thenReturn(JSON.toJSONString(serviceData));
        PowerMockito.when(HttpRemoteService.remoteExe(any(), anyMap(), anyMap(), anyString())).thenReturn(JSON.toJSONString(serviceData));
        try {
            service.pushWipExtSemiToFactory(wipList, sysLookupTypesDTO, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PUSH_WIP_EXT_TO_FACTORY_FAILED, e.getMessage());
        }
        serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE, RetCode.BUSINESSERROR_MSGID));
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), any())).thenReturn(JSON.toJSONString(serviceData));
        try {
            service.pushWipExtSemiToFactory(wipList, sysLookupTypesDTO, empNo);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PUSH_WIP_EXT_TO_FACTORY_FAILED, e.getMessage());
        }
    }

    @Test
    public void repushCustomerLogDataMethod() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class, ServiceDataBuilderUtil.class);
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        customerDataLogDTO.setFactoryId(52);
        customerDataLogDTO.setCreateBy("00000000");
        customerDataLogDTO.setSn("12345");
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setAttribute1("http://imes.dev.zte.com.cn/zte-mes-manufactureshare-centerfactory/test/test");
        sysLookupTypesDTO.setAttribute2("sn");
        sysLookupTypesDTO.setAttribute3("post");
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), any(), any())).thenReturn("result");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString())).thenReturn("success");
        List<CustomerDataLogDTO> list = new ArrayList<>();
        list.add(customerDataLogDTO);
        service.repushCustomerLogDataMethod(list, sysLookupTypesDTO, customerDataLogDTO.getCreateBy());
        Assert.assertNotNull(list);
    }
}