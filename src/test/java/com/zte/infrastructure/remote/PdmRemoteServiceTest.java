package com.zte.infrastructure.remote;

import com.zte.infrastructure.feign.PdmInOneClient;
import com.zte.interfaces.dto.mbom.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.util.encrypt.secure.SecureEncryptorUtils;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.powermock.api.mockito.PowerMockito.mock;

@PrepareForTest({MicroServiceRestUtil.class, MESHttpHelper.class, SecureEncryptorUtils.class,
        ServiceDataBuilderUtil.class,JacksonJsonConverUtil.class, HttpClientUtil.class})
public class PdmRemoteServiceTest extends BaseTestCase {
    @InjectMocks
    private PdmRemoteService pdmRemoteService;
    @Mock
    private PdmInOneClient pdmInOneClient;

    @Before
    public void init() {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, JacksonJsonConverUtil.class,MESHttpHelper.class,
                ServiceDataBuilderUtil.class,SecureEncryptorUtils.class,HttpClientUtil.class);
    }

    @Test
    public void initValue() {
        PowerMockito.mockStatic(SecureEncryptorUtils.class, ServiceDataBuilderUtil.class, HttpClientUtil.class, JacksonJsonConverUtil.class);
        assertNotNull(pdmRemoteService);
    }

    @Test
    public void getCategory(){
        String result = pdmRemoteService.getCategory("123");
        Assert.assertTrue(StringUtils.isEmpty(result));

        String itemNo = "123456789123ABC";
        result = pdmRemoteService.getCategory(itemNo);
        Assert.assertTrue(StringUtils.isEmpty(result));

        PdmPage<ProdInstanceDTO> pdmPage = new PdmPage<>();
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.any(), Mockito.any()))
                .thenReturn(pdmPage);
        result = pdmRemoteService.getCategory(itemNo);
        Assert.assertTrue(StringUtils.isEmpty(result));

        List<ProdInstanceDTO> dataList = new ArrayList<>();
        ProdInstanceDTO prodInstanceDTO = new ProdInstanceDTO();
        dataList.add(prodInstanceDTO);
        pdmPage.setRows(dataList);
        result = pdmRemoteService.getCategory(itemNo);
        Assert.assertTrue(StringUtils.isEmpty(result));

        prodInstanceDTO.setCustomPartType("123456");
        result = pdmRemoteService.getCategory(itemNo);
        Assert.assertFalse(StringUtils.isEmpty(result));
    }

    /* Started by AICoder, pid:l601de9febi14bc14b180831b08a4f497b87d01f */
    @Test
    public void queryGbomDetail(){
        List<GbomDetailDTO> gbomDetail = pdmRemoteService.queryGbomDetail(null);
        assertTrue(CollectionUtils.isEmpty(gbomDetail));

        pdmRemoteService.queryGbomDetail("123");
        PdmPage<GbomDetailDTO> pdmPage = new PdmPage<>();
        List<GbomDetailDTO> list = new LinkedList<>();
        pdmPage.setGbomItem(list);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(any(), any()))
                .thenReturn(pdmPage);
        pdmRemoteService.queryGbomDetail("123");

        for (int i = 0; i <100; i++) {
            GbomDetailDTO GBOMItem = new GbomDetailDTO();
            list.add(GBOMItem);
        }
        pdmRemoteService.queryGbomDetail("123");
    }
    /* Ended by AICoder, pid:l601de9febi14bc14b180831b08a4f497b87d01f */

    @Test
    public void testGetItemNoToCustomerNumberMap_whenValidInput() {
        // Arrange
        PdmProdInstanceResDTO pdmProdInstanceResDTO1 = new PdmProdInstanceResDTO();
        pdmProdInstanceResDTO1.setInstanceNo("item1");
        pdmProdInstanceResDTO1.setCustomNo("cust1");
        PdmProdInstanceResDTO pdmProdInstanceResDTO2 = new PdmProdInstanceResDTO();
        pdmProdInstanceResDTO2.setInstanceNo("item2");
        pdmProdInstanceResDTO2.setCustomNo("cust2");
        List<String> itemNos = Arrays.asList("item1", "item2");
        Page<PdmProdInstanceResDTO> page = new Page<>();
        page.setRows(Arrays.asList(pdmProdInstanceResDTO1, pdmProdInstanceResDTO2));
        when(pdmInOneClient.queryProdInstance(any())).thenReturn(page);

        // Mocking static methods

        // Act
        Map<String, String> result = pdmRemoteService.getItemNoToCustomerNumberMap(itemNos);

        // Assert
        assertEquals(2, result.size());
        assertEquals("cust1", result.get("item1"));
        assertEquals("cust2", result.get("item2"));
    }
    @Test
    public void testGetPdmProdInstanceResDTOList_whenValidInput_returnsExpectedResult() {
        // Arrange
        List<String> itemNos = Arrays.asList("item1", "item2");

        Page<PdmProdInstanceResDTO> page = mock(Page.class);
        List<PdmProdInstanceResDTO> rows = Arrays.asList(new PdmProdInstanceResDTO(), new PdmProdInstanceResDTO());
        when(page.getRows()).thenReturn(rows);
        when(pdmInOneClient.queryProdInstance(any(PdmProdInstanceQueryDTO.class))).thenReturn(page);

        // Act
        List<PdmProdInstanceResDTO> result = pdmRemoteService.getPdmProdInstanceResDTOList(itemNos);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());

    }

    @Test
    public void testGetPdmProdInstanceResDTOList_whenEmptyItemNos_returnsEmptyList() {
        // Arrange
        List<String> itemNos = Collections.emptyList();

        // Act
        List<PdmProdInstanceResDTO> result = pdmRemoteService.getPdmProdInstanceResDTOList(itemNos);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}