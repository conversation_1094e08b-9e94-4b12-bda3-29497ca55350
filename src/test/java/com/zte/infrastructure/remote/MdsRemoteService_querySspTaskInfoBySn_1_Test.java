/*Started by AICoder, pid:3b9524f8fdu5c1e1492a0b5e61e87b6b663147b8*/
package com.zte.infrastructure.remote;
import com.zte.application.SysLookupValuesService;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.MdsSspTaskInfoDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteUtil.class, ServiceDataBuilderUtil.class})
public class MdsRemoteService_querySspTaskInfoBySn_1_Test {

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> valueOperations;

    @InjectMocks
    private MdsRemoteService mdsRemoteService;

    @Before
    public void setUp() {
        // Reset any mocks before each test
        reset(sysLookupValuesService, redisTemplate);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(valueOperations.get(anyString())).thenReturn("cachedToken");
        ReflectionTestUtils.setField(mdsRemoteService, "querySspTaskInfoUrl", null);
    }

    @Test
    public void testQuerySspTaskInfoBySn_WhenQuerySspTaskInfoIsEmpty() {
        // Given
        String sn = "testSN";
        SysLookupValues lookupMeaning = new SysLookupValues();
        lookupMeaning.setLookupMeaning("TEST112");
        when(sysLookupValuesService.findByLookupCode(anyInt())).thenReturn(lookupMeaning);
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), anyMap(), anyString(), anyString()))
                .thenReturn("{\"data\": {}}");
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString())).thenReturn("{}");

        // When
        MdsSspTaskInfoDTO result = mdsRemoteService.querySspTaskInfoBySn(sn);

        // Then
        assertNotNull(result);
        verify(sysLookupValuesService).findByLookupCode(anyInt());
        verify(redisTemplate).opsForValue();
    }

    @Test
    public void testQuerySspTaskInfoBySn_WhenSysLookupValuesIsNull() {
        // Given
        String sn = "testSN";
        when(sysLookupValuesService.findByLookupCode(anyInt())).thenReturn(null);

        // When & Then
        MesBusinessException exception =
                assertThrows(
                        MesBusinessException.class,
                        () -> mdsRemoteService.querySspTaskInfoBySn(sn));
        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
    }

    @Test
    public void testQuerySspTaskInfoBySn_WhenSysLookupValuesMeaningEmpty() {
        // Given
        String sn = "testSN";
        when(sysLookupValuesService.findByLookupCode(anyInt())).thenReturn(new SysLookupValues());

        // When & Then
        MesBusinessException exception =
                assertThrows(
                        MesBusinessException.class,
                        () -> mdsRemoteService.querySspTaskInfoBySn(sn));
        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
    }

    @Test
    public void testQuerySspTaskInfoBySn_QuerySspTaskInfoUrlNotNull() {
        // Given
        String sn = "testSN";
        ReflectionTestUtils.setField(mdsRemoteService, "querySspTaskInfoUrl", "test111");
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), anyMap(), anyString(), anyString()))
                .thenReturn("{\"data\": {}}");
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(anyString())).thenReturn("{}");

        // When
        MdsSspTaskInfoDTO result = mdsRemoteService.querySspTaskInfoBySn(sn);

        // Then
        assertNotNull(result);
        verify(sysLookupValuesService, never()).findByLookupCode(anyInt());
        verify(redisTemplate).opsForValue();
    }
}
/*Ended by AICoder, pid:3b9524f8fdu5c1e1492a0b5e61e87b6b663147b8*/