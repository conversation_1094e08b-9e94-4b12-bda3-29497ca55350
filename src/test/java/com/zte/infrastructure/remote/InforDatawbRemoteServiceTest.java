package com.zte.infrastructure.remote;

import com.alibaba.fastjson.JSON;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;


@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteService.class, HttpRemoteUtil.class, ConstantInterface.class, HttpClientUtil.class,
        MicroServiceRestUtil.class, InterfaceEnum.class, CommonUtils.class, ServiceDataBuilderUtil.class})
public class InforDatawbRemoteServiceTest extends TestCase {
    @InjectMocks
    private InforDatawbRemoteService inforDatawbRemoteService;
    @Mock
    private ConstantInterface constantInterface;

    @Test
    public void updateStockUploadLog() {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(JSON.toJSONString(new ServiceData() {{
        }}));
        inforDatawbRemoteService.updateStockUploadLog(new CustomerDataLogDTO());
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        Assert.assertNotNull(customerDataLogDTO);
    }

    /* Started by AICoder, pid:0a3f43225098c0a14f6308f280cde928b5977ea1 */
    @Test(timeout = 8000)
    public void testUpdateStockUploadLog_Success() {
        PowerMockito.mockStatic(HttpRemoteUtil.class,ServiceDataBuilderUtil.class);
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        inforDatawbRemoteService.updateStockUploadLog(customerDataLogDTO);
        assertNotNull(customerDataLogDTO);
    }
    /*Ended by AICoder, pid:f4f15m6ec0z7ad51453d0a6820323524d315ae88*/


}