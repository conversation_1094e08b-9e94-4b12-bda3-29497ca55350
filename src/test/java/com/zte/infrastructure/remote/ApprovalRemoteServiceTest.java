package com.zte.infrastructure.remote;

import com.zte.common.model.MessageId;
import com.zte.interfaces.dto.ApprovalProcessInfoEntityDTO;
import com.zte.interfaces.dto.approval.*;
import com.zte.iss.approval.sdk.bean.*;
import com.zte.iss.approval.sdk.client.ApprovalFlowClient;
import com.zte.iss.approval.sdk.client.ApprovalTaskClient;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.mockito.Matchers.any;

/**
 * @author: 10328274
 * @email： <EMAIL>
 * @DateTime: 2022/6/15 16:57
 * @Description:
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ApprovalFlowClient.class,ApprovalTaskClient.class})
public class ApprovalRemoteServiceTest extends BaseTestCase {

    @InjectMocks
    ApprovalRemoteService approvalRemoteService;

    private String applyNo = "c0f3899c-b1cf-4be5-9ed1-65ab2ffa7862";
    private String scrapFlowCode = "c0f3899c-b1cf-4be5-9ed1-65ab2ffa7862";

    @Test
    public void scrapFlowStart() throws Exception {
        ScrapFlowStartDTO scrapFlowStartDTO = new ScrapFlowStartDTO();
        scrapFlowStartDTO.setEmpNo("10328274");
        scrapFlowStartDTO.setEmpName("黄敏");

        scrapFlowStartDTO.setApplyNo(UUID.randomUUID().toString());
        scrapFlowStartDTO.setScrapType("1");
        scrapFlowStartDTO.setScrapTypeName("材料问题");
        scrapFlowStartDTO.setAmount("100.00");

        List<AppendixDTO> appendix = new ArrayList<>();
        AppendixDTO appendix1 = new AppendixDTO();
        appendix1.setUid("10328274");
        appendix1.setName("测试文档.xlsx");
        appendix1.setFileUrl("http://10.5.36.20:29833/zte-bmt-sac-otcbff/zte-bmt-sac-otc/file/download/27f7b136-7b76-40d1-9159-6886328f9475");
        appendix1.setMd5("199241303742914560");
        appendix1.setSize("185000");
        appendix.add(appendix1);
        scrapFlowStartDTO.setAppendix(appendix);

        scrapFlowStartDTO.setQualitier("10260525");
        scrapFlowStartDTO.setFinancer("10275508");
        scrapFlowStartDTO.setQualitiyMinister("00286523");
        scrapFlowStartDTO.setProductManager("00286569");

        scrapFlowStartDTO.setWorkshoper("");
        scrapFlowStartDTO.setProductMinister("");

        PowerMockito.mockStatic(ApprovalFlowClient.class);
        PowerMockito.when(ApprovalFlowClient.start(any())).thenReturn("");
        approvalRemoteService.scrapFlowStart(scrapFlowStartDTO);

        scrapFlowStartDTO.setFlowCode("1111");
        List<ApprovalProcessInfoEntityDTO> approvalProcessInfoEntityDTOList = new ArrayList<>();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO = new ApprovalProcessInfoEntityDTO();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO1 = new ApprovalProcessInfoEntityDTO();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO2 = new ApprovalProcessInfoEntityDTO();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO3 = new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTOList.add(approvalProcessInfoEntityDTO);
        approvalProcessInfoEntityDTOList.add(approvalProcessInfoEntityDTO1);
        approvalProcessInfoEntityDTOList.add(approvalProcessInfoEntityDTO2);
        approvalProcessInfoEntityDTOList.add(approvalProcessInfoEntityDTO3);
        approvalProcessInfoEntityDTO.setNodeCode("a1");
        approvalProcessInfoEntityDTO.setApproverId("1111");
        approvalProcessInfoEntityDTO1.setNodeCode("a1");
        approvalProcessInfoEntityDTO1.setApproverId("2222");
        approvalProcessInfoEntityDTO2.setNodeCode("a2");
        approvalProcessInfoEntityDTO2.setApproverId("3333");
        scrapFlowStartDTO.setApprovalProcessInfoEntityDTOList(approvalProcessInfoEntityDTOList);
        Assert.assertNotNull(approvalRemoteService.scrapFlowStart(scrapFlowStartDTO));
    }

    @Test
    public void resetUser() throws Exception {
        ScrapResetUserDTO scrapResetUserDTO = new ScrapResetUserDTO();
        scrapResetUserDTO.setApplyNo(applyNo);
        scrapResetUserDTO.setEmpNo("10328274");
        scrapResetUserDTO.setProductManager("10307315");
        PowerMockito.mockStatic(ApprovalFlowClient.class);
        PowerMockito.when(ApprovalFlowClient.changeFlowParams((ParamChangeFlowInstIdDTO) any())).thenReturn("");
        approvalRemoteService.resetUser(scrapResetUserDTO,"111");
        Assert.assertNull(approvalRemoteService.resetUser(scrapResetUserDTO,null));
    }

    @Test
    public void reassign() {
        ScrapReassignDTO scrapReassignDTO = new ScrapReassignDTO();
        scrapReassignDTO.setApplyNo(applyNo);
        scrapReassignDTO.setEmpNo("10275508");

        scrapReassignDTO.setReceiver("10266925");
        PageRows<ActiveNodeDTO> pageRows = new PageRows<ActiveNodeDTO>();
        pageRows.setRows(Collections.singletonList(new ActiveNodeDTO()));
        PowerMockito.mockStatic(ApprovalFlowClient.class);
        PowerMockito.mockStatic(ApprovalTaskClient.class);
        PowerMockito.when(ApprovalFlowClient.queryCurrentActiveNode(any())).thenReturn(pageRows);
        PowerMockito.when(ApprovalTaskClient.reassign(any())).thenReturn("");
        approvalRemoteService.reassign(scrapReassignDTO,"111");
        Assert.assertNotNull(approvalRemoteService.reassign(scrapReassignDTO,null));
    }

    @Test
    public void queryCurrentActiveNode() {
        FlowBaseDTO flowBaseDTO = new FlowBaseDTO();
        flowBaseDTO.setApplyNo(applyNo);
        flowBaseDTO.setEmpNo("10328274");

        PageRows<ActiveNodeDTO> pageRows = new PageRows<ActiveNodeDTO>();
        pageRows.setRows(Collections.singletonList(new ActiveNodeDTO()));
        PowerMockito.mockStatic(ApprovalFlowClient.class);
        PowerMockito.when(ApprovalFlowClient.queryCurrentActiveNode(any())).thenReturn(pageRows);
        approvalRemoteService.queryCurrentActiveNode(flowBaseDTO, "11");
        Assert.assertNotNull(approvalRemoteService.queryCurrentActiveNode(flowBaseDTO, null));
    }

    @Test
    public void getFlowRecordsByBillNo() {
        FlowBaseDTO flowBaseDTO = new FlowBaseDTO();
        flowBaseDTO.setApplyNo(applyNo);
        flowBaseDTO.setEmpNo("10328274");

        PageRows<ActiveNodeDTO> pageRows = new PageRows<ActiveNodeDTO>();
        pageRows.setRows(Collections.singletonList(new ActiveNodeDTO()));
        PowerMockito.mockStatic(ApprovalFlowClient.class);
        PowerMockito.when(ApprovalFlowClient.queryCurrentActiveNode(any())).thenReturn(pageRows);
        approvalRemoteService.getFlowRecordsByBillNo("1", "2", "3");
        Assert.assertNotNull(approvalRemoteService.getFlowRecordsByBillNo("1", "2", null));
    }

    @Test
    public void revoke() {
        PowerMockito.mockStatic(ApprovalFlowClient.class);
        Assert.assertNull(approvalRemoteService.revoke("1","2","flowCode"));
    }

    @Test
    public void changeInstanceParamByFlowInstanceId() throws MesBusinessException {

        PowerMockito.mockStatic(ApprovalFlowClient.class);
        List<ApprovalProcessDTO> approvalProcessDTOList = new ArrayList<>();
        ApprovalProcessDTO approvalProcessDTO=new ApprovalProcessDTO();
        approvalProcessDTO.setFlowInstanceId("test123");
        approvalProcessDTOList.add(approvalProcessDTO);
        HashMap<String,Object> parameterMap = new HashMap<>();
        parameterMap.put("productManager","00286523");
        PowerMockito.when(ApprovalFlowClient.queryProcess((ProcessBusinessIdDTO) any())).thenReturn(approvalProcessDTOList);
        PowerMockito.when(ApprovalFlowClient.changeInstanceParamByFlowInstanceId(any())).thenReturn("");
        try {
            approvalRemoteService.changeInstanceParamByFlowInstanceId("00286523","billNo",parameterMap,null);
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.CURRENT_DOCUMENT_INFORMATION_NOT_FOUND));
        }
        try {
            approvalRemoteService.changeInstanceParamByFlowInstanceId("00286523","billNo",parameterMap,"111");
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.CURRENT_DOCUMENT_INFORMATION_NOT_FOUND));
        }
    }

    @Test
    public void showFlowInstancePanorama() throws MesBusinessException {
        PowerMockito.mockStatic(ApprovalFlowClient.class);
        List<ApprovalProcessDTO> approvalProcessDTOList = new ArrayList<>();
        ApprovalProcessDTO approvalProcessDTO=new ApprovalProcessDTO();
        approvalProcessDTO.setFlowInstanceId("test123");
        approvalProcessDTOList.add(approvalProcessDTO);
        HashMap<String,Object> parameterMap = new HashMap<>();
        parameterMap.put("productManager","00286523");
        PowerMockito.when(ApprovalFlowClient.queryProcess((ProcessBusinessIdDTO) any())).thenReturn(approvalProcessDTOList);
        List<ApprovalNodeInfoDTO> approvalNodeInfoDTOList = new ArrayList<>();
        PowerMockito.when(ApprovalFlowClient.showFlowInstancePanorama(any())).thenReturn(approvalNodeInfoDTOList);
        approvalRemoteService.showFlowInstancePanorama("00286523","billNo",null);
        Assert.assertNotNull(approvalRemoteService.showFlowInstancePanorama("00286523","billNo","111"));



//        try {
//            approvalRemoteService.showFlowInstancePanorama("00286523","billNo",null);
//        } catch (MesBusinessException e) {
//            Assert.assertTrue(e.getExMsgId().equals(MessageId.CURRENT_DOCUMENT_INFORMATION_NOT_FOUND));
//        }
//        try {
//            approvalRemoteService.showFlowInstancePanorama("00286523","billNo","111");
//        } catch (MesBusinessException e) {
//            Assert.assertTrue(e.getExMsgId().equals(MessageId.CURRENT_DOCUMENT_INFORMATION_NOT_FOUND));
//        }
    }
}
