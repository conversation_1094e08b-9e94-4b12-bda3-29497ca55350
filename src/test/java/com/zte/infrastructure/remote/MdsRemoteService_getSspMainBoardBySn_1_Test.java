/*Started by AICoder, pid:gc0fcb923ff5e9e140c50a8e21f3cd425c63c0ca*/
package com.zte.infrastructure.remote;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.zte.application.SysLookupValuesService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.MdsSspSnMainBoardDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.util.ReflectionTestUtils;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.reset;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteUtil.class, ServiceDataBuilderUtil.class, JacksonJsonConverUtil.class})
public class MdsRemoteService_getSspMainBoardBySn_1_Test {

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> valueOperations;

    @InjectMocks
    private MdsRemoteService mdsRemoteService;

    @Before
    public void setUp() {
        // Reset any mocks before each test
        reset(sysLookupValuesService, redisTemplate);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(valueOperations.get(anyString())).thenReturn("cachedToken");
        ReflectionTestUtils.setField(mdsRemoteService, "querySspTaskInfoUrl", null);
    }

    @Test
    public void testGetSspMainBoardBySn_WithValidSn() throws Exception {
        String sn = "validSn";
        String url = "http://example.com";
        String accessToken = "testToken";
        String responseMsg = "{\"data\": {}}";
        MdsSspSnMainBoardDTO expectedDto = new MdsSspSnMainBoardDTO();

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning(url);

        ReflectionTestUtils.setField(mdsRemoteService, "querySspMainBoardInfoUrl", null);
        when(sysLookupValuesService.findByLookupCode(Constant.LOOK_UP_VALUE_2020014)).thenReturn(sysLookupValues);
        when(redisTemplate.opsForValue().get(Constant.MDS_OBTAINS_TOKEN_KEY)).thenReturn(accessToken);
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), eq(url), any())).thenReturn(responseMsg);
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(responseMsg)).thenReturn(responseMsg);
        when(JacksonJsonConverUtil.jsonToListBean(eq(responseMsg), any())).thenReturn(Lists.newArrayList(expectedDto));

        MdsSspSnMainBoardDTO result = mdsRemoteService.getSspMainBoardBySn(sn);

        assertNotNull(result);
        assertEquals(expectedDto, result);
    }

    @Test
    public void testGetSspMainBoardBySn_WithEmptySn() throws Exception {
        String sn = "validSn";
        String url = "http://example.com";
        String accessToken = "testToken";
        String responseMsg = "{\"data\": {}}";
        MdsSspSnMainBoardDTO expectedDto = new MdsSspSnMainBoardDTO();

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning(url);

        ReflectionTestUtils.setField(mdsRemoteService, "querySspMainBoardInfoUrl", null);
        when(sysLookupValuesService.findByLookupCode(Constant.LOOK_UP_VALUE_2020014)).thenReturn(sysLookupValues);
        when(redisTemplate.opsForValue().get(Constant.MDS_OBTAINS_TOKEN_KEY)).thenReturn(accessToken);
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), eq(url), any())).thenReturn(responseMsg);
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(responseMsg)).thenReturn(responseMsg);
        when(JacksonJsonConverUtil.jsonToListBean(eq(responseMsg), any())).thenReturn(Lists.newArrayList());

        assertNull(mdsRemoteService.getSspMainBoardBySn(sn));
    }

    @Test
    public void testGetSspMainBoardBySn_WithEmptyQueryUrl() {
        ReflectionTestUtils.setField(mdsRemoteService, "querySspMainBoardInfoUrl", null);
        when(sysLookupValuesService.findByLookupCode(Constant.LOOK_UP_VALUE_2020014)).thenReturn(null);

        MesBusinessException exception = assertThrows(MesBusinessException.class, () -> {
            mdsRemoteService.getSspMainBoardBySn("validSn");
        });

        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
        assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, exception.getExMsgId());
    }

    @Test
    public void testGetSspMainBoardBySn_WithEmptyQueryUrl_2() {
        ReflectionTestUtils.setField(mdsRemoteService, "querySspMainBoardInfoUrl", null);
        when(sysLookupValuesService.findByLookupCode(Constant.LOOK_UP_VALUE_2020014)).thenReturn(new SysLookupValues());

        MesBusinessException exception = assertThrows(MesBusinessException.class, () -> {
            mdsRemoteService.getSspMainBoardBySn("validSn");
        });

        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
        assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, exception.getExMsgId());
    }

    @Test
    public void testGetSspMainBoardBySn_WithInvalidResponse() throws Exception {
        String sn = "validSn";
        String url = "http://example.com";
        String accessToken = "testToken";
        String responseMsg = "";

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning(url);

        when(sysLookupValuesService.findByLookupCode(Constant.LOOK_UP_VALUE_2020014)).thenReturn(sysLookupValues);

        ReflectionTestUtils.setField(mdsRemoteService, "querySspMainBoardInfoUrl", url);
        when(redisTemplate.opsForValue().get(Constant.MDS_OBTAINS_TOKEN_KEY)).thenReturn(accessToken);
        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), eq(url), any())).thenReturn(responseMsg);
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(responseMsg)).thenThrow(new MesBusinessException("0005", "customize.msg"));

        MesBusinessException exception = assertThrows(MesBusinessException.class, () -> {
            mdsRemoteService.getSspMainBoardBySn(sn);
        });

        assertEquals(RetCode.BUSINESSERROR_CODE, exception.getExCode());
        assertEquals("customize.msg", exception.getExMsgId());
    }
}
/*Ended by AICoder, pid:gc0fcb923ff5e9e140c50a8e21f3cd425c63c0ca*/