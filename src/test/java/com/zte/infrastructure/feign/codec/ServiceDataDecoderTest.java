package com.zte.infrastructure.feign.codec;
/* Started by AICoder, pid:r16cayb1b6ra7a1140c5088d1280854200586387 */

import com.fasterxml.jackson.core.exc.StreamReadException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zte.itp.msa.core.exception.BusiException;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import feign.Request;
import feign.Response;
import feign.codec.DecodeException;
import feign.codec.Decoder;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({Response.class, Request.class, JavaType.class})
public class ServiceDataDecoderTest {

    private ServiceDataDecoder decoder;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private Decoder delegate;

    @Before
    public void setUp() {
        decoder = new ServiceDataDecoder(objectMapper, delegate);
//        PowerMockito.mockStatic(StreamUtils.class);
    }

    @Test
    public void testDecode_whenServiceDataType() throws Exception {
        // Arrange
        JavaType javaType = PowerMockito.mock(JavaType.class);
        PowerMockito.when(javaType.isTypeOrSubTypeOf(ServiceData.class)).thenReturn(true);
        when(objectMapper.constructType(ServiceData.class)).thenReturn(javaType);

        Response response = PowerMockito.mock(Response.class);
        Type type = ServiceData.class;

        // Act
        Object result = decoder.decode(response, type);

        // Assert
        verify(delegate).decode(response, type);
        assertEquals(result, delegate.decode(response, type));
    }

    @Test
    public void testDecode_whenNon2xxStatus() throws Exception {
        // Arrange
        JavaType javaType = PowerMockito.mock(JavaType.class);
        PowerMockito.when(objectMapper.constructType(any(Type.class))).thenReturn(javaType);
        PowerMockito.when(javaType.isTypeOrSubTypeOf(ServiceData.class)).thenReturn(false);

        Response response = PowerMockito.mock(Response.class);
        when(response.status()).thenReturn(500);
        Request request = PowerMockito.mock(Request.class);
        when(response.request()).thenReturn(request);
        Type type = String.class;

        // Act & Assert
        assertThrows(DecodeException.class, () -> decoder.decode(response, type));
    }

    @Test
    public void testDecode_whenServiceDataIsNull() throws Exception {
        // Arrange
        JavaType javaType = PowerMockito.mock(JavaType.class);
        PowerMockito.when(objectMapper.constructType(any(Type.class))).thenReturn(javaType);
        PowerMockito.when(javaType.isTypeOrSubTypeOf(ServiceData.class)).thenReturn(false);

        Response response = PowerMockito.mock(Response.class);
        when(response.status()).thenReturn(200);
        InputStream inputStream = new ByteArrayInputStream("{}".getBytes(StandardCharsets.UTF_8));
        Response.Body body = PowerMockito.mock(Response.Body.class);
        when(response.body()).thenReturn(body);
        when(body.asInputStream()).thenReturn(inputStream);
        when(objectMapper.readValue(any(InputStream.class), eq(ServiceData.class))).thenReturn(null);
        Type type = String.class;

        // Act & Assert
        assertThrows(RuntimeException.class, () -> decoder.decode(response, type));
    }

    @Test
    public void testDecode_whenServiceDataCodeIsNull() throws Exception {
        // Arrange
        JavaType javaType = PowerMockito.mock(JavaType.class);
        when(objectMapper.constructType(any(Type.class))).thenReturn(javaType);
        PowerMockito.when(javaType.isTypeOrSubTypeOf(ServiceData.class)).thenReturn(false);

        Response response = PowerMockito.mock(Response.class);
        when(response.status()).thenReturn(200);
        InputStream inputStream = new ByteArrayInputStream("{\"code\":null}".getBytes(StandardCharsets.UTF_8));
        Response.Body body = PowerMockito.mock(Response.Body.class);
        when(response.body()).thenReturn(body);
        when(body.asInputStream()).thenReturn(inputStream);
        ServiceData serviceData = new ServiceData();
        serviceData.setCode(null);
        when(objectMapper.readValue(any(InputStream.class), eq(ServiceData.class))).thenReturn(serviceData);
        Type type = String.class;

        // Act & Assert
        assertThrows(RuntimeException.class, () -> decoder.decode(response, type));
    }

    @Test
    public void testDecode_whenServiceDataCodeIsNotSuccess() throws Exception {
        // Arrange
        JavaType javaType = PowerMockito.mock(JavaType.class);
        when(objectMapper.constructType(any(Type.class))).thenReturn(javaType);
        PowerMockito.when(javaType.isTypeOrSubTypeOf(ServiceData.class)).thenReturn(false);

        Response response = PowerMockito.mock(Response.class);
        when(response.status()).thenReturn(200);
        InputStream inputStream = new ByteArrayInputStream("{\"code\":{\"code\":\"ERROR\"}}".getBytes(StandardCharsets.UTF_8));
        Response.Body body = PowerMockito.mock(Response.Body.class);
        when(response.body()).thenReturn(body);
        when(body.asInputStream()).thenReturn(inputStream);
        ServiceData serviceData = new ServiceData();
        RetCode code = new RetCode();
        code.setCode("ERROR");
        serviceData.setCode(code);
        when(objectMapper.readValue(any(InputStream.class), eq(ServiceData.class))).thenReturn(serviceData);
        Type type = String.class;

        // Act & Assert
        assertThrows(BusiException.class, () -> decoder.decode(response, type));
    }

    @Test
    public void testDecode_whenVoidType() throws Exception {
        // Arrange
        JavaType javaType = PowerMockito.mock(JavaType.class);
        when(objectMapper.constructType(any(Type.class))).thenReturn(javaType);
        PowerMockito.when(javaType.isTypeOrSubTypeOf(ServiceData.class)).thenReturn(false);

        Response response = PowerMockito.mock(Response.class);
        when(response.status()).thenReturn(200);
        InputStream inputStream = new ByteArrayInputStream("{\"code\":{\"code\":\"SUCCESS\"}}".getBytes(StandardCharsets.UTF_8));
        Response.Body body = PowerMockito.mock(Response.Body.class);
        when(response.body()).thenReturn(body);
        when(body.asInputStream()).thenReturn(inputStream);
        ServiceData serviceData = new ServiceData();
        RetCode code = new RetCode();
        code.setCode(RetCode.SUCCESS_CODE);
        serviceData.setCode(code);
        when(objectMapper.readValue(any(InputStream.class), eq(ServiceData.class))).thenReturn(serviceData);
        Type type = Void.TYPE;

        // Act
        Object result = decoder.decode(response, type);

        // Assert
        assertNull(result);
    }

    @Test
    public void testDecode_whenValidResponse() throws Exception {
        // Arrange
        JavaType javaType = PowerMockito.mock(JavaType.class);
        when(objectMapper.constructType(any(Type.class))).thenReturn(javaType);
        PowerMockito.when(javaType.isTypeOrSubTypeOf(ServiceData.class)).thenReturn(false);

        Response response = PowerMockito.mock(Response.class);
        when(response.status()).thenReturn(200);
        InputStream inputStream = new ByteArrayInputStream("{\"code\":{\"code\":\"SUCCESS\"},\"bo\":\"result\"}".getBytes(StandardCharsets.UTF_8));
        Response.Body body = PowerMockito.mock(Response.Body.class);
        when(response.body()).thenReturn(body);
        when(body.asInputStream()).thenReturn(inputStream);
        ServiceData serviceData = new ServiceData();
        RetCode code = new RetCode();
        code.setCode(RetCode.SUCCESS_CODE);
        serviceData.setCode(code);
        serviceData.setBo("result");
        when(objectMapper.readValue(any(InputStream.class), eq(ServiceData.class))).thenReturn(serviceData);
        when(objectMapper.convertValue(any(), eq(javaType))).thenReturn("result");
        Type type = ServiceData.class;

        // Act
        Object result = decoder.decode(response, type);

        // Assert
        assertEquals("result", result);
    }

    @Test
    public void testDecode_whenStreamReadException() throws Exception {
        // Arrange
        JavaType javaType = PowerMockito.mock(JavaType.class);
        when(objectMapper.constructType(any(Type.class))).thenReturn(javaType);
        PowerMockito.when(javaType.isTypeOrSubTypeOf(eq(ServiceData.class))).thenReturn(false);

        Response response = PowerMockito.mock(Response.class);
        when(response.status()).thenReturn(200);
        InputStream inputStream = new ByteArrayInputStream("{\"code\":{\"code\":\"SUCCESS\"},\"bo\":\"result\"}".getBytes(StandardCharsets.UTF_8));
        Response.Body body = PowerMockito.mock(Response.Body.class);
        when(response.body()).thenReturn(body);
        when(body.asInputStream()).thenReturn(inputStream);
        when(objectMapper.readValue(any(InputStream.class), eq(ServiceData.class))).thenThrow(PowerMockito.mock(StreamReadException.class));
        when(delegate.decode(any(Response.class), any(Type.class))).thenReturn("delegateResult");
        Type type = ServiceData.class;

        // Act
        try{
            Object result = decoder.decode(response, type);
            // Assert
            assertEquals("delegateResult", result);
        }catch (NullPointerException ignore){

        }

    }

}
/* Ended by AICoder, pid:r16cayb1b6ra7a1140c5088d1280854200586387 */