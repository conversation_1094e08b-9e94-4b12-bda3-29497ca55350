package com.zte.autoTest;

import com.zte.application.impl.SendMaterialInfoServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PkCodeInfoRepository;
import com.zte.domain.model.SendMaterialInfo;
import com.zte.domain.model.SendMaterialInfoRepository;
import com.zte.interfaces.dto.SendMaterialInfoDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @date 2022/3/17
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({RetCode.class, SpringContextUtil.class})
public class SendMaterialInfoServiceImplTest extends BaseTestCase {
    @InjectMocks
    SendMaterialInfoServiceImpl sendMaterialInfoService;

    @Mock
    private SendMaterialInfoRepository repository;

    @Mock
    private PkCodeInfoRepository pkCodeInfoRepository;

    @Mock
    private LocaleMessageSourceBean localeMessageSourceBean;


    @Test
    public void getTotalQtyAndRegisteredQty() throws Exception {
        PowerMockito.mockStatic(SpringContextUtil.class);
        SendMaterialInfoDTO sendMaterialInfoDTO = new SendMaterialInfoDTO();
        sendMaterialInfoDTO.setSku("1");
        sendMaterialInfoDTO.setSusr1("777888");
        sendMaterialInfoDTO.setFromid("JL114454");

        List<SendMaterialInfo> sendMaterialInfoList = new ArrayList<>();
        SendMaterialInfo sendMaterialInfo = new SendMaterialInfo();
        sendMaterialInfo.setQty(1);
        sendMaterialInfoList.add(sendMaterialInfo);
        PowerMockito.when(repository.selectRecordByCondition(any()))
                .thenReturn(sendMaterialInfoList);
        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        pkCodeInfo.setRawQty(BigDecimal.ONE);
        pkCodeInfoList.add(pkCodeInfo);
        PowerMockito.when(pkCodeInfoRepository.getRawQtyByFormIdAndProductTask(any()))
                .thenReturn(pkCodeInfo);

        when(SpringContextUtil.getBean("localeResolverMes")).thenReturn(localeMessageSourceBean);
        try{
            sendMaterialInfoService.getTotalQtyAndRegisteredQty(sendMaterialInfoDTO);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


}
