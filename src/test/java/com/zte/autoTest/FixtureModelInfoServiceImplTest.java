package com.zte.autoTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.impl.FixtureInfoHeadServiceImpl;
import com.zte.domain.model.FixtureInfoHeadRepository;
import com.zte.interfaces.dto.FixtureInfoDetailDTO;
import com.zte.interfaces.dto.FixtureInfoHeadDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2022-10-18 16:49
 */
@RunWith(PowerMockRunner.class)
public class FixtureModelInfoServiceImplTest extends BaseTestCase {
    @InjectMocks
    private FixtureInfoHeadServiceImpl fixtureInfoHeadService;
    @Mock
    private FixtureInfoHeadRepository fixtureModelInfoRepository;


    @Test
    public void insertHeadInfo() throws Exception {
        List<FixtureInfoDetailDTO> list = new ArrayList<>();
        list.add(new FixtureInfoDetailDTO());
        fixtureInfoHeadService.insertHeadInfo(list);
        Assert.assertNotNull(list);
    }

    @Test
    public void batchUpdate() throws Exception {
        List<FixtureInfoHeadDTO> list = new ArrayList<>();
        list.add(new FixtureInfoHeadDTO());
        fixtureInfoHeadService.batchUpdate(list);
        Assert.assertNotNull(list);
    }

    @Test
    public void batchInsert() throws Exception {
        List<FixtureInfoHeadDTO> list = new ArrayList<>();
        list.add(new FixtureInfoHeadDTO());
        fixtureInfoHeadService.batchInsert(list);
        Assert.assertNotNull(list);
    }
    @Test
    public void getList() throws Exception {
        Assert.assertNotNull(fixtureInfoHeadService.getList(new FixtureInfoHeadDTO()));
    }

    @Test
    public void getHeadInfoByFixtureModel() throws Exception {
        Assert.assertNull(fixtureInfoHeadService.getHeadInfoByFixtureModel("2"));
    }

    @Test
    public void getFixtureModelAll() throws Exception {
        Assert.assertNotNull(fixtureInfoHeadService.getFixtureModelAll());
    }

    @Test
    public void querySolderStockId() throws Exception {
        List<FixtureInfoHeadDTO> resList = new LinkedList<>();
        FixtureInfoHeadDTO a1 = new FixtureInfoHeadDTO();
        resList.add(a1);
        PowerMockito.when(fixtureModelInfoRepository.querySolderStockId(any()))
                .thenReturn(resList);
        Assert.assertNotNull(fixtureInfoHeadService.querySolderStockId(Arrays.asList("123")));
    }
    @Test
    public void delFixtureByFixtureModelList() throws Exception {
        fixtureInfoHeadService.delFixtureByFixtureModelList(new ArrayList<>());
        Assert.assertNotNull(fixtureInfoHeadService.delFixtureByFixtureModelList(Arrays.asList("123")));
    }

    @Test
    public void getFixtureModelList() throws Exception {
        Assert.assertNotNull(fixtureInfoHeadService.getFixtureModelList("123"));
    }

    @Test
    public void getSingleLineCfgQtyByModel() {
        Mockito.when(fixtureModelInfoRepository.getSingleLineCfgQtyByModel(any())).thenReturn(1);
        Assert.assertNotNull(fixtureInfoHeadService.getSingleLineCfgQtyByModel("123"));
    }
}
