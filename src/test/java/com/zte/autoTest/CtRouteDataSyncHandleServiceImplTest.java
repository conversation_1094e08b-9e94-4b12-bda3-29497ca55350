package com.zte.autoTest;

import com.google.common.collect.Lists;
import com.zte.application.impl.craftTech.CtRouteDataSyncHandleServiceImpl;
import com.zte.application.kafka.producer.CraftInfoSynchronizationProducer;
import com.zte.common.utils.Constant;
import com.zte.domain.model.craftTech.*;
import com.zte.interfaces.dto.CtBasicDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;

/**
 * @Author: panXu
 * @Date: 2020/8/27 9:56
 * @Description:
 */
public class CtRouteDataSyncHandleServiceImplTest extends BaseTestCase {

    @InjectMocks
    private CtRouteDataSyncHandleServiceImpl service;

    @Mock
    private CtRouteDataSyncRepository repository;

    @Mock
    private CraftInfoSynchronizationProducer craftInfoSynchronizationProducer;

    @Mock
    private CtBasicRepository ctBasicRepository;

    @Mock
    private CtRouteHeadRepository ctRouteHeadRepository;

    @Mock
    private CtRouteDetailRepository ctRouteDetailRepository;

    @Test
    public void handleMerge() {
        List<CtRouteDataSync> dataMergeList = new ArrayList<>();
        CtRouteDataSync dataMerge = new CtRouteDataSync();
        dataMerge.setBomNo("123456");
        dataMerge.setCraftId(1);
        dataMerge.setCraftNo("123456789");
        dataMerge.setItemNo("123456789");
        dataMerge.setNewRouteId("ADC5BE83B83724CAE054A0369FAF49F8");
        dataMerge.setProcessType("STEP");
        dataMerge.setRouteId("ADC5BE83B83724CAE054A0369FAF49F8");
        dataMerge.setSourceSysId("12244");
        dataMerge.setProcessType(Constant.SUB_BOARD);
        CtRouteDataSync data = new CtRouteDataSync();
        data.setBomNo("1234561");
        data.setCraftId(1);
        data.setCraftNo("123456789");
        data.setItemNo("1234561");
        data.setNewRouteId("ADC5BE83B83724CAE054A0369FAF49F8");
        data.setProcessType("STEP");
        data.setRouteId("ADC5BE83B83724CAE054A0369FAF49F8");
        data.setSourceSysId("12244");
        dataMerge.setProcessType(Constant.MAIN_BOARD);
        data.setBothUpdated(true);
        dataMergeList.add(dataMerge);
        dataMergeList.add(data);
        PowerMockito.when(repository.mainMergeData(anyInt(), any())).thenReturn(dataMergeList);
        service.handleMerge("2", Constant.MAIN_BOARD, "Y", 100);
        service.handleMerge("2", Constant.SUB_BOARD, "", 100);
        PowerMockito.when(repository.getCraftVerByBomNos(any())).thenReturn(
                Lists.newArrayList(
                        new CtBasic(){{setCraftVersion("V.A");setItemOrTask("1234561");}},
                        new CtBasic(){{setCraftVersion("V.Z");setItemOrTask("123456");}}
                )
        );
        PowerMockito.when(ctBasicRepository.getBasicByItemNos(any())).thenReturn(Lists.newArrayList(new CtBasicDTO(){{setCraftId("1");}}));
        PowerMockito.when(ctRouteHeadRepository.getHeadByCraftIds(any())).thenReturn(Lists.newArrayList(new CtRouteHead(){{setCraftId("1");setRouteId("1");}}));
        PowerMockito.when(ctRouteDetailRepository.getListByRouteIdList(any())).thenReturn(Lists.newArrayList(new CtRouteDetail(){{setRouteId("1");}}));
        service.handleMerge("2", Constant.MAIN_BOARD, "Y", 100);
        service.handleMerge("2", Constant.SUB_BOARD, "", 10000);
        service.handleMerge("2", Constant.MAIN_BOARD, "", 100);
        service.handleMerge("2", Constant.SUB_BOARD, "Y", 10000);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

}
