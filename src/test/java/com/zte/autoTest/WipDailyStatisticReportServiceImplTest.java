package com.zte.autoTest;

import com.zte.application.BProdBomHeaderService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.impl.WipDailyStatisticReportServiceImpl;
import com.zte.application.sncabind.PsTaskService;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.WipDailyStatisticReportRepository;
import com.zte.domain.model.sncabind.PsTaskRepository;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.WipDailyStatisticQueryDTO;
import com.zte.interfaces.dto.WipDailyStatisticReportEntityDTO;
import com.zte.interfaces.sncabind.dto.PsTaskDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.AsyncExportFileCommonService;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.*;

/**
 * <AUTHOR>
 * @date 2021/10/13
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisHelper.class})
public class WipDailyStatisticReportServiceImplTest extends BaseTestCase {
    @InjectMocks
    private WipDailyStatisticReportServiceImpl wipDailyStatisticReportService;

    @Mock
    private WipDailyStatisticReportRepository wipDailyStatisticReportrepository;
    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private CloudDiskHelper cloudDiskHelper;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private HttpServletResponse httpServletResponse;
    @Mock
    private AsyncExportFileCommonService asyncExportFileCommonService;
    @Mock
    private BProdBomHeaderService bProdBomHeaderService;
    @Mock
    private PsTaskService psTaskService;
    @Mock
    private PsTaskRepository psTaskRepository;

    @Test
    public void multipleMailExport() throws Exception {
        List<WipDailyStatisticReportEntityDTO> wipDailyStatisticReportEntityDTOList = new ArrayList<>();
        WipDailyStatisticReportEntityDTO wipDailyStatisticReportEntityDTO = new WipDailyStatisticReportEntityDTO();
        wipDailyStatisticReportEntityDTOList.add(wipDailyStatisticReportEntityDTO);
        WipDailyStatisticQueryDTO wipDailyStatisticQueryDTO = new WipDailyStatisticQueryDTO();
        List<String> selectedFields = new ArrayList<>();
        selectedFields.add("taskNo");
        wipDailyStatisticQueryDTO.setSelectedFields(selectedFields);
        wipDailyStatisticQueryDTO.setTitle(selectedFields.toArray(new String[selectedFields.size()]));
        wipDailyStatisticQueryDTO.setProps(selectedFields.toArray(new String[selectedFields.size()]));

        Page<WipDailyStatisticReportEntityDTO> firstPage = new Page<>();
        firstPage.setTotalPage(NumConstant.NUM_48);
        firstPage.setRows(wipDailyStatisticReportEntityDTOList);
        firstPage.setTotalPage(NumConstant.NUM_48);
        firstPage.setTotal(NumConstant.NUM_60);
        PowerMockito.when(wipDailyStatisticReportrepository.pageList(anyObject())).thenReturn(wipDailyStatisticReportEntityDTOList);
        try {
            Whitebox.invokeMethod(wipDailyStatisticReportService, "multipleMailExport", wipDailyStatisticQueryDTO, wipDailyStatisticReportEntityDTO, firstPage, new StringBuilder());
        }catch (Exception e){
            Assert.assertEquals(null, e.getMessage());
        }

    }
    @Test
    public void transformCode() throws Exception {
        List<SysLookupValues> lookupValueList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setAttribute1("55");
        sysLookupValues.setLookupMeaning("1");
        lookupValueList.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.eq(Constant.LOOKUP_TYPE_2222))).thenReturn(lookupValueList);
        List<WipDailyStatisticReportEntityDTO> wipDailyStatisticReportEntityDTOList = new ArrayList<>();
        WipDailyStatisticReportEntityDTO wipDailyStatisticReportEntityDTO = new WipDailyStatisticReportEntityDTO();
        wipDailyStatisticReportEntityDTO.setIsComplete("Y");
        wipDailyStatisticReportEntityDTO.setIsScheduled("Y");
        wipDailyStatisticReportEntityDTOList.add(wipDailyStatisticReportEntityDTO);

        WipDailyStatisticReportEntityDTO wipDailyStatisticReportEntityDTO1 = new WipDailyStatisticReportEntityDTO();
        wipDailyStatisticReportEntityDTO1.setIsComplete("N");
        wipDailyStatisticReportEntityDTO1.setIsScheduled("N");
        wipDailyStatisticReportEntityDTO1.setOrgId(BigDecimal.ONE);
        wipDailyStatisticReportEntityDTOList.add(wipDailyStatisticReportEntityDTO1);

        WipDailyStatisticReportEntityDTO wipDailyStatisticReportEntityDTO2 = new WipDailyStatisticReportEntityDTO();
        wipDailyStatisticReportEntityDTO2.setIsComplete("N");
        wipDailyStatisticReportEntityDTO2.setIsScheduled("N");
        wipDailyStatisticReportEntityDTO2.setOrgId(BigDecimal.TEN);
        wipDailyStatisticReportEntityDTOList.add(wipDailyStatisticReportEntityDTO2);
        Whitebox.invokeMethod(wipDailyStatisticReportService, "transformCode", wipDailyStatisticReportEntityDTOList);
        Assert.assertNotNull(wipDailyStatisticReportEntityDTOList);
    }
    @Test
    public void export2() throws Exception {
        List<WipDailyStatisticReportEntityDTO> wipDailyStatisticReportEntityDTOList = new ArrayList<>();
        WipDailyStatisticReportEntityDTO wipDailyStatisticReportEntityDTO = new WipDailyStatisticReportEntityDTO();
        wipDailyStatisticReportEntityDTOList.add(wipDailyStatisticReportEntityDTO);
        WipDailyStatisticQueryDTO wipDailyStatisticQueryDTO = new WipDailyStatisticQueryDTO();
        List<String> selectedFields = new ArrayList<>();
        selectedFields.add("taskNo");
        wipDailyStatisticQueryDTO.setSelectedFields(selectedFields);
        wipDailyStatisticQueryDTO.setTitle(selectedFields.toArray(new String[selectedFields.size()]));
        wipDailyStatisticQueryDTO.setProps(selectedFields.toArray(new String[selectedFields.size()]));

        Page<WipDailyStatisticReportEntityDTO> firstPage = new Page<>();
        firstPage.setTotalPage(NumConstant.NUM_48);
        firstPage.setRows(wipDailyStatisticReportEntityDTOList);
        firstPage.setTotalPage(NumConstant.NUM_48);
        firstPage.setTotal(NumConstant.NUM_60);
        try {
            Whitebox.invokeMethod(wipDailyStatisticReportService, "export", httpServletResponse, wipDailyStatisticQueryDTO, "fileName", wipDailyStatisticReportEntityDTO, firstPage);
        }catch (Exception e){
            Assert.assertNotNull(MessageId.CURRENTLY_EXPORTING, e.getMessage());
        }
        firstPage.setTotal(NumConstant.NUM_100000);
        Whitebox.invokeMethod(wipDailyStatisticReportService,"export",httpServletResponse,wipDailyStatisticQueryDTO,"fileName",wipDailyStatisticReportEntityDTO,firstPage);

        PowerMockito.when(wipDailyStatisticReportrepository.pageList(anyObject())).thenReturn(wipDailyStatisticReportEntityDTOList);
//        Whitebox.invokeMethod(wipDailyStatisticReportService,"export",httpServletResponse,wipDailyStatisticQueryDTO,"fileName",wipDailyStatisticReportEntityDTO,firstPage);

    }

    @Test
    public void export() throws Exception {
        List<WipDailyStatisticReportEntityDTO> wipDailyStatisticReportEntityDTOList = new ArrayList<>();
        WipDailyStatisticReportEntityDTO wipDailyStatisticReportEntityDTO = new WipDailyStatisticReportEntityDTO();
        wipDailyStatisticReportEntityDTOList.add(wipDailyStatisticReportEntityDTO);
        WipDailyStatisticQueryDTO wipDailyStatisticQueryDTO = new WipDailyStatisticQueryDTO();
        List<String> selectedFields = new ArrayList<>();
        selectedFields.add("taskNo");
        wipDailyStatisticQueryDTO.setSelectedFields(selectedFields);

        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(false);
        try {
            wipDailyStatisticReportService.export(httpServletResponse,wipDailyStatisticQueryDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.CURRENTLY_EXPORTING.equals(e.getMessage()));
        }
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        try {
            wipDailyStatisticReportService.export(httpServletResponse,wipDailyStatisticQueryDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.LOOKUP_6001_EMPTY.equals(e.getMessage()));
        }

        List<SysLookupValues> lookupValueList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setAttribute1("55");
        sysLookupValues.setLookupMeaning("855");
        lookupValueList.add(sysLookupValues);

        SysLookupValues sysLookupValues1 = new SysLookupValues();
        sysLookupValues1.setAttribute2("Y");
        sysLookupValues1.setAttribute4("taskNo");
        lookupValueList.add(sysLookupValues1);

        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.eq(Constant.WIP_DAILY_SYS_TYPE))).thenReturn(lookupValueList);
        try {
            wipDailyStatisticReportService.export(httpServletResponse, wipDailyStatisticQueryDTO);
        }catch (Exception e){}
        PowerMockito.when(wipDailyStatisticReportrepository.pageList(any())).thenReturn(wipDailyStatisticReportEntityDTOList);
        try {
            wipDailyStatisticReportService.export(httpServletResponse, wipDailyStatisticQueryDTO);
        }catch (Exception e){}
    }
    @Test
    public void pageList() throws Exception {
        List<WipDailyStatisticReportEntityDTO> wipDailyStatisticReportEntityDTOList = new ArrayList<>();
        WipDailyStatisticReportEntityDTO wipDailyStatisticReportEntityDTO = new WipDailyStatisticReportEntityDTO();
        wipDailyStatisticReportEntityDTOList.add(wipDailyStatisticReportEntityDTO);
        wipDailyStatisticReportEntityDTO.setProductType(NumConstant.STRING_ONE);
        wipDailyStatisticReportService.pageList(wipDailyStatisticReportEntityDTO);
        wipDailyStatisticReportEntityDTO.setProductType(NumConstant.STRING_TWO);
        wipDailyStatisticReportService.pageList(wipDailyStatisticReportEntityDTO);
        wipDailyStatisticReportEntityDTO.setProductType(NumConstant.STRING_THREE);
        Page<WipDailyStatisticReportEntityDTO> pageInfo = wipDailyStatisticReportService.pageList(wipDailyStatisticReportEntityDTO);
        wipDailyStatisticReportEntityDTO.setOuterFlag("是");
        wipDailyStatisticReportService.pageList(wipDailyStatisticReportEntityDTO);
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = new ArrayList<>();
        BProdBomHeaderDTO bProdBomHeaderDTO = new BProdBomHeaderDTO();
        bProdBomHeaderDTOS.add(bProdBomHeaderDTO);
        bProdBomHeaderDTO.setProdplanId("1234567");
        bProdBomHeaderDTO.setProductCode("1234567");
        PowerMockito.when(bProdBomHeaderService.queryProductCodeByProdPlanIdList(any())).thenReturn(bProdBomHeaderDTOS);
        PowerMockito.when(wipDailyStatisticReportrepository.pageList(any())).thenReturn(wipDailyStatisticReportEntityDTOList);
        wipDailyStatisticReportService.pageList(wipDailyStatisticReportEntityDTO);
        wipDailyStatisticReportEntityDTO.setProdplanId("1234567");
        wipDailyStatisticReportService.pageList(wipDailyStatisticReportEntityDTO);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);

    }
    @Test
    public void pageList1() throws Exception {
        List<WipDailyStatisticReportEntityDTO> wipDailyStatisticReportEntityDTOList = new ArrayList<>();
        WipDailyStatisticReportEntityDTO wipDailyStatisticReportEntityDTO = new WipDailyStatisticReportEntityDTO();
        wipDailyStatisticReportEntityDTO.setProductType(NumConstant.STRING_THREE);
        wipDailyStatisticReportEntityDTO.setOuterFlag("");
        wipDailyStatisticReportEntityDTO.setSourceSysNo("SMT-A->入库");
        wipDailyStatisticReportEntityDTO.setProdplanId("1234567");
        wipDailyStatisticReportEntityDTOList.add(wipDailyStatisticReportEntityDTO);
        PowerMockito.when(wipDailyStatisticReportrepository.pageList(any())).thenReturn(wipDailyStatisticReportEntityDTOList);
        List<PsTaskDTO> psTaskDTOList = new ArrayList<>();
        PsTaskDTO psTaskDTO = new PsTaskDTO();
        psTaskDTO.setProdplanId("1234567");
        psTaskDTO.setOutSourceFactoryCode("");
        psTaskDTOList.add(psTaskDTO);
        PowerMockito.when(psTaskService.getPsTaskByProdplanIdList(any())).thenReturn(psTaskDTOList);
        wipDailyStatisticReportService.pageList(wipDailyStatisticReportEntityDTO);
        Assert.assertNotNull(wipDailyStatisticReportEntityDTO);
    }
    @Test
    public void pageList2() throws Exception {
        List<WipDailyStatisticReportEntityDTO> wipDailyStatisticReportEntityDTOList = new ArrayList<>();
        WipDailyStatisticReportEntityDTO wipDailyStatisticReportEntityDTO = new WipDailyStatisticReportEntityDTO();
        wipDailyStatisticReportEntityDTO.setProductType(NumConstant.STRING_THREE);
        wipDailyStatisticReportEntityDTO.setOuterFlag("");
        wipDailyStatisticReportEntityDTO.setSourceSysNo("外协->入库");
        wipDailyStatisticReportEntityDTO.setProdplanId("1234567");
        wipDailyStatisticReportEntityDTOList.add(wipDailyStatisticReportEntityDTO);
        PowerMockito.when(wipDailyStatisticReportrepository.pageList(any())).thenReturn(wipDailyStatisticReportEntityDTOList);
        List<PsTaskDTO> psTaskDTOList = new ArrayList<>();
        PsTaskDTO psTaskDTO = new PsTaskDTO();
        psTaskDTO.setProdplanId("1234567");
        psTaskDTO.setOutSourceFactoryCode("");
        psTaskDTOList.add(psTaskDTO);
        PowerMockito.when(psTaskService.getPsTaskByProdplanIdList(any())).thenReturn(psTaskDTOList);
        wipDailyStatisticReportService.pageList(wipDailyStatisticReportEntityDTO);
        Assert.assertNotNull(wipDailyStatisticReportEntityDTO);
    }

    @Test
    public void pageList3() throws Exception {
        List<WipDailyStatisticReportEntityDTO> wipDailyStatisticReportEntityDTOList = new ArrayList<>();
        WipDailyStatisticReportEntityDTO wipDailyStatisticReportEntityDTO = new WipDailyStatisticReportEntityDTO();
        wipDailyStatisticReportEntityDTO.setProductType(NumConstant.STRING_THREE);
        wipDailyStatisticReportEntityDTO.setOuterFlag("");
        wipDailyStatisticReportEntityDTO.setSourceSysNo("SMT-A->入库");
        wipDailyStatisticReportEntityDTO.setProdplanId("1234567");
        wipDailyStatisticReportEntityDTOList.add(wipDailyStatisticReportEntityDTO);
        PowerMockito.when(wipDailyStatisticReportrepository.pageList(any())).thenReturn(wipDailyStatisticReportEntityDTOList);
        List<PsTaskDTO> psTaskDTOList = new ArrayList<>();
        PsTaskDTO psTaskDTO = new PsTaskDTO();
        psTaskDTO.setProdplanId("1234567");
        psTaskDTO.setOutSourceFactoryCode("wx1122");
        psTaskDTOList.add(psTaskDTO);
        PowerMockito.when(psTaskService.getPsTaskByProdplanIdList(any())).thenReturn(psTaskDTOList);
        wipDailyStatisticReportService.pageList(wipDailyStatisticReportEntityDTO);
        Assert.assertNotNull(wipDailyStatisticReportEntityDTO);
    }

    @Test
    public void pageList4() throws Exception {
        List<WipDailyStatisticReportEntityDTO> wipDailyStatisticReportEntityDTOList = new ArrayList<>();
        WipDailyStatisticReportEntityDTO wipDailyStatisticReportEntityDTO = new WipDailyStatisticReportEntityDTO();
        wipDailyStatisticReportEntityDTO.setProductType(NumConstant.STRING_THREE);
        wipDailyStatisticReportEntityDTO.setOuterFlag("");
        wipDailyStatisticReportEntityDTO.setSourceSysNo("外协->入库");
        wipDailyStatisticReportEntityDTO.setProdplanId("1234567");
        wipDailyStatisticReportEntityDTOList.add(wipDailyStatisticReportEntityDTO);
        PowerMockito.when(wipDailyStatisticReportrepository.pageList(any())).thenReturn(wipDailyStatisticReportEntityDTOList);
        List<PsTaskDTO> psTaskDTOList = new ArrayList<>();
        PsTaskDTO psTaskDTO = new PsTaskDTO();
        psTaskDTO.setProdplanId("1234567");
        psTaskDTO.setOutSourceFactoryCode("wx1122");
        psTaskDTOList.add(psTaskDTO);
        PowerMockito.when(psTaskService.getPsTaskByProdplanIdList(any())).thenReturn(psTaskDTOList);
        wipDailyStatisticReportService.pageList(wipDailyStatisticReportEntityDTO);
        Assert.assertNotNull(wipDailyStatisticReportEntityDTO);
    }

    @Test
    public void save() throws Exception {
        List<WipDailyStatisticReportEntityDTO> wipDailyStatisticReportEntityDTOList = new ArrayList<>();
        WipDailyStatisticReportEntityDTO wipDailyStatisticReportEntityDTO = new WipDailyStatisticReportEntityDTO();
        wipDailyStatisticReportEntityDTOList.add(wipDailyStatisticReportEntityDTO);
        wipDailyStatisticReportService.save(wipDailyStatisticReportEntityDTOList);
        Assert.assertNotNull(wipDailyStatisticReportService.save(new ArrayList<>()));
    }


}
