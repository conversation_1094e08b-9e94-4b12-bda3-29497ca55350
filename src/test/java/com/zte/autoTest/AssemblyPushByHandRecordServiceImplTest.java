package com.zte.autoTest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.CenterFactoryCallSiteService;
import com.zte.application.ProgramVerifyConfirmInfoService;
import com.zte.application.impl.AssemblyPushByHandRecordServiceImpl;
import com.zte.common.BusManageUtils;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.ExcelUtils;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.ResultData;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.ExcelName;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.AssemblyPushByHandRecordRepository;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.AssemblyPushByHandRecordEntityDTO;
import com.zte.interfaces.dto.busmanage.BmEmployeeInfoDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.BaseTestCase;
import org.apache.commons.collections.CollectionUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.*;

/**
 * <AUTHOR>
 * @date 2021/10/13
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpClientUtil.class,ConstantInterface.class,HttpRemoteService.class, CommonUtils.class,HttpRemoteUtil.class})
public class AssemblyPushByHandRecordServiceImplTest extends BaseTestCase {
    @InjectMocks
    private AssemblyPushByHandRecordServiceImpl assemblyPushByHandRecordService;

    @Mock
    private AssemblyPushByHandRecordRepository assemblyPushByHandRecordrepository;

    @Mock
    private FactoryConfig factoryConfig;

    @Mock
    private CenterFactoryCallSiteService centerFactoryCallSiteService;

    @Mock
    private ProgramVerifyConfirmInfoService programVerifyConfirmInfoService;


    public static void main(String[] args)throws Exception {
        InputStream inputStream =  new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\打印.xlsx"));
        ResultData resultData = null;
        Workbook wb;
        try {
            wb = WorkbookFactory.create(inputStream);
        } catch (Exception e1) {
            e1.printStackTrace();
            return ;
        }
        // 得到第一个sheet
        Sheet sheet = wb.getSheetAt(NumConstant.NUM_ZERO);
        Row rowHeader = sheet.getRow(NumConstant.NUM_ZERO);
        // 获取标题个数
        int cellNum = rowHeader.getLastCellNum();
        List<BmEmployeeInfoDTO> excelInfoList = null;
        String[] propNames;
        // 4是劳务工excel，不需要工号
        if (BusManageUtils.CHECK_FOUR == cellNum) {
            // 劳务工不校验工号，校验姓名不为空
            propNames = ExcelName.PROP_NAME_OTHER;
            resultData = ExcelUtils.resolveExcelForEmpInfo(wb, BmEmployeeInfoDTO.class, propNames);
        } else {
            // 正式员工校验工号不能为空
            propNames = ExcelName.PROP_NAME_OFFICIAL;
            resultData = ExcelUtils.resolveExcelForEmpInfo(wb, BmEmployeeInfoDTO.class, propNames);
        }

        if(null == resultData.getData()){
            return ;
        }
        if (!BusManageUtils.CHECK_ZERO.equals(resultData.getCode())) {
            return;
        }
        excelInfoList = (List<BmEmployeeInfoDTO>) resultData.getData();
        if (CollectionUtils.isEmpty(excelInfoList)) {
            return ;
        }

        List<JSONObject> list = new ArrayList<>();
        for (BmEmployeeInfoDTO bmEmployeeInfoDTO : excelInfoList) {
            JSONObject jsObject = new JSONObject();
            jsObject.put("barcode",bmEmployeeInfoDTO.getWorkNo().trim());
            jsObject.put("itemName",bmEmployeeInfoDTO.getDepartmentName().trim());
            jsObject.put("parentCategoryCode","SN_CODE");
            jsObject.put("sourceSystem",BusinessConstant.SOURCESYS_IMES);
            jsObject.put("itemCode",bmEmployeeInfoDTO.getEmployeeName().trim());
            list.add(jsObject);
        }
        System.out.println(list);
        String params = JacksonJsonConverUtil.beanToJson(list);
        String p = JacksonJsonConverUtil.beanToJson(list);
//        Map<String, String> header = MESHttpHelper.getHttpRequestHeader();
//        header.put(SysGlobalConst.HTTP_HEADER_X_TENANT_ID, "10001");
//        header.put(SysGlobalConst.HTTP_HEADER_X_TENANT_ID.toLowerCase(), "10001");
//        header.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO, "A0342386065259630592");
//        header.put(SysGlobalConst.HTTP_HEADER_X_EMP_NO.toLowerCase(), "A0342386065259630592");
//        header.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE, "A0342386065259630592");
//        header.put(SysGlobalConst.HTTP_HEADER_X_AUTH_VALUE.toLowerCase(), "A0342386065259630592");
//        String params = JacksonJsonConverUtil.beanToJson(list);
//        String url = "http://ibarcode.zte.com.cn/zte-iss-barcodecenter-barcode/barcode/update";
//        String result = HttpRemoteUtil.remoteExe(params,header,url, MicroServiceNameEum.SENDTYPEPOST);
//        System.out.println(result);

    }

    @Test
    public void pageList() throws Exception {
        AssemblyPushByHandRecordEntityDTO assemblyPushByHandRecordEntityDTO=new AssemblyPushByHandRecordEntityDTO();
        assemblyPushByHandRecordEntityDTO.setStartTime(new Date());
        assemblyPushByHandRecordEntityDTO.setEndTime(new Date());
        assemblyPushByHandRecordEntityDTO.setLastUpdatedBy("10270446");
        List<AssemblyPushByHandRecordEntityDTO> assemblyPushByHandRecordEntityDTOList= new ArrayList<>();
        assemblyPushByHandRecordEntityDTOList.add(assemblyPushByHandRecordEntityDTO);
        PowerMockito.when(assemblyPushByHandRecordrepository.pageList(any())).thenReturn(assemblyPushByHandRecordEntityDTOList);
        Map<String, String> map=new HashMap<>();
        map.put("10270446","lk=zk");
        PowerMockito.when(programVerifyConfirmInfoService.getUserNew(any())).thenReturn(map);

        Page<AssemblyPushByHandRecordEntityDTO> pageInfo = assemblyPushByHandRecordService.pageList(assemblyPushByHandRecordEntityDTO);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }
    @Test
    public void saveOrUpdate() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class,CommonUtils.class,HttpRemoteUtil.class);
        PowerMockito.when(factoryConfig.getCommonEntityId()).thenReturn("2");
        List<AssemblyPushByHandRecordEntityDTO> assemblyPushByHandRecordEntityDTOList=new ArrayList<>();
        AssemblyPushByHandRecordEntityDTO assemblyPushByHandRecordEntityDTO1=new AssemblyPushByHandRecordEntityDTO();
        assemblyPushByHandRecordEntityDTO1.setFactoryName("MES");
        assemblyPushByHandRecordEntityDTO1.setItemCode("itemCode1");
        assemblyPushByHandRecordEntityDTO1.setItemVersion("AB");
        assemblyPushByHandRecordEntityDTO1.setBomRevision("AB");
        assemblyPushByHandRecordEntityDTOList.add(assemblyPushByHandRecordEntityDTO1);
        AssemblyPushByHandRecordEntityDTO assemblyPushByHandRecordEntityDTO2=new AssemblyPushByHandRecordEntityDTO();
        assemblyPushByHandRecordEntityDTO2.setFactoryName("南京工厂");
        assemblyPushByHandRecordEntityDTO2.setFactoryId(new BigDecimal("58"));
        assemblyPushByHandRecordEntityDTO2.setItemCode("itemCode2");
        assemblyPushByHandRecordEntityDTO2.setItemVersion("AC");
        assemblyPushByHandRecordEntityDTO1.setBomRevision("AC");
        assemblyPushByHandRecordEntityDTOList.add(assemblyPushByHandRecordEntityDTO2);
        PowerMockito.when(centerFactoryCallSiteService.queryAssemblyResult(anyString(),anyString())).thenReturn(assemblyPushByHandRecordEntityDTOList);
        AssemblyPushByHandRecordEntityDTO assemblyPushByHandRecordEntityDTO=new AssemblyPushByHandRecordEntityDTO();
        assemblyPushByHandRecordEntityDTO.setItemVersion("AB");
        assemblyPushByHandRecordEntityDTO.setItemCode("123456789012");
        assemblyPushByHandRecordEntityDTO.setCombineFlag("Y");
        assemblyPushByHandRecordEntityDTO.setRemark("remark");
        assemblyPushByHandRecordEntityDTO.setStartTime(new Date());
        assemblyPushByHandRecordEntityDTO.setEndTime(new Date());

        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://");
        ServiceData serviceData =new ServiceData();
        serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE,RetCode.SUCCESS_MSGID));
        serviceData.setBo(1);
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(
                JSON.toJSONString(serviceData));
        PowerMockito.when(HttpRemoteService.remoteExe(Mockito.eq(InterfaceEnum.updateForCenter), anyMap(), anyMap(), anyString())).thenReturn(
                JSON.toJSONString(serviceData));
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                JSON.toJSONString("msg"));

        Assert.assertNotNull(assemblyPushByHandRecordService.saveOrUpdate(assemblyPushByHandRecordEntityDTO));
    }

}
