package com.zte.autoTest;

import com.alibaba.fastjson.JSONObject;
import com.zte.application.AbnormalMaterialBillHeadService;
import com.zte.application.HrmUserCenterService;
import com.zte.application.IMESLogService;
import com.zte.application.impl.ApprovalConfigServiceImpl;
import com.zte.application.impl.ApprovalProcessInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.KafkaConstant;
import com.zte.domain.model.AbnormalMaterialBillHeadRepository;
import com.zte.domain.model.ApprovalProcessInfoRepository;
import com.zte.domain.model.part.SparePartHeadRepository;
import com.zte.infrastructure.remote.ApprovalRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.ProductionmgmtRemoteService;
import com.zte.interfaces.dto.ApprovalProcessInfoDTO;
import com.zte.interfaces.dto.ApprovalProcessInfoEntityDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.ScrapBillHeaderEntityDTO;
import com.zte.interfaces.dto.approval.ScrapFlowStartDTO;
import com.zte.iss.approval.sdk.bean.ApprovalNodeInfoDTO;
import com.zte.iss.approval.sdk.bean.ApproverInfo;
import com.zte.iss.approval.sdk.bean.FlowApprovalRecordDTO;
import com.zte.iss.approval.sdk.bean.TaskVo;
import com.zte.iss.approval.sdk.client.ApprovalTaskClient;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.BaseTestCase;
import org.checkerframework.checker.units.qual.A;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.zte.common.utils.KafkaConstant.COMPLETE;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.doNothing;

/**
 * <AUTHOR>
 * @date 2021/10/13
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpClientUtil.class, ConstantInterface.class, HttpRemoteService.class, CommonUtils.class, HttpRemoteUtil.class,ApprovalTaskClient.class})
public class ApprovalProcessInfoServiceImplTest extends BaseTestCase {
    @InjectMocks
    private ApprovalProcessInfoServiceImpl approvalProcessInfoService;

    @Mock
    private ApprovalProcessInfoRepository approvalProcessInfoRepository;
    @Mock
    private AbnormalMaterialBillHeadRepository abnormalMaterialBillHeadRepository;

    @Mock
    private ApprovalRemoteService approvalRemoteService;

    @Mock
    private HrmUserCenterService hrmUserCenterService;
    @Mock
    private ApprovalConfigServiceImpl approvalConfigService;
    @Mock
    private ProductionmgmtRemoteService productionmgmtRemoteService;
    @Mock
    private AbnormalMaterialBillHeadService abnormalMaterialBillHeadService;
    @Mock
    private IMESLogService imesLogService;
    @Mock
    private SparePartHeadRepository sparePartHeadRepository;
    @Mock
    private ConstantInterface constantInterface;

    @Test
    public void getNodeCodeUniversal() throws Exception {
        JSONObject data = new JSONObject();
        data.put("businessId","IMESBF10225");
        data.put("extendedCode","faniner");
        data.put("approver","102704");
        data.put(KafkaConstant.FLOWCODE,"102704");
        List<ApprovalProcessInfoEntityDTO> approvalProcessInfoEntityDTOList = new ArrayList<>();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO=new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTO.setNodeCode("faniner");
        approvalProcessInfoEntityDTO.setApproverId("102704");
        approvalProcessInfoEntityDTOList.add(approvalProcessInfoEntityDTO);
        Assert.assertNotNull(Whitebox.invokeMethod(approvalProcessInfoService,"getNodeCodeUniversal","2",data));
    }

    @Test
    public void updateSiteFactoryBillStatus() throws Exception {
        JSONObject data = new JSONObject();
        data.put("businessId","IMESBF10225");
        data.put("extendedCode","faniner");
        data.put("approver","102704");
        List<ApprovalProcessInfoEntityDTO> approvalProcessInfoEntityDTOList = new ArrayList<>();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO=new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTO.setNodeCode("faniner");
        approvalProcessInfoEntityDTO.setApproverId("102704");
        approvalProcessInfoEntityDTOList.add(approvalProcessInfoEntityDTO);
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(approvalProcessInfoService,"scrapFlowCode","scrapFlowCode");
        Whitebox.invokeMethod(approvalProcessInfoService,"updateSiteFactoryBillStatus",data,"2",Constant.ApprovalStatus.AGREE,"4","55");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void updateSiteFactoryBillStatus1() throws Exception {
        JSONObject data = new JSONObject();
        data.put("businessId","IMESBF10225");
        data.put("extendedCode","faniner");
        data.put("approver","102704");
        List<ApprovalProcessInfoEntityDTO> approvalProcessInfoEntityDTOList = new ArrayList<>();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO=new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTO.setNodeCode("faniner");
        approvalProcessInfoEntityDTO.setApproverId("102704");
        approvalProcessInfoEntityDTOList.add(approvalProcessInfoEntityDTO);
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(approvalProcessInfoService,"verifyFrockFlowCode","verifyFrockFlowCode");
        Whitebox.invokeMethod(approvalProcessInfoService,"updateSiteFactoryBillStatus",data,"2",Constant.ApprovalStatus.AGREE,"4","55");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void dealApprovalInfo() throws Exception {
        JSONObject data = new JSONObject();
        data.put("businessId","IMESBF10225");
        data.put("extendedCode","faniner");
        data.put("approver","102704");
        List<ApprovalProcessInfoEntityDTO> approvalProcessInfoEntityDTOList = new ArrayList<>();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO=new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTO.setNodeCode("faniner");
        approvalProcessInfoEntityDTO.setApproverId("102704");
        approvalProcessInfoEntityDTOList.add(approvalProcessInfoEntityDTO);
        Whitebox.invokeMethod(approvalProcessInfoService,"dealApprovalInfo",data,"1","2",approvalProcessInfoEntityDTOList);
        Assert.assertNotNull(approvalProcessInfoEntityDTOList);
    }

    @Test
    public void showFlowInstancePanorama() throws Exception {
        JSONObject data = new JSONObject();
        data.put("businessId","IMESBF10225");
        data.put("extendedCode","faniner");
        data.put("approver","102704");
        List<ApprovalProcessInfoEntityDTO> approvalProcessInfoEntityDTOList = new ArrayList<>();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO=new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTO.setNodeCode("faniner");
        approvalProcessInfoEntityDTO.setApproverId("102704");
        approvalProcessInfoEntityDTOList.add(approvalProcessInfoEntityDTO);
        PowerMockito.when(approvalProcessInfoRepository.getList(any())).thenReturn(approvalProcessInfoEntityDTOList);
        List<ApprovalNodeInfoDTO> approvalNodeInfoDTOList = new ArrayList<>();
        ApprovalNodeInfoDTO approvalNodeInfoDTO = new ApprovalNodeInfoDTO();
        approvalNodeInfoDTOList.add(approvalNodeInfoDTO);
        List<ApproverInfo> approverInfoList = new ArrayList<>();
        ApproverInfo approverInfo = new ApproverInfo();
        approverInfo.setApproverId("102704");
        approverInfo.setResult("Y");
        approverInfoList.add(approverInfo);
        ApproverInfo approverInfo2 = new ApproverInfo();
        approverInfo2.setApproverId("102704");
        approverInfo2.setResult("N");
        approverInfoList.add(approverInfo2);
        ApproverInfo approverInfo3 = new ApproverInfo();
        approverInfo3.setApproverId("102704");
        approverInfoList.add(approverInfo3);
        approvalNodeInfoDTO.setApproverList(approverInfoList);
        PowerMockito.when(approvalRemoteService.showFlowInstancePanorama(any(),anyString(),any())).thenReturn(approvalNodeInfoDTOList);

        approvalProcessInfoService.showFlowInstancePanorama("102704","IMES","111");
        approvalProcessInfoService.showFlowInstancePanorama("102704","IMES",null);

        Map<String, String> nodeNameToCode = new HashMap<>();
        PowerMockito.when(approvalConfigService.getNodeNameToCode(any())).thenReturn(nodeNameToCode);
        approvalProcessInfoService.showFlowInstancePanorama("102704","IMES","111");

        nodeNameToCode.put("111","222");
        PowerMockito.when(approvalConfigService.getNodeNameToCode(any())).thenReturn(nodeNameToCode);
        approvalProcessInfoService.showFlowInstancePanorama("102704","IMES","111");

        approvalNodeInfoDTO.setNodeName("111");
        approvalProcessInfoService.showFlowInstancePanorama("102704","IMES","111");

        PowerMockito.when(approvalRemoteService.showFlowInstancePanorama(any(),anyString(),any())).thenReturn(new ArrayList<>());
        approvalNodeInfoDTO.setNodeName("111");
        Assert.assertNotNull(approvalProcessInfoService.showFlowInstancePanorama("102704","IMES","111"));

    }

    @Test
    public void setValueFromNodeCodeList() throws Exception {
        List<String> nodeCodeList= new ArrayList<>();
        List<ApprovalProcessInfoEntityDTO> approvalProcessInfoEntityDTOList = new ArrayList<>();
        ApprovalProcessInfoEntityDTO dto = new ApprovalProcessInfoEntityDTO();
        dto.setNodeCode("test");
        dto.setApproverId("00286569");
        approvalProcessInfoEntityDTOList.add(dto);
        ApproverInfo approverInfo = new ApproverInfo();
        approverInfo.setApproverId("TEST");
        ApprovalProcessInfoDTO approvalProcessInfoDTO = new ApprovalProcessInfoDTO();
        nodeCodeList.add("test");
        Assert.assertNotNull(nodeCodeList);
        Whitebox.invokeMethod(approvalProcessInfoService, "setValueFromNodeCodeList", nodeCodeList,approvalProcessInfoEntityDTOList,approverInfo,approvalProcessInfoDTO);
        approverInfo.setApproverId("00286569");
        Assert.assertNotNull(dto);
        Whitebox.invokeMethod(approvalProcessInfoService, "setValueFromNodeCodeList", nodeCodeList,approvalProcessInfoEntityDTOList,approverInfo,approvalProcessInfoDTO);
    }

    @Test
    public void dealBoardScrapInfoAndApprovalInfo2() throws Exception {
        JSONObject data = new JSONObject();
        data.put("extendedCode","faniner");
        data.put(KafkaConstant.BUSINESS_ID,"faniner");
        data.put("approver","102704");
        data.put("result","Y");

        JSONObject data2 = new JSONObject();
        data2.put("extendedCode","faniner");
        data2.put(KafkaConstant.BUSINESS_ID,"faniner");
        data2.put("approver","102704");
        data2.put("result","N");

        JSONObject data3 = new JSONObject();
        data3.put("extendedCode","faniner");
        data3.put(KafkaConstant.BUSINESS_ID,"faniner");
        data3.put(KafkaConstant.APPROVAL_RESULT,Constant.FLAG_Y);
        data3.put(KafkaConstant.STATUS,COMPLETE);
        List<ApprovalProcessInfoEntityDTO> approvalProcessInfoEntityDTOList = new ArrayList<>();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO=new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTO.setNodeCode("faniner");
        approvalProcessInfoEntityDTO.setApproverId("102704");
        approvalProcessInfoEntityDTOList.add(approvalProcessInfoEntityDTO);
        PowerMockito.when(approvalProcessInfoRepository.getList(any())).thenReturn(approvalProcessInfoEntityDTOList);
        PowerMockito.when(abnormalMaterialBillHeadRepository.updateSelectiveById(Mockito.any())).thenReturn(1l);
        // 触发流水线
        approvalProcessInfoService.dealApprovalInfoAndUpdateBillStatus("IMES-taskCompleted",data,"IMES");
        doNothing().when(abnormalMaterialBillHeadService).sendEmailAfterApproval(Mockito.any());
        approvalProcessInfoService.dealApprovalInfoAndUpdateBillStatus("IMES-taskCompleted",data2,"IMES");
        try{
            approvalProcessInfoService.dealApprovalInfoAndUpdateBillStatus("IMES",data3,"IMES");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
    }
    @Test
    public void dealBoardScrapInfoAndApprovalInfo() throws Exception {
        JSONObject data = new JSONObject();
        data.put("businessId","IMESBF10225");
        data.put("extendedCode","faniner");
        data.put("approver","102704");
        List<ApprovalProcessInfoEntityDTO> approvalProcessInfoEntityDTOList = new ArrayList<>();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO=new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTO.setNodeCode("faniner");
        approvalProcessInfoEntityDTO.setApproverId("102704");
        approvalProcessInfoEntityDTOList.add(approvalProcessInfoEntityDTO);
        PowerMockito.when(approvalProcessInfoRepository.getList(any())).thenReturn(approvalProcessInfoEntityDTOList);
        approvalProcessInfoService.dealApprovalInfoAndUpdateBillStatus("IMES-taskCompleted",data,"IMES");
        approvalProcessInfoService.dealApprovalInfoAndUpdateBillStatus("IMES-taskReassign",data,"IMES");
        approvalProcessInfoService.dealApprovalInfoAndUpdateBillStatus("IMES",data,"IMES");
        Map<String, String> nodeNameToCode = new HashMap<>();
        PowerMockito.when(approvalConfigService.getNodeNameToCode(any())).thenReturn(nodeNameToCode);
        try{
            approvalProcessInfoService.dealApprovalInfoAndUpdateBillStatus("IMES",data,"IMES");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
    }

    @Test
    public void startTheApprovalProcess() throws Exception {
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO = new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTO.setBillNo("billNo");
        List<ApprovalProcessInfoEntityDTO> approvalProcessInfoEntityDTOList = new ArrayList<>();
        approvalProcessInfoEntityDTOList.add(approvalProcessInfoEntityDTO);
        ScrapFlowStartDTO scrapFlowStartDTO = new ScrapFlowStartDTO();
        scrapFlowStartDTO.setApprovalProcessInfoEntityDTOList(approvalProcessInfoEntityDTOList);
        approvalProcessInfoService.scrapFlowStart(scrapFlowStartDTO);
        Assert.assertNotNull(scrapFlowStartDTO);
    }

    @Test
    public void pageList() throws Exception {
        List<FlowApprovalRecordDTO> flowApprovalRecordDTOList = new ArrayList<>();
        FlowApprovalRecordDTO flowApprovalRecordDTO = new FlowApprovalRecordDTO();
        flowApprovalRecordDTOList.add(flowApprovalRecordDTO);
        PowerMockito.when(approvalRemoteService.getFlowRecordsByBillNo(any(), any(), any())).thenReturn(flowApprovalRecordDTOList);
        Map<String, HrmPersonInfoDTO> map=new HashMap<>();
        map.put("10",new HrmPersonInfoDTO());
        map.put("17",new HrmPersonInfoDTO());
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any
                ())).thenReturn(map);
        approvalProcessInfoService.getFlowRecordsByBillNo("2", "1", "1");
        approvalProcessInfoService.getFlowRecordsByBillNo("2", "1", null);
        Map<String, String> nodeNameToCode = new HashMap<>();
        PowerMockito.when(approvalConfigService.getNodeNameToCode(any())).thenReturn(nodeNameToCode);
        Assert.assertNotNull(approvalProcessInfoService.getFlowRecordsByBillNo("2", "1", "1"));
    }


    @Test
    public void scrapFlowWithdraw() throws Exception {
        PowerMockito.when(approvalRemoteService.revoke(any(),any(),any())).thenReturn("{}");
        approvalProcessInfoService.scrapFlowWithdraw("billNo","11", null);
        try{
            approvalProcessInfoService.scrapFlowWithdraw("billNo","11", null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
    }

    @Test
    public void getExtendedCode() throws Exception {
        JSONObject data = new JSONObject();
        data.put("businessId","IMESBF10225");
        Assert.assertNotNull(Whitebox.invokeMethod(approvalProcessInfoService,"getExtendedCode","key",data,"11"));
    }

    @Test
    public void updateApproverInfo() throws Exception {
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO =new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTO.setBillNo("test123");
        approvalProcessInfoEntityDTO.setCurrApprover("00286523");
        approvalProcessInfoEntityDTO.setChangeApprover("00286523");
        PowerMockito.when(approvalProcessInfoRepository.updateApproverInfo(approvalProcessInfoEntityDTO)).thenReturn(1);
        try{
            approvalProcessInfoService.updateApproverInfo(approvalProcessInfoEntityDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
    }

    @Test
    public void getList() throws Exception {
        List<ApprovalProcessInfoEntityDTO> list = new ArrayList<>();
        ApprovalProcessInfoEntityDTO approvalProcessInfoEntityDTO =new ApprovalProcessInfoEntityDTO();
        approvalProcessInfoEntityDTO.setBillNo("test123");
        approvalProcessInfoEntityDTO.setCurrApprover("00286523");
        approvalProcessInfoEntityDTO.setChangeApprover("00286523");
        list.add(approvalProcessInfoEntityDTO);
        PowerMockito.when(approvalProcessInfoRepository.getList(approvalProcessInfoEntityDTO)).thenReturn(list);
        try{
            approvalProcessInfoService.getList(approvalProcessInfoEntityDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
    }

    @Test
    public void scrapFlowWithChangeApproval2() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class, ConstantInterface.class, HttpRemoteService.class, CommonUtils.class, HttpRemoteUtil.class,ApprovalTaskClient.class);

        Map<String,HrmPersonInfoDTO> map = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("hl");
        hrmPersonInfoDTO.setId("00286523");
        map.put("00286523",hrmPersonInfoDTO);
        ScrapBillHeaderEntityDTO record = new ScrapBillHeaderEntityDTO();
        record.setBillNo("test123");
        record.setCurrApprover("00286523");
        record.setBillStatus("审批中");
        record.setCurrApprover("00286523");
        record.setEmpNo("00286523");
        record.setChangedApprover("00286523");
        record.setScrapType("1");
        record.setImportFileId("testDoc");
        record.setImportFileName("testDoc");
        ApprovalProcessInfoEntityDTO tempApprovalInfo = new ApprovalProcessInfoEntityDTO();
        tempApprovalInfo.setBillNo("test123");
        tempApprovalInfo.setSeq(0);
        tempApprovalInfo.setNodeCode("test");
        tempApprovalInfo.setApproverId("00286523");
        List<ApprovalProcessInfoEntityDTO> approvalProcessList = new ArrayList<>();
        approvalProcessList.add(tempApprovalInfo);


        PageRows<TaskVo> taskVoPageRows = new PageRows<>();
        List<TaskVo> taskVos = new ArrayList<>();
        TaskVo taskVo = new TaskVo();
        taskVo.setBusinessId("test123");
        taskVo.setTaskId("test123");
        taskVo.setFlowInstanceId("test123");
        taskVos.add(taskVo);
        taskVoPageRows.setRows(taskVos);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.any())).thenReturn(map);
        PowerMockito.when(approvalProcessInfoRepository.getList(Mockito.any())).thenReturn(approvalProcessList);
        PowerMockito.when(approvalRemoteService.changeInstanceParamByFlowInstanceId(Mockito.anyString(),Mockito.anyString(),Mockito.any(),Mockito.any())).thenReturn("");
        PowerMockito.when(approvalProcessInfoRepository.updateApproverInfo(Mockito.any())).thenReturn(1);
        List<ApprovalNodeInfoDTO> approvalNodeInfoDTOList = new ArrayList<>();
        ApprovalNodeInfoDTO approvalNodeInfoDTO = new ApprovalNodeInfoDTO();
        approvalNodeInfoDTO.setStatus(Constant.ACTIVE);
        approvalNodeInfoDTOList.add(approvalNodeInfoDTO);
        List<ApproverInfo> approverInfoList = new ArrayList<>();
        ApproverInfo approverInfo = new ApproverInfo();
        approverInfo.setApproverId("102704");
        approverInfo.setResult("Y");
        approverInfoList.add(approverInfo);
        ApproverInfo approverInfo2 = new ApproverInfo();
        approverInfo2.setApproverId("00286523");
        approverInfo2.setResult("N");
        approverInfoList.add(approverInfo2);
        ApproverInfo approverInfo3 = new ApproverInfo();
        approverInfo3.setApproverId("102704");
        approverInfoList.add(approverInfo3);
        approvalNodeInfoDTO.setApproverList(approverInfoList);
        PowerMockito.when(approvalRemoteService.showFlowInstancePanorama(any(),anyString(),any())).thenReturn(approvalNodeInfoDTOList);
        approvalProcessInfoService.scrapFlowWithChangeApproval(record);
        record.setFlowCode("FlowCode");
        approvalProcessInfoService.scrapFlowWithChangeApproval(record);
        Assert.assertNotNull(record);
    }

    @Test
    public void scrapFlowWithChangeApproval() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class, ConstantInterface.class, HttpRemoteService.class, CommonUtils.class, HttpRemoteUtil.class,ApprovalTaskClient.class);

        Map<String,HrmPersonInfoDTO> map = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("hl");
        hrmPersonInfoDTO.setId("00286523");
        map.put("00286523",hrmPersonInfoDTO);
        ScrapBillHeaderEntityDTO record = new ScrapBillHeaderEntityDTO();
        record.setBillNo("test123");
        record.setCurrApprover("00286523");
        record.setBillStatus("审批中");
        record.setCurrApprover("00286523");
        record.setEmpNo("00286523");
        record.setChangedApprover("00286523");
        record.setScrapType("1");
        record.setImportFileId("testDoc");
        record.setImportFileName("testDoc");
        ApprovalProcessInfoEntityDTO tempApprovalInfo = new ApprovalProcessInfoEntityDTO();
        tempApprovalInfo.setBillNo("test123");
        tempApprovalInfo.setSeq(0);
        tempApprovalInfo.setNodeCode("test");
        tempApprovalInfo.setApproverId("00286523");
        List<ApprovalProcessInfoEntityDTO> approvalProcessList = new ArrayList<>();
        approvalProcessList.add(tempApprovalInfo);


        PageRows<TaskVo> taskVoPageRows = new PageRows<>();
        List<TaskVo> taskVos = new ArrayList<>();
        TaskVo taskVo = new TaskVo();
        taskVo.setBusinessId("test123");
        taskVo.setTaskId("test123");
        taskVo.setFlowInstanceId("test123");
        taskVos.add(taskVo);
        taskVoPageRows.setRows(taskVos);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.any())).thenReturn(map);
        PowerMockito.when(approvalProcessInfoRepository.getList(Mockito.any())).thenReturn(approvalProcessList);
        PowerMockito.when(approvalRemoteService.changeInstanceParamByFlowInstanceId(Mockito.anyString(),Mockito.anyString(),Mockito.any(),Mockito.any())).thenReturn("");
        PowerMockito.when(approvalProcessInfoRepository.updateApproverInfo(Mockito.any())).thenReturn(1);
        List<ApprovalNodeInfoDTO> approvalNodeInfoDTOList = new ArrayList<>();
        ApprovalNodeInfoDTO approvalNodeInfoDTO = new ApprovalNodeInfoDTO();
        approvalNodeInfoDTO.setStatus(Constant.ACTIVE);
        approvalNodeInfoDTOList.add(approvalNodeInfoDTO);
        List<ApproverInfo> approverInfoList = new ArrayList<>();
        ApproverInfo approverInfo = new ApproverInfo();
        approverInfo.setApproverId("102704");
        approverInfo.setResult("Y");
        approverInfoList.add(approverInfo);
        ApproverInfo approverInfo2 = new ApproverInfo();
        approverInfo2.setApproverId("102704");
        approverInfo2.setResult("N");
        approverInfoList.add(approverInfo2);
        ApproverInfo approverInfo3 = new ApproverInfo();
        approverInfo3.setApproverId("102704");
        approverInfoList.add(approverInfo3);
        approvalNodeInfoDTO.setApproverList(approverInfoList);
        PowerMockito.when(approvalRemoteService.showFlowInstancePanorama(any(),anyString(),any())).thenReturn(approvalNodeInfoDTOList);
        approvalProcessInfoService.scrapFlowWithChangeApproval(record);
        record.setFlowCode("FlowCode");
        approvalProcessInfoService.scrapFlowWithChangeApproval(record);
        Assert.assertNotNull(record);
    }


    @Test
    public void getUrl() throws Exception {
        MockitoAnnotations.initMocks(this);
        ReflectionTestUtils.setField(approvalProcessInfoService,"scrapFlowCode","zte-mes-manufactureshare-scrap");
        ReflectionTestUtils.setField(approvalProcessInfoService,"scrapTerminalFlowCode","zte-mes-manufactureshare-scrap-terminal");
        Whitebox.invokeMethod(approvalProcessInfoService,"getUrl","zte-mes-manufactureshare-scrap");
        Assert.assertNotNull(Whitebox.invokeMethod(approvalProcessInfoService,"getUrl","zte-mes-manufactureshare-scrap-terminal"));
    }
}
