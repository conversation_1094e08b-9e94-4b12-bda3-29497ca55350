package com.zte.autoTest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.IMESLogService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.impl.PdmTechnicalChangeInfoServiceImpl;
import com.zte.application.kafka.producer.PdmTechnicalChangeInfoProducer;
import com.zte.application.sncabind.PsTaskService;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.*;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.PlanScheduleRemoteService;
import com.zte.interfaces.dto.PdmTechnicalChangeInfoEntityDTO;
import com.zte.interfaces.dto.QaKxonlineBomlockedEntityDTO;
import com.zte.interfaces.dto.SynchronizeSpmDateDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.sncabind.dto.PsTaskDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Matchers.*;

/**
 * <AUTHOR>
 * @date 2022/10/20
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpClientUtil.class, CommonUtils.class,RedisHelper.class,
        BasicsettingRemoteService.class, ConstantInterface.class, DatawbRemoteService.class})
public class PdmTechnicalChangeInfoServiceImplTest extends BaseTestCase {
    @InjectMocks
    private PdmTechnicalChangeInfoServiceImpl pdmTechnicalChangeInfoService;
    @Mock
    private PdmTechnicalChangeInfoRepository pdmTechnicalChangeInforepository;

    @Mock
    LocaleMessageSourceBean lmb;
    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Mock
    private PsTaskService psTaskService;
    @Mock
    private IMESLogService iMESLogService;
    @Mock
    private PdmTechnicalChangeInfoProducer pdmTechnicalChangeInfoProducer;
    @Mock
    private SysLookupTypesRepository sysLookupTypesRepository;
    @Mock
    private PlanScheduleRemoteService planScheduleRemoteService;

    @Mock
    SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private DatawbRemoteService datawbRemoteService;

    @Mock
    private TechnicalSummaryInfoRepository technicalSummaryInfoRepository;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> redisOpsValue;

    @Test
    public void sendKafkaMsg() throws Exception {
        List<PdmTechnicalChangeInfoEntityDTO> oldDataList = new ArrayList<>();
        oldDataList.add(new PdmTechnicalChangeInfoEntityDTO(){{setChgReqNo("1");setUpdatedDate(new Date());}});
        oldDataList.add(new PdmTechnicalChangeInfoEntityDTO(){{setChgReqNo("1");setUpdatedDate(new Date());}});
        Date date = new Date();
        Map<String, List<PdmTechnicalChangeInfoEntityDTO>> pdmMap = new HashMap<>();
        pdmMap.put("1",oldDataList);
        for (Map.Entry<String, List<PdmTechnicalChangeInfoEntityDTO>> entry : pdmMap.entrySet()) {
            pdmTechnicalChangeInfoService.sendKafkaMsg(oldDataList, entry);
        }
        for (int i = 0; i < 100000; i++) {
            oldDataList.add(new PdmTechnicalChangeInfoEntityDTO(){{setChgReqNo("1");setChangeReason("技改原因");setUpdatedDate(date);}});
        }
        pdmMap.put("1",oldDataList);
        for (Map.Entry<String, List<PdmTechnicalChangeInfoEntityDTO>> entry : pdmMap.entrySet()) {
            pdmTechnicalChangeInfoService.sendKafkaMsg(oldDataList, entry);
        }
        assertNotNull(oldDataList.get(0).getChgReqNo());
    }
    @Test
    public void monitorTechnicalOrderProcessing() throws Exception {
        ReflectionTestUtils.setField(pdmTechnicalChangeInfoService, "pdmDealFailedHours", 2);
        List<PdmTechnicalChangeInfoEntityDTO> list = new ArrayList<>();
        PowerMockito.when(pdmTechnicalChangeInforepository.getProcessingFailureData(any())).thenReturn(list);
        int num = pdmTechnicalChangeInfoService.monitorTechnicalOrderProcessing();
        Assert.assertEquals(num, NumConstant.NUM_ZERO);
        list.add(new PdmTechnicalChangeInfoEntityDTO() {{
            setChgReqNo("");
        }});
        PowerMockito.when(pdmTechnicalChangeInforepository.getProcessingFailureData(any())).thenReturn(list);
        num = pdmTechnicalChangeInfoService.monitorTechnicalOrderProcessing();
        Assert.assertEquals(num, NumConstant.NUM_ONE);
    }
    @Test
    public void getPdmTechnicalChangeInfoEntityDTOS()throws Exception {
        PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO = new PdmTechnicalChangeInfoEntityDTO();
        Whitebox.invokeMethod(pdmTechnicalChangeInfoService,"getPdmTechnicalChangeInfoEntityDTOS",pdmTechnicalChangeInfoEntityDTO,pdmTechnicalChangeInfoEntityDTO);
        List<String> chgList = new ArrayList<>();
        chgList.add("232");
        pdmTechnicalChangeInfoEntityDTO.setChgReqNoList(chgList);
        Assert.assertNotNull(Whitebox.invokeMethod(pdmTechnicalChangeInfoService,"getPdmTechnicalChangeInfoEntityDTOS",pdmTechnicalChangeInfoEntityDTO,pdmTechnicalChangeInfoEntityDTO));
    }
    @Test
    public void dealNoProcessingRequiredData() throws Exception {
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList = new ArrayList<>();
        for (int i = 0; i < 55; i++) {
            PdmTechnicalChangeInfoEntityDTO param = new PdmTechnicalChangeInfoEntityDTO();
            param.setUpdatedDate(new Date());
            PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO = new PdmTechnicalChangeInfoEntityDTO();
            pdmTechnicalChangeInfoEntityDTO.setUpdatedDate(new Date());
            pdmTechnicalChangeInfoEntityDTO.setChgReqNo("55520055");
            pdmTechnicalChangeInfoEntityDTO.setFactoryId("55");
            pdmTechnicalChangeInfoEntityDTOList.add(pdmTechnicalChangeInfoEntityDTO);
        }
        PdmTechnicalChangeInfoEntityDTO param = new PdmTechnicalChangeInfoEntityDTO();
        param.setUpdatedDate(new Date());
        PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO = new PdmTechnicalChangeInfoEntityDTO();
        pdmTechnicalChangeInfoEntityDTO.setUpdatedDate(new Date());
        pdmTechnicalChangeInfoEntityDTO.setChgReqNo("55520055");
        pdmTechnicalChangeInfoEntityDTO.setFactoryId("55");
        pdmTechnicalChangeInfoEntityDTO.setTaskStatus(Constant.TASK_COMPLETED);
        pdmTechnicalChangeInfoEntityDTOList.add(pdmTechnicalChangeInfoEntityDTO);
        Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "dealNoProcessingRequiredData", pdmTechnicalChangeInfoEntityDTOList);
        Assert.assertNotNull(pdmTechnicalChangeInfoEntityDTOList);
    }

    @Test
    public void dealMoreThanFifty2() throws Exception {
        Map<String, List<PdmTechnicalChangeInfoEntityDTO>> allMap = new HashMap<>();
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            PdmTechnicalChangeInfoEntityDTO param = new PdmTechnicalChangeInfoEntityDTO();
            param.setUpdatedDate(new Date());
            PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO = new PdmTechnicalChangeInfoEntityDTO();
            pdmTechnicalChangeInfoEntityDTO.setUpdatedDate(new Date());
            pdmTechnicalChangeInfoEntityDTO.setChgReqNo("55520055");
            pdmTechnicalChangeInfoEntityDTO.setFactoryId("55");
            pdmTechnicalChangeInfoEntityDTOList.add(pdmTechnicalChangeInfoEntityDTO);
        }
        PdmTechnicalChangeInfoEntityDTO param = new PdmTechnicalChangeInfoEntityDTO();
        param.setUpdatedDate(new Date());
        PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO = new PdmTechnicalChangeInfoEntityDTO();
        pdmTechnicalChangeInfoEntityDTO.setUpdatedDate(new Date());
        pdmTechnicalChangeInfoEntityDTO.setChgReqNo("55520055");
        pdmTechnicalChangeInfoEntityDTO.setFactoryId("55");
        pdmTechnicalChangeInfoEntityDTO.setTaskStatus(Constant.TASK_COMPLETED);
        pdmTechnicalChangeInfoEntityDTOList.add(pdmTechnicalChangeInfoEntityDTO);
        allMap.put("55520055", pdmTechnicalChangeInfoEntityDTOList);
        Assert.assertNotNull(Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "dealMoreThanFifty", allMap));
    }

    @Test
    public void dealMoreThanFifty() throws Exception {
        Map<String, List<PdmTechnicalChangeInfoEntityDTO>> allMap = new HashMap<>();
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList = new ArrayList<>();
        for (int i = 0; i < 155; i++) {
            PdmTechnicalChangeInfoEntityDTO param = new PdmTechnicalChangeInfoEntityDTO();
            param.setUpdatedDate(new Date());
            PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO = new PdmTechnicalChangeInfoEntityDTO();
            pdmTechnicalChangeInfoEntityDTO.setUpdatedDate(new Date());
            pdmTechnicalChangeInfoEntityDTO.setChgReqNo("55520055");
            pdmTechnicalChangeInfoEntityDTO.setFactoryId("55");
            pdmTechnicalChangeInfoEntityDTOList.add(pdmTechnicalChangeInfoEntityDTO);
        }
        PdmTechnicalChangeInfoEntityDTO param = new PdmTechnicalChangeInfoEntityDTO();
        param.setUpdatedDate(new Date());
        PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO = new PdmTechnicalChangeInfoEntityDTO();
        pdmTechnicalChangeInfoEntityDTO.setUpdatedDate(new Date());
        pdmTechnicalChangeInfoEntityDTO.setChgReqNo("55520055");
        pdmTechnicalChangeInfoEntityDTO.setFactoryId("55");
        pdmTechnicalChangeInfoEntityDTO.setTaskStatus(Constant.TASK_COMPLETED);
        pdmTechnicalChangeInfoEntityDTOList.add(pdmTechnicalChangeInfoEntityDTO);
        allMap.put("55520055", pdmTechnicalChangeInfoEntityDTOList);
        Assert.assertNotNull(Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "dealMoreThanFifty", allMap));
    }

    @Test
    public void lockInformationSPMSynchronizationToIMES() throws Exception {
        List<SysLookupTypesDTO> valuesList = new ArrayList<>();
        SysLookupTypesDTO typesDTO = new SysLookupTypesDTO();
        SysLookupTypesDTO typesDTO1 = new SysLookupTypesDTO();
        SysLookupTypesDTO typesDTO2 = new SysLookupTypesDTO();
        typesDTO.setLookupMeaning("53");
        typesDTO1.setLookupMeaning("52");
        typesDTO2.setLookupMeaning("55");
        valuesList.add(typesDTO);
        valuesList.add(typesDTO1);
        valuesList.add(typesDTO2);
        PowerMockito.when(sysLookupTypesRepository.getList(Mockito.any())).thenReturn(valuesList);

        List<QaKxonlineBomlockedEntityDTO> qaKxonlineBomlockedEntityDTOList = new ArrayList<>();
        QaKxonlineBomlockedEntityDTO qaKxonlineBomlockedEntityDTO = new QaKxonlineBomlockedEntityDTO();
        qaKxonlineBomlockedEntityDTO.setBomid(777888);
        qaKxonlineBomlockedEntityDTOList.add(qaKxonlineBomlockedEntityDTO);
        QaKxonlineBomlockedEntityDTO qaKxonlineBomlockedEntityDTO1 = new QaKxonlineBomlockedEntityDTO();
        qaKxonlineBomlockedEntityDTO1.setBomid(7778889);
        qaKxonlineBomlockedEntityDTOList.add(qaKxonlineBomlockedEntityDTO1);

        QaKxonlineBomlockedEntityDTO qaKxonlineBomlockedEntityDTO2 = new QaKxonlineBomlockedEntityDTO();
        qaKxonlineBomlockedEntityDTO2.setBomid(777);
        qaKxonlineBomlockedEntityDTOList.add(qaKxonlineBomlockedEntityDTO2);

        QaKxonlineBomlockedEntityDTO qaKxonlineBomlockedEntityDTO3 = new QaKxonlineBomlockedEntityDTO();
        qaKxonlineBomlockedEntityDTO3.setBomid(888);
        qaKxonlineBomlockedEntityDTOList.add(qaKxonlineBomlockedEntityDTO3);

        QaKxonlineBomlockedEntityDTO qaKxonlineBomlockedEntityDTO4 = new QaKxonlineBomlockedEntityDTO();
        qaKxonlineBomlockedEntityDTO4.setBomid(7891459);
        qaKxonlineBomlockedEntityDTOList.add(qaKxonlineBomlockedEntityDTO4);
        PowerMockito.when(datawbRemoteService.getSPMLockInformation(Mockito.any(), anyInt())).thenReturn(qaKxonlineBomlockedEntityDTOList);
        List<PsTask> psTasks = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setItemNo("12345");
        psTask.setProdplanId("");
        psTask.setOrgId(BigDecimal.ONE);
        psTasks.add(psTask);

        PsTask psTask1 = new PsTask();
        psTask1.setItemNo("12345");
        psTask1.setProdplanId("777888");
        psTask1.setOrgId(BigDecimal.ONE);
        psTasks.add(psTask1);

        PsTask psTask2 = new PsTask();
        psTask2.setItemNo("12345");
        psTask2.setOrgId(BigDecimal.ONE);
        psTask2.setProdplanId("7778889");
        psTask2.setFactoryId(new BigDecimal(55));
        psTasks.add(psTask2);

        PsTask psTask3 = new PsTask();
        psTask3.setItemNo("12345");
        psTask3.setProdplanId("777");
        psTask3.setFactoryId(new BigDecimal(51));
        psTask3.setOrgId(BigDecimal.ONE);
        psTasks.add(psTask3);

        PsTask psTask4 = new PsTask();
        psTask4.setItemNo("12345");
        psTask4.setOrgId(BigDecimal.ONE);
        psTask4.setProdplanId("888");
        psTask4.setTaskStatus(Constant.TASK_COMPLETED);
        psTask4.setFactoryId(new BigDecimal(51));
        psTasks.add(psTask4);
        PowerMockito.when(psTaskService.selectPsTaskByProdIdSet(Mockito.any())).thenReturn(psTasks);
        String msg = JSON.toJSONString(new ServiceData<>());
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(), Mockito.anyString(), Mockito.anyMap()))
                .thenReturn(msg);
        pdmTechnicalChangeInfoService.lockInformationSPMSynchronizationToIMES("1027");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void dealTechnicalBill() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class, CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                JSON.toJSONString("msg"));
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList = new ArrayList<>();
        PdmTechnicalChangeInfoEntityDTO param = new PdmTechnicalChangeInfoEntityDTO();
        param.setUpdatedDate(new Date());
        PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO = new PdmTechnicalChangeInfoEntityDTO();
        pdmTechnicalChangeInfoEntityDTO.setUpdatedDate(new Date());
        pdmTechnicalChangeInfoEntityDTO.setChgReqNo("55520055");
        pdmTechnicalChangeInfoEntityDTO.setFactoryId("55");
        pdmTechnicalChangeInfoEntityDTO.setProdplanId("777888");
        pdmTechnicalChangeInfoEntityDTOList.add(pdmTechnicalChangeInfoEntityDTO);

        PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO2 = new PdmTechnicalChangeInfoEntityDTO();
        pdmTechnicalChangeInfoEntityDTO2.setUpdatedDate(new Date());
        pdmTechnicalChangeInfoEntityDTO2.setChgReqNo("55520055");
        pdmTechnicalChangeInfoEntityDTO2.setFactoryId("52");
        pdmTechnicalChangeInfoEntityDTO2.setProdplanId("7778889");
        pdmTechnicalChangeInfoEntityDTOList.add(pdmTechnicalChangeInfoEntityDTO2);

        for (int i = 0; i < 55; i++) {
            PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO1 = new PdmTechnicalChangeInfoEntityDTO();
            pdmTechnicalChangeInfoEntityDTO1.setUpdatedDate(new Date());
            pdmTechnicalChangeInfoEntityDTO1.setChgReqNo("555200551");
            pdmTechnicalChangeInfoEntityDTO1.setProdplanId("777888" + i);
            pdmTechnicalChangeInfoEntityDTOList.add(pdmTechnicalChangeInfoEntityDTO1);
        }
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("2020-01-10 10:10:10");

        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(sysLookupValues);
        SysLookupValues sysLookupValues2 = new SysLookupValues();
        sysLookupValues2.setLookupMeaning("15");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(new SysLookupValues() {{
            setLookupCode(Constant.LOOKUP_TYPE_6717002);
        }})).thenReturn(sysLookupValues2);
        SysLookupValues sysLookupValues3 = new SysLookupValues();
        sysLookupValues3.setLookupMeaning("10");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(new SysLookupValues() {{
            setLookupCode(new BigDecimal(Constant.LOOK_UP_CODE_1003017001));
        }})).thenReturn(sysLookupValues3);

        PowerMockito.when(pdmTechnicalChangeInforepository.getMaxUpdatedDate(any())).thenReturn(param);
        PowerMockito.when(pdmTechnicalChangeInforepository.getNeedDealList(any())).thenReturn(pdmTechnicalChangeInfoEntityDTOList);
        List<PsTaskDTO> listDTO = new ArrayList<>();
        PsTaskDTO psTaskDTO = new PsTaskDTO();
        psTaskDTO.setProdplanId("777888");
        psTaskDTO.setFactoryId(new BigDecimal(55));
        psTaskDTO.setItemNo("12");
        listDTO.add(psTaskDTO);
        PsTaskDTO psTaskDTO2 = new PsTaskDTO();
        psTaskDTO2.setProdplanId("7778889");
        psTaskDTO2.setFactoryId(new BigDecimal(55));
        psTaskDTO2.setItemNo("12");
        listDTO.add(psTaskDTO2);
        PowerMockito.when(psTaskService.getFactoryIdByProdplanId(any())).thenReturn(listDTO);
        List<SysLookupTypesDTO> lookupValueList = new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTOUrl = new SysLookupTypesDTO();
        sysLookupTypesDTOUrl.setLookupMeaning("10270446,14");
        lookupValueList.add(sysLookupTypesDTOUrl);
        PowerMockito.when(sysLookupTypesRepository.getList(anyMap())).thenReturn(lookupValueList);

        List<PsTask> taskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("77");
        PowerMockito.when(planScheduleRemoteService.getPsTaskByProdPlanIdList(anyString(), any())).thenReturn(taskList);

        List<SysLookupValues> leadList = new ArrayList<>();
        SysLookupValues leadDto = new SysLookupValues();
        leadDto.setLookupMeaning("55");
        leadList.add(leadDto);
        PowerMockito.when(sysLookupValuesService.getList(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(leadList);
        try {pdmTechnicalChangeInfoService.dealTechnicalBill(param);} catch (Exception e) {
            Assert.assertEquals(null,e.getMessage());
        }
    }


    @Test
    public void init() throws Exception {
        pdmTechnicalChangeInfoService.init();
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void batchUpdateStatus() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                JSON.toJSONString("msg"));
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList = new ArrayList<>();
        PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO = new PdmTechnicalChangeInfoEntityDTO();
        pdmTechnicalChangeInfoEntityDTO.setChgReqNo("55561156");
        pdmTechnicalChangeInfoEntityDTOList.add(pdmTechnicalChangeInfoEntityDTO);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        PowerMockito.when(redisOpsValue.get(any())).thenReturn("Y");
        pdmTechnicalChangeInfoService.batchUpdateStatus(pdmTechnicalChangeInfoEntityDTOList);
        Assert.assertNotNull(pdmTechnicalChangeInfoEntityDTOList);
    }

    @Test
    public void updateTechnicalChangeInfoDealStatus() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class);
        List<PdmTechnicalChangeInfoEntityDTO> pdmTechnicalChangeInfoEntityDTOList = new ArrayList<>();
        PdmTechnicalChangeInfoEntityDTO pdmTechnicalChangeInfoEntityDTO = new PdmTechnicalChangeInfoEntityDTO();
        pdmTechnicalChangeInfoEntityDTOList.add(pdmTechnicalChangeInfoEntityDTO);
        pdmTechnicalChangeInfoService.updateTechnicalChangeInfoDealStatus(pdmTechnicalChangeInfoEntityDTOList,true);
        pdmTechnicalChangeInfoEntityDTO = new PdmTechnicalChangeInfoEntityDTO();
        pdmTechnicalChangeInfoEntityDTO.setDealStatus("F");
        pdmTechnicalChangeInfoEntityDTO.setChgReqNo("no1");
        pdmTechnicalChangeInfoEntityDTOList.add(pdmTechnicalChangeInfoEntityDTO);

        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);
        PowerMockito.when(redisOpsValue.get(any())).thenReturn("Y");

        pdmTechnicalChangeInfoService.updateTechnicalChangeInfoDealStatus(pdmTechnicalChangeInfoEntityDTOList,true);
        PowerMockito.when(redisOpsValue.get(any())).thenReturn(null);
        pdmTechnicalChangeInfoService.updateTechnicalChangeInfoDealStatus(pdmTechnicalChangeInfoEntityDTOList,false);
        List<SysLookupTypesDTO> valuesList = new ArrayList<>();
        SysLookupTypesDTO typesDTO = new SysLookupTypesDTO();
        SysLookupTypesDTO typesDTO1 = new SysLookupTypesDTO();
        SysLookupTypesDTO typesDTO2 = new SysLookupTypesDTO();
        typesDTO.setLookupMeaning("53");
        typesDTO1.setLookupMeaning("52");
        typesDTO2.setLookupMeaning("55");
        valuesList.add(typesDTO);
        valuesList.add(typesDTO1);
        valuesList.add(typesDTO2);
        PowerMockito.when(sysLookupTypesRepository.getList(Mockito.any())).thenReturn(valuesList);
        pdmTechnicalChangeInfoService.updateTechnicalChangeInfoDealStatus(pdmTechnicalChangeInfoEntityDTOList,false);
        Assert.assertNotNull(pdmTechnicalChangeInfoEntityDTOList);
    }

    @Test
    public void getByNo() {
        pdmTechnicalChangeInfoService.getByNo("1","1");
        PowerMockito.when(pdmTechnicalChangeInforepository.getList(any())).thenReturn(Lists.newArrayList(
                new PdmTechnicalChangeInfoEntityDTO() {{
                    setLifecycleStatus("已发放");
                }},
                new PdmTechnicalChangeInfoEntityDTO() {{
                    setLifecycleStatus("审核中");
                }}
        ));
        Assert.assertNotNull(pdmTechnicalChangeInfoService.getByNo("1","2"));
    }

    @Test
    public void synchronizeSpmDateSnScheduleTaskTest() throws Exception {
        SynchronizeSpmDateDTO dto = new SynchronizeSpmDateDTO();
        try {
            pdmTechnicalChangeInfoService.synchronizeSpmDateSnScheduleTask(dto);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        try {
            pdmTechnicalChangeInfoService.synchronizeSpmDateScheduleTask(dto);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("2022-01-01 11:11:11");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(sysLookupValues);

        dto.setLastSynchronizeTime("2022-02-01 11:11:11");
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                "error");
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("test");
        PowerMockito.mockStatic(DatawbRemoteService.class);

        List<SynchronizeSpmDateDTO> resultList = new ArrayList<>();
        SynchronizeSpmDateDTO synchronizeSpmDateDTO = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO.setChgRegNo("test");
        synchronizeSpmDateDTO.setProdplanId(123L);
        resultList.add(synchronizeSpmDateDTO);

        SynchronizeSpmDateDTO synchronizeSpmDateDTO1 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO1.setProdplanId(123L);
        resultList.add(synchronizeSpmDateDTO1);

        SynchronizeSpmDateDTO synchronizeSpmDateDTO2 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO2.setChgRegNo("test");
        resultList.add(synchronizeSpmDateDTO2);
        SynchronizeSpmDateDTO synchronizeSpmDateDTO3 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO3.setChgRegNo("test");
        synchronizeSpmDateDTO3.setProdplanId(1234L);
        resultList.add(synchronizeSpmDateDTO3);
        PowerMockito.when(DatawbRemoteService.pageSelectSPMDateSnForTechnicalChange(dto, 1, Constant.INT_1000))
                .thenReturn(resultList);
        PowerMockito.when(DatawbRemoteService.pageSelectSPMDateForTechnicalChange(dto, 1, Constant.INT_1000))
                .thenReturn(resultList);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("123");
        psTask.setFactoryId(new BigDecimal("52"));
        psTaskList.add(psTask);
        PowerMockito.when(psTaskService.queryFactoryIdByProdIdList(anyList()))
                .thenReturn(psTaskList);

        pdmTechnicalChangeInfoService.synchronizeSpmDateSnScheduleTask(dto);

        pdmTechnicalChangeInfoService.synchronizeSpmDateScheduleTask(dto);
    }

    @Test
    public void getEmailMsgTest() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                JSON.toJSONString("msg"));
        SynchronizeSpmDateDTO dto = new SynchronizeSpmDateDTO();
        Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "getEmailMsg", new Exception(), dto);

        dto.setStartTime(new Date());
        dto.setEndTime(new Date());
        Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "getEmailMsg", new Exception(), dto);

        Assert.assertNotNull(Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "getEmailMsg",
                new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.FAILED_TO_GET_REDIS_LOCK, new String[]{"1"}), dto));

    }

    @Test
    public void sendEmailMsgTest() throws Exception {

        Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "sendEmailMsg",
                "test", "test");

        List<SysLookupValues> sysList = new ArrayList<>();
        PowerMockito.when(sysLookupValuesRepository.getList(any())).thenReturn(sysList);
        Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "sendEmailMsg",
                "test", "test");
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setAttribute1(Constant.EMAIL);
        sysLookupValues.setLookupMeaning("122323");
        Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "sendEmailMsg",
                "test", "test");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void synchronizeLastDayFinishTechScheduleTaskTest() throws Exception {
        SynchronizeSpmDateDTO dto = new SynchronizeSpmDateDTO();
        try {
            pdmTechnicalChangeInfoService.synchronizeLastDayFinishTechScheduleTask(dto);
        } catch (Exception e) {
        }
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("2022-01-01 11:11:11");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(sysLookupValues);

        dto.setLastSynchronizeTime("2022-02-01 11:11:11");
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                "error");
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("test");
        PowerMockito.mockStatic(DatawbRemoteService.class);

        List<SynchronizeSpmDateDTO> resultList = new ArrayList<>();
        SynchronizeSpmDateDTO synchronizeSpmDateDTO = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO.setChgRegNo("test");
        synchronizeSpmDateDTO.setProdplanId(123L);
        resultList.add(synchronizeSpmDateDTO);

        SynchronizeSpmDateDTO synchronizeSpmDateDTO1 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO1.setProdplanId(123L);
        resultList.add(synchronizeSpmDateDTO1);

        SynchronizeSpmDateDTO synchronizeSpmDateDTO2 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO2.setChgRegNo("test");
        resultList.add(synchronizeSpmDateDTO2);
        SynchronizeSpmDateDTO synchronizeSpmDateDTO3 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO3.setChgRegNo("test");
        synchronizeSpmDateDTO3.setProdplanId(1234L);
        resultList.add(synchronizeSpmDateDTO3);
        PowerMockito.when(DatawbRemoteService.selectLastDayFinishTechSPMData())
                .thenReturn(resultList);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("123");
        psTask.setFactoryId(new BigDecimal("52"));
        psTaskList.add(psTask);
        PowerMockito.when(psTaskService.queryFactoryIdByProdIdList(anyList()))
                .thenReturn(psTaskList);
        pdmTechnicalChangeInfoService.synchronizeLastDayFinishTechScheduleTask(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void synchronizeFinishTechDataScheduleTaskTest() throws Exception {
        SynchronizeSpmDateDTO dto = new SynchronizeSpmDateDTO();
        try {
            pdmTechnicalChangeInfoService.synchronizeFinishTechDataScheduleTask(dto);
        } catch (Exception e) {
        }
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("2022-01-01 11:11:11");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(sysLookupValues);

        dto.setLastSynchronizeTime("2022-02-01 11:11:11");
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                "error");
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("test");
        PowerMockito.mockStatic(DatawbRemoteService.class);

        List<SynchronizeSpmDateDTO> resultList = new ArrayList<>();
        SynchronizeSpmDateDTO synchronizeSpmDateDTO = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO.setChgRegNo("test");
        synchronizeSpmDateDTO.setProdplanId(123L);
        resultList.add(synchronizeSpmDateDTO);

        SynchronizeSpmDateDTO synchronizeSpmDateDTO1 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO1.setProdplanId(123L);
        resultList.add(synchronizeSpmDateDTO1);

        SynchronizeSpmDateDTO synchronizeSpmDateDTO2 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO2.setChgRegNo("test");
        resultList.add(synchronizeSpmDateDTO2);
        SynchronizeSpmDateDTO synchronizeSpmDateDTO3 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO3.setChgRegNo("test");
        synchronizeSpmDateDTO3.setProdplanId(1234L);
        resultList.add(synchronizeSpmDateDTO3);
        PowerMockito.when(DatawbRemoteService.pageSelectFinishTechSPMData(dto, 1, 1000))
                .thenReturn(resultList);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("123");
        psTask.setFactoryId(new BigDecimal("52"));
        psTaskList.add(psTask);
        PowerMockito.when(psTaskService.queryFactoryIdByProdIdList(anyList()))
                .thenReturn(psTaskList);
        pdmTechnicalChangeInfoService.synchronizeFinishTechDataScheduleTask(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void delegateToLocalFactoryTest() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                "error");

        BigDecimal factoryId = null;
        String emp = "test";
        List<SynchronizeSpmDateDTO> localFactorySpmDataList = new ArrayList<>();
        SynchronizeSpmDateDTO dto1 = new SynchronizeSpmDateDTO();
        localFactorySpmDataList.add(dto1);
        int optType = 1;
        factoryId = new BigDecimal("52");
        Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "delegateToLocalFactory", factoryId, emp, localFactorySpmDataList, optType);


        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        Map<String, Map<String, String>> map = new HashMap<>();
        Map<String, String> map1 = new HashMap<>();
        map1.put("lookupMeaning", "51");
        map1.put("attribute2", "testCS");
        map.put("1245001", map1);
        PowerMockito.when(BasicsettingRemoteService.getsysLookupTypeList(Mockito.anyString())).thenReturn(map);
        PowerMockito.when(sysLookupValuesService.getAddressByFactoryId(Mockito.anyString())).thenReturn("");

        Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "delegateToLocalFactory", factoryId, emp, localFactorySpmDataList, optType);
        PowerMockito.when(sysLookupValuesService.getAddressByFactoryId(Mockito.anyString())).thenReturn("2323");
        Assert.assertNotNull(Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "delegateToLocalFactory", factoryId, emp, localFactorySpmDataList, optType));

    }


    @Test
    public void specialTechDataSynchronizationToIMESTest() throws Exception {
        PowerMockito.mockStatic(DatawbRemoteService.class);
        SynchronizeSpmDateDTO dto = new SynchronizeSpmDateDTO();
        PowerMockito.when(technicalSummaryInfoRepository.selectTechSPMDataOfHead())
                .thenReturn(new ArrayList<>());
        try {
            pdmTechnicalChangeInfoService.specialTechDataSynchronizationToIMES();
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("2022-01-01 11:11:11");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(sysLookupValues);

        dto.setLastSynchronizeTime("2022-02-01 11:11:11");
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                "error");
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("test");


        List<SynchronizeSpmDateDTO> resultList = new ArrayList<>();
        SynchronizeSpmDateDTO synchronizeSpmDateDTO = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO.setChgRegNo("test");
        synchronizeSpmDateDTO.setProdplanId(123L);
        resultList.add(synchronizeSpmDateDTO);

        SynchronizeSpmDateDTO synchronizeSpmDateDTO1 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO1.setProdplanId(123L);
        resultList.add(synchronizeSpmDateDTO1);

        SynchronizeSpmDateDTO synchronizeSpmDateDTO2 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO2.setChgRegNo("test");
        resultList.add(synchronizeSpmDateDTO2);
        SynchronizeSpmDateDTO synchronizeSpmDateDTO3 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO3.setChgRegNo("test");
        synchronizeSpmDateDTO3.setProdplanId(1234L);
        resultList.add(synchronizeSpmDateDTO3);
        PowerMockito.when(technicalSummaryInfoRepository.selectTechSPMDataOfHead())
                .thenReturn(resultList);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("123");
        psTask.setFactoryId(new BigDecimal("52"));
        psTaskList.add(psTask);
        PowerMockito.when(psTaskService.queryFactoryIdByProdIdList(anyList()))
                .thenReturn(psTaskList);
        pdmTechnicalChangeInfoService.specialTechDataSynchronizationToIMES();

        PsTask psTask2 = new PsTask();
        psTask2.setProdplanId("1234");
        psTask2.setFactoryId(new BigDecimal("52"));
        psTaskList.add(psTask2);
        PowerMockito.when(psTaskService.queryFactoryIdByProdIdList(anyList()))
                .thenReturn(psTaskList);
        List<SynchronizeSpmDateDTO> spmDataOfHeadDTOList = new LinkedList<>();
        SynchronizeSpmDateDTO a1 = new SynchronizeSpmDateDTO();
        a1.setErrorMsg("ddd");
        spmDataOfHeadDTOList.add(a1);
        PowerMockito.when(DatawbRemoteService.selectTechSPMDataOfHead())
                .thenReturn(spmDataOfHeadDTOList)
        ;
        pdmTechnicalChangeInfoService.specialTechDataSynchronizationToIMES();
    }

    @Test
    public void synchronizeSpecialSPMSnScheduleTaskTest() throws Exception {
        PowerMockito.mockStatic(DatawbRemoteService.class);
        SynchronizeSpmDateDTO dto = new SynchronizeSpmDateDTO();
        try {
            pdmTechnicalChangeInfoService.synchronizeSpecialSPMSnScheduleTask(dto);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        try {
            pdmTechnicalChangeInfoService.synchronizeSpecialSPMSnScheduleTask(dto);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);

        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("2022-01-01 11:11:11");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(sysLookupValues);

        dto.setLastSynchronizeTime("2022-02-01 11:11:11");
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                "error");
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("test");
        PowerMockito.mockStatic(DatawbRemoteService.class);

        List<SynchronizeSpmDateDTO> resultList = new ArrayList<>();
        SynchronizeSpmDateDTO synchronizeSpmDateDTO = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO.setChgRegNo("test");
        synchronizeSpmDateDTO.setProdplanId(123L);
        resultList.add(synchronizeSpmDateDTO);

        SynchronizeSpmDateDTO synchronizeSpmDateDTO1 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO1.setProdplanId(123L);
        resultList.add(synchronizeSpmDateDTO1);

        SynchronizeSpmDateDTO synchronizeSpmDateDTO2 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO2.setChgRegNo("test");
        resultList.add(synchronizeSpmDateDTO2);
        SynchronizeSpmDateDTO synchronizeSpmDateDTO3 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO3.setChgRegNo("test");
        synchronizeSpmDateDTO3.setProdplanId(1234L);
        resultList.add(synchronizeSpmDateDTO3);
        PowerMockito.when(technicalSummaryInfoRepository.pageSelectSpecialSPMDataSn(dto))
                .thenReturn(resultList);
        PowerMockito.when(technicalSummaryInfoRepository.pageSelectSpecialSPMDataSn(dto))
                .thenReturn(resultList);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask = new PsTask();
        psTask.setProdplanId("123");
        psTask.setFactoryId(new BigDecimal("52"));
        psTaskList.add(psTask);
        PowerMockito.when(psTaskService.queryFactoryIdByProdIdList(anyList()))
                .thenReturn(psTaskList);

        pdmTechnicalChangeInfoService.synchronizeSpecialSPMSnScheduleTask(dto);
        SynchronizeSpmDateDTO synchronizeSpmDateDTO4 = new SynchronizeSpmDateDTO();
        synchronizeSpmDateDTO4.setChgRegNo("test");
        synchronizeSpmDateDTO4.setProdplanId(123L);
        synchronizeSpmDateDTO4.setErrorMsg("444");
        resultList.add(synchronizeSpmDateDTO4);
        pdmTechnicalChangeInfoService.synchronizeSpecialSPMSnScheduleTask(dto);
    }

    @Test
    public void getThresholdOfSysCodeTest() throws Exception {
        Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "getThresholdOfSysCode");
        SysLookupValues dto = new SysLookupValues();
        dto.setLookupMeaning("200");

        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any()))
                .thenReturn(dto);
        Assert.assertNotNull(Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "getThresholdOfSysCode"));

    }

    @Test
    public void checkProdStatusNumTest() throws Exception {
        List<PdmTechnicalChangeInfoEntityDTO> onlineBatchList = new ArrayList<>();
        PdmTechnicalChangeInfoEntityDTO dto1 = new PdmTechnicalChangeInfoEntityDTO();
        dto1.setFactoryId("52");
        onlineBatchList.add(dto1);
        PdmTechnicalChangeInfoEntityDTO dto2 = new PdmTechnicalChangeInfoEntityDTO();
        dto2.setFactoryId("52");
        onlineBatchList.add(dto2);
        PdmTechnicalChangeInfoEntityDTO dto3 = new PdmTechnicalChangeInfoEntityDTO();
        dto3.setFactoryId("55");
        onlineBatchList.add(dto3);
        PdmTechnicalChangeInfoEntityDTO dto4 = new PdmTechnicalChangeInfoEntityDTO();
        dto4.setFactoryId("58");
        onlineBatchList.add(dto4);
        PdmTechnicalChangeInfoEntityDTO dto5 = new PdmTechnicalChangeInfoEntityDTO();
        dto5.setFactoryId("58");
        onlineBatchList.add(dto5);
        PdmTechnicalChangeInfoEntityDTO dto6 = new PdmTechnicalChangeInfoEntityDTO();
        dto6.setFactoryId("58");
        onlineBatchList.add(dto6);
        Integer threshold = 3;
        Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "checkProdStatusNum", onlineBatchList, threshold);
        threshold = 2;
        Assert.assertFalse(Whitebox.invokeMethod(pdmTechnicalChangeInfoService, "checkProdStatusNum", onlineBatchList, threshold));
    }
}
