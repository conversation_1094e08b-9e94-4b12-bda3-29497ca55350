package com.zte.autoTest;

import com.zte.application.impl.SysLookupValuesServiceImpl;
import com.zte.application.impl.WarehouseEntryInfoHistoryServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.DateUtil;
import com.zte.domain.model.BarSubmit;
import com.zte.domain.model.BarSubmitRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.WarehouseEntryInfoHistoryRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.WarehouseEntryInfoHistoryDTO;
import com.zte.interfaces.dto.datawb.ProdPlanDetailDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;

/**
 * <AUTHOR>
 * @date 2022/6/16
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({RetCode.class, SpringContextUtil.class, RedisHelper.class})
public class WarehouseEntryInfoHistoryServiceImplTest extends BaseTestCase {
    @InjectMocks
    WarehouseEntryInfoHistoryServiceImpl warehouseEntryInfoHistoryServiceImpl;
    @Mock
    private WarehouseEntryInfoHistoryRepository warehouseEntryInfoHistoryRepository;
    @Mock
    private BarSubmitRepository barSubmitRepository;
    @Mock
    private SysLookupValuesServiceImpl sysLookupValuesService;
    @Mock
    private DatawbRemoteService datawbRemoteService;

    @Test
    public void getWarehouseEntryStatics() throws Exception {
        Map map = new HashMap();
        map.put("yearNo","2020");
        map.put("monthNo","01");

        Page<WarehouseEntryInfoHistoryDTO> page = new Page<>();
        page.setParams(map);
        try {
            warehouseEntryInfoHistoryServiceImpl.getWarehouseEntryInfoHistory(page);
        }catch (Exception e){
            Assert.assertEquals(MessageId.YEAR_OR_MONTH_IS_NULL, e.getMessage());
        }
        warehouseEntryInfoHistoryRepository.getWarehouseEntryInfoHistory(page);

        List<WarehouseEntryInfoHistoryDTO> list = new ArrayList<>();
        WarehouseEntryInfoHistoryDTO insertWarehouseEntryInfoHistoryDTO = new WarehouseEntryInfoHistoryDTO();
        insertWarehouseEntryInfoHistoryDTO.setWarehouseEntryId(String.valueOf(new Random().nextInt(9999)));
        list.add(insertWarehouseEntryInfoHistoryDTO);
        warehouseEntryInfoHistoryServiceImpl.insertWarehouseEntryInfoHistory(list,new BigDecimal("55"));
        warehouseEntryInfoHistoryRepository.insertWarehouseEntryInfoHistory(list);

        List<String> warehouseEntryIdList = new ArrayList<>();
        warehouseEntryIdList.add("2");
        BigDecimal factoryId = new BigDecimal("55");
        warehouseEntryInfoHistoryServiceImpl.queryWarehouseEntryIdIsExist(warehouseEntryIdList,factoryId);
        warehouseEntryInfoHistoryRepository.queryWarehouseEntryIdIsExist(warehouseEntryIdList,factoryId);
    }

    @Test
    public void stockStaticsCenter() throws Exception{
        PowerMockito.mockStatic(RedisHelper.class);
        WarehouseEntryInfoHistoryDTO dto = new WarehouseEntryInfoHistoryDTO();
        PowerMockito.when(RedisHelper.setnx(any(), any(), anyInt())).thenReturn(true);
        SysLookupValues sysLookupValues = new SysLookupValues();
        List<ProdPlanDetailDTO> planDetailDTOS = new ArrayList<>();
        ProdPlanDetailDTO detailDTO = new ProdPlanDetailDTO();
        detailDTO.setProdplanId(new BigDecimal(55555));
        detailDTO.setBomNo("12345ABB");
        PowerMockito.when(datawbRemoteService.getItemNoByProdplanId(any())).thenReturn(planDetailDTOS);
        try {
            warehouseEntryInfoHistoryServiceImpl.stockStaticsCenter(dto);
        } catch (Exception e){
            Assert.assertEquals(null, e.getMessage());
        }
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(sysLookupValues);
        sysLookupValues.setLookupMeaning("2022-12-10 00:00:01");
        try {
            warehouseEntryInfoHistoryServiceImpl.stockStaticsCenter(dto);
        } catch (Exception e){
            Assert.assertEquals(MessageId.YEAR_OR_MONTH_IS_NULL, e.getMessage());
        }
        sysLookupValues.setLookupMeaning("2332-12-10 00:00:01");
        dto.setStartDateStr("2022-12-10 00:00:00");
        dto.setEndDateStr("2022-01-13 00:00:01");
        try {
            warehouseEntryInfoHistoryServiceImpl.stockStaticsCenter(dto);
        } catch (Exception e){
            Assert.assertEquals(MessageId.YEAR_OR_MONTH_IS_NULL, e.getMessage());
        }
        dto.setEndDateStr("2023-01-13 00:00:01");
        List<WarehouseEntryInfoHistoryDTO> barList = new ArrayList<>();
        WarehouseEntryInfoHistoryDTO dto1= new WarehouseEntryInfoHistoryDTO();
        Long count = 0L;
        PowerMockito.when(barSubmitRepository.getCountByTime(any())).thenReturn(count);
        try {
            warehouseEntryInfoHistoryServiceImpl.stockStaticsCenter(dto);
        } catch (Exception e){
            Assert.assertEquals(MessageId.YEAR_OR_MONTH_IS_NULL, e.getMessage());
        }
        count = 1L;
        PowerMockito.when(barSubmitRepository.getCountByTime(any())).thenReturn(count);
        dto1.setWarehouseEntryId("123");
        dto1.setProdplanId(new BigDecimal(5555));
        barList.add(dto1);
        PowerMockito.when(barSubmitRepository.getBarSubmitByTime(any())).thenReturn(barList);
        BarSubmit startStopTime = new BarSubmit();
        startStopTime.setEndDate(DateUtil.convertStringToDate("2332-12-10 00:00:02", DateUtil.DATE_FORMATE_FULL));
        PowerMockito.when(barSubmitRepository.getStartStopTime()).thenReturn(startStopTime);
        PowerMockito.when(warehouseEntryInfoHistoryRepository.insertWarehouseEntryStatics(any())).thenReturn(1);
        try {
            warehouseEntryInfoHistoryServiceImpl.stockStaticsCenter(dto);
        } catch (Exception e){
            Assert.assertEquals(MessageId.QUERY_BOM_NO_NULL, e.getMessage());
        }
        dto1.setProdplanId(new BigDecimal(55555));
        try {
            warehouseEntryInfoHistoryServiceImpl.stockStaticsCenter(dto);
        } catch (Exception e){
            Assert.assertEquals(MessageId.QUERY_BOM_NO_NULL, e.getMessage());
        }
        dto.setEndDateStr(null);
        try {
            warehouseEntryInfoHistoryServiceImpl.stockStaticsCenter(dto);
        } catch (Exception e){
            Assert.assertEquals(MessageId.QUERY_BOM_NO_NULL, e.getMessage());
        }
        planDetailDTOS.add(detailDTO);
        PowerMockito.when(datawbRemoteService.getItemNoByProdplanId(any())).thenReturn(planDetailDTOS);
        try {
            warehouseEntryInfoHistoryServiceImpl.stockStaticsCenter(dto);
        } catch (Exception e){
            Assert.assertEquals(null, e.getMessage());
        }
        warehouseEntryInfoHistoryServiceImpl.insertWarehouseEntryStatics(barList, dto);
    }
}
