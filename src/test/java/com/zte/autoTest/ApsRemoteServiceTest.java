package com.zte.autoTest;

import com.alibaba.fastjson.JSON;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.infrastructure.remote.ApsRemoteService;
import com.zte.interfaces.dto.APSDataDTO;
import com.zte.interfaces.dto.aps.ApsDerivativeCodeDTO;
import com.zte.interfaces.dto.aps.ApsDerivativeCodeQueryDTO;
import com.zte.interfaces.dto.aps.ApsQueryDTO;
import com.zte.interfaces.dto.aps.ApsResponseDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.microservice.name.MicroServiceNameEum;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ReflectUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import lombok.var;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2022-10-18 16:49
 */
@PrepareForTest({HttpRemoteUtil.class, ServiceDataBuilderUtil.class, JSON.class, MESHttpHelper.class
,JacksonJsonConverUtil.class})
public class ApsRemoteServiceTest extends BaseTestCase {
    @InjectMocks
    private ApsRemoteService apsRemoteService;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;


    /*Started by AICoder, pid:l031a8aadfrec75149b808a9a1d0cb388f45af9b*/

    @Before
    public void setUp() {
        // 使用反射设置私有字段的值
        try {
            var field = ApsRemoteService.class.getDeclaredField("getDerivedCodeUrl");
            field.setAccessible(true);
            field.set(apsRemoteService, "http://example.com/api");
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void getApsDerivativeCodeDTOS() {
        ReflectUtil.setFieldValue(apsRemoteService,"getDerivedCodeUrl","http://example.com/api");
        PowerMockito.mockStatic(HttpRemoteUtil.class,ServiceDataBuilderUtil.class);
        // Given
        List<ApsDerivativeCodeQueryDTO> queryList = Collections.singletonList(new ApsDerivativeCodeQueryDTO());
        String requestJson = JSON.toJSONString(queryList);
        String response = "{\"data\":\"success\"}";
        String bo = "[{\"id\":1,\"code\":\"code1\"}]";
        List<ApsDerivativeCodeDTO> expectedList = Collections.singletonList(new ApsDerivativeCodeDTO());

        when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(),any()))
                .thenReturn(response);
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response)).thenReturn(bo);

        // When
        List<ApsDerivativeCodeDTO> result = apsRemoteService.getApsDerivativeCodeDTOS(queryList);

        // Then
        assertEquals(expectedList.size(), result.size());
        when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(response)).thenReturn("");

        // When
        result = apsRemoteService.getApsDerivativeCodeDTOS(queryList);

        // Then
        assertNotEquals(expectedList.size(), result.size());
    }

    /*Ended by AICoder, pid:l031a8aadfrec75149b808a9a1d0cb388f45af9b*/

    @Test
    public void getAPSDataAll() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class,ServiceDataBuilderUtil.class);
        List<APSDataDTO> apsDataDTOList = new ArrayList<>();
        apsDataDTOList.add(new APSDataDTO(){{setPlanGroupId("1");setModelNo("1");}});
        ServiceData serviceData = new ServiceData();
        serviceData.setBo(apsDataDTOList);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn("");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("");
        try {
            apsRemoteService.getAPSDataAll();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.GET_APS_PLAN_GROUP_AND_MODEL_ERROR, e.getMessage());
        }
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(Mockito.anyString(), Mockito.any(), Mockito.any(), Mockito.any()))
                .thenReturn(JSON.toJSONString(serviceData));
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("[]");
        try {
            apsRemoteService.getAPSDataAll();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.GET_APS_PLAN_GROUP_AND_MODEL_ERROR, e.getMessage());
        }
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn(JSON.toJSONString(apsDataDTOList));
        apsRemoteService.getAPSDataAll();
    }
    @Test
    public void getBatchProdPlanSendBomHead() throws Exception {
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        List<SysLookupValues> list = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        a1.setLookupCode(new BigDecimal("1005052001"));
        a1.setLookupMeaning("123");
        list.add(a1);
        SysLookupValues a2 = new SysLookupValues();
        a2.setAttribute1(Constant.X_AUTH_VALUE);
        a2.setLookupMeaning("1234");
        list.add(a2);
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.anyMap()))
                .thenReturn(list)
        ;

        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(new HashMap<>());


        List<ApsResponseDTO> apsResponseDTOS = new LinkedList<>();
        ApsResponseDTO b1 = new ApsResponseDTO();
        apsResponseDTOS.add(b1);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.anyString())).thenReturn("rows");
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.anyString(),Mockito.anyObject()))
                .thenReturn(apsResponseDTOS);

        List<String> prodplanNoList = new LinkedList<>();
        prodplanNoList.add("99800-zft");
        ApsQueryDTO params = new ApsQueryDTO();
        params.setProdplanNoList(prodplanNoList);
        try{
            apsRemoteService.getBatchProdPlanSendBomHead(params);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TASK_NO_NOT_EXIST_APS, e.getMessage());
        }

    }
}
