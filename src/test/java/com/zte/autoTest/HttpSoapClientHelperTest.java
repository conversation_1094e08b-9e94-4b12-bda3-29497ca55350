package com.zte.autoTest;

import com.zte.common.HttpSoapClientHelper;
import com.zte.common.utils.Constant;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.io.ByteArrayInputStream;
import java.io.DataInputStream;
import java.io.DataOutputStream;
import java.io.FileInputStream;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/11/23 19:17
 * @Description
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpSoapClientHelper.class })
public class HttpSoapClientHelperTest {

    @Mock
    private FileInputStream inputStream;
    @Mock
    private URL url;
    @Mock
    private HttpURLConnection httpURLConnection;
    @Mock
    private DataOutputStream dataOutputStream;
    @Mock
    private DataInputStream dataInputStream;

    @Test
    public void testFormDataUploadFile() throws Exception {
        PowerMockito.whenNew(URL.class).withAnyArguments().thenReturn(url);
        PowerMockito.when(url.openConnection()).thenReturn(httpURLConnection);
        PowerMockito.whenNew(DataOutputStream.class).withAnyArguments().thenReturn(dataOutputStream);
        PowerMockito.whenNew(DataInputStream.class).withAnyArguments().thenReturn(dataInputStream);
        PowerMockito.when(dataInputStream.read(Mockito.any())).thenReturn(-1);
        PowerMockito.when(httpURLConnection.getInputStream()).thenReturn(new ByteArrayInputStream(new byte[]{123}));
        try{
            HttpSoapClientHelper.formDataUploadFile("http://test.imes.zte.com.cn/zte-mes-manufactureshare-centerfactory/resource/factoryList","test.docx",inputStream);
        }catch (Exception e){
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }
}
