package com.zte.autoTest;

import com.zte.application.impl.SpTagsParamServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SpTagsParam;
import com.zte.domain.model.SpTagsParamRepository;
import com.zte.interfaces.dto.SpTagsParamDTO;
import com.zte.interfaces.dto.SpTagsParamPageQueryDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.util.BaseTestCase;
import org.checkerframework.checker.units.qual.A;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/8/8 9:42
 * @Description
 */
@RunWith(PowerMockRunner.class)
public class SpTagsParamServiceTest extends BaseTestCase {

    @InjectMocks
    private SpTagsParamServiceImpl service;

    @Mock
    private SpTagsParamRepository spTagsParamRepository;

    @Test
    public void queryTags() {
        List<String> list = new ArrayList<>();
        PowerMockito.when(spTagsParamRepository.queryTags()).thenReturn(list);
        Assert.assertNotNull(service.queryTags());
    }

    @Test
    public void queryParams() {
        List<String> list = new ArrayList<>();
        PowerMockito.when(spTagsParamRepository.queryParams(Mockito.anyString())).thenReturn(list);
        Assert.assertNotNull(service.queryParams("test"));
    }

    @Test
    public void queryList() {
        PowerMockito.when(spTagsParamRepository.selectById(Mockito.any())).thenReturn(new SpTagsParam());
        SpTagsParamDTO query = new SpTagsParamDTO();
        Assert.assertNotNull(service.queryList(query));
    }

    @Test
    public void queryPage() {
        List<SpTagsParam> list = new ArrayList<>();
        PowerMockito.when(spTagsParamRepository.countPage(Mockito.any())).thenReturn(1l);
        PowerMockito.when(spTagsParamRepository.queryPage(Mockito.any())).thenReturn(list);
        SpTagsParamPageQueryDTO query = new SpTagsParamPageQueryDTO();
        PageRows<SpTagsParam> pageRows = service.queryPage(query);
        Assert.assertTrue(pageRows.getRows().size() >= 0);
    }

    @Test
    public void getById() {
        List<SpTagsParam> list = new ArrayList<>();
        PowerMockito.when(spTagsParamRepository.queryList(Mockito.any())).thenReturn(list);
        Assert.assertNull(service.getById("test"));
    }

    @Test
    public void save() {
        List<SpTagsParam> list = new ArrayList<>();
        SpTagsParam spTagsParam1 = new SpTagsParam();
        spTagsParam1.setParamName("test");
        spTagsParam1.setParamName("test2");
        list.add(spTagsParam1);
        SpTagsParam spTagsParam2 = new SpTagsParam();
        spTagsParam2.setId("test");
        spTagsParam2.setParamName("test");
        spTagsParam2.setParamName("test2");
        list.add(spTagsParam2);
        PowerMockito.when(spTagsParamRepository.insertBatch(Mockito.any())).thenReturn(1L);
        PowerMockito.when(spTagsParamRepository.updateById(Mockito.any())).thenReturn(1L);
        service.save(list);
        Assert.assertNotNull(list);
    }

    @Test
    public void add() {
        PowerMockito.when(spTagsParamRepository.insert(Mockito.any())).thenReturn(1L);
        service.add(new SpTagsParam());
        SpTagsParam spTagsParam = new SpTagsParam();
        Assert.assertNotNull(spTagsParam);
    }

    @Test
    public void updateById() {
        PowerMockito.when(spTagsParamRepository.insert(Mockito.any())).thenReturn(1L);
        service.updateById(new SpTagsParam());
        SpTagsParam spTagsParam = new SpTagsParam();
        Assert.assertNotNull(spTagsParam);
    }
    @Test
    public void removeByIds() {
        List<String> list = new ArrayList<>();
        PowerMockito.when(spTagsParamRepository.deleteByIds(Mockito.any())).thenReturn(1L);
        service.removeByIds(list);
        Assert.assertNotNull(list);
    }
}
