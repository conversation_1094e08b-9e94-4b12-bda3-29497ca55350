package com.zte.autoTest.unitTest;

import com.zte.application.*;
import com.zte.application.impl.HrmUserCenterServiceImpl;
import com.zte.application.impl.ProdBindingSettingServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.ProdBindingSettingRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

/**
 * 单元测试
 *
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ BasicsettingRemoteService.class,MESHttpHelper.class,
        RedisCacheUtils.class,ObtainRemoteServiceDataUtil.class,CrafttechRemoteService.class})
public class ProdBindingSettingServiceNewImplTest extends BaseTestCase {

    @InjectMocks
    private ProdBindingSettingServiceImpl prodBindingSettingServiceImpl;

    @Mock
    private RedisLock redisLock;

    @Mock
    private ProdBindingSettingRepository prodBindingSettingRepository;
    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private BBomHeaderService bBomHeaderService;
    @Mock
    private ProdUnbindingSettingService prodUnbindingSettingService;
    @Mock
    private SysLookupTypesService sysLookupTypesService;
    @Mock
    private HrmUserCenterServiceImpl hrmUserCenterService;
    @Mock
    private CommonService commonService;

    @Before
    public void init() {
        // 待测的类标注为@InjectMocks
        // 依赖的其他类标注为 @Mock
        // 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
        MockitoAnnotations.initMocks(this);
    }


    @Test
    public void getNeedBindListNew() throws Exception {
        PowerMockito.when(prodBindingSettingRepository.getNeedBindList(Mockito.any(), Mockito.any(), Mockito.any(),
                Mockito.any())).thenReturn(null);
        prodBindingSettingServiceImpl.getNeedBindList("55", "2", "2");
        prodBindingSettingServiceImpl.getNeedBindList(null, "2", "2");
        Assert.assertNull(prodBindingSettingServiceImpl.getNeedBindList("55", null, "2"));
    }

    @Test
    public void postNeedBindList() throws Exception {
        BindQueryDTO dto = new BindQueryDTO();
        dto.setProductCode("123");
        dto.setItemCode("123");
        dto.setMainProductCode("123");
        dto.setProdplanId("123");
        Assert.assertNotNull(prodBindingSettingServiceImpl.postNeedBindList(dto));
    }

    @Test
    public void queryProdBindingBatch() throws Exception {
        ProdBindingSettingDTO dto = new ProdBindingSettingDTO();
        List<String> productList = new LinkedList<>();
        productList.add("1");
        dto.setProductCodeList(productList);


        List<ProdBindingSettingDTO> settingDTOS = new LinkedList<>();
        ProdBindingSettingDTO a1 = new ProdBindingSettingDTO();
        settingDTOS.add(a1);
        PowerMockito.when(prodBindingSettingRepository.queryProdBindingBatch(Mockito.anyObject()))
                .thenReturn(settingDTOS);
        try{
            prodBindingSettingServiceImpl.queryProdBindingBatch(dto);
        } catch (Exception e){
            Assert.assertEquals(MessageId.PRODUCT_CODE_IS_EMPTY, e.getMessage());
        }
    }

    @Test
    public void queryProdBindingInfoPage() throws Exception {

        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(MESHttpHelper.class);

        Page<ProdBindingSettingDTO> page = new Page<>();
        ProdBindingSettingDTO prodBindingSettingDTO=new ProdBindingSettingDTO();
        prodBindingSettingDTO.setProductCode("ZTE1111");
        prodBindingSettingDTO.setItemCode("ZTE1111");
        prodBindingSettingDTO.setCreateBy("00286523");
        prodBindingSettingDTO.setProcessCode("S1015");
        page.setParams(prodBindingSettingDTO);
        List<ProdBindingSettingDTO> resultList =new ArrayList<>();
        ProdBindingSettingDTO resultDto=new ProdBindingSettingDTO();
        resultDto.setProductCode("ZTE1111");
        resultDto.setItemCode("ZTE1111");
        resultDto.setCreateBy("00286523");
        resultDto.setLastUpdatedBy("00286523");
        resultDto.setProcessCode("S1015");
        resultDto.setProcessName("出库");
        resultList.add(resultDto);

        List<BSProcess> bsProcessList=new ArrayList<>();
        BSProcess bsProcess=new BSProcess();
        bsProcess.setProcessCode("S1015");
        bsProcess.setProcessName("出库");
        bsProcessList.add(bsProcess);

        Map<String, HrmPersonInfoDTO> bsPubHrMap =new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO=new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("hl");
        bsPubHrMap.put("00286523",hrmPersonInfoDTO);

        PowerMockito.when(prodBindingSettingRepository.queryProdBindingInfoPage(Mockito.any())).thenReturn(resultList);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.any())).thenReturn(bsPubHrMap);
        PowerMockito.when(commonService.getIMESBasicUrl()).thenReturn("");
        Page<ProdBindingSettingDTO> pageInfo = prodBindingSettingServiceImpl.queryProdBindingInfoPage(prodBindingSettingDTO);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }
}
