package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.HrmUserCenterService;
import com.zte.application.impl.ProgramVerifyConfirmInfoServiceImpl;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@RunWith(PowerMockRunner.class)
public class ProgramVerifyConfirmInfoServiceImplTest extends BaseTestCase {

    @InjectMocks
    ProgramVerifyConfirmInfoServiceImpl service;
    @Mock
    private HrmUserCenterService hrmUserCenterService;

    @Test
    public void getInStrFromList() {
        Assert.assertNotNull(service.getInStrFromList(Lists.newArrayList("1")));
    }

    @Test
    public void getUserNew() throws Exception {
        List<String> list = new LinkedList<>();
        service.getUserNew(list);

        Map<String, HrmPersonInfoDTO> hrmPersonInfo = new HashMap<>();
        HrmPersonInfoDTO a1 = new HrmPersonInfoDTO();
        hrmPersonInfo.put("123", a1);
        hrmPersonInfo.put("234", null);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.any()))
                .thenReturn(hrmPersonInfo);
        Assert.assertNotNull(service.getUserNew(list));
    }

}