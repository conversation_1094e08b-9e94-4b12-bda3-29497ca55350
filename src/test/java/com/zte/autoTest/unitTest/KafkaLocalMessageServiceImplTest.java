package com.zte.autoTest.unitTest;

import com.zte.application.impl.kafka.KafkaLocalMessageServiceImpl;
import com.zte.application.kafka.producer.KafkaMessageProducer;
import com.zte.common.utils.Constant;
import com.zte.domain.model.KafkaLocalMessageRepository;
import com.zte.interfaces.dto.KafkaLocalMessageDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * <AUTHOR>
 * @date 2022-10-24 16:47
 */
@RunWith(PowerMockRunner.class)
public class KafkaLocalMessageServiceImplTest extends BaseTestCase {
    @InjectMocks
    private KafkaLocalMessageServiceImpl kafkaLocalMessageService;
    @Mock
    private KafkaLocalMessageRepository kafkaLocalMessageRepository;
    @Mock
    private KafkaMessageProducer kafkaMessageProducer;

    @Before
    public void init() {

    }

    @Test
    public void queryPage() {
        Page<KafkaLocalMessageDTO> page = new Page<>();
        Page<KafkaLocalMessageDTO> pageInfo =kafkaLocalMessageService.queryPage(page);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }


    @Test
    public void retrySendFailKafkaMsg() throws Exception {
        Page<KafkaLocalMessageDTO> page = new Page<>();

        List<KafkaLocalMessageDTO> list = new LinkedList<>();
        KafkaLocalMessageDTO a1 = new KafkaLocalMessageDTO();
        list.add(a1);
        PowerMockito.when(kafkaLocalMessageRepository.queryPage(Mockito.any()))
                .thenReturn(list)
        ;
        Assert.assertNotNull(kafkaLocalMessageService.retrySendFailKafkaMsg(page));
    }

    /* Started by AICoder, pid:r4295n45e54eab114c9d08de70c040287d04da8f */
    @Test
    public void testInsertMessage_NormalCase() {
        String topic = "testTopic";
        String topicKey = "testKey";
        String msg = "testMsg";
        String errorMsg = "testErrorMsg";

        when(kafkaLocalMessageRepository.batchInsertData(anyList())).thenReturn(1);

        int result = kafkaLocalMessageService.insertMessage(topic, topicKey, msg, errorMsg);

        assertEquals(1, result);
    }
    /* Ended by AICoder, pid:r4295n45e54eab114c9d08de70c040287d04da8f */
}
