package com.zte.autoTest.unitTest;

import com.zte.application.impl.BmEmailReceiverServiceImpl;
import com.zte.domain.model.BmEmailReceiver;
import com.zte.domain.model.BmEmailReceiverRepository;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: 10307315
 * @Date: 2022/3/10 上午10:06
 */
@RunWith(PowerMockRunner.class)
public class BmEmailReceiverServiceImplTest extends BaseTestCase {
    @InjectMocks
    private BmEmailReceiverServiceImpl bmEmailReceiveService;
    @Mock
    private BmEmailReceiverRepository bmEmailReceiverRepository;

    @Test
    public void selectBmEmailInfoTest() {
        String busType = "";
        String groupType = "";
        int page = 1;
        int rows = 2;
        List<BmEmailReceiver> array = new ArrayList<>();
        BmEmailReceiver dto = new BmEmailReceiver();
        array.add(dto);
        PowerMockito.when(bmEmailReceiverRepository.selectBmEmailInfo(Mockito.anyString(), Mockito.anyString(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(array);
        Assert.assertNotNull(bmEmailReceiveService.selectBmEmailInfo(busType, groupType, page, rows));

    }

    @Test
    public void getBmEmailInfoCountTest() {
        String busType = "";
        String groupType = "";
        Long total = 1L;
        PowerMockito.when(bmEmailReceiverRepository.isExists(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(total);
        Assert.assertNotNull(bmEmailReceiveService.getBmEmailInfoCount(busType, groupType));
    }
}
