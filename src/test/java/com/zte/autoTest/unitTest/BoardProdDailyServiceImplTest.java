package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.SysErrorLogService;
import com.zte.application.impl.BoardProdDailyServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.domain.model.BoardProdDaily;
import com.zte.domain.model.BoardProdDailyRepository;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class, ServiceDataBuilderUtil.class})
public class BoardProdDailyServiceImplTest extends BaseTestCase {

    @InjectMocks
    private BoardProdDailyServiceImpl service;

    @Mock
    private BoardProdDailyRepository boardProdDailyRepository;

    @Mock
    private SysErrorLogService sysErrorLogService;

    @Before
    public void init(){
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
    }
    @Test
    public void setMsg() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        try {
            PowerMockito.when(sysErrorLogService.exceptionHandler(anyString())).thenReturn("sfdsfdsf444444");
            service.setRetCode(new ServiceData(),new Exception("222"));
        }catch (Exception e){
            Assert.assertEquals(null, e.getMessage());
        }

    }

    @Test
    public void getSelected() {
        List<BoardProdDaily> boardProdDailyList = new ArrayList<>();
        boardProdDailyList.add(new BoardProdDaily(){{
            setUnitName("1");
            setOrganizationName("1");
            setCategory("1");
            setPlanOrgName("1");
            setProductClassName("1");
        }});
        boardProdDailyList.add(new BoardProdDaily(){{
            setUnitName("1");
            setOrganizationName("1");
            setCategory("1");
        }});
        PowerMockito.when(boardProdDailyRepository.getSelectOptions()).thenReturn(boardProdDailyList);
        Assert.assertNull(service.getSelected());
    }
}