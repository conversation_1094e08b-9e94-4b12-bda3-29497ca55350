package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.impl.CFFactoryServiceImpl;
import com.zte.application.impl.CfItemExceptionReceiptsServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.CFFactory;
import com.zte.domain.model.CFFactoryRepository;
import com.zte.domain.model.CfItemExceptionFiling;
import com.zte.domain.model.CfItemExceptionFilingRepository;
import com.zte.domain.model.CfItemExceptionReceipts;
import com.zte.domain.model.CfItemExceptionReceiptsRepository;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.domain.model.sncabind.PsTaskRepository;
import com.zte.interfaces.dto.CfItemExceptionFilingDTO;
import com.zte.interfaces.dto.CfItemExceptionReceiptsDTO;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Pattern;

@PrepareForTest({CfItemExceptionReceiptsServiceImpl.class, CFFactoryServiceImpl.class,
        CommonUtils.class,CfItemExceptionReceiptsRepository.class,PsTaskRepository.class,
        CFFactoryRepository.class,CfItemExceptionFilingRepository.class})
public class CfItemExceptionReceiptsServiceImplTest extends BaseTestCase {
    @InjectMocks
    private CfItemExceptionReceiptsServiceImpl cfItemExceptionReceiptsService;

    @Mock
    private CFFactoryServiceImpl cfFactoryService;

    @Mock
    private EmailUtils emailUtils;

    @Mock
    private CfItemExceptionReceiptsRepository cfItemExceptionReceiptsRepository;

    @Mock
    private PsTaskRepository psTaskRepository;

    @Mock
    private CFFactoryRepository cfFactoryRepository;

    @Mock
    private CfItemExceptionFilingRepository cfItemExceptionFilingRepository;

    private static final Pattern PATTERN = Pattern.compile(Constant.NUM_REGULAR);


    /**
     * 物料异常单据单元测试
     * @throws Exception
     */
    @Test
    public void getItemExceptionReceiptsTest() throws Exception{
        PowerMockito.mockStatic(CommonUtils.class);
        CfItemExceptionReceiptsDTO dto = new CfItemExceptionReceiptsDTO();
        Map<String, Object> map = CommonUtils.transBean2Map(dto);
        PowerMockito.when(cfItemExceptionReceiptsRepository.getExceptionReceiptsCount(map)).thenReturn(1L);
        CFFactory factory = new CFFactory();
        PowerMockito.when(cfFactoryRepository.get("")).thenReturn(factory);
        List<PsTask> psTasks = new ArrayList<>();
        PowerMockito.when(psTaskRepository.getPsTaskList(map)).thenReturn(psTasks);
        List<CfItemExceptionReceipts> pageList = new ArrayList<>();
        PowerMockito.when(cfItemExceptionReceiptsRepository.getPageList(map)).thenReturn(pageList);
        List<CFFactory> factories = new ArrayList<>();
        PowerMockito.when(cfFactoryRepository.getList(map)).thenReturn(factories);
        PowerMockito.when(cfFactoryService.getList(map)).thenReturn(factories);
        Assert.assertNotNull(cfItemExceptionReceiptsService.getExceptionReceiptsCount(dto));
        Assert.assertNotNull(cfItemExceptionReceiptsService.getReceiptsPageList(dto));
    }

    @Test
    public void insertItemExceptionReceiptsTest() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        CfItemExceptionReceiptsDTO dto = new CfItemExceptionReceiptsDTO();
        Map<String, Object> map = CommonUtils.transBean2Map(dto);
        PowerMockito.when(cfItemExceptionReceiptsRepository.insertExceptionReceipts(map)).thenReturn(1);
        List<CfItemExceptionFilingDTO> filings = new ArrayList<>();
        CfItemExceptionFilingDTO filingDTO = new CfItemExceptionFilingDTO();
        filingDTO.setId(UUID.randomUUID().toString());
        filingDTO.setReelIdNo("");
        filingDTO.setCreateDate(new Date());
        filings.add(filingDTO);
        CfItemExceptionFiling filing = new CfItemExceptionFiling();
        BeanUtils.copyProperties(filingDTO,filing);

        List<CfItemExceptionFiling> filingList = new ArrayList<>();
        filingList.add(filing);
        dto.setExceptionFilingList(filings);
        PowerMockito.when(cfItemExceptionFilingRepository.insertExceptionFilingList(filingList)).thenReturn(1);
        PowerMockito.when(cfItemExceptionReceiptsRepository.updateExceptionReceipts(map)).thenReturn(1);
        PowerMockito.when(cfItemExceptionFilingRepository.updateExceptionFilingList(filingList)).thenReturn(1);
        PowerMockito.when(cfItemExceptionReceiptsRepository.deleteExceptionReceipts("","")).thenReturn(1);
        PowerMockito.when(cfItemExceptionFilingRepository.deleteExceptionFiling("","")).thenReturn(1);

        dto.setExceptionFilingList(Lists.newArrayList(new CfItemExceptionFilingDTO()));
        dto.setId("1");
        Assert.assertNotNull(cfItemExceptionReceiptsService.insertReceipts(dto));
        cfItemExceptionReceiptsService.updateReceipts(dto);

        cfItemExceptionReceiptsService.deleteReceipts("","");
    }

    @Test
    public void getFilingList()throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        CfItemExceptionFilingDTO dto = new CfItemExceptionFilingDTO();
        List<CfItemExceptionFilingDTO> filingList = new ArrayList<>();
        filingList.add(dto);
        Assert.assertNotNull(cfItemExceptionReceiptsService.getFilingList(UUID.randomUUID().toString(),filingList));
    }

    /**
     * 校验未完工任务号
     * @throws Exception
     */
    @Test
    public void getUnfinishedTaskNo()throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(cfFactoryRepository.get("Hy2019")).thenReturn(new CFFactory());
        List<PsTask> list = new ArrayList<>();
        PowerMockito.when(psTaskRepository.getPsTaskList(new HashMap<>())).thenReturn(list);
        Assert.assertNotNull(cfItemExceptionReceiptsService.getUnfinishedTaskNo("Hy2019","ZZ河源A1020200731002"));
    }

    /**
     * 校验工厂
     * @throws Exception
     */
    @Test
    @PrepareForTest({CfItemExceptionReceiptsServiceImpl.class, CFFactoryServiceImpl.class,
            CommonUtils.class,CfItemExceptionReceiptsRepository.class,PsTaskRepository.class,
            CFFactoryRepository.class,CfItemExceptionFilingRepository.class})
    public void verifyFactory()throws Exception {
        PowerMockito.mock(CFFactoryRepository.class);
        List<CFFactory> cfFactories = new ArrayList<>();
        PowerMockito.when(cfFactoryService.getList(new HashMap<>())).thenReturn(cfFactories);
        Assert.assertNotNull(cfItemExceptionReceiptsService.verifyFactory(""));
    }

    @Test
    public void sendEmail()throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        CfItemExceptionReceiptsDTO dto = new CfItemExceptionReceiptsDTO();
        dto.setDocumentNo("6606000001");
        dto.setStatus("1");
        dto.setExaminePerson("6606000001");
        String[] params = { dto.getDocumentNo() };
        Map<String, String> map = new HashMap<>();
        PowerMockito.when(emailUtils.sendMail(PATTERN.matcher(dto.getExaminePerson()).replaceAll(""),
                CommonUtils.getLmbMessage(MessageId.YOU_HAVE_BILL_TO_APPROVE,params), "",
                CommonUtils.getLmbMessage(MessageId.YOU_HAVE_BILL_TO_APPROVE,params), "")).thenReturn(false);
        cfItemExceptionReceiptsService.sendEmail(dto);
        Assert.assertNotNull(dto);
    }


}
