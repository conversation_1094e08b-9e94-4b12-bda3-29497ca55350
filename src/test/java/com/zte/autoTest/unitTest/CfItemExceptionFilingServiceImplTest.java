package com.zte.autoTest.unitTest;

import com.zte.application.impl.CfItemExceptionFilingServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.domain.model.CfItemExceptionFiling;
import com.zte.domain.model.CfItemExceptionFilingRepository;
import com.zte.interfaces.dto.CfItemExceptionFilingDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CfItemExceptionFilingServiceImpl.class, CfItemExceptionFilingRepository.class, CommonUtils.class})
public class CfItemExceptionFilingServiceImplTest extends BaseTestCase {

    @InjectMocks
    private CfItemExceptionFilingServiceImpl cfItemExceptionFilingServiceImpl;

    @Mock
    private CfItemExceptionFilingRepository cfItemExceptionFilingRepository;
    @Before
    public  void  init(){

    }

    @Test
    public void itemExceptionFilingTest() throws Exception{
        PowerMockito.mockStatic(CommonUtils.class);
        CfItemExceptionFilingDTO dto = new CfItemExceptionFilingDTO();
        Map<String, Object> map = CommonUtils.transBean2Map(dto);
        List<CfItemExceptionFiling> filings = new ArrayList<>();
        List<String> reelIdNo = new ArrayList<>();
        PowerMockito.when(cfItemExceptionFilingRepository.getPageList(map)).thenReturn(filings);
        PowerMockito.when(cfItemExceptionFilingRepository.getExceptionFilingCount(map)).thenReturn(1L);
        PowerMockito.when(cfItemExceptionFilingRepository.getReelIdNoAndFromId(map)).thenReturn(reelIdNo);
        cfItemExceptionFilingServiceImpl.getCount(dto);
        Assert.assertNotNull(cfItemExceptionFilingServiceImpl.getPageList(dto));
        cfItemExceptionFilingServiceImpl.getReelIdNo(dto);
    }

    @Test
    public void getReelIdNo() throws Exception{
        PowerMockito.mockStatic(CommonUtils.class);
        CfItemExceptionFilingDTO dto = new CfItemExceptionFilingDTO();
        List<String> list = new ArrayList<>();
        PowerMockito.when(cfItemExceptionFilingRepository.getReelIdNoAndFromId(CommonUtils.transBean2Map(dto))).thenReturn(list);
        Assert.assertNotNull(cfItemExceptionFilingServiceImpl.getReelIdNo(dto));
    }
}
