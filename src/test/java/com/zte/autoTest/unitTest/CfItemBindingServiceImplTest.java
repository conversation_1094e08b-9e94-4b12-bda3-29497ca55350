package com.zte.autoTest.unitTest;

import com.zte.application.CommonService;
import com.zte.application.HrmUserCenterService;
import com.zte.application.IMESLogService;
import com.zte.application.impl.ProdBindingSettingServiceImpl;
import com.zte.application.sncabind.impl.CfItemBindingServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BBomDetail;
import com.zte.domain.model.BBomDetailRepository;
import com.zte.domain.model.BBomHeaderRepository;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.BsItemInfoRepository;
import com.zte.domain.model.ProdBindingSetting;
import com.zte.domain.model.ProdBindingSettingRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.domain.model.SysUser;
import com.zte.domain.model.SysUserRepository;
import com.zte.domain.model.craftTech.CtRouteDetailRepository;
import com.zte.domain.model.sncabind.CfItemBindingRepository;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.CfItemBindingDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.ProdBindingSettingDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-04-21 15:44
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CrafttechRemoteService.class, SpringContextUtil.class})
public class CfItemBindingServiceImplTest extends BaseTestCase {
    @InjectMocks
    private CfItemBindingServiceImpl cfItemBindingServiceImpl;
    @Mock
    private CfItemBindingRepository cfItemBindingRepository;
    @Mock
    private BsItemInfoRepository bsItemInfoRepository;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private BBomDetailRepository bomDetailRepository;
    @Mock
    private ProdBindingSettingRepository prodBindingSettingRepository;
    @Mock
    private CommonService commonService;
    @Mock
    private SysUserRepository sysUserRepository;
    @Mock
    private HrmUserCenterService hrmUserCenterService;
    @Mock
    private LocaleMessageSourceBean LocaleMessageSourceBean;
    @Mock
    private BBomHeaderRepository bBomHeaderRepository;
    @Mock
    private IMESLogService imesLogService;
    @Mock
    private CtRouteDetailRepository ctRouteDetailRepository;
    @Mock
    private ProdBindingSettingServiceImpl prodBindingSettingServiceImpl;

    @Test
    public void addBindingData() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        CfItemBindingDTO cfItemBindingDTO = new CfItemBindingDTO();
        cfItemBindingDTO.setItemNo("123");
        cfItemBindingDTO.setItemType("1");
        cfItemBindingDTO.setMainCraftSection("SMT-A");
        cfItemBindingDTO.setMaintenanceStatus("1");

        List<BsItemInfo> infoList = new LinkedList<>();
        BsItemInfo bsItemInfo = new BsItemInfo();
        bsItemInfo.setItemType("1");
        infoList.add(bsItemInfo);
        PowerMockito.when(bsItemInfoRepository.getInfoList(Mockito.anyObject()))
                .thenReturn(infoList);
        try {
            cfItemBindingServiceImpl.addBindingData(cfItemBindingDTO);
        } catch (Exception e) {
            Assert.assertTrue(StringUtils.isBlank(e.getMessage()));
        }

        cfItemBindingDTO.setItemType("0");
        cfItemBindingDTO.setOverstepMaxSize(false);
        cfItemBindingDTO.setConfirmOperation(Constant.FLAG_Y);
        cfItemBindingDTO.setMaintenanceStatus("0");
        bsItemInfo.setItemType("0");

        List<BBomDetailDTO> bomDetailDTOList = new LinkedList<>();
        BBomDetailDTO c1 = new BBomDetailDTO();
        c1.setItemCode("123");
        c1.setUsageCount(new BigDecimal(2));
        c1.setProductCode("1234");
        bomDetailDTOList.add(c1);
        PowerMockito.when(bomDetailRepository.selectBomByItemCode(Mockito.any()))
                .thenReturn(bomDetailDTOList);

        List<ProdBindingSettingDTO> prodBindingSettingList = new LinkedList<>();
        ProdBindingSettingDTO d1 = new ProdBindingSettingDTO();
        d1.setItemCode("1235");
        d1.setProductCode("1234");
        prodBindingSettingList.add(d1);
        PowerMockito.when(prodBindingSettingRepository.queryBindingProductCode(Mockito.anyList()))
                .thenReturn(prodBindingSettingList);

        List<SysLookupValues> list = new LinkedList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setAttribute1("1");
        list.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.any()))
                .thenReturn(list);

        List<CtRouteDetailDTO> detailDTOS = new LinkedList<>();
        CtRouteDetailDTO f1 = new CtRouteDetailDTO();
        f1.setNextProcess("6");
        f1.setCraftSection("smta");
        f1.setProcessSeq(new BigDecimal(2));
        f1.setItemNo("1234");
        detailDTOS.add(f1);
        CtRouteDetailDTO f2 = new CtRouteDetailDTO();
        f2.setNextProcess("8");
        f2.setCraftSection("smta");
        f2.setProcessSeq(new BigDecimal(1));
        f2.setItemNo("1234");
        detailDTOS.add(f2);
        CtRouteDetailDTO f3 = new CtRouteDetailDTO();
        f3.setNextProcess("N");
        f3.setCraftSection("smta");
        f3.setProcessSeq(new BigDecimal(3));
        f3.setItemNo("1234");
        detailDTOS.add(f3);
        PowerMockito.when(ctRouteDetailRepository.queryRouteDetailBatch(Mockito.anyList(),
                Mockito.anyList(), Mockito.anyString())).thenReturn(detailDTOS);
        try {
            cfItemBindingServiceImpl.analyzeBindingData(cfItemBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PLS_SELECT_PROCESS_CODE, e.getMessage());
        }
        cfItemBindingDTO.setProcessCode("N");
        try {
            cfItemBindingServiceImpl.analyzeBindingData(cfItemBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PROCESS_CODE_CANNOT_BE_N, e.getMessage());
        }
        cfItemBindingDTO.setProcessCode("1");
        cfItemBindingServiceImpl.addBindingData(cfItemBindingDTO);

        d1.setItemCode("123");
        cfItemBindingDTO.setMaintenanceStatus("2");
        cfItemBindingServiceImpl.addBindingData(cfItemBindingDTO);
    }

    @Test
    public void analyzeBindingData() throws Exception {
        CfItemBindingDTO cfItemBindingDTO = new CfItemBindingDTO();
        cfItemBindingDTO.setItemNo("123");
        cfItemBindingDTO.setItemType("1");
        cfItemBindingDTO.setMaintenanceStatus("1");

        List<BsItemInfo> infoList = new LinkedList<>();
        BsItemInfo a1 = new BsItemInfo();
        a1.setItemType("1");
        infoList.add(a1);
        PowerMockito.when(bsItemInfoRepository.getInfoList(Mockito.anyObject()))
                .thenReturn(infoList);

        List<BBomDetailDTO> bomDetailDTOList = new LinkedList<>();
        BBomDetailDTO b1 = new BBomDetailDTO();
        b1.setItemCode("123");
        b1.setUsageCount(new BigDecimal(2));
        b1.setProductCode("1234");
        bomDetailDTOList.add(b1);
        BBomDetailDTO b2 = new BBomDetailDTO();
        b2.setItemCode("123");
        b2.setUsageCount(new BigDecimal(2));
        b2.setProductCode("32131");
        bomDetailDTOList.add(b2);
        PowerMockito.when(bomDetailRepository.selectBomByItemCode(Mockito.any()))
                .thenReturn(bomDetailDTOList);

        List<ProdBindingSettingDTO> prodBindingSettingList = new LinkedList<>();
        ProdBindingSettingDTO d1 = new ProdBindingSettingDTO();
        d1.setItemCode("1235");
        d1.setProductCode("1234");
        prodBindingSettingList.add(d1);
        ProdBindingSettingDTO d2 = new ProdBindingSettingDTO();
        d2.setItemCode("12322");
        d2.setProductCode("32131");
        prodBindingSettingList.add(d2);
        PowerMockito.when(prodBindingSettingRepository.queryBindingProductCode(Mockito.anyList()))
                .thenReturn(prodBindingSettingList);
        List<SysLookupValues> list = new LinkedList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setAttribute1("1");
        list.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.anyObject()))
                .thenReturn(list);

        cfItemBindingServiceImpl.analyzeBindingData(cfItemBindingDTO);

        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(BusinessConstant.RESOURCE_SERVICE_NAME))
                .thenReturn(LocaleMessageSourceBean)
        ;


        cfItemBindingDTO.setMaintenanceStatus("0");
        try {
            cfItemBindingServiceImpl.analyzeBindingData(cfItemBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PLS_SELECT_PROCESS_CODE, e.getMessage());
        }
        cfItemBindingDTO.setProcessCode("1");

        try {
            cfItemBindingServiceImpl.analyzeBindingData(cfItemBindingDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_PRODUCT_HAS_PROCESS_CODE, e.getMessage());
        }

        List<CtRouteDetailDTO> detailDTOS = new LinkedList<>();
        CtRouteDetailDTO f1 = new CtRouteDetailDTO();
        f1.setNextProcess("6");
        f1.setCraftSection("smta");
        f1.setProcessSeq(new BigDecimal(2));
        f1.setItemNo("1234");
        detailDTOS.add(f1);
        CtRouteDetailDTO f2 = new CtRouteDetailDTO();
        f2.setNextProcess("8");
        f2.setCraftSection("smta");
        f2.setProcessSeq(new BigDecimal(1));
        f2.setItemNo("1234");
        detailDTOS.add(f2);
        CtRouteDetailDTO f3 = new CtRouteDetailDTO();
        f3.setNextProcess("N");
        f3.setCraftSection("smta");
        f3.setProcessSeq(new BigDecimal(3));
        f3.setItemNo("123");
        detailDTOS.add(f3);
        PowerMockito.when(ctRouteDetailRepository.queryRouteDetailBatch(Mockito.anyList(),
                Mockito.anyList(), Mockito.anyString())).thenReturn(detailDTOS);
        cfItemBindingServiceImpl.analyzeBindingData(cfItemBindingDTO);
    }

    @Test
    public void queryCfBindingPage() throws Exception {
        Page<CfItemBindingDTO> page = new Page<>();
        List<CfItemBindingDTO> cfItemBindingDTOS = new LinkedList<>();
        CfItemBindingDTO a1 = new CfItemBindingDTO();
        a1.setLastUpdatedBy("123");
        a1.setCreatedBy("123");
        cfItemBindingDTOS.add(a1);
        PowerMockito.when(cfItemBindingRepository.queryCfBindingPage(Mockito.any()))
                .thenReturn(cfItemBindingDTOS)
        ;

        List<SysUser> sysUsersTemp = new LinkedList<>();
        SysUser b1 = new SysUser();
        b1.setAccount("123");
        b1.setUserName("3");
        sysUsersTemp.add(b1);
        PowerMockito.when(sysUserRepository.selectList(Mockito.anyList()))
                .thenReturn(sysUsersTemp);
        Map<String, HrmPersonInfoDTO> hrmPersonInfoTemp = new HashMap<>();
        hrmPersonInfoTemp.put("123", new HrmPersonInfoDTO());
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.anyList()))
                .thenReturn(hrmPersonInfoTemp);

        Page<CfItemBindingDTO> pageInfo = cfItemBindingServiceImpl.queryCfBindingPage(page);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }

    @Test
    public void deleteObjectByCondition() {
        CfItemBindingDTO cfItemBindingDTO = new CfItemBindingDTO();
        cfItemBindingServiceImpl.deleteObjectByCondition(cfItemBindingDTO);
        Assert.assertNotNull(cfItemBindingDTO);
    }

    @Test
    public void queryUnBingForItemNo() {

        List<BBomDetail> allChildByProductCode = new LinkedList<>();
        BBomDetail a1 = new BBomDetail();
        a1.setProductCode("129206751425AKB");
        a1.setItemCode("129206751425AKB1");
        allChildByProductCode.add(a1);

        BBomDetail a2 = new BBomDetail();
        a2.setProductCode("129206751425AKB1");
        a2.setItemCode("129206751425AKB2");
        allChildByProductCode.add(a2);

        BBomDetail a3 = new BBomDetail();
        a3.setProductCode("129206751425AKB1");
        a3.setItemCode("129206751425AKB3");
        allChildByProductCode.add(a3);

        PowerMockito.when(bBomHeaderRepository.getAllChildByProductCode(Mockito.anyString()))
                .thenReturn(allChildByProductCode)
        ;

        List<ProdBindingSetting> settings = new LinkedList<>();
        ProdBindingSetting b1 = new ProdBindingSetting();
        b1.setProductCode("129206751425AKB");
        b1.setCraftSection("SMT-B");
        b1.setItemCode("129206751425AKB1");
        b1.setLastUpdatedDate(new Date());
        b1.setEnabledFlag(Constant.FLAG_Y);
        settings.add(b1);
        ProdBindingSetting b2 = new ProdBindingSetting();
        b2.setProductCode("129206751425AKB1");
        b2.setCraftSection("SMT-B");
        b2.setItemCode("129206751425AKB2");
        b2.setLastUpdatedDate(new Date());
        b2.setEnabledFlag(Constant.FLAG_N);
        settings.add(b2);
        ProdBindingSetting b3 = new ProdBindingSetting();
        b3.setProductCode("129206751425AKB1");
        b3.setCraftSection("SMT-B");
        b3.setItemCode("129206751425AKB3");
        b3.setLastUpdatedDate(new Date());
        b3.setEnabledFlag(Constant.FLAG_N);
        settings.add(b3);
        PowerMockito.when(prodBindingSettingRepository.queryChildBindingBatch(Mockito.anyList()))
                .thenReturn(settings);

        List<CfItemBindingDTO> list = new LinkedList<>();
        CfItemBindingDTO c1 = new CfItemBindingDTO();
        c1.setItemNo("129206751425AKB2");
        c1.setMainCraftSection("SMT-A");
        list.add(c1);
        PowerMockito.when(cfItemBindingRepository.queryCfBindingBatch(Mockito.any()))
                .thenReturn(list)
        ;


        Assert.assertNotNull(cfItemBindingServiceImpl.queryUnBingForItemNo("129206751425AKB"));
    }

    @Test
    public void saveBindingData() {
        CfItemBindingDTO cfItemBindingDTO = new CfItemBindingDTO();
        cfItemBindingServiceImpl.saveBindingData(cfItemBindingDTO);
        Assert.assertNotNull(cfItemBindingDTO);
    }
}
