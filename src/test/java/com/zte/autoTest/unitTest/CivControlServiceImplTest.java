package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.impl.CivControlServiceImpl;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.BBomDetailRepository;
import com.zte.domain.model.BProdBomDetailRepository;
import com.zte.infrastructure.remote.EccnRemoteService;
import com.zte.infrastructure.remote.IscpRemoteService;
import com.zte.interfaces.dto.BProdBomDetailDTO;
import com.zte.interfaces.dto.CivControlInfoDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;

/**
 * 功能描述：
 *
 * @Author:
 * @Date: 2020/12/23 17:25
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpClientUtil.class})
public class CivControlServiceImplTest extends BaseTestCase {

	@InjectMocks
	private CivControlServiceImpl service;

	@Mock
	private BBomDetailRepository bBomDetailRepository;

	@Mock
	private BProdBomDetailRepository bProdBomDetailRepository;

	@Mock
	private IscpRemoteService iscpRemoteService;
	@Mock
	private EccnRemoteService eccnRemoteService;


	@Test
	public void checkCivItem() throws Exception {
		CivControlInfoDTO civControlInfoDTO = new CivControlInfoDTO();

		// 测试默认情况
		Whitebox.invokeMethod(service, "checkCivItem", civControlInfoDTO);
		Assert.assertNotNull(civControlInfoDTO);

		// 测试设置导出控制方法
		civControlInfoDTO.setExportcontrolmethod(MpConstant.DEFAULT_EXPORT_CONTROL_METHOD);
		Whitebox.invokeMethod(service, "checkCivItem", civControlInfoDTO);
		Assert.assertNotNull(civControlInfoDTO);

		// 测试设置导出控制方法名称
		civControlInfoDTO.setExportcontrolmethodName(MpConstant.DEFAULT_EXPORT_CONTROL_METHOD);
		Whitebox.invokeMethod(service, "checkCivItem", civControlInfoDTO);
		Assert.assertNotNull(civControlInfoDTO);

		CivControlInfoDTO civControlInfoDTO1 = new CivControlInfoDTO();
		civControlInfoDTO1.setExportcontrolmethod(null);
		civControlInfoDTO1.setExportcontrolmethodName(MpConstant.DEFAULT_EXPORT_CONTROL_METHOD);
		Whitebox.invokeMethod(service, "checkCivItem", civControlInfoDTO1);
		Assert.assertNotNull(civControlInfoDTO1);
	}


	@Test
	public void itemNoCiv() throws Exception {
		List<BProdBomDetailDTO> mBomDetailList = new ArrayList<>();
		BProdBomDetailDTO bProdBomDetailDTO = new BProdBomDetailDTO();
		bProdBomDetailDTO.setItemCode("046050200109");
		mBomDetailList.add(bProdBomDetailDTO);
		PowerMockito.when(bProdBomDetailRepository.getMBomDetailList("8899787", null, null)).thenReturn(new ArrayList<>());
		List<CivControlInfoDTO> infoDTOList = new ArrayList<>();
		CivControlInfoDTO civControlInfoDTO = new CivControlInfoDTO();
		civControlInfoDTO.setItemNo("046050200109");
		infoDTOList.add(civControlInfoDTO);
		PowerMockito.when(iscpRemoteService.getCivControlInfo(any())).thenReturn(new ArrayList<>());
		PowerMockito.when(bBomDetailRepository.getBomItemCodeByProductCode(any())).thenReturn(Lists.newArrayList("046050200109"));
		Assert.assertNotNull(service.itemNoCiv(null, "130000220554ABB", "8899787"));
		PowerMockito.when(bProdBomDetailRepository.getMBomDetailList("8899787", null, null)).thenReturn(mBomDetailList);
		Assert.assertNotNull(service.itemNoCiv(null, "130000220554ABB", "8899787"));
		Assert.assertNotNull(service.itemNoCiv(null, "130000220554ABB", null));
		Assert.assertNotNull(service.itemNoCiv(null, null, null));
		Assert.assertNotNull(service.itemNoCiv(Lists.newArrayList("046050200109"), null, null));
		PowerMockito.when(iscpRemoteService.getCivControlInfo(any())).thenReturn(infoDTOList);
		Assert.assertNotNull(service.itemNoCiv(Lists.newArrayList("046050200109"), null, null));
	}

	@Test
	public void itemNoEccn() throws Exception {
		PowerMockito.when(bProdBomDetailRepository.getProdRawItemByProdplanId(Mockito.anyString())).thenReturn(new ArrayList<>());
		PowerMockito.when(bBomDetailRepository.getRawItemByProductCode(any())).thenReturn(Lists.newArrayList("1"));
		PowerMockito.when(eccnRemoteService.getEccnItemNo(any())).thenReturn(new ArrayList<>());
		Assert.assertNotNull(service.itemNoECCN(null, "130000220554ABB", "8899787"));
		PowerMockito.when(bProdBomDetailRepository.getProdRawItemByProdplanId(Mockito.anyString())).thenReturn(Lists.newArrayList("046050200109"));
		Assert.assertNotNull(service.itemNoECCN(null, "130000220554ABB", "8899787"));
		Assert.assertNotNull(service.itemNoECCN(null, "130000220554ABB", null));
		Assert.assertNotNull(service.itemNoECCN(null, null, null));
		Assert.assertNotNull(service.itemNoECCN(Lists.newArrayList("046050200109"), null, null));
	}


}
