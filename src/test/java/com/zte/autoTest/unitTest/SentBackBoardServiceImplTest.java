package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.bytedance.impl.SentBackBoardServiceImpl;
import com.zte.application.kafka.producer.KafkaMessageProducer;
import com.zte.interfaces.dto.bytedance.SentBackBoardDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-05-10 15:47
 */
@PrepareForTest({JSON.class})
public class SentBackBoardServiceImplTest extends BaseTestCase {
    @InjectMocks
    private SentBackBoardServiceImpl sentBackBoardServiceImpl;
    @Mock
    private KafkaMessageProducer kafkaMessageProducer;

    @Before
    public void init(){
        PowerMockito.mockStatic(JSON.class);
    }

    @Test
    public void sentBackBoardToByteDance( )  throws Exception{
        List<SentBackBoardDTO> sentBackBoardDTOList = new LinkedList<>();
        Assert.assertFalse(sentBackBoardServiceImpl.sentBackBoardToByteDance(sentBackBoardDTOList));
    }
}
