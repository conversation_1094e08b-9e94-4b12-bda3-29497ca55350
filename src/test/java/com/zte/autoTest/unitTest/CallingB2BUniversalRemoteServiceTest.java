package com.zte.autoTest.unitTest;

import com.zte.application.impl.ResourceInfoServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.model.NumConstant;
import com.zte.common.utils.CallingB2BUniversalService;
import com.zte.domain.model.ResourceWarningRecordRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.CallingB2BUniversalRemoteService;
import com.zte.interfaces.dto.NetworkAccessIdentificationNumberApplicationDTO;
import com.zte.interfaces.dto.ResourceInfoEntityDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

/**
 * <AUTHOR>
 * @date 2023/12/1
 */
@PrepareForTest({HttpClientUtil.class})
public class CallingB2BUniversalRemoteServiceTest  extends BaseTestCase {

    @InjectMocks
    private CallingB2BUniversalRemoteService service;

    @Mock
    private CallingB2BUniversalService callingB2BUniversalService;

    @Test
    public void callB2B()throws Exception{
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(anyString(),anyString(),any())).thenReturn("2");
        String result = service.callB2B("","");
        Assert.assertNull(result);


    }
}
