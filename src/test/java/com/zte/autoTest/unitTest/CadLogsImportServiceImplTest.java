package com.zte.autoTest.unitTest;

import com.zte.application.sncabind.impl.CadLogsImportServiceImpl;
import com.zte.domain.model.CadUploadRecord;
import com.zte.domain.model.CadUploadRecordRepository;
import com.zte.interfaces.sncabind.dto.CadLogsDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @Title: CAD日志记录
 * @Description:
 * @date 2020/11/9 15:17
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CadUploadRecordRepository.class})
public class CadLogsImportServiceImplTest extends BaseTestCase {

    @InjectMocks
    private CadLogsImportServiceImpl service;

    @Mock
    private CadUploadRecordRepository repository;

    @Test
    public void barodeUpdate() {
        CadLogsDTO dto = new CadLogsDTO();
        dto.setId("1212444");
        dto.setMaterialName("diiiske-jje-2011");
        List<CadUploadRecord> bomList = new LinkedList<>();
        CadUploadRecord b1 = new CadUploadRecord();
        CadUploadRecord b2 = new CadUploadRecord();
        b1.setProductCode("123");
        b2.setProductCode("234");
        bomList.add(b1);
        bomList.add(b2);
        PowerMockito.when(repository.queryAllByLimit(dto)).thenReturn(bomList);
        PowerMockito.when(repository.getAmount(dto)).thenReturn(1L);
        PageRows<CadUploadRecord> pageRows = service.getLogsRecord(dto);
        Assert.assertTrue(pageRows.getRows().size() >= 0);
    }
}
