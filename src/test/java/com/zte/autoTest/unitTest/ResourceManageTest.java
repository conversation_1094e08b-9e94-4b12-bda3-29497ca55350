package com.zte.autoTest.unitTest;

import com.zte.common.authority.ResourceManage;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: Russell.pan
 * @Date: 2021/6/1 10:40
 * @Description:
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({RequestContextHolder.class})
public class ResourceManageTest extends BaseTestCase {

    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;

    @InjectMocks
    private ResourceManage resourceManage;
    @Mock
    private ServletRequestAttributes servletReqAttr;
    @Mock
    private HttpServletRequest request;


    @Test
    public void convertFactoryDomain() throws Exception {
        final List<SysLookupValues> sysLookupValues = new ArrayList<>();
        SysLookupValues sysLookupValue = new SysLookupValues();
        sysLookupValue.setLookupMeaning("1");
        sysLookupValue.setRemark("1");
        sysLookupValue.setAttribute2("22");
        sysLookupValues.add(sysLookupValue);
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.any())).thenReturn(sysLookupValues);
        PowerMockito.mockStatic(RequestContextHolder.class);
        PowerMockito.when(RequestContextHolder.getRequestAttributes()).thenReturn(new ServletRequestAttributes(request));
        // ServletRequestAttributes(request);
        PowerMockito.when(request.getHeader(Constant.HOST)).thenReturn("uat55");

        Assert.assertNotNull(resourceManage.convertFactoryDomain());
        PowerMockito.when(request.getHeader(Constant.ORIGIN)).thenReturn("https://imes.zte.com.cn");

        Assert.assertNotNull(resourceManage.convertFactoryDomain());
    }
}
