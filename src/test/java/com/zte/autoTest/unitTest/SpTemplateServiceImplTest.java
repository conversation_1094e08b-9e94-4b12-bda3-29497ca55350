package com.zte.autoTest.unitTest;

import com.zte.application.SpTemplateItemService;
import com.zte.application.impl.SpTemplateServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SpTemplate;
import com.zte.domain.model.SpTemplateRepository;
import com.zte.interfaces.dto.SpTemplateDTO;
import com.zte.interfaces.dto.SpTemplateItemDTO;
import com.zte.interfaces.dto.SpTemplateQueryDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Matchers;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/7/14 16:38
 * @Description
 */
@RunWith(PowerMockRunner.class)
public class SpTemplateServiceImplTest extends BaseTestCase {

    @InjectMocks
    private SpTemplateServiceImpl spTemplateService;

    @Mock
    private SpTemplateRepository spTemplateRepository;
    @Mock
    private SpTemplateItemService spTemplateItemService;

    @Test
    public void queryPage() {
        List<SpTemplate> list = new ArrayList<>();
        SpTemplate spTemplate = new SpTemplate();
        spTemplate.setTemplateId("PG-TEST");
        list.add(spTemplate);
        PowerMockito.doReturn(1L).when(spTemplateRepository).countPage(Matchers.any());
        PowerMockito.doReturn(list).when(spTemplateRepository).queryPage(Matchers.any());
        PageRows<SpTemplate> pageRows = spTemplateService.queryPage(new SpTemplateQueryDTO());
        Assert.assertTrue(pageRows.getRows().size() >= 0);
    }

    @Test
    public void getDetail() {
        List<SpTemplateDTO> list = new ArrayList<>();
        SpTemplateDTO spTemplate = new SpTemplateDTO();
        spTemplate.setTemplateId("PG-TEST");
        list.add(spTemplate);
        PowerMockito.doReturn(list).when(spTemplateRepository).getDetail(Matchers.any());
        Assert.assertNotNull(spTemplateService.getDetail(new SpTemplateDTO()));
    }

    @Test
    public void save() {
        List<SpTemplateDTO> list = new ArrayList<>();
        SpTemplateDTO spTemplate = new SpTemplateDTO();
        spTemplate.setTemplateId("PG-TEST");
        list.add(spTemplate);
        PowerMockito.doReturn(0L).when(spTemplateRepository).countByTemplateName(Matchers.any(), Matchers.any());
        List<SpTemplateItemDTO> itemList = new ArrayList<>();
        SpTemplateItemDTO spTemplateItemDTO = new SpTemplateItemDTO();
        spTemplateItemDTO.setTemplateId("PG-TEST");
        spTemplateItemDTO.setParamName("PG-TEST");
        spTemplateItemDTO.setParamRule("'PG-TEST'");
        itemList.add(spTemplateItemDTO);
        PowerMockito.doReturn(1L).when(spTemplateRepository).insert(Matchers.any());
        PowerMockito.doReturn(1L).when(spTemplateRepository).insert(Matchers.any());
        PowerMockito.doReturn(1L).when(spTemplateRepository).update(Matchers.any());
        PowerMockito.doNothing().when(spTemplateItemService).deleteByTemplateId(Matchers.any(), Matchers.any());
        PowerMockito.doNothing().when(spTemplateItemService).updateById(Matchers.any());
        PowerMockito.doNothing().when(spTemplateItemService).addBatch(Matchers.any());
        PowerMockito.doNothing().when(spTemplateItemService).deleteNotItemIdByTemplateIdAnd(Matchers.any(), Matchers.any(), Matchers.any());
        PowerMockito.doReturn(list).when(spTemplateRepository).getDetail(Matchers.any());
        Assert.assertNotNull(spTemplateService.save(spTemplate));
    }

    @Test
    public void delete() {
        long count = 1;
        PowerMockito.doReturn(count).when(spTemplateRepository).delete(Matchers.any());
        PowerMockito.doNothing().when(spTemplateItemService).deleteByTemplateId(Matchers.any(), Matchers.any());
        spTemplateService.delete("PG-TEST");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test(expected = MesBusinessException.class)
    public void checkTemplate() throws Exception {
        PowerMockito.doReturn(1L).when(spTemplateRepository).countByTemplateName(Matchers.any(), Matchers.any());
        Whitebox.invokeMethod(spTemplateService, "checkTemplate", new SpTemplateDTO() {{
            setTemplateId("test");
            setTemplateName("test");
        }});
    }
}
