/* Started by AICoder, pid:rf6f1g0701n98cd147750b38507c0f8de3454fcd */
package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSONObject;
import com.zte.common.authority.SysAuthorityConfig;
import com.zte.common.model.MessageId;
import com.zte.infrastructure.remote.UcsRemoteService;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.util.BaseTestCase;
import org.apache.commons.math3.analysis.function.Pow;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;

@PrepareForTest({HttpClientUtil.class})
public class UcsRemoteServiceTest extends BaseTestCase {

    @Mock
    private RedisTemplate<String, Object> redisTemplate;

    @Mock
    SysAuthorityConfig sysAuthorityConfig;

    @Mock
    private ValueOperations<String, Object> redisOpsValue;

    @InjectMocks
    UcsRemoteService ucsRemoteService;
    @Test
    public void encryptTemporaryKey() {
        try {
            ucsRemoteService.encryptTemporaryKey("","");
        } catch (Exception e) {
            assertNotNull(e.getMessage());
        }
    }
    @Test
    public void encryptParams() {
        try {
            ucsRemoteService.encryptParams("","");
        } catch (Exception e) {
            assertNotNull(e.getMessage());
        }
    }
    @Test
    public void testGetPublicKey() {
        PowerMockito.mockStatic(HttpClientUtil.class);

        // Mocking Redis operations
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(redisOpsValue);

        // Test when publicKey is found in Redis
        String publicKey = "publicKey";
        PowerMockito.when(redisOpsValue.get(Mockito.any())).thenReturn(publicKey);
        Assert.assertNotNull(ucsRemoteService.getPublicKey());

        // Test when publicKey is not found in Redis and API call fails
        PowerMockito.when(redisOpsValue.get(Mockito.any())).thenReturn(null);
        PowerMockito.when(sysAuthorityConfig.getApiUrl()).thenReturn("2");
        PowerMockito.when(sysAuthorityConfig.getPublicKeyPath()).thenReturn("2");
        PowerMockito.when(HttpClientUtil.httpGet(Mockito.anyString(), Mockito.anyMap(), Mockito.any())).thenReturn("");
        try {
            ucsRemoteService.getPublicKey();
            Assert.fail("Expected exception was not thrown");
        } catch (Exception e) {
            assertEquals(MessageId.FAILED_TO_OBTAIN_UCS_PUBLIC_KEY, e.getMessage());
        }

        // Test when API returns an empty ServiceData object
        ServiceData serviceData = new ServiceData();
        PowerMockito.when(HttpClientUtil.httpGet(Mockito.anyString(), Mockito.anyMap(), Mockito.any()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        try {
            ucsRemoteService.getPublicKey();
            Assert.fail("Expected exception was not thrown");
        } catch (Exception e) {
            assertEquals(MessageId.FAILED_TO_OBTAIN_UCS_PUBLIC_KEY, e.getMessage());
        }

        // Test when API returns a ServiceData object with 'bo' field set
        serviceData.setBo("kke");
        PowerMockito.when(HttpClientUtil.httpGet(Mockito.anyString(), Mockito.anyMap(), Mockito.any()))
                .thenReturn(JSONObject.toJSONString(serviceData));
        try {
            ucsRemoteService.getPublicKey();
        } catch (Exception e) {
            assertEquals(MessageId.FAILED_TO_OBTAIN_UCS_PUBLIC_KEY, e.getMessage());
        }
    }
}

/* Ended by AICoder, pid:rf6f1g0701n98cd147750b38507c0f8de3454fcd */