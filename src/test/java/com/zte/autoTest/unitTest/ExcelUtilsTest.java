package com.zte.autoTest.unitTest;

import com.alibaba.excel.ExcelWriter;
import com.zte.common.CommonUtils;
import com.zte.common.ExcelUtil;
import com.zte.common.ExcelUtils;
import com.zte.interfaces.dto.BsItemFileInputVO;
import com.zte.util.BaseTestCase;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;

@RunWith(PowerMockRunner.class)
@PrepareForTest({WorkbookFactory.class, CommonUtils.class})
public class ExcelUtilsTest extends BaseTestCase {
    @Mock
    private HttpServletResponse httpServletResponse;
    @Mock
    private Workbook workbook;
    @Mock
    InputStream inputStream;
    @Mock
    Workbook wb;
    @Mock
    Sheet sheet;
    @Mock
    Row rowHeader;
    @Mock
    ExcelWriter excelWriter;

    @Before
    public void init() {

    }
    @Test
    public void closeExcelWriter()throws Exception {
        CommonUtils.closeExcelWriter(excelWriter);
        Assert.assertEquals("","");
        CommonUtils.closeExcelWriter(null);
        Assert.assertEquals("","");
    }
    @Test
    public void resolveExcel()throws Exception {
        PowerMockito.mockStatic(WorkbookFactory.class);
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(WorkbookFactory.create(inputStream))
                .thenReturn(wb);
        PowerMockito.when(wb.getSheetAt(Mockito.anyInt()))
                .thenReturn(sheet);
        PowerMockito.when(sheet.getLastRowNum())
                .thenReturn(0);
        PowerMockito.when(sheet.getRow(Mockito.anyInt()))
                .thenReturn(rowHeader);

        String[] propNames = { "errorCode", "descChi", "craftSection", "processCode", "remark", "validResp" };
        ExcelUtils.resolveExcel(inputStream, BsItemFileInputVO.class, propNames);
        ExcelUtils.resolveExcelForEmpInfo(wb, BsItemFileInputVO.class, propNames);

        PowerMockito.when(WorkbookFactory.create(inputStream)).thenThrow(new IOException("TEST1"));
        ExcelUtils.resolveExcel(inputStream, BsItemFileInputVO.class, propNames);
        PowerMockito.doThrow(new IOException("TEST")).when(wb).close();
        Assert.assertNotNull(ExcelUtils.resolveExcel(inputStream, BsItemFileInputVO.class, propNames));

    }

	@Test
	public void parse()throws Exception {
		String value = "2022-08-06 23:23:23";
        Assert.assertNotNull(ExcelUtils.parse(value));
	}

	@Test
	public void parseTwo()throws Exception {
		String value = "2022/08/06 23:23:23";
        Assert.assertNotNull(ExcelUtils.parse(value));
	}

	@Test
	public void parseThree()throws Exception {
		String value = "2022-08-06";
        Assert.assertNotNull(ExcelUtils.parse(value));
	}

	@Test
	public void parseFour()throws Exception {
		String value = "2022/08/06";
        Assert.assertNotNull(ExcelUtils.parse(value));
	}

}
