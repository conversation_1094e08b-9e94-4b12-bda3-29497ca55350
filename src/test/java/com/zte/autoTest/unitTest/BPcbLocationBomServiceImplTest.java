package com.zte.autoTest.unitTest;

import com.zte.application.impl.BPcbLocationBomServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.domain.model.BBomDetailRepository;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class BPcbLocationBomServiceImplTest extends BaseTestCase {

    @InjectMocks
    private BPcbLocationBomServiceImpl service;
    @Mock
    private BBomDetailRepository bBomDetailRepository;

    @Before
    public void before(){
        PowerMockito.mockStatic(CommonUtils.class);
    }

    @Test
    public void getPositionList() {
        Assert.assertNotNull(service.getPositionList(new BBomDetailDTO() {{
            setPlaceNoExt1("1");
        }}));
    }

    @Test
    public void getMiddleStr() {
        Assert.assertNotNull(service.getMiddleStr(22));
    }

    @Test
    public void getMiddleStrNew() {
        Assert.assertNotNull(service.getMiddleStr(1, 100, "111", 1));
    }

    @Test
    public void getTagNumByBomCodeAndItemCode() {
        List<BBomDetailDTO> detailDTOS = new LinkedList<>();
        BBomDetailDTO a1 = new BBomDetailDTO();
        a1.setItemCode("123");
        a1.setPositionExt("123,");
        a1.setPlaceNoExt1("123,");
        a1.setPlaceNoExt2("234,");
        a1.setPlaceNoExt3("123,");
        a1.setPlaceNoExt4("345,");
        a1.setPlaceNoExt5("456");
        detailDTOS.add(a1);
        PowerMockito.when(bBomDetailRepository.selectDetailsByProductCodeAndItem(Mockito.any(), Mockito.anyString()))
                .thenReturn(detailDTOS);

        Assert.assertNotNull(service.getTagNumByBomCodeAndItemCode("123", "123"));
    }

    @Test
    public void handlerContainMinusTest() {
        String content = "21312撒(的谎dasdasd三大2)13123";
        List<String> locList = new ArrayList<>();
        StringBuilder errMsg = new StringBuilder();
        String itemCode = new String();
        service.handlerContainMinus(content, locList, errMsg, itemCode);
        PowerMockito.when(CommonUtils.getLmbMessage(any(),anyString())).thenReturn("");
        service.handlerContainMinus("23(", locList, errMsg, itemCode);
        Assert.assertNotNull(locList);
    }
}