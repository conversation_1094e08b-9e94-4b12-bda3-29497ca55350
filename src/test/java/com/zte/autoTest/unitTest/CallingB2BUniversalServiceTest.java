package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.common.model.MessageId;
import com.zte.common.utils.CallingB2BUniversalService;
import com.zte.infrastructure.remote.CallingB2BUniversalRemoteService;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import static org.mockito.ArgumentMatchers.*;

/**
 * <AUTHOR>
 * @date 2023/12/1
 */
@PrepareForTest({HttpClientUtil.class,JSON.class})
public class CallingB2BUniversalServiceTest extends BaseTestCase {

    @InjectMocks
    private CallingB2BUniversalService service;

    @Test
    public void getRequestBody()throws Exception{
        Object result = service.getRequestBody(null);
        Assert.assertNull(result);


    }
    @Test
    public void handleResponse()throws Exception{
        PowerMockito.mockStatic(JSON.class);
        try {
            service.handleResponse("");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_CALL_B2B_INTERFACE, e.getMessage());
        }
        try {
            service.handleResponse("}");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_CALL_B2B_INTERFACE, e.getMessage());
        }
        ServiceData serviceData = new ServiceData();
        serviceData.setCode(new RetCode());
        String response = "{\n" +
                "  \"code\": {\n" +
                "    \"code\": \"0000\",\n" +
                "    \"msgId\": \"RetCode.Success\",\n" +
                "    \"msg\": \"操作成功\"\n" +
                "  },\n" +
                "  \"bo\": {\"list\":[]}\n" +
                "}";
        PowerMockito.when(JSON.parseObject(eq(response), ArgumentMatchers.same(ServiceData.class))).thenReturn(null);
        try {
            service.handleResponse(response);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_CALL_B2B_INTERFACE, e.getMessage());
        }

        serviceData.setCode(null);
        PowerMockito.when(JSON.parseObject(eq(response), ArgumentMatchers.same(ServiceData.class))).thenReturn(serviceData);
        try {
            service.handleResponse(response);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_CALL_B2B_INTERFACE, e.getMessage());
        }

        serviceData.setCode(new RetCode(RetCode.BUSINESSERROR_CODE,MessageId.FAILED_TO_CALL_B2B_INTERFACE));
        PowerMockito.when(JSON.parseObject(eq(response), ArgumentMatchers.same(ServiceData.class))).thenReturn(serviceData);
        try {
            service.handleResponse(response);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_CALL_B2B_INTERFACE, e.getMessage());
        }
        serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE,MessageId.FAILED_TO_CALL_B2B_INTERFACE));
        PowerMockito.when(JSON.parseObject(eq(response), ArgumentMatchers.same(ServiceData.class))).thenReturn(serviceData);

        service.handleResponse(response);

    }
}
