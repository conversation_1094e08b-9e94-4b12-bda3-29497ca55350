package com.zte.autoTest.unitTest;

import com.zte.application.BBomHeaderService;
import com.zte.application.BsPreItemInfoAsyncService;
import com.zte.application.impl.BsPremanuItemInfoServiceImpl;
import com.zte.application.impl.HrmUserCenterServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.BBomDetailRepository;
import com.zte.domain.model.BsAsyncDataRespository;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.BsItemInfoRepository;
import com.zte.domain.model.BsPremanuBomInfo;
import com.zte.domain.model.BsPremanuBomInfoRepository;
import com.zte.domain.model.BsPremanuItemInfo;
import com.zte.domain.model.BsPremanuItemInfoRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.AsyncBomDTO;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BBomInfoDTO;
import com.zte.interfaces.dto.BsPremanuBomInfoDTO;
import com.zte.interfaces.dto.BsPremanuItemInfoDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.PsWorkOrderBasicDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.Constants;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.util.Pair;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @Date 2020/11/3 10
 * @description:
 */
@PrepareForTest({RetCode.class, SpringContextUtil.class, RequestHeadValidationUtil.class})
public class BsPremanuItemInfoServiceImplTest extends BaseTestCase {

    @InjectMocks
    private BsPremanuItemInfoServiceImpl bsPremanuItemInfoServiceImpl;
    @Mock
    private BsItemInfoRepository bsItemInfoRepository;
    @Mock
    private BBomHeaderService bBomHeaderService;
    @Mock
    private BsPremanuBomInfoRepository bsPremanuBomInfoRepository;
    @Mock
    private BsPremanuItemInfoRepository bsPremanuItemInfoRepository;
    @Mock
    private HrmUserCenterServiceImpl hrmUserCenterService;
    @Mock
    private LocaleMessageSourceBean lmb;
    @Mock
    private BBomDetailRepository bBomDetailRepository;
    @Mock
    private BsAsyncDataRespository bsAsyncDataRespository;
    @Mock
    private BsPreItemInfoAsyncService bsPreItemInfoAsyncService;
    @Mock
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private IdGenerator idGenerator;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);
        PowerMockito.when((RequestHeadValidationUtil.validaFactoryIdAndEmpno())).thenReturn(Pair.of("53", "54"));
    }


    @Test
    public void getSubLevelPremanuInfo() throws Exception {
        // 1.
        BsPremanuItemInfoDTO entity = new BsPremanuItemInfoDTO();
        entity.setPage(1);
        entity.setRows(100);
        entity.setBomCode("123");
        List<BsItemInfo> bomList = new LinkedList<>();
        BsItemInfo b1 = new BsItemInfo();
        BsItemInfo b2 = new BsItemInfo();
        b1.setItemNo("123");
        b2.setItemNo("234");
        bomList.add(b1);
        bomList.add(b2);
        PowerMockito.when(bsItemInfoRepository.getInfoList(Mockito.any())).thenReturn(bomList);

        // 2.
        List<BBomInfoDTO> bomLis = new LinkedList<>();
        List<BBomDetailDTO> itemList = new LinkedList<>();
        BBomDetailDTO dt1 = new BBomDetailDTO();
        dt1.setItemCode("123");
        dt1.setUsageCount(new BigDecimal("123"));
        itemList.add(dt1);
        BBomDetailDTO dt2 = new BBomDetailDTO();
        dt2.setItemCode("234");
        dt2.setUsageCount(new BigDecimal("234"));
        itemList.add(dt2);
        BBomInfoDTO dt3 = new BBomInfoDTO();
        dt3.setListDetail(itemList);
        bomLis.add(dt3);
        PowerMockito.when(bBomHeaderService.getBBomInfoList(Mockito.any())).thenReturn(bomLis);

        // 3.
        List<BsPremanuBomInfo> preBomList = new LinkedList<>();
        BsPremanuBomInfo c1 = new BsPremanuBomInfo();
        c1.setSortSeq(new BigDecimal("1"));
        c1.setTagNum("123");
        c1.setItemNo("123");
        preBomList.add(c1);
        PowerMockito.when(bsPremanuBomInfoRepository.selectBsPremanuBomInfoSelective(Mockito.any()))
                .thenReturn(preBomList);

        // 4.
        List<BsPremanuItemInfo> preItemList = new LinkedList<>();
        BsPremanuItemInfo d1 = new BsPremanuItemInfo();
        d1.setItemNo("123");
        d1.setTagNum("123");
        preItemList.add(d1);

        PowerMockito.when(bsPremanuItemInfoRepository.getPreManuItemInfo(Mockito.any()))
                .thenReturn(preItemList);
        Page<BsPremanuItemInfoDTO> pageItem = bsPremanuItemInfoServiceImpl.getSubLevelPremanuInfo(entity);
        Assert.assertTrue(pageItem.getRows().size() >= 0);
    }

    @Test
    public void addPreManuItemBatch() throws Exception {

        when(SpringContextUtil.getBean(Constants.LOCALE_RESOLVER_MES)).thenReturn(lmb);
        when(lmb.getMessage(Mockito.any())).thenReturn("OK");

        List<BsPremanuItemInfoDTO> list = new ArrayList<>();
        BsPremanuItemInfoDTO bsPremanuItemInfoDTO = new BsPremanuItemInfoDTO();
        bsPremanuItemInfoDTO.setItemNo("123");
        bsPremanuItemInfoDTO.setTraceCode("123");
        list.add(bsPremanuItemInfoDTO);
        try {
            bsPremanuItemInfoServiceImpl.addPreManuItemBatch(list);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ADD_PRE_MANU_INFO_BATCH_FAILED, e.getMessage());
        }

    }

    @Test
    public void runBomCodeAsync() {
        List<AsyncBomDTO> list = new LinkedList<>();
        AsyncBomDTO a1 = new AsyncBomDTO();
        a1.setItemNo("123");
        list.add(a1);

        Assert.assertNotNull(bsPremanuItemInfoServiceImpl.runBomCodeAsync(list));
        List<PsWorkOrderBasicDTO> list1 = new LinkedList<>();
        PsWorkOrderBasicDTO a2 = new PsWorkOrderBasicDTO();
        a2.setItemNo("123");
        list1.add(a2);
        bsPremanuItemInfoServiceImpl.asyncCycleSaveSubLevelPremanuInfo(list1);
    }

    @Test
    public void getBsBomInfo() throws Exception {
        BsPremanuItemInfo dto = new BsPremanuItemInfo();
        dto.setBomCode("123");
        Assert.assertNotNull(bsPremanuItemInfoServiceImpl.getBsBomInfo(dto));
        bsPremanuItemInfoServiceImpl.getDipBomInfo(dto);
    }

    @Test
    public void deleteBsPremanuItemInfoById() throws Exception {
        BsPremanuItemInfo record = new BsPremanuItemInfo();
        record.setSortSeq(new BigDecimal(1));

        List<BsPremanuItemInfo> preList = new LinkedList<>();
        BsPremanuItemInfo a1 = new BsPremanuItemInfo();
        a1.setTypeCode("CX");
        a1.setSortSeq(new BigDecimal(1));
        preList.add(a1);
        BsPremanuItemInfo a2 = new BsPremanuItemInfo();
        a2.setTypeCode("XP");
        a2.setSortSeq(new BigDecimal(2));
        preList.add(a2);
        BsPremanuItemInfo a3 = new BsPremanuItemInfo();
        a3.setTypeCode("HK");
        a3.setSortSeq(new BigDecimal(3));
        preList.add(a3);

        PowerMockito.when(bsPremanuItemInfoRepository.getPremanuInfoList(Mockito.anyMap()))
                .thenReturn(preList);
        bsPremanuItemInfoServiceImpl.deleteBsPremanuItemInfoById(record);
        PowerMockito.when(bsPremanuItemInfoRepository.getPremanuInfoList(Mockito.anyMap()))
                .thenReturn(new LinkedList());
        Assert.assertNotNull(bsPremanuItemInfoServiceImpl.deleteBsPremanuItemInfoById(record));
    }

    /* Started by AICoder, pid:195aao5242d72f7149cc0b76301b941bc5441f97 */
    @Test
    public void convertParamsTest() throws Exception {
        BsPremanuBomInfoDTO dto = new BsPremanuBomInfoDTO();
        Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl, "convertParams", null);
        Assert.assertTrue(true);
        Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl, "convertParams", dto);
        Assert.assertTrue(dto.getItemNoList() == null);
        dto.setInItemNo("'1','2'");
        Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl, "convertParams", dto);
        Assert.assertTrue(dto.getItemNoList().size() == 2);
    }
    /* Ended by AICoder, pid:195aao5242d72f7149cc0b76301b941bc5441f97 */

    @Test
    public void testGetBsItemPreInfoSuccess() throws Exception {
        BsPremanuItemInfo dto = new BsPremanuItemInfo();
        try{
            bsPremanuItemInfoServiceImpl.getBsItemPreInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PLEASE_INPUT_ONE_CONDITION, e.getMessage());
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date1 = sdf.parse("2022-10-01");
        Date date2 = sdf.parse("2023-10-03");
        Date date3 = sdf.parse("2023-08-08");
        BsPremanuItemInfo dto2 = new BsPremanuItemInfo();
        dto2.setPageInt(0);
        dto2.setRowsInt(10);
        dto2.setCreateStartDate(date1);
        dto2.setCreateEndDate(date3);
        dto2.setLastUpdatedStartDate(date1);
        dto2.setLastUpdatedEndDate(date3);
        dto2.setItemNo("111");
        dto2.setCreateBy("00111");
        dto2.setLastUpdatedBy("00222");
        Page<BsPremanuItemInfo> pageInfo = new Page<>();
        pageInfo.setParams(dto2);
        List<BsPremanuItemInfo> list = new ArrayList<>();
        PowerMockito.when(bsPremanuItemInfoRepository.queryBsItemPreInfoList(Mockito.any())).thenReturn(list);
        try{
            bsPremanuItemInfoServiceImpl.getBsItemPreInfo(dto2);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        list.add(dto2);
        PowerMockito.when(bsPremanuItemInfoRepository.queryBsItemPreInfoList(Mockito.any())).thenReturn(list);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("大兴");
        hrmPersonInfoDTOMap.put("00111", hrmPersonInfoDTO);
        HrmPersonInfoDTO hrmPersonInfoDTO1 = new HrmPersonInfoDTO();
        hrmPersonInfoDTO1.setEmpName("小兴");
        hrmPersonInfoDTOMap.put("00222", hrmPersonInfoDTO1);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);
        pageInfo.setRows(list);
        try{
            bsPremanuItemInfoServiceImpl.getBsItemPreInfo(dto2);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        Assert.assertNotNull(dto2);
    }

    @Test
    public void checkQueryParams() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date1 = sdf.parse("2022-10-01");
        Date date2 = sdf.parse("2023-10-03");
        Date date3 = sdf.parse("2023-08-08");

        BsPremanuItemInfo dto1 = new BsPremanuItemInfo();
        dto1.setInItemNo("");
        dto1.setTypeCode("222");
        try{
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl,"checkQueryParams",dto1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PLEASE_INPUT_ONE_CONDITION, e.getMessage());
        }
        BsPremanuItemInfo dto2 = new BsPremanuItemInfo();
        dto2.setItemNo("111");
        dto2.setTypeCode("");
        try{
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl,"checkQueryParams",dto2);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PLEASE_INPUT_ONE_CONDITION, e.getMessage());
        }

        BsPremanuItemInfo dto5 = new BsPremanuItemInfo();
        dto5.setItemNo("");
        dto5.setTypeCode("");
        try{
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl,"checkQueryParams",dto5);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PLEASE_INPUT_ONE_CONDITION, e.getMessage());
        }

        BsPremanuItemInfo dto3 = new BsPremanuItemInfo();
        dto3.setItemNo("");
        dto3.setTypeCode("");
        dto3.setTraceCode("333");
        dto3.setDeliveryProcess("");
        try{
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl,"checkQueryParams",dto3);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PLEASE_INPUT_ONE_CONDITION, e.getMessage());
        }

        BsPremanuItemInfo dto4 = new BsPremanuItemInfo();
        dto4.setItemNo("");
        dto4.setTypeCode("");
        dto4.setTraceCode("");
        dto4.setDeliveryProcess("444");
        try{
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl,"checkQueryParams",dto4);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PLEASE_INPUT_ONE_CONDITION, e.getMessage());
        }

        BsPremanuItemInfo dto6 = new BsPremanuItemInfo();
        dto6.setItemNo("");
        dto6.setTypeCode("");
        dto6.setTraceCode("");
        dto6.setDeliveryProcess("");
        try{
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl,"checkQueryParams",dto6);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PLEASE_INPUT_ONE_CONDITION, e.getMessage());
        }

        dto6.setLastUpdatedStartDate(date1);
        dto6.setLastUpdatedEndDate(date3);
        dto6.setCreateStartDate(date1);
        dto6.setCreateEndDate(date3);
        try{
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl,"checkQueryParams",dto6);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        Assert.assertNotNull(dto6);
    }

    @Test
    public void checkDate() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date1 = sdf.parse("2022-10-01");
        Date date2 = sdf.parse("2023-10-03");
        Date date3 = sdf.parse("2023-08-08");
        BsPremanuItemInfo dto = new BsPremanuItemInfo();
        dto.setCreateStartDate(null);
        dto.setCreateEndDate(date3);
        try {
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl, "checkDate", dto);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        BsPremanuItemInfo dto1 = new BsPremanuItemInfo();
        dto1.setCreateStartDate(date1);
        dto1.setCreateEndDate(null);
        try {
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl, "checkDate", dto1);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }

        BsPremanuItemInfo dto2 = new BsPremanuItemInfo();
        dto2.setCreateStartDate(null);
        dto2.setCreateEndDate(null);
        try {
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl, "checkDate", dto2);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PLEASE_INPUT_ONE_CONDITION, e.getMessage());
        }

        dto2.setLastUpdatedStartDate(date1);
        dto2.setLastUpdatedEndDate(date3);
        try {
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl, "checkDate", dto2);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }

        BsPremanuItemInfo dto3 = new BsPremanuItemInfo();
        dto3.setLastUpdatedStartDate(date1);
        dto3.setLastUpdatedEndDate(date3);
        dto3.setCreateStartDate(date1);
        dto3.setCreateEndDate(date2);
        try {
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl, "checkDate", dto3);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DAY_OVER_ONE_YEAR, e.getMessage());
        }

        BsPremanuItemInfo dto4 = new BsPremanuItemInfo();
        dto4.setLastUpdatedStartDate(date1);
        dto4.setLastUpdatedEndDate(date3);
        dto4.setCreateStartDate(date1);
        dto4.setCreateEndDate(date3);
        try {
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl, "checkDate", dto4);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void checkLastUpdatedDate() throws Exception {
        BsPremanuItemInfo dto = new BsPremanuItemInfo();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date1 = sdf.parse("2022-10-01");
        Date date2 = sdf.parse("2023-10-03");
        Date date3 = sdf.parse("2023-08-08");
        dto.setLastUpdatedStartDate(null);
        dto.setLastUpdatedEndDate(date3);
        try {
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl, "checkLastUpdatedDate", dto);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        BsPremanuItemInfo dto1 = new BsPremanuItemInfo();
        dto1.setLastUpdatedStartDate(date1);
        dto1.setLastUpdatedEndDate(null);
        try {
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl, "checkLastUpdatedDate", dto1);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }

        BsPremanuItemInfo dto2 = new BsPremanuItemInfo();
        dto2.setLastUpdatedStartDate(null);
        dto2.setLastUpdatedEndDate(null);
        try {
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl, "checkLastUpdatedDate", dto2);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PLEASE_INPUT_ONE_CONDITION, e.getMessage());
        }

        BsPremanuItemInfo dto3 = new BsPremanuItemInfo();
        dto3.setLastUpdatedStartDate(date1);
        dto3.setLastUpdatedEndDate(date2);
        try {
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl, "checkLastUpdatedDate", dto3);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DAY_OVER_ONE_YEAR, e.getMessage());
        }

        BsPremanuItemInfo dto4 = new BsPremanuItemInfo();
        dto4.setLastUpdatedStartDate(date1);
        dto4.setLastUpdatedEndDate(date3);
        try {
            Whitebox.invokeMethod(bsPremanuItemInfoServiceImpl, "checkLastUpdatedDate", dto4);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DAY_OVER_ONE_YEAR, e.getMessage());
        }
    }
    @Test
    public void testQueryExportDataSuccess() throws Exception {
        BsPremanuItemInfo dto = new BsPremanuItemInfo();
        try{
            bsPremanuItemInfoServiceImpl.queryExportData(dto,1,10);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PLEASE_INPUT_ONE_CONDITION, e.getMessage());
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date date1 = sdf.parse("2022-10-01");
        Date date2 = sdf.parse("2023-10-03");
        Date date3 = sdf.parse("2023-08-08");
        dto.setPageInt(0);
        dto.setRowsInt(10);
        dto.setCreateStartDate(date1);
        dto.setCreateEndDate(date3);
        dto.setLastUpdatedStartDate(date1);
        dto.setLastUpdatedEndDate(date3);
        dto.setItemNo("111");
        dto.setCreateBy("00111");
        dto.setLastUpdatedBy("00222");
        Page<BsPremanuItemInfo> pageInfo = new Page<>();
        pageInfo.setParams(dto);
        List<BsPremanuItemInfo> list = null;
        PowerMockito.when(bsPremanuItemInfoRepository.queryBsItemPreInfoList(Mockito.any())).thenReturn(list);
        try{
            bsPremanuItemInfoServiceImpl.queryExportData(dto,1,10);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }

        List<BsPremanuItemInfo> list1 = new ArrayList<>();
        list1.add(dto);
        PowerMockito.when(bsPremanuItemInfoRepository.queryBsItemPreInfoList(Mockito.any())).thenReturn(list1);

        Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("大兴");
        hrmPersonInfoDTOMap.put("00111", hrmPersonInfoDTO);
        HrmPersonInfoDTO hrmPersonInfoDTO1 = new HrmPersonInfoDTO();
        hrmPersonInfoDTO1.setEmpName("小兴");
        hrmPersonInfoDTOMap.put("00222", hrmPersonInfoDTO1);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(hrmPersonInfoDTOMap);
        pageInfo.setRows(list);
        try{
            bsPremanuItemInfoServiceImpl.queryExportData(dto,1,10);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        Assert.assertNotNull(dto);
    }


    @Test
    public void testCountExportTotalWhenBsPremanuItemInfoIsValid() {
        try {
            bsPremanuItemInfoServiceImpl.countExportTotal(null);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
        BsPremanuItemInfo dto = new BsPremanuItemInfo();
        dto.setItemNo("111");
        try {
            bsPremanuItemInfoServiceImpl.countExportTotal(dto);
        } catch (Exception e) {
            Assert.assertTrue(e instanceof NullPointerException);
        }
    }
    
    
}
