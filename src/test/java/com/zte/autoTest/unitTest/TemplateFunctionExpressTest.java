package com.zte.autoTest.unitTest;

import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.TemplateFunctionExpress;
import com.zte.interfaces.dto.SpTemplateItemDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/7/14 19:02
 * @Description
 */
@RunWith(PowerMockRunner.class)
public class TemplateFunctionExpressTest extends BaseTestCase {

    @InjectMocks
    TemplateFunctionExpress templateFunctionExpress = new TemplateFunctionExpress("(-1+5)");

    @Test
    public void testRandom() {
        int number = 1000;
        for (int i = 0; i < number; i++) {//生成指定个数的字符串
            System.out.println(new TemplateFunctionExpress("'CMCC-'||RANDSTR('2345679abcdefhkstuxyzACDEFGHKNPQRSTUXYZ',4)").caculate());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }


    @Test
    public void trimZero() {
        templateFunctionExpress.trimZero("");
        templateFunctionExpress.trimZero("1");
        Assert.assertNotNull(templateFunctionExpress.trimZero("0001"));
    }
    @Test
    public void checkFunParamNumber() {
        templateFunctionExpress.caculate();
        templateFunctionExpress.caculate("'abc'");
        templateFunctionExpress.caculate("'abc'||'123'");
        templateFunctionExpress.caculate("3+5+1");
        templateFunctionExpress.caculate("5-2");
        templateFunctionExpress.caculate("5*2");
        Assert.assertNotNull(templateFunctionExpress.caculate("5/2"));
        Assert.assertEquals(new TemplateFunctionExpress("UTL_RAW.BIT_AND('1119','1212')").caculate(), "1010");
        Assert.assertEquals(new TemplateFunctionExpress("UTL_RAW.BIT_OR('1119','1212')").caculate(), "131B");
        Assert.assertEquals(new TemplateFunctionExpress("UTL_RAW.BIT_XOR('1119','1212')").caculate(), "030B");
        Assert.assertEquals(new TemplateFunctionExpress("MOD(6,2)").caculate(), "0");
        Assert.assertEquals(new TemplateFunctionExpress("MOD(6,4)").caculate(), "2");
        new TemplateFunctionExpress("TRUNC(6)").caculate();
        new TemplateFunctionExpress("'CMCC-2CHR'||'-5G'").caculate();
        new TemplateFunctionExpress("ROUND(6.1)").caculate();
        new TemplateFunctionExpress("ROUND(26.36)").caculate();
        new TemplateFunctionExpress("LOWER('ABC')").caculate();
        new TemplateFunctionExpress("UPPER('abc')").caculate();
        new TemplateFunctionExpress("POWER(1,2)").caculate();
        Assert.assertEquals(new TemplateFunctionExpress("TO_NUMBER('6.335','fm99.9')").caculate(), "6.3");
        Assert.assertEquals(new TemplateFunctionExpress("TO_NUMBER('00124.0','fm999999')").caculate(), "124");
        Assert.assertEquals(new TemplateFunctionExpress("TO_NUMBER('00162.463','fm999999.99')").caculate(), "162.46");
        Assert.assertEquals(new TemplateFunctionExpress("TO_NUMBER('123467987','fm999999')").caculate(), "123467");
        Assert.assertEquals(new TemplateFunctionExpress("TO_NUMBER('66','fmXX')").caculate(), "102");
        Assert.assertEquals(new TemplateFunctionExpress("TO_NUMBER('66','fmXXX')").caculate(), "102");
        Assert.assertEquals(new TemplateFunctionExpress("TO_CHAR(6.335,'fm99.9')").caculate(), "6.3");
        Assert.assertEquals(new TemplateFunctionExpress("TO_CHAR(69.335,'fm99.9')").caculate(), "69.3");
        Assert.assertEquals(new TemplateFunctionExpress("TO_CHAR(65469.335,'fm99.9')").caculate(), "69.3");
        Assert.assertEquals(new TemplateFunctionExpress("TO_CHAR(69,'fm99')").caculate(), "69");
        Assert.assertEquals(new TemplateFunctionExpress("TO_CHAR(66,'fmXX')").caculate(), "42");
        Assert.assertEquals(new TemplateFunctionExpress("SUBSTR('12345',0,3)").caculate(), "123");
        Assert.assertEquals(new TemplateFunctionExpress("SUBSTR('123456',-3,2)").caculate(), "45");
        Assert.assertEquals(new TemplateFunctionExpress("FIXEDSTR('提示语','默认值',1,5)").caculate(), "默认值");
        Assert.assertEquals(new TemplateFunctionExpress("FIXEDSTR('提示语','123456',1,5)+1").caculate(), "123457");
        Assert.assertEquals(new TemplateFunctionExpress("1+FIXEDSTR('提示语','123456',1,5)").caculate(), "123457");
        Assert.assertEquals(new TemplateFunctionExpress("FIXEDSTR('提示语','123456',1,5)||'dd'").caculate(), "123456dd");
        Assert.assertEquals(new TemplateFunctionExpress("1+FIXEDSTR('提示语','123456',1,5)||'a'").caculate(), "123457a");
        new TemplateFunctionExpress("RANDSTR('abc',1)").caculate();
        new TemplateFunctionExpress("REPLACE('abcd','c','3')").caculate();
        new TemplateFunctionExpress("GETWHOLEDEVICECODE()").caculate();
        new TemplateFunctionExpress("ROWNUMBER()").caculate();
        new TemplateFunctionExpress("LENGTH('avc')").caculate();
        new TemplateFunctionExpress("ASCII('a')").caculate();
        new TemplateFunctionExpress("CHR(65)").caculate();
        Assert.assertEquals(new TemplateFunctionExpress("TO_NUMBER(REPLACE('10-19-C6-51-00-01','-',''),'fmXXXXXXXXXXXXX')").caculate(), "17702887424001");
        Assert.assertEquals(new TemplateFunctionExpress("TO_NUMBER(REPLACE('10-19-C6-51-00-01','-',''),'fmXXXXXXXXXXXXXXX')+1+281474976710656").caculate(), "299177864134658");
        Assert.assertEquals(new TemplateFunctionExpress("TO_CHAR(TO_NUMBER(REPLACE('10-19-C6-51-00-01','-',''),'fmXXXXXXXXXXXXXXX')+1+281474976710656,'FMXXXXXXXXXXXXXXX')").caculate(), "11019C6510002");
        Assert.assertEquals(new TemplateFunctionExpress("SUBSTR(TO_CHAR(TO_NUMBER(REPLACE('10-19-C6-51-00-01','-',''),'fmXXXXXXXXXXXXXXX')+1+281474976710656,'FMXXXXXXXXXXXXXXX'),-6,2)").caculate(), "51");

        Assert.assertEquals(new TemplateFunctionExpress("LOWER(SUBSTR('AHBCDF',0,3))").caculate(), "ahb");
        Assert.assertEquals(new TemplateFunctionExpress("UPPER(LOWER(SUBSTR('abcdef',0,3)))").caculate(), "ABC");

        TemplateFunctionExpress express = new TemplateFunctionExpress("");
        try {
            express.checkExpression("1+2 * (");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_BRACKETS, e.getMessage());
        }
        try {
            express.checkExpression("6-");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_TWO_OPERATOR, e.getMessage());
        }
        try {
            express.checkExpression("MOD(a,s,2)");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_FUNCTION, e.getMessage());
        }
        try {
            List<String> rp = new ArrayList<>();
            rp.add("++");
            rp.add("**");
            express.caculate(rp);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_TWO_OPERATOR, e.getMessage());
        }

    }

    @Test
    public void checkTemplateItemTest() throws Exception {
        SpTemplateItemDTO item = new SpTemplateItemDTO();
        templateFunctionExpress.checkTemplateItem(item);
        item.setWhetherAdd(true);
        item.setParamType(Constant.ParamType.MAC);
        item.setGenerationMethod(Constant.GenerationMethod.CUSTOMIZED_MAC_START);
        item.setParamValue("-,3");
        templateFunctionExpress.checkTemplateItem(item);
        item.setParamValue(":,3");
        templateFunctionExpress.checkTemplateItem(item);
        item.setParamValue("@,3");
        try {
            templateFunctionExpress.checkTemplateItem(item);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_RULE_ERROR,e.getMessage());
        }


    }
}
