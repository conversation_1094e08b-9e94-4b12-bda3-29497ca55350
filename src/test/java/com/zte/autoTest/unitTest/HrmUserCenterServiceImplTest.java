package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.SysLookupValuesService;
import com.zte.application.impl.HrmUserCenterServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.*;

/**
 * <AUTHOR>
 * @date 2023/1/16
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteUtil.class})
public class HrmUserCenterServiceImplTest extends BaseTestCase {
    @InjectMocks
    private HrmUserCenterServiceImpl hrmUserCenterService;
    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Test
    public void getNodeCodeUniversal() throws Exception {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        List<SysLookupValues> sysLookupValueList =new ArrayList<>();
        SysLookupValues sysLookupValues1=new SysLookupValues();
        sysLookupValues1.setLookupMeaning("url");
        sysLookupValues1.setLookupCode(new BigDecimal("6694001"));
        sysLookupValues1.setAttribute1("printer 300DPL");
        SysLookupValues sysLookupValues2=new SysLookupValues();
        sysLookupValues2.setLookupMeaning("url");
        sysLookupValues2.setLookupCode(new BigDecimal("6694002"));
        sysLookupValues2.setAttribute1("printer 300DPL");
        sysLookupValueList.add(sysLookupValues2);
        sysLookupValueList.add(sysLookupValues1);
        PowerMockito.when(sysLookupValuesService.selectValuesByType(any())).thenReturn(sysLookupValueList);
        Map<String, HrmPersonInfoDTO> map = new HashMap<>();
        map.put("1",new HrmPersonInfoDTO());
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(),anyMap(),anyString(),anyString())).thenReturn( JSON.toJSONString(new ServiceData() {{
            setBo(map);
        }}));
        List<String> userList = new ArrayList<>();
        userList.add(null);
        try {
            hrmUserCenterService.getHrmPersonInfo(userList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_OBTAIN_HR_INFO, e.getMessage());
        }
        userList.add("10");
        try {
            hrmUserCenterService.getHrmPersonInfo(userList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FAILED_TO_OBTAIN_HR_INFO, e.getMessage());
        }
    }
}
