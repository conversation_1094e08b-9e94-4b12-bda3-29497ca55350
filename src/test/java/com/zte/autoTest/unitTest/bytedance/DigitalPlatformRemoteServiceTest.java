package com.zte.autoTest.unitTest.bytedance;

import com.zte.common.model.MessageId;
import com.zte.idatashare.client.clientfactory.DataServiceClientV1;
import com.zte.idatashare.client.dataservice.QueryResult;
import com.zte.infrastructure.remote.DigitalPlatformRemoteService;
import com.zte.interfaces.dto.bytedance.BoardRepairQueryDTO;
import com.zte.itp.msa.util.encrypt.secure.SecureEncryptorUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.test.util.ReflectionTestUtils;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2023-05-23 16:11
 */
@PrepareForTest({DataServiceClientV1.class, SecureEncryptorUtils.class})
public class DigitalPlatformRemoteServiceTest extends BaseTestCase {
    /* Started by AICoder, pid:t5383n82d8e8527146b40b11207be277e3e73793 */
    @Mock
    private String defaultEmpNo;
    @Mock
    private String dbEnv;
    @Mock
    private Long rmaBoardAppId;
    @Mock
    private Long faultInformationAppId;
    @Mock
    private Long boardRepairStationAppId;
    @Mock
    private String appKey;
    @Mock
    private String appSecret;
    @InjectMocks
    private DigitalPlatformRemoteService digitalPlatformRemoteService;
    @Mock
    private DataServiceClientV1 dataServiceClient;

    @Before
    public void init() {
        PowerMockito.mockStatic(DataServiceClientV1.class);
    }

    @Test
    public void rmaBoardQuery() throws Exception {
        BoardRepairQueryDTO queryDTO = new BoardRepairQueryDTO();
        int current = 1;
        int pageSize = 100;
        PowerMockito.mockStatic(SecureEncryptorUtils.class);
        PowerMockito.field(DigitalPlatformRemoteService.class, "rmaBoardAppId")
                .set(digitalPlatformRemoteService, 33L);
        PowerMockito.field(DigitalPlatformRemoteService.class, "faultInformationAppId")
                .set(digitalPlatformRemoteService, 33L);
        PowerMockito.field(DigitalPlatformRemoteService.class, "boardRepairStationAppId")
                .set(digitalPlatformRemoteService, 33L);
        PowerMockito.field(DigitalPlatformRemoteService.class, "appKey")
                .set(digitalPlatformRemoteService, "33");
        PowerMockito.field(DigitalPlatformRemoteService.class, "appSecret")
                .set(digitalPlatformRemoteService, "33");
        PowerMockito.field(DigitalPlatformRemoteService.class, "defaultEmpNo")
                .set(digitalPlatformRemoteService, "33");
        ReflectionTestUtils.setField(digitalPlatformRemoteService, "appCode", "mRshSs4EemGV3ZnrOg36T7zc4NMRPbzENHoFfHk7vfrxk6p52sKzdBNzrSXxYYJqiaAaMR2mBuYzAeh9HGXkNA==");

        ReflectionTestUtils.setField(digitalPlatformRemoteService, "appEnName", "2");

        ReflectionTestUtils.setField(digitalPlatformRemoteService, "vendorName", "2");
        ReflectionTestUtils.setField(digitalPlatformRemoteService, "dataServiceHost", "2");
        PowerMockito.when(SecureEncryptorUtils.decrypt(any(),any())).thenReturn("2");
        PowerMockito.when(DataServiceClientV1.getInstance(any())).thenReturn(dataServiceClient);
        try {
            digitalPlatformRemoteService.rmaBoardQuery(queryDTO, current, pageSize);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.INTERFACE_CALL_ERROR, e.getMessage());
        }
        QueryResult queryResult = new QueryResult();
        PowerMockito.when(dataServiceClient.invoke(any()))
                .thenReturn(queryResult);
        digitalPlatformRemoteService.rmaBoardQuery(queryDTO, current, pageSize);
    }
    /* Ended by AICoder, pid:t5383n82d8e8527146b40b11207be277e3e73793 */

}
