package com.zte.autoTest.unitTest.bytedance;

import com.alibaba.fastjson.JSON;
import com.zte.application.CustomerDataLogService;
import com.zte.application.impl.bytedance.  BoardRepairServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.idatashare.client.dataservice.QueryResult;
import com.zte.domain.model.CustomerDataLogRepository;
import com.zte.domain.model.CustomerItemsRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.infrastructure.remote.AsmRemoteService;
import com.zte.infrastructure.remote.DigitalPlatformRemoteService;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.interfaces.dto.CustomerItemsDTO;
import com.zte.interfaces.dto.bytedance.BoardInformationDTO;
import com.zte.interfaces.dto.bytedance.BoardRepairQueryDTO;
import com.zte.interfaces.dto.bytedance.BoardRepairStationDTO;
import com.zte.interfaces.dto.bytedance.FaultInformationSnDTO;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.ExceptionCommonUtil;
import com.zte.springbootframe.util.RequestHeadValidationUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.util.Pair;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-05-17 15:11
 */
@PrepareForTest({RequestHeadValidationUtil.class,JSON.class, JacksonJsonConverUtil.class, ExceptionCommonUtil.class})
public class BoardRepairServiceImplTest extends BaseTestCase {
    @InjectMocks
    private BoardRepairServiceImpl boardRepairServiceImpl;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private CustomerItemsRepository customerItemsRepository;
    @Mock
    private DigitalPlatformRemoteService bigitalPlatformRemoteService;
    @Mock
    private CustomerDataLogService customerDataLogService;
    @Mock
    private CustomerDataLogRepository customerDataLogRepository;
    @Mock
    private  QueryResult queryResult;
    @Mock
    private AsmRemoteService asmRemoteService;
    @Mock
    private EmailUtils emailUtils;

    @Before
    public void init() {
        PowerMockito.mockStatic(RequestHeadValidationUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ExceptionCommonUtil.class);
        PowerMockito.mockStatic(JSON.class);
    }


    @Test
    public void boardRepairScheduled() throws Exception {
        List<CustomerDataLogDTO> customList = new LinkedList<>();
        CustomerDataLogDTO cus1 = new CustomerDataLogDTO();
        customList.add(cus1);
        PowerMockito.when(customerDataLogRepository.selectByParamsPage(Mockito.any()))
                .thenReturn(customList)
        ;
        PowerMockito.when(RequestHeadValidationUtil.validaFactoryIdAndEmpno())
                .thenReturn(Pair.of("51", "23"));
        BoardRepairQueryDTO boardRepairQueryDTO = new BoardRepairQueryDTO();
        boardRepairQueryDTO.setScheduledType("1");
        boardRepairQueryDTO.setEmpNo("123");
        boardRepairQueryDTO.setFactoryId("43");
        List<SysLookupValues> list34 = new LinkedList<>();
        SysLookupValues sysLookup1 = new SysLookupValues();
        sysLookup1.setLookupMeaning("ll");
        sysLookup1.setAttribute1("email");
        list34.add(sysLookup1);
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.any()))
                .thenReturn(list34)
        ;
        try {
            boardRepairServiceImpl.boardRepairScheduled(boardRepairQueryDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        List<SysLookupValues> sysLookupValues = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        a1.setLookupMeaning("2022-05-05 12:44:44");
        a1.setAttribute1("1");
        a1.setLookupType(new BigDecimal("1004105"));
        a1.setDescriptionChin("a1");
        sysLookupValues.add(a1);
        SysLookupValues a2 = new SysLookupValues();
        a2.setLookupMeaning("2022-05-05 12:44:44");
        a2.setDescriptionChin("a2");
        a2.setAttribute1("2");
        a2.setLookupType(new BigDecimal("1004105"));
        sysLookupValues.add(a2);
        SysLookupValues a3 = new SysLookupValues();
        a3.setLookupMeaning("2022-05-05 12:44:44");
        a3.setDescriptionChin("ff");
        a3.setAttribute1("3");
        a3.setLookupType(new BigDecimal("1004105"));
        sysLookupValues.add(a3);
        PowerMockito.when(sysLookupValuesRepository.selectByTypeBatch(Mockito.anyList()))
                .thenReturn(sysLookupValues);
        boardRepairServiceImpl.boardRepairScheduled(boardRepairQueryDTO);

        List<CustomerItemsDTO> list = new LinkedList<>();
        CustomerItemsDTO b1 = new CustomerItemsDTO();
        b1.setZteCode("1234");
        b1.setCustomerName("bytedance");
        list.add(b1);
        CustomerItemsDTO b2 = new CustomerItemsDTO();
        b2.setZteCode("456");
        b2.setCustomerName("bytedance");
        list.add(b2);
        PowerMockito.when(customerItemsRepository.pageCustomerItemsInfo(Mockito.any()))
                .thenReturn(list);
        List<BoardInformationDTO> boardList = new LinkedList<>();
        BoardInformationDTO c1 = new BoardInformationDTO();
        BoardInformationDTO c2 = new BoardInformationDTO();
        c2.setBoardPn("456");
        boardList.add(c1);
        boardList.add(c2);

        PowerMockito.when(bigitalPlatformRemoteService.rmaBoardQuery(Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(queryResult)
        ;
        queryResult.setTotal(2L);
        boardRepairServiceImpl.boardRepairScheduled(boardRepairQueryDTO);
        List<Map<String, Object>> listMap = new LinkedList<>();
        Map<String, Object> map = new HashMap<>();
        map.put("123", "dd");
        listMap.add(map);
        PowerMockito.when(queryResult.getRows()).thenReturn(listMap);
        PowerMockito.when(queryResult.getRows(BoardInformationDTO.class)).thenReturn(boardList);
        boardRepairServiceImpl.boardRepairScheduled(boardRepairQueryDTO);


        boardRepairQueryDTO.setScheduledType("2");
        List<FaultInformationSnDTO> faultList = new LinkedList<>();
        FaultInformationSnDTO d1 = new FaultInformationSnDTO();
        faultList.add(d1);

        PowerMockito.when(bigitalPlatformRemoteService.faultInformationQuery(Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(queryResult);
        PowerMockito.when(queryResult.getRows()).thenReturn(null);
        boardRepairServiceImpl.boardRepairScheduled(boardRepairQueryDTO);
        PowerMockito.when(queryResult.getRows()).thenReturn(listMap);
        PowerMockito.when(queryResult.getRows(FaultInformationSnDTO.class)).thenReturn(faultList);
        boardRepairServiceImpl.boardRepairScheduled(boardRepairQueryDTO);


        boardRepairQueryDTO.setScheduledType("3");
        List<BoardRepairStationDTO> boardRepairStationList = new LinkedList<>();
        BoardRepairStationDTO e1 = new BoardRepairStationDTO();
        boardRepairStationList.add(e1);

        PowerMockito.when(asmRemoteService.queryBoardStationMsg(Mockito.any()))
                .thenReturn(boardRepairStationList);
        PowerMockito.when(queryResult.getRows()).thenReturn(null);
        boardRepairServiceImpl.boardRepairScheduled(boardRepairQueryDTO);
        PowerMockito.when(queryResult.getRows()).thenReturn(listMap);
        PowerMockito.when(queryResult.getRows(BoardRepairStationDTO.class)).thenReturn(boardRepairStationList);
        boardRepairServiceImpl.boardRepairScheduled(boardRepairQueryDTO);


    }


    @Test
    public void boardRepairRetry() throws Exception {

        List<CustomerDataLogDTO> list = new LinkedList<>();
        PowerMockito.when(RequestHeadValidationUtil.validaFactoryIdAndEmpno())
                .thenReturn(Pair.of("53", "dd"));
        CustomerDataLogDTO customerDataLogDTO = new CustomerDataLogDTO();
        try {
            boardRepairServiceImpl.boardRepairRetry(list);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
        customerDataLogDTO.setId("34");
        list.add(customerDataLogDTO);
        boardRepairServiceImpl.boardRepairRetry(list);

        List<CustomerDataLogDTO> customList = new LinkedList<>();
        CustomerDataLogDTO a1 = new CustomerDataLogDTO();
        customList.add(a1);
        PowerMockito.when(customerDataLogRepository.selectByParamsPage(Mockito.any()))
                .thenReturn(customList)
        ;
        boardRepairServiceImpl.boardRepairRetry(list);
    }

    @Test
    public void callDataTimeSection() throws  Exception{
        BoardRepairQueryDTO boardRepairQueryDTO = new BoardRepairQueryDTO();
        boardRepairServiceImpl.callDataTimeSection(boardRepairQueryDTO);

        boardRepairQueryDTO.setCreatTimeStart("2022-05-05 12:44:44");
        boardRepairQueryDTO.setCreatTimeEnd("2022-05-05 12:44:44");
        PowerMockito.when(RequestHeadValidationUtil.validaFactoryIdAndEmpno())
                .thenReturn(Pair.of("45", "56"));
        boardRepairServiceImpl.callDataTimeSection(boardRepairQueryDTO);
        Assert.assertNotNull(boardRepairQueryDTO);
        boardRepairQueryDTO.setCustomerNameList(Arrays.asList("1234"));
        boardRepairServiceImpl.callDataTimeSection(boardRepairQueryDTO);
        Assert.assertNotNull(boardRepairQueryDTO);
    }
}
