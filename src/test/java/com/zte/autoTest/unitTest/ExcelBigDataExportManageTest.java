package com.zte.autoTest.unitTest;

import com.zte.common.utils.ExcelBigDataExportManage;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.Matchers.any;

@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisCacheUtils.class})
public class ExcelBigDataExportManageTest extends BaseTestCase{

    @InjectMocks
    ExcelBigDataExportManage manage;

    @Test
    public void serverFileManager() {
        PowerMockito.mockStatic(RedisCacheUtils.class);
        PowerMockito.when(RedisCacheUtils.get(any(), any())).thenReturn(null);
        try {
            manage.serverFileManager("1");
        }
        catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }
}