package com.zte.autoTest.unitTest;

import com.zte.application.impl.ResourceOptLogServiceImpl;
import com.zte.domain.model.ResourceOptLogRepository;
import com.zte.interfaces.dto.ResourceOptLogDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>  魏振东
 * @description: TODO
 * @date 2023/10/16 上午10:53
 */
@RunWith(PowerMockRunner.class)
public class ResourceOptLogServiceTest {

    @InjectMocks
    ResourceOptLogServiceImpl resourceOptLogService;

    @Mock
    ResourceOptLogRepository repository;

    @Test
    public void pageTest(){
        resourceOptLogService.batchInsert(new ArrayList<>());
        PowerMockito.when(repository.batchInsert(any())).thenReturn(1);
        List<ResourceOptLogDTO> list = new ArrayList<>();
        list.add(new ResourceOptLogDTO());
        resourceOptLogService.batchInsert(list);
        Assert.assertNotNull(list);
    }
}
