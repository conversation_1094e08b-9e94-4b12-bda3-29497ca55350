package com.zte.autoTest.unitTest;

import com.zte.application.impl.BBomDetailServiceImpl;
import com.zte.domain.model.BBomDetailRepository;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

@RunWith(PowerMockRunner.class)
public class BBomDetailServiceImplTest extends BaseTestCase {
    @InjectMocks
    BBomDetailServiceImpl service;

    @Mock
    BBomDetailRepository bBomDetailRepository;

    @Test
    public void getPbNumByProduction() {
        Assert.assertNotNull(service.getPbNumByProduction("1"));
    }
}