package com.zte.autoTest.unitTest;

import com.zte.application.impl.CenterRedisOperationServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.interfaces.dto.CenterRedisDTO;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;


/**
 * @ProjectName release
 * @PackageName com.zte.autoTest.unitTest
 * @Date 星期二
 * @user 10275508
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class CenterRedisOperationServiceImplTest{
    @InjectMocks
    private CenterRedisOperationServiceImpl centerRedisOperationServiceImpl;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void createBillNo() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.createNo(Mockito.anyString(),Mockito.anyString(),Mockito.anyInt(),Mockito.anyString())).thenReturn("202101");
        CenterRedisDTO redisDTO = new CenterRedisDTO();
        redisDTO.setKey("IMES");
        redisDTO.setInterval(StringUtils.EMPTY);
        redisDTO.setLength(6);
        redisDTO.setSimpleDateFormat("yyyy");

        centerRedisOperationServiceImpl.createBillNo(redisDTO);
        Assert.assertEquals("202101", redisDTO.getBillNo());
    }

    @Test
    public void increaseSequence() {
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.increaseSequence(Mockito.any(),Mockito.any())).thenReturn(1);
        int i = centerRedisOperationServiceImpl.increaseSequence("", 90L);
        Assert.assertEquals(1, i);
    }
}
