package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.impl.CenterFactoryCallSiteServiceImpl;
import com.zte.common.ConstantInterface;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.PlanScheduleRemoteService;
import com.zte.interfaces.dto.AssemblyPushByHandRecordEntityDTO;
import com.zte.interfaces.dto.PsWorkOrderBasicDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.*;

import static org.junit.Assert.assertThrows;
import static org.mockito.Matchers.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteService.class,HttpRemoteUtil.class,ConstantInterface.class,PlanScheduleRemoteService.class})
public class CenterFactoryCallSiteServiceImplTest extends BaseTestCase {

    @InjectMocks
    private CenterFactoryCallSiteServiceImpl service;

    @Mock
    private SysLookupTypesRepository sysLookupTypesRepository;

    @Test
    public void queryInfoTransferOrder() throws Exception{
        PowerMockito.mockStatic(PlanScheduleRemoteService.class);
        List<SysLookupTypesDTO> valuesList =new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO=new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("55");
        valuesList.add(sysLookupTypesDTO);
        PowerMockito.when(sysLookupTypesRepository.getList(anyMap())).thenReturn(valuesList);
        List<PsWorkOrderBasicDTO> list =new ArrayList<>();
        PsWorkOrderBasicDTO psWorkOrderBasicDTO=new PsWorkOrderBasicDTO();
        psWorkOrderBasicDTO.setInforExeFlag("Y");
        list.add(psWorkOrderBasicDTO);
        PowerMockito.when(PlanScheduleRemoteService.getWorkOrderInfo(anyString(),anyString(),anyMap())).thenReturn(list);

        try {
            service.queryInfoTransferOrder("7300298");
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void queryPsTaskInfoByTaskNo() throws Exception{
        PowerMockito.mockStatic(PlanScheduleRemoteService.class);
        List<SysLookupTypesDTO> valuesList =new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO=new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("55");
        valuesList.add(sysLookupTypesDTO);
        PowerMockito.when(sysLookupTypesRepository.getList(anyMap())).thenReturn(valuesList);
        List<PsWorkOrderBasicDTO> list =new ArrayList<>();
        PsWorkOrderBasicDTO psWorkOrderBasicDTO=new PsWorkOrderBasicDTO();
        psWorkOrderBasicDTO.setInforExeFlag("Y");
        list.add(psWorkOrderBasicDTO);
        PowerMockito.when(PlanScheduleRemoteService.getWorkOrderInfo(anyString(),anyString(),anyMap())).thenReturn(list);

        Assert.assertNull(service.queryPsTaskInfoByTaskNo("7300298","55"));
    }

    @Test
    public void queryAssemblyResult() throws Exception{
        PowerMockito.mockStatic(HttpRemoteService.class,HttpRemoteUtil.class,ConstantInterface.class);
        List<SysLookupTypesDTO> valuesList =new ArrayList<>();
        SysLookupTypesDTO sysLookupTypesDTO=new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("55");
        valuesList.add(sysLookupTypesDTO);
        PowerMockito.when(sysLookupTypesRepository.getList(anyMap())).thenReturn(valuesList);
        ServiceData<AssemblyPushByHandRecordEntityDTO> serviceData=new ServiceData<>();
        AssemblyPushByHandRecordEntityDTO assemblyPushByHandRecordEntityDTO=new AssemblyPushByHandRecordEntityDTO();
        assemblyPushByHandRecordEntityDTO.setItemVersion("AB");
        assemblyPushByHandRecordEntityDTO.setItemCode("12145566666");
        serviceData.setBo(assemblyPushByHandRecordEntityDTO);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), any())).thenReturn(JSON.toJSONString(serviceData));
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://ip/zte-mes-manufactureshare-centerfactory/bBomDetailCtrl/getBomItemList");

        PowerMockito.when(HttpRemoteService.remoteExe(any(), anyMap(), anyMap(), anyString())).thenReturn(JSON.toJSONString(serviceData));

        Assert.assertNotNull(service.queryAssemblyResult("7300298","AB"));
    }

    /* Started by AICoder, pid:2e49e2741fef66c14b04094fe094824151394703 */
    @Test
    public void testQueryScheduleStartAndEndTime_Success() throws Exception {
        PowerMockito.mockStatic(PlanScheduleRemoteService.class);
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_1245);

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("1");
        sysLookupTypesDTO.setRemark("remark");

        PowerMockito.when(sysLookupTypesRepository.getList(requestParam)).thenReturn(Collections.singletonList(sysLookupTypesDTO));
        PowerMockito.when(PlanScheduleRemoteService.getScheduleStartAndEndTime(anyString(), anyString(), anyMap()))
                .thenReturn(Collections.singletonMap("scheduleStartDate", new Date()));

        Map<String, Date> result = service.queryScheduleStartAndEndTime("task123", "1");

        Assert.assertEquals(1, result.size());
    }

    @Test
    public void testQueryScheduleStartAndEndTime_NoSysLookupTypesDTO() throws Exception {
        PowerMockito.mockStatic(PlanScheduleRemoteService.class);
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_1245);

        PowerMockito.when(sysLookupTypesRepository.getList(requestParam)).thenReturn(Collections.emptyList());

        Map<String, Date> result = service.queryScheduleStartAndEndTime("task123", "1");

        Assert.assertNull(result);
    }

    @Test
    public void testQueryScheduleStartAndEndTime_Exception() throws Exception {
        PowerMockito.mockStatic(PlanScheduleRemoteService.class);
        Map<String, Object> requestParam = new HashMap<>();
        requestParam.put("lookupType", Constant.LOOK_UP_CODE_1245);

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setLookupMeaning("1");
        sysLookupTypesDTO.setRemark("remark");

        PowerMockito.when(sysLookupTypesRepository.getList(requestParam)).thenReturn(Collections.singletonList(sysLookupTypesDTO));
        PowerMockito.when(PlanScheduleRemoteService.getScheduleStartAndEndTime(anyString(), anyString(), anyMap()))
                .thenThrow(new RuntimeException("Remote service error"));

        Exception exception = assertThrows(Exception.class, () -> {
            service.queryScheduleStartAndEndTime("task123", "1");
        });

        Assert.assertNotNull(exception);
    }
    /* Ended by AICoder, pid:2e49e2741fef66c14b04094fe094824151394703 */
}