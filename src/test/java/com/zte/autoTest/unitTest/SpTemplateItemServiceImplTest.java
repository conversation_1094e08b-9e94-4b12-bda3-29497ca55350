package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.zte.application.impl.ResourceApplicationServiceImpl;
import com.zte.application.impl.SpSpecialityParamServiceImpl;
import com.zte.application.impl.SpTemplateItemServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.SpecialityParamConstant;
import com.zte.common.utils.TemplateFunctionExpress;
import com.zte.domain.model.SpSpecialityParam;
import com.zte.domain.model.SpTemplateItem;
import com.zte.domain.model.SpTemplateItemRepository;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Matchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022/7/14 17:10
 * @Description
 */
@RunWith(PowerMockRunner.class)
public class SpTemplateItemServiceImplTest extends BaseTestCase {

    @InjectMocks
    private SpTemplateItemServiceImpl spTemplateItemService;
    @Mock
    private ResourceApplicationServiceImpl resourceApplicationService;
    @Mock
    private SpSpecialityParamServiceImpl spSpecialityParamService;

    @Mock
    private SpTemplateItemRepository spTemplateItemRepository;

    @Test
    public void queryPage() {
        List<SpTemplateItem> list = new ArrayList<>();
        SpTemplateItem spTemplate = new SpTemplateItem();
        spTemplate.setTemplateId("PG-TEST");
        list.add(spTemplate);
        PowerMockito.doReturn(1L).when(spTemplateItemRepository).countPage(Matchers.any());
        PowerMockito.doReturn(list).when(spTemplateItemRepository).queryPage(Matchers.any());
        PageRows<SpTemplateItem> pageRows = spTemplateItemService.queryPage(new SpTemplateItemQueryDTO());
        Assert.assertTrue(pageRows.getRows().size() >= 0);
    }

    @Test
    public void addBatch() {
        List<SpTemplateItem> list = new ArrayList<>();
        SpTemplateItem spTemplate = new SpTemplateItem();
        spTemplate.setTemplateId("PG-TEST");
        list.add(spTemplate);
        PowerMockito.doReturn(1L).when(spTemplateItemRepository).insertBatch(Matchers.any());
        spTemplateItemService.addBatch(list);
        Assert.assertNotNull(list);
    }

    @Test
    public void updateById() {
        SpTemplateItem spTemplate = new SpTemplateItem();
        spTemplate.setTemplateId("PG-TEST");
        PowerMockito.doReturn(1L).when(spTemplateItemRepository).updateById(Matchers.any());
        spTemplateItemService.updateById(spTemplate);
        Assert.assertNotNull(spTemplate);
    }

    @Test
    public void add() {
        SpTemplateDTO spTemplateDTO = new SpTemplateDTO();
        try {
            spTemplateItemService.previewOrAdd(spTemplateDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_NULL,e.getMessage());
        }
        spTemplateDTO.setWhetherAdd(true);
        List<SpTemplateItemDTO> itemList = new ArrayList() {{
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(false);
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.MAC);
                setGenerationMethod(Constant.GenerationMethod.MAC_START);
                setParamValue("-");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.MAC);
                setGenerationMethod(Constant.GenerationMethod.CUSTOMIZED_MAC_START);
                setParamValue("-,3");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.MAC);
                setGenerationMethod(Constant.GenerationMethod.MAC_END);
                setParamValue("16");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.MAC);
                setGenerationMethod(Constant.GenerationMethod.MAC_ADD);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.GPON_SN);
                setGenerationMethod(Constant.GenerationMethod.GPON_SN);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.GPON_SN);
                setGenerationMethod(Constant.GenerationMethod.CUSTOMIZED_GPON_SN);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.GPON_SN);
                setGenerationMethod(Constant.GenerationMethod.TELMEX_GPON_SN);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.WHOLE_DEVICE_CODE);
                setGenerationMethod(Constant.GenerationMethod.SYSTEM_ALLOCATION);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.ASSIGNMENT);
                setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_DB);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.ASSIGNMENT);
                setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_TEXT);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.ASSIGNMENT);
                setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_VARIABLE);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.ASSIGNMENT);
                setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_TEXT_AND_RANGE);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.INTERVAL_VALUE);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.RANDOM_TYPE);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
                setGenerationMethod(Constant.GenerationMethod.D_SN);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
                setGenerationMethod(Constant.GenerationMethod.DSN_CMCC);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
                setGenerationMethod(Constant.GenerationMethod.DSN_CUCC);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
                setGenerationMethod(Constant.GenerationMethod.DSN_CTCC);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
                setGenerationMethod(Constant.GenerationMethod.DSN_CTCC_FTTO);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
                setGenerationMethod(Constant.GenerationMethod.DSN_CTCC_SN_EMPTY);
                setParamValue("test");
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.D_SN);
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.SCRAMBLING_CODE);
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.NACC);
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.CUSTOMIZE);
            }});
            add(new SpTemplateItemDTO() {{
                setWhetherAdd(true);
                setParamType(Constant.ParamType.STBID);
                setGenerationMethod(Constant.GenerationMethod.STBID_MANUAL);
                setParamRule("MacStart,1111-22#222-333-0-####,Y");
            }});
        }};
        spTemplateDTO.setItemList(itemList);
        Assert.assertNotNull(spTemplateItemService.previewOrAdd(spTemplateDTO));
    }

    @Test
    public void preview2() {
        SpTemplateDTO spTemplateDTO = new SpTemplateDTO();
        List<SpTemplateItemDTO> itemList = new ArrayList () {{
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.MAC);
                setParamName("MACStart");
                setGenerationMethod(Constant.GenerationMethod.MAC_START);
                setParamRule("MACStart(-)");
                setParamValue("2");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.MAC);
                setParamName("MACStart");
                setGenerationMethod(Constant.GenerationMethod.CUSTOMIZED_MAC_START);
                setParamRule("MACStart(-,3)");
                setParamValue("2");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.MAC);
                setParamName("MACEnd");
                setGenerationMethod(Constant.GenerationMethod.MAC_END);
                setParamRule("2");
                setParamValue("2");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.MAC);
                setParamName("MacAdd");
                setGenerationMethod(Constant.GenerationMethod.MAC_ADD);
                setParamRule("MacStart,2");
                setParamValue("2");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.MAC);
                setParamName("MacAdd");
                setGenerationMethod(Constant.GenerationMethod.MAC_ADD);
                setParamRule("MACStart,2");
                setParamValue("2");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.GPON_SN);
                setParamName("gpon-sn");
                setGenerationMethod(Constant.GenerationMethod.CUSTOMIZED_GPON_SN);
                setParamRule("GponSn(ZTEGC0)");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.GPON_SN);
                setParamName("Telmex");
                setGenerationMethod(Constant.GenerationMethod.TELMEX_GPON_SN);
                setParamRule("GponSnTelmex()");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.WHOLE_DEVICE_CODE);
                setParamRule("319");
                setParamName("wd");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.ASSIGNMENT);
                setParamName("CMEI");
                setParamRule("DB(CMEI,TEST)");
                setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_DB);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.ASSIGNMENT);
                setParamName("CTEI");
                setParamRule("DB(CTEI,TEST)");
                setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_DB);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.ASSIGNMENT);
                setParamName("CUEI");
                setParamRule("DB(CUEI,TEST)");
                setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_DB);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.ASSIGNMENT);
                setParamName("IMEI");
                setParamRule("DB(IMEI,TEST)");
                setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_DB);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.ASSIGNMENT);
                setParamName("SN");
                setParamRule("DB(SN,123456)");
                setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_DB);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.ASSIGNMENT);
                setParamName("SN");
                setParamRule("DB(SN,012345678912345678)");
                setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_DB);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.ASSIGNMENT);
                setParamName("STB SN");
                setParamRule("DB(STB SN,TEST)");
                setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_DB);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.ASSIGNMENT);
                setParamName("So-net-SN");
                setParamRule("DB(So-net-SN,TEST)");
                setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_DB);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.ASSIGNMENT);
                setParamName("Text");
                setParamRule("Text(666)");
                setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_TEXT);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.ASSIGNMENT);
                setParamName("ASSIGNMENT_VARIABLE");
                setParamRule("Variable(MACStart)");
                setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_VARIABLE);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.ASSIGNMENT);
                setParamName("ASSIGNMENT_VARIABLE");
                setParamRule("TextAndRange(pre-,MacStart/1/6,,-end)");
                setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_TEXT_AND_RANGE);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.ASSIGNMENT);
                setParamName("ASSIGNMENT_VARIABLE");
                setParamRule("TextAndRange(pre-,MacStart/1/6,MacStart/1/6,-end)");
                setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_TEXT_AND_RANGE);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.RANDOM_TYPE);
                setParamName("Random");
                setParamRule("Random(pre-,0123abcABC,$%#,3,0,-end)");
                setGenerationMethod(Constant.GenerationMethod.RANDOM_PREFIX);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.RANDOM_TYPE);
                setParamName("Random");
                setParamRule("Random(pre-,0123abcABC,$%#,3,1,-end)");
                setGenerationMethod(Constant.GenerationMethod.RANDOM_SUFFIX);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.RANDOM_TYPE);
                setParamName("Random");
                setParamRule("Random(pre-,0123abcABC,$%#,3,2,-end)");
                setGenerationMethod(Constant.GenerationMethod.RANDOM_PREFIX_SUFFIX);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.RANDOM_TYPE);
                setParamName("Random");
                setParamRule("Random(pre-,0123abcABC,$%#,3,2,-end)");
                setGenerationMethod(Constant.GenerationMethod.RANDOM);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.D_SN);
                setParamName("D_SN");
                setParamRule("D_SN(ZTEX012)");
                setGenerationMethod(Constant.GenerationMethod.D_SN);
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.INTERVAL_VALUE);
                setGenerationMethod(Constant.GenerationMethod.INTERVAL_VALUE_PREFIX);
                setParamName("interval");
                setParamRule("IntervalValue(pre-,MACStart/1/6,0,-end)");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.INTERVAL_VALUE);
                setGenerationMethod(Constant.GenerationMethod.INTERVAL_VALUE_PREFIX);
                setParamName("interval");
                setParamRule("IntervalValue(pre-,MACStart/1/6,1,-end)");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.INTERVAL_VALUE);
                setGenerationMethod(Constant.GenerationMethod.INTERVAL_VALUE_PREFIX);
                setParamName("interval");
                setParamRule("IntervalValue(pre-,MACStart/1/6,,-end)");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
                setParamName("DSN_CMCC");
                setGenerationMethod(Constant.GenerationMethod.DSN_CMCC);
                setParamRule("DSN_CMCC()");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
                setParamName("DSN_CMCC");
                setGenerationMethod(Constant.GenerationMethod.DSN_CMCC);
                setParamRule("DSN_CMCC(test)");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
                setParamName("DSN_CUCC");
                setGenerationMethod(Constant.GenerationMethod.DSN_CUCC);
                setParamRule("DSN_CUCC(,TEST,MACStart)");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
                setParamName("DSN_CUCC");
                setGenerationMethod(Constant.GenerationMethod.DSN_CUCC);
                setParamRule("DSN_CUCC(,TEST,Mactart)");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
                setParamName("DSN_CUCC");
                setGenerationMethod(Constant.GenerationMethod.DSN_CUCC);
                setParamRule("DSN_CUCC(test,TEST,MACStart)");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
                setParamName("CTCC");
                setGenerationMethod(Constant.GenerationMethod.DSN_CTCC);
                setParamRule("DSN_CTCC(EE,01)");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
                setParamName("FTTO");
                setGenerationMethod(Constant.GenerationMethod.DSN_CTCC_FTTO);
                setParamRule("DSN_FTTO(E,00)");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.DEVICE_SERIAL_NUMBER);
                setParamName("DSN_CTCC_SN");
                setGenerationMethod(Constant.GenerationMethod.DSN_CTCC_SN_EMPTY);
                setParamRule("DSN_CTCC_SN(FTTO)");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.NASN);
                setParamName("nasn");
                setGenerationMethod(Constant.GenerationMethod.NASN);
                setParamRule("0001-1000");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.SCRAMBLING_CODE);
                setParamName("nasn");
                setGenerationMethod(Constant.GenerationMethod.NASN);
                setParamRule("0001-1000");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.NACC);
                setParamName("nasn");
                setGenerationMethod(Constant.GenerationMethod.NASN);
                setParamRule("0001-1000");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.NACC);
                setParamName("nacc");
                setParamRule("0001-1000");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.CUSTOMIZE);
                setGenerationMethod(Constant.GenerationMethod.CUSTOMIZE);
                setParamName("test");
                setParamRule("SUBSTR([MACStart],1,6)");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.STBID);
                setParamName("STBID");
                setGenerationMethod(Constant.GenerationMethod.STBID_MANUAL);
                setParamRule("STBID(MacStart,1111-22#222-333-0-####,Y)");
            }});
        }};
        spTemplateDTO.setItemList(itemList);
        PowerMockito.when(resourceApplicationService.incrementMacAddress(Mockito.anyString(),Mockito.anyString(),Mockito.anyLong())).thenReturn("001020304050");
        Assert.assertNotNull(spTemplateItemService.previewOrAdd(spTemplateDTO));
        List<SpTemplateItemDTO> itemList1 = new ArrayList () {{
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.MAC);
                setGenerationMethod(Constant.GenerationMethod.MAC_START);
                setParamRule("MACStart(-)");
                setParamValue("2");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.MAC);
                setGenerationMethod(Constant.GenerationMethod.MAC_END);
                setParamRule("2");
            }});
        }};
        spTemplateDTO.setItemList(itemList1);
        Assert.assertNotNull(spTemplateItemService.previewOrAdd(spTemplateDTO));
    }

    @Test
    public void checkTemplateItem() {
        List<SpTemplateItemDTO> itemList = new ArrayList<SpTemplateItemDTO>() {{
            add(new SpTemplateItemDTO() {{
                setParamName("MacStart");
                setParamRule("MacStart(1)");
            }});
        }};
        spTemplateItemService.checkTemplateItem(itemList);
        itemList = new ArrayList<SpTemplateItemDTO>() {{
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.NASN);
                setParamRule("0001-1000");
            }});
        }};
        try {
            spTemplateItemService.checkTemplateItem(itemList);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), MessageId.NET_WORK_PARAMS_MUST_EXISTING_SIMULTANEOUSLY);
        }
        itemList = new ArrayList<SpTemplateItemDTO>() {{
            add(new SpTemplateItemDTO() {{
                setParamName("NASN");
                setParamType(Constant.ParamType.NASN);
                setParamRule("0001-1000");
            }});
            add(new SpTemplateItemDTO() {{
                setParamName("SC");
                setParamType(Constant.ParamType.SCRAMBLING_CODE);
                setParamRule("0001-1000");
            }});
            add(new SpTemplateItemDTO() {{
                setParamName("NACC");
                setParamType(Constant.ParamType.NACC);
                setParamRule("0001-1000");
            }});
        }};
        spTemplateItemService.checkTemplateItem(itemList);
    }

    @Test(expected = MesBusinessException.class)
    public void checkTemplateItem2() {
        List<SpTemplateItemDTO> itemList = new ArrayList<SpTemplateItemDTO>(){{add(new SpTemplateItemDTO(){{}});
            add(new SpTemplateItemDTO(){{}});}};
        spTemplateItemService.checkTemplateItem(itemList);
    }

    @Test(expected = MesBusinessException.class)
    public void ruleHander() throws Exception {
        List<SpTemplatePreviewDTO> list = new ArrayList<>();
        SpTemplatePreviewDTO spTemplatePreviewDTO = new SpTemplatePreviewDTO(){{setParamName("stest");}};
        list.add(spTemplatePreviewDTO);
        Whitebox.invokeMethod(spTemplateItemService, "ruleHander", "", SpecialityParamConstant.BRACKETS_START,list);
    }

    @Test(expected = MesBusinessException.class)
    public void ruleHander2() throws Exception {
        List<SpTemplatePreviewDTO> list = new ArrayList<>();
        SpTemplatePreviewDTO spTemplatePreviewDTO = new SpTemplatePreviewDTO(){{setParamName("stest");}};
        list.add(spTemplatePreviewDTO);
        Whitebox.invokeMethod(spTemplateItemService, "ruleHander", "", "fdst-1",list);
    }

    @Test
    public void getRandomType() throws Exception {
        SpTemplateItemDTO itemDTO = new SpTemplateItemDTO();
        Whitebox.invokeMethod(spTemplateItemService, "getRandomType", itemDTO);

        itemDTO.setParamType(Constant.ParamType.RANDOM_TYPE);
        try {
            Whitebox.invokeMethod(spTemplateItemService, "getRandomType", itemDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, e.getMessage());
        }

        itemDTO.setParamRule("Random(0123abcABC,$%#,3,1,-end)");
        try {
            Whitebox.invokeMethod(spTemplateItemService, "getRandomType", itemDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, e.getMessage());
        }

        itemDTO.setParamRule("Random(pre-,,$%#,3,0,-end)");
        try {
            Whitebox.invokeMethod(spTemplateItemService, "getRandomType", itemDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, e.getMessage());
        }

        itemDTO.setParamRule("Random(pre-,,$%#,3,1,-end)");
        try {
            Whitebox.invokeMethod(spTemplateItemService, "getRandomType", itemDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, e.getMessage());
        }

        itemDTO.setParamRule("Random(pre-,,$%#,3,2,-end)");
        try {
            Whitebox.invokeMethod(spTemplateItemService, "getRandomType", itemDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, e.getMessage());
        }

        itemDTO.setParamRule("Random(pre-,,$%#,3,3,-end)");
        try {
            Whitebox.invokeMethod(spTemplateItemService, "getRandomType", itemDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPECIALITY_PARAM_TEMPLATE_ITEM_RULE_ERR, e.getMessage());
        }
    }

    @Test
    public void handleByMac() throws Exception {
        String mac = "00-00-00-00-00-01";
        Assert.assertNotNull(mac);
        Whitebox.invokeMethod(spTemplateItemService, "handleByMac", mac);

        mac = "00:00:00:00:00:01";
        Whitebox.invokeMethod(spTemplateItemService, "handleByMac", mac);

        mac = "000000000001";
        Whitebox.invokeMethod(spTemplateItemService, "handleByMac", mac);
    }

    @Test
    public void previewStbid() throws Exception {
        SpTemplatePreviewDTO previewDTO = new SpTemplatePreviewDTO();
        PreviewParamsInfoDTO previewParamsInfoDTO = new PreviewParamsInfoDTO();
        previewParamsInfoDTO.setSeparator("");
        String itemJson = "{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"ad264d76-0f33-4d88-8d3a-71445a35ef87\",\"orderNum\":2,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart,1111-22#222-333-0-####,Y)\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false}";
        SpTemplateItemDTO itemDTO = JSONObject.parseObject(itemJson,SpTemplateItemDTO.class);
        Whitebox.invokeMethod(spTemplateItemService, "previewStbid",itemDTO, previewDTO, previewParamsInfoDTO);

        String item1Json = "{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"ad264d76-0f33-4d88-8d3a-71445a35ef87\",\"orderNum\":2,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart1111-22#222-333-0-####,Y)\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false}";
        SpTemplateItemDTO itemDTO1 = JSONObject.parseObject(item1Json,SpTemplateItemDTO.class);
        try {
            Whitebox.invokeMethod(spTemplateItemService, "previewStbid",itemDTO1, previewDTO, previewParamsInfoDTO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.STBIDTYPE_STBIDTEMPLATE_ERROR, e.getMessage());
        }

        String item2Json = "{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"ad264d76-0f33-4d88-8d3a-71445a35ef87\",\"orderNum\":2,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart,111122#222-333-0-####,Y)\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false}";
         SpTemplateItemDTO itemDTO2 = JSONObject.parseObject(item2Json,SpTemplateItemDTO.class);
        try {
            Whitebox.invokeMethod(spTemplateItemService, "previewStbid",itemDTO2, previewDTO, previewParamsInfoDTO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.STBIDTYPE_STBIDCFG_ERROR, e.getMessage());
        }

        String item3Json = "{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"ad264d76-0f33-4d88-8d3a-71445a35ef87\",\"orderNum\":2,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart,1111-222222-333-0-1111,Y)\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false}";
        SpTemplateItemDTO itemDTO3 = JSONObject.parseObject(item3Json,SpTemplateItemDTO.class);
        Whitebox.invokeMethod(spTemplateItemService, "previewStbid",itemDTO3, previewDTO, previewParamsInfoDTO);

        String item4Json = "{\"createBy\":\"10338918\",\"createTime\":1749906367445,\"enabledFlag\":\"Y\",\"generationMethod\":\"非数字化平台获取\",\"itemId\":\"ad264d76-0f33-4d88-8d3a-71445a35ef87\",\"orderNum\":2,\"paramName\":\"STBID\",\"paramRule\":\"STBID(MacStart,1111-2#222-333-0-####,Y)\",\"paramType\":\"STBID\",\"templateId\":\"1ca13958-7185-4546-b4b3-d7a42aeb3c90\",\"updateBy\":\"10338918\",\"updateTime\":1749906474000,\"whetherAdd\":false}";
        SpTemplateItemDTO itemDTO4 = JSONObject.parseObject(item4Json,SpTemplateItemDTO.class);
        try {
            Whitebox.invokeMethod(spTemplateItemService, "previewStbid",itemDTO4, previewDTO, previewParamsInfoDTO);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.STBIDTYPE_STBIDCFG_ERROR, e.getMessage());
        }

    }

    @Test
    public void addRandom() throws Exception {
        SpTemplateItemDTO itemDTO = new SpTemplateItemDTO();
        SpTemplatePreviewDTO previewDTO = new SpTemplatePreviewDTO();
        Whitebox.invokeMethod(spTemplateItemService, "addRandom",  itemDTO,previewDTO);
        Assert.assertNotNull(itemDTO);
    }
}
