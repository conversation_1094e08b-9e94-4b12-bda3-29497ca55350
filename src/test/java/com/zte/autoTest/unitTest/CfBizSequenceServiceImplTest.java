package com.zte.autoTest.unitTest;

import com.zte.application.impl.CfBizSequenceServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.domain.model.CfBizSequenceRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.interfaces.dto.CfBizSequenceDTO;
import com.zte.interfaces.dto.SysLookupValuesDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-10-09 15:47
 */
public class CfBizSequenceServiceImplTest extends BaseTestCase {
    @InjectMocks
    private CfBizSequenceServiceImpl cfBizSequenceServiceImpl;
    @Mock
    private CfBizSequenceRepository cfBizSequenceRepository;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Test
    public void createBillNo() throws Exception {

        CfBizSequenceDTO cfBizSequenceDTO = new CfBizSequenceDTO();
        cfBizSequenceDTO.setBizCode("LR");
        cfBizSequenceDTO.setLength(6);
        cfBizSequenceDTO.setInterval("0");
        cfBizSequenceDTO.setBillCount(4);
        cfBizSequenceDTO.setMinValue(0L);
        cfBizSequenceDTO.setMaxValue(9999l);
        cfBizSequenceDTO.setCurrValue(1L);
        cfBizSequenceDTO.setLastUpdatedDate(new Date());
        cfBizSequenceDTO.setResetCurrValueFlag("Y");
        PowerMockito.when(cfBizSequenceRepository.selectByBizCodeLocked(Mockito.any())).thenReturn(null);
        try {
            cfBizSequenceServiceImpl.createBillNo(cfBizSequenceDTO);
        }catch ( Exception e){
            Assert.assertEquals(MessageId.SEQUENCE_NO_VALUE, e.getMessage());
        }

        cfBizSequenceDTO.setBillCount(0);
        PowerMockito.when(cfBizSequenceRepository.selectByBizCodeLocked(Mockito.any())).thenReturn(cfBizSequenceDTO);
        cfBizSequenceServiceImpl.createBillNo(cfBizSequenceDTO);

        cfBizSequenceDTO.setBillCount(null);
        cfBizSequenceDTO.setSimpleDateFormat("yyMMdd hhmmss");
        cfBizSequenceServiceImpl.createBillNo(cfBizSequenceDTO);

        Date newDate = new Date(new Date().getTime() + 10000);
        cfBizSequenceDTO.setLastUpdatedDate(newDate);
        cfBizSequenceDTO.setResetCurrValueFlag("Y");
        cfBizSequenceServiceImpl.createBillNo(cfBizSequenceDTO);

        cfBizSequenceDTO.setCurrValue(10000L);
        cfBizSequenceDTO.setResetCurrValueFlag("Y");
        cfBizSequenceDTO.setInterval(null);
        try {
            cfBizSequenceServiceImpl.createBillNo(cfBizSequenceDTO);
        }catch ( Exception e){
            Assert.assertEquals(MessageId.SEQUENCE_BEYOND_MAXVALUE, e.getMessage());

        }
        cfBizSequenceDTO.setResetCurrValueFlag("N");
        cfBizSequenceServiceImpl.createBillNo(cfBizSequenceDTO);

        cfBizSequenceDTO.setLastUpdatedDate(newDate);
        cfBizSequenceDTO.setResetCurrValueFlag("N");
        cfBizSequenceServiceImpl.createBillNo(cfBizSequenceDTO);

        cfBizSequenceDTO.setLastUpdatedDate(new Date());
        cfBizSequenceDTO.setCurrValue(10000L);
        cfBizSequenceDTO.setResetCurrValueFlag("N");
        List<SysLookupValues> list = new ArrayList<>();
        SysLookupValues a1 = new SysLookupValues();
        a1.setLookupMeaning("N");
        list.add(a1);
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.anyMap())).thenReturn(list);
        try {
            cfBizSequenceServiceImpl.createBillNo(cfBizSequenceDTO);
        }catch ( Exception e)
        {
            Assert.assertNotNull(e.getMessage(), e.getMessage());
        }



    }

    @Test
    public void queryBillAuto() throws Exception {
        Assert.assertNull(cfBizSequenceServiceImpl.queryBillAuto(new CfBizSequenceDTO()));
    }

    @Test
    public void initBillInfo() throws Exception {
        CfBizSequenceDTO dto = new CfBizSequenceDTO();
        cfBizSequenceServiceImpl.initBillInfo(new CfBizSequenceDTO());
        Assert.assertNotNull(dto);
    }

    @Test
    public void deleteByBizCode() {
        Assert.assertNotNull(cfBizSequenceServiceImpl.deleteByBizCode("jik"));
    }


}
