package com.zte.autoTest.unitTest;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ReflectUtil;
import com.alibaba.fastjson.JSON;
import com.zte.application.*;
import com.zte.application.impl.HrmUserCenterServiceImpl;
import com.zte.application.kafka.producer.KafkaMessageProducer;
import com.zte.application.sncabind.PsTaskService;
import com.zte.application.sncabind.impl.PsTaskServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.KafkaConstant;
import com.zte.domain.model.BBomHeader;
import com.zte.domain.model.BBomHeaderRepository;
import com.zte.domain.model.CFFactory;
import com.zte.domain.model.KafkaLocalMessageRepository;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.domain.model.sncabind.PsTaskRepository;
import com.zte.infrastructure.remote.*;
import com.zte.interfaces.dto.BProdBomHeaderDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.RedisCacheDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.dto.TableColumnsDTO;
import com.zte.interfaces.dto.aps.ApsDerivativeCodeDTO;
import com.zte.interfaces.dto.aps.ApsDerivativeCodeQueryDTO;
import com.zte.interfaces.dto.aps.ApsResponseDTO;
import com.zte.interfaces.dto.task.PsTaskTreeDTO;
import com.zte.interfaces.sncabind.dto.PsTaskDTO;
import com.zte.interfaces.sncabind.dto.PsTaskExportDTO;
import com.zte.interfaces.sncabind.dto.SpecifiedPsTaskDTO;
import com.zte.interfaces.sncabind.dto.TechnicalAndLockInfoDTO;
import com.zte.interfaces.sncabind.vo.SpecifiedPsTaskVO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.AsyncExportFileCommonService;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.ImesExcelUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.mockito.ArgumentMatchers.*;

/**
 * <AUTHOR>
 * @date 2022-10-17 14:22
 */
@PrepareForTest({JacksonJsonConverUtil.class, ImesExcelUtil.class, SpringContextUtil.class,
        CommonUtils.class, BasicsettingRemoteService.class, ConstantInterface.class, HttpClientUtil.class})
public class PsTaskServiceImplTest extends BaseTestCase {
    @InjectMocks
    private PsTaskServiceImpl psTaskServiceImpl;
    @Mock
    private BProdBomHeaderService bProdBomHeaderService;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> operations;
    @Mock
    HttpServletResponse response;
    @Mock
    private PsTaskRepository psTaskRepository;
    @Mock
    private CFFactoryService cFFactoryService;
    @Mock
    private ApsRemoteService apsRemoteService;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private BBomHeaderRepository bBomHeaderRepository;
    @Mock
    private KafkaMessageProducer kafkaMessageProducer;
    @Mock
    private KafkaLocalMessageRepository kafkaLocalMessageRepository;
    @Mock
    private SysLookupTypesRepository sysLookupTypesRepository;
    @Mock
    private HrmUserCenterServiceImpl hrmUserCenterService;
    @Mock
    LocaleMessageSourceBean lmb;
    @Mock
    CenterFactoryCallSiteService centerFactoryCallSiteService;
    @Mock
    private AsyncExportFileCommonService asyncExportFileCommonService;
    @Mock
    private PlanScheduleRemoteService planScheduleRemoteService;
    @Mock
    private ICenterRemoteService iCenterRemoteService;
    @Mock
    private EmailUtils emailUtils;
    @Mock
    private Integer batchExhaustionEarlyWarningValue;
    @Mock
    private OpenApiRemoteService openApiRemoteService;
    @Mock
    private BProdBomChangeDetailService bomChangeDetailService;
    @Mock
    private PdmRemoteService pdmRemoteService;

    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ImesExcelUtil.class);
        PowerMockito.mockStatic(SpringContextUtil.class, ConstantInterface.class, HttpClientUtil.class);
        PowerMockito.field(PsTaskServiceImpl.class, "batchExhaustionEarlyWarningValue").set(psTaskServiceImpl, 5000);
    }
    @Test
    public void setChangeVersion() throws Exception {
        Map<String, ApsResponseDTO> apsResponseDTOMap = new HashMap<>();
        ApsResponseDTO apsResponseDTO = new ApsResponseDTO(){{setChangeVersion("2");setProdplanNo("no");}};

        Whitebox.invokeMethod(psTaskServiceImpl,"setChangeVersion",new ApsResponseDTO(){{setChangeVersion("2");setProdplanNo("no");}},apsResponseDTOMap,new PsTaskDTO());
        Assert.assertNotNull(apsResponseDTO);
        apsResponseDTOMap.put("2",apsResponseDTO);
        Whitebox.invokeMethod(psTaskServiceImpl,"setChangeVersion",new ApsResponseDTO(){{setChangeVersion("2");setProdplanNo("no");}},apsResponseDTOMap,new PsTaskDTO());
        Assert.assertNotNull(apsResponseDTO);
    }
    /* Started by AICoder, pid:fe510q58191123b14fe0096ed132e4431169c52f */
    @Test
    public void generateManufacturingBOM() throws Exception {
        List<PsTaskDTO> insertList = new ArrayList<>();
        insertList.add(new PsTaskDTO(){{setProdplanNo("taskNo2");setChangeVersion("v2");setFactoryId(new BigDecimal("55"));}});
        insertList.add(new PsTaskDTO(){{setProdplanNo("taskNo3");setChangeVersion("v3");}});
        Whitebox.invokeMethod(psTaskServiceImpl,"generateManufacturingBOM",new ArrayList<>());
        Assert.assertEquals(insertList.size(),2);

        List<ApsDerivativeCodeDTO> apsDerivativeCodeDTOList = new ArrayList<>();
        apsDerivativeCodeDTOList.add(new ApsDerivativeCodeDTO() {{
            setProdplanNo("taskNo2");
            setDeriveItemNo("itemNo2");
            setChangeVersion("v1");
        }});
        apsDerivativeCodeDTOList.add(new ApsDerivativeCodeDTO() {{
            setProdplanNo("taskNo2");
            setDeriveItemNo("itemNo2");
            setChangeVersion("v1");
        }});
        apsDerivativeCodeDTOList.add(new ApsDerivativeCodeDTO() {{
            setProdplanNo("taskNo3");
            setDeriveItemNo("itemNo2");
            setChangeVersion("v1");
        }});
        PowerMockito.when(apsRemoteService.getApsDerivativeCodeDTOS(any())).thenReturn(apsDerivativeCodeDTOList);
        Whitebox.invokeMethod(psTaskServiceImpl,"generateManufacturingBOM",insertList);
        Assert.assertEquals(insertList.size(),2);

        apsDerivativeCodeDTOList.add(new ApsDerivativeCodeDTO() {{
            setProdplanNo("taskNo2");
            setDeriveItemNo("itemNo2");
            setChangeVersion("v2");
        }});
        PowerMockito.when(apsRemoteService.getApsDerivativeCodeDTOS(any())).thenReturn(apsDerivativeCodeDTOList);
        Whitebox.invokeMethod(psTaskServiceImpl,"generateManufacturingBOM",insertList);
        Assert.assertEquals(insertList.size(),2);

        List<ApsDerivativeCodeQueryDTO> apsDerivativeCodeQueryDTOList = new ArrayList<>();
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO(){{setProdplanNo("taskNo2");setChangeVersion("v2");}});
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO(){{setProdplanNo("taskNo2");setChangeVersion("v2");}});
        apsDerivativeCodeQueryDTOList.add(new ApsDerivativeCodeQueryDTO(){{setProdplanNo("taskNo3");setChangeVersion("v3");setErrorMsg("2");}});
        PowerMockito.when(bProdBomHeaderService.dealApsDerivedCode(any(),anyBoolean())).thenReturn(apsDerivativeCodeQueryDTOList);
        Whitebox.invokeMethod(psTaskServiceImpl,"generateManufacturingBOM",insertList);
        Assert.assertEquals(insertList.size(),2);
    }

    @Test
    public void dealNoNeedPropertiesDTO() throws Exception {
        List<PsTaskDTO> insertList = new ArrayList<>();
        insertList.add(new PsTaskDTO(){{setProdplanNo("taskNo2");setChangeVersion("v2");setFactoryId(new BigDecimal("55"));}});
        insertList.add(new PsTaskDTO(){{setProdplanNo("taskNo3");setChangeVersion("v3");setDealResult("Y");}});
        Whitebox.invokeMethod(psTaskServiceImpl,"dealNoNeedPropertiesDTO",insertList);
        Assert.assertEquals(insertList.size(),2);

        psTaskServiceImpl.insertDataBatch(new ArrayList<>());
        Assert.assertEquals(insertList.size(),2);
    }
    @Test
    public void setDealResult() throws Exception {
        ApsDerivativeCodeQueryDTO apsDerivativeCodeDTO = new ApsDerivativeCodeQueryDTO() {{
            setProdplanNo("taskNo2");
            setChangeVersion("v2");
        }};
        Map<String, ApsDerivativeCodeQueryDTO> queryDTOMap = new HashMap<>();
        queryDTOMap.put("taskNo2_v2",apsDerivativeCodeDTO);
        PsTaskDTO psTaskDTO = new PsTaskDTO(){{
            setProdplanNo("taskNo2");
            setChangeVersion("v2");}};
        Whitebox.invokeMethod(psTaskServiceImpl,"setDealResult",psTaskDTO,queryDTOMap);
        Assert.assertEquals(queryDTOMap.size(),1);

        apsDerivativeCodeDTO.setErrorMsg("2");
        queryDTOMap.put("taskNo2_v2",apsDerivativeCodeDTO);
        Whitebox.invokeMethod(psTaskServiceImpl,"setDealResult",psTaskDTO,queryDTOMap);
        Assert.assertEquals(queryDTOMap.size(),1);
    }
    /* Ended by AICoder, pid:fe510q58191123b14fe0096ed132e4431169c52f */
    @Test
    public void getTechnicalAndLockInfoDTO() throws Exception {
        Assert.assertNull(psTaskServiceImpl.getTechnicalAndLockInfoDTO("", new SysLookupTypesDTO(), JSON.toJSONString(new TechnicalAndLockInfoDTO())));
    }

    @Test
    public void getTechnicalAndLockInfoByProdplanId() throws Exception {
        Assert.assertNull(psTaskServiceImpl.getTechnicalAndLockInfoByProdplanId(""));
    }


    @Test
    public void getPsTaskByItemNoList() throws Exception {
        List<String> list = new ArrayList<>();
        psTaskServiceImpl.getPsTaskByItemNoList(list);
        list.add("1");
        psTaskServiceImpl.getPsTaskByItemNoList(list);
        List<PsTaskDTO> tempList = new ArrayList<>();
        tempList.add(new PsTaskDTO());
        PowerMockito.when(psTaskRepository.getPsTaskByItemNoList(any())).thenReturn(tempList);
        psTaskServiceImpl.getPsTaskByItemNoList(list);
        PowerMockito.when(psTaskRepository.getPsTaskByItemNoList(any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(psTaskServiceImpl.getPsTaskByItemNoList(list));
    }

    @Test
    public void getPsTaskByProdplanIdList() throws Exception {
        List<String> list = new ArrayList<>();
        psTaskServiceImpl.getPsTaskByProdplanIdList(list);
        list.add("1");
        psTaskServiceImpl.getPsTaskByProdplanIdList(list);
        List<PsTaskDTO> tempList = new ArrayList<>();
        tempList.add(new PsTaskDTO());
        PowerMockito.when(psTaskRepository.getPsTaskByProdplanIdList(any())).thenReturn(tempList);
        psTaskServiceImpl.getPsTaskByProdplanIdList(list);
        PowerMockito.when(psTaskRepository.getPsTaskByProdplanIdList(any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(psTaskServiceImpl.getPsTaskByProdplanIdList(list));
    }

    @Test
    public void queryCacheRows() {
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(operations);
        Assert.assertNull(psTaskServiceImpl.queryCacheRows("REDIS"));
    }

    @Test
    public void saveCacheRows() {
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(operations);
        RedisCacheDTO redisCacheDTO = new RedisCacheDTO();
        redisCacheDTO.setRedisKey("rtt");
        redisCacheDTO.setValue(",,");
        psTaskServiceImpl.saveCacheRows(redisCacheDTO);
        psTaskServiceImpl.deleteCacheRows("rtt");
        Assert.assertNotNull(redisCacheDTO);
    }

    @Test
    public void exportTaskQueryTable() throws Exception {
        PsTaskExportDTO dto = new PsTaskExportDTO();
        dto.setEmpNo("1234");
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(operations);
        PowerMockito.when(operations.get(Mockito.anyString())).thenReturn("null");

        TableColumnsDTO tableColumnsDTO1 = new TableColumnsDTO();
        tableColumnsDTO1.setLabel("prodplanNo");
        tableColumnsDTO1.setLabel("prodplanNo");
        tableColumnsDTO1.setShowCol(false);

        TableColumnsDTO tableColumnsDTO2 = new TableColumnsDTO();
        tableColumnsDTO2.setLabel("taskNo");
        tableColumnsDTO2.setLabel("taskNo");
        tableColumnsDTO2.setShowCol(true);

        TableColumnsDTO tableColumnsDTO3 = new TableColumnsDTO();
        tableColumnsDTO3.setLabel("test");
        tableColumnsDTO3.setLabel("test");
        tableColumnsDTO3.setShowCol(true);
        List<TableColumnsDTO> list2 = new LinkedList<>();
        list2.add(tableColumnsDTO1);
        list2.add(tableColumnsDTO2);
        list2.add(tableColumnsDTO3);
        dto.setTableColumns(list2);

        List<PsTask> list = new LinkedList<>();
        PsTask a1 = new PsTask();
        a1.setInforExe("1");
        a1.setGrantBy("00286523");
        a1.setPlanner("00286523");
        a1.setTechnologist("00286523");
        list.add(a1);
        PowerMockito.when(psTaskRepository.getPsTaskListWithRouteDetail(any())).thenReturn(list)
        ;
        Map<String, HrmPersonInfoDTO> map = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("hl");
        hrmPersonInfoDTO.setId("00286523");
        map.put("00286523", hrmPersonInfoDTO);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(map);

        List<CFFactory> factoryList = new LinkedList<>();
        CFFactory b1 = new CFFactory();
        factoryList.add(b1);
        PowerMockito.when(cFFactoryService.getList(any())).thenReturn(factoryList)
        ;

        List<TableColumnsDTO> tableColumns = new LinkedList<>();
        TableColumnsDTO c1 = new TableColumnsDTO();
        c1.setProp("prodplanNo");
        c1.setShowCol(true);
        c1.setLabel("centerTaskQuery.TABLE_COL_PRODPLAN_NO");
        tableColumns.add(c1);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.anyString(), Mockito.anyObject())).thenReturn(tableColumns);
        dto.setTaskNo("test");
        try {
            psTaskServiceImpl.exportTaskQueryTable(response, dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

        PowerMockito.when(psTaskRepository.getPsTaskListWithRouteDetail(any())).thenReturn(null);

        try {
            psTaskServiceImpl.exportTaskQueryTable(response, dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

        PowerMockito.when(psTaskRepository.getPsTaskListWithRouteDetail(any())).thenReturn(list);
        try {
            psTaskServiceImpl.exportTaskQueryTable(response, dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(null);
        try {
            psTaskServiceImpl.exportTaskQueryTable(response, dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }

        dto.setTableColumns(null);
        try {
            psTaskServiceImpl.exportTaskQueryTable(response, dto);
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void validateParamOfPsTaskTest() throws Exception {
        PsTaskDTO psTaskDTO = null;
        boolean queryFlag = true;

        try {
            Whitebox.invokeMethod(psTaskServiceImpl, "validateParamOfPsTask", psTaskDTO, queryFlag);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CENTER_TASK_QUERY_PARAM_IS_NULL, e.getMessage());
        }
        psTaskDTO = new PsTaskDTO();
        try {
            Whitebox.invokeMethod(psTaskServiceImpl, "validateParamOfPsTask", psTaskDTO, queryFlag);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CENTER_TASK_QUERY_PAGE_OR_ROW_ILLEGAL, e.getMessage());
        }
        psTaskDTO.setPage("1");
        try {
            Whitebox.invokeMethod(psTaskServiceImpl, "validateParamOfPsTask", psTaskDTO, queryFlag);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CENTER_TASK_QUERY_PAGE_OR_ROW_ILLEGAL, e.getMessage());
        }
        psTaskDTO.setRows("1");
        try {
            Whitebox.invokeMethod(psTaskServiceImpl, "validateParamOfPsTask", psTaskDTO, queryFlag);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CENTER_TASK_QUERY_FIVE_PARAMS_NOT_ALL_NULL, e.getMessage());
        }
        psTaskDTO.setReleaseDateStart(new Date());
        psTaskDTO.setReleaseDateEnd(new Date());
        try {
            Whitebox.invokeMethod(psTaskServiceImpl, "validateParamOfPsTask", psTaskDTO, queryFlag);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CENTER_TASK_QUERY_FIVE_PARAMS_NOT_ALL_NULL, e.getMessage());
        }
        psTaskDTO.setReleaseDateStart(null);
        psTaskDTO.setReleaseDateEnd(null);
        psTaskDTO.setToGrantDateStart(new Date());
        psTaskDTO.setToGrantDateEnd(new Date());
        try {
            Whitebox.invokeMethod(psTaskServiceImpl, "validateParamOfPsTask", psTaskDTO, queryFlag);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CENTER_TASK_QUERY_TIME_LARGER_2_YEARS_WHEN_NOT_PROD_AND_TASK, e.getMessage());
        }
        psTaskDTO.setToGrantDateStart(null);
        psTaskDTO.setToGrantDateEnd(null);
        psTaskDTO.setGrantTimeStart(new Date());
        psTaskDTO.setGrantTimeEnd(new Date());
        try {
            Whitebox.invokeMethod(psTaskServiceImpl, "validateParamOfPsTask", psTaskDTO, queryFlag);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.CENTER_TASK_QUERY_TIME_LARGER_2_YEARS_WHEN_NOT_PROD_AND_TASK, e.getMessage());
        }

    }

    @Test
    public void getPsTaskCountWithRouteDetail() throws Exception {
        PowerMockito.when(psTaskRepository.getPsTaskCountWithRouteDetail(any())).thenReturn(1L);
        Assert.assertNotNull(psTaskServiceImpl.getPsTaskCountWithRouteDetail(new HashMap<>()));
    }

    @Test
    public void transferLeadFlagTest() throws Exception {
        List<PsTask> list = new ArrayList<>();
        Whitebox.invokeMethod(psTaskServiceImpl, "transferLeadFlag", list);
        Assert.assertNotNull(list);
    }
    @Test
    public void updateLocalFactoryQty() throws Exception {
        PsTaskDTO a1 = new PsTaskDTO();
        a1.setTaskNo("1");
        a1.setDelSns("1");
        a1.setPlanner("00286523");
        a1.setFactoryId(new BigDecimal(55));
        a1.setTaskQty(new BigDecimal(51));
        try {
            psTaskServiceImpl.updateLocalFactoryQty(new PsTaskDTO());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
        try {
            psTaskServiceImpl.updateLocalFactoryQty(a1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
        a1.setProdplanId("2");
        try {
            psTaskServiceImpl.updateLocalFactoryQty(a1);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TASK_NO_IS_NOT_EXISTS, e.getMessage());
        }
        PowerMockito.when(psTaskRepository.selectPsTaskByTaskNo(any(),any())).thenReturn(new PsTask());
        psTaskServiceImpl.updateLocalFactoryQty(a1);
        Assert.assertNotNull(a1);
    }
    @Test
    public void setQtyChangeFlag() throws Exception {
        List<PsTask> list = new LinkedList<>();
        PsTask a1 = new PsTask();
        a1.setInforExe("1");
        a1.setTaskNo("1");
        a1.setGrantBy("00286523");
        a1.setPlanner("00286523");
        a1.setFactoryId(new BigDecimal(55));
        a1.setTaskQty(new BigDecimal(51));
        list.add(a1);
        list.add(new PsTask(){{setTaskNo("1");setTaskQty(new BigDecimal(0));}});
        list.add(new PsTask(){{setFactoryId(new BigDecimal(51));setTaskNo("1");setTaskQty(new BigDecimal(51));}});
        PowerMockito.when(openApiRemoteService.queryTaskInfo(any())).thenReturn(list);
        psTaskServiceImpl.setQtyChangeFlag(list);
        Assert.assertNotNull(list);
        List<PsTask> list2 = new LinkedList<>();
        PsTask a2 = new PsTask();
        a2.setInforExe("1");
        a2.setTaskNo("11");
        a2.setGrantBy("00286523");
        a2.setPlanner("00286523");
        a2.setFactoryId(new BigDecimal(55));
        a2.setTaskQty(new BigDecimal(51));
        list2.add(a2);
        list2.add(new PsTask(){{setTaskNo("5");setTaskQty(new BigDecimal(0));}});
        list.add(new PsTask(){{setFactoryId(new BigDecimal(51));setTaskNo("5");setTaskQty(new BigDecimal(0));}});
        PowerMockito.when(openApiRemoteService.queryTaskInfo(any())).thenReturn(list);
        psTaskServiceImpl.setQtyChangeFlag(list2);
        Assert.assertNotNull(list);
    }
    @Test
    public void getPsTaskListWithRouteDetail() throws Exception {
        List<PsTask> list = new LinkedList<>();
        PsTask a1 = new PsTask();
        a1.setInforExe("1");
        a1.setGrantBy("00286523");
        a1.setPlanner("00286523");
        a1.setFactoryId(new BigDecimal(55));
        list.add(a1);
        Map<String, HrmPersonInfoDTO> map = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("hl");
        hrmPersonInfoDTO.setId("00286523");
        map.put("00286523", hrmPersonInfoDTO);
        PowerMockito.when(psTaskRepository.getPsTaskListWithRouteDetail(any())).thenReturn(list);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(map);
        psTaskServiceImpl.getPsTaskListWithRouteDetail(new HashMap<>(), "", "", 1L, 1L);
        ReflectUtil.setFieldValue(psTaskServiceImpl,"pdmApiOpen","true");
        psTaskServiceImpl.getPsTaskListWithRouteDetail(new HashMap<>(), "", "", 0L, 0L);
        PowerMockito.when(psTaskRepository.getPsTaskListWithRouteDetail(any())).thenReturn(null);
        psTaskServiceImpl.getPsTaskListWithRouteDetail(new HashMap<>(), "", "", 1L, 1L);
        PowerMockito.when(psTaskRepository.getPsTaskListWithRouteDetail(any())).thenReturn(list);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any())).thenReturn(null);
        a1.setItemNo("10349620");
        PowerMockito.when(pdmRemoteService.getItemNoToCustomerNumberMap(anyCollection())).thenReturn(new HashMap<>());
        Assert.assertNotNull(psTaskServiceImpl.getPsTaskListWithRouteDetail(new HashMap<>(), "", "", 1L, 1L));
    }


    @Test
    public void pullTaskNoFromAps() throws Exception {
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(operations);
        PowerMockito.when(operations.setIfAbsent(Mockito.anyString(), any(), Mockito.anyInt(), Mockito.anyObject()))
                .thenReturn(true);
        PowerMockito.when(SpringContextUtil.getBean("psTaskServiceImpl")).thenReturn(psTaskServiceImpl);

        List<ApsResponseDTO> prodPlanList = new LinkedList<>();
        ApsResponseDTO a1 = new ApsResponseDTO();
        a1.setProdStatus("07");
        a1.setProdAddress("7");
        prodPlanList.add(a1);
        PowerMockito.when(apsRemoteService.getBatchProdPlanSendBomHead(any())).thenReturn(prodPlanList)
        ;
        List<SysLookupValues> list = new LinkedList<>();
        SysLookupValues b1 = new SysLookupValues();
        b1.setLookupCode(new BigDecimal(1005052001));
        b1.setLookupMeaning("7");
        b1.setAttribute1("835");
        b1.setAttribute2("52");
        list.add(b1);
        SysLookupValues b2 = new SysLookupValues();
        b2.setLookupCode(new BigDecimal(1005052001));
        b2.setLookupMeaning("7");
        b2.setAttribute1("email");
        b2.setAttribute2("52");
        list.add(b2);
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.anyMap())).thenReturn(list)
        ;

        BBomHeader c1 = new BBomHeader();
        c1.setProductCode("2345");
        PowerMockito.when(bBomHeaderRepository.selectBatchByHeadId(Mockito.anyString()))
                .thenReturn(c1);

        PowerMockito.when(psTaskRepository.getNextProdplanId()).thenReturn("7520022");

        List<ApsResponseDTO> apsList = new LinkedList<>();
        ApsResponseDTO d1 = new ApsResponseDTO();
        d1.setProdplanNo("99999-356z");
        d1.setChangeType("add");
        apsList.add(d1);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.anyString(),
                Mockito.anyObject())).thenReturn(apsList)
        ;

        PowerMockito.when(operations.get(Mockito.anyString())).thenReturn("6");

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("Y");
        PowerMockito.when(sysLookupValuesRepository.selectSysLookupValuesById(any()))
                .thenReturn(sysLookupValues)
        ;
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(SpringContextUtil.getBean("psTaskServiceImpl", PsTaskService.class))
                .thenReturn(psTaskServiceImpl);
        psTaskServiceImpl.pullTaskNoFromAps(false, "123");

        try {
            psTaskServiceImpl.pullTaskNoFromAps(false, "123");
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void pullTaskNo() throws Exception {
        List<ApsResponseDTO> prodPlanList = new LinkedList<>();
        ApsResponseDTO a1 = new ApsResponseDTO();
        a1.setProdStatus("07");
        a1.setProdAddress("7");
        a1.setProdplanNo("789800-yz");
        a1.setChangeVersion("V00001");
        prodPlanList.add(a1);
        PowerMockito.when(apsRemoteService.getBatchProdPlanSendBomHead(any()))
                .thenReturn(prodPlanList)
        ;

        List<SysLookupValues> list = new LinkedList<>();
        SysLookupValues b1 = new SysLookupValues();
        b1.setLookupCode(new BigDecimal(1005052001));
        b1.setLookupMeaning("7");
        b1.setAttribute1("835");
        b1.setAttribute2("52");
        list.add(b1);
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.anyMap())).thenReturn(list)
        ;

        BBomHeader c1 = new BBomHeader();
        c1.setProductCode("2345");
        PowerMockito.when(bBomHeaderRepository.selectBatchByHeadId(any()))
                .thenReturn(c1);

        PowerMockito.when(psTaskRepository.getNextProdplanId()).thenReturn("7520022");

        List<ApsResponseDTO> taskNoList = new LinkedList<>();
        ApsResponseDTO apsResponseDTO = new ApsResponseDTO();
        apsResponseDTO.setProdplanNo("789800-yz");
        taskNoList.add(apsResponseDTO);

        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("Y");
        PowerMockito.when(sysLookupValuesRepository.selectSysLookupValuesById(any()))
                .thenReturn(sysLookupValues);
        PowerMockito.when(SpringContextUtil.getBean("psTaskServiceImpl", PsTaskService.class))
                .thenReturn(psTaskServiceImpl);
        psTaskServiceImpl.pullTaskNo(false, taskNoList);

        PowerMockito.field(PsTaskServiceImpl.class, "batchExhaustionEarlyWarningValue").set(psTaskServiceImpl, 5000000);

        psTaskServiceImpl.pullTaskNo(false, taskNoList);

        PowerMockito.when(psTaskRepository.getExistingTaskNo(Mockito.anyList()))
                .thenReturn(Arrays.asList("11"));
        try {
            psTaskServiceImpl.pullTaskNo(false, taskNoList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TASK_NO_EXIST_IMES, e.getMessage());
        }

        PowerMockito.when(psTaskRepository.getExistingTaskNo(Mockito.anyList()))
                .thenReturn(null);
        try {
            psTaskServiceImpl.pullTaskNo(true, taskNoList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TASK_NO_EXIST_IMES, e.getMessage());
        }

        a1.setChangeVersion(null);
        try {
            psTaskServiceImpl.pullTaskNo(true, taskNoList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.APS_TASK_STATUS_ERROR, e.getMessage());
        }
    }

    @Test
    public void buildPsTaskDataTest() throws Exception {
        ApsResponseDTO apsResponseDTO = new ApsResponseDTO();
        List<SysLookupValues> list = new ArrayList<>();
        try {
            Whitebox.invokeMethod(psTaskServiceImpl, "buildPsTaskData", apsResponseDTO, list,new HashMap<>());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PROD_ADDRESS_EMPTY, e.getMessage());
        }

    }

    @Test
    public void sendTaskMsgToAps() throws Exception {
        List<PsTask> psTaskList = new LinkedList<>();
        PsTask a1 = new PsTask();
        a1.setTaskNo("gtht-z");
        a1.setProdplanId("8899765");
        psTaskList.add(a1);
        PowerMockito.when(psTaskRepository.queryTaskByTaskNoList(any(), Mockito.anyList()))
                .thenReturn(psTaskList);
        List<String> list = new LinkedList<>();
        list.add("123");
        psTaskServiceImpl.sendTaskMsgToAps(list);
        Assert.assertNotNull(psTaskList);
    }

    @Test
    public void taskInfoChangeableQuery() throws Exception {
        List<ApsResponseDTO> list = new LinkedList<>();
        ApsResponseDTO a1 = new ApsResponseDTO();
        list.add(a1);
        PowerMockito.when(SpringContextUtil.getBean("localeResolverMes")).thenReturn(lmb);
        Assert.assertNotNull(psTaskServiceImpl.taskInfoChangeableQuery(list));
    }

    @Test
    public void taskInfoChangeableQueryTest() throws Exception {
        try {
            psTaskServiceImpl.taskInfoChangeableQuery(new ArrayList<>());
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getExMsgId().equals(MessageId.TASK_NO_IS_NULL));
        }
        PowerMockito.when(SpringContextUtil.getBean("localeResolverMes")).thenReturn(lmb);
        List<ApsResponseDTO> originalList = new ArrayList<>();
        ApsResponseDTO dto1 = new ApsResponseDTO();
        dto1.setProdplanModifyNo("prodplanModifyNo");
        ApsResponseDTO dto2 = new ApsResponseDTO();
        dto2.setProdplanNo("prodplanNo");
        ApsResponseDTO dto3 = new ApsResponseDTO();
        dto3.setProdplanNo("prodplanNo");
        dto3.setProdplanModifyNo("prodplanModifyNo");
        ApsResponseDTO dto4 = new ApsResponseDTO();
        dto4.setProdplanNo("tasKNoCS");
        dto4.setProdplanModifyNo("prodplanModifyNo");
        ApsResponseDTO dto5 = new ApsResponseDTO();
        dto5.setProdplanNo("tasKNoSZ");
        dto5.setProdplanModifyNo("prodplanModifyNo");
        ApsResponseDTO dto6 = new ApsResponseDTO();
        dto6.setProdplanNo("tasKNoHY");
        dto6.setProdplanModifyNo("prodplanModifyNo");
        ApsResponseDTO dto7 = new ApsResponseDTO();
        dto7.setProdplanNo("tasKNoNJ");
        dto7.setProdplanModifyNo("prodplanModifyNo");
        ApsResponseDTO dto8 = new ApsResponseDTO();
        dto8.setProdplanNo("tasKNoXA");
        dto8.setProdplanModifyNo("prodplanModifyNo");
        ApsResponseDTO dto9 = new ApsResponseDTO();
        dto9.setProdplanNo("tasKNoNotExit");
        dto9.setProdplanModifyNo("prodplanModifyNo");
        originalList.add(dto1);
        originalList.add(dto2);
        originalList.add(dto3);
        originalList.add(dto4);
        originalList.add(dto5);
        originalList.add(dto6);
        originalList.add(dto7);
        originalList.add(dto8);
        originalList.add(dto9);
        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask1 = new PsTask();
        psTask1.setFactoryId(new BigDecimal("52"));
        psTask1.setTaskNo("tasKNoCS");
        PsTask psTask2 = new PsTask();
        psTask2.setFactoryId(new BigDecimal("53"));
        psTask2.setTaskNo("tasKNoSZ");
        PsTask psTask3 = new PsTask();
        psTask3.setFactoryId(new BigDecimal("55"));
        psTask3.setTaskNo("tasKNoHY");
        PsTask psTask4 = new PsTask();
        psTask4.setFactoryId(new BigDecimal("58"));
        psTask4.setTaskNo("tasKNoNJ");
        PsTask psTask5 = new PsTask();
        psTask5.setFactoryId(new BigDecimal("56"));
        psTask5.setTaskNo("tasKNoXA");
        PsTask psTask6 = new PsTask();
        psTask6.setFactoryId(new BigDecimal("59"));
        psTask6.setTaskNo("tasKNoNotExit");
        psTaskList.add(psTask1);
        psTaskList.add(psTask2);
        psTaskList.add(psTask3);
        psTaskList.add(psTask4);
        psTaskList.add(psTask5);
        psTaskList.add(psTask6);
        PowerMockito.when(psTaskRepository.queryTaskByTaskNoList(Mockito.anyString(), Mockito.anyList())).thenReturn(psTaskList);
        try {
            psTaskServiceImpl.taskInfoChangeableQuery(originalList);
        } catch (Exception e) {
        }
    }

    @Test
    public void delegateToLocalFactoryTest() throws Exception {
        BigDecimal factoryId = null;
        String emp = "test";
        List<ApsResponseDTO> localFactoryTaskNos = new ArrayList<>();
        ApsResponseDTO dto1 = new ApsResponseDTO();
        dto1.setProdplanNo("taskNo");
        localFactoryTaskNos.add(dto1);
        boolean updateFlag = false;
        PowerMockito.when(SpringContextUtil.getBean("localeResolverMes")).thenReturn(lmb);
        Whitebox.invokeMethod(psTaskServiceImpl, "delegateToLocalFactory", factoryId, emp, localFactoryTaskNos, updateFlag);

        factoryId = new BigDecimal("52");
        PowerMockito.mockStatic(BasicsettingRemoteService.class);
        Map<String, Map<String, String>> map = new HashMap<>();
        Map<String, String> map1 = new HashMap<>();
        map1.put("lookupMeaning", "51");
        map1.put("attribute2", "testCS");
        map.put("1245001", map1);
        PowerMockito.when(BasicsettingRemoteService.getsysLookupTypeList(Mockito.anyString())).thenReturn(map);
        List<SysLookupValues> sysLookValues = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("51");
        sysLookupValues.setAttribute2("testCS");
        sysLookValues.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.anyString())).thenReturn(sysLookValues);
        PowerMockito.when(sysLookupValuesService.getAddressByFactoryId(Mockito.anyString())).thenReturn("");
        Whitebox.invokeMethod(psTaskServiceImpl, "delegateToLocalFactory", factoryId, emp, localFactoryTaskNos, updateFlag);
        PowerMockito.when(sysLookupValuesService.getAddressByFactoryId(Mockito.anyString())).thenReturn("2323");
        map1.put("lookupMeaning", "52");
        Assert.assertNotNull(Whitebox.invokeMethod(psTaskServiceImpl, "delegateToLocalFactory", factoryId, emp, localFactoryTaskNos, updateFlag));

    }

    @Test
    public void consumerModifyProdPlanNoMsgTest() throws Exception {
        String msg = "[{\"curDemandEndDate\":\"2022-12-05 00:00:00.0\",\"curLeadFlag\":\"2\",\"curQty\":10," +
                "\"orgDemandEndDate\":\"2022-12-05 00:00:00.0\",\"orgLeadFlag\":\"1\",\"orgQty\":10,\"prodplanModifyNo\":\"xbt221128CF03-z-B03\"," +
                "\"prodplanNo\":\"xbt221128CF03-z\",\"empNo\":\"10307315\"}]";

        try {
            psTaskServiceImpl.consumerModifyProdPlanNoMsg(msg);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.KAFKA_MSG_SAVE_DATABASE_TASK_UPDATE, e.getMessage());
        }
    }

    @Test
    public void addRetryNumberOrSaveKafkaMsgTest() throws Exception {
        String msg = "[{\"curDemandEndDate\":\"2022-12-05 00:00:00.0\",\"curLeadFlag\":\"2\",\"curQty\":10," +
                "\"orgDemandEndDate\":\"2022-12-05 00:00:00.0\",\"orgLeadFlag\":\"1\",\"orgQty\":10,\"prodplanModifyNo\":\"xbt221128CF03-z-B03\"," +
                "\"prodplanNo\":\"xbt221128CF03-z\",\"empNo\":\"10307315\"}]";
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(operations);
        PowerMockito.when(operations.get(Mockito.anyString())).thenReturn(3);
        PowerMockito.mockStatic(CommonUtils.class);
        Whitebox.invokeMethod(psTaskServiceImpl, "addRetryNumberOrSaveKafkaMsg",
                new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.EMP_NO_IS_NULL), msg, KafkaConstant.ApsKafkaConstant.PRODUCER_TOPIC,
                KafkaConstant.ApsKafkaConstant.APS2_IMES_MODIFY_PROD_PLAN, MessageId.KAFKA_MSG_SAVE_DATABASE_TASK_UPDATE);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void getSpecifiedPsTaskList() throws Exception {
        SpecifiedPsTaskDTO specifiedPsTaskDTO = new SpecifiedPsTaskDTO();
        specifiedPsTaskDTO.setSn("889972200001");
        specifiedPsTaskDTO.setTaskNo("123456,321654");
        specifiedPsTaskDTO.setPage("1");
        specifiedPsTaskDTO.setRows("100");

        List<SpecifiedPsTaskVO> list = new LinkedList<>();
        SpecifiedPsTaskVO a1 = new SpecifiedPsTaskVO();
        a1.setSourceSys("STEP");
        a1.setIsLead("0");
        list.add(a1);
        PowerMockito.when(psTaskRepository.getSpecifiedPsTaskList(any()))
                .thenReturn(list)
        ;

        List<SysLookupTypesDTO> valuesList = new LinkedList<>();
        PowerMockito.when(sysLookupTypesRepository.getList(any()))
                .thenReturn(valuesList);

        List<SpecifiedPsTaskVO> specifiedPsTaskVOList = new LinkedList<>();
        PowerMockito.when(bBomHeaderRepository.selectBBomHeaderByItemNoList(Mockito.anyList()))
                .thenReturn(specifiedPsTaskVOList)
        ;

        Page<SpecifiedPsTaskVO> page = psTaskServiceImpl.getSpecifiedPsTaskList(specifiedPsTaskDTO);
        Assert.assertTrue(page.getRows().size() >= 0);
    }

    @Test
    public void getLeadFlagDescByProdplanId() {
        PowerMockito.when(psTaskRepository.getLeadFlagByProdplanId(Mockito.anyString())).thenReturn("4");
        List<SysLookupValues> list = new ArrayList<>();
        PowerMockito.when(sysLookupValuesService.selectValuesByType(Mockito.anyInt())).thenReturn(list);
        String leadFlagDesc = psTaskServiceImpl.getLeadFlagDescByProdplanId("7777666");
        Assert.assertEquals("", leadFlagDesc);
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("4");
        sysLookupValues.setDescriptionChin("HSF-S");
        list.add(sysLookupValues);
        leadFlagDesc = psTaskServiceImpl.getLeadFlagDescByProdplanId("7777666");
        Assert.assertEquals("HSF-S", leadFlagDesc);
    }

    @Test
    public void selectPsTaskByProdIdSetTest() {
        Set<String> prodIdSet = new HashSet<>();
        psTaskServiceImpl.selectPsTaskByProdIdSet(prodIdSet);
        prodIdSet.add("test");
        Assert.assertNotNull(psTaskServiceImpl.selectPsTaskByProdIdSet(prodIdSet));
    }

    @Test
    public void getPsTaskByTaskNo() throws Exception {
        try {
            psTaskServiceImpl.getPsTaskByTaskNo(null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TASK_NO_CANNOT_BE_NULL, e.getMessage());
        }
        psTaskServiceImpl.getPsTaskByTaskNo("123");
    }

    @Test
    public void checkTaskExist() throws Exception {
        List<SysLookupTypesDTO> factoryList = new ArrayList<>();
        SysLookupTypesDTO dto1 = new SysLookupTypesDTO();
        factoryList.add(dto1);
        PowerMockito.when(centerFactoryCallSiteService.queryPsTaskInfoByTaskNo(any(), any())).thenReturn(null);
        Whitebox.invokeMethod(psTaskServiceImpl, "checkTaskExist", factoryList, "111");

        PsTask psTask = new PsTask();
        PowerMockito.when(centerFactoryCallSiteService.queryPsTaskInfoByTaskNo(any(), any())).thenReturn(psTask);
        try {
            Whitebox.invokeMethod(psTaskServiceImpl, "checkTaskExist", factoryList, "111");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TASK_HAS_BEEN_ISSUED_TO_THE_LOCAL_FACTORY, e.getMessage());
        }
    }

    @Test
    public void getSysLookupTypesDTOS() throws Exception {
        PsTaskDTO taskDto = new PsTaskDTO();
        PsTask psTaskModel = new PsTask();
        taskDto.setTaskNo("1111");

        List<SysLookupTypesDTO> factoryList = new ArrayList<>();
        SysLookupTypesDTO dto1 = new SysLookupTypesDTO();
        dto1.setLookupMeaning("55");
        factoryList.add(dto1);
        PowerMockito.when(centerFactoryCallSiteService.queryPsTaskInfoByTaskNo(any(), any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(psTaskServiceImpl, "getSysLookupTypesDTOS", taskDto, psTaskModel, factoryList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.THE_CURRENT_BATCH_IS_ISSUED, e.getMessage());
        }

        PsTask psTask = new PsTask();
        PowerMockito.when(centerFactoryCallSiteService.queryPsTaskInfoByTaskNo(any(), any())).thenReturn(psTask);
        try {
            Whitebox.invokeMethod(psTaskServiceImpl, "getSysLookupTypesDTOS", taskDto, psTaskModel, factoryList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.THE_BATCH_HAS_GENERATED_A_NESTING_LIST, e.getMessage());
        }

        taskDto.setFactoryId(Constant.FACTORY_ID_51);
        try {
            Whitebox.invokeMethod(psTaskServiceImpl, "getSysLookupTypesDTOS", taskDto, psTaskModel, factoryList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.THE_CURRENT_BATCH_IS_ISSUED, e.getMessage());
        }
    }

    @Test
    public void getFactoryIdByProdplanIdTest() throws Exception {
        List<String> prodList = new ArrayList<>();
        prodList.add("test");
        Assert.assertNotNull(psTaskServiceImpl.getFactoryIdByProdplanId(prodList));
    }

    @Test
    public void getSubTaskTreeByTaskNo() {
        List<String> taskNos = new LinkedList<>();
        taskNos.add("xbt221114CF01-z-1-1");
        List<PsTaskTreeDTO> tempList = new LinkedList<>();
        PsTaskTreeDTO a1 = new PsTaskTreeDTO();
        a1.setTaskNo("xbt221114CF01-z-1");
        a1.setPartsPlanno("xbt221114CF01-z");
        tempList.add(a1);
        PsTaskTreeDTO a2 = new PsTaskTreeDTO();
        a2.setTaskNo("xbt221114CF01-z-1-1");
        a2.setPartsPlanno("xbt221114CF01-z");
        tempList.add(a2);
        PsTaskTreeDTO a3 = new PsTaskTreeDTO();
        a3.setTaskNo("xbt221114CF01-z-1-1-1");
        a3.setPartsPlanno("xbt221114CF01-z");
        tempList.add(a3);

        PowerMockito.when(psTaskRepository.getSubTaskByTaskNoList(Mockito.anyList()))
                .thenReturn(tempList)
        ;

        Assert.assertNotNull(psTaskServiceImpl.getSubTaskTreeByTaskNo(taskNos, "N"));
    }

    @Test
    public void getWmesTaskNo() throws Exception {
        List<String> taskNoList = new ArrayList<>();
        List<String> wmesTaskNo = psTaskServiceImpl.getWmesTaskNo(taskNoList);
        Assert.assertEquals(0, wmesTaskNo.size());
        taskNoList.add("1");
        PowerMockito.when(psTaskRepository.getWmesTaskNo(any())).thenReturn(new ArrayList<String>() {{
            add("111");
        }});
        List<String> taskNo = psTaskServiceImpl.getWmesTaskNo(taskNoList);
        Assert.assertEquals(1, taskNo.size());
    }


    @Test
    public void getPsTask() throws Exception {
        List<PsTask> res = psTaskServiceImpl.getPsTask(null);
        Assert.assertTrue(CollUtil.isEmpty(res));

        List<String> taskNoList = new ArrayList<>();
        taskNoList.add("111");
        List<PsTask> psTasks = new ArrayList<>();
        PowerMockito.when(psTaskRepository.queryTaskByTaskNoList(any(),any())).thenReturn(psTasks);
        res = psTaskServiceImpl.getPsTask(taskNoList);
        Assert.assertEquals(0, res.size());
    }

    @Test
    public void selectErpStatusByProdplanId() throws Exception {
        try{
            psTaskServiceImpl.selectErpStatusByProdplanId(null);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.DATA_IS_EMPTY, e.getMessage());
        }
        List<String> prodplanList = new ArrayList<>();
        prodplanList.add("111");
        prodplanList.add("222");
        List<PsTaskDTO> list = new ArrayList<>();
        PsTaskDTO dto = new PsTaskDTO();
        dto.setProdplanId("111");
        dto.setErpStatus("完成");
        list.add(dto);
        PowerMockito.when(psTaskRepository.selectErpStatusByProdplanId(Mockito.any())).thenReturn(list);
        Assert.assertNotNull(psTaskServiceImpl.selectErpStatusByProdplanId(prodplanList));
    }

    @Test
    public void getTaskInfoByTaskNo() throws Exception {
        String taskNo = "";
        psTaskServiceImpl.getTaskInfoByTaskNo(taskNo);
        taskNo = "TEST";
        Assert.assertNotNull(taskNo);
        psTaskServiceImpl.getTaskInfoByTaskNo(taskNo);
    }

    @Test
    public void taskBomChangeableQueryTest() {
        String taskNo = "";
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(MessageId.APS_TASK_NO_IS_NULL)).thenReturn("APS_TASK_NO_IS_NULL");
        CommonUtils.getLmbMessage(MessageId.APS_TASK_NO_IS_NULL);
        String s = psTaskServiceImpl.taskBomChangeableQuery(taskNo);
        Assert.assertTrue("APS_TASK_NO_IS_NULL".equals(s));

        taskNo = "test";
        List<PsTask> psTasks = new ArrayList<>();
        PowerMockito.when(psTaskRepository.selectPsTaskList(Mockito.anyString(), Mockito.anyString())).thenReturn(psTasks);
        PowerMockito.when(CommonUtils.getLmbMessage(MessageId.TASK_NO_INFO_IS_NULL)).thenReturn("TASK_NO_INFO_IS_NULL");
        String s1 = psTaskServiceImpl.taskBomChangeableQuery(taskNo);
        Assert.assertTrue("TASK_NO_INFO_IS_NULL".equals(s1));

        PsTask psTask1 = new PsTask();
        psTasks.add(psTask1);
        PowerMockito.when(psTaskRepository.selectPsTaskList(Mockito.anyString(), Mockito.anyString())).thenReturn(psTasks);
        PowerMockito.when(CommonUtils.getLmbMessage(MessageId.APS_TASK_NO_INFO_FACTORY_ID_IS_NULL)).thenReturn("APS_TASK_NO_INFO_FACTORY_ID_IS_NULL");
        String s2 = psTaskServiceImpl.taskBomChangeableQuery(taskNo);
        Assert.assertTrue("APS_TASK_NO_INFO_FACTORY_ID_IS_NULL".equals(s2));

        psTask1.setFactoryId(new BigDecimal("52"));
        PowerMockito.when(psTaskRepository.selectPsTaskList(Mockito.anyString(), Mockito.anyString())).thenReturn(psTasks);
        String address = "";
        PowerMockito.when(sysLookupValuesService.getAddressByFactoryId(Mockito.any())).thenReturn(address);
        PowerMockito.when(CommonUtils.getLmbMessage(MessageId.FACTORY_ID_OF_CENTER_PSTASK_IS_ILLEGALITY)).thenReturn("FACTORY_ID_OF_CENTER_PSTASK_IS_ILLEGALITY");
        String s3 = psTaskServiceImpl.taskBomChangeableQuery(taskNo);
        Assert.assertTrue("FACTORY_ID_OF_CENTER_PSTASK_IS_ILLEGALITY".equals(s3));

        address = "test";
        PowerMockito.when(sysLookupValuesService.getAddressByFactoryId(Mockito.any())).thenReturn(address);
        PowerMockito.when(openApiRemoteService.taskBomChangeableQueryDelegate(Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString())).thenReturn("result");
        String s4 = psTaskServiceImpl.taskBomChangeableQuery(taskNo);
        Assert.assertTrue("result".equals(s4));
    }

    @Test
    public void queryFactoryIdByTaskNoList() {
        List<String> prodIdList= new ArrayList<>();
        prodIdList.add("2");
        Assert.assertNotNull(psTaskServiceImpl.queryFactoryIdByTaskNoList(null));

        Assert.assertNotNull(psTaskServiceImpl.queryFactoryIdByTaskNoList(prodIdList));

        PowerMockito.when(psTaskRepository.queryFactoryIdByTaskNoList(any())).thenReturn(new ArrayList(){{
            add(new PsTask());
        }});
        Assert.assertEquals(psTaskServiceImpl.queryFactoryIdByTaskNoList(prodIdList).size(),1);
    }
    /* Started by AICoder, pid:73afcc1f9akfdd3148b308d030d71e224a82aaa3 */

    @Test
    public void setMBomTest() throws Exception {
        Whitebox.invokeMethod(psTaskServiceImpl, "setMBom", null);
        Assert.assertTrue(1==1);
        List<PsTask> list = new ArrayList<>();
        PsTask entity = new PsTask();
        entity.setProdplanId("1234567");
        list.add(entity);
        PsTask entity1 = new PsTask();
        entity1.setProdplanId("12345671");
        entity1.setItemNo("itemNo");
        list.add(entity1);
        List<BProdBomHeaderDTO> bProdBomHeaderDTOS = new ArrayList<>();
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        dto.setProdplanId("1234567");
        dto.setProductCode("test");
        bProdBomHeaderDTOS.add(dto);
        PowerMockito.when(bProdBomHeaderService.queryProductCodeByProdPlanIdList(Mockito.anyList())).thenReturn(bProdBomHeaderDTOS);
        Whitebox.invokeMethod(psTaskServiceImpl, "setMBom", list);
        Assert.assertTrue(list.get(0).getmBom().equals("test"));
        Assert.assertTrue(list.get(1).getmBom().equals("itemNo"));
    }
    /* Ended by AICoder, pid:73afcc1f9akfdd3148b308d030d71e224a82aaa3 */

    @Test
    public void setProdListByMBom () {
        Assert.assertTrue(psTaskServiceImpl.setProdListByMBom(new PsTaskDTO()));
        PsTaskDTO psTaskDTO = new PsTaskDTO();
        psTaskDTO.setItemNo("123");
        Assert.assertTrue(psTaskServiceImpl.setProdListByMBom(psTaskDTO));
        List<BProdBomHeaderDTO> bomList = new ArrayList<>();
        BProdBomHeaderDTO dto = new BProdBomHeaderDTO();
        bomList.add(dto);
        PowerMockito.when(bomChangeDetailService.selectOriginalByProductCode(Mockito.any())).thenReturn(bomList);
        Assert.assertTrue(psTaskServiceImpl.setProdListByMBom(psTaskDTO));
        dto.setProdplanId("123");
        psTaskDTO.setProdplanId("321");
        psTaskDTO.setItemNo("123");
        Assert.assertFalse(psTaskServiceImpl.setProdListByMBom(psTaskDTO));
        psTaskDTO.setProdplanId("123");
        psTaskDTO.setItemNo("123");
        Assert.assertTrue(psTaskServiceImpl.setProdListByMBom(psTaskDTO));
    }
}
