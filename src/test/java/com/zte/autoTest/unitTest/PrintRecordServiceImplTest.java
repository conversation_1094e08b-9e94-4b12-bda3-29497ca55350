package com.zte.autoTest.unitTest;

import com.zte.application.HrmUserCenterService;
import com.zte.application.impl.PrintRecordServiceImpl;
import com.zte.domain.model.PrintRecord;
import com.zte.domain.model.PrintRecordRepository;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.PrintRecordDTO;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest(ServiceDataBuilderUtil.class)
public class PrintRecordServiceImplTest extends BaseTestCase {

    @InjectMocks
    private PrintRecordServiceImpl service;

    @Mock
    private PrintRecordRepository recordRepository;

    @Mock
    private HrmUserCenterService hrmUserCenterService;


    @Test
    public void batchInsert() {
        PowerMockito.when(recordRepository.batchInsert(anyList())).thenReturn(1L);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.success()).thenReturn(new ServiceData<>());
        List<PrintRecord> list = new ArrayList<>();
        PrintRecord printRecord = new PrintRecord();
        list.add(printRecord);
        Assert.assertNotNull(service.batchInsert(list));
    }
    @Test
    public void queryRecordBySn() {
        PowerMockito.when(recordRepository.queryRecordBySn(anyString())).thenReturn(new PrintRecord());
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.success()).thenReturn(new ServiceData<>());
        Assert.assertNotNull(service.queryRecordBySn("777888990011"));
    }
    @Test
    public void getListBySns()throws Exception {
        PowerMockito.when(recordRepository.queryRecordBySn(anyString())).thenReturn(new PrintRecord());
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.when(ServiceDataBuilderUtil.success()).thenReturn(new ServiceData<>());
        Assert.assertNotNull(service.getListBySns("777888990011"));
    }
    @Test
    public void getPage() throws Exception{
        PrintRecordDTO printRecordDTO =new PrintRecordDTO();
        List<String> snList =new ArrayList<>();
        snList.add("777");
        printRecordDTO.setSnList(snList);
        List<PrintRecord> list =new ArrayList<>();
        PrintRecord printRecord =new PrintRecord();
        printRecord.setSn("ZS0001555");
        printRecord.setUpdateBy("10");
        printRecord.setCreateBy("17");
        list.add(printRecord);
        PowerMockito.when(recordRepository.getList(any
                ())).thenReturn(list);
        Map<String, HrmPersonInfoDTO> map=new HashMap<>();
        map.put("10",new HrmPersonInfoDTO());
        map.put("17",new HrmPersonInfoDTO());
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(any
                ())).thenReturn(map);
        Page<PrintRecord> pageInfo = service.getPage(printRecordDTO);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }
    @Test
    public void batchUpdateBySnList() throws Exception{
        PrintRecordDTO printRecordDTO=new PrintRecordDTO();
        List<String> snList =new ArrayList<>();
        snList.add("777");
        printRecordDTO.setSnList(snList);
        service.batchUpdateBySnList(printRecordDTO);
        Assert.assertNotNull(printRecordDTO);
    }
}