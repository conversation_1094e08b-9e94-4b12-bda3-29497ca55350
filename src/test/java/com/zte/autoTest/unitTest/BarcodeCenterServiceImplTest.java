package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.SysLookupValuesService;
import com.zte.application.impl.BarcodeCenterServiceImpl;
import com.zte.common.ConstantInterface;
import com.zte.common.utils.Constant;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Matchers.*;

/**
 * <AUTHOR>
 * @date 2021/6/19
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class, MESHttpHelper.class, BarcodeCenterRemoteService.class,BasicsettingRemoteService.class,HttpRemoteUtil.class,HttpRemoteService.class})
public class BarcodeCenterServiceImplTest extends BaseTestCase {
    @InjectMocks
    private BarcodeCenterServiceImpl barcodeCenterServiceImpl;

    @InjectMocks
    private BarcodeCenterRemoteService barcodeCenterRemoteServiceInject;

    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    private ConstantInterface constantInterface;

    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private RedisTemplate redisTemplate;
    @Mock
    private ValueOperations<String , String> valueOperations;

    @Test
    public void getTemplateNameList()throws Exception {
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, BasicsettingRemoteService.class,HttpRemoteUtil.class,HttpRemoteService.class);
        SysLookupTypesDTO sysLookupTypesDTOUrl=new SysLookupTypesDTO();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        PowerMockito.when(BasicsettingRemoteService.getSysLookUpValue(anyString(),anyString())).thenReturn(sysLookupTypesDTOUrl);
        Map<String,String> header=new HashMap<>();
        header.put("X-Factory-Id","52");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyMap(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        barcodeCenterServiceImpl.getTemplateNameList("templateContent");
        SysLookupValues sysLookupValues=new SysLookupValues();
        sysLookupValues.setLookupMeaning("url");
        sysLookupValues.setLookupCode(new BigDecimal("1004052011"));
        sysLookupValues.setAttribute1("printer 300DPL");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(sysLookupValues);
        List<SysLookupValues> sysLookupValueList =new ArrayList<>();
        SysLookupValues sysLookupValues2=new SysLookupValues();
        sysLookupValues2.setLookupMeaning("url");
        sysLookupValues2.setLookupCode(new BigDecimal("1004052012"));
        sysLookupValues2.setAttribute1("printer 300DPL");
        sysLookupValueList.add(sysLookupValues);
        sysLookupValueList.add(sysLookupValues2);
        PowerMockito.when(sysLookupValuesService.selectValuesByType(any())).thenReturn(sysLookupValueList);
        Assert.assertNotNull(barcodeCenterRemoteServiceInject.getTemplateNameList("templateContent"));
        BarcodeCenterTemplatePrintDTO barcodeCenterTemplatePrintDTO =new BarcodeCenterTemplatePrintDTO();
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(""));
                }})
        );
        barcodeCenterRemoteServiceInject.serverTemplatePrint(barcodeCenterTemplatePrintDTO);

    }



    @Test
    public void getDownloadUrl()throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, BarcodeCenterRemoteService.class,BasicsettingRemoteService.class,HttpRemoteUtil.class,HttpRemoteService.class);

        SysLookupValues sysLookupTypesDTOUrl=new SysLookupValues();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(any())).thenReturn(sysLookupTypesDTOUrl);
        BarcodeCenterDTO barcodeCenterDTO1=new BarcodeCenterDTO();
        barcodeCenterDTO1.setDownloadUrl("222");
        ServiceData serviceData=new ServiceData();
        serviceData.setBo(barcodeCenterDTO1);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));

        BarcodeCenterDTO barcodeCenterDTO=new BarcodeCenterDTO();
        barcodeCenterDTO.setSn("sn");
        barcodeCenterDTO.setPrinter("pinter");
        barcodeCenterDTO.setTemplateName("name");
        Assert.assertNotNull(barcodeCenterServiceImpl.getDownloadUrl());

    }

    @Test
    public void testBarcodeQuery()throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, BarcodeCenterRemoteService.class,BasicsettingRemoteService.class,HttpRemoteUtil.class,HttpRemoteService.class);
        SysLookupValues sysLookupTypesDTOUrl=new SysLookupValues();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(any())).thenReturn(sysLookupTypesDTOUrl);

        BarcodeExpandVO barcodeExpandVO1=new BarcodeExpandVO();
        barcodeExpandVO1.setCategoryName("111");
        BarcodeExpandVO barcodeExpandVO2 = new BarcodeExpandVO();
        barcodeExpandVO2.setCategoryName("222");
        List<BarcodeExpandVO> barcodeExpandVOList = new ArrayList<>();
        barcodeExpandVOList.add(barcodeExpandVO1);
        barcodeExpandVOList.add(barcodeExpandVO2);
        ServiceData serviceData=new ServiceData();
        serviceData.setBo(barcodeExpandVOList);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));

        BarcodeExpandQueryDTO barcodeExpandQueryDTO = new BarcodeExpandQueryDTO();
        List<String> barcodeList = new ArrayList<>();
        String a = "22";
        String b = "33";
        barcodeList.add(a);
        barcodeList.add(b);
        barcodeExpandQueryDTO.setParentCategoryCode("11");
        barcodeExpandQueryDTO.setBarcodeList(barcodeList);
        Assert.assertNotNull(barcodeCenterServiceImpl.barcodeQuery(barcodeExpandQueryDTO));
    }

    @Test
    public void testBarcodeQuery1()throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, BarcodeCenterRemoteService.class,BasicsettingRemoteService.class,HttpRemoteUtil.class,HttpRemoteService.class);
        Assert.assertNotNull(barcodeCenterServiceImpl.barcodeQuery(null));
    }

    @Test
    public void testBarcodeQuery2()throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, BarcodeCenterRemoteService.class,BasicsettingRemoteService.class,HttpRemoteUtil.class,HttpRemoteService.class);
        BarcodeExpandQueryDTO barcodeExpandQueryDTO = new BarcodeExpandQueryDTO();
        barcodeExpandQueryDTO.setParentCategoryCode("11");
        Assert.assertNotNull(barcodeCenterServiceImpl.barcodeQuery(barcodeExpandQueryDTO));
    }

    @Test
    public void testBarcodeQuery3()throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, BarcodeCenterRemoteService.class,BasicsettingRemoteService.class,HttpRemoteUtil.class,HttpRemoteService.class);
        SysLookupValues sysLookupTypesDTOUrl=new SysLookupValues();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(any())).thenReturn(sysLookupTypesDTOUrl);
        BarcodeExpandVO barcodeExpandVO1=new BarcodeExpandVO();
        barcodeExpandVO1.setCategoryName("111");
        BarcodeExpandVO barcodeExpandVO2 = new BarcodeExpandVO();
        barcodeExpandVO2.setCategoryName("222");
        List<BarcodeExpandVO> barcodeExpandVOList = new ArrayList<>();
        ServiceData serviceData=new ServiceData();
        serviceData.setBo(barcodeExpandVOList);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));

        BarcodeExpandQueryDTO barcodeExpandQueryDTO = new BarcodeExpandQueryDTO();
        List<String> barcodeList = new ArrayList<>();
        String a = "22";
        String b = "33";
        barcodeList.add(a);
        barcodeList.add(b);
        barcodeExpandQueryDTO.setParentCategoryCode("11");
        barcodeExpandQueryDTO.setBarcodeList(barcodeList);
        Assert.assertNotNull(barcodeCenterServiceImpl.barcodeQuery(barcodeExpandQueryDTO));
    }

    @Test
    public void testBarcodeQuery4()throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, BarcodeCenterRemoteService.class,BasicsettingRemoteService.class,HttpRemoteUtil.class,HttpRemoteService.class);
        SysLookupValues sysLookupTypesDTOUrl=new SysLookupValues();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(any())).thenReturn(sysLookupTypesDTOUrl);
        BarcodeExpandVO barcodeExpandVO1=new BarcodeExpandVO();
        barcodeExpandVO1.setCategoryName("111");
        BarcodeExpandVO barcodeExpandVO2 = new BarcodeExpandVO();
        barcodeExpandVO2.setCategoryName("222");
        List<BarcodeExpandVO> barcodeExpandVOList = new ArrayList<>();
        barcodeExpandVOList.add(barcodeExpandVO1);
        barcodeExpandVOList.add(barcodeExpandVO2);
        ServiceData serviceData=new ServiceData();
        serviceData.setBo(barcodeExpandVOList);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(null);

        BarcodeExpandQueryDTO barcodeExpandQueryDTO = new BarcodeExpandQueryDTO();
        List<String> barcodeList = new ArrayList<>();
        String a = "22";
        String b = "33";
        barcodeList.add(a);
        barcodeList.add(b);
        barcodeExpandQueryDTO.setParentCategoryCode("11");
        barcodeExpandQueryDTO.setBarcodeList(barcodeList);
        Assert.assertThrows(MesBusinessException.class,()->barcodeCenterServiceImpl.barcodeQuery(barcodeExpandQueryDTO));
    }

    @Test
    public void testBarcodeQuery5()throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, BarcodeCenterRemoteService.class,BasicsettingRemoteService.class,HttpRemoteUtil.class,HttpRemoteService.class);
        SysLookupValues sysLookupTypesDTOUrl=new SysLookupValues();
        sysLookupTypesDTOUrl.setLookupMeaning("http://");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(any())).thenReturn(sysLookupTypesDTOUrl);
        BarcodeExpandVO barcodeExpandVO1=new BarcodeExpandVO();
        barcodeExpandVO1.setCategoryName("111");
        BarcodeExpandVO barcodeExpandVO2 = new BarcodeExpandVO();
        barcodeExpandVO2.setCategoryName("222");
        List<BarcodeExpandVO> barcodeExpandVOList = new ArrayList<>();
        barcodeExpandVOList.add(barcodeExpandVO1);
        barcodeExpandVOList.add(barcodeExpandVO2);
        ServiceData serviceData=new ServiceData();
        serviceData.setBo(barcodeExpandVOList);
        RetCode retCode = new RetCode();
        retCode.setCode(RetCode.BUSINESSERROR_CODE);
        retCode.setMsg(RetCode.BUSINESSERROR_MSGID);
        serviceData.setCode(retCode);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(serviceData));

        BarcodeExpandQueryDTO barcodeExpandQueryDTO = new BarcodeExpandQueryDTO();
        List<String> barcodeList = new ArrayList<>();
        String a = "22";
        String b = "33";
        barcodeList.add(a);
        barcodeList.add(b);
        barcodeExpandQueryDTO.setParentCategoryCode("11");
        barcodeExpandQueryDTO.setBarcodeList(barcodeList);
        Assert.assertThrows(MesBusinessException.class,()->barcodeCenterServiceImpl.barcodeQuery(barcodeExpandQueryDTO));
    }

    /*Started by AICoder, pid:wa3fd1d91alea8214fcb086b2022038961611dec*/

    @Test(timeout = 8000, expected = MesBusinessException.class)
    public void testCallBarcodeCenterWithEmptyLookupValues() throws MesBusinessException {
        List<SysLookupValues> lookupValues = Collections.emptyList();
        Mockito.when(sysLookupValuesService.selectValuesByType(Constant.LOOKUP_TYPE_1004052))
                .thenReturn(lookupValues);
        barcodeCenterRemoteServiceInject.callBarcodeCenter(1, "params");
    }

    @Test(timeout = 8000)
    public void testCallBarcodeCenterWithNonEmptyLookupValues() throws MesBusinessException {
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(),any(),any(),any())).thenReturn("2");
        List<SysLookupValues> lookupValues = new ArrayList<>();
        lookupValues.add(new SysLookupValues(){{setLookupCode(BigDecimal.ONE);setLookupMeaning("2");}});
        Mockito.when(sysLookupValuesService.selectValuesByType(Constant.LOOKUP_TYPE_1004052))
                .thenReturn(lookupValues);
        String result = barcodeCenterRemoteServiceInject.callBarcodeCenter(1, "params");
        assertNotNull(result);
    }
    /*Ended by AICoder, pid:wa3fd1d91alea8214fcb086b2022038961611dec*/



}