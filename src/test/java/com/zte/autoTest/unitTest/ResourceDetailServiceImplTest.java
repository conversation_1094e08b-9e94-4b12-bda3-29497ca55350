package com.zte.autoTest.unitTest;

import com.zte.application.impl.ResourceDetailServiceImpl;
import com.zte.domain.model.ResourceDetail;
import com.zte.domain.model.ResourceDetailRepository;
import com.zte.interfaces.dto.ResourceDetailEntityDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * @Author: panXu
 * @Date: 2021/3/8 19:36
 * @Description:
 */
@RunWith(PowerMockRunner.class)
public class ResourceDetailServiceImplTest  extends BaseTestCase {


    @Mock
    private ResourceDetailRepository resourceDetailrepository;

    @InjectMocks
    private ResourceDetailServiceImpl resourceDetailService;

    @Mock
    private ThreadPoolTaskExecutor taskExecutor;

    @Test
    public void pageList() throws Exception
    {
        ResourceDetailEntityDTO record = new ResourceDetailEntityDTO();
        Page<ResourceDetailEntityDTO> pageInfo = resourceDetailService.pageList(record);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }

    @Test
    public void save() throws Exception
    {
        ResourceDetailEntityDTO record = new ResourceDetailEntityDTO();
        Assert.assertNotNull(resourceDetailService.save(record));
    }

    @Test
    public void update() throws Exception
    {
        ResourceDetailEntityDTO record = new ResourceDetailEntityDTO();
        Assert.assertNotNull(resourceDetailService.save(record));
    }

    @Test
    public void batchInsert() throws Exception
    {
        List<ResourceDetailEntityDTO> recordList = new ArrayList<>();
        ResourceDetailEntityDTO entityDTO = new ResourceDetailEntityDTO();
        entityDTO.setResourceId(UUID.randomUUID().toString());
        recordList.add(entityDTO);
        Assert.assertNotNull(resourceDetailService.batchInsert(recordList));
    }

    @Test
    public void insertRecord() throws Exception
    {
        List<ResourceDetailEntityDTO> child = new ArrayList<>();
        ResourceDetailEntityDTO entityDTO = new ResourceDetailEntityDTO();
        child.add(entityDTO);
        PowerMockito.when(resourceDetailrepository.insertRecord(Mockito.any())).thenReturn(1);
        resourceDetailService.insertRecord(child , "1234");
        Assert.assertNotNull(child);
    }

    @Test
    public void searchRecord() {
        Assert.assertNotNull(resourceDetailService.searchRecord());
    }

    @Test
    public void testUpdateBatchOfStatus() {
        PowerMockito.doReturn(1).when(resourceDetailrepository).updateBatchOfStatus(Mockito.any());
        resourceDetailService.updateBatchOfStatus(new ResourceDetail());
        ResourceDetail detail = new ResourceDetail();
        Assert.assertNotNull(detail);
    }

    @Test
    public void batchInsertDetailInfo() throws Exception {
        List<ResourceDetailEntityDTO> detailsList = new ArrayList() {{
           add(new ResourceDetailEntityDTO() {{
               setResourceId("123456");
           }});
        }};
        Assert.assertNotNull(detailsList);
        resourceDetailService.batchInsertDetailInfo(detailsList);
    }
}
