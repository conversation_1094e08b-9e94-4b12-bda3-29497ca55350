package com.zte.autoTest.unitTest;

import com.zte.application.AsynCompensationCenterService;
import com.zte.application.BsItemInfoService;
import com.zte.application.PrintRecordService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.impl.SspMaterialPrintServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.SocketUtils;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.PrintRecord;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.interfaces.dto.BarcodeExpandVO;
import com.zte.interfaces.dto.SspPrintDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Matchers.any;

/**
 * <AUTHOR>
 * @date 2022/2/15
 */
@PrepareForTest({CommonUtils.class, SocketUtils.class})
public class SspMaterialPrintServiceImplTest extends BaseTestCase {
    @InjectMocks
    private SspMaterialPrintServiceImpl service;

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    private BsItemInfoService bsItemInfoService;

    @Mock
    private AsynCompensationCenterService asynCompensationCenterService;

    @Mock
    private PrintRecordService printRecordService;

    @Test
    public void supplementPrint() throws Exception {
        SysLookupValues sysLookupValues=new SysLookupValues();
        sysLookupValues.setLookupMeaning("1");
        sysLookupValues.setAttribute1("printer 300DPL");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(sysLookupValues);
        SspPrintDTO sspPrintDTO=new SspPrintDTO();
        sspPrintDTO.setSn("*********");
        sspPrintDTO.setFirstPrint(Constant.FLAG_N);
        sspPrintDTO.setItemCode("itemCode");
        sspPrintDTO.setIp("ip");
        sspPrintDTO.setPrintType("1");
        sspPrintDTO.setTemplateName("templateName");
        sspPrintDTO.setLeadFlag("HS");
        sspPrintDTO.setPrintCount(1);
        sspPrintDTO.setPrintNum(2);

        BsItemInfo bsItemInfo=new BsItemInfo();
        PowerMockito.when(bsItemInfoService.selectBsItemInfoById(any())).thenReturn(bsItemInfo);
        List<SysLookupValues> sysLookupValueList =new ArrayList<>();
        sysLookupValueList.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesService.selectValuesByType(any())).thenReturn(sysLookupValueList);
        List<String> barCodeList =new ArrayList<>();
        barCodeList.add("*********");
        PowerMockito.when(barcodeCenterRemoteService.barcodeGenerate(any())).thenReturn(barCodeList);
        List<PrintRecord> printRecordList= new ArrayList<>();
        PrintRecord printRecord =new PrintRecord();
        printRecord.setSn("*********");
        printRecordList.add(printRecord);
        PowerMockito.when(printRecordService.getListBySns(any())).thenReturn(printRecordList);


        Whitebox.invokeMethod(service,"print",sspPrintDTO);
        Assert.assertNotNull(sspPrintDTO);
    }

    @Test
    public void getTemplateNameList() throws Exception {
        SysLookupValues sysLookupValues=new SysLookupValues();
        sysLookupValues.setLookupMeaning("1");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(sysLookupValues);
        Assert.assertNotNull(Whitebox.invokeMethod(service,"getTemplateNameList"));

    }
    @Test
    public void print() throws Exception {
        SysLookupValues sysLookupValues=new SysLookupValues();
        sysLookupValues.setLookupMeaning("1");
        sysLookupValues.setAttribute1("printer 300DPL");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(sysLookupValues);
        SspPrintDTO sspPrintDTO=new SspPrintDTO();
        sspPrintDTO.setFirstPrint(Constant.FLAG_Y);
        sspPrintDTO.setItemCode("itemCode");
        sspPrintDTO.setIp("ip");
        sspPrintDTO.setPrintType("1");
        sspPrintDTO.setTemplateName("templateName");
        sspPrintDTO.setLeadFlag("HS");
        sspPrintDTO.setPrintCount(1);
        sspPrintDTO.setPrintNum(2);

        BsItemInfo bsItemInfo=new BsItemInfo();
        PowerMockito.when(bsItemInfoService.selectBsItemInfoById(any())).thenReturn(bsItemInfo);
        List<SysLookupValues> sysLookupValueList =new ArrayList<>();
        sysLookupValueList.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesService.selectValuesByType(any())).thenReturn(sysLookupValueList);
        List<String> barCodeList =new ArrayList<>();
        barCodeList.add("*********");
        PowerMockito.when(barcodeCenterRemoteService.barcodeGenerate(any())).thenReturn(barCodeList);

        Whitebox.invokeMethod(service,"print",sspPrintDTO);
        Assert.assertNotNull(sspPrintDTO);
    }

    @Test
    public void notImesPrint() throws Exception {
        SspPrintDTO sspPrintDTO = new SspPrintDTO();
        sspPrintDTO.setIp("*************");
        sspPrintDTO.setPrintCount(1);
        sspPrintDTO.setPrinter("0");
        sspPrintDTO.setSn("1123");
        PowerMockito.when(printRecordService.getListBySns(any())).thenReturn(new ArrayList<>());

        // 第二次调用条码中心查询列表
        List<BarcodeExpandVO> barcodeExpandVOList = new ArrayList<>();
        BarcodeExpandVO barcodeExpandVO = new BarcodeExpandVO();
        barcodeExpandVO.setBarcode("abc1123");
        barcodeExpandVO.setItemCode("1231231");
        barcodeExpandVO.setIsLead("无铅");
        barcodeExpandVOList.add(barcodeExpandVO);

        // 记录表空测试 且 未输入打印模板
        Assertions.assertThrows(MesBusinessException.class, () -> {
            service.print(sspPrintDTO);
        }, MessageId.DOZEN_SN_NOT_EXIST_PRINT_DATA);

        // 设置 根据打印类型获取数据字典配置的打印机名称
        List<SysLookupValues> sysLookupValueList = new ArrayList<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("0");
        sysLookupValues.setAttribute1("printer 300DPL");
        sysLookupValueList.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesService.selectValuesByType(any())).thenReturn(sysLookupValueList);


        // 条码中心 第一次调用返回空列表 第二次非空
        PowerMockito.when(barcodeCenterRemoteService.getBarcodeFromBarcodeCenterList(any())).thenReturn(new ArrayList<>()).thenReturn(barcodeExpandVOList);
        // 分支 打印份数
        sspPrintDTO.setTemplateName("SSP物料标签打印模板2.btw");

        // 条码中心为空测试
        Assertions.assertThrows(MesBusinessException.class, () -> {
            service.print(sspPrintDTO);
        }, MessageId.BAR_CODE_CENTER_IS_NULL);

        // 条码心不为空正常测试
        Assertions.assertDoesNotThrow(() -> {
            service.print(sspPrintDTO);
        });

        // 打印次数分支为空测试
        sspPrintDTO.setPrintCount(null);
        Assertions.assertDoesNotThrow(() -> {
            service.print(sspPrintDTO);
        });
    }

}
