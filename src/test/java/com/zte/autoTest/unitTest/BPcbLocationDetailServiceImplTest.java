package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.BsAsyncDataService;
import com.zte.application.BsPremanuItemInfoService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.impl.BPcbLocationDetailServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.interfaces.dto.BPcbLocationDetailDTO;
import com.zte.interfaces.dto.BsAsyncDataDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.util.Pair;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import static org.mockito.Matchers.any;


/**
 * @Author:
 * @Date: 2020/10/21 15:16
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class BPcbLocationDetailServiceImplTest extends BaseTestCase {

    @InjectMocks
    private BPcbLocationDetailServiceImpl service;

    @Mock
    private BPcbLocationDetailRepository repository;

    @Mock
    private BBomHeaderRepository bBomHeaderRepository;
    @Mock
    private BsAsyncDataService bsAsyncDataService;
    @Mock
    private BsPremanuItemInfoService bsPremanuItemInfoService;
    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private MultipartFile file;
    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Test
    public void uploadCadFileForCloudDiskTest() throws Exception {
        // 模拟输入参数
        BPcbLocationDetailDTO dto = new BPcbLocationDetailDTO();
        dto.setFile(file);
        dto.setCreateUser("emp123");
        dto.setProductCode("productABC");

        // 情境 1: 上传成功
        PowerMockito.when(cloudDiskHelper.fileUpload(file, dto.getCreateUser())).thenReturn("fileId123");

        // 执行测试
        service.uploadCadFileForCloudDisk(dto, true);

        // 情境 2: 上传失败，抛出异常
        PowerMockito.doThrow(new Exception()).when(cloudDiskHelper).fileUpload(file, dto.getCreateUser());

        // 期望抛出异常
        Assert.assertThrows(MesBusinessException.class, () -> {
            service.uploadCadFileForCloudDisk(dto, true);
        });

        // 情境 3: 不上传文件
        service.uploadCadFileForCloudDisk(dto, false);

        PowerMockito.when(bBomHeaderRepository.getList(any())).thenReturn(null);
        try {
            service.downloadCadFile("1212", "12121");
        } catch (Exception e){
        }

        List<BBomHeader> list = new ArrayList<>();
        BBomHeader bBomHeader = new BBomHeader();
        bBomHeader.setFileName("1213");
        list.add(bBomHeader);
        PowerMockito.when(bBomHeaderRepository.getList(any())).thenReturn(list);
        PowerMockito.when(cloudDiskHelper.getFileDownloadUrl(any(), any(), any())).thenReturn("fileUrl");
        service.downloadCadFile("1212", "12121");

    }

    @Test
    public void getByProductCodes() {
        BPcbLocationDetailDTO dto = new BPcbLocationDetailDTO();
        dto.setProductCode("code111");
        List<BPcbLocationDetailDTO> list = new ArrayList<>();
        list.add(dto);
        List<String> productCodes = new ArrayList<>();
        productCodes.add("code111");
        PowerMockito.when(repository.getByProductCodes(any())).thenReturn(list);
        service.getByProductCodes(productCodes);
        Assert.assertNotNull(dto);
    }

    @Mock
    private RedisLock redisLock;

    @Before
    public void init(){
        PowerMockito.mockStatic(CommonUtils.class);
    }

    @Test
    @PrepareForTest({BPcbLocationDetailServiceImpl.class,CommonUtils.class})
    public void batchInsertFromBom() {
        try {
            BPcbLocationDetailDTO dto = new BPcbLocationDetailDTO();
            dto.setProductCode("aa");
            List<BBomHeader> list = new ArrayList<>();
            BBomHeader bBomHeader = new BBomHeader();
            list.add(bBomHeader);
            PowerMockito.when(bBomHeaderRepository.selectBBomHeaderNotCal(any())).thenReturn(list);
            PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
            PowerMockito.when(redisLock.lock()).thenReturn(true);
            service.batchInsertFromBom(dto, 10);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void uploadCadFile() {
        try {
            service.uploadCadFile(new BPcbLocationDetailDTO(){{setFile(new MockMultipartFile("1", new byte[1]));}});
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void deleteCadDetailsByProductCode() {
        List<String> productCodeList = new ArrayList<>();
        service.deleteCadDetailsByProductCode(productCodeList);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void asyncQueryCadExpireResult() {
        Assert.assertNotNull(service.asyncQueryCadExpireResult(Mockito.any()));
    }

    @Test
    public void selectBPcbLocationDetailByProductCode() {
        List<BPcbLocationDetail> list = new ArrayList<>();
        String productCode = "123";
        PowerMockito.when(repository.selectBPcbLocationDetailByProductCode(Mockito.any())).thenReturn(list);
        service.selectBPcbLocationDetailByProductCode(productCode);
        Assert.assertNotNull(list);
    }

    @Test
    public void cancelCadExpireResult() throws Exception {
        Assert.assertNotNull(service.cancelCadExpireResult(new BsAsyncDataDTO(), Pair.of("1", "2")));
    }

    @Test
    public void confirmCadExpireResult() throws Exception {
        List<BsAsyncDataDTO> bsAsyncDataDTOS = new LinkedList<>();
        BsAsyncDataDTO b1 =  new BsAsyncDataDTO();
        bsAsyncDataDTOS.add(b1);
        PowerMockito.when(bsAsyncDataService.queryAsyncByCondition(Mockito.any())).thenReturn(bsAsyncDataDTOS);
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.any())).thenReturn(new ArrayList<>());
        try{
            service.confirmCadExpireResult(new BsAsyncDataDTO(), Pair.of("1", "2"));
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        List<SysLookupValues> sysList = new ArrayList<>();
        SysLookupValues sysLookupValue = new SysLookupValues();
        sysLookupValue.setLookupMeaning("Y");
        sysLookupValue.setLookupCode(new BigDecimal("652307001"));
        sysList.add(sysLookupValue);
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.any())).thenReturn(sysList);
        try{
            service.confirmCadExpireResult(new BsAsyncDataDTO(), Pair.of("1", "2"));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        sysLookupValue.setLookupMeaning("N");
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.any())).thenReturn(sysList);
        try{
            service.confirmCadExpireResult(new BsAsyncDataDTO(), Pair.of("1", "2"));
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void isWriteProcess() {
        Assert.assertTrue(service.isWriteProcess(new BPcbLocationDetailDTO(),
                Lists.newArrayList(), new BsAsyncDataDTO(),
                Lists.newArrayList(new BPcbLocationDetail(){{setItemCode("1");setPointLoc("1");}})));
    }

    @Test
    public void getPointLocPage(){
        BPcbLocationDetailDTO dto = new BPcbLocationDetailDTO();
        List<String> pointLoc = new ArrayList<String>(){{add("D1");add("D2");}};
        PowerMockito.when(repository.getPointLocPage(Mockito.any())).thenReturn(pointLoc);
        Assert.assertNotNull(service.getPointLocPage(dto));
    }
}
