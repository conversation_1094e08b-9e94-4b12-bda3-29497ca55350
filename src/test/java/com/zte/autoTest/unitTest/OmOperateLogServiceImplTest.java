package com.zte.autoTest.unitTest;

import com.zte.application.HrmUserCenterService;
import com.zte.application.impl.OmOperateLogServiceImpl;
import com.zte.domain.model.OmOperateLog;
import com.zte.domain.model.OmOperateLogRepository;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.OmOperateLogDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023-10-07 16:46
 */
public class OmOperateLogServiceImplTest extends BaseTestCase {
    @InjectMocks
    private OmOperateLogServiceImpl omOperateLogServiceImpl;
    @Mock
    private HrmUserCenterService hrmUserCenterService;
    @Mock
    private OmOperateLogRepository omOperateLogRepository;
    @Before
    public void init(){

    }

    @Test
    public void getPageList() throws Exception {
        OmOperateLogDTO dto = new OmOperateLogDTO();
        omOperateLogServiceImpl.getPageList(dto);

        List<OmOperateLog> pageList = new LinkedList<>();
        OmOperateLog a1 = new OmOperateLog();
        a1.setCreateBy("123");
        pageList.add(a1);
        omOperateLogServiceImpl.getPageList(dto);

        OmOperateLog a2 = new OmOperateLog();
        a2.setCreateBy("1234");
        pageList.add(a2);
        Map<String, HrmPersonInfoDTO> hrmPersonInfo = new HashMap<>();
        HrmPersonInfoDTO b1 = new HrmPersonInfoDTO();
        hrmPersonInfo.put("1234", b1);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.any()))
                .thenReturn(hrmPersonInfo);
        PowerMockito.when(omOperateLogRepository.getPageList(Mockito.any())).thenReturn(pageList);
        Assert.assertNotNull(omOperateLogServiceImpl.getPageList(dto));
    }
}
