package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.common.ConstantInterface;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.LinkedList;

import static org.mockito.Matchers.any;

/**
 * @Author: 10307315
 * @Date: 2021/10/9 下午5:45
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpRemoteService.class,MESHttpHelper.class,ConstantInterface.class,HttpClientUtil.class,JSON.class,ServiceDataBuilderUtil.class,MicroServiceRestUtil.class})
public class BasicsettingRemoteServiceTest extends BaseTestCase {
    @Mock
    private BasicsettingRemoteService basicsettingRemoteService;

    @Test
    public void getsysLookupTypeListTest() throws Exception{
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelective(any(), any(), any(), any(), any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(new ServiceData(){{setBo(Lists.newArrayList("1"));}})));
		Assert.assertNotNull(BasicsettingRemoteService.getsysLookupTypeList("1245"));
    }

	@Test
	public void getLookUpValuesBatch() throws Exception {
		PowerMockito.mockStatic(MESHttpHelper.class);
		PowerMockito.mockStatic(ConstantInterface.class);
		PowerMockito.mockStatic(HttpClientUtil.class);
		PowerMockito.mockStatic(JSON.class);
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class);

		Assert.assertNotNull(BasicsettingRemoteService.getLookUpValuesBatch(new LinkedList<>()));
	}

}
