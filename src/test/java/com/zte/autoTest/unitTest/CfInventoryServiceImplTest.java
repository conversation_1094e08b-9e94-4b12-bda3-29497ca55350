package com.zte.autoTest.unitTest;
import com.google.common.collect.Lists;
import com.zte.application.impl.CfInventoryServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.domain.model.CfCheckHistory;
import com.zte.domain.model.CfCheckHistoryRepository;
import com.zte.domain.model.CfInventory;
import com.zte.domain.model.CfInventoryRepository;
import com.zte.interfaces.dto.CfInventoryDTO;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * @Title: 物料接收及盘点
 * @Description:
 * @date 2020/11/18
 */
@PrepareForTest({CfInventoryServiceImpl.class,CfInventoryRepository.class,
        CfCheckHistoryRepository.class,CommonUtils.class, RetCode.class})
public class CfInventoryServiceImplTest extends BaseTestCase {
    @InjectMocks
    private CfInventoryServiceImpl cfInventoryServiceImpl;

    @Mock
    private CfInventoryRepository cfInventoryRepository;

    @Mock
    private CfCheckHistoryRepository cfCheckHistoryRepository;
    @Mock
    private RetCode retCode;

    @Before
    public  void  init() throws Exception{
        PowerMockito.mockStatic(RetCode.class);
        PowerMockito.whenNew(RetCode.class).withAnyArguments().thenReturn(retCode);
    }

    @Test
    public void itemInventoryTest() throws Exception{
        PowerMockito.mockStatic(CommonUtils.class);
        CfInventoryDTO inventoryDTO = new CfInventoryDTO()
                .setId(UUID.randomUUID().toString().replaceAll("-", ""))
                .setTaskNo("ZTE484257793")
                .setItemNo("ZTE-WL625982639")
                .setItemName("物料名称")
                .setDemandNum(new BigDecimal(4394))
                .setSentNum(new BigDecimal(4000))
                .setUnit("箱")
                .setAcceptedNum(new BigDecimal(2000))
                .setAcceptedBy("占威")
                .setAcceptedDate(new Date())
                .setCheckNum(new BigDecimal(2000))
                .setCheckBy("占威")
                .setCheckDate(new Date())
                .setCheckDiff(new BigDecimal(0))
                .setCheckRemark("物料备注")
                .setOrgId(new BigDecimal(2))
                .setFactoryId(new BigDecimal(75))
                .setEntityId(new BigDecimal(2))
                .setEnabledFlag("Y")
                .setCreateBy("高航000")
                .setCheckDate(new Date())
                .setLastUpdatedBy("高航00")
                .setLastUpdatedDate(new Date())
                .setSupplierNo("85658950")
                .setTimeStatus("1");
        Map<String, Object> map = CommonUtils.transBean2Map(inventoryDTO);
        List<CfInventory> cfInventories = new ArrayList<>();
        //物料接收库存数据查询
        PowerMockito.when(cfInventoryRepository.getList(map)).thenReturn(cfInventories);
        PowerMockito.when(cfInventoryRepository.getCount(map)).thenReturn(1L);
        PowerMockito.when(cfInventoryRepository.getPsTaskByFactoryId("ZTE484257793")).thenReturn("75");
        PowerMockito.when(CommonUtils.transBean2Map(inventoryDTO)).thenReturn(null);;
        //盘点历史数据查询
        CfCheckHistory cfCheckHistory = new CfCheckHistory();
        BeanUtils.copyProperties(inventoryDTO,cfCheckHistory);
        List<CfCheckHistory> histories = Lists.newArrayList(cfCheckHistory);
        cfCheckHistoryRepository.insertCfCheckHistory(histories);
        cfInventoryServiceImpl.getCfInventoryPage(null,inventoryDTO);
        cfInventoryServiceImpl.getList(inventoryDTO);
        cfInventoryServiceImpl.getCount(CommonUtils.transBean2Map(inventoryDTO));
        cfInventoryServiceImpl.mergeIntoCfInventory(Lists.newArrayList(inventoryDTO));
        cfInventoryServiceImpl.verifyInventory(Lists.newArrayList(inventoryDTO));
        cfInventoryServiceImpl.findAllInventoryData(null,inventoryDTO);
        Assert.assertNotNull(inventoryDTO);
    }

    @Test
    public void getCombinationData() throws  Exception{
        HashMap<String, Object> map = new HashMap<>();
        map.put("taskNo","ZZ河源A1020200731002");
        map.put("factoryId","52");
        map.put("itemNo","056471200155");
        map.put("itemName","线扣(室内) | 300×4.8//300×4. 8");
        map.put("requiredQuantity","50");
        map.put("quantityIssued","50");
        map.put("primaryUnitOfMeasure","50");
        List<Object> list = new ArrayList<>();
        list.add(map);
        map.put("list",list);
        CfInventoryDTO inventoryDTO = new CfInventoryDTO();
        CfInventory inventory = new CfInventory();
        inventory.setTaskNo("ZZ河源A1020200731002");
        inventoryDTO.setTaskNo("ZZ河源A1020200731002");
        List<CfInventory> inventories = new ArrayList<>();
        inventories.add(inventory);
        PowerMockito.when(cfInventoryRepository.getCombinationData(map)).thenReturn(inventories);
        Assert.assertNotNull(cfInventoryServiceImpl.getCombinationData("52",list,inventoryDTO));
    }
    @Test
    public void getCfCheckHistoryList() throws  Exception{
        CfInventoryDTO dto = new CfInventoryDTO();
        dto.setTaskNo("ZZ河源A1020200731002");
        dto.setSentNum(new BigDecimal(0));
        List<CfInventoryDTO> cfInventoryDTOS = new ArrayList<>();
        cfInventoryDTOS.add(dto);
        Assert.assertNotNull(Whitebox.invokeMethod(cfInventoryServiceImpl, "getCfCheckHistoryList", "52",cfInventoryDTOS));
    }
}
