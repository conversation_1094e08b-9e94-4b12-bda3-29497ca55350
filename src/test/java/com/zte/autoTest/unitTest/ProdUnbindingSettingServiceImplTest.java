package com.zte.autoTest.unitTest;

import com.zte.application.impl.ProdUnbindingSettingServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.ProdUnbindingSetting;
import com.zte.domain.model.ProdUnbindingSettingRepository;
import com.zte.interfaces.dto.ProdUnbindingSettingDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.Matchers.any;

@RunWith(PowerMockRunner.class)
public class ProdUnbindingSettingServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ProdUnbindingSettingServiceImpl service;

    @Mock
    private ProdUnbindingSettingRepository repository;

    @Test
    public void getPageList() {
        Page<ProdUnbindingSetting> page = service.getPageList(new ProdUnbindingSettingDTO() {{setPageNum(1); setPageSize(1);}});
        Assert.assertTrue(page.getRows().size() >= 0);
    }

    @Test
    public void addUnbindingSetting() throws MesBusinessException {
        PowerMockito.when(repository.getUnbindingCount(any())).thenReturn(0);
        service.addUnbindingSetting(new ProdUnbindingSetting());
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void getCanSetItemCode() {
        Assert.assertNotNull(service.getCanSetItemCode("1", "1"));
    }

    @Test
    public void deleteUnbindingSetting() {
        service.deleteUnbindingSetting(new ProdUnbindingSettingDTO());
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }
}