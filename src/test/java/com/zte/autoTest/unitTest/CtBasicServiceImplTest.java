package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.craftTech.CraftSequenceService;
import com.zte.application.craftTech.CtRouteHeadService;
import com.zte.application.impl.craftTech.CtBasicServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.craftTech.CtBasic;
import com.zte.domain.model.craftTech.CtBasicRepository;
import com.zte.domain.model.craftTech.CtRouteDetailRepository;
import com.zte.interfaces.dto.CtBasicDTO;
import com.zte.interfaces.dto.CtBasicVersionUpdateDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.CtRouteInfoDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.FactoryConfig;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyMap;
import static org.mockito.ArgumentMatchers.anyObject;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class CtBasicServiceImplTest extends BaseTestCase {

    @InjectMocks
    private CtBasicServiceImpl service;

    @Mock
    private CtBasicRepository repository;

    @Mock
    private CtRouteHeadService ctRouteHeadService;

    @Mock
    private CtRouteDetailRepository ctRouteDetailRepository;

    @Mock
    private FactoryConfig factoryConfig;

    @Mock
    private CraftSequenceService craftSequenceService;

    @Test
    public void insertCtBasic() throws Exception {

        service.insertCtBasic(new CtBasic());
        verify(repository, times(1)).insertCtBasic(anyObject());
    }

    @Test
    public void insertCtBasicSelective() throws Exception {
        CtBasic record = new CtBasic();
        Assert.assertNotNull(service.insertCtBasicSelective(record));
    }

    @Test
    public void commitCtBasic() throws Exception {
        CtBasic ctBasic = new CtBasic();
        ctBasic.setCraftId("craftId");
        try{
            service.commitCtBasic(ctBasic);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
    }

    @Test
    public void selectCtBasicById() throws Exception {
        CtBasic ctBasic = new CtBasic();
        ctBasic.setCraftId("craftId");
        service.selectCtBasicById(ctBasic);
        verify(repository, times(1)).selectCtBasicById(anyObject());
    }


    @Test
    public void versionUpdate() throws Exception {

        CtBasicVersionUpdateDTO dto = new CtBasicVersionUpdateDTO();
        dto.setCraftId("craftId");

        CtBasicDTO ctBasicDTO = new CtBasicDTO();
        ctBasicDTO.setCraftVersion("V.AB");
        when(repository.selectCtBasicById(anyObject())).thenReturn(ctBasicDTO);


        List<CtRouteInfoDTO> ctRouteInfo = Lists.newArrayList();
        for (int i = 0; i < 2; i++) {
            CtRouteInfoDTO ctRouteInfoDTO = new CtRouteInfoDTO();
            ctRouteInfoDTO.setRouteId(String.valueOf(i));

            List<CtRouteDetailDTO> listDetail = Lists.newArrayList();
            listDetail.add(new CtRouteDetailDTO());
            ctRouteInfoDTO.setListDetail(listDetail);
            ctRouteInfo.add(ctRouteInfoDTO);
        }
        when(ctRouteHeadService.getBatchRouteHeadAndDetail(anyMap())).thenReturn(ctRouteInfo);
        service.versionUpdate(dto);
        verify(repository, times(1)).insertCtBasicSelective(anyObject());
        verify(ctRouteHeadService, times(2)).insertCtRouteHeadSelective(anyObject());
        verify(ctRouteDetailRepository, times(2)).batchInsertRouteDetail(anyList());
    }

    @Test
    public void getNextVersion() throws Exception {

        String returnVersion = "";
        String retVer = service.getNextVersion(returnVersion);
        Assert.assertEquals(retVer, "");

        returnVersion = "V.ZZ";
        retVer = service.getNextVersion(returnVersion);
        Assert.assertEquals(retVer, "V.AAA");

        returnVersion = "V.Z";
        retVer = service.getNextVersion(returnVersion);
        Assert.assertEquals(retVer, "V.AA");

        returnVersion = "V.B";
        retVer = service.getNextVersion(returnVersion);
        Assert.assertEquals(retVer, "V.C");

        returnVersion = "V.ZA";
        retVer = service.getNextVersion(returnVersion);
        Assert.assertEquals(retVer, "V.ZB");

        Assert.assertNotNull(service.getNextVersion(returnVersion));

    }

    @Test
    public void checkUnCommitVersion() {
        PowerMockito.when(repository.getUnCommitVersionCount(any())).thenReturn(1);
        try {
            service.checkUnCommitVersion(new CtBasicDTO());
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.EXISTING_UNCOMMIT_CRAFT, e.getMessage());
        }
    }
}
