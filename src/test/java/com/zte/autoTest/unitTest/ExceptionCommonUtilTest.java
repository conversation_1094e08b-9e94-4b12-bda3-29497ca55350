package com.zte.autoTest.unitTest;

import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.itp.msa.core.model.RetCode;
import com.zte.springbootframe.common.exception.GlobalDefaultExceptionHandler;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.ExceptionCommonUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

/**
 * <AUTHOR>
 * @date 2023-07-13 9:38
 */
@PrepareForTest({GlobalDefaultExceptionHandler.class, CommonUtils.class,ExceptionCommonUtil.class})
public class ExceptionCommonUtilTest extends BaseTestCase {

    @Before
    public void init(){
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.mockStatic(GlobalDefaultExceptionHandler.class);
        PowerMockito.mockStatic(ExceptionCommonUtil.class);
    }

    @Test
    public void getString(){
        PowerMockito.mockStatic(ExceptionCommonUtil.class);
        MesBusinessException e = new MesBusinessException(RetCode.BUSINESSERROR_CODE, MessageId.CUSTOMIZE_MSG, new Object[]{"55"});
        ExceptionCommonUtil.getExceptionMsg(e);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }
}
