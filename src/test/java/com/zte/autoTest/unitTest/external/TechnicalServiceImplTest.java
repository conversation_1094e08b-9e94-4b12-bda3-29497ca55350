package com.zte.autoTest.unitTest.external;

import com.alibaba.fastjson.JSON;
import com.zte.application.impl.external.TechnicalServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.domain.model.sncabind.PsTaskRepository;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-02-02 19:49
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({JSON.class, HttpClientUtil.class, ServiceDataBuilderUtil.class})
public class TechnicalServiceImplTest {
    @InjectMocks
    private TechnicalServiceImpl technicalServiceImpl;
    @Mock
    private PsTaskRepository psTaskRepository;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
    }

    @Test
    public void checkTechnicalInfo() throws Exception {
        try {
            technicalServiceImpl.checkTechnicalInfo("");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NULL, e.getMessage());
        }
        try {
            technicalServiceImpl.checkTechnicalInfo("2");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_RULE_PARAMS_ERROR, e.getMessage());
        }

        try {
            technicalServiceImpl.checkTechnicalInfo("889975800005");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TASK_DATA_ERROR, e.getMessage());
        }

        List<PsTask> psTasks = new LinkedList<>();
        PsTask a1 = new PsTask();
        a1.setProdplanId("88997580");
        a1.setFactoryId(new BigDecimal(51));
        psTasks.add(a1);
        PowerMockito.when(psTaskRepository.selectPsTaskByProdIdSet(Mockito.anyObject()))
                .thenReturn(psTasks);
        try {
            technicalServiceImpl.checkTechnicalInfo("889975800005");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.FACTORY_ID_OF_CENTER_PSTASK_IS_NULL, e.getMessage());
        }
        a1.setFactoryId(new BigDecimal("52"));
        try {
            technicalServiceImpl.checkTechnicalInfo("889975800005");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }

        List<SysLookupValues> factoryList = new LinkedList<>();
        SysLookupValues b1 = new SysLookupValues();
        b1.setLookupMeaning("52");
        b1.setAttribute2("http://imes.zte.com.cn");
        factoryList.add(b1);
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.anyObject()))
                .thenReturn(factoryList)
        ;
        technicalServiceImpl.checkTechnicalInfo("889975800005");

        //
        technicalServiceImpl.checkTechnicalSn("889975800005");
    }

    @Test
    public  void checkTechnicalSn(){
        List<PsTask> psTasks = new LinkedList<>();
        PsTask a1 = new PsTask();
        a1.setProdplanId("88997580");
        a1.setFactoryId(new BigDecimal(51));
        psTasks.add(a1);
        PowerMockito.when(psTaskRepository.selectPsTaskByProdIdSet(Mockito.any()))
                .thenReturn(psTasks);
        a1.setFactoryId(new BigDecimal("52"));
        List<SysLookupValues> factoryList = new LinkedList<>();
        SysLookupValues b1 = new SysLookupValues();
        b1.setLookupMeaning("52");
        b1.setAttribute2("http://imesDE.zte.com.cn");
        factoryList.add(b1);
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.any()))
                .thenReturn(factoryList);
        //
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any()))
                .thenReturn("1234,566778");
        Assert.assertNotNull(technicalServiceImpl.checkTechnicalSn("889975800005"));
    }

}
