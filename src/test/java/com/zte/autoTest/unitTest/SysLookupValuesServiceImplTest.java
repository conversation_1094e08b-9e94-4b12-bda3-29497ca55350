package com.zte.autoTest.unitTest;

import com.zte.application.IMESLogService;
import com.zte.application.impl.SysLookupValuesServiceImpl;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.anyObject;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

public class SysLookupValuesServiceImplTest extends BaseTestCase {

    @InjectMocks
    private SysLookupValuesServiceImpl sysLookupValuesServiceImpl;

    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private IMESLogService imesLogService;



    @Test
    public void findByLookupCode() throws Exception {
        Assert.assertNull(sysLookupValuesServiceImpl.findByLookupCode(1));
    }
    @Test
    public void insertSysLookupValues() throws Exception {
        sysLookupValuesServiceImpl.insertSysLookupValues(new SysLookupValues());
        verify(sysLookupValuesRepository,times(1))
                .insertSysLookupValues(anyObject());
    }

    @Test
    public void insertSysLookupValuesSelective() throws Exception {
        sysLookupValuesServiceImpl.insertSysLookupValuesSelective(new SysLookupValues());
        verify(sysLookupValuesRepository,times(1))
                .insertSysLookupValuesSelective(anyObject());
    }

    @Test
    public void deleteSysLookupValuesById() throws Exception {
        SysLookupValues sysParam = new SysLookupValues();
        sysParam.setLookupCode(new BigDecimal("123"));
        PowerMockito.when(sysLookupValuesRepository.selectSysLookupValuesById(Mockito.any())).thenReturn(sysParam);
        sysLookupValuesServiceImpl.deleteSysLookupValuesById(new SysLookupValues());
        verify(sysLookupValuesRepository,times(1))
                .deleteSysLookupValuesById(anyObject());
    }

    @Test
    public void updateSysLookupValuesByIdSelective() throws Exception {
        SysLookupValues sysParam = new SysLookupValues();
        sysParam.setLookupCode(new BigDecimal("123"));
        PowerMockito.when(sysLookupValuesRepository.selectSysLookupValuesById(Mockito.any())).thenReturn(sysParam);
        sysLookupValuesServiceImpl.updateSysLookupValuesByIdSelective(new SysLookupValues());
        verify(sysLookupValuesRepository,times(1))
                .updateSysLookupValuesByIdSelective(anyObject());
    }

    @Test
    public void updateSysLookupValuesById() throws Exception {
        SysLookupValues sysParam = new SysLookupValues();
        sysParam.setLookupCode(new BigDecimal("123"));
        PowerMockito.when(sysLookupValuesRepository.selectSysLookupValuesById(Mockito.any())).thenReturn(sysParam);
        sysLookupValuesServiceImpl.updateSysLookupValuesById(new SysLookupValues());
        verify(sysLookupValuesRepository,times(1))
                .updateSysLookupValuesById(anyObject());
    }

    @Test
    public void selectSysLookupValuesById() throws Exception {
        sysLookupValuesServiceImpl.selectSysLookupValuesById(new SysLookupValues());
        verify(sysLookupValuesRepository,times(1))
                .selectSysLookupValuesById(anyObject());
    }

    @Test
    public void getList() throws Exception {
        Map<String, Object> record = new HashMap<>();
        String orderField = null ;
        String order = "order";
        sysLookupValuesServiceImpl.getList(record,orderField,order);
        verify(sysLookupValuesRepository,times(1)).getList(record);
    }

    @Test
    public void getValuesByLang() throws Exception {
        Map<String, Object> record = new HashMap<>();
        String orderField = null ;
        String order = "order";
        String langType = "en_US";
        
        List<SysLookupValues> list = new ArrayList<>();
        SysLookupValues sysLookupValue = new SysLookupValues();
        sysLookupValue.setLookupMeaning("长沙工厂");
        sysLookupValue.setDescriptionEng("CS FACTORY");
        list.add(sysLookupValue);
        PowerMockito.when(sysLookupValuesRepository.getList(anyObject())).thenReturn(list);
        
        sysLookupValuesServiceImpl.getValuesByLang(record,orderField,order,langType);
        verify(sysLookupValuesRepository,times(1)).getList(record);
    }

    @Test
    public void getAddressByFactoryIdTest() throws Exception {
        String id = "52";
        List<SysLookupValues> list = new ArrayList<>();
        SysLookupValues sysLookupValue = new SysLookupValues();
        sysLookupValue.setLookupMeaning("52");
        sysLookupValue.setAttribute2("cs.http");
        list.add(sysLookupValue);
        PowerMockito.when(sysLookupValuesRepository.getList(anyObject())).thenReturn(list);
        Assert.assertNotNull(sysLookupValuesServiceImpl.getAddressByFactoryId(id));
    }
    @Test
    public void getCount() throws Exception {
        Map<String, Object> record = new HashMap<>();
        String orderField = null ;
        String order = "order";
        sysLookupValuesServiceImpl.getCount(record,orderField,order);
        Mockito.verify(sysLookupValuesRepository,times(1)).getCount(record);
    }

	@Test
	public void getLookUpValuesBatch() {
		List<String> lookUpTypeList = new LinkedList<>();
		lookUpTypeList.add("123");
		List<SysLookupValues> resultList = new LinkedList<>();
		SysLookupValues a1 = new SysLookupValues();
		resultList.add(a1);
		PowerMockito.when(sysLookupValuesRepository.selectByTypeBatch(Mockito.anyList()))
				.thenReturn(resultList);
		try{
			sysLookupValuesServiceImpl.getLookUpValuesBatch(lookUpTypeList);
		} catch (Exception e){
			Assert.assertNull(e.getMessage());
		}
	}

	@Test
	public void getLookUpValuesBatch2() {
		List<String> lookUpTypeList = new LinkedList<>();
		lookUpTypeList.add("123");
		PowerMockito.when(sysLookupValuesRepository.selectByTypeBatch(Mockito.anyList()))
				.thenReturn(null);
		try{
			sysLookupValuesServiceImpl.getLookUpValuesBatch(lookUpTypeList);
		} catch (Exception e){
			Assert.assertNull(e.getMessage());
		}
	}

    @Test
    public void getPage() throws Exception {
        Map<String, Object> record = new HashMap<>();
        String orderField = null ;
        String order = "";
        Long page = 1l ;
        Long rows = 10l ;
        sysLookupValuesServiceImpl.getPage(record,orderField,order,page,rows);
        Mockito.verify(sysLookupValuesRepository,times(1)).getPage(record);
    }
}