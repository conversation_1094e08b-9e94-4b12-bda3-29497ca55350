package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.BomCraftAttributeService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.impl.HrmUserCenterServiceImpl;
import com.zte.application.impl.craftTech.CtRouteHeadServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.domain.model.craftTech.*;
import com.zte.domain.model.craftTech.CtRouteDetail;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Mockito.*;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class,RedisHelper.class,BasicsettingRemoteService.class,ServiceDataBuilderUtil.class, MicroServiceRestUtil.class})
public class CtRouteHeadServiceImplTest extends BaseTestCase {

    @InjectMocks
    private CtRouteHeadServiceImpl service;

    @Mock
    private CtRouteHeadRepository ctRouteHeadRepository;

    @Mock
    private CtRouteDetailRepository ctRouteDetailRepository;

    @Mock
    private CtBasicRepository ctBasicRepository;

    @Mock
    private BsProcessRepository bSProcessRepository;

	@Mock
	EmailUtils emailUtils;

	@Mock
	private SysLookupValuesService sysLookupValuesService;

	@Mock
	private HrmUserCenterServiceImpl hrmUserCenterService;
	@Mock
	private BomCraftAttributeService bomCraftAttributeService;
	@Mock
	private BsPremanuBomInfoRepository bsPremanuBomInfoRepository;
	@Mock
	private CadUploadRecordRepository cadRepository;
	@Mock
	private BBomHeaderRepository bBomHeaderRepository;

	@Test
    public void verifyProductionTypeAndProcessType() throws Exception {
        List<CtRouteHead> ctRouteHeadList = new ArrayList<>();
        CtRouteHead ctRouteHead = new CtRouteHead();
        ctRouteHeadList.add(ctRouteHead);
        PowerMockito.when(ctRouteHeadRepository.getHeadByCraftIds(any())).thenReturn(ctRouteHeadList);
        List<BatchRouteDTO> listEntity = Lists.newArrayList();
        BatchRouteDTO batchRouteDTO = new BatchRouteDTO();
        listEntity.add(batchRouteDTO);
        try{
            Whitebox.invokeMethod(service,"verifyProductionTypeAndProcessType",listEntity,0,"craftId","headId");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.THE_PROCESS_PATH_OF_THE_MAINTAINED_PROCESS_TYPE,e.getExMsgId());
        }
        ctRouteHead.setProcessType("主板");
        ctRouteHead.setProductType("量产");
        try{
            Whitebox.invokeMethod(service,"verifyProductionTypeAndProcessType",listEntity,0,"craftId","headId");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.PRODUCTION_TYPE_ALREADY_EXISTS,e.getExMsgId());
        }
    }
    @Test
    public void insertCtRouteHeadSelective() throws Exception {

        service.insertCtRouteHeadSelective(new CtRouteHead());
        verify(ctRouteHeadRepository, times(1)).insertCtRouteHeadSelective(anyObject());
    }

    @Test
    public void batchInsertRoute_condition_A() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(Mockito.anyString(),Mockito.anyString(),Mockito.anyInt())).thenReturn(true);

        CtRouteHeadServiceImpl serivce = PowerMockito.spy(new CtRouteHeadServiceImpl());
        List<BatchRouteDTO> listEntity = Lists.newArrayList();
        BatchRouteDTO batchRouteDTO = new BatchRouteDTO();
        listEntity.add(batchRouteDTO);
        PowerMockito.mockStatic(CommonUtils.class);
        when(ctBasicRepository.getList(anyMap())).thenReturn(null);

        doReturn(null).when(serivce).batchInsertRouteReturnMessage(listEntity,new ArrayList<>(),1);

        Assert.assertNull(service.batchInsertRoute(listEntity));

    }

    @Test
//    @PrepareForTest({CommonUtils.class,CtRouteHeadServiceImpl.class})
    public void batchInsertRoute_condition_B() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(Mockito.anyString(),Mockito.anyString(),Mockito.anyInt())).thenReturn(true);
        CtRouteHeadServiceImpl serivce = PowerMockito.spy(new CtRouteHeadServiceImpl());
        List<BatchRouteDTO> listEntity = Lists.newArrayList();
        BatchRouteDTO batchRouteDTO = new BatchRouteDTO();
        batchRouteDTO.setRouteId("111");
        List<BSProcessDTO> bsProcessDTOList = Lists.newArrayList();
        BSProcessDTO bsProcessDTO = new BSProcessDTO();
        bsProcessDTO.setProcessCode("P0002");
        bsProcessDTOList.add(bsProcessDTO);
        batchRouteDTO.setListProcess(bsProcessDTOList);
        PowerMockito.mockStatic(CommonUtils.class);
        listEntity.add(batchRouteDTO);

        List<CtBasicDTO> ctBasicList = Lists.newArrayList();
        CtBasicDTO ctBasicDTO = new CtBasicDTO();
        ctBasicDTO.setCraftSection("单板装配");
        ctBasicList.add(ctBasicDTO);
        when(ctBasicRepository.getList(anyMap())).thenReturn(ctBasicList);
        doReturn(null).when(serivce).batchInsertRouteReturnMessage(listEntity,new ArrayList<>(),1);
        Assert.assertNull(service.batchInsertRoute(listEntity));
    }

    @Test
//    @PrepareForTest({CommonUtils.class,CtRouteHeadServiceImpl.class})
    public void batchInsertRoute_condition_C() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(Mockito.anyString(),Mockito.anyString(),Mockito.anyInt())).thenReturn(true);
        CtRouteHeadServiceImpl serivce = PowerMockito.spy(new CtRouteHeadServiceImpl());
        List<BatchRouteDTO> listEntity = Lists.newArrayList();
        BatchRouteDTO batchRouteDTO = new BatchRouteDTO();
        batchRouteDTO.setRouteId("111");
        List<BSProcessDTO> bsProcessDTOList = Lists.newArrayList();
        BSProcessDTO bsProcessDTO = new BSProcessDTO();
        bsProcessDTO.setProcessCode("P0001");
        bsProcessDTOList.add(bsProcessDTO);
        batchRouteDTO.setListProcess(bsProcessDTOList);
        PowerMockito.mockStatic(CommonUtils.class);
        listEntity.add(batchRouteDTO);

        List<CtBasicDTO> ctBasicList = Lists.newArrayList();
        CtBasicDTO ctBasicDTO = new CtBasicDTO();
        ctBasicDTO.setCraftSection("单板测试");
        ctBasicList.add(ctBasicDTO);
        when(ctBasicRepository.getList(anyMap())).thenReturn(ctBasicList);
        doReturn(null).when(serivce).batchInsertRouteReturnMessage(listEntity,new ArrayList<>(),1);
        Assert.assertNull(service.batchInsertRoute(listEntity));
    }

    @Test
    public void batchInsertRoute_condition_D() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(Mockito.anyString(),Mockito.anyString(),Mockito.anyInt())).thenReturn(true);
        List<BatchRouteDTO> listEntity = Lists.newArrayList();
        BatchRouteDTO batchRouteDTO = new BatchRouteDTO();
        batchRouteDTO.setRouteId("");
        List<BSProcessDTO> bsProcessDTOList = Lists.newArrayList();
        BSProcessDTO bsProcessDTO = new BSProcessDTO();
        bsProcessDTO.setProcessCode("P0001");
        bsProcessDTOList.add(bsProcessDTO);
        batchRouteDTO.setListProcess(bsProcessDTOList);

        listEntity.add(batchRouteDTO);

        List<CtBasicDTO> ctBasicList = Lists.newArrayList();
        CtBasicDTO ctBasicDTO = new CtBasicDTO();
        ctBasicDTO.setCraftSection("单板装配");
        ctBasicList.add(ctBasicDTO);
        when(ctBasicRepository.getList(anyMap())).thenReturn(ctBasicList);

        when(ctRouteHeadRepository.insertCtRouteHeadSelective(anyObject())).thenReturn(1);

        Assert.assertNotNull(service.batchInsertRoute(listEntity));
    }

    @Test
//    @PrepareForTest({CommonUtils.class})
    public void batchInsertRoute_condition_E() throws Exception {
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(Mockito.anyString(),Mockito.anyString(),Mockito.anyInt())).thenReturn(true);
        List<BatchRouteDTO> listEntity = Lists.newArrayList();
        BatchRouteDTO batchRouteDTO = new BatchRouteDTO();
        batchRouteDTO.setRouteId("111");
        List<BSProcessDTO> bsProcessDTOList = Lists.newArrayList();
        BSProcessDTO bsProcessDTO = new BSProcessDTO();
        bsProcessDTO.setProcessCode("P0001");
        bsProcessDTOList.add(bsProcessDTO);
        bsProcessDTOList.add(new BSProcessDTO());
        batchRouteDTO.setListProcess(bsProcessDTOList);

        listEntity.add(batchRouteDTO);

        List<CtBasicDTO> ctBasicList = Lists.newArrayList();
        CtBasicDTO ctBasicDTO = new CtBasicDTO();
        ctBasicDTO.setCraftSection("单板装配");
        ctBasicList.add(ctBasicDTO);
        when(ctBasicRepository.getList(anyMap())).thenReturn(ctBasicList);

        when(ctRouteHeadRepository.updateCtRouteHeadByIdSelective(anyObject())).thenReturn(1);

		service.batchInsertRoute(listEntity);
        verify(ctRouteDetailRepository, times(1)).deleteDetailByRouteIdPhysical(anyObject());
    }

	@Test
	public void processIntoRouteDetail() throws Exception {
		String headId = "test";
		String empNo = "00286569";
		BigDecimal factoryId = new BigDecimal(52);
		BigDecimal entityId = new BigDecimal(2);

		List<BSProcessDTO> listProcess = new ArrayList<>();
		BSProcessDTO bsProcessDTO = new BSProcessDTO();
		bsProcessDTO.setProcessCode("test1");
		bsProcessDTO.setRedoRules(new String[]{"1"});
		bsProcessDTO.setWorkTime(new BigDecimal(20001));

		try{
			listProcess.add(bsProcessDTO);
			Whitebox.invokeMethod(service,"processIntoRouteDetail",listProcess,headId,empNo,factoryId,entityId);
		}catch(Exception e){
			Assert.assertEquals(MessageId.WORK_TIME_ERROR, e.getMessage());
		}
		bsProcessDTO.setWorkTime(new BigDecimal(0));
		try{
			listProcess.add(bsProcessDTO);
			Whitebox.invokeMethod(service,"processIntoRouteDetail",listProcess,headId,empNo,factoryId,entityId);
		}catch(Exception e){
			Assert.assertEquals(MessageId.WORK_TIME_ERROR, e.getMessage());
		}
		bsProcessDTO.setWorkTime(new BigDecimal(20.3));
		try{
			listProcess.add(bsProcessDTO);
			Whitebox.invokeMethod(service,"processIntoRouteDetail",listProcess,headId,empNo,factoryId,entityId);
		}catch(Exception e){
			Assert.assertEquals(MessageId.WORK_TIME_ERROR, e.getMessage());
		}
		bsProcessDTO.setWorkTime(new BigDecimal(2));
		bsProcessDTO.setRemainTime(new BigDecimal(20001));
		try{
			listProcess.add(bsProcessDTO);
			Whitebox.invokeMethod(service,"processIntoRouteDetail",listProcess,headId,empNo,factoryId,entityId);
		}catch(Exception e){
			Assert.assertEquals(MessageId.REMAIN_TIME_ERROR, e.getMessage());
		}
		bsProcessDTO.setRemainTime(new BigDecimal(0));
		try{
			listProcess.add(bsProcessDTO);
			Whitebox.invokeMethod(service,"processIntoRouteDetail",listProcess,headId,empNo,factoryId,entityId);
		}catch(Exception e){
			Assert.assertEquals(MessageId.REMAIN_TIME_ERROR, e.getMessage());
		}
		bsProcessDTO.setRemainTime(new BigDecimal(20.3));
		try{
			listProcess.add(bsProcessDTO);
			Whitebox.invokeMethod(service,"processIntoRouteDetail",listProcess,headId,empNo,factoryId,entityId);
		}catch(Exception e){
			Assert.assertEquals(MessageId.REMAIN_TIME_ERROR, e.getMessage());
		}
	}

	@Test
	public void processIntoRouteDetailTwo() throws Exception {
		String headId = "test";
		String empNo = "00286569";
		BigDecimal factoryId = new BigDecimal(52);
		BigDecimal entityId = new BigDecimal(2);

		List<BSProcessDTO> listProcess = new ArrayList<>();
		BSProcessDTO bsProcessDTO = new BSProcessDTO();
		bsProcessDTO.setProcessCode("test1");
		bsProcessDTO.setRedoRules(new String[]{"1"});
		bsProcessDTO.setWorkTime(new BigDecimal(1));

		BSProcessDTO bsProcessDTO1 = new BSProcessDTO();
		bsProcessDTO1.setProcessCode("test2");
		bsProcessDTO1.setRedoRules(new String[]{"1","2"});
		bsProcessDTO1.setRemainTime(new BigDecimal(2));

		BSProcessDTO bsProcessDTO2 = new BSProcessDTO();
		bsProcessDTO2.setProcessCode("test3");
		bsProcessDTO2.setRedoRules(new String[]{"2"});
		bsProcessDTO2.setWorkTime(new BigDecimal(2));
		bsProcessDTO2.setRemainTime(new BigDecimal(2));

		BSProcessDTO bsProcessDTO3 = new BSProcessDTO();
		bsProcessDTO3.setProcessCode("test4");
		bsProcessDTO3.setRedoRules(new String[]{"1","2"});

		BSProcessDTO bsProcessDTO4 = new BSProcessDTO();
		bsProcessDTO4.setProcessCode("N");

		listProcess.add(bsProcessDTO);
		listProcess.add(bsProcessDTO1);
		listProcess.add(bsProcessDTO2);
		listProcess.add(bsProcessDTO3);
		listProcess.add(bsProcessDTO4);

		try{
			Whitebox.invokeMethod(service,"processIntoRouteDetail",listProcess,headId,empNo,factoryId,entityId);
		}catch(Exception e){
			Assert.assertNull(e.getMessage());
		}
	}

	@Test
	public void getListRouteBatch() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		List<SysLookupValues> lookUpValuesBatch = new LinkedList<>();
		SysLookupValues c1 = new SysLookupValues();
		c1.setLookupMeaning("test");
		c1.setAttribute1("1");
		c1.setLookupType(new BigDecimal("6908"));
		lookUpValuesBatch.add(c1);
		SysLookupValues c2 = new SysLookupValues();
		c2.setLookupMeaning("test1");
		c2.setAttribute1("1");
		c2.setLookupType(new BigDecimal("6908"));
		lookUpValuesBatch.add(c2);
		SysLookupValues C3 = new SysLookupValues();
		C3.setLookupMeaning("test2");
		C3.setAttribute1("2");
		C3.setLookupType(new BigDecimal("6909"));
		lookUpValuesBatch.add(C3);
		SysLookupValues C4 = new SysLookupValues();
		C4.setLookupMeaning("test2");
		C4.setAttribute1("2");
		C4.setLookupType(new BigDecimal("6912"));
		lookUpValuesBatch.add(C4);
		PowerMockito.when(sysLookupValuesService.getLookUpValuesBatch(Mockito.any())).thenReturn(lookUpValuesBatch);

		List<BSProcessDTO> listProcess = new ArrayList<>();

		BSProcessDTO bsProcessDTO = new BSProcessDTO();
		bsProcessDTO.setNextProcess("test1");
		bsProcessDTO.setRedoRules(new String[]{"1"});
		bsProcessDTO.setWorkTime(new BigDecimal(1));

		BSProcessDTO bsProcessDTO1 = new BSProcessDTO();
		bsProcessDTO1.setNextProcess("test2");
		bsProcessDTO1.setRedoRules(new String[]{"1","2"});
		bsProcessDTO1.setRemainTime(new BigDecimal(2));

		BSProcessDTO bsProcessDTO2 = new BSProcessDTO();
		bsProcessDTO2.setNextProcess("test3");
		bsProcessDTO2.setRedoRules(new String[]{"2"});
		bsProcessDTO2.setWorkTime(new BigDecimal(2));
		bsProcessDTO2.setRemainTime(new BigDecimal(2));

		BSProcessDTO bsProcessDTO3 = new BSProcessDTO();
		bsProcessDTO3.setNextProcess("N");

		listProcess.add(bsProcessDTO);
		listProcess.add(bsProcessDTO1);
		listProcess.add(bsProcessDTO2);
		listProcess.add(bsProcessDTO3);

		List<BSProcess> listProcess1 = new ArrayList<>();
		BSProcess bsProcess = new BSProcess();
		bsProcess.setProcessCode("test");
		BSProcess bsProcess1 = new BSProcess();
		bsProcess1.setProcessCode("test1");
		BSProcess bsProcess2 = new BSProcess();
		bsProcess2.setProcessCode("test2");
		BSProcess bsProcess3 = new BSProcess();
		bsProcess3.setProcessCode("test3");
		listProcess1.add(bsProcess);
		listProcess1.add(bsProcess1);
		listProcess1.add(bsProcess2);
		listProcess1.add(bsProcess3);
		PowerMockito.when(ctRouteDetailRepository.getProcessList(anyMap())).thenReturn(listProcess);
		PowerMockito.when(bSProcessRepository.getList(anyMap())).thenReturn(listProcess1);
		List<BatchRouteDTO> listRoute = new ArrayList<>();
		BatchRouteDTO batchRouteDTO = new BatchRouteDTO();
		batchRouteDTO.setRouteId("test");
		listRoute.add(batchRouteDTO);
		try{
			Whitebox.invokeMethod(service,"getListRouteBatch",listRoute);
		}catch(Exception e){
			Assert.assertNull(e.getMessage());
		}
        PowerMockito.when(sysLookupValuesService.getLookUpValuesBatch(Mockito.any())).thenReturn(new ArrayList<>());
        try{
            Whitebox.invokeMethod(service,"getListRouteBatch",listRoute);
        }catch(Exception e){
            Assert.assertNull(e.getMessage());
        }
	}

	@Test
	public void getListRouteBatchTwo() throws Exception {
		PowerMockito.mockStatic(BasicsettingRemoteService.class);
		List<BatchRouteDTO> listRoute = new ArrayList<>();
		BatchRouteDTO batchRouteDTO = new BatchRouteDTO();
		batchRouteDTO.setRouteId("test");
		listRoute.add(batchRouteDTO);
		List<SysLookupValues> lookUpValuesBatch = new LinkedList<>();
		PowerMockito.when(sysLookupValuesService.getLookUpValuesBatch(Mockito.any())).thenReturn(lookUpValuesBatch);

		try{
			Whitebox.invokeMethod(service,"getListRouteBatch",listRoute);
		}catch(Exception e){
			Assert.assertNull(e.getMessage());
		}
	}


    @Test
    public void updateCtRouteHeadByIdSelective() throws Exception {

        service.updateCtRouteHeadByIdSelective(new CtRouteHead());
        verify(ctRouteHeadRepository, times(1)).updateCtRouteHeadByIdSelective(anyObject());
    }


    @Test
    public void getBatchRoute_A() throws Exception {

        Map<String, Object> record = new HashMap<>();
        record.put("startRow", "1");
        record.put("endRow", "1");

        List<CtRouteHeadDTO> listHead = Lists.newArrayList();
        listHead.add(new CtRouteHeadDTO());

        when(ctRouteHeadRepository.getPage(anyMap())).thenReturn(listHead);

        List<BSProcessDTO> listProcess = Lists.newArrayList();
        BSProcessDTO bsProcessDTO = new BSProcessDTO();
        bsProcessDTO.setProcessCode("111");
        listProcess.add(bsProcessDTO);
        when(ctRouteDetailRepository.getProcessList(anyMap())).thenReturn(listProcess);

        List<BSProcess> list = Lists.newArrayList();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessCode("111");
        list.add(bsProcess);
        when(bSProcessRepository.getList(anyMap())).thenReturn(list);

        List<BatchRouteDTO> listRoute = service.getBatchRoute(record);
        Assert.assertNotNull(listRoute);
        Assert.assertEquals(listRoute.size(), 1);
    }

    @Test
    public void getBatchRoute_B() throws Exception {
		PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
		PowerMockito.mockStatic(MicroServiceRestUtil.class);
        Map<String, Object> record = new HashMap<>();

        List<CtRouteHeadDTO> listHead = Lists.newArrayList();
        listHead.add(new CtRouteHeadDTO());

        when(ctRouteHeadRepository.getList(anyMap())).thenReturn(listHead);

        List<BSProcessDTO> listProcess = Lists.newArrayList();
        BSProcessDTO bsProcessDTO = new BSProcessDTO();
        bsProcessDTO.setProcessCode("111");
        listProcess.add(bsProcessDTO);
        when(ctRouteDetailRepository.getProcessList(anyMap())).thenReturn(listProcess);

        List<BSProcess> list = Lists.newArrayList();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessCode("111");
        list.add(bsProcess);
        when(bSProcessRepository.getList(anyMap())).thenReturn(list);

        List<BatchRouteDTO> listRoute = service.getBatchRoute(record);
        Assert.assertNotNull(listRoute);
        Assert.assertEquals(listRoute.size(), 1);

        record.put("itemNo","xtcs009");
        List<BatchRouteDTO> listRoute1 = service.getBatchRoute(record);
        Assert.assertTrue(listRoute1.size()>0);

        record.put(Constant.CRAFT_ID, "1");
        service.getBatchRoute(record);

        record.put(Constant.ROUTE_ID, "1");
        service.getBatchRoute(record);
    }

    @Test
    public void getBatchRouteHeadAndDetail_A() throws Exception {

        Map<String, Object> record = new HashMap<>();
        record.put("startRow", "1");
        record.put("endRow", "1");

        List<CtRouteHeadDTO> listHead = Lists.newArrayList();
        listHead.add(new CtRouteHeadDTO());
        when(ctRouteHeadRepository.getPage(anyMap())).thenReturn(listHead);

        List<CtRouteDetail> listDetail = Lists.newArrayList();
        listDetail.add(new CtRouteDetail());
        when(ctRouteDetailRepository.getList(anyMap())).thenReturn(listDetail);

        Assert.assertNotNull(service.getBatchRouteHeadAndDetail(record));
    }

    @Test
    public void getBatchRouteHeadAndDetail_B() throws Exception {

        Map<String, Object> record = new HashMap<>();

        List<CtRouteHeadDTO> listHead = Lists.newArrayList();
        listHead.add(new CtRouteHeadDTO());
        when(ctRouteHeadRepository.getList(anyMap())).thenReturn(listHead);

        List<CtRouteDetail> listDetail = Lists.newArrayList();
        listDetail.add(new CtRouteDetail());
        when(ctRouteDetailRepository.getList(anyMap())).thenReturn(listDetail);

        Assert.assertNotNull(service.getBatchRouteHeadAndDetail(record));
    }

    @Test
    public void getCount() throws Exception {

        Map<String, Object> record = new HashMap<>();
        service.getCount(record);
        verify(ctRouteHeadRepository, times(1)).getCount(anyMap());
    }

    @Test
    public void insertHeadByCheck() throws MesBusinessException {
        service.insertHeadByCheck(new CtRouteHead());
        PowerMockito.when(ctRouteHeadRepository.getCountByCraftId(any())).thenReturn(1);
        try {
            service.insertHeadByCheck(new CtRouteHead());
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.THIS_VERSION_CRAFT_EXISTS, e.getMessage());
        }
    }

    @Test
    public void getSameItemLastRoute() {
        Assert.assertNull(service.getSameItemLastRoute(new BatchRouteDTO()));
    }

    /**
     * 单元测试
     */
	@Test
	public void checkPrepareStatusOfItemNo() throws Exception {
		List<BomCraftAttribute> getCraftAttrList = new ArrayList<>();
		PowerMockito.when(bomCraftAttributeService.getBomFurnaceTempList(any())).thenReturn(getCraftAttrList);
		List<BBomHeader> bBomHeaderList = new ArrayList<>();
		PowerMockito.when(bBomHeaderRepository.getList(any())).thenReturn(bBomHeaderList);
		List<BsPremanuBomInfo> preBomDetails = new ArrayList<>();
		PowerMockito.when(bsPremanuBomInfoRepository.selectBsPreBomDetailsByBomCode(any())).thenReturn(preBomDetails);
		Assert.assertNotNull(service.checkPrepareStatusOfItemNo("test"));
	}

    /**
     * 单元测试
     */
	@Test
	public void checkPrepareStatusOfItemNoTwo() throws Exception {
		List<BomCraftAttribute> getCraftAttrList = new ArrayList<>();
		BomCraftAttribute bomCraftAttribute = new BomCraftAttribute();
		bomCraftAttribute.setBomNo("test");
		getCraftAttrList.add(bomCraftAttribute);
		PowerMockito.when(bomCraftAttributeService.getBomFurnaceTempList(any())).thenReturn(getCraftAttrList);

		List<BBomHeader> bBomHeaderList = new ArrayList<>();
		BBomHeader bomHeader = new BBomHeader();
		bomHeader.setProductCode("test");
		bomHeader.setImportedCadFlag("Y");
		bBomHeaderList.add(bomHeader);
		PowerMockito.when(bBomHeaderRepository.getList(any())).thenReturn(bBomHeaderList);
		List<BsPremanuBomInfo> preBomDetails = new ArrayList<>();
		BsPremanuBomInfo bsPremanuBomInfo = new BsPremanuBomInfo();
		bsPremanuBomInfo.setItemNo("test");
		preBomDetails.add(bsPremanuBomInfo);
		PowerMockito.when(bsPremanuBomInfoRepository.selectBsPreBomDetailsByBomCode(any())).thenReturn(preBomDetails);
		Assert.assertNotNull(service.checkPrepareStatusOfItemNo("test"));
	}

	@Test
	public void sendRouteEmail() throws Exception {
		CtRouteQueryDTO dto = new CtRouteQueryDTO();
		dto.setRouteDetail("test");
		dto.setCraftVersion("V.A");
		dto.setEmailSender("10275508");
		dto.setProcessType("主板");
		dto.setCraftName("test");
		dto.setItemNo("test");

		Map<String, HrmPersonInfoDTO> hrmPersonInfoDTOMap = new HashMap<>();
		HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
		hrmPersonInfoDTO.setEmpName("lzh");
		hrmPersonInfoDTO.setId("00286569");
		hrmPersonInfoDTOMap.put("00286569",hrmPersonInfoDTO);

		PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.anyList())).thenReturn(hrmPersonInfoDTOMap);
		PowerMockito.when(emailUtils.sendMail(anyString(), anyString(), anyString(), anyString(), anyString())).thenReturn(true);
		service.sendRouteEmail(dto,"00286569");
        Assert.assertNotNull(dto);
    }
}
