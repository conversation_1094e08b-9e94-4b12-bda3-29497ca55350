package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.SysLookupValuesService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.BarcodeCenterTemplatePrintDTO;
import com.zte.interfaces.dto.GenBarcodeParamDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Matchers.any;

/**
 *
 * @Author:
 * @Date: 2020/10/22 11:29
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class, HttpRemoteUtil.class, HttpRemoteService.class})
public class BarcodeCenterRemoteServiceTest extends BaseTestCase {

	@InjectMocks
	private BarcodeCenterRemoteService service;
	@Mock
	private SysLookupValuesService sysLookupValuesService;

	@Before
	public void init() {
		List<SysLookupValues> list = getSysLookupValues();
		PowerMockito.when(sysLookupValuesService.selectValuesByType(any())).thenReturn(list);
	}

	@Test
	public void setXEmpNo() throws Exception {
		BarcodeCenterTemplatePrintDTO barcodeCenterTemplatePrintDTO=new BarcodeCenterTemplatePrintDTO();
		service.setXEmpNo(barcodeCenterTemplatePrintDTO,new HashMap<>());
		Assert.assertNotNull(barcodeCenterTemplatePrintDTO);
	}

	private List<SysLookupValues> getSysLookupValues() {
		return Lists.newArrayList(new SysLookupValues () {{
			setLookupCode(new BigDecimal(1004052001)); setLookupMeaning("http://dev.ibarcode.zte.com.cn/zte-iss-barcodecenter-barcode");
		}},new SysLookupValues () {{
			setLookupCode(new BigDecimal(1004052002)); setLookupMeaning("10001");
		}},new SysLookupValues () {{
			setLookupCode(new BigDecimal(1004052003)); setLookupMeaning("A0509777019518562304");
		}},new SysLookupValues () {{
			setLookupCode(new BigDecimal(1004052004)); setLookupMeaning("9648B76C92DBA3BDAE028233271D80E0A6B10F416714E40C2FBCFF5FF48BF678");
		}},new SysLookupValues () {{
			setLookupCode(new BigDecimal(1004052005)); setLookupMeaning("/barcode/update");
		}},new SysLookupValues () {{
			setLookupCode(new BigDecimal(1004052006)); setLookupMeaning("/barcode/expandQuery");
		}},new SysLookupValues () {{
			setLookupCode(new BigDecimal(1004052007)); setLookupMeaning("/barcode/barcodeQuery");
		}},new SysLookupValues () {{
			setLookupCode(new BigDecimal(1004052008)); setLookupMeaning("/barcode/register");
		}});
	}

	@Test
	public void barcodeUpdate() throws Exception {
		PowerMockito.mockStatic(HttpRemoteUtil.class);
		PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
				.thenReturn(JSON.toJSONString(new ServiceData()));
		Assert.assertNotNull(service.barcodeUpdate(Lists.newArrayList(new PkCodeInfo())));
	}
	
	@Test
	public void blankGenerate() throws Exception {
		List<String> reelIds = new ArrayList<>();
		reelIds.add("ZTE00111");
		ServiceData rt = new ServiceData();
		rt.setBo(reelIds);
		PowerMockito.mockStatic(HttpRemoteUtil.class);
		PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
				.thenReturn(JSON.toJSONString(rt));
		Assert.assertNotNull(service.blankGenerate(1, "1" , "1"));
	}

	@Test
	public void barcodeGenerate() throws Exception {
		List<String> reelIds = new ArrayList<>();
		reelIds.add("ZTE00111");
		ServiceData rt = new ServiceData();
		rt.setBo(reelIds);
		PowerMockito.mockStatic(HttpRemoteUtil.class);
		PowerMockito.when(HttpRemoteUtil.remoteExeFoExternal(anyString(), any(), any(), any()))
				.thenReturn(JSON.toJSONString(rt));
		GenBarcodeParamDTO genBarcodeParamDTO = new GenBarcodeParamDTO();
		Assert.assertNotNull(service.barcodeGenerate(genBarcodeParamDTO));
	}
}
