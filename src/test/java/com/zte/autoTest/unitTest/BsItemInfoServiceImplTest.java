package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.impl.BsItemInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BsItemInfo;
import com.zte.domain.model.BsItemInfoRepository;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.core.classloader.annotations.PrepareForTest;

/**
 * <AUTHOR>
 * @Date 2020/11/8 22
 * @description:
 */
@PrepareForTest({CommonUtils.class})
public class BsItemInfoServiceImplTest extends BaseTestCase {

    @InjectMocks
    private BsItemInfoServiceImpl bsItemInfoService;
    @Mock
    private BsItemInfoRepository bsItemInfoRepository;

    @Test
    public void getBsItemInfoByItemNo(){
        BsItemInfo bsItemInfo =  new BsItemInfo();
        bsItemInfo.setItemNo("'123','124'");

        Assert.assertNotNull(bsItemInfoService.getBsItemInfoByItemNo(bsItemInfo));

    }

	@Test
	public void getStyleListByList() {
		bsItemInfoService.getStyleListByList(Lists.newArrayList("1"));
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
	}

	@Test
	public void selectBsItemInfoByNo() throws Exception {
        BsItemInfo record = new BsItemInfo();
        record.setLikeItemNo("123");
        try {
            bsItemInfoService.selectBsItemInfoByNo(record);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ITEM_CODE_LENGTH_NOT_TWELVE, e.getMessage());
        }
        record.setLikeItemNo("123456789987");
        bsItemInfoService.selectBsItemInfoByNo(record);
	}
}
