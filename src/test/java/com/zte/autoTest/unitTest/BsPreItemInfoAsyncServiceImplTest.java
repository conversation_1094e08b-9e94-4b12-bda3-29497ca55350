package com.zte.autoTest.unitTest;

import com.zte.application.CommonService;
import com.zte.application.CommonTransactionalService;
import com.zte.application.impl.BsPreItemInfoAsyncServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.domain.model.*;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.ICenterRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.util.Pair;

import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.assertTrue;

/**
 * <AUTHOR>
 * @date 2021-04-01 8:24
 */
@PrepareForTest({RedisHelper.class, BasicsettingRemoteService.class,SpringContextUtil.class})
public class BsPreItemInfoAsyncServiceImplTest extends BaseTestCase {
    @Mock
    private BBomHeaderRepository bomHeaderRepository;
    @Mock
    private BBomDetailRepository bBomDetailRepository;
    @Mock
    private BsItemInfoRepository bsItemInfoRepository;
    @Mock
    private BsAsyncDataRespository bsAsyncDataRespository;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private BsPremanuBomInfoRepository bsPremanuBomInfoRepository;
    @Mock
    private BsPremanuItemInfoRepository bsPremanuItemInfoRepository;
    @Mock
    private BPcbLocationDetailRepository bPcbLocationDetailRepository;
    @InjectMocks
    private BsPreItemInfoAsyncServiceImpl bsPreItemInfoAsyncServiceImpl;
    @Mock
    private BsBomHierarchicalHeadRepository bsBomHierarchicalHeadRepository;
    @Mock
    private BsBomHierarchicalDetailRepository bsBomHierarchicalDetailRepository;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private CommonTransactionalService commonTransactionalService;
    @Mock
    private ValueOperations valueOperations;
    @Mock
    private ICenterRemoteService iCenterRemoteService;
    @Mock
    private String bomExceptionMessageTo;
    @Mock
    private LocaleMessageSourceBean lmb;
    @Mock
    private BProdBomChangeDetailRepository bProdBomChangeDetailRepository;
    @Mock
    private CommonService commonService;
    @Mock
    private BProdBomHeaderRepository bProdBomHeaderRepository;
    @Mock
    private IdGenerator idGenerator;
    /*Started by AICoder, pid:j8194o9c92b40a114cf30859d1a6572722a166c8*/

    @Before
    public void init() throws Exception{
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.when(commonService.generatedProdBomHierarchical(Mockito.any(), Mockito.any()))
                .thenReturn(null);
        PowerMockito.when(SpringContextUtil.getBean(BusinessConstant.RESOURCE_SERVICE_NAME)).thenReturn(lmb);
        PowerMockito.field(BsPreItemInfoAsyncServiceImpl.class, "bomRunWaiteMaxSecond").set(bsPreItemInfoAsyncServiceImpl, 60);
    }
    @Test
    public void testBuildErrorMsgWithEmptyBomItemList() throws Exception {
        List<String> bomCodeList = new ArrayList<>();
        Map<String, BigDecimal> bomCalMap = new HashMap<>();
        List<BBomDetailDTO> bomItemList = Collections.emptyList();
        Whitebox.invokeMethod(bsPreItemInfoAsyncServiceImpl,"buildErrorMsg",bomCodeList, "productCode", bomCalMap, bomItemList);
        assertTrue(bomCodeList.isEmpty());
    }

    @Test
    public void testBuildErrorMsgWithNullBomCalQty() throws Exception{
        List<String> bomCodeList = new ArrayList<>();
        Map<String, BigDecimal> bomCalMap = new HashMap<>();
        List<BBomDetailDTO> bomItemList = new ArrayList<>();
        BBomDetailDTO bomDetailDTO = new BBomDetailDTO();
        bomDetailDTO.setItemCode("itemCode");
        bomDetailDTO.setUsageCount(new BigDecimal("10"));
        bomItemList.add(bomDetailDTO);

        BBomDetailDTO bomDetailDTO2 = new BBomDetailDTO();
        bomDetailDTO2.setItemCode("itemCode2");
        bomDetailDTO2.setUsageCount(new BigDecimal("10"));
        bomItemList.add(bomDetailDTO2);
        bomCalMap.put("itemCode2", new BigDecimal(10));

        Whitebox.invokeMethod(bsPreItemInfoAsyncServiceImpl,"buildErrorMsg",bomCodeList, "productCode", bomCalMap, bomItemList);
        assertTrue(bomCodeList.contains("productCode"));
    }

    @Test
    public void testBuildErrorMsgWithUnequalBomCalQtyAndUsageCount() throws Exception {
        List<String> bomCodeList = new ArrayList<>();
        Map<String, BigDecimal> bomCalMap = new HashMap<>();
        List<BBomDetailDTO> bomItemList = new ArrayList<>();
        BBomDetailDTO bomDetailDTO = new BBomDetailDTO();
        bomDetailDTO.setItemCode("itemCode");
        bomDetailDTO.setUsageCount(new BigDecimal("10"));
        bomCalMap.put("itemCode", new BigDecimal("20"));
        bomItemList.add(bomDetailDTO);

        Whitebox.invokeMethod(bsPreItemInfoAsyncServiceImpl,"buildErrorMsg",bomCodeList, "productCode", bomCalMap, bomItemList);
        assertTrue(bomCodeList.contains("productCode"));

        BBomDetailDTO bomDetailDTO2 = new BBomDetailDTO();
        bomDetailDTO2.setItemCode("itemCode");
        bomDetailDTO2.setUsageCount(new BigDecimal("10"));
        bomItemList.add(bomDetailDTO2);

        PowerMockito.field(BsPreItemInfoAsyncServiceImpl.class, "bomExceptionMessageTo")
                .set(bsPreItemInfoAsyncServiceImpl, "123,123");
        Whitebox.invokeMethod(bsPreItemInfoAsyncServiceImpl,"buildErrorMsg",bomCodeList, "productCode", bomCalMap, bomItemList);
    }
    /*Ended by AICoder, pid:j8194o9c92b40a114cf30859d1a6572722a166c8*/

    /* Started by AICoder, pid:rd42cjaa0c1b1d6144c00ad00177892a20405236 */
    @Test
    public void runBomCodeAsync() throws Exception {
        Pair<String, String> pair = Pair.of("52", "123");
        List<AsyncBomDTO> list = new LinkedList<>();
        AsyncBomDTO asyncBomDTO = new AsyncBomDTO();
        asyncBomDTO.setItemNo("123");
        asyncBomDTO.setItemName("123");
        list.add(asyncBomDTO);

        PowerMockito.when(bomHeaderRepository.getHasCadProductCode(Mockito.any())).thenReturn(new LinkedList<String>() {{
            add("123");
        }});

        List<SysLookupValues> dirList = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        a1.setLookupMeaning("2");
        dirList.add(a1);
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.any())).thenReturn(dirList);

        List<BBomHeader> headerList = new LinkedList<>();
        BBomHeader b1 = new BBomHeader();
        b1.setProductCode("123");
        b1.setChiDesc("123");
        headerList.add(b1);
        PowerMockito.when(bomHeaderRepository.getList(Mockito.any())).thenReturn(headerList);

        List<BBomDetail> bBomDetails = new LinkedList<>();
        BBomDetail c1 = new BBomDetail();
        c1.setItemCode("124");
        c1.setUsageCount(new BigDecimal("24"));
        BBomDetail c2 = new BBomDetail();
        c2.setItemCode("123");
        c2.setUsageCount(new BigDecimal("24"));
        BBomDetail c3 = new BBomDetail();
        c3.setItemCode("040");
        c3.setUsageCount(new BigDecimal("24"));
        bBomDetails.add(c1);
        bBomDetails.add(c2);
        bBomDetails.add(c3);
        PowerMockito.when(bBomDetailRepository.selectDetailsByProductCode(Mockito.any())).thenReturn(bBomDetails);

        List<BsItemInfo> infoList = new LinkedList<>();
        BsItemInfo d1 = new BsItemInfo();
        d1.setItemNo("123");
        d1.setAbcType("A");
        d1.setIsSmt(new BigDecimal(1));
        infoList.add(d1);
        BsItemInfo d2 = new BsItemInfo();
        d2.setItemNo("124");
        d2.setAbcType("A");
        d2.setIsSmt(new BigDecimal(1));
        infoList.add(d2);
        BsItemInfo d3 = new BsItemInfo();
        d3.setItemNo("040");
        d3.setAbcType("A");
        d3.setIsSmt(new BigDecimal(1));
        infoList.add(d3);
        PowerMockito.when(bsItemInfoRepository.getInfoList(Mockito.any())).thenReturn(infoList);

        List<BsPremanuBomInfo> preBomDetails = new LinkedList<>();
        BsPremanuBomInfo e1 = new BsPremanuBomInfo();
        e1.setItemNo("124");
        e1.setSortSeq(new BigDecimal("1"));
        e1.setTagNum("gu,HU");
        e1.setBomCode("123");
        preBomDetails.add(e1);
        PowerMockito.when(bsPremanuBomInfoRepository.selectBsPreBomDetailsByBomCode(Mockito.any())).thenReturn(preBomDetails);

        List<BsPremanuItemInfo> preItemInfoList = new LinkedList<>();
        BsPremanuItemInfo f1 = new BsPremanuItemInfo();
        f1.setItemNo("123");
        preItemInfoList.add(f1);
        PowerMockito.when(bsPremanuItemInfoRepository.queryPreManuItemInfoNoPage(Mockito.any())).thenReturn(preItemInfoList);

        List<BPcbLocationDetailDTO> itemList = new LinkedList<>();
        BPcbLocationDetailDTO g1 = new BPcbLocationDetailDTO();
        g1.setItemCode("040");
        g1.setUsageCount("12");
        g1.setCraftSection("SMT-A");
        BPcbLocationDetailDTO g2 = new BPcbLocationDetailDTO();
        g2.setItemCode("125");
        g2.setUsageCount("12");
        g2.setCraftSection("SMT-B");
        itemList.add(g1);
        itemList.add(g2);
        PowerMockito.when(bPcbLocationDetailRepository.querySubLevelList(Mockito.any())).thenReturn(itemList);

        List<BBomDetailDTO> bomItemList = new LinkedList<>();
        BBomDetailDTO h1 = new BBomDetailDTO();
        h1.setItemCode("123");
        h1.setUsageCount(new BigDecimal("24"));
        BBomDetailDTO h2 = new BBomDetailDTO();
        h2.setItemCode("124");
        h2.setUsageCount(new BigDecimal("24"));
        BBomDetailDTO h3 = new BBomDetailDTO();
        h3.setItemCode("040");
        h3.setUsageCount(new BigDecimal("24"));
        bomItemList.add(h1);
        bomItemList.add(h2);
        bomItemList.add(h3);
        PowerMockito.when(bBomDetailRepository.getBomItemList(Mockito.any())).thenReturn(bomItemList);

        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);

        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(RedisHelper.setnx(Mockito.anyString(), Mockito.any(), Mockito.anyInt())).thenReturn(true);
        bsPreItemInfoAsyncServiceImpl.runBomCodeAsync(pair, Pair.of(list, new BsAsyncDataDTO()));
        Assert.assertNotNull(pair);

        List<BProdBomHeaderDTO> changList = new LinkedList<>();
        BProdBomHeaderDTO bp1 = new BProdBomHeaderDTO();
        bp1.setOriginalProductCode("123");
        bp1.setProductCode("123");
        bp1.setProdplanId("fgt");
        changList.add(bp1);
        PowerMockito.when(bProdBomHeaderRepository.queryBProdBomListBatch(Mockito.any(), Mockito.any()))
                .thenReturn(changList);
        PowerMockito.when(bomHeaderRepository.getHasCadProductCode(Mockito.any())).thenReturn(new LinkedList<>());
        bsPreItemInfoAsyncServiceImpl.runBomCodeAsync(pair, Pair.of(list, new BsAsyncDataDTO()));

        PowerMockito.when(bomHeaderRepository.getHasCadProductCode(Mockito.any())).thenReturn(Collections.singletonList("123"));
        bsPreItemInfoAsyncServiceImpl.runBomCodeAsync(pair, Pair.of(list, new BsAsyncDataDTO()));

        PowerMockito.when(RedisHelper.setnx(Mockito.anyString(), Mockito.any(), Mockito.anyInt())).thenReturn(false);
        bsPreItemInfoAsyncServiceImpl.runBomCodeAsync(pair, Pair.of(list, new BsAsyncDataDTO()));

        AsyncBomDTO async1 = new AsyncBomDTO();
        async1.setItemNo("1234");
        async1.setItemName("123");
        list.add(async1);
        PowerMockito.when(RedisHelper.setnx("factory:bom:run:123", "factory:bom:run:123", Constant.INT_3600))
                .thenReturn(true);
        bsPreItemInfoAsyncServiceImpl.runBomCodeAsync(pair, Pair.of(list, new BsAsyncDataDTO()));


        PowerMockito.when(bBomDetailRepository.getBomItemList(Mockito.any())).thenReturn(new LinkedList<>());
        bsPreItemInfoAsyncServiceImpl.runBomCodeAsync(pair, Pair.of(list, new BsAsyncDataDTO()));

        AsyncBomDTO asyncBom3 = new AsyncBomDTO();
        asyncBom3.setItemNo("123");
        asyncBom3.setItemName("123");
        asyncBom3.setProdPlanId("fgt");
        list.add(asyncBom3);
        BsBomHierarchicalHead head1 = new BsBomHierarchicalHead();
        PowerMockito.when(commonService.generatedProdBomHierarchical(Mockito.any(), Mockito.any()))
                .thenReturn(head1);
        PowerMockito.when(bProdBomHeaderRepository.queryBProdBomListBatch(Mockito.any(), Mockito.any())).thenReturn(null);
        bsPreItemInfoAsyncServiceImpl.runBomCodeAsync(pair, Pair.of(list, new BsAsyncDataDTO()));



    }
    /* Ended by AICoder, pid:rd42cjaa0c1b1d6144c00ad00177892a20405236 */

    @Test
    public void doSubLevelNoPcb() throws Exception {
        List<AsyncBomDTO> list = new LinkedList<>();
        AsyncBomDTO asyncBomDTO = new AsyncBomDTO();
        asyncBomDTO.setItemNo("123");
        asyncBomDTO.setItemName("123");
        list.add(asyncBomDTO);

        PowerMockito.when(bomHeaderRepository.getHasCadProductCode(Mockito.any())).thenReturn(new LinkedList<String>() {{
            add("123");
        }});

        List<SysLookupValues> dirList = new LinkedList<>();
        SysLookupValues a1 = new SysLookupValues();
        a1.setLookupMeaning("true");
        dirList.add(a1);
        PowerMockito.when(sysLookupValuesRepository.getList(Mockito.any())).thenReturn(dirList);

        List<BBomHeader> headerList = new LinkedList<>();
        BBomHeader b1 = new BBomHeader();
        b1.setProductCode("123");
        b1.setChiDesc("123");
        b1.setIncludePcbFpc(0);
        headerList.add(b1);
        PowerMockito.when(bomHeaderRepository.getList(Mockito.any())).thenReturn(headerList);
        PowerMockito.when(bomHeaderRepository.selectBBomHeaderByProductCodeList(Mockito.any())).thenReturn(headerList);

        List<BBomDetail> bBomDetails = new LinkedList<>();
        BBomDetail c1 = new BBomDetail();
        c1.setItemCode("124");
        c1.setUsageCount(new BigDecimal("24"));
        BBomDetail c2 = new BBomDetail();
        c2.setItemCode("123");
        c2.setUsageCount(new BigDecimal("24"));
        BBomDetail c3 = new BBomDetail();
        c3.setItemCode("040");
        c3.setUsageCount(new BigDecimal("24"));
        bBomDetails.add(c1);
        bBomDetails.add(c2);
        bBomDetails.add(c3);
        PowerMockito.when(bBomDetailRepository.selectDetailsByProductCode(Mockito.any())).thenReturn(bBomDetails);

        List<BsItemInfo> infoList = new LinkedList<>();
        BsItemInfo d1 = new BsItemInfo();
        d1.setItemNo("123");
        d1.setAbcType("A");
        d1.setIsSmt(new BigDecimal(1));
        infoList.add(d1);
        BsItemInfo d2 = new BsItemInfo();
        d2.setItemNo("124");
        d2.setAbcType("A");
        d2.setIsSmt(new BigDecimal(1));
        infoList.add(d2);
        BsItemInfo d3 = new BsItemInfo();
        d3.setItemNo("040");
        d3.setAbcType("A");
        d3.setIsSmt(new BigDecimal(1));
        infoList.add(d3);
        PowerMockito.when(bsItemInfoRepository.getInfoList(Mockito.any())).thenReturn(infoList);

        List<BsPremanuBomInfo> preBomDetails = new LinkedList<>();
        BsPremanuBomInfo e1 = new BsPremanuBomInfo();
        e1.setItemNo("124");
        e1.setSortSeq(new BigDecimal("1"));
        e1.setTagNum("gu,HU");
        e1.setBomCode("123");
        preBomDetails.add(e1);
        PowerMockito.when(bsPremanuBomInfoRepository.selectBsPreBomDetailsByBomCode(Mockito.any())).thenReturn(preBomDetails);

        List<BsPremanuItemInfo> preItemInfoList = new LinkedList<>();
        BsPremanuItemInfo f1 = new BsPremanuItemInfo();
        f1.setItemNo("123");
        preItemInfoList.add(f1);
        PowerMockito.when(bsPremanuItemInfoRepository.queryPreManuItemInfoNoPage(Mockito.any())).thenReturn(preItemInfoList);

        List<BPcbLocationDetailDTO> itemList = new LinkedList<>();
        BPcbLocationDetailDTO g1 = new BPcbLocationDetailDTO();
        g1.setItemCode("040");
        g1.setUsageCount("12");
        g1.setCraftSection("SMT-A");
        BPcbLocationDetailDTO g2 = new BPcbLocationDetailDTO();
        g2.setItemCode("125");
        g2.setUsageCount("12");
        g2.setCraftSection("SMT-B");
        itemList.add(g1);
        itemList.add(g2);
        PowerMockito.when(bPcbLocationDetailRepository.querySubLevelList(Mockito.any())).thenReturn(itemList);

        List<BBomDetailDTO> bomItemList = new LinkedList<>();
        BBomDetailDTO h1 = new BBomDetailDTO();
        h1.setItemCode("123");
        h1.setUsageCount(new BigDecimal("24"));
        BBomDetailDTO h2 = new BBomDetailDTO();
        h2.setItemCode("124");
        h2.setUsageCount(new BigDecimal("24"));
        BBomDetailDTO h3 = new BBomDetailDTO();
        h3.setItemCode("040");
        h3.setUsageCount(new BigDecimal("24"));
        bomItemList.add(h1);
        bomItemList.add(h2);
        bomItemList.add(h3);
        PowerMockito.when(bBomDetailRepository.getBomItemList(Mockito.any())).thenReturn(bomItemList);

        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        try {
            bsPreItemInfoAsyncServiceImpl.doSubLevelNoPcb(null, true, Pair.of("42", "2"));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODUCT_CODE_IS_EMPTY, e.getMessage());
        }

        try {
            bsPreItemInfoAsyncServiceImpl.doSubLevelNoPcb("123", true, Pair.of("42", "2"));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODUCT_CODE_IS_EMPTY, e.getMessage());
        }

        BProdBomHeaderDTO head = new BProdBomHeaderDTO();
        List<BProdBomHeaderDTO> prodBomHeaderDTOList = new ArrayList<>();
        prodBomHeaderDTOList.add(head);
        PowerMockito.when(bProdBomChangeDetailRepository.selectOriginalByProductCode(Mockito.any()))
                .thenReturn(prodBomHeaderDTOList);
        try {
            bsPreItemInfoAsyncServiceImpl.doSubLevelNoPcb("123", true, Pair.of("42", "2"));
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PRODUCT_CODE_IS_EMPTY, e.getMessage());
        }

        PowerMockito.when(bBomDetailRepository.getBomItemList(Mockito.any())).thenReturn(null);
        bsPreItemInfoAsyncServiceImpl.doSubLevelNoPcb("123", true, Pair.of("42", "2"));

        BsBomHierarchicalHead head1 = new BsBomHierarchicalHead();
        head1.setDetails(new LinkedList<>());
        PowerMockito.when(commonService.generatedProdBomHierarchical(Mockito.any(), Mockito.any())).thenReturn(head1);
        bsPreItemInfoAsyncServiceImpl.doSubLevelNoPcb("123", true, Pair.of("42", "2"));
        b1.setIncludePcbFpc(null);
        Assert.assertThrows(MesBusinessException.class,()->bsPreItemInfoAsyncServiceImpl.doSubLevelNoPcb("123", false, Pair.of("42", "2")));
        b1.setIncludePcbFpc(1);
        Assert.assertThrows(MesBusinessException.class,()->bsPreItemInfoAsyncServiceImpl.doSubLevelNoPcb("123", false, Pair.of("42", "2")));
        b1.setIncludePcbFpc(1);
        bsPreItemInfoAsyncServiceImpl.doSubLevelNoPcb("123", true, Pair.of("42", "2"));
    }

    @Test
    public void saveBomLevelData() throws Exception {
        BsBomHierarchicalHead head = new BsBomHierarchicalHead();
        head.setBomCode("123");
        List<BsBomHierarchicalDetail> details = new LinkedList<>();
        BsBomHierarchicalDetail a1 = new BsBomHierarchicalDetail();
        details.add(a1);
        head.setDetails(details);
        List<BBomHeader> headerList = new LinkedList<>();
        BBomHeader b1 = new BBomHeader();
        b1.setProductCode("123");
        b1.setChiDesc("123");
        b1.setIncludePcbFpc(0);
        headerList.add(b1);
        PowerMockito.when(bomHeaderRepository.selectBBomHeaderByProductCodeList(Mockito.any())).thenReturn(headerList);
        try {
            bsPreItemInfoAsyncServiceImpl.saveBomLevelData(head);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_BOM_DETAILS_DATA, e.getMessage());
        }
    }
}
