package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.CFFactoryService;
import com.zte.application.CenterFactoryCallSiteService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.impl.HrmUserCenterServiceImpl;
import com.zte.application.kafka.producer.KafkaMessageProducer;
import com.zte.application.sncabind.impl.PsTaskServiceImpl;
import com.zte.common.ConstantInterface;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BBomHeaderRepository;
import com.zte.domain.model.KafkaLocalMessageRepository;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.domain.model.sncabind.PsTaskRepository;
import com.zte.infrastructure.remote.ApsRemoteService;
import com.zte.infrastructure.remote.PlanScheduleRemoteService;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.interfaces.sncabind.dto.BarcodeLockHeadEntityDTO;
import com.zte.interfaces.sncabind.dto.PsTaskDTO;
import com.zte.interfaces.sncabind.dto.TechnicalAndLockInfoDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @date 2022-10-17 14:22
 */
@PrepareForTest({ConstantInterface.class, HttpClientUtil.class, ServiceDataBuilderUtil.class})
public class PsTaskServiceImplTwoTest extends BaseTestCase {
    @InjectMocks
    private PsTaskServiceImpl psTaskServiceImpl;
    @Mock
    private RedisTemplate<String, Object> redisTemplate;
    @Mock
    private ValueOperations<String, Object> operations;
    @Mock
    HttpServletResponse response;
    @Mock
    private PsTaskRepository psTaskRepository;
    @Mock
    private CFFactoryService cFFactoryService;
    @Mock
    private ApsRemoteService apsRemoteService;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private BBomHeaderRepository bBomHeaderRepository;
    @Mock
    private KafkaMessageProducer kafkaMessageProducer;
    @Mock
    private KafkaLocalMessageRepository kafkaLocalMessageRepository;
    @Mock
    private SysLookupTypesRepository sysLookupTypesRepository;
    @Mock
    private HrmUserCenterServiceImpl hrmUserCenterService;
    @Mock
    LocaleMessageSourceBean lmb;
    @Mock
    CenterFactoryCallSiteService centerFactoryCallSiteService;
    @Mock
    private PlanScheduleRemoteService planScheduleRemoteService;

    @Test
    public void updatePsTask() throws Exception {
        psTaskServiceImpl.updatePsTask(new PsTaskDTO() {{
            setFromFactoryId("55");
        }}, new PsTask());
        Assert.assertNotNull(psTaskServiceImpl.updatePsTask(new PsTaskDTO(), new PsTask()));
    }

    @Test
    public void getFactoryId() throws Exception {
        psTaskServiceImpl.getFactoryId(new PsTaskDTO(), new PsTask());
        psTaskServiceImpl.getFactoryId(new PsTaskDTO() {{
            setFromFactoryId("55");
        }}, new PsTask());
        Assert.assertNotNull(psTaskServiceImpl.getFactoryId(new PsTaskDTO() {{
        }}, new PsTask() {{
            setFactoryId(new BigDecimal(55));
        }}));
    }

    @Test
    public void setSelectFactoryId() throws Exception {
        psTaskServiceImpl.setSelectFactoryId(new PsTask(), null);
        psTaskServiceImpl.setSelectFactoryId(new PsTask(), new PsTask());
        psTaskServiceImpl.setSelectFactoryId(new PsTask() {{
            setFactoryId(new BigDecimal(55));
        }}, new PsTask());
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void verifyExistLockBill() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class, ConstantInterface.class, ServiceDataBuilderUtil.class);
        TechnicalAndLockInfoDTO technicalAndLockInfoDTO = new TechnicalAndLockInfoDTO();
        technicalAndLockInfoDTO.setProdplanId("777");
        technicalAndLockInfoDTO.setFormFactoryId("55");
        try {
            psTaskServiceImpl.verifyExistLockBill(technicalAndLockInfoDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.SYS_LOOK_NOT_CONFIG.equals(e.getExMsgId()));
        }
        List<SysLookupTypesDTO> valuesList = new ArrayList<>();
        SysLookupTypesDTO typesDTO = new SysLookupTypesDTO();
        SysLookupTypesDTO typesDTO1 = new SysLookupTypesDTO();
        SysLookupTypesDTO typesDTO2 = new SysLookupTypesDTO();
        typesDTO.setLookupMeaning("53");
        typesDTO1.setLookupMeaning("no");
        typesDTO2.setLookupMeaning("55");
        valuesList.add(typesDTO);
        valuesList.add(typesDTO1);
        valuesList.add(typesDTO2);
        PowerMockito.when(sysLookupTypesRepository.getList(any())).thenReturn(valuesList);
        psTaskServiceImpl.verifyExistLockBill(technicalAndLockInfoDTO);

        List<BarcodeLockHeadEntityDTO> barcodeLockHeadEntityDTOList = new ArrayList<>();
        barcodeLockHeadEntityDTOList.add(new BarcodeLockHeadEntityDTO() {{
            setBillNo("no1");
        }});
        barcodeLockHeadEntityDTOList.add(new BarcodeLockHeadEntityDTO() {{
            setBillNo("no2");
        }});
        technicalAndLockInfoDTO.setBarcodeLockHeadEntityDTOList(barcodeLockHeadEntityDTOList);
        try {
            psTaskServiceImpl.verifyExistLockBill(technicalAndLockInfoDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.BATCH_HAS_A_LOCK_ORDER.equals(e.getExMsgId()));
        }
    }

    @Test
    public void dealForExistTecinalOrLockInfo2() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class, ConstantInterface.class, ServiceDataBuilderUtil.class);
        TechnicalAndLockInfoDTO technicalAndLockInfoDTO = new TechnicalAndLockInfoDTO();
        technicalAndLockInfoDTO.setProdplanId("777");
        technicalAndLockInfoDTO.setFormFactoryId("55");
        try {
            psTaskServiceImpl.dealForExistTecinalOrLockInfo(new PsTask() {{
                setProdplanId("id1");
                setFactoryId(new BigDecimal(58));
                setNeedMoveTenicAndLock("Y");
            }}, technicalAndLockInfoDTO);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.TASK_SOURCE_CANNOT_BE_EMPTY.equals(e.getExMsgId()));
        }
    }

    @Test
    public void dealForExistTecinalOrLockInfo() throws Exception {
        PowerMockito.mockStatic(HttpClientUtil.class, ConstantInterface.class);
        psTaskServiceImpl.dealForExistTecinalOrLockInfo(new PsTask() {{
            setProdplanId("id1");
        }});
        try {
            psTaskServiceImpl.dealForExistTecinalOrLockInfo(new PsTask() {{
                setProdplanId("id1");
                setNeedMoveTenicAndLock("Y");
            }});
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.GET_TASK_INFO_ERROR.equals(e.getExMsgId()));
        }
        List<PsTaskDTO> tempList = new ArrayList<>();
        tempList.add(new PsTaskDTO());
        PowerMockito.when(psTaskRepository.getFactoryIdByProdplanId(any())).thenReturn(tempList);
        List<SysLookupTypesDTO> valuesList = new ArrayList<>();
        SysLookupTypesDTO typesDTO = new SysLookupTypesDTO();
        SysLookupTypesDTO typesDTO1 = new SysLookupTypesDTO();
        SysLookupTypesDTO typesDTO2 = new SysLookupTypesDTO();
        typesDTO.setLookupMeaning("53");
        typesDTO1.setLookupMeaning("52");
        typesDTO2.setLookupMeaning("55");
        valuesList.add(typesDTO);
        valuesList.add(typesDTO1);
        valuesList.add(typesDTO2);
        PowerMockito.when(sysLookupTypesRepository.getList(Mockito.any())).thenReturn(valuesList);
        try {
            psTaskServiceImpl.dealForExistTecinalOrLockInfo(new PsTask() {{
                setProdplanId("id1");
                setNeedMoveTenicAndLock("Y");
            }});
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.THE_CURRENT_BATCH_IS_ISSUED.equals(e.getExMsgId()));
        }
        PsTask psTask = new PsTask();
        PowerMockito.when(centerFactoryCallSiteService.queryPsTaskInfoByTaskNo(any(), any())).thenReturn(psTask);
        TechnicalAndLockInfoDTO technicalAndLockInfoDTO = new TechnicalAndLockInfoDTO();
        technicalAndLockInfoDTO.setProdplanId("777");
        technicalAndLockInfoDTO.setFormFactoryId("55");
        String result = JSON.toJSONString(new ServiceData<TechnicalAndLockInfoDTO>() {{
            setBo(technicalAndLockInfoDTO);
        }});
        PowerMockito.when(HttpClientUtil.httpGet(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(result);
        try {
            psTaskServiceImpl.dealForExistTecinalOrLockInfo(new PsTask() {{
                setProdplanId("id1");
                setNeedMoveTenicAndLock("Y");
            }});
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.FACTORY_ID_IS_NULL.equals(e.getExMsgId()));
        }
        try {
            psTaskServiceImpl.dealForExistTecinalOrLockInfo(new PsTask() {{
                setProdplanId("id1");
                setFactoryId(new BigDecimal(53));
                setNeedMoveTenicAndLock("Y");
            }});
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.FACTORY_ID_IS_SAME.equals(e.getExMsgId()));
        }

        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        psTaskServiceImpl.dealForExistTecinalOrLockInfo(new PsTask() {{
            setProdplanId("id1");
            setFactoryId(new BigDecimal(58));
            setNeedMoveTenicAndLock("Y");
        }});
    }


}
