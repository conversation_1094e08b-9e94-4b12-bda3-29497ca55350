package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.impl.JobsSupplierProduceMacServiceImpl;
import com.zte.domain.model.JobsSupplierProduceMac;
import com.zte.domain.model.JobsSupplierProduceMacRepository;
import com.zte.interfaces.dto.JobsSupplierProduceMacQueryDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * ClassName: JobsSupplierProduceMacServiceImplTest
 * Description:
 *
 * <AUTHOR>
 * @date 2023/7/26 下午1:40
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({MicroServiceRestUtil.class})
public class JobsSupplierProduceMacServiceImplTest {
    @InjectMocks
    private JobsSupplierProduceMacServiceImpl service;

    @Mock
    private JobsSupplierProduceMacRepository jobsSupplierProduceMacRepository;

    @Test
    public void exportProduceMacTest() throws Exception {
        JobsSupplierProduceMacQueryDTO dto = new JobsSupplierProduceMacQueryDTO();
        String empNo = "12024123";
        String email = "<EMAIL>";
        List<JobsSupplierProduceMac> macList = new ArrayList<>();
        macList.add(new JobsSupplierProduceMac());
        PowerMockito.when(jobsSupplierProduceMacRepository.getJobsSupplierProduceMac(Mockito.any())).thenReturn(macList);

        PowerMockito.mockStatic(MicroServiceRestUtil.class);

        PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.anyString(),Mockito.anyString(),
                Mockito.anyString(), Mockito.anyString(), Mockito.anyString(), Mockito.anyMap())).thenReturn(
                JSON.toJSONString(new ServiceData() {{
                    setBo("1051654456");
                }}));

        service.exportProduceMac(dto, empNo, email);
        Assert.assertNotNull(dto);
    }
}
