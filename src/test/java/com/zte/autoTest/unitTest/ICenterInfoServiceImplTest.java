package com.zte.autoTest.unitTest;

import com.zte.application.impl.ICenterInfoServiceImpl;
import com.zte.common.authority.HttpConstant;
import com.zte.domain.model.SysLookupTypesRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.HashMap;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyObject;

/**
 * <AUTHOR>
 * @date 2022/5/26
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({HttpClientUtil.class,MESHttpHelper.class})
public class ICenterInfoServiceImplTest extends BaseTestCase {

    @InjectMocks
    ICenterInfoServiceImpl service;

    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;

    @Mock
    private SysLookupTypesRepository sysLookupTypesRepository;

    @Test
    public void getSpaceIdAndContentId() throws Exception{
        PowerMockito.when(sysLookupValuesRepository.selectSysLookupValuesById(any())).thenReturn(new SysLookupValues(){{setLookupMeaning("1");}});
        Assert.assertNotNull(service.getSpaceIdAndContentId("1075001"));
    }

    @Test
    public void getDirectoryTreeBySpaceIdAndContentId() throws Exception{
        PowerMockito.mockStatic(HttpClientUtil.class,MESHttpHelper.class);
        PowerMockito.when(sysLookupValuesRepository.selectSysLookupValuesById(any())).thenReturn(new SysLookupValues(){{setLookupMeaning("1");}});
        PowerMockito.when(sysLookupTypesRepository.selectSysLookupTypesById(anyObject())).thenReturn(new SysLookupTypesDTO(){{setLookupMeaning("1");setRemark("1");}});
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        header.put(HttpConstant.X_AUTH_VALUE,"token");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        String result="{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"操作成功\"},\"bo\":[{\"processId\":\"9e93813f-56f4-4b94-b757-184a3cea29a3\",\"processCode\":\"P0004\",\"processType\":\"手工测试\",\"xType\":\"子工序\",\"processName\":\"维修完毕\",\"toolType\":null,\"processControlGroup\":null,\"isPrintTempLabel\":null,\"isDictionaryConfigProcess\":null,\"isDeliverProcess\":null,\"isFailScan\":null,\"remark\":null,\"createBy\":null,\"processSeq\":null,\"currProcess\":null,\"createDate\":\"2018-01-16 15:41:18\",\"lastUpdatedBy\":\"10207212\",\"lastUpdatedDate\":\"2017-11-02 19:21:22\",\"orgId\":null,\"entityId\":2,\"factoryId\":52,\"enabledFlag\":\"Y\",\"attribute1\":null,\"attribute2\":null,\"attribute3\":null,\"attribute4\":null,\"attribute5\":null,\"craftSection\":\"维修\",\"processControlGroupName\":null,\"jigBinding\":null,\"jigDownline\":null,\"wareHousing\":null,\"sourceSys\":null,\"bimuId\":null,\"processControl\":null,\"controlAddress\":null,\"syncAddress\":null,\"qtyControl\":\"1\",\"scanByStation\":null}],\"other\":{\"msg\":\"操作成功\",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.BSProcessController@getList\",\"code\":\"0000\",\"costTime\":\"520ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Thu May 05 08:47:01 CST 2022\",\"tag\":\"查询工序信息\",\"serviceName\":\"zte-mes-manufactureshare-crafttechsys\",\"userId\":\"10260525\"}}";
        PowerMockito.when(HttpClientUtil.httpGet(Mockito.any(),Mockito.anyMap(),Mockito.any())).thenReturn(result);
        Assert.assertNull(service.getDirectoryTreeBySpaceIdAndContentId("contentId","spaceId"));
    }

    @Test
    public void getContentInfoBySpaceIdAndContent() throws Exception{
        PowerMockito.mockStatic(HttpClientUtil.class,MESHttpHelper.class);
        PowerMockito.when(sysLookupValuesRepository.selectSysLookupValuesById(any())).thenReturn(new SysLookupValues(){{setLookupMeaning("1");}});
        PowerMockito.when(sysLookupTypesRepository.selectSysLookupTypesById(anyObject())).thenReturn(new SysLookupTypesDTO(){{setLookupMeaning("1");setRemark("1");}});
        Map<String, String> header = new HashMap<>();
        header.put("X-Factory-Id", "52");
        header.put("X-Emp-No", "10296137");
        header.put(HttpConstant.X_AUTH_VALUE,"token");
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(header);
        String result="{\n" +
                "\t\"bo\": {\n" +
                "\t\t\"archiveStatus\": 0,\n" +
                "\t\t\"contentBody\": \"</p>\",\n" +
                "\t\t\"contentFrom\": \"SPACE\",\n" +
                "\t\t\"contentType\": \"ARTICLE\",\n" +
                "\t\t\"createBy\": \"王斌斌10218627\",\n" +
                "\t\t\"createDate\": \"2019-06-10 10:57:08\",\n" +
                "\t\t\"createNo\": \"10218627\",\n" +
                "\t\t\"currentVersion\": \"20220524095002\",\n" +
                "\t\t\"description\": \"\",\n" +
                "\t\t\"disabled\": false,\n" +
                "\t\t\"encrypted\": true,\n" +
                "\t\t\"id\": \"a307618b0d6d4a4e86865a5da45954fe\",\n" +
                "\t\t\"isLeaf\": false,\n" +
                "\t\t\"parentId\": \"595475644b94439e94d46b842f70f284\",\n" +
                "\t\t\"parentPath\": \"0-944b6a175e794cf890b54e75de6d0c74-8a42f00210d94748a662e7d69ec68a6f-595475644b94439e94d46b842f70f284\",\n" +
                "\t\t\"sortNo\": 2,\n" +
                "\t\t\"spaceId\": \"6838ce4a34644fb698e99215cc97f8c1\",\n" +
                "\t\t\"spaceName\": \"iCenter开放平台\",\n" +
                "\t\t\"spaceType\": 0,\n" +
                "\t\t\"title\": \"公开API接口-提供给第三方平台\",\n" +
                "\t\t\"updateBy\": \"刘俊伟10307132\",\n" +
                "\t\t\"updateDate\": \"2022-05-24 09:50:03\",\n" +
                "\t\t\"updateNo\": \"10307132\",\n" +
                "\t\t\"useMacro\": false\n" +
                "\t},\n" +
                "\t\"code\": {\n" +
                "\t\t\"code\": \"0000\",\n" +
                "\t\t\"msgId\": \"RetCode.Success\"\n" +
                "\t}\n" +
                "}";
        PowerMockito.when(HttpClientUtil.httpGet(Mockito.any(),Mockito.anyMap(),Mockito.any())).thenReturn(result);
        Assert.assertNotNull(service.getContentInfoBySpaceIdAndContent("contentId","spaceId"));
    }
}