package com.zte.autoTest.unitTest;

import com.zte.common.ExcelUtil;
import com.zte.common.ExcelUtils;
import com.zte.common.utils.MpConstant;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;

import java.text.SimpleDateFormat;

public class ExcelDataHelperTest extends BaseTestCase {

    @Test
    public void getString() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS_O);
        Assert.assertNotNull(ExcelUtils.getString(null));
    }

    @Test
    public void getStringExcelUtil() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS_O);
        Assert.assertNotNull(ExcelUtil.getString(null));
    }

    @Test
    public void checkCellValue() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS_O);
        Assert.assertNotNull(ExcelUtil.checkCellValue("2222"));
    }

    @Test
    public void checkCellValueNew() throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat(MpConstant.DATE_FORMAT_YYYYMMDDHHMMSS_O);
        Assert.assertNotNull(ExcelUtils.checkCellValue("2222"));
    }
}
