package com.zte.autoTest.unitTest;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.BsPremanuItemInfoService;
import com.zte.application.HrmUserCenterService;
import com.zte.application.SendMaterialInfoService;
import com.zte.application.StItemBarcodeService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.impl.PkCodeInfoServiceImpl;
import com.zte.common.ExcelUtils;
import com.zte.common.utils.Constant;
import com.zte.domain.model.PkCodeHistoryRepository;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PkCodeInfoRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.infrastructure.remote.ProductionmgmtRemoteService;
import com.zte.interfaces.dto.BarcodeExpandVO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.PkCodeInfoDTO;
import com.zte.interfaces.dto.PkCodeInfoSyncQueryDTO;
import com.zte.interfaces.dto.SplitReelIdDTO;
import com.zte.interfaces.dto.TaskMaterialIssueSeqEntityDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.AsyncExportFileCommonService;
import com.zte.springbootframe.util.FileUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;

/**
 * @Author:
 * @Date: 2020/10/22 11:29
 */
@PrepareForTest({MicroServiceRestUtil.class, ProductionmgmtRemoteService.class, FileUtils.class, EasyExcel.class,
        DatawbRemoteService.class, MicroServiceRestUtil.class, HttpRemoteService.class,
        ExcelUtils.class, EasyExcelFactory.class})
public class PkCodeInfoServiceImplTest extends BaseTestCase {

    @InjectMocks
    private PkCodeInfoServiceImpl service;

    @Mock
    private PkCodeInfoRepository pkCodeInfoRepository;

    @Mock
    private SendMaterialInfoService sendMaterialInfoService;

    @Mock
    private BsPremanuItemInfoService bsPremanuItemInfoService;
    @Mock
    private StItemBarcodeService stItemBarcodeService;
    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Mock
    private PkCodeHistoryRepository historyRepository;

    @Mock
    private ProductionmgmtRemoteService productionmgmtRemoteService;

    @Mock
    private DatawbRemoteService datawbRemoteService;

    @Mock
    private AsyncExportFileCommonService asyncExportFileCommonService;
    @Mock
    ExcelWriter writer;
    @Mock
    WriteSheet build;
    @Mock
    ExcelWriterSheetBuilder excelWriterSheetBuilder;
    @Mock
    ExcelWriterBuilder writerBuilder;
    @Mock
    private HrmUserCenterService hrmUserCenterService;

    @Before
    public void init() {
        PowerMockito.mockStatic(FileUtils.class);
        PowerMockito.mockStatic(EasyExcel.class);
        PowerMockito.mockStatic(EasyExcelFactory.class);
        PowerMockito.when(FileUtils.createFilePathAndCheck(Mockito.anyString())).thenReturn("124");
        PowerMockito.when(EasyExcelFactory.writerSheet(Mockito.any(), Mockito.any())).thenReturn(excelWriterSheetBuilder);
        PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);
        PowerMockito.when(EasyExcel.write(Mockito.anyString(), Mockito.any())).thenReturn(writerBuilder);
        PowerMockito.when(writerBuilder.build()).thenReturn(writer);
    }

    @Test
    public void exportReelidWorkload() throws Exception {
        PkCodeInfoDTO dto = new PkCodeInfoDTO();
        service.exportReelidWorkload(dto);

        PowerMockito.when(pkCodeInfoRepository.getRelOneList(Mockito.any())).thenReturn(null);
        service.exportReelidWorkload(dto);

        List<PkCodeInfo> list = new LinkedList<>();
        PkCodeInfo a1 = new PkCodeInfo();
        list.add(a1);
        PkCodeInfo a2 = new PkCodeInfo();
        a1.setCreateBy("123");
        a2.setLastUpdatedBy("234");
        list.add(a2);
        PowerMockito.when(pkCodeInfoRepository.getRelOneList(Mockito.any())).thenReturn(list);
        service.exportReelidWorkload(dto);

        Map<String, HrmPersonInfoDTO> hrmPersonInfo = new HashMap<>();
        HrmPersonInfoDTO b1 = new HrmPersonInfoDTO();
        b1.setEmpName("234");
        hrmPersonInfo.put("123", b1);
        hrmPersonInfo.put("234", b1);
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.any())).thenReturn(hrmPersonInfo);
        service.exportReelidWorkload(dto);
        Assert.assertNotNull(dto);
    }

    @Test
    public void setItemNo() throws Exception {
        PowerMockito.mockStatic(HttpRemoteService.class);
        PowerMockito.when(HttpRemoteService.pointToPointSelective(any(), any(), any(), any(), any()))
                .thenReturn(JacksonJsonConverUtil.getMapperInstance().readTree(JSON.toJSONString(new ServiceData() {{
                    setBo(Lists.newArrayList(new PkCodeInfo()));
                }})));
        PkCodeInfoDTO pkCodeInfoDTO = new PkCodeInfoDTO();
        Whitebox.invokeMethod(service, "setItemNo", pkCodeInfoDTO, new PkCodeInfo());
        PkCodeInfoDTO pkCodeInfoDTO1 = new PkCodeInfoDTO();
        pkCodeInfoDTO1.setProductCode("itemNo");
        Whitebox.invokeMethod(service, "setItemNo", pkCodeInfoDTO1, new PkCodeInfo());
        Assert.assertNotNull(pkCodeInfoDTO);
    }

    @Test
    public void batchUpdateQty() throws Exception {
        PowerMockito.when(pkCodeInfoRepository.batchUpdateQty(any())).thenReturn(1);
        List<PkCodeInfoDTO> list = new ArrayList<>();
        PkCodeInfoDTO pkCodeInfoDTO = new PkCodeInfoDTO();
        list.add(pkCodeInfoDTO);
        Assert.assertNotNull(service.batchUpdateQty(list));
    }

    @Test
    public void batchInsertPkCodeInfoAndHistory() throws Exception {
        PowerMockito.when(pkCodeInfoRepository.batchUpdateQty(any())).thenReturn(1);
        PowerMockito.when(historyRepository.batchInsertHistoryByPkCode(anyList(), anyString())).thenReturn(1);
        List<PkCodeInfo> list = new ArrayList<>();
        PkCodeInfo pkCodeInfoDTO = new PkCodeInfo();
        list.add(pkCodeInfoDTO);
        service.batchInsertPkCodeInfoAndHistory(list);
        Assert.assertNotNull(list);
    }

    @Test
    public void saveOrUpdate() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData()));
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(new SysLookupValues() {{
            setLookupMeaning("Y");
        }});
        PowerMockito.when(barcodeCenterRemoteService.getBarcodeFromBarcodeCenter(any())).thenReturn(
                Lists.newArrayList(new BarcodeExpandVO()));
        PowerMockito.when(productionmgmtRemoteService.updateOrInsertByPkcode(Mockito.any(), Mockito.any())).thenReturn(1);

        PkCodeInfo info = new PkCodeInfo();
        info.setItemName("it");
        info.setPkCode("ZTE0219114000064S2");
        info.setFactoryId(new BigDecimal("52"));
        Assert.assertNotNull(service.saveOrUpdate(info));
    }

    @Test
    public void insertOrUpdate() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData()));
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(new SysLookupValues() {{
            setLookupMeaning("Y");
        }});
        PowerMockito.when(barcodeCenterRemoteService.getBarcodeFromBarcodeCenter(any())).thenReturn(
                Lists.newArrayList(new BarcodeExpandVO()));
        PowerMockito.when(productionmgmtRemoteService.updateOrInsertByPkcode(Mockito.any(), Mockito.any())).thenReturn(1);
        PkCodeInfo info = new PkCodeInfo();
        info.setItemName("it");
        info.setPkCode("ZTE0219114000064S2");
        info.setFactoryId(new BigDecimal("52"));
        Assert.assertNotNull(service.insertOrUpdate(info));
    }

    @Test
    public void splitReelId() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class);
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(new ServiceData()));
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(new SysLookupValues() {{
            setLookupMeaning("Y");
        }});
        PowerMockito.when(barcodeCenterRemoteService.getBarcodeFromBarcodeCenter(any())).thenReturn(
                Lists.newArrayList(new BarcodeExpandVO()));
        PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfoById(any())).thenReturn(new PkCodeInfo());
        PowerMockito.when(productionmgmtRemoteService.splitReelId(Mockito.any(), Mockito.any())).thenReturn(1);
        service.splitReelId(new SplitReelIdDTO() {{
            setOldPkInfo(new PkCodeInfoDTO());
            setNewPkInfo(new PkCodeInfoDTO());
        }});
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void selectPkCodeInfoByCodes() {
        PkCodeInfoDTO reqDTO = new PkCodeInfoDTO();
        reqDTO.setPkCodeList(new LinkedList<>());
        reqDTO.getPkCodeList().add("123");
        reqDTO.getPkCodeList().add("1234");
        Assert.assertNotNull(service.selectPkCodeInfoByCodes(reqDTO));
    }

    @Test
    public void registerPkCode() {
        service.registerPkCode(Lists.newArrayList(new PkCodeInfo() {{
            setPkCode("1");
            setSupplierFlag("1");
        }}, new PkCodeInfo() {{
            setSupplierFlag("0");
        }}));
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void insertPkCodeInfoAutoTest() {

        PowerMockito.when(pkCodeInfoRepository.selectPkCodeInfoById(anyObject())).thenReturn(null);

        PowerMockito.when(pkCodeInfoRepository.insertPkCodeInfoSelective(anyObject())).thenReturn(1);

        PkCodeInfo record = new PkCodeInfo();
        record.setPkId("3b66a077-5f2d-4fba-bbf1-35f0e9a6d9f5");
        record.setPkCode("70000920108000000002");
        record.setItemCode("042081200001");
        record.setProductTask("7000092");

        Assert.assertNotNull(service.insertPkCodeInfoAutoTest(record));
    }

    @Test
    public void getPageForSync() {
        service.getPageForSync(new PkCodeInfoSyncQueryDTO());
        Page page = service.getPageForSync(new PkCodeInfoSyncQueryDTO() {{
            setDefaultSync("Y");
        }});
        Assert.assertTrue(page.getRows().size() >= 0);
    }

    @Test
    public void getPkCodePage() {
        Page<PkCodeInfo> dto = new Page<>();
        List<PkCodeInfo> pkCodePage = new ArrayList<>();
        dto.setRows(pkCodePage);
        PowerMockito.when(pkCodeInfoRepository.getPkCodePage(Mockito.any())).thenReturn(pkCodePage);
        Page<PkCodeInfo> page = service.getPkCodePage(dto);
        Assert.assertTrue(page.getRows().size() >= 0);
    }

    @Test
    public void addIssueSeqRelationData() throws Exception {
        List<PkCodeInfo> itemList = new ArrayList<>();
        service.addIssueSeqRelationData(itemList);
        PkCodeInfo pkCodeInfo = new PkCodeInfo();
        itemList.add(pkCodeInfo);
        pkCodeInfo.setSupplerCode("sdsd");

        List<PkCodeInfo> pkCodeInfoList = new ArrayList<>();
        PkCodeInfo pkCodeInfo1 = new PkCodeInfo();
        pkCodeInfo1.setPkCode("test");
        pkCodeInfo1.setSysLotCode("dsadsa");
        PowerMockito.when(pkCodeInfoRepository.selectPkCodeListByReelIds(Mockito.anyList())).thenReturn(pkCodeInfoList);
        List<TaskMaterialIssueSeqEntityDTO> packSpecList = new ArrayList<>();
        TaskMaterialIssueSeqEntityDTO dto = new TaskMaterialIssueSeqEntityDTO();
        dto.setSupplerCode("sdsd");
        packSpecList.add(dto);
        PowerMockito.when(datawbRemoteService.getIsHasDirFlag(Mockito.anyList())).thenReturn(packSpecList);
        service.addIssueSeqRelationData(itemList);
        Assert.assertNotNull(itemList);
        Assert.assertNotNull(pkCodeInfoList);
    }

    @Test
    public void getList() throws Exception {
        List<PkCodeInfo> list = new ArrayList<>();
        PkCodeInfoDTO dto = new PkCodeInfoDTO();
        dto.setPkCode("test");
        PkCodeInfo dto1 = new PkCodeInfo();
        dto1.setPkCode("test");
        list.add(dto1);
        PowerMockito.when(pkCodeInfoRepository.getListByPkCodeAndParentPk(Mockito.anyObject())).thenReturn(list);
        Assert.assertNotNull(service.getList(dto));
    }

    @Test
    public void getPage() throws Exception {
        List<PkCodeInfo> list = new ArrayList<>();
        PkCodeInfoDTO dto = new PkCodeInfoDTO();
        dto.setPkCode("test");
        PkCodeInfo dto1 = new PkCodeInfo();
        dto1.setPkCode("test");
        list.add(dto1);
        PowerMockito.when(pkCodeInfoRepository.getPageByPkCodeAndParentPk(Mockito.anyObject())).thenReturn(list);
        Assert.assertNotNull(service.getPage(dto));
    }

    @Test
    public void getPageTwo() throws Exception {
        List<PkCodeInfo> list = new ArrayList<>();
        PkCodeInfoDTO dto = new PkCodeInfoDTO();
        PkCodeInfo dto1 = new PkCodeInfo();
        dto1.setPkCode("test");
        list.add(dto1);
        PowerMockito.when(pkCodeInfoRepository.getPage(Mockito.anyObject())).thenReturn(list);
        Assert.assertNotNull(service.getPage(dto));
    }

    @Test
    public void downExcel() throws Exception {
        PowerMockito.mockStatic(ExcelUtils.class);
        List<PkCodeInfo> list = new ArrayList<>();
        PkCodeInfoDTO dto = new PkCodeInfoDTO();
        PkCodeInfo dto1 = new PkCodeInfo();
        dto1.setPkCode("test");
        list.add(dto1);
        PowerMockito.when(historyRepository.getListAsInfo(Mockito.anyObject())).thenReturn(list);
        PowerMockito.when(stItemBarcodeService.pkCodeChange(Mockito.anyList())).thenReturn(list);
        Assert.assertNull(service.downExcel(dto));
    }

    @Test
    public void downExcelTwo() throws Exception {
        PowerMockito.mockStatic(ExcelUtils.class);
        List<PkCodeInfo> list = new ArrayList<>();
        PkCodeInfoDTO dto = new PkCodeInfoDTO();
        dto.setPkCode("test");
        PkCodeInfo dto1 = new PkCodeInfo();
        dto1.setPkCode("test");
        list.add(dto1);
        PowerMockito.when(historyRepository.getListAsInfoByPk(Mockito.anyObject())).thenReturn(list);
        PowerMockito.when(stItemBarcodeService.pkCodeChange(Mockito.anyList())).thenReturn(list);
        Assert.assertNull(service.downExcel(dto));
    }


    @Test
    public void batchUpdatePkCodeQty() {
        List<PkCodeInfo> list = new ArrayList<>();
        PkCodeInfo a1 = new PkCodeInfo();
        list.add(a1);
        service.batchUpdatePkCodeQty(list);
        Assert.assertNotNull(list);
    }

    /*Started by AICoder, pid:u18829a3d1r52531421e0822118d9a6657a00797*/
    @Test
    public void testBatchUpdateQtyTaskByPkCodesWithEmptyList() {
        // Given
        // No additional setup needed as we are using an empty list

        // When
        service.batchUpdateQtyTaskByPkCodes(null);

        // Then
        List<PkCodeInfo> list = new LinkedList<>();
        PkCodeInfo a1 = new PkCodeInfo();
        list.add(a1);
        service.batchUpdateQtyTaskByPkCodes(list);
        Assert.assertTrue(!CollectionUtils.isEmpty(list));
    }
    /*Ended by AICoder, pid:u18829a3d1r52531421e0822118d9a6657a00797*/
}
