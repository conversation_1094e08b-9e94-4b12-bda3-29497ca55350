package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.BBomHeaderService;
import com.zte.application.CommonService;
import com.zte.application.ProdUnbindingSettingService;
import com.zte.application.SysLookupTypesService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.impl.BProdBomChangeDetailServiceImpl;
import com.zte.application.impl.HrmUserCenterServiceImpl;
import com.zte.application.impl.ProdBindingSettingServiceImpl;
import com.zte.application.sncabind.impl.CfItemBindingServiceImpl;
import com.zte.common.ConstantInterface;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.ProdBindingSetting;
import com.zte.domain.model.ProdBindingSettingRepository;
import com.zte.domain.model.ProdUnbindingSetting;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.CrafttechRemoteService;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.interfaces.dto.BSProcess;
import com.zte.interfaces.dto.BindQuerySectionDTO;
import com.zte.interfaces.dto.CfItemBindingDTO;
import com.zte.interfaces.dto.CtRouteDetailDTO;
import com.zte.interfaces.dto.HrmPersonInfoDTO;
import com.zte.interfaces.dto.ProdBindSettingDTO;
import com.zte.interfaces.dto.ProdBindingSettingDTO;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

import static org.mockito.Matchers.any;

/**
 * 单元测试
 *
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ProdBindingSettingServiceImpl.class, MESHttpHelper.class, BasicsettingRemoteService.class,ConstantInterface.class,
        ServiceDataBuilderUtil.class,RedisCacheUtils.class,ObtainRemoteServiceDataUtil.class,
        JacksonJsonConverUtil.class,CrafttechRemoteService.class,HttpRemoteUtil.class})
public class ProdBindingSettingServiceImplTest  extends BaseTestCase {

    @InjectMocks
    private ProdBindingSettingServiceImpl prodBindingSettingServiceImpl;

    @Mock
    private RedisLock redisLock;

    @Mock
    private ProdBindingSettingRepository prodBindingSettingRepository;
    @Mock
    private SysLookupValuesService sysLookupValuesService;
    @Mock
    private BBomHeaderService bBomHeaderService;
    @Mock
    private ProdUnbindingSettingService prodUnbindingSettingService;
    @Mock
    private SysLookupTypesService sysLookupTypesService;
    @Mock
    private CommonService commonService;
    @Mock
    private HrmUserCenterServiceImpl hrmUserCenterService;

    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;

    @Mock
    private CfItemBindingServiceImpl cfItemBindingServiceImpl;

    @Mock
    private BProdBomChangeDetailServiceImpl bProdBomChangeDetailService;

    @Mock
    private ConstantInterface constantInterface;

    @Before
    public void init() {
        // 待测的类标注为@InjectMocks
        // 依赖的其他类标注为 @Mock
        // 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
        MockitoAnnotations.initMocks(this);
    }

    @Test
//	@PrepareForTest({MESHttpHelper.class,BasicsettingRemoteService.class})
    public void getNeedBindList() throws Exception {
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(BasicsettingRemoteService.class);

        String productCode = "126510150593AHB";
        String processCode = "B";
        List<ProdBindingSettingDTO> bindingList = new ArrayList<>();
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setProductCode(productCode);
        prodBindingSettingDTO.setProcessCode(processCode);
        prodBindingSettingDTO.setItemType("0");
        prodBindingSettingDTO.setItemCode("123456");
        bindingList.add(prodBindingSettingDTO);
        PowerMockito.when(prodBindingSettingRepository.getProdBindingSettingDTOList(any())).thenReturn(bindingList);

        SysLookupValues sysLookupValue = new SysLookupValues();
        sysLookupValue.setLookupMeaning("55");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(any())).thenReturn(sysLookupValue);

        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setRemark("http://10.5.209.128");
        PowerMockito.when(sysLookupTypesService.selectSysLookupTypeByLookupType(any())).thenReturn(sysLookupTypesDTO);

        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(new HashMap<>());
        BSProcess bSProcess = new BSProcess();
        bSProcess.setProcessCode("B");
        bSProcess.setProcessName("单板装配");
        bSProcess.setRemark("单板装配");
        PowerMockito.when(BasicsettingRemoteService.getLastPackageProcess(any(), any(), any())).thenReturn(bSProcess);

        List<ProdBindingSettingDTO> bindChildBoardList = new ArrayList<>();
        ProdBindingSettingDTO prodBindingSettingDTO1 = new ProdBindingSettingDTO();
        prodBindingSettingDTO1.setProductCode(productCode);
        prodBindingSettingDTO1.setProcessCode(processCode);
        prodBindingSettingDTO1.setItemType("1");
        prodBindingSettingDTO1.setItemCode("123456ABC");
        bindChildBoardList.add(prodBindingSettingDTO1);
        PowerMockito.when(bBomHeaderService.getAllChildBoard(any())).thenReturn(bindChildBoardList);

        PowerMockito.when(prodUnbindingSettingService.getUnbindList(any())).thenReturn(new ArrayList<ProdUnbindingSetting>());

        Assert.assertNotNull(prodBindingSettingServiceImpl.getNeedBindList(productCode, processCode));
    }

    @Test
//	@PrepareForTest({ProdBindingSettingServiceImpl.class, RedisCacheUtils.class})
    public void saveProdBindSetting() throws Exception {
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);
        PowerMockito.when(prodBindingSettingRepository.deleteProdBindingSettingByProductAndProcess(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(1);
		PowerMockito.when(prodBindingSettingRepository.updateProdBindingSettingById(Mockito.anyList(), Mockito.anyString())).thenReturn(1);

        String productCode = "126510150593AHB";
        String processCode = "B";
        ProdBindSettingDTO record = new ProdBindSettingDTO();
        record.setProductCode(productCode);
        record.setProcessCode(processCode);
        List<ProdBindingSettingDTO> dtoList = new ArrayList<>();
        ProdBindingSettingDTO dto = new ProdBindingSettingDTO();
        dto.setItemCode("123456");
        dtoList.add(dto);
        record.setDtoList(dtoList);
		List<ProdBindingSettingDTO> dtoList1 = new ArrayList<>();
		ProdBindingSettingDTO dto1 = new ProdBindingSettingDTO();
		dto1.setItemCode("123457");
		dto1.setMainCraftSection("SMT-A");
		dtoList1.add(dto1);
		record.setNeedUpdateList(dtoList1);
        prodBindingSettingServiceImpl.saveProdBindSetting(record, "10266925", "55", "2");
        Assert.assertNotNull(dto);
    }

    @Test
    public void postNeedBindSectionList() throws Exception {
        BindQuerySectionDTO dto = new BindQuerySectionDTO();
        dto.setMainProductCode("129206751186AJB");
        dto.setProductCode("126510150593AHB");

        ProdBindingSettingDTO dto1 = new ProdBindingSettingDTO();
        List<ProdBindingSettingDTO> mockList = new ArrayList<>();
        dto1.setProductCode("126510150593AHB");
        dto1.setItemCode("003010200185");
        dto1.setItemName("LDO线性电压调整器");
        dto1.setItemType("0");
        dto1.setUsageCount(new BigDecimal(1));
        mockList.add(dto1);

        PowerMockito.when(prodBindingSettingRepository.getNeedBindSectionList(dto.getProductCode(), dto.getMainProductCode(), dto.getProcessCode())).thenReturn(mockList);

        List<ProdBindingSettingDTO> result = prodBindingSettingServiceImpl.postNeedBindSectionList(dto);
        Assert.assertTrue(result.size() >= 0);
    }

	@Test
	public void queryProdBindingInfoPage() throws Exception {
        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.mockStatic(MESHttpHelper.class);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        Page<ProdBindingSettingDTO> page = new Page<>();
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        prodBindingSettingDTO.setProductCode("ZTE1111");
        prodBindingSettingDTO.setItemCode("ZTE1111");
        prodBindingSettingDTO.setCreateBy("00286523");
        prodBindingSettingDTO.setProcessCode("S1015");
        page.setParams(prodBindingSettingDTO);
        List<ProdBindingSettingDTO> resultList = new ArrayList<>();
        ProdBindingSettingDTO resultDto = new ProdBindingSettingDTO();
        resultDto.setProductCode("ZTE1111");
        resultDto.setItemCode("ZTE1111");
        resultDto.setCreateBy("00286523");
        resultDto.setLastUpdatedBy("00286523");
        resultDto.setProcessCode("S1015");
        resultDto.setProcessName("出库");
        resultDto.setUsageCount(new BigDecimal("1"));
        resultDto.setLastUpdatedDate(new Date());
        resultList.add(resultDto);

        List<BSProcess> bsProcessList = new ArrayList<>();
        BSProcess bsProcess = new BSProcess();
        bsProcess.setProcessCode("S1015");
        bsProcess.setProcessName("出库");
        bsProcessList.add(bsProcess);

        Map<String, HrmPersonInfoDTO> bsPubHrMap = new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO = new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("hl");
        bsPubHrMap.put("00286523", hrmPersonInfoDTO);

        PowerMockito.when(prodBindingSettingRepository.queryProdBindingInfoPage(Mockito.any())).thenReturn(new ArrayList<>());
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.any())).thenReturn(bsPubHrMap);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(new HashMap<>());
        PowerMockito.when(commonService.getIMESBasicUrl()).thenReturn("");
        PowerMockito.when(CrafttechRemoteService.getProcessInfo(Mockito.any(), Mockito.any(), Mockito.anyString())).thenReturn(bsProcessList);
        Page<ProdBindingSettingDTO> pageInfo = prodBindingSettingServiceImpl.queryProdBindingInfoPage(prodBindingSettingDTO);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
        PowerMockito.when(prodBindingSettingRepository.queryProdBindingInfoPage(Mockito.any())).thenReturn(resultList);
        pageInfo = prodBindingSettingServiceImpl.queryProdBindingInfoPage(prodBindingSettingDTO);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
        prodBindingSettingDTO.setProductCode("");
        Page<ProdBindingSettingDTO> pageInfo1 = prodBindingSettingServiceImpl.queryProdBindingInfoPage(prodBindingSettingDTO);
        Assert.assertTrue(pageInfo1.getRows().size() >= 0);
        // mock

        // 制造BOM 不为空的分支
        List<BProdBomChangeDetailDTO> changeDetailDTOS = new ArrayList<>();

        PowerMockito.when(bProdBomChangeDetailService.queryBProdBomDetailChangeList(Mockito.any())).thenReturn(changeDetailDTOS);
        prodBindingSettingDTO.setProductCode("ZTE1111_1");
        List<BProdBomChangeDetailDTO> apsList = new LinkedList<>();
        BProdBomChangeDetailDTO bProdBomChangeDetailDTO = new BProdBomChangeDetailDTO();
        bProdBomChangeDetailDTO.setOriginalProductCode("ZTE1111");
        changeDetailDTOS.add(bProdBomChangeDetailDTO);
        apsList.add(bProdBomChangeDetailDTO);
        PowerMockito.when(JacksonJsonConverUtil.jsonToListBean(Mockito.anyString(), Mockito.anyObject())).thenReturn(apsList);
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("123");
        PowerMockito.when(prodBindingSettingRepository.queryProdBindingInfoPage(Mockito.any())).thenReturn(new ArrayList<>());
        pageInfo1 = prodBindingSettingServiceImpl.queryProdBindingInfoPage(prodBindingSettingDTO);
        Assert.assertTrue(pageInfo1.getRows().size() >= 0);
        // 绑定关系不为空
        prodBindingSettingDTO.setProductCode("ZTE1111_1");
        PowerMockito.when(prodBindingSettingRepository.queryProdBindingInfoPage(Mockito.any())).thenReturn(resultList);
        pageInfo1 = prodBindingSettingServiceImpl.queryProdBindingInfoPage(prodBindingSettingDTO);
        Assert.assertTrue(pageInfo1.getRows().size() >= 0);

        Page<ProdBindingSettingDTO> pageInfo2 = new Page<>(Constant.INT_1, Constant.BATCH_SIZE_500);
        pageInfo2.setParams(prodBindingSettingDTO);
         List<ProdBindingSettingDTO> resultList2 = new ArrayList<>(500);
        for (int i = 500; i > 0; i--) {
            resultList2.add(new ProdBindingSettingDTO(){{
                setProductCode("ZTE1111");
                setItemCode("ZTE1111");
                setCreateBy("00286523");
                setLastUpdatedBy("00286523");
                setProcessCode("S1015");
                setProcessName("出库");
                setLastUpdatedDate(new Date());
                setUsageCount(new BigDecimal("1"));
            }});
        }
        PowerMockito.when(prodBindingSettingRepository.queryProdBindingInfoPage(pageInfo2)).thenReturn(resultList2);
        BProdBomChangeDetailDTO bProdBomChangeDetailDTO1 = new BProdBomChangeDetailDTO();
        prodBindingSettingDTO.setProductCode("ZTE1111_1");
        bProdBomChangeDetailDTO.setOriginalProductCode("ZTE1111_1");
        changeDetailDTOS.add(bProdBomChangeDetailDTO1);
        PowerMockito.when(bProdBomChangeDetailService.queryBProdBomDetailChangeList(Mockito.any())).thenReturn(changeDetailDTOS);
        pageInfo1 = prodBindingSettingServiceImpl.queryProdBindingInfoPage(prodBindingSettingDTO);
        Assert.assertTrue(pageInfo1.getRows().size() >= 0);
    }


    @Test
    public void testPaginate_ValidPagination() {
        Page<ProdBindingSettingDTO> pageInfo = new Page<>();
        ProdBindingSettingDTO dto = new ProdBindingSettingDTO();

        dto.setRows(-1);
        prodBindingSettingServiceImpl.paginate(new ArrayList<>(), pageInfo, dto);
        Assert.assertTrue(dto.getRows().intValue() >= 0);
        dto.setRows(1);
        prodBindingSettingServiceImpl.paginate(new ArrayList<>(), pageInfo, dto);
        Assert.assertTrue(dto.getRows().intValue() >= 0);

        List<ProdBindingSettingDTO> settingDTOS = new LinkedList<>();
        ProdBindingSettingDTO a1 = new ProdBindingSettingDTO();
        ProdBindingSettingDTO a2 = new ProdBindingSettingDTO();
        settingDTOS.add(a1);
        settingDTOS.add(a2);
        dto.setPage(-5);
        prodBindingSettingServiceImpl.paginate(settingDTOS, pageInfo, dto);
        Assert.assertTrue(dto.getRows().intValue() >= 0);
        dto.setPage(2);
        dto.setRows(3);
        prodBindingSettingServiceImpl.paginate(settingDTOS, pageInfo, dto);
        Assert.assertTrue(dto.getRows().intValue() >= 0);
    }

    @Test
    public void itemReplace() {
        List<ProdBindingSettingDTO> resultList = new ArrayList<>();
        List<BProdBomChangeDetailDTO> bomDetailDTOS = new ArrayList<>();
        prodBindingSettingServiceImpl.itemReplace(resultList, bomDetailDTOS);
        Assert.assertTrue(resultList.size() == 0);
        ProdBindingSettingDTO prodBindingSettingDTO = new ProdBindingSettingDTO();
        resultList.add(prodBindingSettingDTO);
        prodBindingSettingServiceImpl.itemReplace(resultList, bomDetailDTOS);
        Assert.assertTrue(resultList.size() == 1);
        resultList.clear();
        BProdBomChangeDetailDTO bProdBomChangeDetailDTO = new BProdBomChangeDetailDTO();
        bomDetailDTOS.add(bProdBomChangeDetailDTO);
        prodBindingSettingServiceImpl.itemReplace(resultList, bomDetailDTOS);
        Assert.assertTrue(resultList.size() == 0);
        // 集合不为空
        bProdBomChangeDetailDTO.setOriginalProductCode("orig");
        bProdBomChangeDetailDTO.setItemCode("itemCode");
        prodBindingSettingDTO.setLastUpdatedDate(new Date());
        prodBindingSettingDTO.setUsageCount(new BigDecimal("1"));
        prodBindingSettingDTO.setItemCode("orig");
        resultList.add(prodBindingSettingDTO);
        ProdBindingSettingDTO prodBindingSettingDTO2 = new ProdBindingSettingDTO();
        prodBindingSettingDTO2.setUsageCount(new BigDecimal("1"));
        prodBindingSettingDTO2.setItemCode("orig");
        prodBindingSettingDTO2.setLastUpdatedDate(new Date());

        resultList.add(prodBindingSettingDTO2);
        prodBindingSettingServiceImpl.itemReplace(resultList, bomDetailDTOS);
        Assert.assertTrue(resultList.size()>0 );
    }

    @Test
    public void queryProdBindingBatch() throws Exception {
        ProdBindingSettingDTO dto = new ProdBindingSettingDTO();
        List<String> pr = new LinkedList<>();
        pr.add("1233");
        dto.setProductCodeList(pr);

        List<ProdBindingSettingDTO> settingDTOS = new LinkedList<>();
        ProdBindingSettingDTO a1 = new ProdBindingSettingDTO();
        settingDTOS.add(a1);
        PowerMockito.when(prodBindingSettingRepository.queryProdBindingBatch(Mockito.any()))
                .thenReturn(settingDTOS)
        ;
        Assert.assertNotNull(prodBindingSettingServiceImpl.queryProdBindingBatch(dto));
    }

    @Test
//    @PrepareForTest({CrafttechRemoteService.class})
    public void analyseBindingSetting() throws Exception {
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.when(prodBindingSettingRepository.selectAnalysedProd(any())).thenReturn(Lists.newArrayList("3"));
        PowerMockito.when(prodBindingSettingRepository.getBindingSetting(any())).thenReturn(Lists.newArrayList(
                new ProdBindingSetting(){{setProductCode("5");setItemCode("51");setProcessCode("4");}}
        ));
        PowerMockito.when(CrafttechRemoteService.queryRouteDetailBatch(any(), any(), any())).thenReturn(Lists.newArrayList(
                new CtRouteDetailDTO(){{setNextProcess("5");setProcessSeq(BigDecimal.TEN);setItemNo("5");}},
                new CtRouteDetailDTO(){{setNextProcess("N");setProcessSeq(BigDecimal.TEN);setItemNo("5");}}
        ));
        Assert.assertEquals(0, prodBindingSettingServiceImpl.analyseBindingSetting(Lists.newArrayList("1", "2", "3")));

        PowerMockito.when(prodBindingSettingRepository.getBindingSetting(any())).thenReturn(Lists.newArrayList(
                new ProdBindingSetting(){{setProductCode("5");setItemCode("51");setProcessCode("4");}}
        ));
        PowerMockito.when(CrafttechRemoteService.queryRouteDetailBatch(any(), any(), any())).thenReturn(Lists.newArrayList(
                new CtRouteDetailDTO(){{setNextProcess("5");setProcessSeq(BigDecimal.TEN);setItemNo("5");}},
                new CtRouteDetailDTO(){{setNextProcess("N");setProcessSeq(BigDecimal.TEN);setItemNo("5");}},
                new CtRouteDetailDTO(){{setNextProcess("N");setProcessSeq(BigDecimal.TEN);setItemNo("6");}}
        ));
        Assert.assertEquals(0, prodBindingSettingServiceImpl.analyseBindingSetting(Lists.newArrayList("1", "2", "3")));

        PowerMockito.when(prodBindingSettingRepository.getBindingSetting(any())).thenReturn(Lists.newArrayList(
                new ProdBindingSetting(){{setProductCode("1");setItemCode("11");}},
                new ProdBindingSetting(){{setProductCode("2");setItemCode("21");}},
                new ProdBindingSetting(){{setProductCode("3");setItemCode("31");setProcessCode("1");}},
                new ProdBindingSetting(){{setProductCode("4");setItemCode("41");setProcessCode("2");}},
                new ProdBindingSetting(){{setProductCode("5");setItemCode("51");setProcessCode("5");}}
        ));
        PowerMockito.when(CrafttechRemoteService.queryRouteDetailBatch(any(), any(), any())).thenReturn(Lists.newArrayList(
                new CtRouteDetailDTO(){{setNextProcess("1");setProcessSeq(BigDecimal.ZERO);setItemNo("1");}},
                new CtRouteDetailDTO(){{setNextProcess("3");setProcessSeq(BigDecimal.ONE);setItemNo("1");}},
                new CtRouteDetailDTO(){{setNextProcess("N");setProcessSeq(BigDecimal.TEN);setItemNo("1");}},
                new CtRouteDetailDTO(){{setNextProcess("1");setProcessSeq(BigDecimal.ZERO);setItemNo("2");}},
                new CtRouteDetailDTO(){{setNextProcess("2");setProcessSeq(BigDecimal.ONE);setItemNo("2");}},
                new CtRouteDetailDTO(){{setNextProcess("N");setProcessSeq(BigDecimal.TEN);setItemNo("2");}},
                new CtRouteDetailDTO(){{setNextProcess("4");setProcessSeq(BigDecimal.TEN);setItemNo("4");}},
                new CtRouteDetailDTO(){{setNextProcess("N");setProcessSeq(BigDecimal.TEN);setItemNo("4");}},
                new CtRouteDetailDTO(){{setNextProcess("5");setProcessSeq(BigDecimal.TEN);setItemNo("5");}},
                new CtRouteDetailDTO(){{setNextProcess("N");setProcessSeq(BigDecimal.TEN);setItemNo("5");}}
        ));
        // 获取数据字典装配子工序
        PowerMockito.when(sysLookupValuesRepository.getList(any())).thenReturn(Lists.newArrayList(
                new SysLookupValues(){{setLookupMeaning("3");}}
        ));
        Assert.assertEquals(0, prodBindingSettingServiceImpl.analyseBindingSetting(Lists.newArrayList("1", "2", "3")));
    }

    @Test
    public void deleteBindingByProductList() throws Exception {
        List<String> list = new ArrayList<>();
        list.add("123");
        CfItemBindingDTO cfItemBindingDTO = new CfItemBindingDTO();
        cfItemBindingDTO.setCreatedBy("123");
        prodBindingSettingServiceImpl.deleteBindingByProductList(list, cfItemBindingDTO);
        prodBindingSettingServiceImpl.deleteBindingByProductList(new ArrayList<>(), cfItemBindingDTO);
        List<ProdBindingSetting> settingList = new ArrayList<>();
        ProdBindingSetting setting = new ProdBindingSetting();
        setting.setAttribute1("123");
        settingList.add(setting);
        prodBindingSettingServiceImpl.insertProdBindingSettingBatch(settingList, cfItemBindingDTO);
        prodBindingSettingServiceImpl.insertProdBindingSettingBatch(new ArrayList<>(), cfItemBindingDTO);
        Assert.assertNotNull(list);
        Assert.assertNotNull(cfItemBindingDTO);
    }

    @Test
    public void queryProdBindingInfoByProductCode() throws Exception {

        List<ProdBindingSettingDTO> resultList = new ArrayList<>();
        PowerMockito.when(prodBindingSettingRepository.queryProdBindingInfoByProductCode(Mockito.any())).thenReturn(resultList);
        String productCode = "null";
        prodBindingSettingServiceImpl.queryProdBindingInfoByProductCode(productCode);

        PowerMockito.mockStatic(ObtainRemoteServiceDataUtil.class);
        PowerMockito.mockStatic(CrafttechRemoteService.class);
        PowerMockito.mockStatic(MESHttpHelper.class);

        Page<ProdBindingSettingDTO> page = new Page<>();
        ProdBindingSettingDTO prodBindingSettingDTO=new ProdBindingSettingDTO();
        prodBindingSettingDTO.setProductCode("ZTE1111");
        prodBindingSettingDTO.setItemCode("ZTE1111");
        prodBindingSettingDTO.setCreateBy("00286523");
        prodBindingSettingDTO.setProcessCode("S1015");
        page.setParams(prodBindingSettingDTO);
        ProdBindingSettingDTO resultDto=new ProdBindingSettingDTO();
        resultDto.setProductCode("ZTE1111");
        resultDto.setItemCode("ZTE1111");
        resultDto.setCreateBy("00286523");
        resultDto.setLastUpdatedBy("00286523");
        resultDto.setProcessCode("S1015");
        resultDto.setProcessName("出库");
        resultList.add(resultDto);

        List<BSProcess> bsProcessList=new ArrayList<>();
        BSProcess bsProcess=new BSProcess();
        bsProcess.setProcessCode("S1015");
        bsProcess.setProcessName("出库");
        bsProcessList.add(bsProcess);

        Map<String, HrmPersonInfoDTO> bsPubHrMap =new HashMap<>();
        HrmPersonInfoDTO hrmPersonInfoDTO=new HrmPersonInfoDTO();
        hrmPersonInfoDTO.setEmpName("hl");
        bsPubHrMap.put("00286523",hrmPersonInfoDTO);

        PowerMockito.when(prodBindingSettingRepository.queryProdBindingInfoByProductCode(Mockito.any())).thenReturn(resultList);
        Assert.assertNotNull(prodBindingSettingServiceImpl.queryProdBindingInfoByProductCode(productCode));
        PowerMockito.when(hrmUserCenterService.getHrmPersonInfo(Mockito.any())).thenReturn(bsPubHrMap);
        PowerMockito.when(MESHttpHelper.getHttpRequestHeader()).thenReturn(new HashMap<>());
        PowerMockito.when(commonService.getIMESBasicUrl()).thenReturn("");
        PowerMockito.when(CrafttechRemoteService.getProcessInfo(Mockito.any(),Mockito.any(),Mockito.anyString())).thenReturn(bsProcessList);

    }

    @Test
    public void setBProdProductCode() throws Exception {
        String productCode = "productCode";
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "setBProdProductCode", Collections.emptyList(), productCode);
        Assert.assertEquals("productCode", productCode);
        List<ProdBindingSettingDTO> prodBindingSettingDTOS = Collections.singletonList(new ProdBindingSettingDTO() {{
            setProductCode("123");
        }});
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "setBProdProductCode", prodBindingSettingDTOS, productCode);
        Assert.assertEquals(prodBindingSettingDTOS.get(0).getProductCode(), productCode);
    }

    @Test
    public void lastUpdatedDateFiltering() throws Exception {
        List<ProdBindingSettingDTO> resultList = new ArrayList<>();
        resultList.add(new ProdBindingSettingDTO(){{{
            setLastUpdatedDate(new Date());
        }}});
        ProdBindingSettingDTO dto = new ProdBindingSettingDTO();
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "lastUpdatedDateFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));

        dto.setLastUpdatedDateStart("2021-12-01 00:00:00");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "lastUpdatedDateFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));

        dto.setLastUpdatedDateStart("2031-12-11 00:00:00");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "lastUpdatedDateFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));

        dto.setLastUpdatedDateEnd("2034-12-26 23:59:59");
        dto.setLastUpdatedDateStart("");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "lastUpdatedDateFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));

        dto.setLastUpdatedDateEnd("2020-12-26 23:59:59");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "lastUpdatedDateFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));

        dto.setLastUpdatedDateStart("2021-12-01 00:00:00");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "lastUpdatedDateFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));

        dto.setLastUpdatedDateStart("2021-asd-01 55:00:00");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "lastUpdatedDateFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));
    }


    @Test
    public void createDateFiltering() throws Exception {
        List<ProdBindingSettingDTO> resultList = new ArrayList<>();
        resultList.add(new ProdBindingSettingDTO() {{
            {
                setCreateDate(new Date());
            }
        }});
        ProdBindingSettingDTO dto = new ProdBindingSettingDTO();
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "createDateFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));

        dto.setCreateDateStart("2021-12-01 00:00:00");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "createDateFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));

        dto.setCreateDateStart("2031-12-11 00:00:00");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "createDateFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));

        dto.setCreateDateEnd("2034-12-26 23:59:59");
        dto.setCreateDateStart("");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "createDateFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));

        dto.setCreateDateEnd("2020-12-26 23:59:59");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "createDateFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));

        dto.setCreateDateStart("2021-12-01 00:00:00");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "createDateFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));

        dto.setCreateDateStart("2021-asd-01 55:00:00");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "createDateFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));
    }

    @Test
    public void conditionalFiltering() throws Exception {
        List<ProdBindingSettingDTO> resultList = new ArrayList<>();

        ProdBindingSettingDTO dto = new ProdBindingSettingDTO();
        dto.setCreateDateStart("2021-12-01 00:00:00");
        dto.setCreateDateEnd("2034-12-26 23:59:59");
        dto.setLastUpdatedDateStart("2021-12-01 00:00:00");
        dto.setLastUpdatedDateEnd("2031-12-11 00:00:00");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "conditionalFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isEmpty(resultList));
        resultList.add(new ProdBindingSettingDTO() {{
            {
                setCreateDate(new Date());
                setLastUpdatedDate(new Date());
                setLastUpdatedBy("111");
                setItemCode("code");
            }
        }});
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "conditionalFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));
        dto.setLastUpdatedBy("123");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "conditionalFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));
        dto.setLastUpdatedBy("111");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "conditionalFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));
        dto.setItemCode("abc");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "conditionalFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));
        dto.setItemCode("code");
        Whitebox.invokeMethod(prodBindingSettingServiceImpl, "conditionalFiltering", resultList, dto);
        Assert.assertTrue(CollectionUtils.isNotEmpty(resultList));
    }

}
