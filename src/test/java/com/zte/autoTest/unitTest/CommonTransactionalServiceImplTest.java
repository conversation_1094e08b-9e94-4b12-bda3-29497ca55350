package com.zte.autoTest.unitTest;

import com.zte.application.impl.CommonTransactionalServiceImpl;
import com.zte.domain.model.BsBomHierarchicalDetail;
import com.zte.domain.model.BsBomHierarchicalDetailRepository;
import com.zte.domain.model.BsBomHierarchicalHead;
import com.zte.domain.model.BsBomHierarchicalHeadRepository;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021-06-30 19:09
 */
public class CommonTransactionalServiceImplTest extends BaseTestCase {
    @InjectMocks
    private CommonTransactionalServiceImpl commonTransactionalService;
    @Mock
    private BsBomHierarchicalHeadRepository bsBomHierarchicalHeadRepository;
    @Mock
    private BsBomHierarchicalDetailRepository bsBomHierarchicalDetailRepository;

    @Test
    public void insertBomLevelData() {
        List<BsBomHierarchicalHead> heads = new LinkedList<>();
        BsBomHierarchicalHead a1 = new BsBomHierarchicalHead();
        heads.add(a1);

        List<BsBomHierarchicalDetail> details = new LinkedList<>();
        BsBomHierarchicalDetail b1 = new BsBomHierarchicalDetail();
        details.add(b1);

        List<String> list = new LinkedList<>();
        list.add("123");
        commonTransactionalService.insertBomLevelData(heads, details, list);
        Assert.assertNotNull(details);

        commonTransactionalService.insertBomLevelData(new LinkedList<>(), new LinkedList<>(), list);
    }

}
