package com.zte.autoTest.unitTest;

import com.zte.application.impl.BProdBomChangeDetailServiceImpl;
import com.zte.common.ConstantInterface;
import com.zte.common.utils.ObtainRemoteServiceDataUtil;
import com.zte.domain.model.BProdBomChangeDetailRepository;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(PowerMockRunner.class)
@PrepareForTest({MESHttpHelper.class, ConstantInterface.class,
        ServiceDataBuilderUtil.class, RedisCacheUtils.class, ObtainRemoteServiceDataUtil.class,
        JacksonJsonConverUtil.class, HttpRemoteUtil.class})
public class BProdBomChangeDetailServiceImplTest {

    @InjectMocks
    private BProdBomChangeDetailServiceImpl bProdBomChangeDetailService;

    @Mock
    private BProdBomChangeDetailRepository bProdBomChangeDetailRepository;

    private BProdBomChangeDetailDTO dto;

    @Before
    public void setUp() {
        dto = new BProdBomChangeDetailDTO();
    }

    @Test
    public void testQueryBProdBomDetailChangeList_EmptyList() throws Exception {
        // Given
        PowerMockito.when(bProdBomChangeDetailRepository.queryBProdBomDetailChangeList(any()))
                .thenReturn(Collections.emptyList());
        dto.setProductCode("");
        dto.setProdplanIdList(Collections.emptyList());
        // When
        List<BProdBomChangeDetailDTO> result = bProdBomChangeDetailService.queryBProdBomDetailChangeList(dto);

        // Then
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryBProdBomDetailChangeList_SingleElementList() throws Exception {
        // Given
        BProdBomChangeDetailDTO expectedDto = new BProdBomChangeDetailDTO();
        PowerMockito.when(bProdBomChangeDetailRepository.queryBProdBomDetailChangeList(any()))
                .thenReturn(Collections.singletonList(expectedDto));

        // When
        List<String> str = new ArrayList<>();
        str.add("123");
        dto.setProdplanIdList(str);
        List<BProdBomChangeDetailDTO> result = bProdBomChangeDetailService.queryBProdBomDetailChangeList(dto);
        // Then
        assertEquals(1, result.size());

        dto.setProductCode("123");
        dto.setProdplanIdList(Collections.emptyList());
        result = bProdBomChangeDetailService.queryBProdBomDetailChangeList(dto);
        // Then
        assertEquals(1, result.size());
        dto.setProdplanIdList(str);
        result = bProdBomChangeDetailService.queryBProdBomDetailChangeList(dto);
        // Then
        assertEquals(1, result.size());
    }

    /* Started by AICoder, pid:kd966bc774q9e381493409f6e04fcd14c309050c */
    @Test
    public void testSelectMBomDetailChangeByHeaderId() {
        BProdBomChangeDetailDTO params = new BProdBomChangeDetailDTO();
        params.setBomHeaderId("1212Q5436QREGTQRW324512346");
        params.setItemCode("013040100001");
        List<BProdBomChangeDetailDTO> bProdBomChangeDetailDTOs = new ArrayList<>();
        bProdBomChangeDetailDTOs.add(params);
        PowerMockito.when(bProdBomChangeDetailRepository.selectMBomDetailChangeByHeaderId(any())).thenReturn(bProdBomChangeDetailDTOs);
        Page<BProdBomChangeDetailDTO> bProdBomChangeDetailDTOPage = bProdBomChangeDetailService.selectMBomDetailChangeByHeaderId(params);
        Assert.assertEquals(bProdBomChangeDetailDTOPage.getRows().get(0).getItemCode(), "013040100001");
    }
    /* Ended by AICoder, pid:kd966bc774q9e381493409f6e04fcd14c309050c */

    @Test
    public void queryMBomDetailChangeByProdplanId() throws Exception {
        // When
        List<BProdBomChangeDetailDTO> result = bProdBomChangeDetailService.queryMBomDetailChangeByProdplanId(null);

        // Then
        assertEquals(0, result.size());

        result = bProdBomChangeDetailService.queryMBomDetailChangeByProdplanId(dto);

        // Then
        assertEquals(0, result.size());
        dto.setProdplanIdList(Arrays.asList("1"));
        PowerMockito.when(bProdBomChangeDetailRepository.queryMBomDetailChangeByProdplanId(any())).thenReturn(Arrays.asList(new BProdBomChangeDetailDTO()));
        result = bProdBomChangeDetailService.queryMBomDetailChangeByProdplanId(dto);
        // Then
        assertEquals(1, result.size());
        PowerMockito.when(bProdBomChangeDetailRepository.queryMBomDetailChangeByProdplanId(any())).thenReturn(null);
        result = bProdBomChangeDetailService.queryMBomDetailChangeByProdplanId(dto);
        // Then
        assertEquals(0, result.size());
    }

    /* Started by AICoder, pid:09ca444e6d519eb144c308b3807c9130f026cade */
    @Test
    public void testDeleteByHeaderIds() {
        // 测试删除空列表的情况
        int count = bProdBomChangeDetailService.deleteByProdplanIds(Collections.emptyList(), "");
        assertEquals(0, count);

        // 模拟删除操作返回1
        when(bProdBomChangeDetailRepository.deleteByProdplanIds(any(), any()))
                .thenReturn(1);

        count = bProdBomChangeDetailService.deleteByProdplanIds(Arrays.asList("2"), "2");
        assertEquals(1, count);
    }

    /* Ended by AICoder, pid:09ca444e6d519eb144c308b3807c9130f026cade */

    /* Started by AICoder, pid:i69b3xe782l259614d9f091b40024d342ae40996 */
    @Test
    public void testBatchInsert() {
        // 测试删除空列表的情况
        List<BProdBomChangeDetailDTO> emptyList = Collections.emptyList();
        bProdBomChangeDetailService.batchInsert(emptyList);
        assertEquals(0, emptyList.size());

        // 测试插入单个元素的情况
        BProdBomChangeDetailDTO detail = new BProdBomChangeDetailDTO();
        List<BProdBomChangeDetailDTO> singleItemList = Arrays.asList(detail);
        bProdBomChangeDetailService.batchInsert(singleItemList);
        assertEquals(1, singleItemList.size());
    }

    /* Ended by AICoder, pid:i69b3xe782l259614d9f091b40024d342ae40996 */
}
