package com.zte.autoTest.unitTest.spare;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.builder.ExcelReaderSheetBuilder;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.zte.application.ApprovalProcessInfoService;
import com.zte.application.MailLogService;
import com.zte.application.impl.parts.SparePartAllocationServiceImpl;
import com.zte.application.kafka.component.KafkaProdListener;
import com.zte.application.kafka.producer.CommonProducer;
import com.zte.common.model.MessageId;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.domain.model.ApprovalProcessInfoRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.domain.model.part.SparePartDetailRepository;
import com.zte.domain.model.part.SparePartHeadRepository;
import com.zte.interfaces.dto.ApprovalProcessInfoEntityDTO;
import com.zte.interfaces.dto.spare.SparePartAllocationDetailDTO;
import com.zte.interfaces.dto.spare.SparePartAllocationHeadDTO;
import com.zte.interfaces.dto.spare.SparePartAllocationItemDetailDTO;
import com.zte.interfaces.dto.spare.SparePartAllocationQueryDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.message.SpringKafkaProducer;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.util.BigExcelProcesser;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EasyExcelUtils;
import com.zte.springbootframe.util.FileUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-06-06 14:30
 */
@PrepareForTest({EasyExcelUtils.class,EasyExcelFactory.class,SpringContextUtil.class, FileUtils.class})
public class SparePartAllocationServiceImplTest extends BaseTestCase {
    @InjectMocks
    private SparePartAllocationServiceImpl service;
    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;
    @Mock
    private SparePartHeadRepository sparePartHeadRepository;
    @Mock
    private SparePartDetailRepository sparePartDetailRepository;
    @Mock
    private ApprovalProcessInfoRepository approvalProcessInfoRepository;
    @Mock
    private CommonProducer commonProducer;
    @Mock
    private MultipartFile file;
    @Mock
    ExcelReaderBuilder read ;
    @Mock
    ExcelReaderSheetBuilder builder;
    @Mock
    HttpServletResponse response;
    @Mock
    ExcelWriter excelWriter;
    @Mock
    ExcelWriterBuilder write;
    @Mock
    ServletOutputStream outputStream;
    @Mock
    ExcelWriterSheetBuilder excelWriterSheetBuilder;
    @Mock
    LocaleMessageSourceBean lmb;
    @Mock
    private ApprovalProcessInfoService approvalProcessInfoService;
    @Mock
    private CloudDiskHelper cloudDiskHelper;
    @Mock
    BigExcelProcesser bigExcelProcesser;
    @Before
    public void init() throws Exception {
        PowerMockito.mockStatic(EasyExcelUtils.class);
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.mockStatic(EasyExcelFactory.class);
        PowerMockito.mockStatic(FileUtils.class);
        PowerMockito.whenNew(BigExcelProcesser.class).withNoArguments().thenReturn(bigExcelProcesser);
    }

    @Test
    public void querySpareHeadAndDetails() {
        SparePartAllocationHeadDTO headDTO = new SparePartAllocationHeadDTO();
        List<ApprovalProcessInfoEntityDTO> approveList = new LinkedList<>();
        ApprovalProcessInfoEntityDTO a1 = new ApprovalProcessInfoEntityDTO();
        approveList.add(a1);
        headDTO.setApproveList(approveList);
        headDTO.setFactoryId("53");
        try {
            service.querySpareHeadAndDetails(headDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_IS_NULL, e.getMessage());
        }
        headDTO.setBillNo("123");
        try {
            service.querySpareHeadAndDetails(headDTO);
        } catch (Exception e) {
            Assert.assertTrue(MessageId.SPARE_PART_BILL_LOST.equals(e.getMessage()));
        }

        PowerMockito.when(sparePartHeadRepository.querySpareHeadAndDetails(Mockito.any()))
                .thenReturn(headDTO);
        service.querySpareHeadAndDetails(headDTO);
    }

    @Test
    public void updateSpareHeadAndDetails() {
        SparePartAllocationHeadDTO headDTO = new SparePartAllocationHeadDTO();
        try {
            service.updateSpareHeadAndDetails(headDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }

        headDTO.setBillNo("123");
        try {
            service.updateSpareHeadAndDetails(headDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
        headDTO.setPartType("1");
        try {
            service.updateSpareHeadAndDetails(headDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
        headDTO.setTransferFactoryId("54");
        try {
            service.updateSpareHeadAndDetails(headDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
        headDTO.setReceivingFactoryId("54");
        try {
            service.updateSpareHeadAndDetails(headDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPARE_PART_FACTORY_ERROR, e.getMessage());
        }
        headDTO.setReceivingFactoryId("53");
        List<SparePartAllocationDetailDTO> detailList = new LinkedList<>();
        SparePartAllocationDetailDTO a1 = new SparePartAllocationDetailDTO();
        a1.setPartName("123");
        a1.setQuantity(2);
        detailList.add(a1);
        SparePartAllocationDetailDTO a2 = new SparePartAllocationDetailDTO();
        a2.setQuantity(2);
        a2.setDetailId("123");
        detailList.add(a2);
        headDTO.setDetailList(detailList);

        try {
            service.updateSpareHeadAndDetails(headDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }

        List<ApprovalProcessInfoEntityDTO> approveList = new LinkedList<>();
        ApprovalProcessInfoEntityDTO b1 = new ApprovalProcessInfoEntityDTO();
        b1.setId("123");
        b1.setNodeCode("1");
        b1.setApproverId("23");
        b1.setNodeName("1");
        approveList.add(b1);
        ApprovalProcessInfoEntityDTO b2 = new ApprovalProcessInfoEntityDTO();
        b2.setNodeCode("1");
        b2.setNodeName("2");
        approveList.add(b2);
        headDTO.setApproveList(approveList);
        try {
            service.updateSpareHeadAndDetails(headDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPARE_PART_DETAIL_ERROR, e.getMessage());
        }

        a2.setPartName("567");
        try {
            service.updateSpareHeadAndDetails(headDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPARE_PART_APPROVE_ERROR, e.getMessage());
        }

        b2.setApproverId("23");
        try {
            service.updateSpareHeadAndDetails(headDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPARE_PART_BILL_LOST, e.getMessage());
        }
        SparePartAllocationHeadDTO originHead = new SparePartAllocationHeadDTO();
        List<ApprovalProcessInfoEntityDTO> originApproveList = new LinkedList<>();
        ApprovalProcessInfoEntityDTO b3 = new ApprovalProcessInfoEntityDTO();
        b3.setId("123");
        b3.setNodeCode("1");
        b3.setApproverId("234");
        b3.setNodeName("1");
        ApprovalProcessInfoEntityDTO b4 = new ApprovalProcessInfoEntityDTO();
        b4.setId("4");
        originApproveList.add(b3);
        originApproveList.add(b4);
        originHead.setApproveList(originApproveList);
        List<SparePartAllocationDetailDTO> originList = new LinkedList<>();
        SparePartAllocationDetailDTO a3 = new SparePartAllocationDetailDTO();
        a3.setQuantity(2);
        a3.setPartName("44");
        a3.setDetailId("123");
        SparePartAllocationDetailDTO a4 = new SparePartAllocationDetailDTO();
        a4.setDetailId("566");
        originList.add(a3);
        originList.add(a4);
        originHead.setDetailList(originList);
        PowerMockito.when(sparePartHeadRepository.querySpareHeadAndDetails(Mockito.any()))
                .thenReturn(originHead);
        try {
            service.updateSpareHeadAndDetails(headDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPARE_PART_STATUS_ERROR, e.getMessage());
        }

        originHead.setStatus("0");
        headDTO.setFactoryId("51");
        service.updateSpareHeadAndDetails(headDTO);

    }


    @Test
    public void savePartHeadAndDetails() throws Exception {

        PowerMockito.when(SpringContextUtil.getBean("sparePartAllocationServiceImpl", SparePartAllocationServiceImpl.class))
                .thenReturn(service);
        SparePartAllocationHeadDTO headDTO = new SparePartAllocationHeadDTO();
        headDTO.setBillNo("BJDB");
        headDTO.setFactoryId("56");
        headDTO.setStatus("1");
        headDTO.setTransferFactoryId("53");
        headDTO.setReceivingFactoryId("52");
        headDTO.setPartType("0");
        List<SparePartAllocationDetailDTO> detailList = new LinkedList<>();
        SparePartAllocationDetailDTO a1 = new SparePartAllocationDetailDTO();
        a1.setPartName("45");
        a1.setQuantity(2);
        detailList.add(a1);
        headDTO.setDetailList(detailList);
        List<ApprovalProcessInfoEntityDTO> appList = new LinkedList<>();
        ApprovalProcessInfoEntityDTO b1 = new ApprovalProcessInfoEntityDTO();
        b1.setNodeName("de");
        b1.setNodeCode("d");
        b1.setApproverId("45");
        appList.add(b1);
        headDTO.setApproveList(appList);

        service.savePartHeadAndDetails(headDTO);
        Assert.assertNotNull(headDTO);
    }


    @Test
    public void updateBillHeadStatus() throws Exception{
        SparePartAllocationHeadDTO headDTO = new SparePartAllocationHeadDTO();
        SparePartAllocationHeadDTO head = new SparePartAllocationHeadDTO();
        head.setStatus("1");
        List<SparePartAllocationDetailDTO> detailList = new LinkedList<>();
        SparePartAllocationDetailDTO a1 = new SparePartAllocationDetailDTO();
        detailList.add(a1);
        head.setDetailList(detailList);
        PowerMockito.when(sparePartHeadRepository.querySpareHeadAndDetails(Mockito.any()))
                .thenReturn(head)
        ;
        PowerMockito.whenNew(BigExcelProcesser.class).withNoArguments().thenReturn(bigExcelProcesser);
        try {
            service.updateBillHeadStatus(headDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
        headDTO.setBillNo("123");
        try {
            service.updateBillHeadStatus(headDTO);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.PARAMS_ERROR, e.getMessage());
        }
        headDTO.setStatus("2");
        headDTO.setCreateBy("123123");
        try {
            service.updateBillHeadStatus(headDTO);
        } catch (Exception e) {
        }
        headDTO.setStatus("1");
        try {
            service.updateBillHeadStatus(headDTO);
        } catch (Exception e) {
        }
    }

    @Test
    public void getEmailEntity () throws Exception {
        SparePartAllocationHeadDTO headDTO = new SparePartAllocationHeadDTO();
        headDTO.setBillNo("123");
        headDTO.setEmailCc("123");
        PowerMockito.when(sparePartHeadRepository.querySpareHeadAndDetails(Mockito.any())).thenReturn(headDTO);
        try {
            Whitebox.invokeMethod(service, "getEmailEntity", headDTO);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void downLoadExcelTemplate() throws Exception {
        PowerMockito.when(response.getOutputStream()).thenReturn(outputStream);
        PowerMockito.when(EasyExcelFactory.write(outputStream, SparePartAllocationDetailDTO.class))
                .thenReturn(write);
        PowerMockito.when(write.excelType(ExcelTypeEnum.XLSX)).thenReturn(write);
        PowerMockito.when(write.build()).thenReturn(excelWriter);
        PowerMockito.when(EasyExcelFactory.writerSheet(Mockito.any(), Mockito.any()))
                .thenReturn(excelWriterSheetBuilder);

        service.downLoadExcelTemplate(response);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void checkInputData() {

        PowerMockito.when(SpringContextUtil.getBean(BusinessConstant.RESOURCE_SERVICE_NAME))
                .thenReturn(lmb);
        List<SparePartAllocationDetailDTO> resultList = new LinkedList<>();
        SparePartAllocationDetailDTO a1 = new SparePartAllocationDetailDTO();
        resultList.add(a1);

        SparePartAllocationDetailDTO a2 = new SparePartAllocationDetailDTO();
        a2.setPartName("234");
        a2.setQuantityStr("5.6");
        resultList.add(a2);
        Assert.assertNotNull(service.checkInputData(resultList));
    }

    @Test
    public void newSparePartItemDetail() throws Exception{
        try{
            service.newSparePartItemDetail(new SparePartAllocationItemDetailDTO());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ITEM_DETAIL_CAN_NOT_BE_NUL, e.getMessage());
        }
        SparePartAllocationItemDetailDTO detailDTO = new SparePartAllocationItemDetailDTO();
        detailDTO.setDetailId("123");
        detailDTO.setFirstFlag(true);
        service.newSparePartItemDetail(detailDTO);
    }

    @Test
    public void getSpareOutBoundList() throws Exception{
        try{
            service.getSpareOutBoundList(new SparePartAllocationHeadDTO());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SPARE_PART_BILL_NO_NOT_NULL, e.getMessage());
        }
        SparePartAllocationHeadDTO headDTO = new SparePartAllocationHeadDTO();
        headDTO.setBillNo("123");
        service.getSpareOutBoundList(headDTO);
        List<SparePartAllocationDetailDTO> detailDTOS = new ArrayList<>();
        SparePartAllocationDetailDTO detailDTO = new SparePartAllocationDetailDTO();
        detailDTO.setQuantity(5);
        detailDTO.setItemQuantity(1);
        detailDTOS.add(detailDTO);
        headDTO.setDetailList(detailDTOS);
        headDTO.setPartType("52");
        headDTO.setTransferFactoryName("52");
        headDTO.setReceivingFactoryName("52");
        PowerMockito.when(sparePartHeadRepository.getSpareOutBoundList(Mockito.any())).thenReturn(headDTO);
        service.getSpareOutBoundList(headDTO);
        List<SysLookupValues> sysLookupValues = new ArrayList<>();
        SysLookupValues values = new SysLookupValues();
        values.setLookupMeaning("52");
        values.setDescriptionChin("cs");
        sysLookupValues.add(values);
        PowerMockito.when(sysLookupValuesRepository.selectValuesByType(Mockito.any())).thenReturn(sysLookupValues);
        service.getSpareOutBoundList(headDTO);
    }
    @Test
    public void querySpareInfoByPartCode() throws Exception {
        try{
            service.querySpareInfoByPartCode("");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SN_IS_NULL, e.getMessage());
        }
        String partCode="code111";
        List<SparePartAllocationQueryDTO> list = new ArrayList<>();
        SparePartAllocationQueryDTO dto = new SparePartAllocationQueryDTO();
        dto.setPartCode("code111");
        dto.setBillStatus("5");
        dto.setAllocationStatus("5");
        list.add(dto);
        PowerMockito.when(sparePartHeadRepository.querySpareInfoByPartCode(Mockito.any())).thenReturn(list);
        service.querySpareInfoByPartCode(partCode);
    }

}
