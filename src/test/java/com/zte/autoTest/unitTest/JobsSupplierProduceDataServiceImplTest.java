package com.zte.autoTest.unitTest;

import com.zte.application.impl.JobsSupplierProduceDataServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.domain.model.JobsSupplierProduceDataRepository;
import com.zte.interfaces.dto.JobsSupplierProduceDataDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class JobsSupplierProduceDataServiceImplTest extends BaseTestCase {

    @InjectMocks
    JobsSupplierProduceDataServiceImpl service;

    @Mock
    JobsSupplierProduceDataRepository jobsSupplierProduceDataRepository;

    @Test
    public void addMsg() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        try {
            service.addMsg(new ArrayList<String>(),new Exception("222"));
        }catch (Exception e){
            Assert.assertEquals(null,e.getMessage());
        }

    }

    @Test
    public void createExcelTest() throws Exception {
        JobsSupplierProduceDataDTO dto = new JobsSupplierProduceDataDTO();
        Long maxRows = 10L;
        PowerMockito.when(jobsSupplierProduceDataRepository.getCount(Mockito.anyMap())).thenReturn(maxRows);
        dto.setExportType(0);
        service.createExcel(dto);

        dto.setExportType(1);
        service.createExcel(dto);
        Assert.assertNotNull(dto);
    }


}