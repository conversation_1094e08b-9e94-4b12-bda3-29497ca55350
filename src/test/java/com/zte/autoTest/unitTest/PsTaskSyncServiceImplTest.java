package com.zte.autoTest.unitTest;

import com.google.common.collect.Lists;
import com.zte.application.sncabind.impl.PsTaskSyncServiceImpl;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.domain.model.sncabind.PsTaskRepository;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.junit.Assert.*;
import static org.mockito.Matchers.any;

@RunWith(PowerMockRunner.class)
public class PsTaskSyncServiceImplTest extends BaseTestCase {

    @InjectMocks
    PsTaskSyncServiceImpl service;

    @Mock
    private PsTaskRepository repository;

    @Test
    public void mergePsTask() {
        service.mergePsTask(Lists.newArrayList(new PsTask(){{setProdplanId("1");}}));
        PowerMockito.when(repository.getExistingPlanId(any())).thenReturn(Lists.newArrayList("1"));
        Assert.assertNotNull(service.mergePsTask(Lists.newArrayList(new PsTask(){{setProdplanId("1");}})));
    }
}