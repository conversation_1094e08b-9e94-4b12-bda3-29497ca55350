package com.zte.autoTest.unitTest;

import com.zte.application.impl.CfCheckHistoryServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.domain.model.CfCheckHistory;
import com.zte.domain.model.CfCheckHistoryRepository;
import com.zte.domain.model.CfInventoryRepository;
import com.zte.interfaces.dto.CfCheckHistoryDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Title: 盘点历史记录
 * @Description:
 * @date 2020/11/25
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CfCheckHistoryServiceImpl.class,CfInventoryRepository.class,
        CfCheckHistoryRepository.class, CommonUtils.class})
public class CfCheckHistoryServiceImplTest extends BaseTestCase {

    @InjectMocks
    private CfCheckHistoryServiceImpl cfCheckHistoryServiceImpl;

    @Mock
    private CfInventoryRepository cfInventoryRepository;

    @Mock
    private CfCheckHistoryRepository cfCheckHistoryRepository;

    @Test
    public void itemCheckHistoryTest() throws Exception{
        PowerMockito.mockStatic(CommonUtils.class);
        CfCheckHistoryDTO historyDTO = new CfCheckHistoryDTO()
            .setSupplierNo("85658950")
            .setTaskNo("ZTE484257793")
            .setStartTime(new Date())
            .setEndTime(new Date());
        Map<String, Object> map = CommonUtils.transBean2Map(historyDTO);
        List<CfCheckHistory> cfCheckHistoryList = new ArrayList<>();
        PowerMockito.when(cfCheckHistoryRepository.getPage(map)).thenReturn(cfCheckHistoryList);
        PowerMockito.when(cfCheckHistoryRepository.getCount(map)).thenReturn(1L);
        PowerMockito.when(cfInventoryRepository.getPsTaskByFactoryId("ZTE484257793")).thenReturn("75");
        PowerMockito.when(cfInventoryRepository.getCfFactoryId("85658950")).thenReturn("75");
        cfCheckHistoryServiceImpl.getCount(historyDTO);
        cfCheckHistoryServiceImpl.getCfCheckHistoryPage(historyDTO);
        historyDTO.setSupplierNo("");
        Assert.assertNotNull(cfCheckHistoryServiceImpl.getCfCheckHistoryPage(historyDTO));
    }

}
