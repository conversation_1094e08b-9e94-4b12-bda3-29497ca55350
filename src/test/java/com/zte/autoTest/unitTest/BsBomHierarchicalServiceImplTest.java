package com.zte.autoTest.unitTest;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.builder.ExcelWriterSheetBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.zte.application.impl.BsBomHierarchicalServiceImpl;
import com.zte.domain.model.BProdBomChangeDetailRepository;
import com.zte.domain.model.BsBomHierarchicalDetail;
import com.zte.domain.model.BsBomHierarchicalDetailRepository;
import com.zte.domain.model.BsBomHierarchicalHeadRepository;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.interfaces.dto.BsBomHierarchicalDetailDTO;
import com.zte.springbootframe.util.AsyncExportFileCommonService;
import com.zte.springbootframe.util.FileUtils;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2020/12/14 17
 * @description:
 */
@PrepareForTest({FileUtils.class, EasyExcel.class, EasyExcelFactory.class})
public class BsBomHierarchicalServiceImplTest extends BaseTestCase {
    @InjectMocks
    private BsBomHierarchicalServiceImpl bsBomHierarchicalServiceImpl;
    @Mock
    private BsBomHierarchicalDetailRepository bsBomHierarchicalDetailRepository;
    @Mock
    private BsBomHierarchicalHeadRepository bsBomHierarchicalHeadRepository;
    @Mock
    private AsyncExportFileCommonService asyncExportFileCommonService;
    @Mock
    ExcelWriter writer;
    @Mock
    WriteSheet build;
    @Mock
    ExcelWriterSheetBuilder excelWriterSheetBuilder;
    @Mock
    ExcelWriterBuilder writerBuilder;

    @Mock
    private BProdBomChangeDetailRepository bProdBomChangeDetailRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(FileUtils.class);
        PowerMockito.mockStatic(EasyExcel.class);
        PowerMockito.mockStatic(EasyExcelFactory.class);
        PowerMockito.when(FileUtils.createFilePathAndCheck(Mockito.anyString())).thenReturn("124");
        PowerMockito.when(EasyExcelFactory.writerSheet(Mockito.any(), Mockito.any())).thenReturn(excelWriterSheetBuilder);
        PowerMockito.when(excelWriterSheetBuilder.build()).thenReturn(build);
        PowerMockito.when(EasyExcel.write(Mockito.anyString(), Mockito.any())).thenReturn(writerBuilder);
        PowerMockito.when(writerBuilder.build()).thenReturn(writer);
    }

    @Test
    public void selectBatchBomByBomCode() {
        List<BsBomHierarchicalDetailDTO> list = new LinkedList<>();
        BsBomHierarchicalDetailDTO lis = new BsBomHierarchicalDetailDTO();
        list.add(lis);
        Assert.assertNotNull(bsBomHierarchicalServiceImpl.selectBatchBomByBomCode(list));
    }

    /* Started by AICoder, pid:4cf09rb575721d7147690904d01d954f46107ce9 */
    @Test
    public void testSetMbomInfo() {
        List<BsBomHierarchicalDetailDTO> dto = new ArrayList<>();
        BsBomHierarchicalDetailDTO bsBomHierarchicalDetailDTO = new BsBomHierarchicalDetailDTO();
        bsBomHierarchicalDetailDTO.setProdplanId("7011619");
        bsBomHierarchicalDetailDTO.setBomCode("139571751152ZTA");
        dto.add(bsBomHierarchicalDetailDTO);
        BsBomHierarchicalDetailDTO bsBomHierarchicalDetailDTO1 = new BsBomHierarchicalDetailDTO();
        bsBomHierarchicalDetailDTO1.setProdplanId("7011619");
        bsBomHierarchicalDetailDTO1.setBomCode("239571751152ZTA");
        dto.add(bsBomHierarchicalDetailDTO1);
        BsBomHierarchicalDetailDTO bsBomHierarchicalDetailDTO2 = new BsBomHierarchicalDetailDTO();
        dto.add(bsBomHierarchicalDetailDTO2);
        List<BProdBomChangeDetailDTO> bProdBomChangeDetails = new ArrayList<>();
        BProdBomChangeDetailDTO bProdBomChangeDetailDTO = new BProdBomChangeDetailDTO();
        bProdBomChangeDetailDTO.setOriginalProductCode("139571751152ZTA");
        bProdBomChangeDetailDTO.setProductCode("139571751152ZTA_1");
        bProdBomChangeDetails.add(bProdBomChangeDetailDTO);
        PowerMockito.when(bProdBomChangeDetailRepository.queryBProdBomDetailChangeList(Mockito.any())).thenReturn(bProdBomChangeDetails);
        bsBomHierarchicalServiceImpl.setMbomInfo(dto);
        PowerMockito.when(bProdBomChangeDetailRepository.queryBProdBomDetailChangeList(Mockito.any())).thenReturn(null);
        bsBomHierarchicalServiceImpl.setMbomInfo(dto);
        Assert.assertEquals(dto.get(0).getBomCode(), "139571751152ZTA");
    }
    /* Ended by AICoder, pid:4cf09rb575721d7147690904d01d954f46107ce9 */


    @Test
    public void selectHeadAndDetailBatch() {
        List<String> list = new LinkedList<>();
        list.add("123");
        Assert.assertNotNull(bsBomHierarchicalServiceImpl.selectHeadAndDetailBatch(list));
    }

    @Test
    public void selectBsBomHierarchical() throws Exception {
        BsBomHierarchicalDetail record = new BsBomHierarchicalDetail();
        PowerMockito.when(bsBomHierarchicalDetailRepository.selectBsBomHierarchical(Mockito.any()))
                .thenReturn(null);
        bsBomHierarchicalServiceImpl.exportBsBomHierarchical(record);

        PowerMockito.when(bsBomHierarchicalDetailRepository.selectBsBomHierarchical(Mockito.any()))
                .thenReturn(new LinkedList<>());
        bsBomHierarchicalServiceImpl.exportBsBomHierarchical(record);
        Assert.assertNotNull(record);

    }

}
