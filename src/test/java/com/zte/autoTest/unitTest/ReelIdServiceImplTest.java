package com.zte.autoTest.unitTest;

import com.zte.application.impl.ReelIdServiceImpl;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PkCodeInfoRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import static org.mockito.Matchers.any;

/**
 *
 * @Author:
 * @Date: 2020/11/6 11:11
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({DatawbRemoteService.class})
public class ReelIdServiceImplTest  extends BaseTestCase {
	@InjectMocks
	private ReelIdServiceImpl service;

	@Mock
	private PkCodeInfoRepository pkCodeInfoRepository;

	@Test
	public void registerBoxReelId() throws MesBusinessException {
		PowerMockito.mockStatic(DatawbRemoteService.class);
		PowerMockito.when(DatawbRemoteService.getItemNameByItemNo(any())).thenReturn("1");
		try{
			service.registerBoxReelId(new PkCodeInfo(){{setItemCode("1");}});
		} catch (Exception e) {
			Assert.assertEquals(null, e.getMessage());
		}
	}
}
