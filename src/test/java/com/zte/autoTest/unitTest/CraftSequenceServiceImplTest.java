package com.zte.autoTest.unitTest;

import com.zte.application.impl.craftTech.CraftSequenceServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.craftTech.CraftSequence;
import com.zte.domain.model.craftTech.CraftSequenceRepository;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;

import java.util.Date;

import static org.mockito.Matchers.anyObject;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;

public class CraftSequenceServiceImplTest extends BaseTestCase {

    @InjectMocks
    private CraftSequenceServiceImpl service;

    @Mock
    private CraftSequenceRepository repository;

    @Test
    public void getCraftSequence()throws Exception {
        CraftSequence craftSequence=new CraftSequence();
        craftSequence.setCraftNo("CF001");
        craftSequence.setCurrData(new Date());
        PowerMockito.when(repository.selectCraftSequenceAll()).thenReturn(craftSequence);
        Assert.assertNotNull(service.getCraftSequence("55"));
    }

    @Test
    public void setCraftSequenceRepository() throws Exception {
        service.setCraftSequenceRepository(repository);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void insertCraftSequence() throws Exception {
        service.insertCraftSequence(new CraftSequence());
        verify(repository,times(1)).insertCraftSequence(anyObject());
    }

    @Test
    public void updateCraftSequence() throws Exception {
        service.updateCraftSequence(new CraftSequence());
        verify(repository,times(1)).updateCraftSequence(anyObject());
    }

    @Test
    public void selectCraftSequenceAll() throws Exception {
        service.selectCraftSequenceAll();
        verify(repository,times(1)).selectCraftSequenceAll();
    }

}