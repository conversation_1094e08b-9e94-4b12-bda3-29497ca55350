package com.zte.autoTest.unitTest;

import com.zte.application.impl.ReelIdServiceImpl;
import com.zte.application.impl.SmtLineOeeServiceImpl;
import com.zte.domain.model.PkCodeInfo;
import com.zte.domain.model.PkCodeInfoRepository;
import com.zte.domain.model.SmtLineOee;
import com.zte.domain.model.SmtLineOeeRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import static org.mockito.Matchers.any;

/**
 *
 * @Author:
 * @Date: 2020/11/6 11:11
 */
@RunWith(PowerMockRunner.class)
public class SmtLineOeeServiceImplTest extends BaseTestCase {
	@InjectMocks
	private SmtLineOeeServiceImpl smtLineOeeService;

	@Mock
	private SmtLineOeeRepository smtLineOeeRepository;

	@Test
	public void insertSmtLineOee() throws MesBusinessException {
		List<SmtLineOee> smtLineOeeList = new ArrayList<>();
		SmtLineOee smtLineOee = new SmtLineOee();
		smtLineOee.setId(UUID.randomUUID().toString());
		smtLineOee.setLineCode("SMT-1");
		smtLineOee.setCalculateDate("2021-01-23 00:00:00");
		smtLineOee.setWorkshopCode("cs0001");
		smtLineOee.setWorkshopName("长沙生产部");
		smtLineOee.setPosition("1234");
		smtLineOee.setFactoryId(new BigDecimal("51"));
		smtLineOee.setOrgId(new BigDecimal("4437"));
		smtLineOee.setIsDoubleTrack("Y");
		smtLineOee.setLocationName("123");
		smtLineOee.setProductionEfficiency(new BigDecimal("10"));
		smtLineOee.setOee(new BigDecimal("10"));
		smtLineOee.setTimeMobility(new BigDecimal("10"));
		smtLineOee.setCreateBy("10275524");
		smtLineOee.setRemark("备注1");
		smtLineOee.setUpdatedBy("10275525");
		smtLineOeeList.add(smtLineOee);
		SmtLineOee smtLineOee2 = new SmtLineOee();
		smtLineOee2.setId(UUID.randomUUID().toString());
		smtLineOee2.setLineCode("SMT-1");
		smtLineOee2.setCalculateDate("2021-01-23 00:00:00");
		smtLineOee2.setWorkshopCode("cs0001");
		smtLineOee2.setWorkshopName("长沙生产部");
		smtLineOee2.setPosition("1234");
		smtLineOee2.setFactoryId(new BigDecimal("51"));
		smtLineOee2.setOrgId(new BigDecimal("4437"));
		smtLineOee2.setIsDoubleTrack("Y");
		smtLineOee2.setLocationName("123");
		smtLineOee2.setProductionEfficiency(new BigDecimal("10"));
		smtLineOee2.setOee(new BigDecimal("10"));
		smtLineOee2.setTimeMobility(new BigDecimal("10"));
		smtLineOee2.setCreateBy("10275524");
		smtLineOee2.setRemark("备注1");
		smtLineOee2.setUpdatedBy("10275525");
		smtLineOeeList.add(smtLineOee2);
		PowerMockito.when(smtLineOeeRepository.selectByLineCodeAndTime(any() , any() , any(), any())).thenReturn(Arrays.asList(smtLineOee)).thenReturn(new ArrayList<>());
		PowerMockito.when(smtLineOeeRepository.updateByPrimaryKey(any())).thenReturn(1);
		PowerMockito.when(smtLineOeeRepository.insertSelective(any())).thenReturn(1);
		Assert.assertNotNull(smtLineOeeService.insertSmtLineOee(smtLineOeeList));
	}
}
