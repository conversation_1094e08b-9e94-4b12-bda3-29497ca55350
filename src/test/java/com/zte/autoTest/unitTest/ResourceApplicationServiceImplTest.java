package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.SpTagsParamService;
import com.zte.application.impl.ResourceApplicationServiceImpl;
import com.zte.application.impl.ResourceDetailServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.enums.ResourceStatusEnum;
import com.zte.common.excel.ExcelCommonUtils;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.common.utils.MpConstant;
import com.zte.domain.model.*;
import com.zte.interfaces.dto.*;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.FileUtils;
import com.zte.uac.ssoclient.util.MD5;
import com.zte.util.BaseTestCase;
import org.apache.commons.io.IOUtils;
import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.mock.web.MockMultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;

import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyInt;
import static org.mockito.Matchers.anyString;

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class,MesBusinessException.class,ExcelCommonUtils.class, FileUtils.class, ResourceApplicationServiceImpl.class})
public class ResourceApplicationServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ResourceApplicationServiceImpl resourceApplicationService;

    @Mock
    private ResourceApplicationRepository resourceApplicationRepository;

    @Mock
    private ResourceInfoRepository resourceInfoRepository;

    @Mock
    private ResourceDetailRepository resourceDetailRepository;
    @Mock
    private SpSpecialityParamRepository spSpecialityParamRepository;
    @Mock
    private ResourceInfoDetailRepository resourceInfoDetailRepository;


    @Mock
    private RedisTemplate<String,String > redisTemplate;

    @Mock
    private ValueOperations<String , String> valueOperations;

    @Mock
    private ResourceDetailServiceImpl resourceDetailService;

    @Mock
    private SpTagsParamService spTagsParamService;

    @Mock
    private XSSFWorkbook xssfWorkbook;

    @Mock
    private SXSSFWorkbook swb;

    @Mock
    private SXSSFSheet sheet;
    @Mock
    private SXSSFRow row;
    @Mock
    private SXSSFCell cell;
    @Mock
    private BufferedWriter bufferedWriter;

    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Mock
    FileOutputStream fos;

    @Mock
    BufferedOutputStream bos;
    @Mock
    private RedisLock redisLock;


    @Test
//    @PrepareForTest({ResourceApplicationServiceImpl.class,ResourceApplicationRepository.class})
    public void getResourceApplicationCount() throws Exception{
        ResourceApplicationDTO dto = new ResourceApplicationDTO();
        dto.setApplyBillNo("DXBC20210303125983974");
        PowerMockito.when(resourceApplicationRepository.getResourceApplicationCount(dto)).thenReturn(1L);
        try{
            resourceApplicationService.getResourceApplicationCount(dto);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getMessage());
        }
    }

    @Test
//    @PrepareForTest({ResourceApplicationServiceImpl.class,ResourceApplicationRepository.class})
    public void getPageList() throws Exception{
        ResourceApplicationDTO dto = new ResourceApplicationDTO();
        dto.setApplyBillNo("DXBC20210303125983974");
        List<ResourceApplication> objects = new ArrayList<>();
        PowerMockito.when(resourceApplicationRepository.getPageList(dto)).thenReturn(objects);
        try{
            resourceApplicationService.getPageList(dto);
        } catch (Exception e) {
            Assert.assertEquals(RetCode.VALIDATIONERROR_CODE, e.getMessage());
        }
    }

    @Test
    public void exportThread() throws Exception{
        ResourceApplicationDTO dto = new ResourceApplicationDTO();
        dto.setFormat("xls");
        dto.setApplyBillNo("DXBC20210303125983974");
        List<ResourceApplication> objects = new ArrayList<>();
        String key = Constant.RESOURCE_EXPORT + MD5.getMD5Code(JSON.toJSONString(dto)) + 1;
        PowerMockito.when(redisTemplate.hasKey(any())).thenReturn(true);
        PowerMockito.when(resourceApplicationRepository.getPageList(dto)).thenReturn(objects);
        Assert.assertNotNull(resourceApplicationService.exportThread(dto));
    }

    @Test
    public void getFilePath() throws Exception{
        PowerMockito.when(redisTemplate.hasKey(any())).thenReturn(false);
        try {
            resourceApplicationService.getFilePath("1");
        } catch (MesBusinessException e) {
            e.printStackTrace();
            Assert.assertEquals(MessageId.EXPORT_TIMEOUT, e.getMessage());
        }
    }

    @Test
    public void getImportSuccessKey() throws Exception{
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        try {
            resourceApplicationService.getImportSuccessKey("1");
        } catch (MesBusinessException e) {
            e.printStackTrace();
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void importData() throws Exception{
        PowerMockito.when(redisTemplate.hasKey(any())).thenReturn(true);
        ResourceApplicationDTO dto = new ResourceApplicationDTO();
        dto.setFormat("xls");
        dto.setApplyBillNo("DXBC20210303125983974");
        try {
            resourceApplicationService.importData(dto);
        }catch (Exception e){
            e.printStackTrace();
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void importThread() throws Exception{
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        PowerMockito.mockStatic(CommonUtils.class);
        ResourceApplicationDTO dto = new ResourceApplicationDTO();
        try {
            resourceApplicationService.importThread(dto);
        }catch (Exception e){
            e.printStackTrace();
            Assert.assertEquals(null, e.getMessage());
        }
        dto.setInputStream(new ByteArrayInputStream(new byte[]{123}));
        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);
        List<SpTagsParam> paramList = new ArrayList<>();
        SpTagsParam param = new SpTagsParam();
        param.setParamType(1);
        param.setParamName("qwe");
        paramList.add(param);
        PowerMockito.when(spTagsParamService.queryList(Mockito.any())).thenReturn(paramList);
        dto.setFile(new MockMultipartFile("data", "qqqqqqqe.csv", "text/plain", "111".getBytes(StandardCharsets.UTF_8)));
        try {
            resourceApplicationService.importThread(dto);
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
    }

    @Test
    public void getApplyBillNo() throws Exception{
        ResourceApplicationDTO dto = new ResourceApplicationDTO();
        dto.setPage(1);
        dto.setRows(100);
        Page<ResourceDetail> page = new Page<>(dto.getPage(), dto.getRows());
        page.setParams(dto);
        LinkedList<ResourceDetail> list = new LinkedList<ResourceDetail>();
        PowerMockito.when(resourceDetailRepository.exportApplyResource(page)).thenReturn(list);
        Page<ResourceDetail> pageInfo = resourceApplicationService.getApplyBillNo(dto);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }

    @Test
    public void writeExcel() throws Exception {
        PowerMockito.mockStatic(ExcelCommonUtils.class);
        PowerMockito.mockStatic(FileUtils.class);
        PowerMockito.when(ExcelCommonUtils.getWorkbook(anyString(), anyString())).thenReturn(xssfWorkbook);
        PowerMockito.whenNew(SXSSFWorkbook.class).withAnyArguments().thenReturn(swb);
        PowerMockito.when(swb.getSheetAt(anyInt())).thenReturn(sheet);
        PowerMockito.when(sheet.createRow(anyInt())).thenReturn(row);
        PowerMockito.when(row.createCell(anyInt())).thenReturn(cell);
        PowerMockito.when(redisTemplate.hasKey(any())).thenReturn(true);

        List<SpTagsParam> paramList = new ArrayList<>();
        SpTagsParam spTagsParam1 = new SpTagsParam();
        spTagsParam1.setParamName("test1");
        spTagsParam1.setParamType(1);
        paramList.add(spTagsParam1);
        SpTagsParam spTagsParam2 = new SpTagsParam();
        spTagsParam2.setParamName("test2");
        spTagsParam2.setParamType(0);
        paramList.add(spTagsParam2);
        PowerMockito.when(spTagsParamService.queryList(any())).thenReturn(paramList);

        LinkedList<ResourceDetail> list = new LinkedList<ResourceDetail>();
        PowerMockito.when(resourceDetailRepository.exportApplyResource(any())).thenReturn(list);
        PowerMockito.when(cloudDiskHelper.fileUpload(anyString(), anyString(), anyInt())).thenReturn("123");
        PowerMockito.when(cloudDiskHelper.getFileDownloadUrl(anyString(), any(), anyString())).thenReturn("http://123");

        PowerMockito.whenNew(FileOutputStream.class).withAnyArguments().thenReturn(fos);
        PowerMockito.whenNew(BufferedOutputStream.class).withAnyArguments().thenReturn(bos);

        ResourceApplicationDTO dto = new ResourceApplicationDTO();
        dto.setCreateUser("12345");
        dto.setUpdateUser("12345");
        dto.setEmpNo("12345");
        String downloadUrl = resourceApplicationService.writeExcel(dto, "1");
        dto.setFormat(Constant.XLS);
        Assert.assertNull(resourceApplicationService.writeExcel(dto, "1"));
    }

	@Test
	public void insertResourceDetail() throws Exception {
		PowerMockito.mockStatic(CommonUtils.class);
		String key = "redis_key_upload_program_file_DXBC20210303125983974";
		List<String> list = new ArrayList<>();
		list.add("1");
		List<ResourceDetail> resourceDetailList = new ArrayList<>();
		ResourceApplicationDTO record = new ResourceApplicationDTO();
		record.setApplyBillNo("DXBC20210303125983974");
		resourceDetailList.add(new ResourceDetail());
		String partitionTime = "2020-12-12 13:00:00";

		List<String> resourceSnList = new ArrayList<>();
		resourceSnList.add("DXBC20210303125983974");
		PowerMockito.when(resourceDetailRepository.compareResourceSn(record.getApplyBillNo(),list)).thenReturn(resourceSnList);

		try {
			Whitebox.invokeMethod(resourceApplicationService, "insertResourceDetail", list,resourceDetailList,record,partitionTime,key);
		}catch (Exception e){
			e.printStackTrace();
            Assert.assertEquals(MessageId.RESOURCE_NO_DOES_NOT_MATCH, e.getMessage());
		}
	}

	@Test
	public void insertResourceDetailTest() throws Exception {
		PowerMockito.mockStatic(CommonUtils.class);
		String key = "redis_key_upload_program_file_DXBC20210303125983974";
		List<String> list = new ArrayList<>();
		list.add("1");
		List<ResourceDetail> resourceDetailList = new ArrayList<>();
		ResourceApplicationDTO record = new ResourceApplicationDTO();
		record.setApplyBillNo("DXBC20210303125983974");
		resourceDetailList.add(new ResourceDetail());
		String partitionTime = "2020-12-12 13:00:00";

		List<String> resourceSnList = new ArrayList<>();

		PowerMockito.when(resourceDetailRepository.compareResourceSn(record.getApplyBillNo(),list)).thenReturn(resourceSnList);
		MockitoAnnotations.initMocks(this);

		try {
			Whitebox.invokeMethod(resourceApplicationService, "insertResourceDetail", list,resourceDetailList,record,partitionTime,key);
		}catch (Exception e){
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
	}

    @Test
    public void splitMessage() throws Exception {
        List<String> list = new ArrayList<>();
        List<ResourceDetail> resourceDetailList = new ArrayList<>();
        ResourceApplicationDTO record = new ResourceApplicationDTO();
        record.setApplyId(UUID.randomUUID().toString());
        String message = "DXBC2021030351860624,DXBC2021030351860624";
		List<String> resourceSnList = new ArrayList<>();
		resourceSnList.add("DXBC20210303125983974");

        List<SpTagsParam> paramList = new ArrayList<>();
        SpTagsParam spTagsParam1 = new SpTagsParam();
        spTagsParam1.setParamName("test1");
        spTagsParam1.setParamType(1);
        paramList.add(spTagsParam1);
        SpTagsParam spTagsParam2 = new SpTagsParam();
        spTagsParam2.setParamName("test2");
        spTagsParam2.setParamType(0);
        paramList.add(spTagsParam2);

        PowerMockito.when(resourceDetailRepository.compareResourceSn(record.getApplyBillNo(),list)).thenReturn(resourceSnList);
        Whitebox.invokeMethod(resourceApplicationService, "splitMessage", list,resourceDetailList,paramList,record,message);
        Assert.assertNotNull(list);
        Assert.assertNotNull(resourceDetailList);
        Assert.assertNotNull(paramList);
    }

    @Test
    public void writeCsvFile() throws Exception{
        ResourceApplicationDTO dto = new ResourceApplicationDTO();
        dto.setApplyBillNo("GHDXBC20210303");
        dto.setFormat("txt");
        ResourceDetail resourceDetail = new ResourceDetail();
        resourceDetail.setResourceSn("1");
        InputStream errorStream = IOUtils.toInputStream("error message", "UTF-8");
        dto.setInputStream(errorStream);
        LinkedList<ResourceDetail> list = new LinkedList<ResourceDetail>();
        list.add(resourceDetail);
        PowerMockito.when(resourceDetailRepository.exportApplyResource(any())).thenReturn(list);
        PowerMockito.when(redisTemplate.hasKey(any())).thenReturn(false);
        try {
            resourceApplicationService.writeCsvFile(dto, "1");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.EXPORT_TIMEOUT, e.getMessage());
        }
        PowerMockito.when(redisTemplate.hasKey(any())).thenReturn(true);
        try {
            resourceApplicationService.writeCsvFile(dto, "1");
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void pageList() throws Exception
    {
        ResourceApplicationEntityDTO entityDTO = new ResourceApplicationEntityDTO();
        Page<ResourceApplicationEntityDTO> result = resourceApplicationService.pageList(entityDTO);
        Assert.assertTrue(result.getRows().size() >= 0);
    }

    @Test
    public void writeFile() throws Exception{
        PowerMockito.mock(CommonUtils.class);
        ResourceApplicationDTO record = new ResourceApplicationDTO();
        record.setApplyBillNo("GHDXBC20210303");
        record.setFormat("txt");
        ResourceDetail resourceDetail = new ResourceDetail();
        resourceDetail.setResourceSn("10000");
        HttpServletResponse response = Mockito.mock(HttpServletResponse.class);
        List<SpTagsParam> paramList = new ArrayList<>();
        SpTagsParam spTagsParam = new SpTagsParam();
        spTagsParam.setParamType(1);
        paramList.add(spTagsParam);
        SpTagsParam spTagsParam2 = new SpTagsParam();
        spTagsParam2.setParamType(0);
        paramList.add(spTagsParam2);
        try {
            PowerMockito.doNothing().when(bufferedWriter).write(Mockito.anyString());
            PowerMockito.doNothing().when(bufferedWriter).newLine();
            resourceApplicationService.writeFile(bufferedWriter, record, "adada", paramList);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void writeCsvHead() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);

        List<SpTagsParam> paramList = new ArrayList<>();
        SpTagsParam spTagsParam = new SpTagsParam();
        spTagsParam.setParamType(1);
        paramList.add(spTagsParam);
        SpTagsParam spTagsParam2 = new SpTagsParam();
        spTagsParam2.setParamType(0);
        paramList.add(spTagsParam2);

        PowerMockito.doNothing().when(bufferedWriter).write(Mockito.anyString());
        PowerMockito.doNothing().when(bufferedWriter).newLine();
        try {
            Whitebox.invokeMethod(resourceApplicationService, "writeCsvHead", bufferedWriter, paramList, "");
        } catch (Exception e) {
            e.printStackTrace();
        }
        Assert.assertNotNull(paramList);
    }

    @Test
    public void save() throws Exception
    {
        ResourceApplicationEntityDTO entityDTO = new ResourceApplicationEntityDTO();
        entityDTO.setResourceId(UUID.randomUUID().toString());
        entityDTO.setUserType("specialUser");
        entityDTO.setApplyQty(new BigDecimal("1000"));
        entityDTO.setStandardQty(new BigDecimal("10"));

        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        PowerMockito.when(valueOperations.setIfAbsent(Mockito.any() , Mockito.any() , Mockito.anyLong() ,
                Mockito.any())).thenReturn(true);
        PowerMockito.when(valueOperations.increment(Mockito.any())).thenReturn(100L);
        PowerMockito.when(redisTemplate.delete(Mockito.anyString())).thenReturn(true);


        List<ResourceInfoEntityDTO> resourceInfoList = new ArrayList<>();
        ResourceInfoEntityDTO entityModel = new ResourceInfoEntityDTO();
        entityModel.setResourceId(UUID.randomUUID().toString());
        entityModel.setResourceStatus("初始化");
        entityModel.setResourceStart("44-FB-6A-60-02-12");
        entityModel.setResourceEnd("45-FB-5A-60-02-12");
        entityModel.setResourceAmount(new BigInteger("1099243192321"));
        entityModel.setAvailableQuantity(new BigInteger("1099243170310"));
        entityModel.setResourceType("MAC");
        resourceInfoList.add(entityModel);
        PowerMockito.when(resourceInfoRepository.getList(Mockito.any())).thenReturn(resourceInfoList);

        ResourceApplicationEntityDTO applicationEntityDTO = new ResourceApplicationEntityDTO();
        applicationEntityDTO.setResourceId(UUID.randomUUID().toString());
        applicationEntityDTO.setResourceStart("44-FB-6A-60-09-E2");
        applicationEntityDTO.setResourceEnd("44-FB-6A-60-58-01");
        PowerMockito.when(resourceApplicationRepository.selectOneOfNewestValid(Mockito.any(), Mockito.any())).thenReturn(applicationEntityDTO);
        PowerMockito.when(resourceDetailService.batchInsert(Mockito.any())).thenReturn(1L);
        PowerMockito.when(resourceApplicationRepository.batchInsert(Mockito.any())).thenReturn(1);
        PowerMockito.when(resourceInfoRepository.batchUpdate(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(resourceApplicationService.save(entityDTO));
    }

	@Test
	public void validateRequestData() throws Exception
	{
		ResourceApplicationEntityDTO entityDTO = new ResourceApplicationEntityDTO();
		entityDTO.setResourceId(UUID.randomUUID().toString());
		entityDTO.setUserType("generalUser");
		entityDTO.setApplyTask("test");
		entityDTO.setApplyQty(new BigDecimal("1000"));
		entityDTO.setStandardQty(new BigDecimal("10"));

		PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
		PowerMockito.when(valueOperations.setIfAbsent(Mockito.any() , Mockito.any() , Mockito.anyLong() ,
                Mockito.any())).thenReturn(true);
		PowerMockito.when(valueOperations.increment(Mockito.any())).thenReturn(100L);
		PowerMockito.when(redisTemplate.delete(Mockito.anyString())).thenReturn(true);

		List<ResourceApplicationEntityDTO> resourceTaskList = new ArrayList<>();
		PowerMockito.when(resourceApplicationRepository.taskList(Mockito.any())).thenReturn(resourceTaskList);

		List<ResourceInfoEntityDTO> resourceInfoList = new ArrayList<>();
		ResourceInfoEntityDTO entityModel = new ResourceInfoEntityDTO();
		entityModel.setResourceId(UUID.randomUUID().toString());
		entityModel.setResourceStatus("初始化");
		entityModel.setResourceStart("44-FB-6A-60-02-12");
		entityModel.setResourceEnd("45-FB-5A-60-02-12");
		entityModel.setResourceAmount(new BigInteger("1099243192321"));
		entityModel.setAvailableQuantity(new BigInteger("1099243170310"));
		entityModel.setResourceType("MAC");
		resourceInfoList.add(entityModel);
		PowerMockito.when(resourceInfoRepository.getList(Mockito.any())).thenReturn(resourceInfoList);
        Assert.assertNotNull(resourceApplicationService.validateRequestData(entityDTO,true));
	}

    @Test
    public void update() throws Exception
    {
        ResourceApplicationEntityDTO entityDTO = new ResourceApplicationEntityDTO();
        Assert.assertNotNull(resourceApplicationService.update(entityDTO));
    }

    @Test
    public void retryInsert() throws Exception
    {
        List<String> recordIdList = new ArrayList<>();
        List<ResourceApplicationRecordDTO> recordDTOList = new ArrayList<>();
        ResourceApplicationRecordDTO recordDTO = new ResourceApplicationRecordDTO();
        recordDTO.setApplyId("1111");
        recordDTO.setResourceNo("2");
        recordDTO.setResourceStart("ZTEGCAEFF50F");
        recordDTO.setResourceEnd("ZTEGCAEFF51F");
        recordDTOList.add(recordDTO);
        PowerMockito.when(resourceApplicationRepository.getRecordListByIds(Mockito.any())).thenReturn(recordDTOList);

        ResourceApplicationEntityDTO entityDTO = new ResourceApplicationEntityDTO();
        entityDTO.setResourceType("GPON-SN");
        entityDTO.setStandardQty(new BigDecimal("1"));
        entityDTO.setApplyQty(new BigDecimal("1"));
        PowerMockito.when(resourceApplicationRepository.getResourceApplicationEntityById(Mockito.any())).thenReturn(entityDTO);

        PowerMockito.when(resourceDetailService.batchInsert(Mockito.any())).thenReturn(1L);
        PowerMockito.when(resourceApplicationRepository.deleteRecord(Mockito.any())).thenReturn(1);
        Assert.assertNotNull(resourceApplicationService.retryInsert(recordIdList));
    }

    @Test
    public void splitMessageTry() throws Exception {
        List<String> list = new ArrayList<>();
        List<ResourceDetail> resourceDetailList = new ArrayList<>();
        List<SpTagsParam> paramList = new ArrayList<>();
        SpTagsParam spTagsParam1 = new SpTagsParam();
        spTagsParam1.setParamName("test1");
        spTagsParam1.setParamType(1);
        paramList.add(spTagsParam1);
        SpTagsParam spTagsParam2 = new SpTagsParam();
        spTagsParam2.setParamName("test2");
        spTagsParam2.setParamType(0);
        paramList.add(spTagsParam2);
        ResourceApplicationDTO record = new ResourceApplicationDTO();
        record.setApplyId(UUID.randomUUID().toString());
        String message = "0000";
        try {
            Whitebox.invokeMethod(resourceApplicationService, "splitMessage", list,resourceDetailList,paramList,record,message);
            Whitebox.invokeMethod(resourceApplicationService, "splitMessage", list,resourceDetailList,new ArrayList<String>(),record,message);
        } catch (Exception e) {
            e.printStackTrace();
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void deleteRetryRecord() throws Exception {
        List<String> recordList = new ArrayList<>();
        recordList.add("11");
        Assert.assertNotNull(resourceApplicationService.deleteRetryRecord(recordList));
    }

    @Test
    public void allocate() throws Exception {
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        PowerMockito.when(valueOperations.setIfAbsent(Mockito.any() , Mockito.any() , Mockito.anyLong() , Mockito.any())).thenReturn(true);
        PowerMockito.when(valueOperations.increment(Mockito.any())).thenReturn(100L);
        PowerMockito.when(redisTemplate.delete(Mockito.anyString())).thenReturn(true);

        ResourceApplicationEntityDTO entityDTO = new ResourceApplicationEntityDTO();
        entityDTO.setResourceType("MAC1");
        entityDTO.setStandardQty(new BigDecimal("1"));
        entityDTO.setApplyQty(new BigDecimal("1"));
        PowerMockito.when(resourceApplicationRepository.getResourceApplicationEntityById(Mockito.any())).thenReturn(entityDTO);

        PowerMockito.when(resourceDetailRepository.getResourceDetailCount(Mockito.any())).thenReturn(2l);

        List<ResourceApplicationEntityDTO> resourceTaskList = new ArrayList<>();
        PowerMockito.when(resourceApplicationRepository.taskList(Mockito.any())).thenReturn(resourceTaskList);

        PowerMockito.when(resourceApplicationRepository.updateResourceApplication(Mockito.any())).thenReturn(2L);

        List<ResourceDetail> detailList = new ArrayList<>();
        ResourceDetail detail = new ResourceDetail();
        detail.setResourceStart("ZTEGCAEFF50F");
        detail.setResourceSn("ZTEGCAEFF51F");
        detailList.add(detail);
        detailList.add(detail);
        PowerMockito.when(resourceDetailRepository.getFirstAndLast((Mockito.any()))).thenReturn(detailList);
        PowerMockito.when(resourceApplicationRepository.countResourceApplicationByParentApplyBillNo((Mockito.any()))).thenReturn(2L);

        PowerMockito.when(resourceDetailService.batchInsert(Mockito.any())).thenReturn(1L);
        PowerMockito.when(resourceDetailRepository.updateApplyId((Mockito.any()))).thenReturn(1);

        ResourceApplicationAllocateDTO splitDTO = new ResourceApplicationAllocateDTO();
        splitDTO.setParentApplyId("test");
        splitDTO.setApplyQty(BigDecimal.ONE);
        splitDTO.setApplyTask("test");
        splitDTO.setCreateUser("test");
        resourceApplicationService.allocate(splitDTO);
        Assert.assertNotNull(entityDTO);
    }


    @Test
    public void testGetList() {
        PowerMockito.when(resourceApplicationRepository.taskList(Mockito.any())).thenReturn(new ArrayList<>());
        Assert.assertNotNull(resourceApplicationService.getList(""));
    }

    @Test
    public void testGetAndCheckSpTagsParams() {
        PowerMockito.when(spTagsParamService.queryList(Mockito.any())).thenReturn(new ArrayList<SpTagsParam>(){{add(new SpTagsParam(){{setParamType(2);}});}});
        try {
            Whitebox.invokeMethod(resourceApplicationService,"getAndCheckSpTagsParams",new ResourceApplicationDTO());
        } catch (Exception e) {
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }

    @Test
    public void invalid() {
        ResourceApplicationInvalidDTO dto = new ResourceApplicationInvalidDTO();
        dto.setApplyId("test");
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);

        PowerMockito.when(valueOperations.increment(Mockito.any())).thenReturn(100L);
        PowerMockito.when(redisTemplate.delete(Mockito.anyString())).thenReturn(true);
        PowerMockito.when(resourceApplicationRepository.getResourceApplicationEntityById(Mockito.any())).thenReturn(null);
        try {
            resourceApplicationService.invalid(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.RESOURCE_APPLICATION_NOT_EXIST, e.getMessage());
        }
        PowerMockito.when(resourceApplicationRepository.getResourceApplicationEntityById(Mockito.any()))
                .thenReturn(new ResourceApplicationEntityDTO(){{setResourceType(MpConstant.MAC);setApplyQty(BigDecimal.ONE);}});

        PowerMockito.when(resourceApplicationRepository.selectOneOfNewestValid2(Mockito.anyString(),Mockito.anyString(),Mockito.anyString()))
                .thenReturn(new ResourceApplicationEntityDTO(){{setResourceType(MpConstant.MAC);setApplyQty(BigDecimal.ONE);setApplyId("222");}});
        try {
            resourceApplicationService.invalid(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.RESOURCE_APPLICATION_IS_APPLYING, e.getMessage());
        }

        PowerMockito.when(resourceApplicationRepository.selectOneOfNewestValid2(Mockito.anyString(),Mockito.anyString(),Mockito.anyString()))
                .thenReturn(null);
        ResourceApplicationEntityDTO entityDTO = new ResourceApplicationEntityDTO();
        entityDTO.setApplyId("test");
        entityDTO.setResourceType(MpConstant.MAC);
        entityDTO.setResourceStart("A0-00-00-00-01-01");
        entityDTO.setResourceEnd("A0-00-00-00-01-12");
        PowerMockito.when(resourceApplicationRepository.selectOneOfNewestValid(Mockito.any(),Mockito.any())).thenReturn(entityDTO);
        PowerMockito.when(valueOperations.setIfAbsent(Mockito.any() , Mockito.any() , Mockito.anyLong() ,
                Mockito.any())).thenReturn(true);

        PowerMockito.when(resourceApplicationRepository.updateResourceApplication(Mockito.any())).thenReturn(1L);

        List<ResourceInfoEntityDTO> resourceInfoList = new ArrayList<>();
        ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
        resourceInfoEntityDTO.setResourceType(MpConstant.MAC);
        resourceInfoEntityDTO.setResourceStart("A0-00-00-00-01-01");
        resourceInfoEntityDTO.setResourceEnd("A0-00-00-00-01-15");
        resourceInfoEntityDTO.setAvailableQuantity(BigInteger.ONE);
        resourceInfoEntityDTO.setResourceAmount(BigInteger.ONE);
        resourceInfoList.add(resourceInfoEntityDTO);

        PowerMockito.when(resourceInfoRepository.getList(Mockito.any())).thenReturn(resourceInfoList);
        PowerMockito.when(resourceInfoRepository.batchUpdateSelectivity(Mockito.any())).thenReturn(1);
        PowerMockito.when(resourceDetailRepository.deleteByApplyId(Mockito.any())).thenReturn(1);

        resourceApplicationService.invalid(dto);

        PowerMockito.when(resourceApplicationRepository.getResourceApplicationEntityById(Mockito.any()))
                .thenReturn(new ResourceApplicationEntityDTO(){{setResourceType(MpConstant.MAC);setParentApplyBillNo("test");setApplyQty(BigDecimal.ONE);}});
        List<ResourceApplicationDTO> appliyList = new ArrayList<>();
        appliyList.add(new ResourceApplicationDTO(){{setResourceStatus(ResourceStatusEnum.ALLOCATED.name());setAllocatedQty(1L);}});
        PowerMockito.when(resourceApplicationRepository.selectByApplyBillNo(Mockito.any())).thenReturn(appliyList);
        PowerMockito.when(resourceApplicationRepository.updateResourceApplication(Mockito.any())).thenReturn(1L);
        PowerMockito.when(resourceDetailRepository.updateNewApplyId(Mockito.any(),Mockito.any())).thenReturn(1);
        resourceApplicationService.invalid(dto);
    }

    @Test
    public void continueGenerateApplicationDetailInfo() throws Exception {
        ResourceApplicationEntityDTO record = new ResourceApplicationEntityDTO();
        record.setResourceType(Constant.ResourceType.MAC);
        record.setSeparator("-");
        record.setResourceStart("00-19-C6-51-00-01");
        record.setApplyQty(new BigDecimal("5"));
        record.setResourceStep(16);
        record.setConsumptionQty(0l);
        record.setApplyAmount(new BigDecimal("2"));
        record.setIsLastNo("N");
        List<SpTemplateItemDTO> itemList = new ArrayList() {{
            add(new SpTemplateItemDTO() {{
                setGenerationMethod(Constant.GenerationMethod.GPON_SN);
                setParamRule(Constant.GPON_SN_FUNCTION);
            }});
            add(new SpTemplateItemDTO() {{
                setGenerationMethod(Constant.GenerationMethod.MAC_START);
                setParamRule("(-,F)");
            }});
            add(new SpTemplateItemDTO() {{
                setGenerationMethod(Constant.GenerationMethod.MAC_START);
                setParamRule("(-)");
            }});
            add(new SpTemplateItemDTO() {{
                setParamType(Constant.ParamType.NASN);
                setParamRule("999");
            }});
        }};
        resourceApplicationService.continueGenerateApplicationDetailInfo(record, itemList);
        record.setConsumptionQty(1L);
        record.setIsLastNo("Y");
        resourceApplicationService.continueGenerateApplicationDetailInfo(record, itemList);
        record.setResourceApplyEnd("00-19-C6-51-00-05");
        record.setResourceEnd("00-19-C6-51-00-05");
        resourceApplicationService.continueGenerateApplicationDetailInfo(record, itemList);
        record.setResourceEnd("00-19-C6-51-00-09");
        resourceApplicationService.continueGenerateApplicationDetailInfo(record, itemList);
        record.setResourceType(Constant.ResourceType.GPON_SN);
        record.setResourceStart("ZTEGC0300101");
        record.setResourceEnd("ZTEGC0300106");
        record.setResourceApplyEnd("ZTEGC0300105");
        resourceApplicationService.continueGenerateApplicationDetailInfo(record, itemList);
        record.setResourceType(Constant.ResourceType.CMEI);
        record.setResourceStart("801205000000031");
        record.setResourceEnd("801205000000036");
        record.setResourceApplyEnd("801205000000035");
        resourceApplicationService.continueGenerateApplicationDetailInfo(record, itemList);
        record.setResourceApplyEnd("00-19-C6-51-00-09");
        try {
            resourceApplicationService.continueGenerateApplicationDetailInfo(record, itemList);
        } catch (Exception e) {
            Assert.assertNotNull(e);
        }
        record.setResourceType(Constant.ResourceType.NETWORK_ACCESS);
        try {
            resourceApplicationService.continueGenerateApplicationDetailInfo(record, itemList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.RESOURCE_WARNING_DATABASE_RESOURCE_IS_EMPTY, e.getMessage());
        }
        record.setResourceType(Constant.ResourceType.DEVICE_SERIAL_NUMBER);
        record.setResourceApplyEnd("");
        resourceApplicationService.continueGenerateApplicationDetailInfo(record, itemList);
    }

    @Test
    public void saveApplicationInfo() throws Exception {
        ResourceApplicationEntityDTO record = new ResourceApplicationEntityDTO();
        record.setUserType(Constant.GENERAL_USER);
        record.setResourceId("11112321");
        try {
            resourceApplicationService.saveApplicationInfo(record);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.RESOURCE_INFO_NOT_EXIST,e.getMessage());
        }
        List<ResourceInfoEntityDTO> resourceInfoList = new ArrayList() {{
            add(new ResourceInfoEntityDTO() {{
                setAvailableQuantity(BigInteger.ONE);
            }});
        }};
        PowerMockito.when(resourceInfoRepository.getList(Mockito.any())).thenReturn(resourceInfoList);
        PowerMockito.when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        record.setStandardQty(BigDecimal.ONE);
        record.setConsumptionQty(0L);
        record.setApplyAmount(BigDecimal.ONE);
        record.setResourceType(Constant.ResourceType.MAC);
        record.setResourceStart("00-19-C6-51-00-01");
        record.setSeparator("-");
        record.setApplyQty(BigDecimal.ONE);
        resourceApplicationService.saveApplicationInfo(record);
        record.setResourceStatus("INIT");
        List<ResourceInfoEntityDTO> resourceInfoList1 = new ArrayList() {{
            add(new ResourceInfoEntityDTO() {{
                setAvailableQuantity(BigInteger.ONE);
            }});
        }};
        PowerMockito.when(resourceInfoRepository.getList(Mockito.any())).thenReturn(resourceInfoList1);
        resourceApplicationService.saveApplicationInfo(record);
    }

    @Test
    public void validApplyQtyToMACStart() throws Exception {
        SpSpecialityParam spSpecialityParam = new SpSpecialityParam();
        spSpecialityParam.setApplyQty(10L);
        spSpecialityParam.setUsageScope("2");
        spSpecialityParam.setProductBigClass("1");
        spSpecialityParam.setProductSmallClass("3");
        spSpecialityParam.setStep("1");
        PowerMockito.when(resourceApplicationRepository.getResourceInfoByCondition(Mockito.any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToMACStart", spSpecialityParam,new ArrayList<>());
        }catch (Exception e){
            Assert.assertEquals(MessageId.NO_MAC_AVAILABLE_RESOURCE, e.getMessage());
        }
        ResourceApplicationEntityDTO entityDTO = new ResourceApplicationEntityDTO();
        entityDTO.setResourceStart("00-19-C6-51-08-00");
        entityDTO.setAvailableQuantity(4L);
        PowerMockito.when(resourceApplicationRepository.getResourceInfoByCondition(Mockito.any())).thenReturn(entityDTO);
        entityDTO.setAvailableQuantity(6L);
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToMACStart", spSpecialityParam,new ArrayList<>());
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToMACStart", spSpecialityParam,new ArrayList<>());
        entityDTO.setAvailableQuantity(4L);
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToMACStart", spSpecialityParam,new ArrayList<>());
        entityDTO.setResourceApplyEnd("00-19-C6-51-08-34");
        entityDTO.setResourceEnd("00-19-C6-51-08-36");
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToMACStart", spSpecialityParam,new ArrayList<>());
        entityDTO.setAvailableQuantity(6L);
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToMACStart", spSpecialityParam,new ArrayList<>());
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToMACStart", spSpecialityParam,new ArrayList<>());
        entityDTO.setAvailableQuantity(4L);
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToMACStart", spSpecialityParam,new ArrayList<>());
        try {
            Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToMACStart", spSpecialityParam,new ArrayList<>());
        } catch (Exception e){
            Assert.assertNotNull(e.getMessage());
        }
    }

    @Test
    public void validApplyQtyToCustomizedMACStart() throws Exception {
        SpSpecialityParam spSpecialityParam = new SpSpecialityParam();
        spSpecialityParam.setApplyQty(5L);
        spSpecialityParam.setUsageScope("2");
        spSpecialityParam.setProductBigClass("1");
        spSpecialityParam.setProductSmallClass("3");
        spSpecialityParam.setStep("1");
        SpTemplateItemDTO itemDTO = new SpTemplateItemDTO();
        itemDTO.setParamRule("MacStart(-,3)");PowerMockito.when(resourceApplicationRepository.getResourceInfoByCondition(Mockito.any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToCustomizedMACStart", spSpecialityParam,itemDTO,new ArrayList<>());
        }catch (Exception e){
            Assert.assertEquals(MessageId.NO_MAC_AVAILABLE_RESOURCE, e.getMessage());
        }
        ResourceApplicationEntityDTO entityDTO = new ResourceApplicationEntityDTO();
        entityDTO.setResourceStart("00-19-C6-51-08-03");
        entityDTO.setAvailableQuantity(4L);
        itemDTO.setParamRule("MacStart(-,3)");PowerMockito.when(resourceApplicationRepository.getResourceInfoByCondition(Mockito.any())).thenReturn(entityDTO);
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToCustomizedMACStart", spSpecialityParam,itemDTO,new ArrayList<>());
        entityDTO.setResourceStart("00-19-C6-51-08-02");
        entityDTO.setResourceEnd("00-19-C6-51-08-11");
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToCustomizedMACStart", spSpecialityParam,itemDTO,new ArrayList<>());
        spSpecialityParam.setApplyQty(20L);
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToCustomizedMACStart", spSpecialityParam,itemDTO,new ArrayList<>());
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToCustomizedMACStart", spSpecialityParam,itemDTO,new ArrayList<>());
        spSpecialityParam.setApplyQty(5L);
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToCustomizedMACStart", spSpecialityParam,itemDTO,new ArrayList<>());
        entityDTO.setResourceStart("00-19-C6-51-08-10");
        entityDTO.setResourceEnd("00-19-C6-51-08-19");
        entityDTO.setResourceApplyEnd("00-19-C6-51-08-12");
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToCustomizedMACStart", spSpecialityParam,itemDTO,new ArrayList<>());
        entityDTO.setResourceApplyEnd("00-19-C6-51-08-11");
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToCustomizedMACStart", spSpecialityParam,itemDTO,new ArrayList<>());
        try {
            Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToCustomizedMACStart", spSpecialityParam,itemDTO,new ArrayList<>());
        } catch (Exception e){
            Assert.assertNotNull(e.getMessage());
        }
    }

    @Test
    public void validApplyQtyToGponSn() throws Exception {
        SpSpecialityParam spSpecialityParam = new SpSpecialityParam();
        spSpecialityParam.setApplyQty(5L);
        spSpecialityParam.setUsageScope("2");
        spSpecialityParam.setProductBigClass("1");
        spSpecialityParam.setProductSmallClass("3");
        PowerMockito.when(resourceApplicationRepository.getResourceInfoByCondition(Mockito.any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToGponSn", spSpecialityParam, new ArrayList<>());
        }catch (Exception e){
            Assert.assertEquals(MessageId.NO_GPON_SN_AVAILABLE_RESOURCE, e.getMessage());
        }
        ResourceApplicationEntityDTO entityDTO = new ResourceApplicationEntityDTO();
        entityDTO.setResourceStart("801205000000031");
        entityDTO.setAvailableQuantity(4L);
        PowerMockito.when(resourceApplicationRepository.getResourceInfoByCondition(Mockito.any())).thenReturn(entityDTO);
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToGponSn", spSpecialityParam, new ArrayList<>());
        entityDTO.setResourceApplyEnd("801205000000034");
        entityDTO.setAvailableQuantity(6L);
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToGponSn", spSpecialityParam, new ArrayList<>());
    }

    @Test
    public void validApplyQtyToCustomizedGponSn() throws Exception {
        SpSpecialityParam spSpecialityParam = new SpSpecialityParam();
        spSpecialityParam.setApplyQty(5L);
        spSpecialityParam.setUsageScope("2");
        spSpecialityParam.setProductBigClass("1");
        spSpecialityParam.setProductSmallClass("3");
        SpTemplateItemDTO itemDTO = new SpTemplateItemDTO();
        itemDTO.setParamRule("GponSn(ZTECGD)");
        PowerMockito.when(resourceApplicationRepository.getResourceInfoByCondition(Mockito.any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToCustomizedGponSn", spSpecialityParam,itemDTO,new ArrayList<>());
        }catch (Exception e){
            Assert.assertEquals(MessageId.NO_GPON_SN_AVAILABLE_RESOURCE, e.getMessage());
        }
        ResourceApplicationEntityDTO entityDTO = new ResourceApplicationEntityDTO();
        entityDTO.setResourceStart("801205000000031");
        entityDTO.setAvailableQuantity(4L);
        PowerMockito.when(resourceApplicationRepository.getResourceInfoByCondition(Mockito.any())).thenReturn(entityDTO);
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToCustomizedGponSn", spSpecialityParam,itemDTO,new ArrayList<>());
        entityDTO.setResourceApplyEnd("801205000000034");
        entityDTO.setAvailableQuantity(6L);
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToCustomizedGponSn", spSpecialityParam,itemDTO,new ArrayList<>());
        entityDTO.setAvailableQuantity(2L);
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToCustomizedGponSn", spSpecialityParam,itemDTO,new ArrayList<>());
    }

    @Test
    public void validApplyQtyToDBParams() throws Exception {
        SpSpecialityParam spSpecialityParam = new SpSpecialityParam();
        spSpecialityParam.setApplyQty(5L);
        spSpecialityParam.setUsageScope("2");
        spSpecialityParam.setProductBigClass("1");
        spSpecialityParam.setProductSmallClass("3");
        SpTemplateItemDTO itemDTO = new SpTemplateItemDTO();
        itemDTO.setParamRule("CMEI,000000000000001");
        PowerMockito.when(resourceApplicationRepository.getResourceInfoByCondition(Mockito.any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToDBParams",itemDTO,new ArrayList<>(),spSpecialityParam);
        }catch (Exception e){
            Assert.assertEquals(MessageId.NO_AVAILABLE_RESOURCE, e.getMessage());
        }
        ResourceApplicationEntityDTO entityDTO = new ResourceApplicationEntityDTO();
        entityDTO.setResourceStart("ZTEGC0300101");
        entityDTO.setAvailableQuantity(4L);
        PowerMockito.when(resourceApplicationRepository.getResourceInfoByCondition(Mockito.any())).thenReturn(entityDTO);
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToDBParams",itemDTO,new ArrayList<>(),spSpecialityParam);
        entityDTO.setResourceApplyEnd("ZTEGC0300104");
        entityDTO.setAvailableQuantity(6L);
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToDBParams",itemDTO,new ArrayList<>(),spSpecialityParam);
        entityDTO.setAvailableQuantity(2L);
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToDBParams",itemDTO,new ArrayList<>(),spSpecialityParam);
    }

    @Test
    public void validApplyQty() throws Exception {
        SpTemplateItemDTO itemDTO = new SpTemplateItemDTO();
        itemDTO.setParamType(Constant.ParamType.MAC);
        itemDTO.setGenerationMethod(Constant.GenerationMethod.MAC_START);
        try {
            resourceApplicationService.validApplyQty(itemDTO,new SpSpecialityParam(), new ArrayList<>());
        }catch (Exception e){
            Assert.assertEquals(MessageId.NO_MAC_AVAILABLE_RESOURCE, e.getMessage());
        }
        itemDTO.setGenerationMethod(Constant.GenerationMethod.CUSTOMIZED_MAC_START);
        try {
            resourceApplicationService.validApplyQty(itemDTO,new SpSpecialityParam(), new ArrayList<>());
        }catch (Exception e){
            Assert.assertNull(e.getMessage());
        }
        itemDTO.setParamType(Constant.ParamType.GPON_SN);
        itemDTO.setGenerationMethod(Constant.GenerationMethod.GPON_SN);
        try {
            resourceApplicationService.validApplyQty(itemDTO,new SpSpecialityParam(), new ArrayList<>());
        }catch (Exception e){
            Assert.assertEquals(MessageId.NO_GPON_SN_AVAILABLE_RESOURCE, e.getMessage());
        }
        itemDTO.setGenerationMethod(Constant.GenerationMethod.CUSTOMIZED_GPON_SN);
        try {
            resourceApplicationService.validApplyQty(itemDTO,new SpSpecialityParam(), new ArrayList<>());
        }catch (Exception e){
            Assert.assertNull(e.getMessage());
        }

        SpSpecialityParam specialityParam = new SpSpecialityParam();
        specialityParam.setApplyQty(1L);
        itemDTO.setGenerationMethod(Constant.GenerationMethod.TELMEX_GPON_SN);
        try {
            resourceApplicationService.validApplyQty(itemDTO, specialityParam, new ArrayList<>());
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }

        itemDTO.setParamType(Constant.ParamType.NASN);
        itemDTO.setParamRule("0000-11");

        PowerMockito.when(resourceInfoDetailRepository.countByResourceNo(Mockito.any())).thenReturn(0);

        try {
            resourceApplicationService.validApplyQty(itemDTO, specialityParam, new ArrayList<>());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CONSENT_AVAILABLE_QUANTITY_IS_INSUFFICIENT, e.getMessage());
        }
        PowerMockito.when(resourceInfoDetailRepository.countByResourceNo(Mockito.any())).thenReturn(2);
        PowerMockito.when(resourceInfoRepository.getResourceInfoByNo(Mockito.any())).thenReturn(new ResourceInfoEntityDTO());
        resourceApplicationService.validApplyQty(itemDTO, specialityParam, new ArrayList<>());

        itemDTO.setParamType(Constant.ParamType.ASSIGNMENT);
        itemDTO.setGenerationMethod(Constant.GenerationMethod.ASSIGNMENT_DB);
        itemDTO.setParamRule("SN,0001");
        try {
            resourceApplicationService.validApplyQty(itemDTO, specialityParam, new ArrayList<>());
        } catch (Exception e) {
            Assert.assertEquals(MessageId.NO_AVAILABLE_RESOURCE, e.getMessage());
        }
    }

    @Test
    public void getApplyingList() throws Exception {
        resourceApplicationService.getApplyingList("test");
        List<String> applyIdList = new ArrayList() {{
            add("test");
        }};
        Assert.assertNotNull(applyIdList);
        PowerMockito.when(resourceApplicationRepository.getIdByTask(Mockito.any())).thenReturn(applyIdList);
        resourceApplicationService.getApplyingList("test");
    }


    @Test
    public void validApplyQtyToTelmexGponSn() throws Exception {
        SpSpecialityParam spSpecialityParam = new SpSpecialityParam();
        spSpecialityParam.setApplyQty(-5L);
        spSpecialityParam.setUsageScope("2");
        spSpecialityParam.setProductBigClass("1");
        spSpecialityParam.setProductSmallClass("3");
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToTelmexGponSn", spSpecialityParam);

        spSpecialityParam.setApplyQty(5L);
        PowerMockito.when(spSpecialityParamRepository.getTelmexEigenValueIndex(Mockito.any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToTelmexGponSn", spSpecialityParam);
        }catch (Exception e){
            Assert.assertEquals(MessageId.NO_GPON_SN_AVAILABLE_RESOURCE, e.getMessage());
        }
        ResourceApplicationEntityDTO entityDTO = new ResourceApplicationEntityDTO();
        entityDTO.setResourceStart("801205000000031");
        entityDTO.setAvailableQuantity(4L);
        PowerMockito.when(spSpecialityParamRepository.getTelmexEigenValueIndex(Mockito.any())).thenReturn(Constant.LONG_65536);
        try {
            Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToTelmexGponSn", spSpecialityParam);
        }catch (Exception e){
            Assert.assertEquals(MessageId.NO_GPON_SN_AVAILABLE_RESOURCE, e.getMessage());
        }
        PowerMockito.when(spSpecialityParamRepository.getTelmexEigenValueIndex(Mockito.any())).thenReturn(65534L);
        Whitebox.invokeMethod(resourceApplicationService, "validApplyQtyToTelmexGponSn", spSpecialityParam);
        Assert.assertNotNull(spSpecialityParam);
    }

    @Test
    public void insertDetailInfo() throws Exception {
        ResourceApplicationEntityDTO record = new ResourceApplicationEntityDTO();
        record.setResourceType(Constant.ResourceType.MAC);
        record.setResourceStart("00-00-00-00-00-01");
        record.setApplyQty(new BigDecimal("2"));
        record.setSeparator("");
        record.setResourceStep(1);
        Assert.assertNotNull(record);
        Whitebox.invokeMethod(resourceApplicationService, "insertDetailInfo", record);

        record.setSeparator("-");
        Whitebox.invokeMethod(resourceApplicationService, "insertDetailInfo", record);

        record.setResourceType(Constant.ResourceType.GPON_SN);
        record.setMCode("ZTEGD");
        record.setResourceStart("ZTEGD0000001");
        Whitebox.invokeMethod(resourceApplicationService, "insertDetailInfo", record);

        record.setMCode("ZTEGC");
        Whitebox.invokeMethod(resourceApplicationService, "insertDetailInfo", record);

        record.setResourceType(Constant.ResourceType.CMEI);
        record.setResourceStart("000000000001");
        Whitebox.invokeMethod(resourceApplicationService, "insertDetailInfo", record);

        record.setResourceType(Constant.ResourceType.CUEI);
        Whitebox.invokeMethod(resourceApplicationService, "insertDetailInfo", record);

        record.setResourceType(Constant.ResourceType.CTEI);
        Whitebox.invokeMethod(resourceApplicationService, "insertDetailInfo", record);
        record.setResourceType(Constant.ResourceType.IMEI);
        Whitebox.invokeMethod(resourceApplicationService, "insertDetailInfo", record);
        record.setResourceStart("000000000000001");
        record.setResourceType(Constant.ResourceType.SN);
        Whitebox.invokeMethod(resourceApplicationService, "insertDetailInfo", record);
        record.setResourceStart("00000000000000000001");
        record.setResourceType(Constant.ResourceType.SN);
        Whitebox.invokeMethod(resourceApplicationService, "insertDetailInfo", record);
        record.setResourceType(Constant.ResourceType.STB_SN);
        Whitebox.invokeMethod(resourceApplicationService, "insertDetailInfo", record);
        record.setResourceType(Constant.ResourceType.SO_NET_SN);
        Whitebox.invokeMethod(resourceApplicationService, "insertDetailInfo", record);
    }


    @Test
    public void networkAccessDetailInfo() throws Exception {
        Whitebox.invokeMethod(resourceApplicationService, "networkAccessDetailInfo", new ResourceApplicationEntityDTO());

        ResourceApplicationEntityDTO entityDTO = new ResourceApplicationEntityDTO();
        entityDTO.setResourceNo("123");
        entityDTO.setApplyQty(BigDecimal.ONE);
        PowerMockito.when(resourceInfoRepository.selectByResourceNo(Mockito.any())).thenReturn(null);
        try {
            Whitebox.invokeMethod(resourceApplicationService, "networkAccessDetailInfo", entityDTO);
        } catch (Exception e) {
            Assert.assertEquals( MessageId.RESOURCE_WARNING_DATABASE_RESOURCE_IS_EMPTY, e.getMessage());
        }

        entityDTO.setRemainQty(BigDecimal.TEN);
        ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
        PowerMockito.when(resourceInfoDetailRepository.getInitResourceNumByNo(Mockito.any())).thenReturn(null);
        PowerMockito.when(resourceInfoRepository.selectByResourceNo(Mockito.any())).thenReturn(resourceInfoEntityDTO);
        Whitebox.invokeMethod(resourceApplicationService, "networkAccessDetailInfo", entityDTO);

        List<ResourceInfoDetailDTO> detailDTOList = new ArrayList() {{
            add(new ResourceInfoDetailDTO() {{
                setScramblingCode("1");
            }});
        }};
        PowerMockito.when(resourceInfoDetailRepository.getInitResourceNumByNo(Mockito.any())).thenReturn(detailDTOList);
        Whitebox.invokeMethod(resourceApplicationService, "networkAccessDetailInfo", entityDTO);
    }
}
