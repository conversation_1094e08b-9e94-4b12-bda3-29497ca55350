package com.zte.autoTest.unitTest;

import com.zte.application.impl.spm.BoardScrapApplicationServiceImpl;
import com.zte.domain.model.spm.BoardScarpApplicationRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.datawb.BoardScrapApplicationDetailDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023-03-08 15:42
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({DatawbRemoteService.class})
public class BoardScrapApplicationServiceImplTest {
    @InjectMocks
    private BoardScrapApplicationServiceImpl boardScrapApplicationServiceImpl;
    @Mock
    private BoardScarpApplicationRepository boardScarpApplicationRepository;

    @Before
    public void init() {
        PowerMockito.mockStatic(DatawbRemoteService.class);
    }

    @Test
    public void insertBoardBatch()
    {
        List<BoardScrapApplicationDetailDTO> list = new LinkedList<>();
        BoardScrapApplicationDetailDTO a1 = new BoardScrapApplicationDetailDTO();
        list.add(a1);
        boardScrapApplicationServiceImpl.insertBoardBatch(list);
        Assert.assertNotNull(list);
    }

}
