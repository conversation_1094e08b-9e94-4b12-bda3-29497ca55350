package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.zte.common.CommonUtils;
import com.zte.common.ConstantInterface;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.model.MessageId;
import com.zte.common.utils.ServiceDataUtil;
import com.zte.domain.model.PkCodeInfo;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.infrastructure.remote.HttpRemoteService;
import com.zte.interfaces.dto.*;
import com.zte.itp.msa.client.util.HttpClientUtil;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.model.ServiceData;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.util.json.JacksonJsonConverUtil;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.RetCode;
import com.zte.springbootframe.util.HttpRemoteUtil;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.springbootframe.util.ServiceDataBuilderUtil;
import com.zte.util.BaseTestCase;
import org.apache.commons.collections.CollectionUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Matchers.any;
import static org.mockito.Matchers.anyMap;
import static org.mockito.Matchers.anyString;

/**
 * @Author: 10307315
 * @Date: 2021/10/11 上午9:08
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({DatawbRemoteService.class, JacksonJsonConverUtil.class, JSON.class, HttpClientUtil.class,
        ServiceDataBuilderUtil.class, ConstantInterface.class, RetCode.class, HttpRemoteUtil.class,
        MicroServiceRestUtil.class, MESHttpHelper.class, CommonUtils.class, SpringContextUtil.class, HttpRemoteService.class})

public class DatawbRemoteServiceTest extends BaseTestCase {
    @InjectMocks
    private DatawbRemoteService datawbRemoteService;
    @Mock
    private ConstantInterface constantInterface;
    @Mock
    LocaleMessageSourceBean lmb;

    @Test
    public void getIsHasDirFlagTest() throws Exception {
        List<PkCodeInfo> list = new ArrayList<>();
        ServiceData ret = new ServiceData();
        ret.setCode(new com.zte.itp.msa.core.model.RetCode(com.zte.itp.msa.core.model.RetCode.SUCCESS_CODE, com.zte.itp.msa.core.model.RetCode.SUCCESS_MSGID));
        List<TaskMaterialIssueSeqEntityDTO> result = new ArrayList<>();
        TaskMaterialIssueSeqEntityDTO dto = new TaskMaterialIssueSeqEntityDTO();
        result.add(dto);
        ret.setBo(result);
        PowerMockito.mockStatic(HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), anyString(), anyString())).thenReturn(JSON.toJSONString(ret));
        PowerMockito.when(constantInterface.getUrl(Mockito.any())).thenReturn("123");
        Assert.assertNotNull(datawbRemoteService.getIsHasDirFlag(list));
    }

    @Test
    public void testExecInfo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, ConstantInterface.class, HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {
                })
        );
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("1231");
        List<TechnicalChangeExecInfoEntityDTO> dtoList = new ArrayList<>();
        TechnicalChangeExecInfoEntityDTO dto = new TechnicalChangeExecInfoEntityDTO();
        dto.setSn("22222");
        dtoList.add(dto);
        try{
            datawbRemoteService.deleteTechnicalInfoBySn(dtoList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TECHNICAL_INFO_LOST, e.getMessage());
        }
    }

    @Test
    public void deleteTechnicalInfo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, ConstantInterface.class, HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {
                })
        );
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("1231");
        TechnicalSummaryInfoDTO dto = new TechnicalSummaryInfoDTO();
        dto.setId("22222");
        try{
            datawbRemoteService.deleteTechnicalInfo(dto);
        } catch (Exception e){
            Assert.assertEquals(MessageId.TECHNICAL_INFO_LOST, e.getMessage());
        }
    }

    @Test
    public void updateTechnicalInfo() throws Exception {
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, ConstantInterface.class, HttpRemoteUtil.class);
        PowerMockito.when(HttpRemoteUtil.remoteExe(anyString(), anyMap(), any(), any())).thenReturn(
                JSON.toJSONString(new ServiceData() {
                })
        );
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("1231");
        TechnicalSummaryInfoDTO dto = new TechnicalSummaryInfoDTO();
        dto.setId("22222");
        try{
            datawbRemoteService.insertTechnicalInfo(dto);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TECHNICAL_INFO_LOST, e.getMessage());
        }
    }

    @Test
    public void pageSelectSPMDateForTechnicalChangeTest() throws Exception {
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.when(SpringContextUtil.getBean("localeResolverMes")).thenReturn(lmb);
        PowerMockito.mockStatic(CommonUtils.class, HttpClientUtil.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                JSON.toJSONString("msg"));
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, ConstantInterface.class);
        SynchronizeSpmDateDTO dto = new SynchronizeSpmDateDTO();
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("1231");
        ServiceData serviceData = new ServiceData();
        serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        serviceData.setBo(new ArrayList<>());
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any()))
                .thenReturn(JSON.toJSONString(serviceData));
        try {
            datawbRemoteService.pageSelectSPMDateForTechnicalChange(dto, 1, 2);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        try {
            datawbRemoteService.pageSelectSPMDateSnForTechnicalChange(dto, 1, 2);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        try {
            datawbRemoteService.pageSelectSpecialSPMDataSn(dto, 1, 2);
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }

    @Test
    public void selectTechSPMDataOfHeadTest() throws Exception {
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.when(SpringContextUtil.getBean("localeResolverMes")).thenReturn(lmb);
        PowerMockito.mockStatic(CommonUtils.class, HttpClientUtil.class);
        PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
                JSON.toJSONString("msg"));
        PowerMockito.mockStatic(MicroServiceRestUtil.class, MESHttpHelper.class, ConstantInterface.class);
        SynchronizeSpmDateDTO dto = new SynchronizeSpmDateDTO();
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("1231");
        ServiceData serviceData = new ServiceData();
        serviceData.setCode(new RetCode(RetCode.SUCCESS_CODE, RetCode.SUCCESS_MSGID));
        serviceData.setBo(new ArrayList<>());
        PowerMockito.when(MicroServiceRestUtil.invokeService(any(), any(), any(), any(), any(), any()))
                .thenReturn(JSON.toJSONString(serviceData));
        try {
            datawbRemoteService.selectTechSPMDataOfHead();
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
        try {
            datawbRemoteService.selectTechSPMDataOfHead();
        } catch (Exception e) {
            Assert.assertEquals(null, e.getMessage());
        }
    }


    @Test
    public void postBoardStoveMaintenanceSpm() throws Exception {
        PowerMockito.mockStatic(JSON.class);
        PowerMockito.mockStatic(HttpClientUtil.class);
        PowerMockito.mockStatic(ServiceDataBuilderUtil.class);
        PowerMockito.mockStatic(JacksonJsonConverUtil.class);
        PowerMockito.mockStatic(ConstantInterface.class);
        Assert.assertNull(DatawbRemoteService.postBoardStoveMaintenanceSpm(1));
        Assert.assertTrue(CollectionUtils.isEmpty(DatawbRemoteService.getIncrementalItem(new MtlSystemItemsDTO())));
        PowerMockito.when(ConstantInterface.getUrlStatic(Mockito.any())).thenReturn("!23123");
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.any(), Mockito.anyString(), Mockito.anyMap())).thenReturn("123123");
        PowerMockito.when(ServiceDataBuilderUtil.checkHttpResponseThenReturnBo(Mockito.any())).thenReturn("qweewqq");
        Assert.assertTrue(CollectionUtils.isEmpty(DatawbRemoteService.getIncrementalItem(new MtlSystemItemsDTO())));
    }

    /* Started by AICoder, pid:w210as27b4g959e14c89089f307d0449b6150002 */
    @Test
    public void statByTroubleSmallCode() {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://imes.local.zte.com.cn/zte-mes-manufactureshare-datawbsys/mtnRepairLines/statByTroubleSmallCode");
        PowerMockito.mockStatic(HttpClientUtil.class);
        ServiceData<Object> serviceData = new ServiceData<>();
        PowerMockito.when(HttpClientUtil.httpGet(Mockito.anyString(), Mockito.anyMap(), Mockito.any())).thenReturn(JSONObject.toJSONString(serviceData));
        List<PmRepairInfoStatDTO> result = DatawbRemoteService.statByTroubleSmallCode("", "");
        Assert.assertTrue(result.isEmpty());

        ServiceData<List<PmRepairInfoStatDTO>> servData = new ServiceData<>();
        List<PmRepairInfoStatDTO> statDTOS = new ArrayList<>();
        PmRepairInfoStatDTO statDTO = new PmRepairInfoStatDTO();
        statDTO.setCount(5);
        statDTO.setRepairProductType("维修大类");
        statDTO.setRepairProductStype("维修小类");
        statDTOS.add(statDTO);
        servData.setBo(statDTOS);
        PowerMockito.when(HttpClientUtil.httpGet(Mockito.anyString(), Mockito.anyMap(), Mockito.any())).thenReturn(JSONObject.toJSONString(servData));
        result = DatawbRemoteService.statByTroubleSmallCode("122396531007", "面板侧ETH以太网环回测试_光口");
        Assert.assertFalse(result.isEmpty());
    }

    @Test
    public void statByTroubleSmallCodeAndSiteNo() {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://imes.local.zte.com.cn/zte-mes-manufactureshare-datawbsys/mtnRepairLines/statByTroubleSmallCodeAndSiteNo");
        PowerMockito.mockStatic(HttpClientUtil.class);
        ServiceData<Object> serviceData = new ServiceData<>();
        PowerMockito.when(HttpClientUtil.httpGet(Mockito.anyString(), Mockito.anyMap(), Mockito.any())).thenReturn(JSONObject.toJSONString(serviceData));
        List<PmRepairInfoStatDTO> result = DatawbRemoteService.statByTroubleSmallCodeAndSiteNo("", "");
        Assert.assertTrue(result.isEmpty());

        ServiceData<List<PmRepairInfoStatDTO>> servData = new ServiceData<>();
        List<PmRepairInfoStatDTO> statDTOS = new ArrayList<>();
        PmRepairInfoStatDTO statDTO = new PmRepairInfoStatDTO();
        statDTO.setCount(5);
        statDTO.setRepairProductType("维修大类");
        statDTO.setRepairProductStype("维修小类");
        statDTO.setLocationNo("A1B5");
        statDTOS.add(statDTO);
        servData.setBo(statDTOS);
        PowerMockito.when(HttpClientUtil.httpGet(Mockito.anyString(), Mockito.anyMap(), Mockito.any())).thenReturn(JSONObject.toJSONString(servData));
        result = DatawbRemoteService.statByTroubleSmallCodeAndSiteNo("122396531007", "面板侧ETH以太网环回测试_光口");
        Assert.assertFalse(result.isEmpty());
    }
    /* Ended by AICoder, pid:w210as27b4g959e14c89089f307d0449b6150002 */
    @Test
    public void getTaskNoStatusByErp() {
        PowerMockito.mockStatic(ConstantInterface.class);
        PowerMockito.when(ConstantInterface.getUrlStatic(any())).thenReturn("http://imes.local.zte.com.cn/zte-mes-manufactureshare-datawbsys/mtnRepairLines/statByTroubleSmallCodeAndSiteNo");
        PowerMockito.mockStatic(HttpClientUtil.class);
        ServiceData<?> success = ServiceDataUtil.getSuccess();
        PowerMockito.when(HttpClientUtil.httpPostWithJSON(any(), any(), any()))
                .thenReturn(JSON.toJSONString(success));
        List<WipEntityInfoDTO> taskNoStatusByErp = DatawbRemoteService.getTaskNoStatusByErp(anyList());
        Assert.assertNull(taskNoStatusByErp);
    }
}
