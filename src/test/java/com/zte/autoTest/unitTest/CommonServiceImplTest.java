package com.zte.autoTest.unitTest;

import com.zte.application.SysLookupTypesService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.impl.CommonServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.domain.model.SysLookupValues;
import com.zte.interfaces.dto.SysLookupTypesDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022-04-22 11:26
 */
public class CommonServiceImplTest extends BaseTestCase {
    @InjectMocks
    private CommonServiceImpl commonServiceImpl;
    @Mock
    private SysLookupTypesService sysLookupTypesService;
    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Before
    public void init() {

    }

    @Test
    public void getIMESBasicUrl() throws MesBusinessException {
        SysLookupTypesDTO sysLookupTypesDTO = new SysLookupTypesDTO();
        sysLookupTypesDTO.setRemark("ccc");
        PowerMockito.when(sysLookupTypesService.selectSysLookupTypeByLookupType(Mockito.anyObject()))
                .thenReturn(sysLookupTypesDTO);
        try{
            commonServiceImpl.getIMESBasicUrl();
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }

    @Test
    public void setRequestHeadProperties() throws MesBusinessException {
        Map<String, String> requestHeader = new HashMap<>();
        SysLookupValues sysLookupValues = new SysLookupValues();
        sysLookupValues.setLookupMeaning("23");
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(Mockito.anyObject()))
                .thenReturn(sysLookupValues);
        try{
            commonServiceImpl.setRequestHeadProperties(requestHeader);
        }catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
    }
}

