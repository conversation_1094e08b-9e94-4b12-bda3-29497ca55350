package com.zte.autoTest.unitTest;

import com.alibaba.fastjson.JSON;
import com.zte.application.IMESLogService;
import com.zte.application.ProgramVerifyConfirmInfoService;
import com.zte.application.ResourceApplyRecordService;
import com.zte.application.ResourceInfoDetailService;
import com.zte.application.ResourceInfoService;
import com.zte.application.ResourceOptLogService;
import com.zte.application.ResourceUseInfoService;
import com.zte.application.SysLookupValuesService;
import com.zte.application.impl.ResourceInfoServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.model.NumConstant;
import com.zte.common.utils.Constant;
import com.zte.domain.model.ResourceApplyRecord;
import com.zte.domain.model.ResourceInfoDetailRepository;
import com.zte.domain.model.ResourceInfoRepository;
import com.zte.domain.model.ResourceWarningRecord;
import com.zte.domain.model.ResourceWarningRecordRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.CallingB2BUniversalRemoteService;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.ApplicationNetworkIdentificationNumberDTO;
import com.zte.interfaces.dto.DownloadNetworkIdentificationNumberDTO;
import com.zte.interfaces.dto.NetworkAccessIdentificationNumberApplicationDTO;
import com.zte.interfaces.dto.ResourceInfoDetailDTO;
import com.zte.interfaces.dto.ResourceInfoEntityDTO;
import com.zte.interfaces.dto.SpSpecialityNalDTO;
import com.zte.itp.msa.core.model.PageRows;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.km.udm.model.RetCode;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.redis.RedisHelper;
import com.zte.springbootframe.util.CloudDiskHelper;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.springbootframe.util.FileUtils;
import com.zte.springbootframe.util.IdGenerator;
import com.zte.util.BaseTestCase;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.math.BigInteger;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.zte.common.model.NumConstant.NUM_ONE;
import static com.zte.common.utils.Constant.DATE_FORMAT;
import static com.zte.common.utils.Constant.LOOK_UP_CODE_6824001;
import static com.zte.common.utils.Constant.LOOK_UP_CODE_6825;
import static com.zte.common.utils.Constant.LOOK_UP_CODE_6846001;
import static com.zte.common.utils.Constant.NUM_ZERO;
import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Matchers.any;


@RunWith(PowerMockRunner.class)
@PrepareForTest({RedisHelper.class, RedisCacheUtils.class,SpringContextUtil.class,FileUtils.class,DateUtils.class})
public class ResourceInfoServiceImplTest extends BaseTestCase {

    @InjectMocks
    private ResourceInfoServiceImpl service;

    @Mock
    private ResourceWarningRecordRepository resourceWarningRecordRepository;

    @Mock
    private ResourceInfoRepository resourceInforepository;
    @Mock
    private ResourceInfoDetailRepository infoDetailRepository;

    @Mock
    private ProgramVerifyConfirmInfoService programVerifyConfirmInfoService;

    @Mock
    private IdGenerator idGenerator;

    @Mock
    ResourceOptLogService resourceOptLogService;
    @Mock
    DatawbRemoteService datawbRemoteService;
    @Mock
    ResourceUseInfoService resourceUseInfoService;
    @Mock
    SysLookupValuesService sysLookupValuesService;
    @Mock
    CallingB2BUniversalRemoteService callingB2BUniversalRemoteService;
    @Mock
    ResourceApplyRecordService resourceApplyRecordService;
    @Mock
    IMESLogService imesLogService;
    @Mock
    ResourceInfoDetailService resourceInfoDetailService;

    @Mock
    private ResourceInfoService resourceInfoService;

    @Mock
    private CloudDiskHelper cloudDiskHelper;
    @Mock
    private File file;
    @Mock
    private FileInputStream fileInputStream;
    @Mock
    private EmailUtils emailUtils;
    @Test
    public void callB2B()throws Exception{
        SysLookupValues sysLookupValues = new SysLookupValues();
        Assert.assertSame(service.callB2B(new ResourceInfoEntityDTO(),new NetworkAccessIdentificationNumberApplicationDTO()) , NumConstant.NUM_ZERO);
        PowerMockito.when(resourceApplyRecordService.insert(any())).thenReturn(NUM_ONE);
        Assert.assertSame(service.callB2B(new ResourceInfoEntityDTO(),new NetworkAccessIdentificationNumberApplicationDTO()) , NUM_ONE);
    }
    @Test
    public void updateSysLookupValuesById(){
        SysLookupValues sysLookupValues = new SysLookupValues();
        Assert.assertSame(service.updateSysLookupValuesById(new Date(),sysLookupValues,0,"") , NumConstant.NUM_ZERO);
        Assert.assertSame(service.updateSysLookupValuesById(new Date(),sysLookupValues,1,"1") , NumConstant.NUM_ZERO);
        PowerMockito.when(sysLookupValuesService.updateSysLookupValuesById(any())).thenReturn(1);
        Assert.assertSame(service.updateSysLookupValuesById(new Date(),sysLookupValues,1,"") , NUM_ONE);
    }
    @Test
    public void setBenchmarkQuantity()throws Exception{
        PowerMockito.mockStatic(RedisHelper.class,SpringContextUtil.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(false);
        try {
            service.setBenchmarkQuantity("123");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.OPERATION_IN_PROGRESS_PLEASE_WAIT, e.getMessage());
        }
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.eq(LOOK_UP_CODE_6824001))).thenReturn(null);
        try {
            service.setBenchmarkQuantity("123");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getMessage());
        }
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.eq(LOOK_UP_CODE_6824001))).thenReturn(sysLookupValues);
        service.setBenchmarkQuantity("123");
        sysLookupValues.setLookupMeaning("2023-10-10 10:10:00");
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.eq(LOOK_UP_CODE_6824001))).thenReturn(sysLookupValues);
        service.setBenchmarkQuantity("123");
        List<SysLookupValues> sysLookupValuesList = new ArrayList<>();
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.eq(LOOK_UP_CODE_6825))).thenReturn(sysLookupValuesList);
        service.setBenchmarkQuantity("123");
        sysLookupValuesList.add(sysLookupValues);
        PowerMockito.when(sysLookupValuesService.findByLookupType(Mockito.eq(LOOK_UP_CODE_6825))).thenReturn(sysLookupValuesList);
        service.setBenchmarkQuantity("123");
    }

    @Test
    public void automaticApplicationOfIdentificationNumber()throws Exception{
        PowerMockito.mockStatic(RedisHelper.class,SpringContextUtil.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(false);
        try {
            service.automaticApplicationOfIdentificationNumber("123");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.OPERATION_IN_PROGRESS_PLEASE_WAIT, e.getMessage());
        }
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<ResourceInfoEntityDTO> resourceInfoEntityDTOList = new ArrayList<>();
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO());
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO(){{setBenchmarkQty(0L);}});
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO(){{setBenchmarkQty(1L);}});
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO(){{setBenchmarkQty(3000000L);}});
        PowerMockito.when(resourceInforepository.getTheApplicationResourceNumber(Mockito.eq("123"),Mockito.eq(""))).thenReturn(resourceInfoEntityDTOList);
        try {
            service.automaticApplicationOfIdentificationNumber("123");
        } catch (Exception e) {
            Assert.assertNull(e.getMessage());
        }
        PowerMockito.when(SpringContextUtil.getBean(Constant.RESOURCE_INFO_SERVICE, ResourceInfoService.class)).thenReturn(resourceInfoService);
        PowerMockito.when(resourceInfoService.callB2B(Mockito.any(), Mockito.any())).thenThrow(new MesBusinessException(RetCode.SUCCESS_CODE,MessageId.CUSTOMIZE_MSG,new Object[]{"1"}));
        ReflectionTestUtils.setField(service, "applyForBatchQuantity", 100000L);
        service.automaticApplicationOfIdentificationNumber("123");

    }

    @Test
    public void automaticDownloadFlagNumber()throws Exception{
        PowerMockito.mockStatic(RedisHelper.class,SpringContextUtil.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(false);
        try {
            service.automaticDownloadFlagNumber("123");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.OPERATION_IN_PROGRESS_PLEASE_WAIT, e.getMessage());
        }
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<ResourceInfoEntityDTO> resourceInfoEntityDTOList = new ArrayList<>();
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO());
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO(){{setBenchmarkQty(0L);}});
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO(){{setBenchmarkQty(1L);}});
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO(){{setBenchmarkQty(3000000L);}});
        PowerMockito.when(resourceInforepository.getTheApplicationResourceNumber(Mockito.eq("123"),Mockito.eq(""))).thenReturn(resourceInfoEntityDTOList);
        ReflectionTestUtils.setField(service, "autoDownLoadHour", 6);
        ReflectionTestUtils.setField(service, "autoDownloadAlarmHour", 24);
        ReflectionTestUtils.setField(service, "autoDownloadOverdueDay", 7);
        PowerMockito.when(resourceApplyRecordService.queryPage(any())).thenReturn(new PageRows<>());
        service.automaticDownloadFlagNumber("123");

    }

    @Test
    public void automaticDownloadFlagNumber2()throws Exception{
        PowerMockito.mockStatic(RedisHelper.class,SpringContextUtil.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);

        List<ResourceApplyRecord> resourceApplyRecords = new ArrayList<>();
        resourceApplyRecords.add(new ResourceApplyRecord(){{setApplyId("id");}});
        PowerMockito.when(SpringContextUtil.getBean(Constant.RESOURCE_INFO_SERVICE, ResourceInfoService.class)).thenReturn(resourceInfoService);
        PowerMockito.when(resourceInfoService.callB2BDownload(Mockito.any())).thenThrow(new MesBusinessException(RetCode.SUCCESS_CODE,MessageId.CUSTOMIZE_MSG));
        PowerMockito.when(SpringContextUtil.getBean(Constant.RESOURCE_APPLY_RECORD_SERVICE, ResourceApplyRecordService.class)).thenReturn(resourceApplyRecordService);
        Date date = DateUtils.parseDate("20231211",DATE_FORMAT);
        PowerMockito.mockStatic(DateUtils.class);
        PowerMockito.when(DateUtils.addDays(any(),anyInt())).thenReturn(date);
        PowerMockito.when(resourceApplyRecordService.queryList(eq(date),eq("123"),eq(""))).thenReturn(resourceApplyRecords);
        ReflectionTestUtils.setField(service, "autoDownLoadHour", 6);
        ReflectionTestUtils.setField(service, "autoDownloadAlarmHour", 24);
        ReflectionTestUtils.setField(service, "autoDownloadOverdueDay", 7);
        service.automaticDownloadFlagNumber("123");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);

    }


    @Test
    public void callB2BDownload2()throws Exception{
        PowerMockito.mockStatic(RedisHelper.class,SpringContextUtil.class);
        List<ResourceApplyRecord> resourceApplyRecords = new ArrayList<>();
        resourceApplyRecords.add(new ResourceApplyRecord());
        PowerMockito.when(SpringContextUtil.getBean(Constant.RESOURCE_INFO_SERVICE, ResourceInfoService.class)).thenReturn(resourceInfoService);

        PowerMockito.when(SpringContextUtil.getBean(Constant.RESOURCE_APPLY_RECORD_SERVICE, ResourceApplyRecordService.class)).thenReturn(resourceApplyRecordService);
        PowerMockito.when(resourceInfoService.callB2BDownload(Mockito.any())).thenThrow(new Exception());
        service.callB2BDownload(resourceApplyRecords);
        Assert.assertNotNull(resourceApplyRecords);
    }

    @Test
    public void callB2BDownload3()throws Exception{
        PowerMockito.mockStatic(RedisHelper.class,SpringContextUtil.class);
        List<ResourceApplyRecord> resourceApplyRecords = new ArrayList<>();
        resourceApplyRecords.add(new ResourceApplyRecord());
        PowerMockito.when(SpringContextUtil.getBean(Constant.RESOURCE_INFO_SERVICE, ResourceInfoService.class)).thenReturn(resourceInfoService);

        PowerMockito.when(SpringContextUtil.getBean(Constant.RESOURCE_APPLY_RECORD_SERVICE, ResourceApplyRecordService.class)).thenReturn(resourceApplyRecordService);
        PowerMockito.when(resourceInfoService.callB2B(Mockito.any(), Mockito.any())).thenThrow(new MesBusinessException(RetCode.SUCCESS_CODE,MessageId.CUSTOMIZE_MSG));
        service.callB2BDownload(resourceApplyRecords);
        Assert.assertNotNull(resourceApplyRecords);
    }

    @Test
    public void automaticApplicationOfIdentificationNumber2()throws Exception{
        PowerMockito.mockStatic(RedisHelper.class,SpringContextUtil.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<ResourceInfoEntityDTO> resourceInfoEntityDTOList = new ArrayList<>();
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO());
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO(){{setBenchmarkQty(0L);}});
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO(){{setBenchmarkQty(1L);}});
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO(){{setBenchmarkQty(3000000L);}});
        PowerMockito.when(resourceInforepository.getTheApplicationResourceNumber(Mockito.eq("123"),Mockito.eq(""))).thenReturn(resourceInfoEntityDTOList);
        PowerMockito.when(SpringContextUtil.getBean(Constant.RESOURCE_INFO_SERVICE, ResourceInfoService.class)).thenReturn(resourceInfoService);
        PowerMockito.when(resourceInfoService.callB2B(Mockito.any(), Mockito.any())).thenThrow(new MesBusinessException(RetCode.SUCCESS_CODE,MessageId.CUSTOMIZE_MSG));
        ReflectionTestUtils.setField(service, "applyForBatchQuantity", 100000L);
        service.automaticApplicationOfIdentificationNumber("123");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void automaticApplicationOfIdentificationNumber3()throws Exception{
        PowerMockito.mockStatic(RedisHelper.class,SpringContextUtil.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<ResourceInfoEntityDTO> resourceInfoEntityDTOList = new ArrayList<>();
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO());
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO(){{setBenchmarkQty(0L);}});
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO(){{setBenchmarkQty(1L);}});
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO(){{setBenchmarkQty(3000000L);}});
        PowerMockito.when(resourceInforepository.getTheApplicationResourceNumber(Mockito.eq("123"),Mockito.eq(""))).thenReturn(resourceInfoEntityDTOList);
        PowerMockito.when(SpringContextUtil.getBean(Constant.RESOURCE_INFO_SERVICE, ResourceInfoService.class)).thenReturn(resourceInfoService);
        PowerMockito.when(resourceInfoService.callB2B(Mockito.any(), Mockito.any())).thenThrow(new MesBusinessException(RetCode.SUCCESS_CODE,MessageId.CUSTOMIZE_MSG,new Object[]{}));
        ReflectionTestUtils.setField(service, "applyForBatchQuantity", 100000L);
        service.automaticApplicationOfIdentificationNumber("123");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void automaticApplicationOfIdentificationNumber4()throws Exception{
        PowerMockito.mockStatic(RedisHelper.class,SpringContextUtil.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<ResourceInfoEntityDTO> resourceInfoEntityDTOList = new ArrayList<>();
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO(){{setResourceNo("123");setBenchmarkQty(10L);setLastUpdatedBy("system");}});
        PowerMockito.when(resourceInforepository.getTheApplicationResourceNumber(Mockito.eq("123"),Mockito.eq(""))).thenReturn(resourceInfoEntityDTOList);
        PowerMockito.when(SpringContextUtil.getBean(Constant.RESOURCE_INFO_SERVICE, ResourceInfoService.class)).thenReturn(resourceInfoService);

        PowerMockito.when(resourceInfoService.callB2B(Mockito.any(), Mockito.any())).thenThrow(new MesBusinessException(RetCode.SUCCESS_CODE,MessageId.CUSTOMIZE_MSG,new Object[]{}));
        ReflectionTestUtils.setField(service, "applyForBatchQuantity", 100000L);
        service.automaticApplicationOfIdentificationNumber("123");

        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.eq(LOOK_UP_CODE_6846001))).thenReturn(sysLookupValues);
        service.automaticApplicationOfIdentificationNumber("123");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void automaticApplicationOfIdentificationNumber5()throws Exception{
        PowerMockito.mockStatic(RedisHelper.class,SpringContextUtil.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        List<ResourceInfoEntityDTO> resourceInfoEntityDTOList = new ArrayList<>();
        resourceInfoEntityDTOList.add(new ResourceInfoEntityDTO(){{setResourceNo("123");setBenchmarkQty(10L);setLastUpdatedBy("system");}});
        PowerMockito.when(resourceInforepository.getTheApplicationResourceNumber(Mockito.eq("123"),Mockito.eq(""))).thenReturn(resourceInfoEntityDTOList);
        PowerMockito.when(SpringContextUtil.getBean(Constant.RESOURCE_INFO_SERVICE, ResourceInfoService.class)).thenReturn(resourceInfoService);
        ApplicationNetworkIdentificationNumberDTO dto = new ApplicationNetworkIdentificationNumberDTO(){{setRspCode("0001");setErrorInfo("失败");}};
        PowerMockito.when(resourceInfoService.callB2B(Mockito.any(), Mockito.any())).thenThrow(new MesBusinessException(RetCode.SUCCESS_CODE,MessageId.CUSTOMIZE_MSG,new Object[]{JSON.toJSONString(dto)}));
        ReflectionTestUtils.setField(service, "applyForBatchQuantity", 100000L);
        SysLookupValues sysLookupValues = new SysLookupValues(){{setLookupMeaning("00000000");}};
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.eq(LOOK_UP_CODE_6846001))).thenReturn(sysLookupValues);
        PowerMockito.when(emailUtils.sendMail(Mockito.any(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString(),Mockito.anyString())).thenReturn(true);
        service.automaticApplicationOfIdentificationNumber("123");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void pageList() throws Exception {
        ResourceInfoEntityDTO dto = new ResourceInfoEntityDTO();
        dto.setResourceType("MAC");
        dto.setResourceStr("D8-A8-C8-E2-44-F0");
        Page<ResourceInfoEntityDTO> pageInfo = service.pageList(dto);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }

    @Test
    public void pageListTest() throws Exception {
        ResourceInfoEntityDTO dto = new ResourceInfoEntityDTO();
        dto.setResourceType("GPON-SN");
        dto.setResourceStr("ZTEGCAA00000");
        Page<ResourceInfoEntityDTO> pageInfo = service.pageList(dto);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }

    @Test
    public void pageListNalTest() throws Exception {
        ResourceInfoEntityDTO dto = new ResourceInfoEntityDTO();
        dto.setResourceType("NAL");
        dto.setResourceStr("ZTEGCAA00000");
        PowerMockito.when(infoDetailRepository.findResourceNoByNum(any())).thenReturn(null);
        service.pageList(dto);
        dto.setResourceStr("ZTEGCAA00000");
        PowerMockito.when(infoDetailRepository.findResourceNoByNum(any())).thenReturn("abc");
        service.pageList(dto);
        dto.setResourceStr("ZTEGCAA00000");
        dto.setResourceNo("zxc");
        Page<ResourceInfoEntityDTO> pageInfo = service.pageList(dto);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }
    @Test
    public void setWarningInfo() throws Exception {
        List<ResourceInfoEntityDTO> resourceInfolist = new ArrayList<>();
        service.setWarningInfo(resourceInfolist);
        resourceInfolist.add(new ResourceInfoEntityDTO());
        resourceInfolist.add(new ResourceInfoEntityDTO());
        service.setWarningInfo(resourceInfolist);
        resourceInfolist.get(0).setResourceNo("1");
        resourceInfolist.get(1).setResourceNo("2");

        List<ResourceWarningRecord> recordList = new ArrayList<>();
        ResourceWarningRecord resourceWarningRecord = new ResourceWarningRecord();
        resourceWarningRecord.setResourceNo("0");
        ResourceWarningRecord resourceWarningRecord1 = new ResourceWarningRecord();
        resourceWarningRecord1.setResourceNo("1");
        recordList.add(resourceWarningRecord);
        recordList.add(resourceWarningRecord1);

        PowerMockito.when(resourceWarningRecordRepository.selectRecordByResourceNos(any())).thenReturn(recordList);
        service.setWarningInfo(resourceInfolist);
        Assert.assertNotNull(recordList);
    }

    @Test
    public void setUserName() throws Exception {
        ResourceInfoEntityDTO dto = new ResourceInfoEntityDTO();
        dto.setCreateBy("10244");
        dto.setLastUpdatedBy("10244");
        List<ResourceInfoEntityDTO> list = new ArrayList<>();
        list.add(dto);
        Map<String, String> map = new HashMap<>();
        map.put("10244", "lll");
        PowerMockito.when(programVerifyConfirmInfoService.getUserNew(any())).thenReturn(map);
        service.setUserName(list);
        Assert.assertNotNull(list);
        dto.setTagApplicant("10244");
        service.setUserName(list);
        Assert.assertNotNull(list);
        dto.setCertAdministrator("10244");
        service.setUserName(list);
        Assert.assertNotNull(list);
    }

    @Test
    public void save() throws Exception {
        ResourceInfoEntityDTO dto = new ResourceInfoEntityDTO();
        dto.setResourceType("GPON-SN");
        dto.setResourceEnd("ZTEGCADFFFFF");
        dto.setResourceStart("ZTEGCADFFFFA");
        dto.setResourceNo("Test");
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(resourceInforepository.selectOverlappingTime(dto)).thenReturn(0);
        Assert.assertNotNull(service.save(dto));
    }

    @Test
    public void update() throws Exception {
        ResourceInfoEntityDTO dto = new ResourceInfoEntityDTO();
        dto.setResourceType("GPON-SN");
        dto.setResourceEnd("ZTEGCADFFFFF");
        dto.setResourceStart("ZTEGCADFFFFA");
        dto.setResourceNo("Test");
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(resourceInforepository.selectOverlappingTime(dto)).thenReturn(0);
        Assert.assertNotNull(service.update(dto));
    }

    @Test
    public void findMaxCount() throws Exception {
        PowerMockito.mockStatic(RedisCacheUtils.class);
        Assert.assertNotNull(service.findMaxCount("111"));
    }

    @Test
    public void getNextResource() {
        List<ResourceInfoEntityDTO> resourceInfoEntityDTOS = new ArrayList<>();
        ResourceInfoEntityDTO dto = new ResourceInfoEntityDTO();
        dto.setAttribute4("5");
        dto.setAvailableQuantity(BigInteger.ONE);
        resourceInfoEntityDTOS.add(dto);
        PowerMockito.when(resourceInforepository.getList(any())).thenReturn(resourceInfoEntityDTOS);
        PowerMockito.when(resourceInforepository.batchUpdateSelectivity(any())).thenReturn(1);
        Assert.assertNotNull(service.getNextResource("test"));
    }

    @Test
    public void findDeviceByResourceNoTest(){
        service.findDeviceByResourceNo(null);
        PowerMockito.when(resourceInforepository.findDeviceByResourceNo(any())).thenReturn(new ResourceInfoEntityDTO());
        Assert.assertNotNull(service.findDeviceByResourceNo("123"));
    }

    @Test
    public void findDeviceByResourceNosTest(){
        List<String> list = new ArrayList<>();
        service.findDeviceByResourceNos(list);
        PowerMockito.when(resourceInforepository.findDeviceByResourceNos(any())).thenReturn(new ArrayList<>());
        list.add("123");
        Assert.assertNotNull(service.findDeviceByResourceNos(list));
    }

    @Test
    public void networkAllocation()throws Exception{
        PowerMockito.mockStatic(RedisHelper.class,SpringContextUtil.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(false);
        try {
            service.networkAllocation("",1,"","");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.CURRENT_RESOURCE_NUMBER_IS_OPERATION, e.getMessage());
        }
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        PowerMockito.when(resourceInforepository.selectByResourceNo(any())).thenReturn(null);
        try {
            service.networkAllocation("",1,"","");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.RESOURCE_WARNING_DATABASE_RESOURCE_IS_EMPTY, e.getMessage());
        }
        ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
        resourceInfoEntityDTO.setAvailableQuantity(null);
        PowerMockito.when(resourceInforepository.selectByResourceNo(any())).thenReturn(resourceInfoEntityDTO);
        try {
            service.networkAllocation("",10,"","");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.THE_MODEL_CODE_IS_EMPTY, e.getMessage());
        }
        resourceInfoEntityDTO.setAvailableQuantity(null);
        resourceInfoEntityDTO.setModelNumber("0155201");
        PowerMockito.when(resourceInforepository.selectByResourceNo(any())).thenReturn(resourceInfoEntityDTO);
        try {
            service.networkAllocation("",10,"","");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.RESOURCE_NO_AVAILABLE_QUANTITY_NOT_ENOUGH, e.getMessage());
        }
        resourceInfoEntityDTO.setAvailableQuantity(new BigInteger("5"));
        PowerMockito.when(resourceInforepository.selectByResourceNo(any())).thenReturn(resourceInfoEntityDTO);
        PowerMockito.when(SpringContextUtil.getBean(Constant.RESOURCE_INFO_SERVICE, ResourceInfoService.class)).thenReturn(service);
        service.networkAllocation("",1,"","");
        service.networkAllocation("",0,"","");
        resourceInfoEntityDTO.setAvailableQuantity(BigInteger.valueOf(5000));
        service.networkAllocation("",5000,"","");
    }

    @Test
    public void batchProcessingOfResourceNumberInformation(){
        ResourceInfoEntityDTO resourceInfoEntityDTO = new ResourceInfoEntityDTO();
        List<ResourceInfoDetailDTO> resourceInfoDetailDTOList = new ArrayList<>();
        resourceInfoDetailDTOList.add(new ResourceInfoDetailDTO(){{setResourceNum("2");setBusinessId("2");}});
        service.batchProcessingOfResourceNumberInformation("",resourceInfoEntityDTO,resourceInfoDetailDTOList);
        Assert.assertNotNull(resourceInfoEntityDTO);
        Assert.assertNotNull(resourceInfoDetailDTOList);
    }

    @Test
    public void homeEndIndividualParameterBinding(){
        PowerMockito.mockStatic(RedisHelper.class,SpringContextUtil.class);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(false);
        try {
            service.homeEndIndividualParameterBinding("",30,"");
        } catch (Exception e) {
            Assert.assertEquals(MessageId.OPERATION_IN_PROGRESS_PLEASE_WAIT, e.getMessage());
        }
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        service.homeEndIndividualParameterBinding("",30,"");
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    /*Started by AICoder, pid:97f43nd6ea3479e140be0b090096a18603237f7f*/
        @Test(timeout = 8000)
        public void testHomeEndIndividualParameterBinding_EmptyResourceInfoDetailDTOList() {
            List<ResourceInfoDetailDTO> resourceInfoDetailDTOList = new ArrayList<>();

            try {
                resourceInfoService.homeEndIndividualParameterBinding("123", 1, "RES1");
            } catch (Exception e) {
                fail("Should not have thrown any exception");
            }
        }
    /*Ended by AICoder, pid:97f43nd6ea3479e140be0b090096a18603237f7f*/
    @Test
    public void batchProcessing(){
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(Constant.RESOURCE_INFO_SERVICE, ResourceInfoService.class)).thenReturn(service);
        List<ResourceInfoDetailDTO> resourceInfoDetailDTOList = new ArrayList<>();
        resourceInfoDetailDTOList.add(new ResourceInfoDetailDTO(){{setResourceNum("2");setBusinessId("2");}});
        service.batchProcessing("",resourceInfoDetailDTOList);
        Assert.assertNotNull(resourceInfoDetailDTOList);
    }

    @Test
    public void batchProcessingResourceNumber(){
        PowerMockito.mockStatic(SpringContextUtil.class);
        PowerMockito.when(SpringContextUtil.getBean(Constant.RESOURCE_INFO_SERVICE, ResourceInfoService.class)).thenReturn(service);
        List<ResourceInfoDetailDTO> resourceInfoDetailDTOList = new ArrayList<>();
        resourceInfoDetailDTOList.add(new ResourceInfoDetailDTO(){{setResourceNum("2");setBusinessId("2");}});
        resourceInfoDetailDTOList.add(new ResourceInfoDetailDTO(){{setResourceNum("22");setResourceNo("555566665455");setBusinessId("2");}});
        List<SpSpecialityNalDTO> spSpecialityNalDTOList = new ArrayList<>();
        spSpecialityNalDTOList.add(new SpSpecialityNalDTO(){{setNetAccessSignNum("2");}});
        spSpecialityNalDTOList.add(new SpSpecialityNalDTO(){{setNetAccessSignNum("255");}});
        spSpecialityNalDTOList.add(new SpSpecialityNalDTO(){{setNetAccessSignNum("22");}});

        Map<String, ResourceInfoDetailDTO> resourceInfoDetailDTOMap = resourceInfoDetailDTOList.stream().collect(Collectors.toMap(k -> k.getResourceNum(), v -> v, (oldValue, newValue) -> newValue));
        service.batchProcessingResourceNumber("",resourceInfoDetailDTOMap,new ArrayList<>());
        service.batchProcessingResourceNumber("",resourceInfoDetailDTOMap,spSpecialityNalDTOList);

        spSpecialityNalDTOList.clear();
        spSpecialityNalDTOList.add(new SpSpecialityNalDTO());
        service.batchProcessingResourceNumber("",resourceInfoDetailDTOMap,spSpecialityNalDTOList);
        Assert.assertNotNull(spSpecialityNalDTOList);
        Assert.assertNotNull(resourceInfoDetailDTOMap);
    }

    
    @Test
    public void testCheckResourceNo(){
        PowerMockito.when(resourceInforepository.selectByResourceNo(any())).thenReturn(null);
        Assert.assertNull(service.checkResourceNo("123"));
        ResourceInfoEntityDTO dto = new ResourceInfoEntityDTO();
        PowerMockito.when(resourceInforepository.selectByResourceNo(any())).thenReturn(dto);
        Assert.assertFalse(service.checkResourceNo("123"));
        dto.setResourceStatus("INIT");
        PowerMockito.when(resourceInforepository.selectByResourceNo(any())).thenReturn(dto);
        Assert.assertTrue(service.checkResourceNo("123"));
        dto.setResourceStatus("USING");
        PowerMockito.when(resourceInforepository.selectByResourceNo(any())).thenReturn(dto);
        Assert.assertTrue(service.checkResourceNo("123"));
    }
    @Mock
    Future<String> submit;
    @Test
    public void testCheckResult() throws Exception {
        List<Future<String>> list = new ArrayList<>();
        list.add(submit);
        PowerMockito.when(submit.get()).thenReturn(null);
        service.checkResult(list);
        PowerMockito.when(submit.get()).thenReturn("123");
        Assert.assertThrows(MesBusinessException.class,()->service.checkResult(list));
    }

    /*Started by AICoder, pid:76a879be5c591ec149370a73d1ca78959791feb4*/
    @Test
    public void homeEndIndividualParameterBindTest() {
        // Given
        PowerMockito.mockStatic(RedisHelper.class);
        PowerMockito.when(sysLookupValuesService.selectSysLookupValuesById(Mockito.any())).thenReturn(null);
        PowerMockito.when(RedisHelper.setnx(anyString(), anyString(), anyInt())).thenReturn(true);
        try {
            service.homeEndIndividualParameterBind("empNo");
        } catch (Exception e) {
            fail("Should not have thrown any exception");
        }
    }
    /*Ended by AICoder, pid:76a879be5c591ec149370a73d1ca78959791feb4*/
}
