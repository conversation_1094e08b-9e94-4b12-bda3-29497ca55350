package com.zte.autoTest.unitTest;


import com.zte.application.impl.EdiSoSLineServiceImpl;
import com.zte.domain.model.EdiSoSLineRepository;
import com.zte.interfaces.dto.EdiSoSNewDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@RunWith(PowerMockRunner.class)
public class EdiSoSLineServiceImplTest extends BaseTestCase {

    @InjectMocks
    private EdiSoSLineServiceImpl ediSoSLineService;
    @Mock
    private EdiSoSLineRepository ediSoSLineRepository;

    @Test
    public void postHisInforBatch(){
        List<EdiSoSNewDTO> list=new ArrayList<>();
        EdiSoSNewDTO dto=new EdiSoSNewDTO();
        dto.setSerialkey(new BigDecimal(1));
        list.add(dto);
        Assert.assertNotNull(ediSoSLineService.postHisInforBatch(list));
    }

    @Test
    public void getHisInforInfo(){
        EdiSoSNewDTO dto=new EdiSoSNewDTO();
        dto.setSerialkey(new BigDecimal(1));
        Assert.assertNotNull(ediSoSLineService.getHisInforInfo(dto));
    }

    @Test
    public void deleteHisInforInfo(){
        EdiSoSNewDTO dto=new EdiSoSNewDTO();
        dto.setSerialkey(new BigDecimal(1));
        Assert.assertNotNull(ediSoSLineService.deleteHisInforInfo(dto));
    }

}
