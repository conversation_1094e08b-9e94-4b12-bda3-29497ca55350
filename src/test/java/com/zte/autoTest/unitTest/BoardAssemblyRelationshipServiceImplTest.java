package com.zte.autoTest.unitTest;

import com.zte.application.impl.BoardAssemblyRelationshipServiceImpl;
import com.zte.application.sncabind.PsTaskService;
import com.zte.common.model.MessageId;
import com.zte.domain.model.BBomHeaderRepository;
import com.zte.domain.model.BoardAssemblyRelationshipRepository;
import com.zte.domain.model.BsItemInfoRepository;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.interfaces.dto.BoardAssemblyRelationshipDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RunWith(PowerMockRunner.class)
public class BoardAssemblyRelationshipServiceImplTest extends BaseTestCase {

    @InjectMocks
    private BoardAssemblyRelationshipServiceImpl service;

    @Mock
    private BoardAssemblyRelationshipRepository boardAssemblyRelationshipRepository;

    @Mock
    private BBomHeaderRepository bBomHeaderRepository;

    @Mock
    private BsItemInfoRepository bsItemInfoRepository;

    @Mock
    private PsTaskService psTaskService;

    @Test
    public void getBoardAssemblyRelationshipTest() throws Exception {
        BoardAssemblyRelationshipDTO dto = new BoardAssemblyRelationshipDTO();
        try {
            service.getBoardAssemblyRelationship(null);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.BOARD_ASSEMBLY_QUERY_PARAMS_NOT_NULL.equals(e.getExMsgId()));
        }
        dto.setEndTime(new Date());
        try {
            service.getBoardAssemblyRelationship(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.BOARD_ASSEMBLY_QUERY_TIME_NOT_PAIRED.equals(e.getExMsgId()));
        }
        dto.setEndTime(null);
        try {
            service.getBoardAssemblyRelationship(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.BOARD_ASSEMBLY_QUERY_PARAMS_NOT_NULL.equals(e.getExMsgId()));
        }
        dto.setBeginTime(new Date());
        dto.setEndTime(new Date());
        dto.setParentSn("test1");
        try {
            service.getBoardAssemblyRelationship(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.BOARD_ASSEMBLY_QUERY_PARAMS_NOT_ONE_CONDITION.equals(e.getExMsgId()));
        }
        dto.setParentSn(null);
        dto.setPage(1);
        try {
            service.getBoardAssemblyRelationship(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.BOARD_ASSEMBLY_QUERY_PAGE_AND_ROW_NOT_NULL.equals(e.getExMsgId()));
        }
        dto.setRow(101);
        try {
            service.getBoardAssemblyRelationship(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.BOARD_ASSEMBLY_QUERY_ROW_TOO_LARGE.equals(e.getExMsgId()));
        }
        dto.setRow(50);
        Date cur = new Date();
        dto.setBeginTime(new Date(cur.getTime() - 1000L*60L*60L*24L*31L));
        dto.setEndTime(cur);
        try {
            service.getBoardAssemblyRelationship(dto);
        } catch (MesBusinessException e) {
            Assert.assertTrue(MessageId.BOARD_ASSEMBLY_QUERY_TIME_INTERVAL_EXCEEDS_ONE_MONTH.equals(e.getExMsgId()));
        }
        BoardAssemblyRelationshipDTO dto1 = new BoardAssemblyRelationshipDTO();
        dto1.setSn("testSn");
        List<BoardAssemblyRelationshipDTO> resultList = new ArrayList<>();
        PowerMockito.when(boardAssemblyRelationshipRepository.queryParentSnsRecursively(Mockito.anyString())).thenReturn(resultList);
        service.getBoardAssemblyRelationship(dto1);
        BoardAssemblyRelationshipDTO boardAssemblyRelationshipDTO = new BoardAssemblyRelationshipDTO();
        boardAssemblyRelationshipDTO.setItemCode("itemCode");
        boardAssemblyRelationshipDTO.setParentItemCode("parentItemCode");
        boardAssemblyRelationshipDTO.setSn("testSn");
        boardAssemblyRelationshipDTO.setParentSn("testParentSn");
        BoardAssemblyRelationshipDTO boardAssemblyRelationshipDTO1 = new BoardAssemblyRelationshipDTO();
        boardAssemblyRelationshipDTO1.setItemCode("parentItemCode");
        boardAssemblyRelationshipDTO1.setParentItemCode("parentItemCode1");
        boardAssemblyRelationshipDTO1.setSn("testParentSn");
        boardAssemblyRelationshipDTO1.setParentSn("testParentSn1");
        resultList.add(boardAssemblyRelationshipDTO);
        resultList.add(boardAssemblyRelationshipDTO1);
        List<BoardAssemblyRelationshipDTO> pcbVersionAndItemNameList = new ArrayList<>();
        BoardAssemblyRelationshipDTO pcbVersionAndItemName = new BoardAssemblyRelationshipDTO();
        pcbVersionAndItemName.setItemCode("itemCode");
        pcbVersionAndItemName.setItemName("itemName");
        pcbVersionAndItemName.setPcbVersion("pcb");
        BoardAssemblyRelationshipDTO pcbVersionAndItemName1 = new BoardAssemblyRelationshipDTO();
        pcbVersionAndItemName1.setItemCode("parentItemCode1");
        pcbVersionAndItemName1.setItemName("parentItemName1");
        pcbVersionAndItemName1.setPcbVersion("pcb1");
        pcbVersionAndItemNameList.add(pcbVersionAndItemName);
        pcbVersionAndItemNameList.add(pcbVersionAndItemName1);
        PowerMockito.when(bBomHeaderRepository.getPcbVersionByItemCodes(Mockito.anySet())).thenReturn(pcbVersionAndItemNameList);
        PowerMockito.when(bBomHeaderRepository.getItemNameByItemCodes(Mockito.anyList())).thenReturn(pcbVersionAndItemNameList);
        PowerMockito.when(bsItemInfoRepository.getItemNameByItemCodes(Mockito.anyList())).thenReturn(pcbVersionAndItemNameList);
        Page<BoardAssemblyRelationshipDTO> resultPage = service.getBoardAssemblyRelationship(dto1);
        Assert.assertTrue(resultPage.getRows().size() == 1);
        Assert.assertTrue(resultPage.getRows().get(0).getParentSn().equals("testParentSn1"));
        Assert.assertTrue(resultPage.getRows().get(0).getSn().equals("testSn"));
        Assert.assertTrue(resultPage.getRows().get(0).getItemCode().equals("itemCode"));
        Assert.assertTrue(resultPage.getRows().get(0).getParentItemCode().equals("parentItemCode1"));
        Assert.assertTrue(resultPage.getRows().get(0).getItemName().equals("itemName"));
        Assert.assertTrue(resultPage.getRows().get(0).getParentItemName().equals("parentItemName1"));
        Assert.assertTrue(resultPage.getRows().get(0).getPcbVersion().equals("pcb"));
        Assert.assertTrue(resultPage.getRows().get(0).getParentPcbVersion().equals("pcb1"));

        BoardAssemblyRelationshipDTO dto2 = new BoardAssemblyRelationshipDTO();
        dto2.setParentSn("testParentSn");
        List<BoardAssemblyRelationshipDTO> querySnsRecursivelyList = new ArrayList<>();
        PowerMockito.when(boardAssemblyRelationshipRepository.querySnsRecursivelyByParentSn(Mockito.anyString())).thenReturn(querySnsRecursivelyList);
        service.getBoardAssemblyRelationship(dto2);

        BoardAssemblyRelationshipDTO boardAssemblyRelationshipDTO2 = new BoardAssemblyRelationshipDTO();
        boardAssemblyRelationshipDTO2.setItemCode("itemCode");
        boardAssemblyRelationshipDTO2.setParentItemCode("parentItemCode");
        boardAssemblyRelationshipDTO2.setSn("testSn");
        boardAssemblyRelationshipDTO2.setParentSn("testParentSn");
        BoardAssemblyRelationshipDTO boardAssemblyRelationshipDTO3 = new BoardAssemblyRelationshipDTO();
        boardAssemblyRelationshipDTO3.setItemCode("itemCode1");
        boardAssemblyRelationshipDTO3.setParentItemCode("itemCode");
        boardAssemblyRelationshipDTO3.setSn("testSn1");
        boardAssemblyRelationshipDTO3.setParentSn("testSn");
        querySnsRecursivelyList.add(boardAssemblyRelationshipDTO2);
        querySnsRecursivelyList.add(boardAssemblyRelationshipDTO3);

        List<BoardAssemblyRelationshipDTO> pcbVersionAndItemNameList1 = new ArrayList<>();
        BoardAssemblyRelationshipDTO pcbVersionAndItemName2 = new BoardAssemblyRelationshipDTO();
        pcbVersionAndItemName2.setItemCode("itemCode");
        pcbVersionAndItemName2.setItemName("itemName");
        pcbVersionAndItemName2.setPcbVersion("pcb");
        BoardAssemblyRelationshipDTO pcbVersionAndItemName3 = new BoardAssemblyRelationshipDTO();
        pcbVersionAndItemName3.setItemCode("itemCode1");
        pcbVersionAndItemName3.setItemName("itemName1");
        pcbVersionAndItemName3.setPcbVersion("pcb1");
        BoardAssemblyRelationshipDTO pcbVersionAndItemName4 = new BoardAssemblyRelationshipDTO();
        pcbVersionAndItemName4.setItemCode("parentItemCode");
        pcbVersionAndItemName4.setItemName("parentItemName");
        pcbVersionAndItemName4.setPcbVersion("pcbParent");
        pcbVersionAndItemNameList1.add(pcbVersionAndItemName2);
        pcbVersionAndItemNameList1.add(pcbVersionAndItemName3);
        pcbVersionAndItemNameList1.add(pcbVersionAndItemName4);
        PowerMockito.when(bBomHeaderRepository.getPcbVersionByItemCodes(Mockito.anySet())).thenReturn(pcbVersionAndItemNameList1);
        PowerMockito.when(bBomHeaderRepository.getItemNameByItemCodes(Mockito.anyList())).thenReturn(pcbVersionAndItemNameList1);
        PowerMockito.when(bsItemInfoRepository.getItemNameByItemCodes(Mockito.anyList())).thenReturn(pcbVersionAndItemNameList1);

        Page<BoardAssemblyRelationshipDTO> resultPage1 = service.getBoardAssemblyRelationship(dto2);
        Assert.assertTrue(resultPage1.getRows().size() == 2);
        Assert.assertTrue(resultPage1.getRows().get(0).getParentSn().equals("testParentSn"));
        Assert.assertTrue(resultPage1.getRows().get(0).getSn().equals("testSn"));
        Assert.assertTrue(resultPage1.getRows().get(0).getItemCode().equals("itemCode"));
        Assert.assertTrue(resultPage1.getRows().get(0).getParentItemCode().equals("parentItemCode"));
        Assert.assertTrue(resultPage1.getRows().get(0).getItemName().equals("itemName"));
        Assert.assertTrue(resultPage1.getRows().get(0).getParentItemName().equals("parentItemName"));
        Assert.assertTrue(resultPage1.getRows().get(0).getPcbVersion().equals("pcb"));
        Assert.assertTrue(resultPage1.getRows().get(0).getParentPcbVersion().equals("pcbParent"));

        Assert.assertTrue(resultPage1.getRows().get(1).getParentSn().equals("testParentSn"));
        Assert.assertTrue(resultPage1.getRows().get(1).getSn().equals("testSn1"));
        Assert.assertTrue(resultPage1.getRows().get(1).getItemCode().equals("itemCode1"));
        Assert.assertTrue(resultPage1.getRows().get(1).getParentItemCode().equals("parentItemCode"));
        Assert.assertTrue(resultPage1.getRows().get(1).getItemName().equals("itemName1"));
        Assert.assertTrue(resultPage1.getRows().get(1).getParentItemName().equals("parentItemName"));
        Assert.assertTrue(resultPage1.getRows().get(1).getPcbVersion().equals("pcb1"));
        Assert.assertTrue(resultPage1.getRows().get(1).getParentPcbVersion().equals("pcbParent"));

        BoardAssemblyRelationshipDTO dto3 = new BoardAssemblyRelationshipDTO();
        dto3.setBeginTime(new Date());
        dto3.setEndTime(new Date());
        dto3.setPage(1);
        dto3.setRow(50);
        int total = boardAssemblyRelationshipRepository.getCountByScanTime(dto.getBeginTime(), dto.getEndTime());
        PowerMockito.when(boardAssemblyRelationshipRepository.getCountByScanTime(Mockito.any(), Mockito.any())).thenReturn(1);
        List<BoardAssemblyRelationshipDTO> queryList = new ArrayList<>();
        PowerMockito.when(boardAssemblyRelationshipRepository.queryByScanTime(Mockito.any(), Mockito.any(), Mockito.anyInt(), Mockito.anyInt())).thenReturn(queryList);
        service.getBoardAssemblyRelationship(dto3);

        BoardAssemblyRelationshipDTO boardAssemblyRelationshipDTO4 = new BoardAssemblyRelationshipDTO();
        boardAssemblyRelationshipDTO4.setItemCode("itemCode");
        boardAssemblyRelationshipDTO4.setParentItemCode("parentItemCode");
        boardAssemblyRelationshipDTO4.setSn("testSn");
        boardAssemblyRelationshipDTO4.setParentSn("testParentSn");
        queryList.add(boardAssemblyRelationshipDTO4);

        Page<BoardAssemblyRelationshipDTO> resultPage2 = service.getBoardAssemblyRelationship(dto3);
        Assert.assertTrue(resultPage2.getRows().size() == 1);
        Assert.assertTrue(resultPage2.getRows().get(0).getParentSn().equals("testParentSn"));
        Assert.assertTrue(resultPage2.getRows().get(0).getSn().equals("testSn"));
        Assert.assertTrue(resultPage2.getRows().get(0).getItemCode().equals("itemCode"));
        Assert.assertTrue(resultPage2.getRows().get(0).getParentItemCode().equals("parentItemCode"));
        Assert.assertTrue(resultPage2.getRows().get(0).getItemName().equals("itemName"));
        Assert.assertTrue(resultPage2.getRows().get(0).getParentItemName().equals("parentItemName"));
        Assert.assertTrue(resultPage2.getRows().get(0).getPcbVersion().equals("pcb"));
        Assert.assertTrue(resultPage2.getRows().get(0).getParentPcbVersion().equals("pcbParent"));
    }

    @Test
    public void handleNullParentCodeTest() throws Exception {
        List<BoardAssemblyRelationshipDTO> queryParentSnsRecursivelyList = new ArrayList<>();

        service.handleNullParentCode(queryParentSnsRecursivelyList);
        BoardAssemblyRelationshipDTO entity1 = new BoardAssemblyRelationshipDTO();
        entity1.setParentSn("712345600001");
        entity1.setParentItemCode("");
        queryParentSnsRecursivelyList.add(entity1);
        BoardAssemblyRelationshipDTO entity2 = new BoardAssemblyRelationshipDTO();
        entity2.setParentSn("sn2");
        entity2.setParentItemCode("21323");
        queryParentSnsRecursivelyList.add(entity2);
        BoardAssemblyRelationshipDTO entity3 = new BoardAssemblyRelationshipDTO();
        entity3.setParentSn("712344444");
        entity3.setParentItemCode("");
        queryParentSnsRecursivelyList.add(entity3);
        BoardAssemblyRelationshipDTO entity4 = new BoardAssemblyRelationshipDTO();
        entity4.setParentSn("12321");
        entity4.setParentItemCode("");
        queryParentSnsRecursivelyList.add(entity4);

        List<PsTask> psTaskList = new ArrayList<>();
        PsTask psTask1 = new PsTask();
        psTask1.setProdplanId("7123456");
        psTask1.setItemNo("item1");
        psTaskList.add(psTask1);
        PsTask psTask2 = new PsTask();
        psTask2.setProdplanId("7123");
        psTask2.setItemNo("item3");
        psTaskList.add(psTask2);
        PowerMockito.when(psTaskService.selectItemCodeByProd(Mockito.anyList())).thenReturn(psTaskList);

        service.handleNullParentCode(queryParentSnsRecursivelyList);

        Assert.assertTrue(queryParentSnsRecursivelyList.get(0).getParentItemCode().equals(psTask1.getItemNo()));
        Assert.assertTrue(queryParentSnsRecursivelyList.get(2).getParentItemCode().equals(psTask2.getItemNo()));
    }
}
