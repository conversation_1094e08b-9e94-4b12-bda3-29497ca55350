package com.zte.autoTest.unitTest;

import com.zte.application.BsAsyncDataService;
import com.zte.application.impl.BsAsyncDataServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BsAsyncDataRespository;
import com.zte.interfaces.dto.BsAsyncDataDTO;
import com.zte.springbootframe.common.model.Page;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/1/25
 **/
public class BsAsyncDataServiceImplTest extends BaseTestCase {
    @InjectMocks
    private BsAsyncDataServiceImpl asyncDataService;
    @Mock
    private BsAsyncDataRespository bsAsyncDataRespository;

    @Test
    public void queryAsyncByCondition() {
        Assert.assertNotNull(asyncDataService.queryAsyncByCondition(Mockito.any()));
    }

    @Test
    public void queryAsyncByConditionPage() {
        Page pageInfo = asyncDataService.queryAsyncByConditionPage(new BsAsyncDataDTO());
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }

    @Test
    public void insertSingleData() {
        Assert.assertNotNull(asyncDataService.insertSingleData(new BsAsyncDataDTO()));
    }

    @Test
    public void insertDataBatch() {
        Assert.assertNotNull(asyncDataService.insertDataBatch(new LinkedList<BsAsyncDataDTO>() {{
            add(new BsAsyncDataDTO());
        }}));
    }

    @Test
    public void updateDataById() {
        Assert.assertNotNull(asyncDataService.updateDataById(new BsAsyncDataDTO()));
    }

    @Test
    public void deleteDataById() {
        Assert.assertNotNull(asyncDataService.deleteDataById(new BsAsyncDataDTO()));
    }

    @Test
    public void updateDataBatch() {
        Assert.assertNotNull(asyncDataService.updateDataBatch(new LinkedList<BsAsyncDataDTO>() {{
            add(new BsAsyncDataDTO());
        }}));
    }

    @Test
    public void deleteDataByIds() {
        Assert.assertNotNull(asyncDataService.deleteDataByIds(new LinkedList<BsAsyncDataDTO>() {{
            add(new BsAsyncDataDTO());
        }}));
    }

    @Test
    public void deleteCycle() {
        List<BsAsyncDataDTO> resultList = new LinkedList<>();
        BsAsyncDataDTO a1 = new BsAsyncDataDTO();
        a1.setHeadId("123");
        resultList.add(a1);
        PowerMockito.when(bsAsyncDataRespository.queryHeadIdAsyncByConditionPage(Mockito.any())).thenReturn(resultList);
        asyncDataService.deleteCycle();
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }
}
