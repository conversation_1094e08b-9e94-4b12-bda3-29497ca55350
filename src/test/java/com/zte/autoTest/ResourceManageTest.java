package com.zte.autoTest;

import com.zte.common.authority.ResourceManage;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.NumConstant;
import com.zte.infrastructure.remote.UppRemoteService;
import com.zte.itp.authorityclient.client.AuthorityClient;
import com.zte.itp.authorityclient.dto.user.ConstraintResDTO;
import com.zte.itp.authorityclient.dto.user.ResourceResDTO;
import com.zte.itp.authorityclient.entity.output.RetCode;
import com.zte.itp.authorityclient.entity.output.ReturnResourceEntity;
import com.zte.itp.authorityclient.entity.output.ServiceData;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Matchers;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

import static org.mockito.Matchers.any;

/**
 * @Author: 10307315
 * @Date: 2022/1/18 下午2:19
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({ResourceManage.class, AuthorityClient.class})
public class ResourceManageTest extends BaseTestCase {
    @InjectMocks
    ResourceManage resourceManage;

    @Mock
    private RedisTemplate redisTemplate;

    @Mock
    private ListOperations<String, ReturnResourceEntity> listOperations;

    @Mock
    private RedisLock redisLock;

    @Mock
    private HttpServletRequest request;

    @Mock
    private UppRemoteService uppRemoteService;

    @Before
    public void before() throws Exception {
        resourceManage.init();
    }

    @Test
    public void getUacParamByRequest () {
        Assert.assertNotNull(resourceManage.getUacParamByRequest(request));
    }

    @Test(expected = MesBusinessException.class)
    public void getUserPower () throws Exception {
        PowerMockito.mockStatic(AuthorityClient.class);
        ServiceData sd = new ServiceData();
        RetCode retCode = new RetCode();
        retCode.setCode("00010");
        sd.setCode(retCode);
        ReturnResourceEntity returnResourceEntity = new ReturnResourceEntity();
        returnResourceEntity.setResourceId("test");
        HashMap test = new HashMap();
        test.put("test", returnResourceEntity);
        sd.setBo(test);
        PowerMockito.when(AuthorityClient.getUserPower(Mockito.any())).thenReturn(sd);

        Whitebox.invokeMethod(resourceManage, "getUserPower", "",new HashMap<>());
    }

    @Test
    public void getControlPermissionsTest() throws Exception {
        String empNo = "213123";
        Map<String, String> uacMap = new HashMap<>();
        uacMap.put("token", "test");
        uacMap.put("itp", "test");
        String[] listControl = {"test"};
        PowerMockito.mockStatic(AuthorityClient.class);
        com.zte.itp.authorityclient.entity.output.ServiceData sd = new ServiceData();
        List<ResourceResDTO> bo = new ArrayList<>();
        ResourceResDTO dto = new ResourceResDTO();
        dto.setResourceType(3);
        dto.setControlId("test");
        bo.add(dto);
        sd.setBo(bo);
        PowerMockito.when(AuthorityClient.userResources(any()))
                .thenReturn(sd);
        Assert.assertNotNull(resourceManage.getControlPermissions(empNo, uacMap, listControl));
    }

    @Test
    public void getOrgList() throws Exception {
        List<ReturnResourceEntity> userNemuList = new ArrayList<>();
        ReturnResourceEntity returnResourceEntity = new ReturnResourceEntity();
        returnResourceEntity.setResourceId("326");
        returnResourceEntity.setResourceName("中心工厂-res");
        returnResourceEntity.setParentCode("0");
        returnResourceEntity.setControlId("1");
        userNemuList.add(returnResourceEntity);
        PowerMockito.when(redisTemplate.opsForList()).thenReturn(listOperations);
        PowerMockito.when(listOperations.range(Mockito.anyString(), Mockito.anyLong(), Mockito.anyLong())).thenReturn(userNemuList);

        PowerMockito.when(redisTemplate.getExpire(Matchers.any())).thenReturn((long) -2);
        PowerMockito.when(redisTemplate.delete(Mockito.anyString())).thenReturn(Boolean.TRUE);
        PowerMockito.when(redisTemplate.expire(Mockito.anyString(), Mockito.anyLong(), Mockito.anyObject())).thenReturn(Boolean.TRUE);
        Assert.assertNotNull(resourceManage.getOrgList("10328274", new HashMap<>(), false));
    }

    /* Started by AICoder, pid:o5264ub0743dd5d14be70906908d463f164606f0 */
    private void setupRedisMocks(List<ReturnResourceEntity> userNemuList) {
        PowerMockito.when(redisTemplate.opsForList()).thenReturn(listOperations);
        PowerMockito.when(listOperations.range(Mockito.anyString(), Mockito.anyLong(), Mockito.anyLong())).thenReturn(userNemuList);

        PowerMockito.when(redisTemplate.getExpire(Matchers.any())).thenReturn((long) -2);
        PowerMockito.when(redisTemplate.delete(Mockito.anyString())).thenReturn(Boolean.TRUE);
        PowerMockito.when(redisTemplate.expire(Mockito.anyString(), Mockito.anyLong(), Mockito.anyObject())).thenReturn(Boolean.TRUE);
    }

    @Test
    public void getLubanList() throws Exception {
        List<ReturnResourceEntity> userNemuList = new ArrayList<>();
        ReturnResourceEntity returnResourceEntity = new ReturnResourceEntity();
        returnResourceEntity.setResourceId("326");
        returnResourceEntity.setResourceName("鲁班");
        returnResourceEntity.setParentCode("0");
        returnResourceEntity.setControlId("1");
        userNemuList.add(returnResourceEntity);

        setupRedisMocks(userNemuList);
        Assert.assertNotNull(resourceManage.getLubanList("10328274", new HashMap<>()));
    }

    @Test
    public void getLubanList2() throws Exception {
        List<ReturnResourceEntity> userNemuList = new ArrayList<>();
        ReturnResourceEntity returnResourceEntity = new ReturnResourceEntity();
        returnResourceEntity.setResourceId("326");
        returnResourceEntity.setResourceName("dsada");
        returnResourceEntity.setParentCode("0");
        returnResourceEntity.setControlId("1");
        userNemuList.add(returnResourceEntity);

        setupRedisMocks(userNemuList);
        Assert.assertNotNull(resourceManage.getLubanList("10328274", new HashMap<>()));
    }
    /* Ended by AICoder, pid:o5264ub0743dd5d14be70906908d463f164606f0 */

    /* Started by AICoder, pid:i73c5418ed406c0149970b47f0a96030d5e0a045 */
    @Test
    public void getLubanList3() throws Exception {
        List<ReturnResourceEntity> userNemuList = new ArrayList<>();
        ReturnResourceEntity returnResourceEntity = new ReturnResourceEntity();
        returnResourceEntity.setResourceId("326");
        returnResourceEntity.setResourceName("鲁班");
        returnResourceEntity.setParentCode("2");
        returnResourceEntity.setControlId("1");
        userNemuList.add(returnResourceEntity);

        setupRedisMocks(userNemuList);
        Assert.assertNotNull(resourceManage.getLubanList("10328274", new HashMap<>()));
    }

    @Test
    public void getLubanList4() throws Exception {
        List<ReturnResourceEntity> userNemuList = new ArrayList<>();
        ReturnResourceEntity returnResourceEntity = new ReturnResourceEntity();
        returnResourceEntity.setResourceId("326");
        returnResourceEntity.setResourceName("鲁班");
        returnResourceEntity.setParentCode("0");
        returnResourceEntity.setControlId("1");
        userNemuList.add(returnResourceEntity);

        ReturnResourceEntity returnResourceEntity2 = new ReturnResourceEntity();
        returnResourceEntity2.setResourceId("321");
        returnResourceEntity2.setResourceName("鲁班");
        returnResourceEntity2.setParentCode("326");
        returnResourceEntity2.setControlId("1");
        returnResourceEntity2.setResourceType(ResourceManage.ResourceType.OTHER.getType());
        userNemuList.add(returnResourceEntity2);

        setupRedisMocks(userNemuList);
        Assert.assertNotNull(resourceManage.getLubanList("10328274", new HashMap<>()));
    }
    /* Ended by AICoder, pid:i73c5418ed406c0149970b47f0a96030d5e0a045 */

    @Test
    public void getMenuList() throws Exception {
        List<ReturnResourceEntity> userNemuList = new ArrayList<>();
        PowerMockito.when(redisTemplate.opsForList()).thenReturn(listOperations);
        PowerMockito.when(listOperations.range(Mockito.anyString(), Mockito.anyLong(), Mockito.anyLong())).thenReturn(userNemuList);

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);

        PowerMockito.mockStatic(AuthorityClient.class);
        ServiceData sd = new ServiceData();
        RetCode retCode = new RetCode();
        retCode.setCode("0000");
        sd.setCode(retCode);
        ReturnResourceEntity returnResourceEntity = new ReturnResourceEntity();
        returnResourceEntity.setResourceId("test");
        HashMap test = new HashMap();
        test.put("test", returnResourceEntity);
        sd.setBo(test);
        PowerMockito.when(AuthorityClient.getUserPower(Mockito.any())).thenReturn(sd);

        PowerMockito.when(redisTemplate.getExpire(Mockito.anyString(), Mockito.anyObject())).thenReturn((long)NumConstant.NUM_SECOND_WEEK);
        PowerMockito.when(redisTemplate.delete(Mockito.anyString())).thenReturn(Boolean.TRUE);
        PowerMockito.when(redisTemplate.expire(Mockito.anyString(), Mockito.anyLong(), Mockito.anyObject())).thenReturn(Boolean.TRUE);
        resourceManage.init();
        Assert.assertNull(resourceManage.getMenuList("", "10328274", new HashMap(){{put(BusinessConstant.TOKEN,"test");put(BusinessConstant.ITP,"test");}}));
    }

    /* Started by AICoder, pid:2f0f9tb0f5v936614f8708505089103901d1f933 */
    @Test
    public void getMenuListIsNull() throws Exception {
        // Arrange
        PowerMockito.when(redisTemplate.opsForList()).thenReturn(listOperations);
        PowerMockito.when(listOperations.range(Mockito.anyString(), Mockito.anyLong(), Mockito.anyLong())).thenReturn(new ArrayList<>());

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);

        PowerMockito.mockStatic(AuthorityClient.class);
        ServiceData sd = new ServiceData();
        RetCode retCode = new RetCode();
        retCode.setCode("0000");
        sd.setCode(retCode);
        ReturnResourceEntity returnResourceEntity = new ReturnResourceEntity();
        returnResourceEntity.setResourceId("test");
        HashMap test = new HashMap();
        sd.setBo(test);
        PowerMockito.when(AuthorityClient.getUserPower(Mockito.any())).thenReturn(sd);

        PowerMockito.when(redisTemplate.getExpire(Mockito.anyString(), Mockito.anyObject())).thenReturn((long)NumConstant.NUM_SECOND_WEEK);
        PowerMockito.when(redisTemplate.delete(Mockito.anyString())).thenReturn(Boolean.TRUE);
        PowerMockito.when(redisTemplate.expire(Mockito.anyString(), Mockito.anyLong(), Mockito.anyObject())).thenReturn(Boolean.TRUE);

        // Act
        resourceManage.init();
        List<ReturnResourceEntity> result = resourceManage.getMenuList("", "10328274", new HashMap(){{put(BusinessConstant.TOKEN,"test");put(BusinessConstant.ITP,"test");}});

        // Assert
        Assert.assertNull(result);
    }
    /* Ended by AICoder, pid:2f0f9tb0f5v936614f8708505089103901d1f933 */

    @Test
    public void getBtnList() throws Exception {
        List<ReturnResourceEntity> userNemuList = new ArrayList<>();
        PowerMockito.when(redisTemplate.opsForList()).thenReturn(listOperations);
        PowerMockito.when(listOperations.range(Mockito.anyString(), Mockito.anyLong(), Mockito.anyLong())).thenReturn(userNemuList);

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);

        PowerMockito.mockStatic(AuthorityClient.class);
        ServiceData sd = new ServiceData();
        RetCode retCode = new RetCode();
        retCode.setCode("0000");
        sd.setCode(retCode);
        ReturnResourceEntity returnResourceEntity = new ReturnResourceEntity();
        returnResourceEntity.setResourceId("test");
        HashMap test = new HashMap();
        test.put("test", returnResourceEntity);
        sd.setBo(test);
        PowerMockito.when(AuthorityClient.getUserPower(Mockito.any())).thenReturn(sd);

        PowerMockito.when(redisTemplate.getExpire(Mockito.anyString(), Mockito.anyObject())).thenReturn((long)NumConstant.NUM_SECOND_WEEK);
        PowerMockito.when(redisTemplate.delete(Mockito.anyString())).thenReturn(Boolean.TRUE);
        PowerMockito.when(redisTemplate.expire(Mockito.anyString(), Mockito.anyLong(), Mockito.anyObject())).thenReturn(Boolean.TRUE);
        resourceManage.init();
        Assert.assertNotNull(resourceManage.getBtnList("", "10328274", new HashMap(){{put(BusinessConstant.TOKEN,"test");put(BusinessConstant.ITP,"test");}}));
    }

    @Test
    public void getPdaFactoryList() throws Exception {
        List<ReturnResourceEntity> userNemuList = new ArrayList<>();
        PowerMockito.when(redisTemplate.opsForList()).thenReturn(listOperations);
        PowerMockito.when(listOperations.range(Mockito.anyString(), Mockito.anyLong(), Mockito.anyLong())).thenReturn(userNemuList);

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);

        PowerMockito.mockStatic(AuthorityClient.class);
        ServiceData sd = new ServiceData();
        RetCode retCode = new RetCode();
        retCode.setCode("0000");
        sd.setCode(retCode);
        ReturnResourceEntity returnResourceEntity = new ReturnResourceEntity();
        returnResourceEntity.setResourceId("test");
        HashMap test = new HashMap();
        test.put("test", returnResourceEntity);
        sd.setBo(test);
        PowerMockito.when(AuthorityClient.getUserPower(Mockito.any())).thenReturn(sd);

        PowerMockito.when(redisTemplate.getExpire(Mockito.anyString(), Mockito.anyObject())).thenReturn(-2L);
        PowerMockito.when(redisTemplate.delete(Mockito.anyString())).thenReturn(Boolean.TRUE);
        PowerMockito.when(redisTemplate.expire(Mockito.anyString(), Mockito.anyLong(), Mockito.anyObject())).thenReturn(Boolean.TRUE);
        resourceManage.init();
        Assert.assertNotNull(resourceManage.getPdaFactoryList("10328274", new HashMap(){{put(BusinessConstant.TOKEN,"test");put(BusinessConstant.ITP,"test");}}));
    }

    /* Started by AICoder, pid:6bfdbcc4e2p20de1405e0ab8c0d8da2d64972dbc */
    @Test
    public void TestgetUserDataAuthByResourceId() throws Exception {
        String empNo = "10307329";
        String token = "74407153473ecc84c00869360505c934";
        Long moduleId = 100241L;
        Long resourceId = 12126L;
        ServiceData serviceData = new ServiceData<>();
        PowerMockito.when(uppRemoteService.getUserDataAuthByResourceId(Mockito.anyString(), Mockito.anyString(), Mockito.anyLong(), Mockito.anyLong())).thenReturn(null);
        Assert.assertNotNull(resourceManage.getUserDataAuthByResourceId(empNo, token, moduleId, resourceId));
        PowerMockito.when(uppRemoteService.getUserDataAuthByResourceId(Mockito.anyString(), Mockito.anyString(), Mockito.anyLong(), Mockito.anyLong())).thenReturn(serviceData);
        Assert.assertNotNull(resourceManage.getUserDataAuthByResourceId(empNo, token, moduleId, resourceId));
        LinkedHashMap<String, ConstraintResDTO> map = new LinkedHashMap<>();
        ConstraintResDTO constraintResDTO = new ConstraintResDTO();
        map.put("1", constraintResDTO);
        serviceData.setBo(map);
        Assert.assertNotNull(resourceManage.getUserDataAuthByResourceId(empNo, token, moduleId, resourceId));
        serviceData.setBo(constraintResDTO);
        Assert.assertNotNull(resourceManage.getUserDataAuthByResourceId(empNo, token, moduleId, resourceId));
    }
    /* Ended by AICoder, pid:6bfdbcc4e2p20de1405e0ab8c0d8da2d64972dbc */

}
