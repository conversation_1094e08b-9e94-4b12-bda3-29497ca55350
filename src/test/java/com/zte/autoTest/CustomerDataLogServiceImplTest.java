package com.zte.autoTest;

import com.zte.application.impl.CustomerDataLogServiceImpl;
import com.zte.domain.model.CustomerDataLogRepository;
import com.zte.interfaces.dto.CustomerDataLogDTO;
import com.zte.util.BaseTestCase;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

/**
 * <AUTHOR>
 * @date 2023/5/11
 */
public class CustomerDataLogServiceImplTest extends BaseTestCase {
    @InjectMocks
    private CustomerDataLogServiceImpl customerDataLogService;

    @Mock
    private CustomerDataLogRepository customerDataLogRepository;

    @Test
    public void getPushErrorData() throws Exception {
        Assert.assertNotNull(customerDataLogService.getPushErrorData(new CustomerDataLogDTO()));
    }
}
