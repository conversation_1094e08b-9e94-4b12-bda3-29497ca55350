package com.zte.util;

import com.zte.application.impl.SysInformaticaMonitorLogServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BmEmailReceiverRepository;
import com.zte.domain.model.SysInformaticaMonitorLog;
import com.zte.domain.model.SysInformaticaMonitorLogRepository;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description:
 * @date 2020/12/21 10:47
 */
@RunWith(PowerMockRunner.class)
public class SysInformaticaMonitorLogServiceImplTest {
    @InjectMocks
    private SysInformaticaMonitorLogServiceImpl sysInformaticaMonitorLogServiceImpl;

    @Mock
    private SysInformaticaMonitorLogRepository sysInformaticaMonitorLogRepository;

    @Mock
    private BmEmailReceiverRepository bmEmailReceiverRepository;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
    }

//    @Test
//    @PrepareForTest({ObtainRemoteServiceDataUtil.class,HttpClientUtil.class,CommonUtils.class,EmailUtils.class})
//    public void monitoringException() throws Exception {
//        SysInformaticaMonitorLog sysInformaticaMonitorLog = new SysInformaticaMonitorLog();
//        sysInformaticaMonitorLog.setRecordId("abcd99475gvbgd4f");
//        sysInformaticaMonitorLog.setTimerCode("ABCD123");
//        sysInformaticaMonitorLog.setTimerName("定时器1");
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        sysInformaticaMonitorLog.setSynchroExecuteTime(sdf.parse("2020-12-22 14:12:10"));
//        List<SysInformaticaMonitorLog> list = new ArrayList<>();
//        list.add(sysInformaticaMonitorLog);
//
//        PowerMockito.when(sysInformaticaMonitorLogRepository.getRecentRecord(Mockito.anyObject())).thenReturn(list);
//
//        PowerMockito.mockStatic(CommonUtils.class);
//        PowerMockito.when(CommonUtils.getLmbMessage(Mockito.anyString())).thenReturn("aaaaaaa");
//
//        BmEmailReceiver mailToEntity = new BmEmailReceiver();
//        mailToEntity.setEmailReceiverId("7b17c465-rt6f-40a3-a81e-23b4abc164e3");
//        mailToEntity.setBusType(new BigDecimal(5));
//        mailToEntity.setGroupType(new BigDecimal(0));
//        mailToEntity.setEmail("<EMAIL>");
//        mailToEntity.setEnabledFlag("Y");
//        mailToEntity.setFactoryId(new BigDecimal(51));
//        List<BmEmailReceiver> emailReceiverList = new ArrayList<>();
//        emailReceiverList.add(mailToEntity);
//        PowerMockito.when(bmEmailReceiverRepository.selectBmEmailReceiverByBusType(Mockito.anyString(),Mockito.anyString())).thenReturn(emailReceiverList);
//
//        //PowerMockito.mockStatic(EmailUtils.class);
//        PowerMockito.mockStatic(HttpClientUtil.class);
//        PowerMockito.when(HttpClientUtil.httpPostWithJSON(Mockito.anyString(),Mockito.anyString(),Mockito.anyMap())).thenReturn("邮件发送成功");
//
//        sysInformaticaMonitorLogServiceImpl.monitoringException();
//    }

    @Test
    public void deleteHistoryRecordBatch() throws Exception {
        SysInformaticaMonitorLog sysInformaticaMonitorLog = new SysInformaticaMonitorLog();
        sysInformaticaMonitorLog.setRecordId("abcd99475gvbgd4f");
        sysInformaticaMonitorLog.setTimerCode("ABCD123");
        sysInformaticaMonitorLog.setTimerName("定时器1");
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sysInformaticaMonitorLog.setSynchroExecuteTime(sdf.parse("2020-12-22 14:12:10"));

        sysInformaticaMonitorLogServiceImpl.deleteHistoryRecordBatch();
        List<SysInformaticaMonitorLog> list = new ArrayList<>();
        list.add(sysInformaticaMonitorLog);

        PowerMockito.when(sysInformaticaMonitorLogRepository.getRecentRecord(Mockito.anyObject())).thenReturn(list);

        sysInformaticaMonitorLogRepository.deleteHistoryRecordBatch(Mockito.anyList());

        sysInformaticaMonitorLogServiceImpl.deleteHistoryRecordBatch();
        PowerMockito.when(sysInformaticaMonitorLogRepository.getRecentRecord(Mockito.anyObject())).thenReturn(null);
        sysInformaticaMonitorLogServiceImpl.deleteHistoryRecordBatch();
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

}
