package com.zte.util;

import com.zte.application.ProgramOperationHistoryInfoService;
import com.zte.application.ProgramVerifyConfirmInfoService;
import com.zte.application.ProgramVerifyInfoHeaderService;
import com.zte.application.impl.ProgramSampleVerifyHeaderServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.domain.model.*;
import com.zte.interfaces.dto.ProgramSampleVerifyDetailDTO;
import com.zte.interfaces.dto.ProgramSampleVerifyHeaderDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static org.mockito.Matchers.*;

@RunWith(PowerMockRunner.class)
public class ProgramSampleVerifyHeaderServiceImplTest {

    @InjectMocks
    private ProgramSampleVerifyHeaderServiceImpl programSampleVerifyHeaderServiceImpl;

    @Mock
    private ProgramVerifyInfoHeaderRepository programVerifyInfoHeaderRepository;

    @Mock
    private ProgramVerifyInfoDetailRepository programVerifyInfoDetailRepository;

    @Mock
    private ProgramSampleVerifyHeaderRepository programSampleVerifyHeaderRepository;

    @Mock
    private ProgramVerifyConfirmInfoService programVerifyConfirmInfoService;

    @Mock
    private ProgramVerifyInfoHeaderService programVerifyInfoHeaderService;

    @Mock
    private ProgramSampleVerifyDetailRepository programSampleVerifyDetailRepository;

    @Mock
    private ProgramOperationHistoryInfoService programOperationHistoryInfoService;

    @Mock
    private EmailUtils emailUtils;


    @Test
    public void getList() throws Exception {
        ProgramSampleVerifyHeaderDTO dto=new ProgramSampleVerifyHeaderDTO();
        dto.setHeaderId("8711b02d-b1df-4865-a9b5-240feae94815");
        List<ProgramVerifyInfoHeader> list=new ArrayList<>();
        ProgramVerifyInfoHeader header=new ProgramVerifyInfoHeader();
        header.setApplyBillNo("SL202009280001");
        list.add(header);
        PowerMockito.when(programVerifyInfoHeaderRepository.getList(anyObject())).thenReturn(list);
        List<ProgramSampleVerifyDetail> detailList=new ArrayList<>();
        ProgramSampleVerifyDetail detail=new ProgramSampleVerifyDetail();
        detail.setStyle("style1");
        detailList.add(detail);
        ProgramSampleVerifyDetail detail2=new ProgramSampleVerifyDetail();
        detail2.setStyle("style2");
        detailList.add(detail2);
        PowerMockito.when(programSampleVerifyDetailRepository.getList(anyObject())).thenReturn(detailList);
        List<ProgramSampleVerifyHeader> headerList=new ArrayList<>();
        ProgramSampleVerifyHeader programSampleVerifyHeader=new ProgramSampleVerifyHeader();
        headerList.add(programSampleVerifyHeader);
        PowerMockito.when(programSampleVerifyHeaderRepository.getList(anyObject())).thenReturn(headerList);
        Assert.assertNotNull(programSampleVerifyHeaderServiceImpl.getList(dto));
    }

    @Test
    public void save() throws Exception {
        ProgramSampleVerifyHeaderDTO dto=new ProgramSampleVerifyHeaderDTO();
        dto.setHeaderId("8711b02d-b1df-4865-a9b5-240feae94815");
        dto.setEquipmentTechnician("1");
        dto.setProductTechnician("1");
        dto.setVerificationProdplanId("1");
        dto.setVerificationQty(BigDecimal.ONE);
        dto.setZsEngineer("1");
        dto.setRemark("1");
        dto.setCcList("1");
        dto.setIsVerifySmallBatch("Y");
        dto.setFactoryId(BigDecimal.ONE);
        List<ProgramSampleVerifyDetailDTO> psvdDtoList=new ArrayList<>();
        ProgramSampleVerifyDetailDTO programSampleVerifyDetailDTO=new ProgramSampleVerifyDetailDTO();
        programSampleVerifyDetailDTO.setAdapter("1");
        programSampleVerifyDetailDTO.setBurner("1");
        programSampleVerifyDetailDTO.setCheckSum("1");
        programSampleVerifyDetailDTO.setCraftParam("1");
        programSampleVerifyDetailDTO.setCraftPictureId("1");
        programSampleVerifyDetailDTO.setDeviceDriver("1");
        programSampleVerifyDetailDTO.setDeviceVer("1");
        programSampleVerifyDetailDTO.setRemark("1");
        programSampleVerifyDetailDTO.setStyle("style");
        List list=new ArrayList();
        list.add("1");
        programSampleVerifyDetailDTO.setCraftPictureIdList(list);
        psvdDtoList.add(programSampleVerifyDetailDTO);
        dto.setPsvdDtoList(psvdDtoList);
        dto.setCurrentPoint("1");
        dto.setResult(Constant.ABNORMAL_SHUTDOWN);
        ProgramVerifyInfoHeader programVerifyInfoHeader=new ProgramVerifyInfoHeader();
        programVerifyInfoHeader.setApplyBillNo("SL202009280001");
        PowerMockito.when(programVerifyInfoHeaderService.selectByPrimaryKey(anyString())).thenReturn(programVerifyInfoHeader);
        PowerMockito.when(programVerifyConfirmInfoService.getUserNew(anyList())).thenReturn(new HashMap());
        PowerMockito.when(emailUtils.sendMail(anyString(),anyString(),anyString(),anyString(),anyString())).thenReturn(false);
        List<ProgramVerifyInfoDetail> detailList=new ArrayList<>();
        ProgramVerifyInfoDetail detail=new ProgramVerifyInfoDetail();
        detail.setVerNo("verNo1");
        detail.setStyle("style1");
        detail.setProgramType("progaramType1");
        detailList.add(detail);
        ProgramVerifyInfoDetail detail2=new ProgramVerifyInfoDetail();
        detail2.setVerNo("verNo2");
        detail2.setStyle("style2");
        detail2.setProgramType("progaramType2");
        detailList.add(detail2);
        PowerMockito.when(programVerifyInfoDetailRepository.getList(anyObject())).thenReturn(detailList);
        PowerMockito.when(programOperationHistoryInfoService.batchInsert(anyList())).thenReturn(1L);
        Assert.assertNotNull(programSampleVerifyHeaderServiceImpl.save(dto));
    }
    @Test
    public void veifyParam() throws Exception {
        ProgramSampleVerifyHeaderDTO dto=new ProgramSampleVerifyHeaderDTO();
        dto.setHeaderId("8711b02d-b1df-4865-a9b5-240feae94815");
        dto.setEquipmentTechnician("1");
        dto.setProductTechnician("1");
        dto.setVerificationProdplanId("1");
        dto.setVerificationQty(BigDecimal.ONE);
        dto.setZsEngineer("1");
        dto.setRemark("1");
        dto.setCcList("1");
        dto.setIsVerifySmallBatch("Y");
        dto.setFactoryId(BigDecimal.ONE);
        List<ProgramSampleVerifyDetailDTO> psvdDtoList=new ArrayList<>();
        ProgramSampleVerifyDetailDTO programSampleVerifyDetailDTO=new ProgramSampleVerifyDetailDTO();
        programSampleVerifyDetailDTO.setAdapter("1");
        programSampleVerifyDetailDTO.setBurner("1");
        programSampleVerifyDetailDTO.setCheckSum("1");
        programSampleVerifyDetailDTO.setCraftParam("1");
        programSampleVerifyDetailDTO.setCraftPictureId("1");
        programSampleVerifyDetailDTO.setDeviceDriver("1");
        programSampleVerifyDetailDTO.setDeviceVer("1");
        programSampleVerifyDetailDTO.setRemark("1");
        programSampleVerifyDetailDTO.setStyle("style");
        List list=new ArrayList();
        list.add("1");
        programSampleVerifyDetailDTO.setCraftPictureIdList(list);
        psvdDtoList.add(programSampleVerifyDetailDTO);
        dto.setPsvdDtoList(psvdDtoList);
        dto.setCurrentPoint("1");
        dto.setResult(Constant.ABNORMAL_SHUTDOWN);
        ProgramVerifyInfoHeader programVerifyInfoHeader=new ProgramVerifyInfoHeader();
        programVerifyInfoHeader.setApplyBillNo("SL202009280001");
        PowerMockito.when(programVerifyInfoHeaderService.selectByPrimaryKey(anyString())).thenReturn(programVerifyInfoHeader);
        PowerMockito.when(programVerifyConfirmInfoService.getUserNew(anyList())).thenReturn(new HashMap());
        PowerMockito.when(emailUtils.sendMail(anyString(),anyString(),anyString(),anyString(),anyString())).thenReturn(false);
        List<ProgramVerifyInfoDetail> detailList=new ArrayList<>();
        ProgramVerifyInfoDetail detail=new ProgramVerifyInfoDetail();
        detail.setVerNo("verNo1");
        detail.setStyle("style1");
        detail.setProgramType("progaramType1");
        detailList.add(detail);
        ProgramVerifyInfoDetail detail2=new ProgramVerifyInfoDetail();
        detail2.setVerNo("verNo2");
        detail2.setStyle("style2");
        detail2.setProgramType("progaramType2");
        detailList.add(detail2);
        programSampleVerifyHeaderServiceImpl.veifyParam(dto);
        Assert.assertNotNull(dto);
    }


}
