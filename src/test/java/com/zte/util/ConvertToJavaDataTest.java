package com.zte.util;

import com.alibaba.excel.metadata.data.ReadCellData;
import com.zte.common.CommonUtils;
import com.zte.common.excel.DataConverter;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.test.context.ContextConfiguration;

import java.util.Date;

import static org.mockito.ArgumentMatchers.any;

/**
 * 格式转换器单元测试类
 *
 * <AUTHOR>
 * @date 2023/2/16 下午1:43
 */
@PrepareForTest({CommonUtils.class})
@ContextConfiguration
@RunWith(PowerMockRunner.class)
public class ConvertToJavaDataTest {
    @Mock
    private DataConverter dataConverter;

    @Test
    public void tt3() throws Exception {
        ReadCellData<Date> cellData = new ReadCellData();
        cellData.setData(new Date());
        PowerMockito.mockStatic(CommonUtils.class);
        PowerMockito.when(CommonUtils.getLmbMessage(any())).thenReturn("Msg");

        Assert.assertNull(dataConverter.convertToJavaData(cellData, null, null));
        Assert.assertNull(dataConverter.convertToExcelData(new Date(), null, null));

    }

}
