package com.zte.util;

import com.google.common.collect.Lists;
import com.zte.application.SysLookupValuesService;
import com.zte.application.impl.BsBarcodeInfoServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.common.model.MessageId;
import com.zte.domain.model.BsBarcodeInfo;
import com.zte.domain.model.BsBarcodeInfoRepository;
import com.zte.infrastructure.remote.BarcodeCenterRemoteService;
import com.zte.springbootframe.common.model.RetCode;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.mockito.Matchers.anyObject;

/**
 * 单元测试
 * Created by 00263453
 */

@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class, BsBarcodeInfoServiceImpl.class})
public  class BsBarcodeInfoServiceImplTest {

    @InjectMocks
    private BsBarcodeInfoServiceImpl bsBarcodeInfoServiceImpl;

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Mock
    private BsBarcodeInfoRepository bsBarcodeInfoRepository;

    @Mock
    private BarcodeCenterRemoteService barcodeCenterRemoteService;

    @Test
    public void setMsg() throws Exception {
        PowerMockito.mockStatic(CommonUtils.class);
        try {
            bsBarcodeInfoServiceImpl.setMsg(new Exception("222"),new RetCode());
        }catch (Exception e){
            Assert.assertEquals(null, e.getMessage());
        }

    }

    @Test
    public void barcodeSplit() throws Exception {
        BsBarcodeInfoServiceImpl bsBarcodeInfoServiceImpl= PowerMockito.spy(new BsBarcodeInfoServiceImpl());
        bsBarcodeInfoServiceImpl.setBsBarcodeInfoRepository(bsBarcodeInfoRepository);
        BsBarcodeInfo barcode = new BsBarcodeInfo();
        BsBarcodeInfo barcodeNew = new BsBarcodeInfo();
        barcode.setAttribute1("14000800201900290");
        barcode.setBarcode("dgdgrhrhreh");
        Map<String,Object> barcodeQuery =new HashMap<>();
        barcodeQuery.put("barcode","dgdgrhrhreh");
        barcodeQuery.put("attribute1","14000800201900290");
        List<BsBarcodeInfo> checklist = new ArrayList<>();
        BsBarcodeInfo checkContent = new BsBarcodeInfo();
        checkContent.setBarcode("14000800201900290");
        checklist.add(checkContent);
        PowerMockito.when(bsBarcodeInfoRepository.barcodeQuery(barcodeQuery)).thenReturn(checklist);
        barcodeNew.setAttribute1("14000800201900290");
        barcodeNew.setBarcode("dgdgrhrhreh");
        PowerMockito.when(bsBarcodeInfoRepository.insertBsBarcodeInfo(barcodeNew)).thenReturn(1);
        String empNo = "00263453";
        Assert.assertNotNull(bsBarcodeInfoServiceImpl.barcodeSplit(barcode,empNo));
    }

    @Test
    public void getBarcodeFromBarcodeCenter() throws Exception {
        PowerMockito.when(barcodeCenterRemoteService.getBarcodeFromBarcodeCenter(anyObject()))
                .thenReturn(Lists.newArrayList());
        Assert.assertNull(bsBarcodeInfoServiceImpl.getBarcodeFromBarcodeCenter("123"));

    }
}
