package com.zte.util;

import com.zte.application.impl.ZteMbcFactoryInfoServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.CFFactoryRepository;
import com.zte.domain.model.SysLookupValuesRepository;
import com.zte.domain.model.ZteMbcFactoryInfo;
import com.zte.domain.model.ZteMbcFactoryInfoRepository;
import com.zte.infrastructure.remote.BasicsettingRemoteService;
import com.zte.infrastructure.remote.SupplierRemoteService;
import com.zte.interfaces.dto.ZteMbcFactoryInfoDTO;
import com.zte.itp.msa.client.util.HttpClientUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @Description:
 * @date 2020/9/10 15:50
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({SupplierRemoteService.class,BasicsettingRemoteService.class,HttpClientUtil.class})
public class ZteMbcFactoryInfoServiceImplTest {
    @InjectMocks
    private ZteMbcFactoryInfoServiceImpl zteMbcFactoryInfoServiceImpl;

    @Mock
    private ZteMbcFactoryInfoRepository zteMbcFactoryInfoRepository;

    @Mock
    private CFFactoryRepository cFFactoryRepository;

    @Mock
    private SysLookupValuesRepository sysLookupValuesRepository;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(SupplierRemoteService.class,BasicsettingRemoteService.class,HttpClientUtil.class);
    }

    @Test
    public void addOutsourcingFactory(){
        ZteMbcFactoryInfo zteMbcFactoryInfo = new ZteMbcFactoryInfo();
        zteMbcFactoryInfo.setLastUpdatedBy("10238004");
        zteMbcFactoryInfo.setCreatedBy("10238004");
        zteMbcFactoryInfo.setFactoryName("上海洛可可整合设计有限公司");
        zteMbcFactoryInfo.setFactoryFlag(new BigDecimal(1));
        zteMbcFactoryInfo.setIsOutFactory("是");
        zteMbcFactoryInfo.setSupplierNo("85661850");

        List<ZteMbcFactoryInfo> zteMbcFactoryInfoList = new ArrayList<>();
        zteMbcFactoryInfoList.add(zteMbcFactoryInfo);

        PowerMockito.when(zteMbcFactoryInfoRepository.findZteMbcFactoryInfoList(Mockito.anyList())).thenReturn(zteMbcFactoryInfoList);

        Long maxFactoryId = 75L;
        PowerMockito.when(cFFactoryRepository.getMaxFactoryId()).thenReturn(maxFactoryId);

        //向zte_mbc_factory_info表批量插入数据
        zteMbcFactoryInfoRepository.insertZteMbcFactoryInfoBatch(Mockito.anyList());
        //向cf_factory表批量插入数据
        cFFactoryRepository.insertBatch(Mockito.anyList());

        ZteMbcFactoryInfoDTO zteMbcFactoryInfoDTO = new ZteMbcFactoryInfoDTO();
        zteMbcFactoryInfoDTO.setCreatedBy("10238004");
        zteMbcFactoryInfoDTO.setSupplierNo("85658950");
        zteMbcFactoryInfoDTO.setSupplierName("广州蓝色光标市场顾问有限公司");
        zteMbcFactoryInfoDTO.setAddress("广州市天河区珠江东路30号1801");

        List<ZteMbcFactoryInfoDTO> list =new ArrayList<>();
        list.add(zteMbcFactoryInfoDTO);

        String empNo = "6606000002";
        zteMbcFactoryInfoServiceImpl.addOutsourcingFactory(empNo,list);
        Assert.assertNotNull(list);
    }

    @Test
    public void deleteZteMbcFactoryInfo(){
        zteMbcFactoryInfoRepository.updateZteMbcFactoryInfo(Mockito.anyObject());
        cFFactoryRepository.update(Mockito.anyObject());

        String id = "100000";
        zteMbcFactoryInfoServiceImpl.deleteZteMbcFactoryInfo(id);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

}
