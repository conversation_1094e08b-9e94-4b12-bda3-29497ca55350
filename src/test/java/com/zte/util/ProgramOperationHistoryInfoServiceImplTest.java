package com.zte.util;

import com.zte.application.ProgramVerifyConfirmInfoService;
import com.zte.application.impl.ProgramOperationHistoryInfoServiceImpl;
import com.zte.domain.model.ProgramOperationHistoryInfoRepository;
import com.zte.interfaces.dto.ProgramOperationHistoryInfoEntityDTO;
import com.zte.springbootframe.common.model.Page;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import static org.mockito.Matchers.anyList;
import static org.mockito.Matchers.anyObject;

@RunWith(PowerMockRunner.class)
public class ProgramOperationHistoryInfoServiceImplTest {

    @InjectMocks
    private ProgramOperationHistoryInfoServiceImpl programOperationHistoryInfoServiceImpl;

    @Mock
    private ProgramOperationHistoryInfoRepository programOperationHistoryInforepository;

    @Mock
    private ProgramVerifyConfirmInfoService programVerifyConfirmInfoService;


    @Test
    public void getList() throws Exception {
        ProgramOperationHistoryInfoEntityDTO dto=new ProgramOperationHistoryInfoEntityDTO();
        List<ProgramOperationHistoryInfoEntityDTO> list=new ArrayList<>();
        ProgramOperationHistoryInfoEntityDTO returnDto=new ProgramOperationHistoryInfoEntityDTO();
        returnDto.setCreateDate(new Date());
        returnDto.setRemark("Remark");
        returnDto.setResult("result");
        returnDto.setCreateBy("createBy");
        returnDto.setCurrentPoint("currentPoint");
        list.add(returnDto);
        PowerMockito.when(programOperationHistoryInforepository.getList(anyObject())).thenReturn(list);
        PowerMockito.when(programVerifyConfirmInfoService.getUserNew(anyObject())).thenReturn(new HashMap<>());
        Assert.assertNotNull(programOperationHistoryInfoServiceImpl.getList(dto));
    }

    @Test
    public void pageList() throws Exception {
        ProgramOperationHistoryInfoEntityDTO dto=new ProgramOperationHistoryInfoEntityDTO();
        Page<ProgramOperationHistoryInfoEntityDTO> pageInfo = programOperationHistoryInfoServiceImpl.pageList(dto);
        Assert.assertTrue(pageInfo.getRows().size() >= 0);
    }

    @Test
    public void update() throws Exception {
        ProgramOperationHistoryInfoEntityDTO dto=new ProgramOperationHistoryInfoEntityDTO();
        Assert.assertNotNull(programOperationHistoryInfoServiceImpl.update(dto));
    }

    @Test
    public void save() throws Exception {
        ProgramOperationHistoryInfoEntityDTO dto=new ProgramOperationHistoryInfoEntityDTO();
        Assert.assertNotNull(programOperationHistoryInfoServiceImpl.save(dto));
    }

    @Test
    public void batchInsert() throws Exception {
        List<ProgramOperationHistoryInfoEntityDTO> list=new ArrayList<>();
        ProgramOperationHistoryInfoEntityDTO dto=new ProgramOperationHistoryInfoEntityDTO();
        list.add(dto);
        PowerMockito.when(programOperationHistoryInforepository.batchInsert(anyList())).thenReturn(1);
        Assert.assertNotNull(programOperationHistoryInfoServiceImpl.batchInsert(list));
    }
}
