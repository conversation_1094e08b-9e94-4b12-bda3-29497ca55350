package com.zte.util;

import com.zte.application.BsPremanuItemInfoService;
import com.zte.application.SysLookupTypesService;
import com.zte.application.impl.BsPremanuBomInfoServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BsPremanuBomInfo;
import com.zte.domain.model.BsPremanuBomInfoRepository;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.BsPremanuBomInfoDTO;
import com.zte.interfaces.dto.datawb.ProdSmtWriteDTO;
import com.zte.itp.msa.core.util.SpringContextUtil;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.locale.LocaleMessageSourceBean;
import com.zte.springbootframe.common.model.Page;
import com.zte.springbootframe.common.model.RetCode;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.*;

import static org.mockito.ArgumentMatchers.any;
import static org.powermock.api.mockito.PowerMockito.when;

/**
 * <AUTHOR>
 * @Date 2020/9/17 17
 * @description:
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({RetCode.class, SpringContextUtil.class})
public class BsPremanuBomInfoServiceImplTest {

    @InjectMocks
    private BsPremanuBomInfoServiceImpl premanuBomInfoServiceImpl;

    @Mock
    private BsPremanuBomInfoRepository bsPremanuBomInfoRepository;

    @Mock
    private LocaleMessageSourceBean localeMessageSourceBean;

    @Mock
    private SysLookupTypesService sysLookupTypesService;

    @Mock
    private BsPremanuItemInfoService bsPremanuItemInfoService;
    @Mock
    private DatawbRemoteService datawbRemoteService;

    @Before
    public void init() {
        MockitoAnnotations.initMocks(this);
        PowerMockito.mockStatic(SpringContextUtil.class);
    }

    @Test
    public void deletePreBomInfo() throws Exception {
        BsPremanuBomInfoDTO bs = new BsPremanuBomInfoDTO();
        bs.setBomCode("0");
        bs.setRecordId("0");
        when(bsPremanuBomInfoRepository.selectBsPremanuBomInfoSelective(null)).thenReturn(new ArrayList<>());
        Assert.assertNotNull(premanuBomInfoServiceImpl.deletePreBomInfo(bs));

    }

    @Test
    public void addPreManuBom() {
        //
        List<BsPremanuBomInfo> preList = new ArrayList<>();
        BsPremanuBomInfo bs = new BsPremanuBomInfo();
        bs.setSortSeq(new BigDecimal("1"));
        preList.add(bs);
        BsPremanuBomInfo bs2 = new BsPremanuBomInfo();
        bs2.setSortSeq(new BigDecimal("1"));
        bs2.setTagNum("a1");
        preList.add(bs2);
        BsPremanuBomInfoDTO bs3 = new BsPremanuBomInfoDTO();
        bs3.setBomCode("0");
        bs3.setRecordId("0");
        bs3.setDeliveryProcess("SMT");
        bs3.setTagNumList(new ArrayList<>());
        when(bsPremanuBomInfoRepository.selectBsPremanuBomInfoSelective(Mockito.any())).thenReturn(preList);
        when(SpringContextUtil.getBean("localeResolverMes")).thenReturn(localeMessageSourceBean);
        when(localeMessageSourceBean.getMessage(Mockito.any())).thenReturn("OK");
        premanuBomInfoServiceImpl.addPreManuBom(bs3);

        bs3.getTagNumList().add("2");
        Assert.assertNotNull(premanuBomInfoServiceImpl.addPreManuBom(bs3));
    }

    @Test
    public void addPreManuBomBatch() throws Exception {
        try {
            List<BsPremanuBomInfoDTO> dtoList = new ArrayList<>();
            BsPremanuBomInfoDTO dto = new BsPremanuBomInfoDTO();
            dto.setRecordId("R1");
            dto.setItemNo("itemNo");
            dto.setBomCode("1");
            dto.setTagNum("R41");
            dto.setTraceCode("1");
            dto.setTypeCode("2");
            dto.setDeliveryProcess("CS");
            dto.setTypeName("11");
            dto.setTraceName("11");
            List<String> tagNumList = new ArrayList<>();
            tagNumList.add("R41");
            dto.setTagNumList(tagNumList);
            dtoList.add(dto);
            BsPremanuBomInfoDTO dto1 = new BsPremanuBomInfoDTO();
            dto1.setRecordId("R2");
            dto1.setItemNo("itemNo");
            dto1.setBomCode("1");
            dto1.setTagNum("R41");
            dto1.setTraceCode("1");
            dto1.setTypeCode("2");
            dto1.setDeliveryProcess("CS");
            dto1.setTagNumList(tagNumList);
            dto1.setTypeName("11");
            dto1.setTraceName("11");
            dtoList.add(dto1);
            when(bsPremanuBomInfoRepository.selectBsPremanuBomInfoSelective(Mockito.any())).thenReturn(new ArrayList<>());
            when(SpringContextUtil.getBean("localeResolverMes")).thenReturn(localeMessageSourceBean);
            when(localeMessageSourceBean.getMessage(Mockito.any())).thenReturn("OK");
            premanuBomInfoServiceImpl.addPreManuBomBatch(dtoList);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.ADD_PRE_MANU_INFO_BATCH_FAILED, e.getMessage());
        }
        Whitebox.invokeMethod(premanuBomInfoServiceImpl, "pushPreBomToMes", "13");
    }

    @Test
    public void checkTypeAndTraceData() throws MesBusinessException {
        try {
            BsPremanuBomInfoDTO queryDto = new BsPremanuBomInfoDTO();
            queryDto.setItemNo("002020200012");
            queryDto.setBomCode("129571751003AOB");
            List<BsPremanuBomInfoDTO> list = new ArrayList<>();
            list.add(queryDto);
            premanuBomInfoServiceImpl.checkTypeAndTraceData(list);
        } catch (Exception e) {
            Assert.assertEquals(MessageId.TYPE_CODE_NAME_TRACE_CODE_NAME_CAN_NOT_BE_EMPTY_ALONE,e.getMessage());
        }

    }

    @Test
    public void getSortSeqByCode() {
        BsPremanuBomInfoDTO queryDto = new BsPremanuBomInfoDTO();
        queryDto.setItemNo("002020200012");
        queryDto.setBomCode("129571751003AOB");
        Assert.assertNotNull(premanuBomInfoServiceImpl.getSortSeqByCode(queryDto));

    }

    @Test
    public void exportExcle() throws Exception {
        Map<String, Object> map = new HashMap<>();
        map.put("lookupType", "1004041");
        when(sysLookupTypesService.getList(map, null, null)).thenReturn(null);
        Page<BsPremanuBomInfoDTO> pageInfo = new Page<>(1, 5000);
        pageInfo.setParams(null);
        when(bsPremanuBomInfoRepository.selectBsPremanuBomInfoSelective(pageInfo)).thenReturn(null);
        premanuBomInfoServiceImpl.exportExcle(null, null, null);
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

    @Test
    public void judgeIsConfirm() throws Exception {
        List<BsPremanuBomInfoDTO> list = new ArrayList<>();
        BsPremanuBomInfoDTO bs3 = new BsPremanuBomInfoDTO();
        bs3.setBomCode("0");
        bs3.setRecordId("0");
        bs3.setDeliveryProcess("SMT");
        bs3.setTagNumList(new ArrayList<>());
        list.add(bs3);
        Assert.assertNotNull(premanuBomInfoServiceImpl.judgeIsConfirm(list));
    }


    @Test
    public void pushPreBomToMes() throws Exception {
        List<BsPremanuBomInfo> details = new ArrayList<>();
        PowerMockito.when(bsPremanuBomInfoRepository.selectPreBomInfoAll(any())).thenReturn(details);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(datawbRemoteService).deletePreBomByBomList(Mockito.any(),Mockito.anyList());
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(datawbRemoteService).insertOrUpdateBomPre(Mockito.any());
        try {
            premanuBomInfoServiceImpl.pushPreBomToMes("test123");
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        Map<String, List<BsPremanuBomInfo>> collectMap = new HashMap<>();
        collectMap.put("test123",details);
        List<ProdSmtWriteDTO> sendMesList = new LinkedList<>();
        List<ProdSmtWriteDTO> list = new ArrayList<>();
        sendMesList.addAll(list);
        Map<String, List<BsPremanuBomInfo>> bomPreMap = new HashMap<>();
        String tagNum ="test123";
        bomPreMap.put("test123",details);
        BsPremanuBomInfo bsPremanuBomInfo = new BsPremanuBomInfo();
        bsPremanuBomInfo.setBomCode("test123");
        bsPremanuBomInfo.setItemNo("test123");
        bsPremanuBomInfo.setTagNum(tagNum);
        bsPremanuBomInfo.setTypeCode("XP");
        details.add(bsPremanuBomInfo);
        try {
            premanuBomInfoServiceImpl.pushPreBomToMes("test123");
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        bsPremanuBomInfo.setTypeCode("CX");
        try {
            premanuBomInfoServiceImpl.pushPreBomToMes("test123");
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        bsPremanuBomInfo.setTypeCode("HK");
        try {
            premanuBomInfoServiceImpl.pushPreBomToMes("test123");
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
        bsPremanuBomInfo.setBakeRemark("HK");
        try {
            premanuBomInfoServiceImpl.pushPreBomToMes("test123");
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }
    @Test
    public void pushPreBomToMes1() throws Exception {
        List<BsPremanuBomInfo> details = null;
        PowerMockito.when(bsPremanuBomInfoRepository.selectPreBomInfoAll(any())).thenReturn(details);
        Mockito.doAnswer(new Answer<Object>() {
            public Object answer(InvocationOnMock invocation) {
                Object[] args = invocation.getArguments();
                return "called with arguments: " + args;
            }
        }).when(datawbRemoteService).deletePreBomByBomList(Mockito.any(),Mockito.anyList());
        try {
            premanuBomInfoServiceImpl.pushPreBomToMes("test123");
        } catch (MesBusinessException e) {
            Assert.assertTrue(e.getMessage().length()>0);
        }
    }


}
