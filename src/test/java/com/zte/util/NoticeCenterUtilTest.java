package com.zte.util;

import com.zte.application.SysLookupValuesService;
import com.zte.application.impl.CtProgramFileServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.ArtifactoryUtils;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.NoticeCenterService;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.util.NoticeCenterUtils;
import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * 单元测试
 *
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CtProgramFileServiceImpl.class, RedisCacheUtils.class, ArtifactoryUtils.class, IOUtils.class})
public class NoticeCenterUtilTest {

    @InjectMocks
    private NoticeCenterUtils noticeCenterUtils;

    @Mock
    private SysLookupValuesService sysLookupValuesService;

    @Mock
    private NoticeCenterService noticeCenterService;

    @Before
    public void init() {
        // 待测的类标注为@InjectMocks
        // 依赖的其他类标注为 @Mock
        // 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
    }

    @Test
    public void sendEmail() {
        noticeCenterUtils.sendEmail("", "", null);

        try {
            noticeCenterUtils.sendEmail("", "", 1);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
        try {
            noticeCenterUtils.sendEmail(1, "");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }

        SysLookupValues sysLookupValues = new SysLookupValues();
        PowerMockito.when(sysLookupValuesService.findByLookupCode(Mockito.anyInt())).thenReturn(sysLookupValues);
        try {
            noticeCenterUtils.sendEmail("", "", 1);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }
        try {
            noticeCenterUtils.sendEmail(1, "");
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.SYS_LOOK_NOT_CONFIG, e.getExMsgId());
        }

        sysLookupValues.setLookupMeaning("1111");
        noticeCenterUtils.sendEmail("", "", 1);
        noticeCenterUtils.sendEmail(1, "");

        sysLookupValues.setAttribute2("11111");
        noticeCenterUtils.sendEmail("", "", 1);
    }

}
