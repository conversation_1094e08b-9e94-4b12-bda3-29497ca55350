package com.zte.util;


import com.fasterxml.jackson.databind.JsonNode;
import com.zte.application.EdiSoSLineService;
import com.zte.application.impl.EdiSoSLineServiceImpl;
import com.zte.springbootframe.util.MESHttpHelper;
import com.zte.common.enums.InterfaceEnum;
import com.zte.common.utils.BusinessConstant;
import com.zte.common.utils.Constant;
import com.zte.common.utils.ServiceDataUtil;
import com.zte.domain.model.EdiSoSLineRepository;
import com.zte.interfaces.dto.EdiSoSNewDTO;
import com.zte.itp.msa.client.util.MicroServiceRestUtil;
import com.zte.itp.msa.core.util.SpringContextUtil;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 单元测试
 * <AUTHOR>
 *
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({MESHttpHelper.class, ServiceDataUtil.class,
		SpringContextUtil.class, EdiSoSLineService.class,MicroServiceRestUtil.class})
public class EdiSoSLineServiceImplTest extends BaseTestCase {
	@InjectMocks
	private EdiSoSLineServiceImpl ediSoSLineServiceImpl;

	@Mock
	private EdiSoSLineRepository ediSoSLineRepository;

	@Before
    public void init() {
		PowerMockito.mockStatic(MicroServiceRestUtil.class);
		PowerMockito.mockStatic(EdiSoSLineService.class);
		PowerMockito.mockStatic(MESHttpHelper.class);
		PowerMockito.mockStatic(SpringContextUtil.class);
		PowerMockito.mockStatic(ServiceDataUtil.class);
    }

    @Test
	public void postInfoBatchNew()throws Exception{
		List<EdiSoSNewDTO> list=new ArrayList<>();
		EdiSoSNewDTO ediSoSNewDTO=new EdiSoSNewDTO();
		ediSoSNewDTO.setExternalorderkey2("1");
		ediSoSNewDTO.setSerialnumber("123");
		list.add(ediSoSNewDTO);

		List<EdiSoSNewDTO> errorList=new ArrayList<>();
		PowerMockito.when(ediSoSLineRepository.getInforInfoByBillNo(Mockito.any())).thenReturn(errorList);
		ediSoSLineServiceImpl.postInforBatch(list);
		EdiSoSNewDTO errorDto=new EdiSoSNewDTO();
		errorDto.setExternalorderkey2("2");
		errorList.add(errorDto);
		ediSoSLineServiceImpl.postInforBatch(list);
		errorList.add(ediSoSNewDTO);
		Assert.assertNotNull(ediSoSLineServiceImpl.postInforBatch(list));
	}


	/**
	 * 发料错误信息备份单元测试方法
	 * @throws Exception
	 */
	@Test
	public void postInfoBatch() throws Exception{
		Map<String, Object> paramsMap = new HashMap<>(Constant.PK_CODE_LENGTH);
		paramsMap.put(BusinessConstant.WARE_HOUSE_CODE, "'CS0458','CS0161'");
		paramsMap.put(BusinessConstant.PAGE,null);
		paramsMap.put(BusinessConstant.ROWS,null);
		Map<String, String> headerParamsMap = MESHttpHelper.getHttpRequestHeader();
		headerParamsMap.put(BusinessConstant.HTTP_HEAD_X_FACTORY_ID,"52");
		String responseStr = "{\"code\":{\"code\":\"0000\",\"msgId\":\"RetCode.Success\",\"msg\":\"Success\"},\"bo\":[{\"warehouseId\":\"7eded6a4-15bf-4424-aae2-e8288d45369b\"," +
				"\"warehouseCode\":\"CS0458\",\"warehouseName\":\"K1库仓库teset\",\"warehouseType\":\"半成品K1库\"," + "\"businessScope\":\"原材料\"" +
				",\"isLead\":\"环保\",\"address\":\"1234\"," + "\"isParticipateInPlan\":\"0\",\"inventoryGroup\":null,\"erpSubinventory\":\"JT41-1\"," +
				"\"erpGoodsallocation\":\"JT41-1-NBY,JT41-1-XF\",\"remark\":null,\"createBy\":\"00260524\"," +
				"\"createDate\":\"2020-06-05 09:19:20\",\"lastUpdatedBy\":\"00260524\",\"lastUpdatedDate\":\"2020-06-05 09:19:20\"" +
				",\"enabledFlag\":\"Y\",\"orgId\":null,\"factoryId\":52,\"entityId\":2,\"managementType\":\"实物子库\"" +
				",\"receiptMode\":null,\"isAccept\":\"N\",\"outboundErpWarehouse\":\"N\",\"inboundErpWarehouse\":\"N\"" +
				",\"isCheckInWarehouse\":\"N\",\"scheduledAllotTime\":null,\"productionPlanAllotPeriod\":null" +
				",\"feedingInterval\":null,\"preparationPeriod\":null,\"onErpGoodsallocation\":null,\"onErpSubinventory\":null" +
				",\"cenIssuingbin\":null,\"warehouseConfigurationServer\":null}],\"other\":{\"msg\":\"Success\"" +
				",\"opResult\":\"Success\",\"event_id\":\"com.zte.interfaces.LinesideWarehouseInfoController@getWarehouseInfoList\"" +
				",\"code\":\"0000\",\"costTime\":\"80ms\",\"msgId\":\"RetCode.Success\",\"clickTime\":\"Thu Jan 07 14:08:35 CST 2021\"" +
				",\"tag\":\"按查询条件获取仓库数据-GetWarehouseInfoList方式\",\"serviceName\":\"zte-mes-manufactureshare-productiondeliverysys\",\"userId\":\"\"}}";
		PowerMockito.when(MicroServiceRestUtil.invokeService(Mockito.matches(InterfaceEnum.wareHouseInfoList.getServiceName()),
				Mockito.anyObject(),  Mockito.anyObject(),Mockito.anyObject(),Mockito.anyObject(),Mockito.anyObject())).thenReturn(responseStr);
		JsonNode jsonNode = ediSoSLineServiceImpl.getWareHouseCodeList("'CS0458','CS0161'", "52");
		Assert.assertNotNull(jsonNode);
	}
	@Test
	public void getWareHouseCodeList() throws Exception{
		EdiSoSNewDTO newDTO = new EdiSoSNewDTO();
		newDTO.setWareHouseCode("CS0458");
		List<EdiSoSNewDTO> list = new ArrayList<>();
		list.add(newDTO);
		PowerMockito.when(ediSoSLineRepository.postInforBatch(list)).thenReturn(1);
		int i = ediSoSLineServiceImpl.postInforBatch(list);
		Assert.assertTrue(i > 0);
	}

}
