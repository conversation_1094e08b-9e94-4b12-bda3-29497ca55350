package com.zte.util;

import com.zte.application.SysLookupValuesService;
import com.zte.application.impl.BBomDetailServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.domain.model.BBomDetail;
import com.zte.domain.model.BBomDetailRepository;
import com.zte.domain.model.BaBomHead;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.model.Page;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.when;

/**
 * 单元测试
 * <AUTHOR>
 *
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({DatawbRemoteService.class})
public class BBomDetailServiceImplTest {
	@InjectMocks
	private BBomDetailServiceImpl bBomDetailServiceImpl;

	@Mock
	private BBomDetailRepository bBomDetailRepository;

	@Mock
	private SysLookupValuesService sysLookupValuesService;


	@Test
	public void getBomDetailByItemCodeBatch()throws Exception{
		List<BBomDetailDTO> dtoList=new ArrayList<>();
		BBomDetailDTO dto=new BBomDetailDTO();
		dto.setItemCode("23");
		dtoList.add(dto);
		Assert.assertNotNull(bBomDetailServiceImpl.getBomDetailByItemCodeBatch(dtoList));
	}

	@Test
    public void selectBBomDetailByHeaderId() throws Exception {
		BBomDetailDTO dto=new BBomDetailDTO();
		PowerMockito.when(bBomDetailRepository.selectBBomDetailByHeaderId(anyObject())).thenReturn(new ArrayList<>());
		Page<BBomDetailDTO> pageInfo = bBomDetailServiceImpl.selectBBomDetailByHeaderId(dto);
		Assert.assertTrue(pageInfo.getRows().size() >= 0);
	}

	@Test
	public void updatePositionExtById() throws Exception {
		BBomDetail dto=new BBomDetail();
		Assert.assertNotNull(bBomDetailServiceImpl.updatePositionExtById(dto));
	}
	@Test
	public void hasEditPermission() throws Exception {
		String userId="10010112";
		List<SysLookupValues> list=new ArrayList<>();
		SysLookupValues sys=new SysLookupValues();
		sys.setLookupCode(new BigDecimal(Constant.LOOKUP_TYPE_1121001));
		PowerMockito.when(sysLookupValuesService.findByLookupType(anyString())).thenReturn(list);
		Assert.assertFalse(bBomDetailServiceImpl.hasEditPermission(userId));
	}

	/*Started by AICoder, pid:n7580d6fce33a87147a00b2510cc0c80a945d180*/
	@Test(timeout = 8000)
	public void testGetCanBindListWithEmptyProductCode() {
		BBomDetailDTO dto = new BBomDetailDTO();
		dto.setProductCode("");
		assertEquals(null, bBomDetailServiceImpl.getCanBindList(dto));
	}

	@Test(timeout = 8000)
	public void testGetCanBindListWithNullProductCode() {
		BBomDetailDTO dto = new BBomDetailDTO();
		dto.setProductCode(null);
		assertEquals(null, bBomDetailServiceImpl.getCanBindList(dto));
	}

	@Test(timeout = 8000)
	public void testGetCanBindListWithValidProductCode() {
		BBomDetailDTO dto = new BBomDetailDTO();
		dto.setProductCode("validCode");
		dto.setPage(1);
		dto.setRows(10);

		List<BBomDetailDTO> mockList = new ArrayList<>();
		BBomDetailDTO mockDto = new BBomDetailDTO();
		mockList.add(mockDto);

		when(bBomDetailRepository.getCanBindList(any())).thenReturn(mockList);

		BBomDetailServiceImpl spyService = spy(bBomDetailServiceImpl);
		BBomDetailServiceImpl finalSpyService = spyService;

		assertEquals(mockList.size(), finalSpyService.getCanBindList(dto).getRows().size());
	}
	/*Ended by AICoder, pid:n7580d6fce33a87147a00b2510cc0c80a945d180*/
	
	@Test
	public void getCanBindList() throws Exception {
		List<String> excludeItemCodeList = new ArrayList<>();
		excludeItemCodeList.add("123456");
		
		BBomDetailDTO dto = new BBomDetailDTO();
		dto.setExcludeItemCodeList(excludeItemCodeList);
		List<BBomDetailDTO> bomDetailList = new ArrayList<>();
		BBomDetailDTO d1 = new BBomDetailDTO();
		d1.setItemType("1");
		d1.setProductCode("123");
		PowerMockito.when(bBomDetailRepository.getCanBindList(Mockito.any())).thenReturn(bomDetailList);
		Assert.assertNull(bBomDetailServiceImpl.getCanBindList(dto));
	}

	@Test
	public void getBBomDetailByHeaderIds() throws MesBusinessException {
		BBomDetailDTO dto=new BBomDetailDTO();
		List<String> headerIds =new ArrayList<>();
		headerIds.add("1");
		dto.setHeaderIds(headerIds);
		dto.setItemCode("22");
		PowerMockito.when(bBomDetailRepository.getBBomDetailByHeaderIds(anyObject())).thenReturn(new ArrayList<>());
		Assert.assertNotNull(bBomDetailServiceImpl.getBBomDetailByHeaderIds(dto));
	}

	@Test
	public void getBBomDetailByHeaderIdsCount() {
		BBomDetailDTO dto=new BBomDetailDTO();
		List<String> headerIds =new ArrayList<>();
		headerIds.add("1");
		dto.setHeaderIds(headerIds);
		dto.setItemCode("22");
		PowerMockito.when(bBomDetailRepository.getBBomDetailByHeaderIdsCount(anyObject())).thenReturn(1L);
		Assert.assertNotNull(bBomDetailServiceImpl.getBBomDetailByHeaderIdsCount(dto));
	}

	@Test
	public void selectBomByItemCode() {
		BBomDetail record = new BBomDetail();
		List<BBomDetailDTO> bomList = new ArrayList<>();
		BBomDetailDTO dto = new BBomDetailDTO();
		dto.setProductCode("123");
		BBomDetailDTO dto1 = new BBomDetailDTO();
		dto1.setProductCode("1235");
		bomList.add(dto);
		bomList.add(dto1);
		PowerMockito.when(bBomDetailRepository.selectBomByItemCode(Mockito.any())).thenReturn(bomList);
		Assert.assertNotNull(bBomDetailServiceImpl.selectBomByItemCode(record));
	}

	@Test
	public void getPcbItemCodeTest() {
		List<String> itemNoList = new ArrayList<>();
		itemNoList.add("itemNO1");
		List<BBomDetailDTO> bomList = new ArrayList<>();
		bomList.add(new BBomDetailDTO());
		PowerMockito.when(bBomDetailRepository.getPcbItemCode(Mockito.anyList())).thenReturn(bomList);
		List<BBomDetailDTO> pcbItemCode = bBomDetailServiceImpl.getPcbItemCode(itemNoList);
		Assert.assertTrue(pcbItemCode.size() == 1);
	}

	@Test
	public void testselectDetailByProductCodeListAndItemCode() throws Exception {
		PowerMockito.mockStatic(DatawbRemoteService.class);
		BBomDetailDTO bBomDetailDTO = new BBomDetailDTO();
		bBomDetailDTO.setPage(1);
		bBomDetailDTO.setRows(10);
		List<BBomDetail> bomDetailList = new ArrayList<>();
		PowerMockito.when(bBomDetailRepository.selectDetailByProductCodesAndItemCode(Mockito.any())).thenReturn(bomDetailList);
		bBomDetailServiceImpl.selectDetailByProductCodeListAndItemCode(bBomDetailDTO);
		Assert.assertTrue(bBomDetailDTO.getPage() == 1);
		BBomDetail bBomDetail = new BBomDetail();
		bomDetailList.add(bBomDetail);
		PowerMockito.when(DatawbRemoteService.getBomInfoByBomNoList(anyList())).thenReturn(new ArrayList<>());
		bBomDetailServiceImpl.selectDetailByProductCodeListAndItemCode(bBomDetailDTO);
		Assert.assertTrue(bBomDetailDTO.getPage() == 1);
		bBomDetail.setProductCode("code");
		bBomDetail.setCodeDesc("codeDesc");
		BBomDetail bBomDetail1 = new BBomDetail();
		bBomDetail1.setProductCode("code1");
		bBomDetail1.setCodeDesc("codeDesc");
		bomDetailList.add(bBomDetail1);
		bBomDetailServiceImpl.selectDetailByProductCodeListAndItemCode(bBomDetailDTO);

		bBomDetail.setCodeDesc("");
		bBomDetail1.setCodeDesc("");
		bBomDetailServiceImpl.selectDetailByProductCodeListAndItemCode(bBomDetailDTO);

		Assert.assertTrue(bBomDetailDTO.getPage() == 1);
		List<BaBomHead> baBomHeadList = new ArrayList<>();
		PowerMockito.when(DatawbRemoteService.getBomInfoByBomNoListAndCodeType(any())).thenReturn(baBomHeadList);
		BaBomHead baBomHead = new BaBomHead();
		baBomHead.setBomNo("code");
		baBomHead.setCodeDesc("codeDesc");
		baBomHeadList.add(baBomHead);
		BaBomHead baBomHead1 = new BaBomHead();
		baBomHead1.setBomNo("code");
		baBomHead1.setCodeDesc("codeDesc");
		baBomHeadList.add(baBomHead1);
		bBomDetailServiceImpl.selectDetailByProductCodeListAndItemCode(bBomDetailDTO);
		Assert.assertTrue(bBomDetailDTO.getPage() == 1);
	}
}
