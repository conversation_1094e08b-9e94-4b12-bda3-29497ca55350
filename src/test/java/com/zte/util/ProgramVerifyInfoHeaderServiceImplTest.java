package com.zte.util;

import com.zte.application.ProgramOperationHistoryInfoService;
import com.zte.application.ProgramVerifyConfirmInfoService;
import com.zte.application.impl.ProgramVerifyInfoHeaderServiceImpl;
import com.zte.springbootframe.util.EmailUtils;
import com.zte.domain.model.ProgramVerifyInfoDetail;
import com.zte.domain.model.ProgramVerifyInfoDetailRepository;
import com.zte.domain.model.ProgramVerifyInfoHeader;
import com.zte.domain.model.ProgramVerifyInfoHeaderRepository;
import com.zte.interfaces.dto.ProgramVerifyInfoHeaderDTO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import static org.mockito.Matchers.*;

@RunWith(PowerMockRunner.class)
public class ProgramVerifyInfoHeaderServiceImplTest {

    @InjectMocks
    private ProgramVerifyInfoHeaderServiceImpl programVerifyInfoHeaderServiceImpl;

    @Mock
    private ProgramVerifyInfoHeaderRepository programVerifyInfoHeaderRepository;

    @Mock
    private ProgramVerifyInfoDetailRepository programVerifyInfoDetailRepository;

    @Mock
    private ProgramOperationHistoryInfoService programOperationHistoryInfoService;

    @Mock
    private ProgramVerifyConfirmInfoService programVerifyConfirmInfoService;

    @Mock
    private EmailUtils emailUtils;


    @Test
    public void getList() throws Exception {
        ProgramVerifyInfoHeaderDTO dto=new ProgramVerifyInfoHeaderDTO();
        dto.setApplyBillNo("SL202009280001");
        dto.setStatus("拟制中");
        List<ProgramVerifyInfoHeader> list=new ArrayList<>();
        ProgramVerifyInfoHeader header=new ProgramVerifyInfoHeader();
        header.setApplyBillNo("SL202009280001");
        header.setCurrentHandler("10270446");
        list.add(header);
        PowerMockito.when(programVerifyInfoHeaderRepository.getList(anyObject())).thenReturn(list);
        List<ProgramVerifyInfoDetail> detailList=new ArrayList<>();
        ProgramVerifyInfoDetail detail=new ProgramVerifyInfoDetail();
        detail.setVerNo("verNo1");
        detail.setStyle("style1");
        detail.setProgramType("progaramType1");
        detailList.add(detail);
        ProgramVerifyInfoDetail detail2=new ProgramVerifyInfoDetail();
        detail2.setVerNo("verNo2");
        detail2.setStyle("style2");
        detail2.setProgramType("progaramType2");
        detailList.add(detail2);
        PowerMockito.when(programVerifyInfoDetailRepository.getList(anyObject())).thenReturn(detailList);
        Assert.assertNotNull(programVerifyInfoHeaderServiceImpl.getList(dto));
    }

    @Test
    public void getCount() throws Exception {
        ProgramVerifyInfoHeaderDTO dto=new ProgramVerifyInfoHeaderDTO();
        dto.setApplyBillNo("SL202009280001");
        dto.setStatus("拟制中");
        List<ProgramVerifyInfoHeader> list=new ArrayList<>();
        ProgramVerifyInfoHeader header=new ProgramVerifyInfoHeader();
        header.setApplyBillNo("SL202009280001");
        list.add(header);
        PowerMockito.when(programVerifyInfoHeaderRepository.getList(anyObject())).thenReturn(list);
        List<ProgramVerifyInfoDetail> detailList=new ArrayList<>();
        ProgramVerifyInfoDetail detail=new ProgramVerifyInfoDetail();
        detail.setVerNo("verNo1");
        detail.setStyle("style1");
        detail.setProgramType("progaramType1");
        detailList.add(detail);
        ProgramVerifyInfoDetail detail2=new ProgramVerifyInfoDetail();
        detail2.setVerNo("verNo2");
        detail2.setStyle("style2");
        detail2.setProgramType("progaramType2");
        detailList.add(detail2);
        PowerMockito.when(programVerifyInfoDetailRepository.getList(anyObject())).thenReturn(detailList);
        Assert.assertNotNull(programVerifyInfoHeaderServiceImpl.getCount(dto));
    }

    @Test
    public void saveOrSubmit() throws Exception {
        ProgramVerifyInfoHeaderDTO dto=new ProgramVerifyInfoHeaderDTO();
        dto.setCreateBy("1");
        dto.setApplyBillNo("Test202009280001");
        dto.setStatus("拟制中");
        dto.setCurrentHandler("1");
        dto.setCurrentPoint("Test202009280001");
        dto.setFactoryId(BigDecimal.ONE);
        dto.setApplyType("Test202009280001");
        dto.setBomNo("Test202009280001");
        dto.setCcList("Test202009280001");
        dto.setPhone("Test202009280001");
        dto.setProgramCraftTechnician("Test202009280001");
        dto.setProgramEquipmentTechnician("Test202009280001");
        dto.setProgramParam("Test202009280001");
        dto.setProjectManagers("Test202009280001");
        dto.setRemark("Test202009280001");
        dto.setResult("Test202009280001");
        dto.setVerificationProdplanId("Test202009280001");
        dto.setVerificationQty(BigDecimal.ONE);
        dto.setVerificationSite("Test202009280001");
        dto.setZsEngineer("Test202009280001");
         List<ProgramVerifyInfoDetail> pvidList=new ArrayList<>();
        ProgramVerifyInfoDetail programVerifyInfoDetail=new ProgramVerifyInfoDetail();
        programVerifyInfoDetail.setProgramType("Test202009280001");
        programVerifyInfoDetail.setStyle("Test202009280001");
        programVerifyInfoDetail.setVerNo("Test202009280001");
        programVerifyInfoDetail.setApplyBillNo("Test202009280001");
        programVerifyInfoDetail.setBomName("Test202009280001");
        programVerifyInfoDetail.setBomNo("Test202009280001");
        programVerifyInfoDetail.setItemNo("Test202009280001");
        programVerifyInfoDetail.setPosition("Test202009280001");
        programVerifyInfoDetail.setSoftVer("Test202009280001");
        programVerifyInfoDetail.setRemark("Test202009280001");
        pvidList.add(programVerifyInfoDetail);


        PowerMockito.when(programOperationHistoryInfoService.batchInsert(anyList())).thenReturn(1L);
        PowerMockito.when(programVerifyConfirmInfoService.getUserNew(anyList())).thenReturn(new HashMap());
        PowerMockito.when(emailUtils.sendMail(anyString(),anyString(),anyString(),anyString(),anyString())).thenReturn(false);
        Assert.assertNotNull(programVerifyInfoHeaderServiceImpl.saveOrSubmit(dto));
    }
}
