package com.zte.util;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.zte.application.impl.BBomDetailServiceImpl;
import com.zte.application.impl.BBomHeaderServiceImpl;
import com.zte.application.impl.BPcbLocationBomServiceImpl;
import com.zte.common.CommonUtils;
import com.zte.domain.model.BBomHeader;
import com.zte.domain.model.BBomHeaderRepository;
import com.zte.domain.model.BProdBomChangeDetailRepository;
import com.zte.interfaces.dto.BBomDetailDTO;
import com.zte.interfaces.dto.BProdBomChangeDetailDTO;
import com.zte.interfaces.dto.BaCraftRoutingInfoDTO;
import com.zte.interfaces.dto.BaCraftRoutingValidateDTO;
import com.zte.itp.msa.core.model.RetCode;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

import static org.mockito.Matchers.anyObject;
import static org.mockito.Matchers.anyString;

/**
 * 单元测试
 * <AUTHOR>
 *
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CommonUtils.class})
public class BBomHeaderServiceImplTest {
	@InjectMocks
	private BBomHeaderServiceImpl bBomHeaderServiceImpl;

	@Mock
	private BBomHeaderRepository bBomHeaderRepository;

	@Mock
	private BBomDetailServiceImpl bBomDetailServiceImpl;
	@Mock
	private BProdBomChangeDetailRepository bProdBomChangeDetailRepository;
	@Before
    public void init() {
		// 待测的类标注为@InjectMocks
		// 依赖的其他类标注为 @Mock
		// 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
		MockitoAnnotations.initMocks(this);
    }
	@Test
	public void batchUpdateParseStatus() throws Exception {
		List<BBomHeader> bBomHeaderList = new ArrayList<>();
		BBomHeader bBomHeader = new BBomHeader();
		bBomHeader.setBomHeaderId("123456789");
		bBomHeaderList.add(bBomHeader);
		Assert.assertNotNull(bBomHeaderServiceImpl.batchUpdateParseStatus(bBomHeaderList));
	}
	@Test
	public void selectBBomHeaderByProductCodeList() throws Exception {
		bBomHeaderServiceImpl.selectBBomHeaderByProductCodeList(new ArrayList<>());
		List<String> productCodeList = new ArrayList<>();
		productCodeList.add("145858621256ABB");
		Assert.assertNotNull(bBomHeaderServiceImpl.selectBBomHeaderByProductCodeList(productCodeList));
	}
	@Test
    public void getItemNoByPositionAndBomNo() throws Exception {

		List<BBomHeader> bBomHeaderList = new ArrayList<>();
		BBomHeader bBomHeader = new BBomHeader();
		bBomHeader.setBomHeaderId("123456789");
		bBomHeaderList.add(bBomHeader);
		PowerMockito.when(bBomHeaderRepository.getList(Mockito.any())).thenReturn(bBomHeaderList);

		List<BBomDetailDTO> bBomDetailList = new ArrayList<>();
		BBomDetailDTO bBomDetailDTO = new BBomDetailDTO();
		bBomDetailDTO.setItemCode("045021000099");
		bBomDetailDTO.setPositionExt("R1A13A35,R1A46A35,R1A47A35,R7A31A35,R7A32A35,R7A37A35,R7A38A35,R7A39A35,R10A13A35,R10A47A35,R11A13A35,R11A47A35,R12A13A35,R12A47A35,R14A13A35,R14A47A35,R15A13A35,R15A47A35,R1A7A38,R1A8A38,R1A30A35,R1A43,R1A44,R2A7A38,R2A8A38,R2A30A35,R3A7A38,R3A8A38,R4A7A38,R4A8A38,R4A27,R4A28,R4A29,R4A30,R4A31,R4A32,R4A33,R4A34,R5A7A38,R5A8A38,R5A19,R5A43,R5A44,R7A29A35,R9A29A35,R12A43,R12A44,R13A28A35,R15A28A35,R15A39,R16A39,R17A39,R18A39,R23A39,R24A39,R50A9A35,R50A12A35,R73A1,R74A1,R75A1,R79A19A35,R94A49A35,R2A9P9,R4A9P9,R32,R49,R51A12A35,R2,R3,R1A35,R2A35,R22A35,R23A35,R28A35,R29A35,R38A35,R39A35,R118A35,R119A35,R120A35,R121A35,R124A35,R125A35,R143A35,R144A35,R145A35,R146A35,R157A35,R158A35,R163A35,R164A35,R186A35,R187A35,R1A1A19,R4A1A19,R687,R50,R2A13A35,R2A47A35,R87A35");
		bBomDetailList.add(bBomDetailDTO);
		BBomDetailDTO bBomDetailDTO2 = new BBomDetailDTO();
		bBomDetailDTO2.setItemCode("045021000099");
		bBomDetailDTO2.setPositionExt("R1A13A35,R1A46A35,R1A47A35,R7A31A35,R7A32A35,R7A37A35,R7A38A35,R7A39A35," +
				"R10A13A35,R10A47A35,R11A13A35,R11A47A35,R12A13A35,R12A47A35,R14A13A35,R14A47A35,R15A13A35,R15A47A35,R1A7A38,R1A8A38,R1A30A35,R1A43,R1A44,R2A7A38,R2A8A38,R2A30A35,R3A7A38,R3A8A38,R4A7A38,R4A8A38,R4A27,R4A28,R4A29,R4A30,R4A31,R4A32,R4A33,R4A34,R5A7A38,R5A8A38,R5A19,R5A43,R5A44,R7A29A35,R9A29A35,R12A43,R12A44,R13A28A35,R15A28A35,R15A39,R16A39,R17A39,R18A39,R23A39,R24A39,R50A9A35,R50A12A35,R73A1,R74A1,R75A1,R79A19A35,R94A49A35,R2A9P9,R4A9P9,R32,R49,R51A12A35,R2,R3,R1A35,R2A35,R22A35,R23A35,R28A35,R29A35,R38A35,R39A35,R118A35,R119A35,R120A35,R121A35,R124A35,R125A35,R143A35,R144A35,R145A35,R146A35,R157A35,R158A35,R163A35,R164A35,R186A35,R187A35,R1A1A19,R4A1A19,R687,R50,R2A13A35,R2A47A35,R87A35");
		bBomDetailList.add(bBomDetailDTO2);
		PowerMockito.when(bBomDetailServiceImpl.selectDetailByHeaderId(Mockito.any())).thenReturn(bBomDetailList);
		List<BProdBomChangeDetailDTO> bProdBomChangeDetailDTOS = new ArrayList<>();
		PowerMockito.when(bProdBomChangeDetailRepository.queryBProdBomDetailChangeByProdplan(Mockito.any())).thenReturn(bProdBomChangeDetailDTOS);

		BPcbLocationBomServiceImpl bPcbLocationBomService = new BPcbLocationBomServiceImpl();
		bBomHeaderServiceImpl.setbPcbLocationBomService(bPcbLocationBomService);

		String position = "R7A37A35、R7A38A35";
		String bomNo = "129229251001AQB";
		String prodplanId = "1234567";

		Assert.assertNotNull(bBomHeaderServiceImpl.getItemNoByPositionAndBomNo(position, bomNo, prodplanId));

		BProdBomChangeDetailDTO bProdBomChangeDetailDTO = new BProdBomChangeDetailDTO();
		bProdBomChangeDetailDTO.setProdplanId("1234567");
		bProdBomChangeDetailDTO.setItemCode("045021000099");
		bProdBomChangeDetailDTOS.add(bProdBomChangeDetailDTO);
		BProdBomChangeDetailDTO bProdBomChangeDetailDTO2 = new BProdBomChangeDetailDTO();
		bProdBomChangeDetailDTO2.setProdplanId("1234567");
		bProdBomChangeDetailDTO2.setItemCode("045021000099");
		bProdBomChangeDetailDTOS.add(bProdBomChangeDetailDTO2);
		Assert.assertNotNull(bBomHeaderServiceImpl.getItemNoByPositionAndBomNo(position, bomNo, prodplanId));
	}
	@Test
	public void getHasCadProductCode() {
		Assert.assertNotNull(bBomHeaderServiceImpl.getHasCadProductCode(Lists.newArrayList()));
	}

	@Test
	public void getProductCodeByItemCode() {
		PowerMockito.when(bBomHeaderRepository.getProductCodeByItemCode(anyObject())).thenReturn(new ArrayList<>());
		Assert.assertNotNull(bBomHeaderServiceImpl.getProductCodeByItemCode("11"));

	}
	@Test
	public void getAllChildBoardByProductCode() {
		PowerMockito.when(bBomHeaderRepository.getAllChildBoardByProductCode(anyObject())).thenReturn(new ArrayList<>());
		Assert.assertNotNull(bBomHeaderServiceImpl.getAllChildBoardByProductCode("11"));

	}

	@Test
	public void queryImportCadProduct() {
		List<String> productCodeList = new LinkedList<>();
		productCodeList.add("123");
		Assert.assertNotNull(bBomHeaderServiceImpl.queryImportCadProduct(productCodeList));
	}

	@Test
	public void getBomInfoWithRouteByBomNo() {
		PowerMockito.mockStatic(CommonUtils.class);
		PowerMockito.when(CommonUtils.getLmbMessage(anyString(), anyString())).thenReturn(
				JSON.toJSONString("msg"));
		BaCraftRoutingValidateDTO baCraftRoutingValidateDTO = bBomHeaderServiceImpl.getBomInfoWithRouteByBomNo("1");
		Assert.assertEquals(RetCode.BUSINESSERROR_CODE, baCraftRoutingValidateDTO.getRetCode().getCode());

		PowerMockito.when(bBomHeaderRepository.getBomInfoWithRouteByBomNo(Mockito.anyString())).thenReturn(null);
		baCraftRoutingValidateDTO = bBomHeaderServiceImpl.getBomInfoWithRouteByBomNo("125000860001ABC");
		Assert.assertEquals(RetCode.BUSINESSERROR_CODE, baCraftRoutingValidateDTO.getRetCode().getCode());

		BaCraftRoutingInfoDTO baCraftRoutingInfoDTO = new BaCraftRoutingInfoDTO();
		baCraftRoutingInfoDTO.setBomNo("125000860001ABC");
		PowerMockito.when(bBomHeaderRepository.getBomInfoWithRouteByBomNo(Mockito.anyString())).thenReturn(baCraftRoutingInfoDTO);
		baCraftRoutingValidateDTO = bBomHeaderServiceImpl.getBomInfoWithRouteByBomNo("125000860001ABC");
		Assert.assertEquals(RetCode.SUCCESS_CODE, baCraftRoutingValidateDTO.getRetCode().getCode());
		Assert.assertEquals("125000860001ABC", baCraftRoutingValidateDTO.getBaCraftRoutingInfoDTO().getBomNo());

	}

	@Test
	public void queryBBomHeadListByBomIdsTest() throws Exception {
		Set<String> bomSet = new HashSet<>();
		bBomHeaderServiceImpl.queryBBomHeadListByBomIds(bomSet);
		bomSet.add("test");
		Assert.assertNotNull(bBomHeaderServiceImpl.queryBBomHeadListByBomIds(bomSet));
	}

	/* Started by AICoder, pid:cb3373d6a5oae5614c4c0a7f3076190268e53460 */
	@Test
	public void queryBBomHeadIdByProductCodeTest() throws Exception {
		String productCode = "";
		Assert.assertNull(bBomHeaderServiceImpl.queryBBomHeadIdByProductCode(productCode));
	}
	/* Ended by AICoder, pid:cb3373d6a5oae5614c4c0a7f3076190268e53460 */
}
