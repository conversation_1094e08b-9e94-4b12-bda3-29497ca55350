package com.zte.util;

import com.zte.application.impl.CtProgramFileServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.ArtifactoryUtils;
import com.zte.domain.model.CtProgramFileEntity;
import com.zte.domain.model.CtProgramFileRepository;
import com.zte.interfaces.dto.CtProgramFileEntityDTO;
import com.zte.interfaces.dto.ProgramBomInfoDTO;
import com.zte.itp.msa.redis.util.RedisCacheUtils;
import com.zte.springbootframe.common.exception.MesBusinessException;
import com.zte.springbootframe.common.redis.RedisLock;
import com.zte.springbootframe.util.CloudDiskHelper;
import org.apache.commons.io.IOUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.times;

/**
 * 单元测试
 *
 * <AUTHOR>
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({CtProgramFileServiceImpl.class, RedisCacheUtils.class, ArtifactoryUtils.class, IOUtils.class})
public class CtProgramFileServiceImplTest {

    @InjectMocks
    private CtProgramFileServiceImpl ctProgramFileService;

    @Mock
    private CtProgramFileRepository ctProgramFileRepository;

    @Mock
    private CloudDiskHelper cloudDiskHelper;

    @Mock
    private RedisLock redisLock;
    @Mock
    private URL downloadUrl;

    @Mock
    private HttpServletResponse response;

    @Before
    public void init() {
        // 待测的类标注为@InjectMocks
        // 依赖的其他类标注为 @Mock
        // 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
    }

    @Test
    public void getNextSoftVer() throws Exception {

        List<CtProgramFileEntity> list = new ArrayList<>();
        CtProgramFileEntity ctProgramFileEntity = new CtProgramFileEntity();
        ctProgramFileEntity.setPosition("A1、A2、A3");
        ctProgramFileEntity.setSoftVer("A");
        list.add(ctProgramFileEntity);
        PowerMockito.when(ctProgramFileRepository.getList(Mockito.any())).thenReturn(list);

        CtProgramFileEntityDTO dto = new CtProgramFileEntityDTO();
        List<ProgramBomInfoDTO> bomInfoList = new ArrayList<>();
        ProgramBomInfoDTO programBomInfoDTO = new ProgramBomInfoDTO();
        programBomInfoDTO.setBomNo("test");
        programBomInfoDTO.setVerNo("test");
        programBomInfoDTO.setPosition("A1、A2、A3");
        bomInfoList.add(programBomInfoDTO);

        dto.setBomInfoList(bomInfoList);
        dto.setProgramType("ogram");

        Assert.assertNotNull(ctProgramFileService.getNextSoftVer(dto));
    }

    @Test
    public void upload() throws Exception {

        PowerMockito.whenNew(RedisLock.class).withAnyArguments().thenReturn(redisLock);
        PowerMockito.when(redisLock.lock()).thenReturn(true);

        List<CtProgramFileEntity> list = new ArrayList<>();
        CtProgramFileEntity ctProgramFileEntity = new CtProgramFileEntity();
        ctProgramFileEntity.setPosition("A1、A2、A3");
        ctProgramFileEntity.setSoftVer("A");
        list.add(ctProgramFileEntity);
        PowerMockito.when(ctProgramFileRepository.getList(Mockito.any())).thenReturn(list);

        PowerMockito.when(ctProgramFileRepository.batchInsert(Mockito.any())).thenReturn(1);

        PowerMockito.when(cloudDiskHelper.getFileDownloadUrl(Mockito.anyString(), Mockito.any(), Mockito.anyString())).thenReturn("https://idrive.test.zte.com.cn/zte-km-cloududm-clouddisk/objects/downloadByToken/MES/ec24b108-b7ef-4652-b277-ecd2e26f1111");
        PowerMockito.mockStatic(IOUtils.class);
        byte[] data = {};
        PowerMockito.when(IOUtils.toByteArray(Mockito.any(InputStream.class))).thenReturn(data);

        PowerMockito.mockStatic(ArtifactoryUtils.class);

        CtProgramFileEntityDTO dto = new CtProgramFileEntityDTO();
        List<ProgramBomInfoDTO> bomInfoList = new ArrayList<>();
        ProgramBomInfoDTO programBomInfoDTO = new ProgramBomInfoDTO();
        programBomInfoDTO.setBomNo("test");
        programBomInfoDTO.setVerNo("test");
        programBomInfoDTO.setPosition("A1、A2、A3");
        bomInfoList.add(programBomInfoDTO);

        dto.setBomInfoList(bomInfoList);
        dto.setProgramType("ogram");
        dto.setSoftVer("B");
        dto.setFileName("aaa.sql");
        dto.setUploadDirectory("aaa");
        PowerMockito.whenNew(URL.class).withAnyArguments().thenReturn(downloadUrl);
        try {
            ctProgramFileService.upload(dto);
        } catch (MesBusinessException e) {
            Assert.assertEquals(MessageId.DOWNLOAD_IS_FAILED, e.getExMsgId());
        }
    }

    /*Started by AICoder, pid:t88dad37e605fc71486008bce1403e75b511cde0*/
    @Test(expected = MesBusinessException.class)
    public void testDownload_NoDataFound() throws Exception {
        when(ctProgramFileRepository.getList(any(CtProgramFileEntityDTO.class)))
                .thenReturn(Collections.emptyList());

        ctProgramFileService.download("1", response);
    }

    @Test(expected = MesBusinessException.class)
    public void testDownload_PackUploadUrl_MissingUploadDirectory() throws Exception {
        CtProgramFileEntity entity = new CtProgramFileEntity();
        entity.setBomNo("BOM1");
        entity.setVerNo("V1");
        entity.setPosition("POS1");
        entity.setProgramType("TYPE1");

        when(ctProgramFileRepository.getList(any(CtProgramFileEntityDTO.class)))
                .thenReturn(Arrays.asList(entity));

        ctProgramFileService.download("1", response);
    }

    @Test(expected = MesBusinessException.class)
    public void testDownload_PackUploadUrl_MissingBomNo() throws Exception {
        CtProgramFileEntity entity = new CtProgramFileEntity();
        entity.setUploadDirectory("DIR1");
        entity.setVerNo("V1");
        entity.setPosition("POS1");
        entity.setProgramType("TYPE1");

        when(ctProgramFileRepository.getList(any(CtProgramFileEntityDTO.class)))
                .thenReturn(Arrays.asList(entity));

        ctProgramFileService.download("1", response);
    }

    @Test(expected = MesBusinessException.class)
    public void testDownload_PackUploadUrl_MissingVerNo() throws Exception {
        CtProgramFileEntity entity = new CtProgramFileEntity();
        entity.setUploadDirectory("DIR1");
        entity.setBomNo("BOM1");
        entity.setPosition("POS1");
        entity.setProgramType("TYPE1");

        when(ctProgramFileRepository.getList(any(CtProgramFileEntityDTO.class)))
                .thenReturn(Arrays.asList(entity));

        ctProgramFileService.download("1", response);
    }

    @Test(expected = MesBusinessException.class)
    public void testDownload_PackUploadUrl_MissingPosition() throws Exception {
        CtProgramFileEntity entity = new CtProgramFileEntity();
        entity.setUploadDirectory("DIR1");
        entity.setBomNo("BOM1");
        entity.setVerNo("V1");
        entity.setProgramType("TYPE1");

        when(ctProgramFileRepository.getList(any(CtProgramFileEntityDTO.class)))
                .thenReturn(Arrays.asList(entity));

        ctProgramFileService.download("1", response);
    }

    @Test(expected = MesBusinessException.class)
    public void testDownload_PackUploadUrl_MissingProgramType() throws Exception {
        CtProgramFileEntity entity = new CtProgramFileEntity();
        entity.setUploadDirectory("DIR1");
        entity.setBomNo("BOM1");
        entity.setVerNo("V1");
        entity.setPosition("POS1");

        when(ctProgramFileRepository.getList(any(CtProgramFileEntityDTO.class)))
                .thenReturn(Arrays.asList(entity));

        ctProgramFileService.download("1", response);
    }

    @Test(expected = MesBusinessException.class)
    public void testDownload_PackUploadUrl_MissingSoftVer() throws Exception {
        CtProgramFileEntity entity = new CtProgramFileEntity();
        entity.setUploadDirectory("DIR1");
        entity.setBomNo("BOM1");
        entity.setVerNo("V1");
        entity.setPosition("POS1");
        entity.setProgramType("TYPE1");
        entity.setFileName("file.txt");

        when(ctProgramFileRepository.getList(any(CtProgramFileEntityDTO.class)))
                .thenReturn(Arrays.asList(entity));

        ctProgramFileService.download("1", response);
        Assert.assertNotNull(entity);
    }

    @Test(expected = MesBusinessException.class)
    public void testDownload_PackUploadUrl_MissingFileName() throws Exception {
        CtProgramFileEntity entity = new CtProgramFileEntity();
        entity.setUploadDirectory("DIR1");
        entity.setBomNo("BOM1");
        entity.setVerNo("V1");
        entity.setPosition("POS1");
        entity.setProgramType("TYPE1");
        entity.setSoftVer("SV1");

        when(ctProgramFileRepository.getList(any(CtProgramFileEntityDTO.class)))
                .thenReturn(Arrays.asList(entity));

        ctProgramFileService.download("1", response);
        Assert.assertNotNull(entity);
    }

    @Test
    public void testDownload_Success() throws Exception {
        CtProgramFileEntity entity = new CtProgramFileEntity();
        entity.setId("1");
        entity.setUploadDirectory("DIR1");
        entity.setBomNo("BOM1");
        entity.setVerNo("V1");
        entity.setPosition("POS1");
        entity.setProgramType("TYPE1");
        entity.setSoftVer("SV1");
        entity.setFileName("file.txt");

        when(ctProgramFileRepository.getList(any(CtProgramFileEntityDTO.class)))
                .thenReturn(Arrays.asList(entity));

        PowerMockito.mockStatic(ArtifactoryUtils.class);

        ctProgramFileService.download("1", response);
        Assert.assertNotNull(entity);
    }
    /*Ended by AICoder, pid:t88dad37e605fc71486008bce1403e75b511cde0*/
}
