package com.zte.util;

import com.google.common.collect.Lists;
import com.zte.application.impl.BoardCycleDateServiceImpl;
import com.zte.common.model.MessageId;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.BoardCycleDataComputeRepository;
import com.zte.domain.model.BoardCycleDataInfo;
import com.zte.domain.model.BoardCycleDataInfoRepository;
import com.zte.domain.model.sncabind.PsTask;
import com.zte.interfaces.dto.BoardCycleDataDTO;
import com.zte.itp.msa.core.model.PageRows;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;


/**
 * <AUTHOR>
 * @Description:
 * @date 2020/9/10 15:50
 */
@RunWith(PowerMockRunner.class)
public class BoardCycleDateServiceImplTest {

    @InjectMocks
    private BoardCycleDateServiceImpl dateService;

    @Mock
    private BoardCycleDataInfoRepository infoRepository;

    @Mock
    private BoardCycleDataComputeRepository computeRepository;


    @Before
    public void init() {

        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void getBoardCycleList(){
        BoardCycleDataDTO dataDTO = new BoardCycleDataDTO();
        dataDTO.setBomName("ZXSDR R8972E S2300 R16TRA23C");
        dataDTO.setBomNo("129702631000");
        dataDTO.setProductClass("库存返工");
        dataDTO.setPlanningGroupDesc("EGUARD");
        dataDTO.setPageNO(1);
        dataDTO.setEndRow(10);
        dataDTO.setPageSize(30);
        BoardCycleDataInfo boardCycleDataInfo = new BoardCycleDataInfo();
        boardCycleDataInfo.setBomName("ZXSDR R8972E S2300 R16TRA23C");
        boardCycleDataInfo.setBomNo("129702631000");
        boardCycleDataInfo.setProductClass("库存返工");
        boardCycleDataInfo.setPlanningGroupDesc("EGUARD");
        List<BoardCycleDataInfo> list = new ArrayList<>();
        list.add(boardCycleDataInfo);
        PowerMockito.when(infoRepository.getInfoNumber(dataDTO)).thenReturn(1L);
        PowerMockito.when(infoRepository.getBoardCycleList(dataDTO)).thenReturn(list);
        PageRows<BoardCycleDataInfo> page = dateService.getBoardCycleList(dataDTO);
        Assert.assertTrue(page.getRows().size() >= 0);
    }

    @Test
    public void findBoardCycleList(){
        BoardCycleDataInfo boardCycleDataInfo = new BoardCycleDataInfo();
        boardCycleDataInfo.setBomName("ZXSDR R8972E S2300 R16TRA23C");
        boardCycleDataInfo.setBomNo("129702631000");
        boardCycleDataInfo.setProductClass("库存返工");
        boardCycleDataInfo.setPlanningGroupDesc("EGUARD");

        List<BoardCycleDataInfo> list = new ArrayList<>();
        list.add(boardCycleDataInfo);

        PowerMockito.when(infoRepository.getBoardCycleList(Mockito.any())).thenReturn(list);

        BoardCycleDataDTO dto = new BoardCycleDataDTO();
        dto.setBomName("ZXSDR R8972E S2300 R16TRA23C");
        dto.setBomNo("129702631000");
        dto.setProductClass("库存返工");
        dto.setPlanningGroupDesc("EGUARD");
        Assert.assertNotNull(dateService.findBoardCycleList(dto));
    }

    @Test
    public void getBoardList(){
        BoardCycleDataInfo boardCycleDataInfo = new BoardCycleDataInfo();
        boardCycleDataInfo.setBomName("ZXSDR R8972E S2300 R16TRA23C");
        boardCycleDataInfo.setBomNo("129702631000");
        boardCycleDataInfo.setProductClass("库存返工");
        boardCycleDataInfo.setPlanningGroupDesc("EGUARD");
        List<BoardCycleDataInfo> list = new ArrayList<>();
        list.add(boardCycleDataInfo);
        PowerMockito.when(infoRepository.getBoardCycleList(Mockito.any())).thenReturn(list);
        BoardCycleDataDTO dto = new BoardCycleDataDTO();
        dto.setBomName("ZXSDR R8972E S2300 R16TRA23C");
        dto.setBomNo("129702631000");
        dto.setProductClass("库存返工");
        dto.setPlanningGroupDesc("EGUARD");
        Assert.assertNotNull(dateService.getBoardList(dto));
    }

    @Test
    public void getBoardCycleByBom() {
        Assert.assertNotNull(dateService.getBoardCycleByBom(Lists.newArrayList(new PsTask())));
    }

    @Test
    public void getCycleOfVariance() throws Exception{
        BoardCycleDataInfo boardCycleDataInfo = new BoardCycleDataInfo();
        boardCycleDataInfo.setBomNo("122096851132");
        boardCycleDataInfo.setProductClass("DHOME");
        boardCycleDataInfo.setPlanningGroupDesc("测试计划");
        boardCycleDataInfo.setOrgId(new BigDecimal(4437));
        boardCycleDataInfo.setFactoryId(new BigDecimal(55));
        PowerMockito.when(computeRepository.getCycleOfVariance(boardCycleDataInfo)).thenReturn(new BigDecimal(5));
        Assert.assertNotNull(Whitebox.invokeMethod(dateService, "getCycleOfVariance", -24,boardCycleDataInfo));
    }

    @Test
    public void setCycleDataInfo() throws Exception{
        BoardCycleDataInfo boardCycleDataInfo = new BoardCycleDataInfo();
        boardCycleDataInfo.setBomNo("122096851132");
        boardCycleDataInfo.setProductClass("DHOME");
        boardCycleDataInfo.setPlanningGroupDesc("测试计划");
        boardCycleDataInfo.setOrgId(new BigDecimal(4437));
        boardCycleDataInfo.setFactoryId(new BigDecimal(55));
        Whitebox.invokeMethod(dateService, "setCycleDataInfo", boardCycleDataInfo,boardCycleDataInfo);
        Assert.assertNotNull(boardCycleDataInfo);
    }

    @Test
    public void insertBoardCycle() throws Exception{
        BoardCycleDataInfo boardCycleDataInfo = new BoardCycleDataInfo();
        boardCycleDataInfo.setBomNo("122096851132");
        boardCycleDataInfo.setYears(-24);
        boardCycleDataInfo.setProductClass("DHOME");
        boardCycleDataInfo.setPlanningGroupDesc("测试计划");
        boardCycleDataInfo.setOrgId(new BigDecimal(4437));
        boardCycleDataInfo.setFactoryId(new BigDecimal(55));
        List<BoardCycleDataInfo> dataInfos = new ArrayList<>(NumConstant.NUM_TEN);
        dataInfos.add(boardCycleDataInfo);
        PowerMockito.when(computeRepository.countBoardCodeCycleInfo()).thenReturn(100L);
        PowerMockito.when(computeRepository.findBoardCodeCycleInfoGroup(1,100)).thenReturn(dataInfos);
        PowerMockito.when(computeRepository.getCycleOfAvg(boardCycleDataInfo)).thenReturn(boardCycleDataInfo);
        PowerMockito.when(computeRepository.getCycleOfMedian(boardCycleDataInfo)).thenReturn(boardCycleDataInfo);
        try{
            dateService.insertBoardCycle();
        }catch (Exception e){
            Assert.assertEquals(e.getMessage(), e.getMessage());
        }
    }
    @Test
    public void insetBoardCodeCycleInfo() throws Exception{
        BoardCycleDataInfo boardCycleDataInfo = new BoardCycleDataInfo();
        boardCycleDataInfo.setBomNo("122096851132");
        boardCycleDataInfo.setYears(-24);
        boardCycleDataInfo.setProductClass("DHOME");
        boardCycleDataInfo.setPlanningGroupDesc("测试计划");
        boardCycleDataInfo.setOrgId(new BigDecimal(4437));
        boardCycleDataInfo.setFactoryId(new BigDecimal(55));
        List<BoardCycleDataInfo> dataInfos = new ArrayList<>(NumConstant.NUM_TEN);
        dataInfos.add(boardCycleDataInfo);
        PowerMockito.when(computeRepository.countBoardCodeCycleInfo()).thenReturn(100L);
        PowerMockito.when(computeRepository.findBoardCodeCycleInfoGroup(1,100)).thenReturn(dataInfos);
        PowerMockito.when(computeRepository.getCycleOfAvg(boardCycleDataInfo)).thenReturn(boardCycleDataInfo);
        PowerMockito.when(computeRepository.getCycleOfMedian(boardCycleDataInfo)).thenReturn(boardCycleDataInfo);
        try{
            dateService.insetBoardCodeCycleInfo(dataInfos);
        }catch (Exception e){
            Assert.assertEquals(null, e.getMessage());
        }
    }

}
