package com.zte.util;

import com.zte.application.impl.ContractCharacterizationInfoServiceImpl;
import com.zte.application.impl.SysLookupValuesServiceImpl;
import com.zte.common.utils.Constant;
import com.zte.common.utils.DateUtil;
import com.zte.common.utils.NumConstant;
import com.zte.domain.model.ContractCharacterizationInfo;
import com.zte.domain.model.ContractCharacterizationInfoRepository;
import com.zte.domain.model.SysLookupValues;
import com.zte.infrastructure.remote.DatawbRemoteService;
import com.zte.interfaces.dto.ContractCharaInfoQueryDTO;
import com.zte.interfaces.dto.ContractCharacterizationInfoDTO;
import com.zte.interfaces.dto.PageAndIdRegionDTO;
import com.zte.interfaces.dto.datawb.ApsProdModelCountDTO;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 单元测试
 * <AUTHOR>
 *
 */
@RunWith(PowerMockRunner.class)
@PrepareForTest({DatawbRemoteService.class})
public class ContractCharacterizationInfoServiceImplTest {
	
	@InjectMocks
	private ContractCharacterizationInfoServiceImpl contractCharacterizationInfoServiceImpl;
	
	@Mock
	private SysLookupValuesServiceImpl sysLookupValuesServiceImpl;
	
	@Mock
	private ContractCharacterizationInfoRepository contractCharacterizationInforepository;

	
	@Before
    public void init() {
		// 待测的类标注为@InjectMocks
		// 依赖的其他类标注为 @Mock
		// 使用MockitoAnnotations.initMocks(this)自动将依赖的类注入待测类
		MockitoAnnotations.initMocks(this);
    }
	
	@Test
    public void createData() throws Exception {
		String statusNames = "";
		Date now = new Date();
		Date dateStart = now;
		Date dateEnd = DateUtil.getAfterYears(now, NumConstant.NUM_ONE);
		
		ContractCharaInfoQueryDTO dto = new ContractCharaInfoQueryDTO();
		dto.setStatusNames(statusNames);
		dto.setStartTime(DateUtil.convertDateToString(dateStart, DateUtil.DATE_FORMATE_DAY_WITH_0));
		dto.setEndTime(DateUtil.convertDateToString(dateEnd, DateUtil.DATE_FORMATE_DAY_WITH_0));
		dto.setRows(NumConstant.NUM_1000);
		
		// mock分页信息
		List<PageAndIdRegionDTO> pageInfo = new ArrayList<>();
		PageAndIdRegionDTO pageAndIdRegionDTO = new PageAndIdRegionDTO();
		pageAndIdRegionDTO.setPage(NumConstant.NUM_ZERO);
		pageAndIdRegionDTO.setMinId(NumConstant.NUM_ZERO);
		pageAndIdRegionDTO.setMaxId(NumConstant.NUM_1000);
		pageInfo.add(pageAndIdRegionDTO);
		
		PowerMockito.mockStatic(DatawbRemoteService.class);
		PowerMockito.when(DatawbRemoteService.getMinAndMaxEntityIdWithPage(Mockito.any())).thenReturn(pageInfo);
		
		// mock 查询源数据
		List<ContractCharacterizationInfoDTO> list = new ArrayList<>();
		ContractCharacterizationInfoDTO contractCharacterizationInfoDTO = new ContractCharacterizationInfoDTO();
		contractCharacterizationInfoDTO.setId("test");
		contractCharacterizationInfoDTO.setEntityId("test");
		contractCharacterizationInfoDTO.setEntityName("test");
		contractCharacterizationInfoDTO.setOverallUnitName("test");
		contractCharacterizationInfoDTO.setOverallCount(NumConstant.DOUBLE_EIGHTY);
		contractCharacterizationInfoDTO.setEnabledFlag(Constant.FLAG_N);
		list.add(contractCharacterizationInfoDTO);
		PowerMockito.when(DatawbRemoteService.getContractCharaInfo(Mockito.any())).thenReturn(list);
		
		// mock 查询数据字典
		List<SysLookupValues> lookupValues = new ArrayList<>();
		PowerMockito.when(sysLookupValuesServiceImpl.findByLookupType(Constant.LOOKUP_TYPE_1291)).thenReturn(lookupValues);
		
		// mock 查询整机名称、整机数量
		List<ApsProdModelCountDTO> apsProdModelCountList = new ArrayList<>();
		ApsProdModelCountDTO apsProdModelCountDTO = new ApsProdModelCountDTO();
		apsProdModelCountDTO.setEntityName("test");
		apsProdModelCountDTO.setOverallUnitName("test");
		apsProdModelCountDTO.setOverallCount(NumConstant.DOUBLE_EIGHTY);
		apsProdModelCountList.add(apsProdModelCountDTO);
		PowerMockito.when(DatawbRemoteService.getApsProdModelCountByEntityNames(Mockito.any())).thenReturn(apsProdModelCountList);
		
		// mock 当前数据
		List<ContractCharacterizationInfo> currList = new ArrayList<>();
		ContractCharacterizationInfo info = new ContractCharacterizationInfo();
		info.setId("test");
		info.setEntityId("test");
		info.setEntityName("test");
		info.setOverallUnitName("test");
		info.setOverallCount(NumConstant.DOUBLE_EIGHTY);
		info.setEnabledFlag(Constant.FLAG_Y);
		currList.add(info);
		
		PowerMockito.when(contractCharacterizationInforepository.getList(Mockito.any())).thenReturn(currList);
		
		// mock 
		/*
		 * // mock没有返回值（void）的方法
		 * PowerMockito.doNothing().when(contractCharacterizationInfoRepository).batchInsert(alist);
		 */
//		doReturn方式
//		doReturn(NumConstant.NUM_ONE).when(contractCharacterizationInforepository).batchInsert(anyObject());	
		PowerMockito.when(contractCharacterizationInforepository.batchInsert(Mockito.any())).thenReturn(NumConstant.NUM_ONE);
		PowerMockito.when(contractCharacterizationInforepository.batchUpdate(Mockito.any())).thenReturn(NumConstant.NUM_ONE);
		
		contractCharacterizationInfoServiceImpl.createData(statusNames, dateStart, dateEnd,false);
		String runNormal = "Y";
		Assert.assertEquals(Constant.FLAG_Y, runNormal);
	}
}
