package com.zte.util;

import com.zte.common.utils.Constant;
import com.zte.common.utils.TemplateFunctionExpress;
import com.zte.springbootframe.util.MESHttpHelper;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.beans.BeanUtils;

import java.util.Map;
import java.util.UUID;

import static com.zte.common.utils.SpecialityParamConstant.FUNCTION_MOD;


@RunWith(PowerMockRunner.class)
@PrepareForTest({BeanUtils.class, MESHttpHelper.class, UUID.class})
public class TemplateFunctionExpressTest {

    @Test
    public void insertTest() {
        Map<String, Integer> functions = TemplateFunctionExpress.FUNCTIONS;
        TemplateFunctionExpress.checkExpression(FUNCTION_MOD);
        Assert.assertNotNull(functions);
    }

    @Test
    public void testRandom() {
        int number = 100;
        for (int i = 0; i < number; i++) {
            System.out.println(new TemplateFunctionExpress("'CMCC-'||RANDSTR('2345679abcdefhkstuxyzACDEFGHKNPQRSTUXYZ',4)").caculate());
        }
        String runNormal = "Y";
        Assert.assertEquals(Constant.FLAG_Y, runNormal);
    }

}
